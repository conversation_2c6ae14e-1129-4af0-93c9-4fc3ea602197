@extends('layouts.app')

@section('content')

<x-sm-modal>
    <x-slot name="class">add-setting</x-slot>
    <x-slot name="title">ADD SETTING</x-slot>

    <form class="" action="/settings" enctype="multipart/form-data" method="POST" id="addSetting">
        @csrf
        <p>
        <label>Name</label>
        <input type="text" class="form-control" name="key" required>            
        </p>

        <p>
        <label>Value</label>
        <input type="text" class="form-control" name="value" placeholder="Text value" required>
        <input type="file" class="form-control mt-1" name="media">
        </p>
        <p>
            You have an option to attack a file.
        </p>
    </form>
    <x-slot name="footer">
        <button class="btn btn-primary" onclick="addSetting.submit()">Save</button>
    </x-slot>
</x-sm-modal>


<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="title">Settings</x-slot>
        <x-slot name="controls">
            @if( auth()->user()->isSuperAdmin() )
            <a href="" class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target=".add-setting">
                <!-- <i class="icon ion-md-add"></i> -->
                Add Option
            </a>
            @endif
        </x-slot>
    </x-page-header>

    <!-- End Page Header -->
    <div class="card card-table">
        <div class="table-responsive mt-3">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            KEY
                        </th>
                        <th class="text-left">
                            VALUE
                        </th>
                        <th class="text-left">
                            CREATED BY
                        </th>
                        <th class="text-center">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($settings as $setting)
                    <tr>
                        <td>{{ $setting->key ?? '-' }}</td>
                        <td>
                            @if( $setting->path )
                            <x-partials.thumbnail src="{{ $setting->path ?? '' }}"/>
                            @else
                            {{ $setting->value ?? '-' }}
                            @endif
                        </td>
                        <td>{{ $setting->createdBy->name ?? '-' }}</td>
                        <td class="text-center" style="width: 134px;">
                            <div  role="group"  aria-label="Row Actions"class="btn-group">

                                <form
                                    action="{{ route('settings.destroy', $setting) }}"
                                    method="POST"
                                    onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                >
                                    @csrf @method('DELETE')
                                    <button
                                        type="submit"
                                        class="btn btn-light text-danger m-1"
                                    >
                                        <!-- <i class="icon ion-md-trash"></i> --> del
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    
</div>
@endsection

@push('scripts')
<script>
  $("document").ready(function () {
    dataTableBtn()
  });
</script>

@endpush
