@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">geo-alt</x-slot>
        <x-slot name="title">Locations</x-slot>
        <x-slot name="subtitle">Manage your business locations</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item active" aria-current="page">Locations</li>
        </x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Location::class)
            <a href="{{ route('locations.create') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i> Add Location
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('locations.index') }}">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="search" class="form-label">Search</label>
                        <input 
                            type="text" 
                            name="search" 
                            id="search"
                            class="form-control" 
                            value="{{ $search }}" 
                            placeholder="Search by name, code, or description..."
                        >
                    </div>
                    <div class="col-md-4">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">All Statuses</option>
                            @foreach($statuses as $value => $label)
                                <option value="{{ $value }}" {{ $status == $value ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <div class="btn-group w-100">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="bi bi-search me-1"></i> Filter
                            </button>
                            <a href="{{ route('locations.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i> Clear
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- End Filters -->

    <!-- Locations Table -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h4 class="card-header-title">
                        <i class="bi bi-list-ul me-2"></i>Locations List
                    </h4>
                </div>
                <div class="col-auto">
                    <span class="badge bg-soft-secondary">{{ $locations->total() }} total</span>
                </div>
            </div>
        </div>

        @if($locations->count() > 0)
        <div class="table-responsive">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Location</th>
                        <th>Code</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($locations as $location)
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar avatar-soft-primary avatar-circle">
                                        <span class="avatar-initials">{{ substr($location->name, 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5 class="mb-0">
                                        <a href="{{ route('locations.show', $location) }}" class="text-dark">
                                            {{ $location->name }}
                                        </a>
                                    </h5>
                                    @if($location->description)
                                    <p class="text-muted mb-0">{{ Str::limit($location->description, 50) }}</p>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td>
                            @if($location->code)
                                <span class="badge bg-soft-info">{{ $location->code }}</span>
                            @else
                                <span class="text-muted">—</span>
                            @endif
                        </td>
                        <td>
                            @if($location->status)
                                <span class="badge {{ $location->status->id == 1 ? 'bg-soft-success' : 'bg-soft-secondary' }}">
                                    {{ $location->status->name }}
                                </span>
                            @else
                                <span class="text-muted">—</span>
                            @endif
                        </td>
                        <td>
                            <div>
                                <span class="d-block">{{ $location->created_at->format('M d, Y') }}</span>
                                <small class="text-muted">by {{ $location->createdBy->name ?? 'System' }}</small>
                            </div>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                @can('view', $location)
                                <a href="{{ route('locations.show', $location) }}" class="btn btn-sm btn-outline-info">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @endcan

                                @can('update', $location)
                                <a href="{{ route('locations.edit', $location) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                @endcan

                                @can('delete', $location)
                                <form action="{{ route('locations.destroy', $location) }}" method="POST" class="d-inline">
                                    @csrf @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-outline-danger" 
                                            onclick="return confirm('Are you sure you want to delete this location?')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="card-footer">
            {{ $locations->links() }}
        </div>
        @else
        <!-- Empty State -->
        <div class="card-body text-center py-5">
            <div class="avatar avatar-lg avatar-soft-secondary avatar-circle mx-auto mb-3">
                <span class="avatar-initials"><i class="bi bi-geo-alt"></i></span>
            </div>
            <h4>No locations found</h4>
            <p class="text-muted mb-4">
                @if($search || $status)
                    No locations match your current filters. Try adjusting your search criteria.
                @else
                    Get started by creating your first location.
                @endif
            </p>
            @if(!$search && !$status)
                @can('create', App\Models\Location::class)
                <a href="{{ route('locations.create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i> Add First Location
                </a>
                @endcan
            @else
                <a href="{{ route('locations.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-1"></i> View All Locations
                </a>
            @endif
        </div>
        @endif
    </div>
    <!-- End Locations Table -->
</div>
<!-- End Content -->
@endsection
