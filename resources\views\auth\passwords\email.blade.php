@extends('layouts.guest')

@section('content')
<style>
    .bg-gradient {
        background: linear-gradient(135deg, #6b48ff 0%, #00ddeb 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
    }
    .card-login {
        border: none;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        transition: transform 0.3s ease;
    }
    .card-login:hover {
        transform: translateY(-5px);
    }
    .form-control-lg {
        border-radius: 0.5rem;
        border: 1px solid #ced4da;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }
    .form-control-lg:focus {
        border-color: #6b48ff;
        box-shadow: 0 0 0 0.25rem rgba(107, 72, 255, 0.25);
    }
    .btn-primary {
        background: #6b48ff;
        border: none;
        border-radius: 0.5rem;
        padding: 0.75rem;
        font-weight: 600;
        transition: background 0.3s ease, transform 0.2s ease;
    }
    .btn-primary:hover {
        background: #5638cc;
        transform: scale(1.02);
    }
    .input-group-text {
        background: transparent;
        border: 1px solid #ced4da;
        border-radius: 0.5rem;
    }
    .form-label {
        font-weight: 500;
        color: #344767;
    }
    .logo-img {
        transition: transform 0.3s ease;
    }
    .logo-img:hover {
        transform: scale(1.1);
    }
    .text-danger {
        font-size: 0.875rem;
        color: red;
    }
    .shape-bottom {
        position: absolute;
        bottom: 0;
        width: 100%;
    }
    @media (max-width: 576px) {
        .card-login {
            margin: 1rem;
        }
    }
</style>

<div class="bg-gradient">
    <!-- Shape -->
    <div class="shape-bottom">
        <svg preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 1921 273">
            <polygon fill="#fff" points="0,273 1921,273 1921,0" />
        </svg>
    </div>
    <!-- End Shape -->

    <!-- Content -->
    <div class="container py-5 py-sm-7">
        <div class="mx-auto" style="max-width: 32rem;">
            <!-- Card -->
            <div class="card card-login mt-5">
                <div class="card-body p-5">
                    <div class="text-center mb-5">
                        <a href="{{ url('/') }}">
                            <img class="logo-img" src="{{ asset('img/logo.webp') }}" alt="Logo" style="width: 6rem;">
                        </a>
                    </div>
                    @if (session('status'))
                        <div class="alert alert-success" role="alert">
                            {{ session('status') }}
                        </div>
                    @endif

                    <form method="POST" action="{{ route('password.email') }}">
                        @csrf

                        <div class="row mb-3">
                            <label for="email" class="col-md-4 col-form-label text-md-end">{{ __('Email Address') }}</label>

                            <div class="col-md-6">
                                <input id="email" type="email" class="form-control @error('email') is-invalid @enderror" name="email" value="{{ old('email') }}" required autocomplete="email" autofocus>

                                @error('email')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-0">
                            <div class="col-md-6 offset-md-4">
                                <button type="submit" class="btn btn-primary">
                                    {{ __('Send Password Reset Link') }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <!-- End Card -->
        </div>
    </div>
    <!-- End Content -->
</div>

@endsection