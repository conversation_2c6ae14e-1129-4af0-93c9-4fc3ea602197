<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Sale;

use App\Models\Unit;
use App\Models\Product;

use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SaleTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create(['email' => '<EMAIL>']);

        Sanctum::actingAs($user, [], 'web');

        $this->seed(\Database\Seeders\PermissionsSeeder::class);

        $this->withoutExceptionHandling();
    }

    /**
     * @test
     */
    public function it_gets_sales_list()
    {
        $sales = Sale::factory()
            ->count(5)
            ->create();

        $response = $this->getJson(route('api.sales.index'));

        $response->assertOk()->assertSee($sales[0]->product_name);
    }

    /**
     * @test
     */
    public function it_stores_the_sale()
    {
        $data = Sale::factory()
            ->make()
            ->toArray();

        $response = $this->postJson(route('api.sales.store'), $data);

        unset($data['saleable_id']);
        unset($data['saleable_type']);

        $this->assertDatabaseHas('sales', $data);

        $response->assertStatus(201)->assertJsonFragment($data);
    }

    /**
     * @test
     */
    public function it_updates_the_sale()
    {
        $sale = Sale::factory()->create();

        $product = Product::factory()->create();
        $unit = Unit::factory()->create();

        $data = [
            'product_name' => $this->faker->name(),
            'unit_name' => $this->faker->text(255),
            'price' => $this->faker->randomFloat(2, 0, 9999),
            'discount' => $this->faker->randomFloat(2, 0, 9999),
            'quantity' => $this->faker->randomNumber,
            'product_id' => $product->id,
            'unit_id' => $unit->id,
        ];

        $response = $this->putJson(route('api.sales.update', $sale), $data);

        unset($data['saleable_id']);
        unset($data['saleable_type']);

        $data['id'] = $sale->id;

        $this->assertDatabaseHas('sales', $data);

        $response->assertOk()->assertJsonFragment($data);
    }

    /**
     * @test
     */
    public function it_deletes_the_sale()
    {
        $sale = Sale::factory()->create();

        $response = $this->deleteJson(route('api.sales.destroy', $sale));

        $this->assertSoftDeleted($sale);

        $response->assertNoContent();
    }
}
