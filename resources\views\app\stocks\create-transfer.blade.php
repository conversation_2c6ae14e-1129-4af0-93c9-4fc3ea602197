@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">
                            <i class="bi bi-arrow-left-right me-2"></i>Create Stock Transfer
                        </h4>
                        <div class="d-flex gap-2">
                            <a href="{{ route('stocks.transfers') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-list me-1"></i>View Transfers
                            </a>
                            <a href="{{ route('stocks.index') }}" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-box me-1"></i>Stock Inventory
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Form -->
    <div id="transferApp" class="mt-4">
        <form ref="transferForm" action="{{ route('stocks.store-transfer') }}" method="POST" @submit="submitTransfer">
            @csrf
            
            <!-- Transfer Items Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul me-2"></i>Transfer Items
                        <span class="badge bg-primary ms-2">@{{ transfer.items.length }} item(s)</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 25%">Product</th>
                                    <th style="width: 12%">Unit</th>
                                    <th style="width: 10%">Quantity</th>
                                    <th style="width: 20%">New Warehouse</th>
                                    <th style="width: 20%">Reason</th>
                                    <th style="width: 8%">Available</th>
                                    <th style="width: 5%">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(item, index) in transfer.items" :key="index">
                                    <!-- Product -->
                                    <td>
                                        <select v-model="item.product_id" 
                                                @change="updateProductDetails(index)"
                                                :name="'items[' + index + '][product_id]'"
                                                class="form-select"
                                                required>
                                            <option value="">Select Product</option>
                                            <option v-for="product in products" 
                                                    :key="product.id" 
                                                    :value="product.id">
                                                @{{ product.name }}
                                            </option>
                                        </select>
                                    </td>

                                    <!-- Unit -->
                                    <td>
                                        <select v-model="item.unit_id"
                                                @change="updateUnitPrice(index)"
                                                :name="'items[' + index + '][unit_id]'"
                                                class="form-select"
                                                required>
                                            <option value="">Select Unit</option>
                                            <option v-for="unit in getProductUnits(item.product_id)" 
                                                    :key="unit.id" 
                                                    :value="unit.id">
                                                @{{ unit.name }}
                                            </option>
                                        </select>
                                    </td>

                                    <!-- Quantity -->
                                    <td>
                                        <input type="number"
                                               v-model="item.quantity"
                                               @input="validateQuantity(index)"
                                               :name="'items[' + index + '][quantity]'"
                                               class="form-control"
                                               step="0.01"
                                               min="0.01"
                                               :max="item.available_quantity"
                                               required>
                                        <small v-if="item.quantityError" class="text-danger">@{{ item.quantityError }}</small>
                                    </td>

                                    <!-- New Warehouse -->
                                    <td>
                                        <select v-model="item.new_warehouse_id"
                                                :name="'items[' + index + '][new_warehouse_id]'"
                                                class="form-select"
                                                required>
                                            <option value="">Select Warehouse</option>
                                            <option v-for="warehouse in warehouses" 
                                                    :key="warehouse.id" 
                                                    :value="warehouse.id">
                                                @{{ warehouse.name }} (@{{ warehouse.branch.name }})
                                            </option>
                                        </select>
                                    </td>

                                    <!-- Reason -->
                                    <td>
                                        <input type="text"
                                               v-model="item.reason"
                                               :name="'items[' + index + '][reason]'"
                                               class="form-control"
                                               placeholder="Transfer reason..."
                                               required>
                                    </td>

                                    <!-- Available -->
                                    <td>
                                        <span class="badge bg-info">@{{ formatNumber(item.available_quantity || 0) }}</span>
                                    </td>

                                    <!-- Action -->
                                    <td>
                                        <button type="button" 
                                                @click="removeItem(index)"
                                                class="btn btn-outline-danger btn-sm">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Add Item Button -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <button type="button" @click="addItem" class="btn btn-outline-primary">
                            <i class="bi bi-plus-circle me-1"></i>Add Item
                        </button>
                        
                        <div class="text-muted">
                            Total Items: <strong>@{{ transfer.items.length }}</strong>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transfer Notes -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-file-text me-2"></i>Transfer Notes
                    </h5>
                </div>
                <div class="card-body">
                    <textarea v-model="transfer.notes"
                              name="transfer_notes"
                              class="form-control"
                              rows="3"
                              placeholder="Optional notes about this transfer..."></textarea>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="card mt-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{{ route('stocks.transfers') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Cancel
                        </a>
                        <button type="submit" 
                                class="btn btn-primary"
                                :disabled="!isFormValid || isSubmitting">
                            <i class="bi bi-arrow-clockwise spin me-1" v-if="isSubmitting"></i>
                            <i class="bi bi-arrow-left-right me-1" v-else></i>
                            @{{ isSubmitting ? 'Creating Transfer...' : 'Create Transfer' }}
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if data is available
    const productsData = @json($products ?? []);
    const warehousesData = @json($warehouses ?? []);

    console.log('Products loaded:', productsData.length);
    console.log('Warehouses loaded:', warehousesData.length);

    new Vue({
        el: '#transferApp',
        data() {
            return {
                products: productsData,
                warehouses: warehousesData,
                transfer: {
                    items: [],
                    notes: ''
                },
                isSubmitting: false,
                loading: false
            }
        },
        computed: {
            isFormValid() {
                if (this.transfer.items.length === 0) return false;
                
                return this.transfer.items.every(item => {
                    return item.product_id &&
                           item.unit_id &&
                           item.quantity > 0 &&
                           item.quantity <= item.available_quantity &&
                           item.new_warehouse_id &&
                           item.reason &&
                           !item.quantityError;
                });
            }
        },
        mounted() {
            // Set up axios defaults
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                axios.defaults.headers.common['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
            }

            // Add initial item if none exist
            if (this.transfer.items.length === 0) {
                this.addItem();
            }

            console.log('Vue app mounted successfully');
        },
        methods: {
            createNewItem() {
                return {
                    product_id: '',
                    unit_id: '',
                    quantity: 1,
                    new_warehouse_id: '',
                    reason: '',
                    available_quantity: 0,
                    quantityError: ''
                };
            },
            
            addItem() {
                this.transfer.items.push(this.createNewItem());
            },
            
            removeItem(index) {
                this.transfer.items.splice(index, 1);
                if (this.transfer.items.length === 0) {
                    this.addItem();
                }
            },
            
            getProductUnits(productId) {
                const product = this.products.find(p => p.id == productId);
                return product?.units || [];
            },
            
            async updateProductDetails(index) {
                const item = this.transfer.items[index];
                if (!item.product_id) {
                    item.unit_id = '';
                    item.available_quantity = 0;
                    return;
                }
                
                // Reset unit when product changes
                item.unit_id = '';
                
                // Get available stock for this product
                await this.updateAvailableStock(index);
            },
            
            async updateUnitPrice(index) {
                await this.updateAvailableStock(index);
            },
            
            async updateAvailableStock(index) {
                const item = this.transfer.items[index];
                if (!item.product_id || !item.unit_id) {
                    item.available_quantity = 0;
                    return;
                }
                
                try {
                    const response = await axios.get('/stocks/product-available', {
                        params: {
                            product_id: item.product_id,
                            unit_id: item.unit_id
                        }
                    });
                    
                    item.available_quantity = response.data.available_quantity || 0;
                    this.validateQuantity(index);
                } catch (error) {
                    console.error('Error fetching available stock:', error);
                    item.available_quantity = 0;
                }
            },
            
            validateQuantity(index) {
                const item = this.transfer.items[index];
                
                if (item.quantity <= 0) {
                    item.quantityError = 'Quantity must be greater than 0';
                } else if (item.quantity > item.available_quantity) {
                    item.quantityError = `Exceeds available quantity (${this.formatNumber(item.available_quantity)})`;
                } else {
                    item.quantityError = '';
                }
            },
            
            formatNumber(value) {
                return new Intl.NumberFormat().format(value || 0);
            },
            
            submitTransfer(event) {
                if (!this.isFormValid) {
                    event.preventDefault();
                    alert('Please complete all required fields.');
                    return false;
                }
                
                this.isSubmitting = true;
                return true;
            }
        }
    });
});
</script>
@endpush
@endsection
