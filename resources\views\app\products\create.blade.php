@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">box-seam</x-slot>
        <x-slot name="title">Create New Product</x-slot>
        <x-slot name="subtitle">Add a new product to your inventory</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('products.index') }}">Products</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('products.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Products
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Form Card -->
    <div class="card">
        <div class="card-body">
            <x-form
                method="POST"
                action="{{ route('products.store') }}"
                has-files
                class="mt-2"
            >
                @include('app.products.form-inputs')

                <div class="d-flex justify-content-end mt-4 border-top pt-4">
                    <a href="{{ route('products.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-x-circle me-1"></i> Cancel
                    </a>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-1"></i> Create Product
                    </button>
                </div>
            </x-form>
        </div>
    </div>
</div>
@endsection
