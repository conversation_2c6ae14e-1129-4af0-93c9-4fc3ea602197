<?php

namespace App\Http\Controllers;

use App\Models\Status;
use App\Models\Transaction;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Facades\App\Libraries\InvoiceHandler;

class TransactionController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Transaction::class);

        $search = $request->get('search', '');

        // Get both traditional transactions and payments as transactions
        $transactions = collect();

        // Get traditional transactions
        $traditionalTransactions = Transaction::search($search)->latest();
        if(request()->account_id) {
            $traditionalTransactions->where("account_id", request()->account_id);
        }
        $transactions = $transactions->merge($traditionalTransactions->get());

        // Get payments as transactions
        $payments = Payment::with('paymentable')
                          ->when($search, function($query, $search) {
                              $query->where('description', 'like', "%{$search}%")
                                    ->orWhere('comment', 'like', "%{$search}%")
                                    ->orWhere('reference_no', 'like', "%{$search}%");
                          })
                          ->latest()
                          ->get();

        // Transform payments to look like transactions
        $paymentTransactions = $payments->map(function($payment) {
            $paymentable = $payment->paymentable;
            $description = $payment->description ?:
                          ($payment->comment ?:
                          ($paymentable ? "Payment for " . class_basename($paymentable) . " #{$paymentable->id}" : "Payment"));

            return (object) [
                'id' => 'payment_' . $payment->id,
                'date' => $payment->created_at->format('Y-m-d'),
                'description' => $description,
                'amount' => $payment->amount,
                'debit' => $payment->amount, // Payments are typically debits to cash
                'credit' => 0,
                'balance' => $payment->balance,
                'account_id' => null,
                'account' => null,
                'created_at' => $payment->created_at,
                'type' => 'payment',
                'payment' => $payment,
                'reference_no' => $payment->reference_no,
                'paymentable_type' => $payment->paymentable_type,
                'paymentable_id' => $payment->paymentable_id,
                'paymentable' => $paymentable
            ];
        });

        $transactions = $transactions->merge($paymentTransactions);

        // Sort by date descending
        $transactions = $transactions->sortByDesc('created_at');

        return view('app.transactions.index', compact('transactions'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Transaction::class);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');

        return view('app.transactions.create', compact('statuses'));
    }

    /**
     * @param \App\Http\Requests\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', Transaction::class);

        $validated = $request->all();

        $validated[$request->type] = $request->amount;
        $transaction = Transaction::create($validated);
        $balance = Transaction::sum("credit") - Transaction::sum("debit");
        $transaction->update(['balance' => $balance]);

        return redirect()
            ->route('accounts.show', $transaction->account)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Transaction $transaction
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Transaction $transaction)
    {
        $this->authorize('view', $transaction);

        return view('app.transactions.show', compact('transaction'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Transaction $transaction
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Transaction $transaction)
    {
        $this->authorize('update', $transaction);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');

        return view('app.transactions.edit', compact('transaction', 'statuses'));
    }

    /**
     * @param \App\Http\Requests\Request $request
     * @param \App\Models\Transaction $transaction
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Transaction $transaction)
    {
        $this->authorize('update', $transaction);

        $validated = $request->all();

        $validated[$request->type] = $request->amount;
        $transaction->update($validated);

        return redirect()
            ->route('accounts.show', $transaction->account)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Transaction $transaction
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Transaction $transaction)
    {
        $this->authorize('delete', $transaction);

        if ($transaction->name) {
            Storage::delete($transaction->name);
        }

        $transaction->delete();

        return redirect()
            ->back()
            // ->route('transactions.index')
            ->withSuccess(__('crud.common.removed'));
    }


}
