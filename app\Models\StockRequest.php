<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Scopes\Searchable;

class StockRequest extends Model
{
    use HasFactory, SoftDeletes, Searchable;

    protected $fillable = [
        'request_number',
        'product_id',
        'product_name',
        'unit_id',
        'unit_name',
        'unit_quantity',
        'quantity_requested',
        'quantity_approved',
        'quantity_fulfilled',
        'from_warehouse_id',
        'to_warehouse_id',
        'branch_id',
        'business_type_id',
        'reason',
        'notes',
        'status',
        'priority',
        'needed_by',
        'requested_by',
        'approved_by',
        'approved_at',
        'fulfilled_by',
        'fulfilled_at',
        'created_by',
        'updated_by',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'unit_quantity' => 'decimal:2',
        'quantity_requested' => 'decimal:2',
        'quantity_approved' => 'decimal:2',
        'quantity_fulfilled' => 'decimal:2',
        'needed_by' => 'date',
        'approved_at' => 'datetime',
        'fulfilled_at' => 'datetime',
    ];

    protected $dates = [
        'needed_by',
        'approved_at',
        'fulfilled_at',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    // Relationships
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function fromWarehouse()
    {
        return $this->belongsTo(Warehouse::class, 'from_warehouse_id');
    }

    public function toWarehouse()
    {
        return $this->belongsTo(Warehouse::class, 'to_warehouse_id');
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function businessType()
    {
        return $this->belongsTo(BusinessType::class);
    }

    public function requestedBy()
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function fulfilledBy()
    {
        return $this->belongsTo(User::class, 'fulfilled_by');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    public function scopeFulfilled($query)
    {
        return $query->where('status', 'fulfilled');
    }

    public function scopeUrgent($query)
    {
        return $query->where('priority', 'urgent');
    }

    public function scopeOverdue($query)
    {
        return $query->where('needed_by', '<', now())
                    ->whereNotIn('status', ['fulfilled', 'cancelled', 'rejected']);
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pending' => 'bg-warning',
            'approved' => 'bg-success',
            'rejected' => 'bg-danger',
            'partially_fulfilled' => 'bg-info',
            'fulfilled' => 'bg-success',
            'cancelled' => 'bg-secondary',
        ];

        return $badges[$this->status] ?? 'bg-secondary';
    }

    public function getPriorityBadgeAttribute()
    {
        $badges = [
            'low' => 'bg-light text-dark',
            'medium' => 'bg-info',
            'high' => 'bg-warning',
            'urgent' => 'bg-danger',
        ];

        return $badges[$this->priority] ?? 'bg-secondary';
    }

    public function getIsOverdueAttribute()
    {
        return $this->needed_by &&
               $this->needed_by->isPast() &&
               !in_array($this->status, ['fulfilled', 'cancelled', 'rejected']);
    }

    public function getCompletionPercentageAttribute()
    {
        if ($this->quantity_requested <= 0) {
            return 0;
        }

        return round(($this->quantity_fulfilled / $this->quantity_requested) * 100, 2);
    }

    // Static methods
    public static function generateRequestNumber()
    {
        $prefix = 'SR';
        $date = now()->format('Ymd');
        $lastRequest = static::whereDate('created_at', today())
                           ->orderBy('id', 'desc')
                           ->first();

        $sequence = $lastRequest ? (int) substr($lastRequest->request_number, -4) + 1 : 1;

        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
