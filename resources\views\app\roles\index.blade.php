@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">shield-lock</x-slot>
        <x-slot name="title">User Roles</x-slot>
        <x-slot name="subtitle">Manage access control roles for system users</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item active" aria-current="page">Roles</li>
        </x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Role::class)
            <a href="{{ route('roles.create') }}" class="btn btn-primary btn-sm">
                <i class="bi bi-plus-circle me-1"></i> Create New Role
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Roles Table -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="card-header-title">
                        <i class="bi bi-table me-2"></i>System Roles
                    </h5>
                </div>
                <div class="col-auto">
                    <div class="d-flex gap-2">
                        <!-- Search -->
                        <form method="GET" action="{{ route('roles.index') }}" class="d-flex">
                            <div class="input-group input-group-sm">
                                <input type="text" name="search" class="form-control"
                                       placeholder="Search roles..." value="{{ $search }}"
                                       style="min-width: 200px;">
                                <button class="btn btn-outline-secondary" type="submit">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        @if($roles->count() > 0)
        <div class="table-responsive">
            <table class="table table-hover table-nowrap">
                <thead class="table-light">
                    <tr>
                        <th>Role Name</th>
                        <th>Type</th>
                        <th>Users</th>
                        <th>Permissions</th>
                        <th>Created</th>
                        <th class="text-end">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($roles as $role)
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-sm avatar-circle me-3">
                                    <span class="avatar-initials bg-primary">
                                        {{ strtoupper(substr($role->name, 0, 2)) }}
                                    </span>
                                </div>
                                <div>
                                    <h6 class="mb-0">{{ _formatText($role->name) }}</h6>
                                    <small class="text-muted">{{ $role->name }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            @if($role->name == 'super-admin')
                                <span class="badge bg-danger">System Role</span>
                            @else
                                <span class="badge bg-primary">Custom Role</span>
                            @endif
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-people me-2 text-muted"></i>
                                <span class="badge bg-soft-info">{{ $role->users->count() }}</span>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-shield-check me-2 text-muted"></i>
                                <span class="badge bg-soft-primary">{{ $role->permissions->count() }}</span>
                                @if($role->permissions->count() > 0)
                                <div class="ms-2">
                                    <div class="d-flex flex-wrap gap-1">
                                        @foreach($role->permissions->take(3) as $permission)
                                        <span class="badge bg-light text-dark" style="font-size: 0.7rem;">
                                            {{ ucfirst(str_replace(['_', '-'], ' ', $permission->name)) }}
                                        </span>
                                        @endforeach
                                        @if($role->permissions->count() > 3)
                                        <span class="badge bg-secondary" style="font-size: 0.7rem;">
                                            +{{ $role->permissions->count() - 3 }}
                                        </span>
                                        @endif
                                    </div>
                                </div>
                                @endif
                            </div>
                        </td>
                        <td>
                            <span class="text-muted">{{ $role->created_at->format('M d, Y') }}</span>
                            <br>
                            <small class="text-muted">{{ $role->created_at->diffForHumans() }}</small>
                        </td>
                        <td class="text-end">
                            <div class="d-flex gap-1 justify-content-end">
                                @can('view', $role)
                                <a href="{{ route('roles.show', $role) }}"
                                   class="btn btn-outline-primary btn-sm"
                                   title="View Role">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @endcan

                                @can('update', $role)
                                @if($role->name != 'super-admin')
                                <a href="{{ route('roles.edit', $role) }}"
                                   class="btn btn-outline-secondary btn-sm"
                                   title="Edit Role">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                @endif
                                @endcan

                                @can('delete', $role)
                                @if($role->name != 'super-admin')
                                <form action="{{ route('roles.destroy', $role) }}" method="POST" class="d-inline"
                                      onsubmit="return confirm('Are you sure you want to delete this role? This action cannot be undone.')">
                                    @csrf @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger btn-sm" title="Delete Role">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                @endif
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        @if($roles->hasPages())
        <div class="card-footer">
            {{ $roles->appends(request()->query())->links() }}
        </div>
        @endif

        @else
        <div class="card-body">
            <x-empty-state
                icon="shield-lock"
                title="No roles found"
                message="No roles match your search criteria. Try adjusting your search or create a new role."
                action="true"
                actionUrl="{{ route('roles.create') }}"
                actionText="Create First Role"
            />
        </div>
        @endif
    </div>

    <!-- Role Management Info -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-header-title">
                <i class="bi bi-info-circle me-2"></i>About Role Management
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3 mb-md-0">
                    <h6><i class="bi bi-shield me-2"></i>What are roles?</h6>
                    <p class="text-muted small">Roles are collections of permissions that define what actions users can perform in the system.</p>
                </div>
                <div class="col-md-4 mb-3 mb-md-0">
                    <h6><i class="bi bi-key me-2"></i>Managing permissions</h6>
                    <p class="text-muted small">Each role can be assigned specific permissions. Users inherit all permissions from their assigned roles.</p>
                </div>
                <div class="col-md-4">
                    <h6><i class="bi bi-lock me-2"></i>System roles</h6>
                    <p class="text-muted small">The "super-admin" role is a system role with full access that cannot be modified or deleted.</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
