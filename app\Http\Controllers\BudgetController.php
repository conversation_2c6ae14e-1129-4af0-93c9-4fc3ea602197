<?php

namespace App\Http\Controllers;

use App\Models\Account;
use App\Models\Budget;
use App\Models\BudgetItem;
use App\Models\FiscalYear;
use App\Models\FiscalPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BudgetController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Budget::class);

        $search = $request->get('search', '');
        $fiscalYearId = $request->get('fiscal_year_id', '');
        $status = $request->get('status', '');

        $budgets = Budget::search($search);
        
        if ($fiscalYearId) {
            $budgets->where('fiscal_year_id', $fiscalYearId);
        }
        
        if ($status) {
            $budgets->where('status', $status);
        }
        
        $budgets = $budgets->latest()
            ->paginate()
            ->withQueryString();

        $fiscalYears = FiscalYear::orderBy('name')
            ->pluck('name', 'id');

        return view('app.budgets.index', compact(
            'budgets', 
            'search', 
            'fiscalYears',
            'fiscalYearId',
            'status'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', Budget::class);

        $fiscalYears = FiscalYear::orderBy('name')
            ->pluck('name', 'id');

        return view('app.budgets.create', compact('fiscalYears'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', Budget::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'fiscal_year_id' => 'required|exists:fiscal_years,id',
            'status' => 'required|in:draft,approved,active,closed',
            'version' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        // Check if there's already an active budget for this fiscal year
        if ($validated['is_active'] ?? false) {
            $activeBudget = Budget::where('fiscal_year_id', $validated['fiscal_year_id'])
                ->where('is_active', true)
                ->first();
                
            if ($activeBudget) {
                return redirect()
                    ->back()
                    ->withInput()
                    ->withError('There is already an active budget for this fiscal year.');
            }
        }

        $budget = Budget::create($validated);

        return redirect()
            ->route('budgets.edit', $budget)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Budget  $budget
     * @return \Illuminate\Http\Response
     */
    public function show(Budget $budget)
    {
        $this->authorize('view', $budget);

        $budgetItems = $budget->budgetItems()
            ->with(['account', 'fiscalPeriod'])
            ->get();
            
        // Group budget items by account
        $accountBudgets = [];
        foreach ($budgetItems as $item) {
            $accountId = $item->account_id;
            if (!isset($accountBudgets[$accountId])) {
                $accountBudgets[$accountId] = [
                    'account' => $item->account,
                    'total' => 0,
                    'periods' => [],
                ];
            }
            
            $accountBudgets[$accountId]['total'] += $item->amount;
            
            if ($item->fiscalPeriod) {
                $accountBudgets[$accountId]['periods'][$item->fiscal_period_id] = [
                    'period' => $item->fiscalPeriod,
                    'amount' => $item->amount,
                ];
            }
        }
        
        // Get fiscal periods for this fiscal year
        $fiscalPeriods = FiscalPeriod::where('fiscal_year_id', $budget->fiscal_year_id)
            ->orderBy('start_date')
            ->get();

        return view('app.budgets.show', compact(
            'budget', 
            'accountBudgets',
            'fiscalPeriods'
        ));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Budget  $budget
     * @return \Illuminate\Http\Response
     */
    public function edit(Budget $budget)
    {
        $this->authorize('update', $budget);
        
        if ($budget->status === 'closed') {
            return redirect()
                ->route('budgets.show', $budget)
                ->withError('Closed budgets cannot be edited.');
        }

        $fiscalYears = FiscalYear::orderBy('name')
            ->pluck('name', 'id');

        return view('app.budgets.edit', compact('budget', 'fiscalYears'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Budget  $budget
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Budget $budget)
    {
        $this->authorize('update', $budget);
        
        if ($budget->status === 'closed') {
            return redirect()
                ->route('budgets.show', $budget)
                ->withError('Closed budgets cannot be edited.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:draft,approved,active,closed',
            'version' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        // Check if there's already an active budget for this fiscal year
        if (($validated['is_active'] ?? false) && !$budget->is_active) {
            $activeBudget = Budget::where('fiscal_year_id', $budget->fiscal_year_id)
                ->where('is_active', true)
                ->where('id', '!=', $budget->id)
                ->first();
                
            if ($activeBudget) {
                return redirect()
                    ->back()
                    ->withInput()
                    ->withError('There is already an active budget for this fiscal year.');
            }
        }
        
        // If status is changing to approved, set approved_by and approved_at
        if ($validated['status'] === 'approved' && $budget->status !== 'approved') {
            $validated['approved_by'] = auth()->id();
            $validated['approved_at'] = now();
        }

        $budget->update($validated);

        return redirect()
            ->route('budgets.edit', $budget)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Budget  $budget
     * @return \Illuminate\Http\Response
     */
    public function destroy(Budget $budget)
    {
        $this->authorize('delete', $budget);
        
        if ($budget->status !== 'draft') {
            return redirect()
                ->route('budgets.index')
                ->withError('Only draft budgets can be deleted.');
        }

        DB::beginTransaction();
        
        try {
            // Delete budget items
            $budget->budgetItems()->delete();
            
            // Delete budget
            $budget->delete();
            
            DB::commit();
            
            return redirect()
                ->route('budgets.index')
                ->withSuccess(__('crud.common.removed'));
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
    
    /**
     * Show the form for editing budget items.
     *
     * @param  \App\Models\Budget  $budget
     * @return \Illuminate\Http\Response
     */
    public function editItems(Budget $budget)
    {
        $this->authorize('update', $budget);
        
        if ($budget->status === 'closed') {
            return redirect()
                ->route('budgets.show', $budget)
                ->withError('Closed budgets cannot be edited.');
        }
        
        $accounts = Account::where('is_active', true)
            ->orderBy('code')
            ->get()
            ->pluck('full_name', 'id');
            
        $fiscalPeriods = FiscalPeriod::where('fiscal_year_id', $budget->fiscal_year_id)
            ->orderBy('start_date')
            ->get();
            
        $budgetItems = $budget->budgetItems()
            ->with(['account', 'fiscalPeriod'])
            ->get();
            
        // Group budget items by account and period
        $budgetData = [];
        foreach ($budgetItems as $item) {
            $accountId = $item->account_id;
            $periodId = $item->fiscal_period_id;
            
            if (!isset($budgetData[$accountId])) {
                $budgetData[$accountId] = [
                    'account' => $item->account,
                    'periods' => [],
                ];
            }
            
            $budgetData[$accountId]['periods'][$periodId] = [
                'id' => $item->id,
                'amount' => $item->amount,
            ];
        }

        return view('app.budgets.edit_items', compact(
            'budget', 
            'accounts',
            'fiscalPeriods',
            'budgetData'
        ));
    }
    
    /**
     * Update budget items.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Budget  $budget
     * @return \Illuminate\Http\Response
     */
    public function updateItems(Request $request, Budget $budget)
    {
        $this->authorize('update', $budget);
        
        if ($budget->status === 'closed') {
            return redirect()
                ->route('budgets.show', $budget)
                ->withError('Closed budgets cannot be edited.');
        }

        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.account_id' => 'required|exists:accounts,id',
            'items.*.periods' => 'required|array',
            'items.*.periods.*.period_id' => 'required|exists:fiscal_periods,id',
            'items.*.periods.*.amount' => 'required|numeric|min:0',
        ]);

        DB::beginTransaction();
        
        try {
            foreach ($validated['items'] as $accountItem) {
                $accountId = $accountItem['account_id'];
                
                foreach ($accountItem['periods'] as $periodItem) {
                    $periodId = $periodItem['period_id'];
                    $amount = $periodItem['amount'];
                    
                    // Skip zero amounts
                    if ($amount == 0) {
                        continue;
                    }
                    
                    // Check if budget item already exists
                    $budgetItem = BudgetItem::where('budget_id', $budget->id)
                        ->where('account_id', $accountId)
                        ->where('fiscal_period_id', $periodId)
                        ->first();
                        
                    if ($budgetItem) {
                        // Update existing item
                        $budgetItem->update([
                            'amount' => $amount,
                        ]);
                    } else {
                        // Create new item
                        BudgetItem::create([
                            'budget_id' => $budget->id,
                            'account_id' => $accountId,
                            'fiscal_period_id' => $periodId,
                            'amount' => $amount,
                        ]);
                    }
                }
            }
            
            DB::commit();
            
            return redirect()
                ->route('budgets.show', $budget)
                ->withSuccess('Budget items have been updated successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
    
    /**
     * Generate budget vs actual report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function report(Request $request)
    {
        $this->authorize('view-any', Budget::class);

        $validated = $request->validate([
            'budget_id' => 'required|exists:budgets,id',
            'fiscal_period_id' => 'nullable|exists:fiscal_periods,id',
            'format' => 'required|in:html,pdf,csv',
        ]);

        $budget = Budget::findOrFail($validated['budget_id']);
        $fiscalPeriodId = $validated['fiscal_period_id'] ?? null;
        
        // Get budget items
        $budgetItemsQuery = $budget->budgetItems()
            ->with(['account', 'fiscalPeriod']);
            
        if ($fiscalPeriodId) {
            $budgetItemsQuery->where('fiscal_period_id', $fiscalPeriodId);
        }
        
        $budgetItems = $budgetItemsQuery->get();
        
        // Group budget items by account
        $reportData = [];
        foreach ($budgetItems as $item) {
            $accountId = $item->account_id;
            
            if (!isset($reportData[$accountId])) {
                $reportData[$accountId] = [
                    'account' => $item->account,
                    'budget' => 0,
                    'actual' => 0,
                    'variance' => 0,
                    'variance_percent' => 0,
                ];
            }
            
            $reportData[$accountId]['budget'] += $item->amount;
        }
        
        // Get actual amounts
        foreach ($reportData as $accountId => &$data) {
            $account = $data['account'];
            
            // Get journal entry lines for this account
            $query = $account->journalEntryLines()
                ->whereHas('journalEntry', function ($query) use ($budget, $fiscalPeriodId) {
                    $query->where('status', 'posted')
                        ->where('fiscal_year_id', $budget->fiscal_year_id);
                        
                    if ($fiscalPeriodId) {
                        $query->where('fiscal_period_id', $fiscalPeriodId);
                    }
                });
                
            $debitSum = $query->sum('debit');
            $creditSum = $query->sum('credit');
            
            // Calculate actual amount based on normal balance
            if ($account->normal_balance === 'debit') {
                $data['actual'] = $debitSum - $creditSum;
            } else {
                $data['actual'] = $creditSum - $debitSum;
            }
            
            // Calculate variance
            $data['variance'] = $data['budget'] - $data['actual'];
            
            // Calculate variance percentage
            if ($data['budget'] != 0) {
                $data['variance_percent'] = ($data['variance'] / $data['budget']) * 100;
            }
        }
        
        $fiscalPeriod = null;
        if ($fiscalPeriodId) {
            $fiscalPeriod = FiscalPeriod::find($fiscalPeriodId);
        }
        
        $data = [
            'budget' => $budget,
            'fiscalPeriod' => $fiscalPeriod,
            'reportData' => $reportData,
        ];
        
        if ($validated['format'] === 'html') {
            return view('app.budgets.report', $data);
        } elseif ($validated['format'] === 'pdf') {
            $pdf = PDF::loadView('app.budgets.report_pdf', $data);
            return $pdf->download('budget_report_' . date('Y-m-d') . '.pdf');
        } else {
            // CSV format
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="budget_report_' . date('Y-m-d') . '.csv"',
            ];
            
            $callback = function() use ($reportData) {
                $file = fopen('php://output', 'w');
                fputcsv($file, ['Account Code', 'Account Name', 'Budget', 'Actual', 'Variance', 'Variance %']);
                
                foreach ($reportData as $data) {
                    fputcsv($file, [
                        $data['account']->code,
                        $data['account']->name,
                        $data['budget'],
                        $data['actual'],
                        $data['variance'],
                        $data['variance_percent'],
                    ]);
                }
                
                fclose($file);
            };
            
            return response()->stream($callback, 200, $headers);
        }
    }
}
