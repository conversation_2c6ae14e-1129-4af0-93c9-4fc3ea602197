@extends('layouts.app')

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">credit-card</x-slot>
        <x-slot name="title">Make Payment</x-slot>
        <x-slot name="subtitle">Make payment to {{ $supplier->name }}</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('payables.index') }}">Payables</a></li>
            <li class="breadcrumb-item"><a href="{{ route('payables.show', $supplier) }}">{{ $supplier->name }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">Make Payment</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('payables.show', $supplier) }}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-left me-1"></i>Back to Supplier
            </a>
        </x-slot>
    </x-page-header>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Supplier Info Card -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avatar avatar-lg avatar-circle me-3">
                            <span class="avatar-initials bg-warning text-white h5 mb-0">
                                {{ substr($supplier->name, 0, 1) }}
                            </span>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-1">{{ $supplier->name }}</h5>
                            @if($supplier->company)
                                <p class="text-muted mb-0">{{ $supplier->company }}</p>
                            @endif
                        </div>
                        <div class="text-end">
                            <div class="h4 text-danger mb-0">{{ _money($orders->sum('balance')) }}</div>
                            <small class="text-muted">Total Outstanding</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-credit-card me-2"></i>Payment Information
                    </h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('payables.store-payment', $supplier) }}" method="POST" id="paymentForm">
                        @csrf

                        <!-- Order Selection -->
                        <div class="mb-4">
                            <label class="form-label">Select Bill/Order <span class="text-danger">*</span></label>
                            <div class="tom-select-custom">
                                <select class="js-select form-select" name="order_id" id="orderSelect" 
                                        autocomplete="off" data-hs-tom-select-options='{"placeholder": "Select bill to pay..."}' required>
                                    <option value="">Select Bill/Order</option>
                                    @foreach($orders as $order)
                                        <option value="{{ $order->id }}" 
                                                data-total="{{ $order->amount_total }}"
                                                data-paid="{{ $order->amount_paid }}"
                                                data-balance="{{ $order->balance }}"
                                                data-date="{{ $order->created_at->format('M d, Y') }}"
                                                data-due="{{ isset($order->due_date) && $order->due_date ? $order->due_date->format('M d, Y') : '' }}"
                                                {{ request('order_id') == $order->id ? 'selected' : '' }}>
                                            Order #{{ $order->id }} - {{ $order->created_at->format('M d, Y') }} 
                                            (Balance: {{ _money($order->balance) }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            @error('order_id')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Order Details Card -->
                        <div class="card bg-light mb-4" id="orderDetailsCard" style="display: none;">
                            <div class="card-body">
                                <h6 class="card-title">Bill/Order Details</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <strong>Order Date:</strong> <span id="orderDate">-</span>
                                        </div>
                                        <div class="mb-2">
                                            <strong>Due Date:</strong> <span id="dueDate">-</span>
                                        </div>
                                        <div class="mb-2">
                                            <strong>Total Amount:</strong> <span id="totalAmount">-</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <strong>Amount Paid:</strong> <span id="paidAmount">-</span>
                                        </div>
                                        <div class="mb-2">
                                            <strong>Outstanding Balance:</strong> <span id="outstandingBalance" class="text-danger fw-bold">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Amount -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">Payment Amount <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control @error('amount') is-invalid @enderror" 
                                           name="amount" id="paymentAmount" step="0.01" min="0.01" 
                                           value="{{ old('amount') }}" placeholder="0.00" required>
                                </div>
                                @error('amount')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Maximum: <span id="maxAmount">-</span></small>
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">Currency</label>
                                <div class="tom-select-custom">
                                    <select class="js-select form-select" name="currency_id" autocomplete="off" 
                                            data-hs-tom-select-options='{"placeholder": "Select currency..."}'>
                                        <option value="">Default Currency</option>
                                        @foreach($currencies as $currency)
                                            <option value="{{ $currency->id }}" {{ old('currency_id') == $currency->id ? 'selected' : '' }}>
                                                {{ $currency->name }} ({{ $currency->code }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                @error('currency_id')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Reference and Payment Method -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">Reference Number</label>
                                <input type="text" class="form-control @error('reference_no') is-invalid @enderror" 
                                       name="reference_no" value="{{ old('reference_no') }}" 
                                       placeholder="Payment reference number">
                                @error('reference_no')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">Payment Method</label>
                                <div class="tom-select-custom">
                                    <select class="js-select form-select" name="payment_method" autocomplete="off" 
                                            data-hs-tom-select-options='{"placeholder": "Select payment method..."}'>
                                        <option value="">Select Method</option>
                                        <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>Cash</option>
                                        <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                                        <option value="credit_card" {{ old('payment_method') == 'credit_card' ? 'selected' : '' }}>Credit Card</option>
                                        <option value="debit_card" {{ old('payment_method') == 'debit_card' ? 'selected' : '' }}>Debit Card</option>
                                        <option value="check" {{ old('payment_method') == 'check' ? 'selected' : '' }}>Check</option>
                                        <option value="mobile_payment" {{ old('payment_method') == 'mobile_payment' ? 'selected' : '' }}>Mobile Payment</option>
                                        <option value="other" {{ old('payment_method') == 'other' ? 'selected' : '' }}>Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <label class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      name="description" rows="3" 
                                      placeholder="Payment description or notes">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Comments -->
                        <div class="mb-4">
                            <label class="form-label">Internal Comments</label>
                            <textarea class="form-control @error('comment') is-invalid @enderror" 
                                      name="comment" rows="2" 
                                      placeholder="Internal comments (not visible to supplier)">{{ old('comment') }}</textarea>
                            @error('comment')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="{{ route('payables.show', $supplier) }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="bi bi-credit-card me-1"></i>Make Payment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize Tom Select dropdowns
    if (typeof HSCore !== 'undefined' && HSCore.components && HSCore.components.HSTomSelect) {
        HSCore.components.HSTomSelect.init('.js-select');
    }

    // Order selection change handler
    $('#orderSelect').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        
        if (selectedOption.val()) {
            const total = selectedOption.data('total');
            const paid = selectedOption.data('paid');
            const balance = selectedOption.data('balance');
            const date = selectedOption.data('date');
            const due = selectedOption.data('due');
            
            $('#orderDate').text(date);
            $('#dueDate').text(due || 'Not set');
            $('#totalAmount').text('$' + parseFloat(total).toFixed(2));
            $('#paidAmount').text('$' + parseFloat(paid).toFixed(2));
            $('#outstandingBalance').text('$' + parseFloat(balance).toFixed(2));
            $('#maxAmount').text('$' + parseFloat(balance).toFixed(2));
            
            // Set max amount for payment input
            $('#paymentAmount').attr('max', balance);
            
            $('#orderDetailsCard').show();
        } else {
            $('#orderDetailsCard').hide();
        }
    });

    // Payment amount validation
    $('#paymentAmount').on('input', function() {
        const amount = parseFloat($(this).val());
        const maxAmount = parseFloat($(this).attr('max'));
        
        if (amount > maxAmount) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
            $(this).after('<div class="invalid-feedback">Amount cannot exceed outstanding balance</div>');
        } else {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        }
    });

    // Auto-trigger if order is pre-selected
    if ($('#orderSelect').val()) {
        $('#orderSelect').trigger('change');
    }
});
</script>
@endpush
@endsection
