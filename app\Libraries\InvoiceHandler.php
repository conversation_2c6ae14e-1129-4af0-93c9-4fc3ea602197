<?php
namespace App\Libraries;


use App\Models\Product;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Invoice;
use App\Models\Sale;
use App\Models\User;
use App\Models\Unit;
use App\Models\Payment;
use Carbon\Carbon;
/**
 * 
 */
class InvoiceHandler 
{

    public function saveSales($invoice){

        foreach ( _array(request()->products) as $key => $product_id){
            $product = Product::where("id", "$product_id")->first();
            $quantity = _from( request()->quantities, $key) ?? 0;
            $discount = $product->discount;
            $price = $product->price;
            $params['product_id'] = $product_id;
            $params['product_name'] = $product->name;
            $params['quantity'] = _from( request()->quantities, $key) ?? '';
            $params['discount'] = _from( request()->discounts, $key) ?? '';
            $params['unit_id'] = $product->unit_id;
            $params['price'] = $product->price;
            $params['discount'] = $product->discount;
            $params['unit_name'] = $product->unit->name ?? '';
            $sale = $invoice->sales()->create($params);
            $params = [
                "invoice_id" => $invoice->id, 
                "selling_price" => $price, 
                "discount" => $discount, 
            ];
            $stockItems = $product->stockItems()->whereNull("invoice_id")->limit($quantity)->update($params);
        }
    }

    public function addPayment($instance, $params){
        $payment = $instance->payments()->create($params);
        return $payment;
    }
    
    public function saveInvoice()
    {

        $data = _array( request()->invoice );
        $invoice = Invoice::create( $data );
        $sales  = _array( _from($data, "sales") );


        foreach ($sales as $sale) {
            if( request()->customer_id ){
                $customer = Customer::find( request()->customer_id );
                if( $customer ) {
                    $data["customer_id"] = $customer->id;    
                    $data["customer_name"] = $customer->name;    
                    $data["description"] = $customer->name;    
                } else {
                    $data["customer_name"] = request()->customer_id;   
                    $data["description"] = request()->customer_id;   
                }
            }
            $product = Product::find( _from($sale, "product_id") );
            $unit = Unit::find( _from($sale, "unit_id") );
            $sale['unit_quantity'] = $unit->quantity ?? 1 ;
            $sale['unit_name'] = $unit->name ?? '' ;
            $verifyQuantity = $product->total_stock / $sale['unit_quantity'];

            if($verifyQuantity < _from($sale, "quantity") ) {
                return ["error" => "$product->name exceed available stock: " . $verifyQuantity ];
            }

            if(  _from($sale, "product_id") ){
                $invoice->sales()->create($sale);
                if( $invoice->amount_total > $invoice->amount_paid || request()->discount > 0 ){
                    
                    $invoice->update(["status_id" => 11 ]);

                } else {
                    
                    $invoice->update([
                        "approved_by" => auth()->id(),
                        "status_id" => 10
                    ]);
                    $quantity = _from($sale, "quantity") * _from($sale, "unit_quantity");

                    if( $product ){
                        // Create negative stock entries for sale (FIFO)
                        self::createSaleStockEntries($product, $quantity, $sale, $invoice);
                    }
                }
            }

        }

        if( $invoice->amount_total > $invoice->amount_paid){
            $balance = $invoice->amount_total - $invoice->amount_paid;
        } else {
            $balance = 0;
        }

        $invoice->payments()->delete();
        $this->addPayment($invoice, [
            "amount" => request()->amount_total,
            "balance" => $balance,
        ]);

        return $invoice;
    } 

    public function updateInvoice($invoice)
    {
    
        $data = _array( request()->invoice );
        $params = $data;
        unset($params['created_by']);
        unset($params['sales']);
        unset($params['payments']);

        if( request()->customer_id ){
            $customer = Customer::find( request()->customer_id );
            if( $customer ) {
                $data["customer_id"] = $customer->id;    
                $data["customer_name"] = $customer->name;    
                $data["description"] = $customer->name;    
            } else {
                $data["customer_name"] = request()->customer_id;   
                $data["description"] = request()->customer_id;   
            }
        }

        $params["status_id"] = 11;
        $invoice->update($params);        
        $invoice->payments()->delete();
        $this->addPayment($invoice, [
            "amount" => $invoice->amount_paid,
            "balance" => $invoice->balance
        ]);

        $sales  = _array( _from($data, "sales") );
        foreach ($sales as $params) {
            if( _from($params, "product_id") ){
                $sale = Sale::where("id", _from($params, "id") )->first();
                unset($params['']);
                // dd($params);
                if ( $sale) {
                    $sale->update($params);
                } else {
                    $invoice->sales()->create($params);
                }
            }
        }

        return $invoice;
    }


    public function approveInvoice($invoice){
        $params = request()->all();
        $params['approved_by'] = auth()->id();
        $invoice->update($params);
        if( _from($params, "status_id" ) == 10 ){
            foreach ($invoice->sales as $sale) {
                $product = Product::where("id", $sale->product_id )->first();
                $quantity = $sale->quantity * $sale->unit_quantity;
                if( $product ){
                    // Create negative stock entries for sale (FIFO)
                    self::createSaleStockEntries($product, $quantity, $sale, $invoice);
                }
            }
        }
        return $invoice;
    }


    public function havePaidInvoiceInFull($invoice){
        $params = request()->all();
        $params['status_id'] = 10;
        $params['approved_by'] = auth()->id();
        $params['amount_paid'] = $invoice->amount_total;

        $alreadPaid = $invoice->payments()->sum("amount");

        $this->addPayment($invoice, [
            "amount" => $invoice->amount_total - $alreadPaid,
            "balance" => 0,
        ]);

        $invoice->update($params);
        if( _from($params, "status_id" ) == 10 ){
            foreach ($invoice->sales as $sale) {
                $product = Product::where("id", $sale->product_id )->first();
                $quantity = $sale->quantity * $sale->unit_quantity;
                if( $product ){
                    // Create negative stock entries for sale (FIFO)
                    self::createSaleStockEntries($product, $quantity, $sale, $invoice);
                }
            }
        }
        return $invoice;
    }

    /**
     * Create negative stock entries for sales using FIFO method
     */
    public static function createSaleStockEntries($product, $quantityNeeded, $sale, $invoice)
    {
        // Get available stocks for this product (FIFO order - oldest first)
        $availableStocks = \App\Models\Stock::where('product_id', $product->id)
                                           ->where('quantity', '>', 0)
                                           ->where(function($query) {
                                               $query->where('description', 'like', 'STOCK_PURCHASE%')
                                                     ->orWhere('description', 'like', 'STOCK_TRANSFER_IN%')
                                                     ->orWhere('description', 'like', '%Transfer IN%')
                                                     ->orWhere('description', 'like', '%Purchase%')
                                                     ->orWhereNull('stock_id'); // Original stock records
                                           })
                                           ->when(!auth()->user()->isSuperAdmin(), function($query) {
                                               $query->where('branch_id', auth()->user()->branch_id);
                                           })
                                           ->orderBy('expires_at', 'asc') // FIFO by expiry date first
                                           ->orderBy('created_at', 'asc') // Then by creation date
                                           ->get();

        $totalAvailable = $availableStocks->sum('quantity');

        // Validate sale quantity
        if ($quantityNeeded > $totalAvailable) {
            throw new \Exception("Sale quantity for {$product->name} ({$quantityNeeded}) exceeds available stock ({$totalAvailable}).");
        }

        // Allocate sale quantity from available stocks (FIFO)
        $remainingToSell = $quantityNeeded;
        $saleStockEntries = [];

        foreach ($availableStocks as $stock) {
            if ($remainingToSell <= 0) break;

            $quantityFromThisStock = min($remainingToSell, $stock->quantity);

            if($stock->balance >= $remainingToSell){
                $remainingToSell = 0;
            } else {
                $remainingToSell = $remainingToSell - $stock->balance;
            }

            // Create negative stock entry for sale (STOCK_SALE_OUT)
            $saleOut = \App\Models\Stock::create([
                'product_id' => $stock->product_id,
                'product_name' => $stock->product_name,
                'unit_id' => $sale->unit_id ?? $stock->unit_id,
                'unit_name' => $sale->unit_name ?? $stock->unit_name,
                'unit_quantity' => $sale->unit_quantity ?? $stock->unit_quantity,
                'quantity' => -$quantityFromThisStock, // Negative for sale
                'balance' => -$quantityFromThisStock,
                'buying_price' => $stock->buying_price,
                'selling_price' => $sale->selling_price ?? $stock->selling_price,
                'supplier_id' => $stock->supplier_id,
                'branch_id' => $stock->branch_id,
                'warehouse_id' => $stock->warehouse_id,
                'stock_id' => $stock->id, // Reference to original stock
                'expires_at' => $stock->expires_at, // Maintain expiry date
                'description' => 'STOCK_SALE_OUT: Invoice #' . $invoice->id . ' - ' . $product->name,
                'created_by' => auth()->id(),
                'business_type_id' => auth()->user()->business_type_id,
            ]);

            // Update original stock quantity
            $stock->update([
                'balance' => $remainingToSell ? 0 : $stock->balance - $quantityFromThisStock,
            ]);

            // Update stock items to link to invoice
            $stockItemsToUpdate = $stock->stockItems()
                                       ->whereNull('invoice_id')
                                       ->limit($quantityFromThisStock)
                                       ->get();

            foreach ($stockItemsToUpdate as $stockItem) {
                $stockItem->update([
                    'invoice_id' => $invoice->id,
                    'selling_price' => $sale->selling_price ?? $stockItem->selling_price,
                    'stock_id' => $saleOut->id, // Link to sale stock record
                ]);
            }

            $saleStockEntries[] = [
                'original_stock_id' => $stock->id,
                'sale_stock_id' => $saleOut->id,
                'quantity_sold' => $quantityFromThisStock,
                'from_warehouse' => $stock->warehouse->name ?? 'Unknown',
                'expires_at' => $stock->expires_at
            ];

            $remainingToSell -= $quantityFromThisStock;
        }

        return $saleStockEntries;
    }

}