@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row">
        <!-- Employee Profile Card -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body text-center">
                    <div class="avatar avatar-xl mx-auto mb-3">
                        <x-partials.thumbnail src="{{ $employee->image ?? '' }}" />
                    </div>
                    <h5 class="card-title">{{ $employee->name }}</h5>
                    <p class="text-muted">{{ $employee->grade ?? 'Employee' }}</p>
                    <p class="text-muted">Basic Pay: <strong>{{ number_format($employee->basic_pay ?? 0, 2) }}</strong></p>

                    <div class="d-flex justify-content-center gap-2">
                        @can('update', $employee)
                        <a href="{{ route('employees.edit', $employee) }}" class="btn btn-primary btn-sm">
                            <i class="bi bi-pencil"></i> Edit
                        </a>
                        @endcan
                        <a href="{{ route('employee-deduction-contributions.index', ['employee_id' => $employee->id]) }}" class="btn btn-secondary btn-sm">
                            <i class="bi bi-person-lines-fill"></i> Deductions
                        </a>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Contact Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Email:</strong><br>
                        {{ $employee->email ?? 'Not provided' }}
                    </div>
                    <div class="mb-2">
                        <strong>Phone:</strong><br>
                        {{ $employee->phone ?? 'Not provided' }}
                    </div>
                    <div class="mb-2">
                        <strong>Address:</strong><br>
                        {{ $employee->address ?? 'Not provided' }}
                    </div>
                </div>
            </div>

            <!-- Emergency Contact -->
            @if($employee->emergency_phone || $employee->emergency_address)
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Emergency Contact</h6>
                </div>
                <div class="card-body">
                    @if($employee->emergency_phone)
                    <div class="mb-2">
                        <strong>Phone:</strong><br>
                        {{ $employee->emergency_phone }}
                    </div>
                    @endif
                    @if($employee->emergency_address)
                    <div class="mb-2">
                        <strong>Address:</strong><br>
                        {{ $employee->emergency_address }}
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <!-- Employee Details -->
        <div class="col-lg-8">
            <!-- Employment Information -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Employment Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>Employee ID:</strong><br>
                                #{{ $employee->id }}
                            </div>
                            <div class="mb-3">
                                <strong>Grade/Position:</strong><br>
                                {{ $employee->grade ?? 'Not specified' }}
                            </div>
                            <div class="mb-3">
                                <strong>Basic Pay:</strong><br>
                                {{ number_format($employee->basic_pay ?? 0, 2) }}
                            </div>
                            <div class="mb-3">
                                <strong>Gender:</strong><br>
                                {{ $employee->gender ?? 'Not specified' }}
                            </div>
                            <div class="mb-3">
                                <strong>Date of Birth:</strong><br>
                                @if($employee->date_of_birth)
                                    {{ $employee->date_of_birth->format('d M Y') }}
                                    @if($employee->age)
                                        <small class="text-muted">({{ $employee->age }} years old)</small>
                                    @endif
                                @else
                                    <span class="text-muted">Not specified</span>
                                @endif
                            </div>
                            <div class="mb-3">
                                <strong>Marital Status:</strong><br>
                                {{ $employee->marital_status ?? 'Not specified' }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>User Account:</strong><br>
                                @if($employee->user)
                                    <span class="badge bg-success">Linked to {{ $employee->user->name }}</span>
                                @else
                                    <span class="badge bg-secondary">No user account</span>
                                @endif
                            </div>
                            <div class="mb-3">
                                <strong>Created:</strong><br>
                                {{ $employee->created_at ? $employee->created_at->format('d M Y') : 'Unknown' }}
                            </div>
                            <div class="mb-3">
                                <strong>Status:</strong><br>
                                @if($employee->deleted_at)
                                    <span class="badge bg-danger">Inactive</span>
                                @else
                                    <span class="badge bg-success">Active</span>
                                @endif
                            </div>
                        </div>
                    </div>

                    @if($employee->description)
                    <div class="mt-3">
                        <strong>Job Description:</strong><br>
                        <p class="text-muted">{{ $employee->description }}</p>
                    </div>
                    @endif

                    @if($employee->medical_description)
                    <div class="mt-3">
                        <strong>Medical Information:</strong><br>
                        <p class="text-muted">{{ $employee->medical_description }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Recent Payroll Records -->
            <div class="card mt-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">Recent Payroll Records</h6>
                    <a href="{{ route('payroll.index', ['employee_id' => $employee->id]) }}" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    @if($recentPayrolls->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Period</th>
                                    <th>Basic Pay</th>
                                    <th>Net Pay</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentPayrolls as $payroll)
                                <tr>
                                    <td>{{ $payroll->date_from ? $payroll->date_from->format('M Y') : '-' }}</td>
                                    <td>{{ number_format($payroll->basic_pay, 2) }}</td>
                                    <td>{{ number_format($payroll->net_pay, 2) }}</td>
                                    <td>
                                        @if($payroll->approved_by)
                                            <span class="badge bg-success">Approved</span>
                                        @else
                                            <span class="badge bg-warning">Pending</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <p class="text-muted text-center">No payroll records found.</p>
                    @endif
                </div>
            </div>

            <!-- Active Deductions and Contributions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Active Deductions & Contributions</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Deductions</h6>
                            @if($activeDeductions->count() > 0)
                            <ul class="list-unstyled">
                                @foreach($activeDeductions as $deduction)
                                <li class="mb-1">
                                    <span class="badge bg-danger">{{ $deduction->deductionContribution->name }}</span>
                                </li>
                                @endforeach
                            </ul>
                            @else
                            <p class="text-muted">No active deductions</p>
                            @endif
                        </div>
                        <div class="col-md-6">
                            <h6>Contributions</h6>
                            @if($activeContributions->count() > 0)
                            <ul class="list-unstyled">
                                @foreach($activeContributions as $contribution)
                                <li class="mb-1">
                                    <span class="badge bg-success">{{ $contribution->deductionContribution->name }}</span>
                                </li>
                                @endforeach
                            </ul>
                            @else
                            <p class="text-muted">No active contributions</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ route('employees.index') }}" class="btn btn-light">
            <i class="bi bi-arrow-left"></i> Back to Employees
        </a>
    </div>
</div>
@endsection
