<?php
require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Hash;

// Create roles
$adminRole = Role::firstOrCreate(['name' => 'super-admin']);
$accountantRole = Role::firstOrCreate(['name' => 'accountant']);
$financeManagerRole = Role::firstOrCreate(['name' => 'finance-manager']);
$assetManagerRole = Role::firstOrCreate(['name' => 'asset-manager']);
$taxManagerRole = Role::firstOrCreate(['name' => 'tax-manager']);
$budgetManagerRole = Role::firstOrCreate(['name' => 'budget-manager']);
$productionManagerRole = Role::firstOrCreate(['name' => 'production-manager']);

echo "Created roles\n";

// Create admin user
$admin = User::firstOrCreate(
    ['email' => '<EMAIL>'],
    [
        'name' => 'Admin User',
        'password' => Hash::make('password'),
        'status_id' => 1,
    ]
);

echo "Created admin user\n";

// Assign role to admin
$admin->assignRole($adminRole);

echo "Assigned admin role to admin user\n";

// Create accountant user
$accountant = User::firstOrCreate(
    ['email' => '<EMAIL>'],
    [
        'name' => 'Accountant User',
        'password' => Hash::make('password'),
        'status_id' => 1,
    ]
);

echo "Created accountant user\n";

// Assign role to accountant
$accountant->assignRole($accountantRole);

echo "Assigned accountant role to accountant user\n";

echo "User creation complete!\n";
