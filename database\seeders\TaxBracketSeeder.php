<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\TaxBracket;

class TaxBracketSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions for tax brackets
        $permissions = [
            'view tax brackets',
            'create tax brackets',
            'update tax brackets',
            'delete tax brackets',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permissions to admin role
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminRole->givePermissionTo($permissions);
        }

        // Assign view permission to manager role
        $managerRole = Role::where('name', 'manager')->first();
        if ($managerRole) {
            $managerRole->givePermissionTo(['view tax brackets']);
        }

        // Create default tax brackets (Zambian PAYE tax structure)
        $defaultBrackets = [
            [
                'name' => 'Tax Free Threshold',
                'min_amount' => 0,
                'max_amount' => 100000,
                'rate' => 0.0, // 0%
                'order' => 1,
                'is_active' => true,
                'description' => 'First K100,000 is tax-free',
            ],
            [
                'name' => 'Second Bracket',
                'min_amount' => 100000,
                'max_amount' => 450000,
                'rate' => 0.25, // 25%
                'order' => 2,
                'is_active' => true,
                'description' => '25% tax on income from K100,001 to K450,000',
            ],
            [
                'name' => 'Third Bracket',
                'min_amount' => 450000,
                'max_amount' => 2500000,
                'rate' => 0.30, // 30%
                'order' => 3,
                'is_active' => true,
                'description' => '30% tax on income from K450,001 to K2,500,000',
            ],
            [
                'name' => 'Fourth Bracket',
                'min_amount' => 2500000,
                'max_amount' => 5000000,
                'rate' => 0.35, // 35%
                'order' => 4,
                'is_active' => true,
                'description' => '35% tax on income from K2,500,001 to K5,000,000',
            ],
            [
                'name' => 'Top Bracket',
                'min_amount' => 5000000,
                'max_amount' => null, // Unlimited
                'rate' => 0.35, // 35%
                'order' => 5,
                'is_active' => true,
                'description' => '35% tax on income above K5,000,000',
            ],
        ];

        foreach ($defaultBrackets as $bracket) {
            TaxBracket::firstOrCreate(
                [
                    'min_amount' => $bracket['min_amount'],
                    'max_amount' => $bracket['max_amount'],
                ],
                $bracket
            );
        }

        $this->command->info('Tax bracket permissions and default brackets created successfully.');
    }
}
