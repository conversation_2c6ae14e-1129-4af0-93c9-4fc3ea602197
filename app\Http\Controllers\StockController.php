<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Stock;
use App\Models\Product;
use App\Models\Supplier;
use App\Models\Branch;
use App\Models\Location;
use App\Models\Category;
use App\Models\Warehouse;
use App\Models\Unit;
use Illuminate\Http\Request;
use App\Http\Requests\StockStoreRequest;
use App\Http\Requests\StockUpdateRequest;
use Illuminate\Support\Facades\DB;

use Facades\App\Libraries\ProductHandler;
use Facades\App\Cache\Repo;

class StockController extends Controller
{
    /**
     * Display stock transfers
     */
    public function transfers(Request $request)
    {
        $this->authorize('view-any', Stock::class);

        // Get pagination settings
        $perPage = $request->get('per_page', 20);
        $validPerPage = in_array($perPage, [10, 20, 50, 100]) ? $perPage : 20;

        // Get transfer history (stocks with stock_id - indicating they are transfers)
        $transfers = Stock::whereNotNull('stock_id')
                         ->with(['product', 'supplier', 'branch', 'warehouse', 'stock.branch', 'stock.warehouse', 'createdBy', 'approvedBy'])
                         ->when(!auth()->user()->isSuperAdmin(), function($query) {
                             $query->where('branch_id', auth()->user()->branch_id);
                         })
                         ->when(request('from_warehouse'), function($query, $warehouseId) {
                             $query->whereHas('stock', function($q) use ($warehouseId) {
                                 $q->where('warehouse_id', $warehouseId);
                             });
                         })
                         ->when(request('to_warehouse'), function($query, $warehouseId) {
                             $query->where('warehouse_id', $warehouseId);
                         })
                         ->when(request('search'), function($query, $search) {
                             $query->whereHas('product', function($q) use ($search) {
                                 $q->where('name', 'like', "%{$search}%")
                                   ->orWhere('description', 'like', "%{$search}%")
                                   ->orWhere('barcode', 'like', "%{$search}%");
                             });
                         })
                         ->when(request('status'), function($query, $status) {
                             if ($status === 'approved') {
                                 $query->whereNotNull('approved_by');
                             } elseif ($status === 'pending') {
                                 $query->whereNull('approved_by');
                             }
                         })
                         ->when(request('date_from'), function($query, $dateFrom) {
                             $query->whereDate('created_at', '>=', $dateFrom);
                         })
                         ->when(request('date_to'), function($query, $dateTo) {
                             $query->whereDate('created_at', '<=', $dateTo);
                         })
                         ->orderBy('created_at', 'desc')
                         ->paginate($validPerPage)
                         ->withQueryString();

        // Handle export request
        if ($request->get('export') === 'true') {
            return $this->exportTransfers($request);
        }

        $filterOptions = [
            'branches' => Branch::orderBy('name')->get(),
            'warehouses' => Warehouse::active()
                                    ->with('branch')
                                    ->when(!auth()->user()->isSuperAdmin(), function($query) {
                                        $query->where('branch_id', auth()->user()->branch_id);
                                    })
                                    ->orderBy('name')
                                    ->get(),
        ];

        return view('app.stocks.transfers', compact('transfers', 'filterOptions'));
    }



    /**
     * Display stock inventory with enhanced filtering and analytics
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Stock::class);

        // Build base query with relationships
        $query = Stock::with(['product.category', 'supplier', 'branch', 'createdBy'])
                     ->select('stocks.*')
                     ->selectRaw('
                         CASE
                             WHEN quantity <= 5 THEN "low"
                             WHEN quantity <= 10 THEN "medium"
                             ELSE "good"
                         END as stock_level
                     ');

        // Apply branch filter for non-admin users
        if (!auth()->user()->isSuperAdmin()) {
            $query->where('branch_id', auth()->user()->branch_id);
        }

        // Apply filters
        $this->applyFilters($query, $request);

        // Handle export request
        if ($request->get('export') === 'true') {
            return $this->exportStocks($request, $query);
        }

        // Get paginated results
        $stocks = $query->orderBy('created_at', 'desc')->paginate(50);

        // Calculate analytics
        $analytics = $this->calculateStockAnalytics($request);

        // Get filter options
        $filterOptions = $this->getFilterOptions();

        return view('app.stocks.index', compact('stocks', 'analytics', 'filterOptions'));
    }

    /**
     * Apply filters to stock query
     */
    private function applyFilters($query, Request $request)
    {
        // Search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('product', function($productQuery) use ($search) {
                    $productQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('description', 'like', "%{$search}%")
                               ->orWhere('barcode', 'like', "%{$search}%");
                })
                ->orWhereHas('supplier', function($supplierQuery) use ($search) {
                    $supplierQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhere('description', 'like', "%{$search}%")
                ->orWhere('product_name', 'like', "%{$search}%");
            });
        }

        // Product filter
        if ($request->filled('product_id')) {
            $query->where('product_id', $request->product_id);
        }

        // Branch filter
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        // Warehouse filter
        if ($request->filled('warehouse_id')) {
            $query->where('warehouse_id', $request->warehouse_id);
        }

        // Category filter
        if ($request->filled('category_id')) {
            $query->whereHas('product', function($q) use ($request) {
                $q->where('category_id', $request->category_id);
            });
        }

        // Supplier filter
        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        // Stock type filter
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Stock level filter
        if ($request->filled('stock_level')) {
            switch ($request->stock_level) {
                case 'low':
                    $query->where('quantity', '<=', 5);
                    break;
                case 'medium':
                    $query->whereBetween('quantity', [6, 10]);
                    break;
                case 'good':
                    $query->where('quantity', '>', 10);
                    break;
                case 'out_of_stock':
                    $query->where('quantity', '<=', 0);
                    break;
            }
        }

        // Quick date filter
        if ($request->filled('date_filter')) {
            switch ($request->date_filter) {
                case 'today':
                    $query->whereDate('created_at', today());
                    break;
                case 'yesterday':
                    $query->whereDate('created_at', today()->subDay());
                    break;
                case 'this_week':
                    $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'last_week':
                    $query->whereBetween('created_at', [now()->subWeek()->startOfWeek(), now()->subWeek()->endOfWeek()]);
                    break;
                case 'this_month':
                    $query->whereBetween('created_at', [now()->startOfMonth(), now()->endOfMonth()]);
                    break;
                case 'last_month':
                    $query->whereBetween('created_at', [now()->subMonth()->startOfMonth(), now()->subMonth()->endOfMonth()]);
                    break;
            }
        }

        // Date range filters
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Legacy date range filter (for backward compatibility)
        if ($request->filled('from') && $request->filled('to')) {
            $query->whereBetween('created_at', [
                $request->from . ' 00:00:00',
                $request->to . ' 23:59:59'
            ]);
        }

        // Expiry date filters
        if ($request->filled('expires_from')) {
            $query->whereDate('expires_at', '>=', $request->expires_from);
        }

        if ($request->filled('expires_to')) {
            $query->whereDate('expires_at', '<=', $request->expires_to);
        }

        // Expiry status filter
        if ($request->filled('expiry_status')) {
            switch ($request->expiry_status) {
                case 'expired':
                    $query->where('expires_at', '<', now());
                    break;
                case 'expiring_soon':
                    $query->whereBetween('expires_at', [now(), now()->addDays(30)]);
                    break;
                case 'fresh':
                    $query->where('expires_at', '>', now()->addDays(30));
                    break;
                case 'no_expiry':
                    $query->whereNull('expires_at');
                    break;
            }
        }

        // Status filter (approved/pending)
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'approved':
                    $query->whereNotNull('approved_by');
                    break;
                case 'pending':
                    $query->whereNull('approved_by');
                    break;
            }
        }
    }

    /**
     * Calculate stock analytics
     */
    private function calculateStockAnalytics(Request $request)
    {
        $query = Stock::query();

        if (!auth()->user()->isSuperAdmin()) {
            $query->where('branch_id', auth()->user()->branch_id);
        }

        // Apply same filters as main query
        $this->applyFilters($query, $request);

        return [
            'total_items' => $query->count(),
            'total_value' => $query->sum(DB::raw('quantity * buying_price')),
            'low_stock_count' => $query->where('quantity', '<=', 5)->count(),
            'out_of_stock_count' => $query->where('quantity', '<=', 0)->count(),
            'expired_count' => $query->where('expires_at', '<', now())->count(),
            'expiring_soon_count' => $query->whereBetween('expires_at', [now(), now()->addDays(30)])->count(),
        ];
    }

    /**
     * Get filter options for dropdowns
     */
    private function getFilterOptions()
    {
        $user = auth()->user();
        $isSuperAdmin = $user ? $user->isSuperAdmin() : false;
        $userBranchId = $user ? $user->branch_id : null;

        return [
            'branches' => $isSuperAdmin ? Branch::orderBy('name')->get() : collect(),
            'categories' => Category::orderBy('name')->get(),
            'suppliers' => Supplier::orderBy('name')->get(),
            'locations' => Location::orderBy('name')->get(),
            'warehouses' => Warehouse::active()
                                    ->with('branch')
                                    ->when(!$isSuperAdmin && $userBranchId, function($query) use ($userBranchId) {
                                        $query->where('branch_id', $userBranchId);
                                    })
                                    ->orderBy('name')
                                    ->get(),
            'products' => Product::with('category')
                                ->orderBy('name')
                                ->get(),
            'stock_types' => [
                'STOCK_PURCHASE' => 'Stock Purchase',
                'SALE_RETURN' => 'Sale Return',
                'PURCHASE_RETURN' => 'Purchase Return',
                'STOCK_TRANSFER_IN' => 'Transfer In',
                'STOCK_TRANSFER_OUT' => 'Transfer Out',
                'STOCK_ADJUSTMENT' => 'Stock Adjustment',
                'STOCK_COUNT' => 'Stock Count',
                'STOCK_EXPIRY' => 'Stock Expiry',
                'STOCK_SCRAP' => 'Stock Scrap',
                'STOCK_LOSS' => 'Stock Loss',
                'STOCK_REPAIR' => 'Stock Repair',
                'STOCK_RECYCLE' => 'Stock Recycle',
                'STOCK_REORDER' => 'Stock Reorder',
                'STOCK_RETURN' => 'Stock Return',
                'STOCK_WRITE_OFF' => 'Stock Write Off',
                'STOCK_WRITE_DOWN' => 'Stock Write Down',
                'STOCK_WRITE_UP' => 'Stock Write Up',
            ],
        ];
    }

    /**
     * Show create stock form
     */
    public function create(Request $request)
    {
        $this->authorize('create', Stock::class);

        $products = Product::with(['category', 'units'])->orderBy('name')->get();
        $suppliers = Supplier::orderBy('name')->get();
        $locations = Location::orderBy('name')->get();
        $branches = auth()->user()->isSuperAdmin() ? Branch::orderBy('name')->get() : collect();

        return view('app.stocks.create', compact('products', 'suppliers', 'locations', 'branches'));
    }

    /**
     * Store new stock entry
     */
    public function store(StockStoreRequest $request)
    {
        $this->authorize('create', Stock::class);

        $validated = $request->validated();

        // Set defaults
        $validated['balance'] = $validated['quantity'] ?? 0;
        $validated['branch_id'] = $validated['branch_id'] ?? auth()->user()->branch_id;
        $validated['created_by'] = auth()->id();
        $validated['business_type_id'] = auth()->user()->business_type_id;

        // Get product details
        if ($validated['product_id']) {
            $product = Product::find($validated['product_id']);
            if ($product) {
                $validated['product_name'] = $product->name;
                $validated['selling_price'] = $validated['selling_price'] ?? $product->selling_price;
                $validated['buying_price'] = $validated['buying_price'] ?? $product->buying_price;
            }
        }

        DB::beginTransaction();
        try {
            $stock = Stock::create($validated);

            // Generate stock items if needed
            ProductHandler::generateItems($stock);

            DB::commit();

            return redirect()
                ->route('stocks.show', $stock)
                ->with('success', 'Stock entry created successfully!');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to create stock entry: ' . $e->getMessage());
        }
    }

    /**
     * Show stock details
     */
    public function show(Request $request, Stock $stock)
    {
        $this->authorize('view', $stock);

        $stock->load(['product.category', 'supplier', 'branch', 'createdBy', 'updatedBy', 'stockItems']);

        // Calculate stock movements
        $movements = $this->getStockMovements($stock);

        return view('app.stocks.show', compact('stock', 'movements'));
    }

    /**
     * Show edit stock form
     */
    public function edit(Request $request, Stock $stock)
    {
        $this->authorize('update', $stock);

        $products = Product::with(['category', 'units'])->orderBy('name')->get();
        $suppliers = Supplier::orderBy('name')->get();
        $locations = Location::orderBy('name')->get();
        $branches = auth()->user()->isSuperAdmin() ? Branch::orderBy('name')->get() : collect();

        return view('app.stocks.edit', compact('stock', 'products', 'suppliers', 'locations', 'branches'));
    }

    /**
     * Update stock entry
     */
    public function update(StockUpdateRequest $request, Stock $stock)
    {
        $this->authorize('update', $stock);

        $validated = $request->validated();
        $validated['updated_by'] = auth()->id();

        // Update product details if product changed
        if (isset($validated['product_id']) && $validated['product_id'] != $stock->product_id) {
            $product = Product::find($validated['product_id']);
            if ($product) {
                $validated['product_name'] = $product->name;
            }
        }

        DB::beginTransaction();
        try {
            $stock->update($validated);

            // Regenerate stock items if quantity changed
            if (isset($validated['quantity']) && $validated['quantity'] != $stock->getOriginal('quantity')) {
                ProductHandler::generateItems($stock);
            }

            DB::commit();

            return redirect()
                ->route('stocks.show', $stock)
                ->with('success', 'Stock updated successfully!');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to update stock: ' . $e->getMessage());
        }
    }

    /**
     * Delete stock entry
     */
    public function destroy(Request $request, Stock $stock)
    {
        $this->authorize('delete', $stock);

        // Check if stock has any movements
        if ($stock->stockItems()->exists()) {
            return redirect()
                ->back()
                ->with('error', 'Cannot delete stock with existing movements.');
        }

        DB::beginTransaction();
        try {
            $stock->delete();
            DB::commit();

            return redirect()
                ->route('stocks.index')
                ->with('success', 'Stock deleted successfully!');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()
                ->back()
                ->with('error', 'Failed to delete stock: ' . $e->getMessage());
        }
    }


    
    /**
     * Show create transfer form
     */
    public function createTransfer(Request $request)
    {
        $this->authorize('create', Stock::class);

        // Get products with available stock in warehouses
        $products = Product::with([
                        'stocks' => function($query) {
                            $query->where('quantity', '>', 0)
                                  ->whereNull('stock_id') // Only original stock, not transfers
                                  ->whereNotNull('warehouse_id') // Only stocks in warehouses
                                  ->with(['warehouse', 'branch']);
                        },
                        'units', // Load product units
                        'unit'   // Load base unit
                    ])
                    ->whereHas('stocks', function($query) {
                        $query->where('quantity', '>', 0)
                              ->whereNull('stock_id')
                              ->whereNotNull('warehouse_id')
                              ->when(!auth()->user()->isSuperAdmin(), function($q) {
                                  $q->where('branch_id', auth()->user()->branch_id);
                              });
                    })
                    ->when(!auth()->user()->isSuperAdmin(), function($query) {
                        $query->whereHas('stocks', function($q) {
                            $q->where('branch_id', auth()->user()->branch_id);
                        });
                    })
                    ->orderBy('name')
                    ->get();

        // Get all active warehouses with their branches
        $warehouses = Warehouse::active()
                              ->with('branch')
                              ->when(!auth()->user()->isSuperAdmin(), function($query) {
                                  $query->where('branch_id', auth()->user()->branch_id);
                              })
                              ->orderBy('name')
                              ->get();

        return view('app.stocks.create-transfer', compact('products', 'warehouses'));
    }

    /**
     * Get available stock for a product in a specific warehouse
     */
    public function getProductStock(Request $request)
    {
        $request->validate([
            'product_id' => 'required|tenant_exists:products,id',
            'warehouse_id' => 'required|tenant_exists:warehouses,id'
        ]);

        $stocks = Stock::where('product_id', $request->product_id)
                      ->where('warehouse_id', $request->warehouse_id)
                      ->where('quantity', '>', 0)
                      ->whereNull('stock_id') // Only original stock, not transfers
                      ->when(!auth()->user()->isSuperAdmin(), function($query) {
                          $query->where('branch_id', auth()->user()->branch_id);
                      })
                      ->with(['product', 'warehouse', 'branch'])
                      ->orderBy('created_at', 'asc') // FIFO order
                      ->get();

        $totalQuantity = $stocks->sum('quantity');

        return response()->json([
            'stocks' => $stocks,
            'total_quantity' => $totalQuantity,
            'warehouse' => $stocks->first()?->warehouse,
            'product' => $stocks->first()?->product
        ]);
    }

    /**
     * Get available stock for a product across all warehouses for current user
     */
    public function getProductAvailable(Request $request)
    {
        $request->validate([
            'product_id' => 'required|tenant_exists:products,id',
            'unit_id' => 'required|tenant_exists:units,id'
        ]);

        // Get total available stock for this product across user's warehouses
        $totalBaseStock = Stock::where('product_id', $request->product_id)
                              ->where('quantity', '>', 0)
                              ->whereNull('stock_id') // Only original stock, not transfers
                              ->when(!auth()->user()->isSuperAdmin(), function($query) {
                                  $query->where('branch_id', auth()->user()->branch_id);
                              })
                              ->sum('quantity');

        // Get unit information
        $unit = Unit::find($request->unit_id);
        $unitQuantity = $unit ? $unit->quantity : 1;

        // Calculate available quantity in the selected unit
        $availableQuantity = floor($totalBaseStock / $unitQuantity);

        return response()->json([
            'available_quantity' => $availableQuantity,
            'base_stock' => $totalBaseStock,
            'unit_quantity' => $unitQuantity,
            'unit_name' => $unit ? $unit->name : 'Unknown'
        ]);
    }

    /**
     * Search products for transfer with stock information
     */
    public function searchProducts(Request $request)
    {
        $search = $request->get('search', '');

        if (strlen($search) < 1) {
            return response()->json(['data' => []]);
        }

        // Get products with available stock in warehouses
        $products = Product::with("units")->where(function($query) use ($search) {
                        $query->where('name', 'like', "%{$search}%")->orWhere('description', 'like', "%{$search}%")->orWhere('barcode', 'like', "%{$search}%");
                    })
                    ->whereHas('stocks', function($query) {
                        $query->where('balance', '>', 0)
                            ->whereNotNull('approved_by')
                            ->whereNotNull('warehouse_id');
                    })
                    ->limit(10)
                    ->get();

        return response()->json(['data' => $products]);
    }

    /**
     * Show pending transfers for warehouse manager approval
     */
    public function pendingApprovals(Request $request)
    {
        $this->authorize('view-any', Stock::class);

        // Get warehouses managed by current user - more flexible approach
        $managedWarehouses = collect();

        if (auth()->user()->isSuperAdmin()) {
            // Super admin can approve all transfers
            $managedWarehouses = Warehouse::pluck('id');
        } else {
            // Get warehouses where user is manager, creator, or assigned to same branch
            $managedWarehouses = Warehouse::where(function($query) {
                $query->where('manager_id', auth()->id())
                      ->orWhere('created_by', auth()->id())
                      ->orWhere('branch_id', auth()->user()->branch_id); // Allow branch users to approve
            })->pluck('id');
        }

        if ($managedWarehouses->isEmpty()) {
            return view('app.stocks.pending-approvals', [
                'transfers' => collect(),
                'message' => 'You are not assigned as a warehouse manager or do not have access to any warehouses.'
            ]);
        }

        // Get pending transfer IN records (positive quantities) for managed warehouses
        $transfers = Stock::with(['product', 'warehouse', 'branch', 'stock.warehouse', 'stock.branch', 'createdBy'])
                         ->where('quantity', '>', 0) // Transfer IN records
                         ->whereNotNull('stock_id') // Only transfer records
                         ->whereNull('approved_by') // Not yet approved
                         ->whereIn('warehouse_id', $managedWarehouses)
                         ->when(request('search'), function($query, $search) {
                             $query->whereHas('product', function($q) use ($search) {
                                 $q->where('name', 'like', "%{$search}%");
                             });
                         })
                         ->orderBy('created_at', 'desc')
                         ->paginate(20);

        // Debug information
        if (request('debug')) {
    
        }

        return view('app.stocks.pending-approvals', compact('transfers'));
    }

    /**
     * Approve a stock transfer
     */
    public function approveTransfer(Request $request)
    {
        $this->authorize('create', Stock::class);

        $request->validate([
            'transfer_id' => 'required|tenant_exists:stocks,id',
            'approval_notes' => 'nullable|string|max:500'
        ]);

        try {
            DB::beginTransaction();

            $transfer = Stock::with(['warehouse', 'stock'])->findOrFail($request->transfer_id);

            // Check if user can approve this transfer - more flexible approach
            $canApprove = auth()->user()->isSuperAdmin() ||
                         $transfer->warehouse->manager_id == auth()->id() ||
                         $transfer->warehouse->created_by == auth()->id() ||
                         $transfer->warehouse->branch_id == auth()->user()->branch_id; // Allow branch users

            if (!$canApprove) {
                throw new \Exception('You are not authorized to approve transfers for this warehouse.');
            }

            // Check if already approved
            if ($transfer->approved_by) {
                throw new \Exception('This transfer has already been approved.');
            }

            // Approve the transfer
            $transfer->update([
                'approved_by' => auth()->id(),
                'description' => $transfer->description . ' | Approved: ' . ($request->approval_notes ?: 'No notes'),
                'updated_by' => auth()->id(),
            ]);

            // Also approve the corresponding transfer OUT record
            if ($transfer->stock_id) {
                $transferOut = Stock::where('stock_id', $transfer->stock_id)
                                  ->where('quantity', '<', 0)
                                  ->whereNull('approved_by')
                                  ->first();

                if ($transferOut) {
                    $transferOut->update([
                        'approved_by' => auth()->id(),
                        'description' => $transferOut->description . ' | Approved: ' . ($request->approval_notes ?: 'No notes'),
                        'updated_by' => auth()->id(),
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Transfer approved successfully.'
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Approval failed: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Reject a stock transfer
     */
    public function rejectTransfer(Request $request)
    {
        $this->authorize('create', Stock::class);

        $request->validate([
            'transfer_id' => 'required|tenant_exists:stocks,id',
            'rejection_reason' => 'required|string|max:500'
        ]);

        try {
            DB::beginTransaction();

            $transfer = Stock::with(['warehouse', 'stock'])->findOrFail($request->transfer_id);

            // Check if user can reject this transfer - more flexible approach
            $canReject = auth()->user()->isSuperAdmin() ||
                        $transfer->warehouse->manager_id == auth()->id() ||
                        $transfer->warehouse->created_by == auth()->id() ||
                        $transfer->warehouse->branch_id == auth()->user()->branch_id; // Allow branch users

            if (!$canReject) {
                throw new \Exception('You are not authorized to reject transfers for this warehouse.');
            }

            // Check if already approved
            if ($transfer->approved_by) {
                throw new \Exception('Cannot reject an already approved transfer.');
            }

            // Reverse the transfer by deleting transfer records and restoring original stock
            if ($transfer->stock_id) {
                $originalStock = Stock::find($transfer->stock_id);
                if ($originalStock) {
                    // Restore original stock quantity
                    $originalStock->update([
                        'quantity' => $originalStock->quantity + $transfer->quantity,
                        'balance' => $originalStock->balance + $transfer->quantity,
                        'updated_by' => auth()->id(),
                    ]);
                }

                // Delete the transfer OUT record
                $transferOut = Stock::where('stock_id', $transfer->stock_id)
                                  ->where('quantity', '<', 0)
                                  ->first();
                if ($transferOut) {
                    $transferOut->delete();
                }
            }

            // Delete the transfer IN record
            $transfer->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Transfer rejected and reversed successfully.'
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Rejection failed: ' . $e->getMessage()
            ], 400);
        }
    }


    /**
     * Store stock transfer - like sales but for warehouse transfers
     */
    public function storeTransfer(Request $request)
    {
        $this->authorize('create', Stock::class);

        $request->validate([
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|tenant_exists:products,id',
            'items.*.unit_id' => 'required|tenant_exists:units,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.new_warehouse_id' => 'required|tenant_exists:warehouses,id',
            'items.*.reason' => 'required|string|max:500',
            'transfer_notes' => 'nullable|string|max:1000'
        ]);


        try {
            DB::beginTransaction();

            $transferredItems = [];
            $totalItems = count($request->items);

            // Process each transfer item
            foreach ($request->items as $itemData) {
                // Get product, unit, and warehouse
                $product = Product::findOrFail($itemData['product_id']);
                $unit = Unit::findOrFail($itemData['unit_id']);
                $newWarehouse = Warehouse::with('branch')->findOrFail($itemData['new_warehouse_id']);
                // Calculate base quantity needed
                $quantityInUnit = $itemData['quantity'];
                $baseQuantityNeeded = $quantityInUnit * $unit->quantity;
                // Get available stocks for this product across all warehouses (FIFO order - oldest first)
                $availableStocks = Stock::where('product_id', $itemData['product_id'])
                                       ->where('balance', '>', 0)
                                       ->where('warehouse_id', auth()->user()->warehouse_id)
                                    //    ->whereNull('stock_id') // Only original stock, not transfers
                                       ->orderBy('expires_at', 'asc') // FIFO by expiry date first
                                       ->orderBy('created_at', 'asc') // Then by creation date
                                       ->get();

                $totalAvailable = $availableStocks->sum('balance');

                // Validate transfer quantity
                if ($baseQuantityNeeded > $totalAvailable) {
                    throw new \Exception("Transfer quantity for {$product->name} ({$quantityInUnit} {$unit->name} = {$baseQuantityNeeded} base units) exceeds available stock ({$totalAvailable}) across all warehouses.");
                }

                // Allocate transfer quantity from available stocks (FIFO)
                $remainingToTransfer = $baseQuantityNeeded;
                $transferredStockItems = [];

                foreach ($availableStocks as $stock) {
                    if ($remainingToTransfer <= 0) break;

                    $balance = min($remainingToTransfer, $stock->balance);
                    $quantity = $balance / $unit->quantity;
                    $sourceWarehouse = $stock->warehouse;

                    if($stock->balance >= $remainingToTransfer){
                        $remainingToTransfer = 0;
                    } else {
                        $remainingToTransfer = $remainingToTransfer - $stock->balance;
                    }

                    // Create transfer OUT record (negative entry in source warehouse)
                    $transferOut =$stock->stocks()->create([
                        'product_id' => $stock->product_id,
                        'product_name' => $stock->product_name,
                        'unit_id' => $itemData['unit_id'],
                        'unit_name' => $unit->name,
                        'unit_quantity' => $unit->quantity,
                        'quantity' => -$quantity, // Negative for outgoing
                        'balance' => -$balance,
                        'buying_price' => $stock->buying_price,
                        'selling_price' => $stock->selling_price,
                        'supplier_id' => $stock->supplier_id,
                        'type' => "STOCK_TRANSFER_OUT",
                        'branch_id' => $stock->branch_id, // Keep source branch
                        'warehouse_id' => $stock->warehouse_id, // Keep source warehouse
                        'stock_id' => $stock->id, // Reference to original stock
                        'expires_at' => $stock->expires_at, // Maintain expiry date
                        'description' => 'Transfer OUT: ' . $itemData['reason'] . ' | ' . $quantityInUnit . ' ' . $unit->name . ' to ' . $newWarehouse->name,
                        'created_by' => auth()->id(),
                        'business_type_id' => auth()->user()->business_type_id,
                    ]);

                    // Create transfer IN record (positive entry in destination warehouse)
                    $transferIn = $stock->stocks()->create([
                        'product_id' => $stock->product_id,
                        'product_name' => $stock->product_name,
                        'unit_id' => $itemData['unit_id'],
                        'unit_name' => $unit->name,
                        'unit_quantity' => $unit->quantity,
                        'quantity' => $quantity, // Positive for incoming
                        'balance' => $balance,
                        'buying_price' => $stock->buying_price,
                        'selling_price' => $stock->selling_price,
                        'supplier_id' => $stock->supplier_id,
                        'type' => "STOCK_TRANSFER_IN",
                        'branch_id' => $newWarehouse->branch_id, // Use destination warehouse's branch
                        'warehouse_id' => $itemData['new_warehouse_id'], // Destination warehouse
                        'stock_id' => $stock->id, // Reference to original stock
                        'expires_at' => $stock->expires_at, // Maintain expiry date
                        'description' => 'Transfer IN: ' . $itemData['reason'] . ' | ' . $quantityInUnit . ' ' . $unit->name . ' from ' . $sourceWarehouse->name,
                        'created_by' => auth()->id(),
                        'business_type_id' => auth()->user()->business_type_id,
                    ]);

                    // Update original stock quantity
                    $stock->update([
                        'balance' => $remainingToTransfer ? 0 : $stock->balance - $balance,
                    ]);

                    // Transfer stock items to maintain order_id relationships and expiry tracking
                    $stockItemsToTransfer = $stock->stockItems()
                                                 ->whereNull('invoice_id') // Only available items
                                                 ->limit($balance)
                                                 ->get();

                    foreach ($stockItemsToTransfer as $stockItem) {
                        $stockItem->update([
                            'warehouse_id' => $itemData['new_warehouse_id'],
                            'branch_id' => $newWarehouse->branch_id,
                            'stock_id' => $transferIn->id, // Link to new stock record
                            'updated_by' => auth()->id(),
                        ]);
                    }

                    $transferredStockItems[] = [
                        'original_stock_id' => $stock->id,
                        'quantity' => $quantity,
                        'transfer_in_id' => $transferIn->id,
                        'transfer_out_id' => $stock->id,
                    ];

                }

                // Track transferred items for success message
                $transferredItems[] = [
                    'product' => $product->name,
                    'quantity' => $quantityInUnit,
                    'unit' => $unit->name,
                    'base_quantity' => $baseQuantityNeeded,
                    'to' => $newWarehouse->name . ' (' . $newWarehouse->branch->name . ')',
                    'stock_allocations' => $transferredStockItems,
                    'reason' => $itemData['reason']
                ];
            }

            DB::commit();

            $successMessage = "Successfully transferred {$totalItems} item(s): ";
            $itemSummaries = array_map(function($item) {
                return "{$item['quantity']} {$item['unit']} of {$item['product']} to {$item['to']} ({$item['reason']})";
            }, $transferredItems);
            $successMessage .= implode('; ', $itemSummaries);

            return redirect()
                ->route('stocks.transfers')
                ->with('success', $successMessage);

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Transfer failed: ' . $e->getMessage());
        }
    }

    /**
     * Get stock movements for a specific stock
     */
    private function getStockMovements(Stock $stock)
    {
        return $stock->stockItems()
                    ->with(['invoice', 'sale'])
                    ->orderBy('created_at', 'desc')
                    ->limit(20)
                    ->get();
    }

    /**
     * Export transfers data
     */
    public function exportTransfers(Request $request)
    {
        $this->authorize('view-any', Stock::class);

        // Get all transfers with same filters (no pagination)
        $transfers = Stock::whereNotNull('stock_id')
                         ->with(['product', 'supplier', 'branch', 'warehouse', 'stock.branch', 'stock.warehouse', 'createdBy', 'approvedBy'])
                         ->when(!auth()->user()->isSuperAdmin(), function($query) {
                             $query->where('branch_id', auth()->user()->branch_id);
                         })
                         ->when($request->get('from_warehouse'), function($query, $warehouseId) {
                             $query->whereHas('stock', function($q) use ($warehouseId) {
                                 $q->where('warehouse_id', $warehouseId);
                             });
                         })
                         ->when($request->get('to_warehouse'), function($query, $warehouseId) {
                             $query->where('warehouse_id', $warehouseId);
                         })
                         ->when($request->get('search'), function($query, $search) {
                             $query->whereHas('product', function($q) use ($search) {
                                 $q->where('name', 'like', "%{$search}%")
                                   ->orWhere('description', 'like', "%{$search}%")
                                   ->orWhere('barcode', 'like', "%{$search}%");
                             });
                         })
                         ->when($request->get('status'), function($query, $status) {
                             if ($status === 'approved') {
                                 $query->whereNotNull('approved_by');
                             } elseif ($status === 'pending') {
                                 $query->whereNull('approved_by');
                             }
                         })
                         ->when($request->get('date_from'), function($query, $dateFrom) {
                             $query->whereDate('created_at', '>=', $dateFrom);
                         })
                         ->when($request->get('date_to'), function($query, $dateTo) {
                             $query->whereDate('created_at', '<=', $dateTo);
                         })
                         ->orderBy('created_at', 'desc')
                         ->get();

        // Create CSV content
        $csvData = [];
        $csvData[] = [
            'Date',
            'Product',
            'Quantity',
            'Type',
            'From Warehouse',
            'To Warehouse',
            'Status',
            'Created By',
            'Approved By',
            'Description'
        ];

        foreach ($transfers as $transfer) {
            $csvData[] = [
                $transfer->created_at->format('Y-m-d H:i:s'),
                $transfer->product->name ?? 'Unknown Product',
                $transfer->quantity,
                $transfer->quantity > 0 ? 'IN' : 'OUT',
                $transfer->stock->warehouse->name ?? 'Unknown',
                $transfer->warehouse->name ?? 'Unknown',
                $transfer->approved_by ? 'Approved' : 'Pending',
                $transfer->createdBy->name ?? 'System',
                $transfer->approvedBy->name ?? '-',
                $transfer->description ?? ''
            ];
        }

        // Generate CSV file
        $filename = 'stock_transfers_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export stock data
     */
    public function export(Request $request)
    {
        $this->authorize('view-any', Stock::class);

        // Build base query with relationships
        $query = Stock::with(['product.category', 'supplier', 'branch', 'warehouse', 'createdBy', 'approvedBy'])
                     ->select('stocks.*');

        // Apply branch filter for non-admin users
        if (!auth()->user()->isSuperAdmin()) {
            $query->where('branch_id', auth()->user()->branch_id);
        }

        // Apply filters
        $this->applyFilters($query, $request);

        return $this->exportStocks($request, $query);
    }

    /**
     * Export stocks with applied filters
     */
    private function exportStocks(Request $request, $query)
    {
        // Get all stocks with filters (no pagination)
        $stocks = $query->orderBy('created_at', 'desc')->get();

        // Create CSV content
        $csvData = [];
        $csvData[] = [
            'Date',
            'Product Code',
            'Product Name',
            'Category',
            'Type',
            'Warehouse',
            'Branch',
            'Quantity',
            'Unit',
            'Unit Cost',
            'Total Value',
            'Supplier',
            'Status',
            'Approved By',
            'Expires At',
            'Description'
        ];

        foreach ($stocks as $stock) {
            $csvData[] = [
                $stock->created_at->format('Y-m-d H:i:s'),
                $stock->product->barcode ?? '',
                $stock->product->name ?? 'Unknown Product',
                $stock->product->category->name ?? '',
                $stock->type ?? '',
                $stock->warehouse->name ?? '',
                $stock->branch->name ?? '',
                $stock->quantity,
                $stock->unit_name ?? '',
                $stock->buying_price,
                $stock->buying_price * $stock->quantity,
                $stock->supplier->name ?? '',
                $stock->approved_by ? 'Approved' : 'Pending',
                $stock->approvedBy->name ?? '',
                $stock->expires_at ? $stock->expires_at->format('Y-m-d') : '',
                $stock->description ?? ''
            ];
        }

        // Generate CSV file
        $filename = 'stock_inventory_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get low stock alerts
     */
    public function lowStockAlerts()
    {
        $this->authorize('view-any', Stock::class);

        $query = Stock::with(['product', 'branch'])
                     ->where('quantity', '<=', 5);

        if (!auth()->user()->isSuperAdmin()) {
            $query->where('branch_id', auth()->user()->branch_id);
        }

        $lowStockItems = $query->orderBy('quantity', 'asc')->get();

        return response()->json($lowStockItems);
    }

    /**
     * Display stock adjustments
     */
    public function adjustments(Request $request)
    {
        $this->authorize('view-any', Stock::class);

        // Get pagination settings
        $perPage = $request->get('per_page', 20);
        $validPerPage = in_array($perPage, [10, 20, 50, 100]) ? $perPage : 20;

        // Get adjustment history (negative stock entries that are adjustments)
        $adjustments = Stock::where('quantity', '<', 0)
                           ->whereIn('description', function($query) {
                               $query->select(DB::raw("CONCAT(adjustment_type, ': ', comment)"))
                                     ->from('stocks as s2')
                                     ->whereColumn('s2.id', 'stocks.id');
                           })
                           ->orWhere('description', 'like', 'STOCK_%')
                           ->with(['product', 'supplier', 'branch', 'warehouse', 'createdBy', 'approvedBy'])
                           ->when(!auth()->user()->isSuperAdmin(), function($query) {
                               $query->where('branch_id', auth()->user()->branch_id);
                           })
                           ->when(request('search'), function($query, $search) {
                               $query->whereHas('product', function($q) use ($search) {
                                   $q->where('name', 'like', "%{$search}%")
                                     ->orWhere('description', 'like', "%{$search}%")
                                     ->orWhere('barcode', 'like', "%{$search}%");
                               });
                           })
                           ->when(request('status'), function($query, $status) {
                               if ($status === 'approved') {
                                   $query->whereNotNull('approved_by');
                               } elseif ($status === 'pending') {
                                   $query->whereNull('approved_by');
                               }
                           })
                           ->when(request('warehouse'), function($query, $warehouseId) {
                               $query->where('warehouse_id', $warehouseId);
                           })
                           ->when(request('date_from'), function($query, $dateFrom) {
                               $query->whereDate('created_at', '>=', $dateFrom);
                           })
                           ->when(request('date_to'), function($query, $dateTo) {
                               $query->whereDate('created_at', '<=', $dateTo);
                           })
                           ->orderBy('created_at', 'desc')
                           ->paginate($validPerPage)
                           ->withQueryString();

        $filterOptions = [
            'warehouses' => Warehouse::active()
                                    ->with('branch')
                                    ->when(!auth()->user()->isSuperAdmin(), function($query) {
                                        $query->where('branch_id', auth()->user()->branch_id);
                                    })
                                    ->orderBy('name')
                                    ->get(),
        ];

        return view('app.stocks.adjustments', compact('adjustments', 'filterOptions'));
    }

    /**
     * Show create adjustment form
     */
    public function createAdjustment(Request $request)
    {
        $this->authorize('create', Stock::class);

        // Get products with units
        $products = Product::with(['units', 'unit'])
                          ->orderBy('name')
                          ->get();

        // Get warehouses
        $warehouses = Warehouse::active()
                              ->with('branch')
                              ->when(!auth()->user()->isSuperAdmin(), function($query) {
                                  $query->where('branch_id', auth()->user()->branch_id);
                              })
                              ->orderBy('name')
                              ->get();

        return view('app.stocks.create-adjustment', compact('products', 'warehouses'));
    }

    /**
     * Store stock adjustment - creates negative stock entries pending approval
     */
    public function storeAdjustment(Request $request)
    {
        $this->authorize('create', Stock::class);

        $request->validate([
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|tenant_exists:products,id',
            'items.*.unit_id' => 'required|tenant_exists:units,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.warehouse_id' => 'required|tenant_exists:warehouses,id',
            'items.*.adjustment_type' => 'required|string|in:STOCK_ADJUSTMENT,STOCK_COUNT,STOCK_EXPIRY,STOCK_SCRAP,STOCK_LOSS,STOCK_REPAIR,STOCK_RECYCLE,STOCK_WRITE_OFF,STOCK_WRITE_DOWN',
            'items.*.comment' => 'required|string|max:500',
            'adjustment_notes' => 'nullable|string|max:1000'
        ]);

        try {
            DB::beginTransaction();

            $adjustedItems = [];
            $totalItems = count($request->items);

            // Process each adjustment item
            foreach ($request->items as $itemData) {
                // Get product, unit, and warehouse
                $product = Product::findOrFail($itemData['product_id']);
                $unit = Unit::findOrFail($itemData['unit_id']);
                $warehouse = Warehouse::with('branch')->findOrFail($itemData['warehouse_id']);

                // Calculate base quantity
                $quantityInUnit = $itemData['quantity'];
                $baseQuantityToAdjust = $quantityInUnit * $unit->quantity;

                // Create negative stock adjustment record (pending approval)
                $adjustment = Stock::create([
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'unit_id' => $unit->id,
                    'unit_name' => $unit->name,
                    'unit_quantity' => $unit->quantity,
                    'quantity' => -$baseQuantityToAdjust, // Negative for adjustment
                    'balance' => -$baseQuantityToAdjust,
                    'buying_price' => $product->buying_price ?? 0,
                    'selling_price' => $product->selling_price ?? 0,
                    'supplier_id' => null, // No supplier for adjustments
                    'branch_id' => $warehouse->branch_id,
                    'warehouse_id' => $warehouse->id,
                    'stock_id' => null, // Not a transfer
                    'description' => $itemData['adjustment_type'] . ': ' . $itemData['comment'],
                    'created_by' => auth()->id(),
                    'business_type_id' => auth()->user()->business_type_id,
                    'approved_by' => null, // Pending approval
                ]);

                // Track adjusted items for success message
                $adjustedItems[] = [
                    'product' => $product->name,
                    'quantity' => $quantityInUnit,
                    'unit' => $unit->name,
                    'base_quantity' => $baseQuantityToAdjust,
                    'warehouse' => $warehouse->name . ' (' . $warehouse->branch->name . ')',
                    'type' => $itemData['adjustment_type'],
                    'comment' => $itemData['comment']
                ];
            }

            DB::commit();

            $successMessage = "Successfully created {$totalItems} stock adjustment(s) pending approval: ";
            $itemSummaries = array_map(function($item) {
                return "{$item['quantity']} {$item['unit']} of {$item['product']} in {$item['warehouse']} ({$item['type']})";
            }, $adjustedItems);
            $successMessage .= implode('; ', $itemSummaries);

            return redirect()
                ->route('stocks.adjustments')
                ->with('success', $successMessage);

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Adjustment failed: ' . $e->getMessage());
        }
    }

    /**
     * Get available stock for a product in a specific warehouse and unit
     */
    public function getWarehouseStock(Request $request)
    {
        $request->validate([
            'product_id' => 'required|tenant_exists:products,id',
            'unit_id' => 'required|tenant_exists:units,id',
            'warehouse_id' => 'required|tenant_exists:warehouses,id'
        ]);

        // Get total available stock for this product in the specific warehouse
        $totalBaseStock = Stock::where('product_id', $request->product_id)
                              ->where('warehouse_id', $request->warehouse_id)
                              ->where('quantity', '>', 0)
                              ->whereNull('stock_id') // Only original stock, not transfers
                              ->when(!auth()->user()->isSuperAdmin(), function($query) {
                                  $query->where('branch_id', auth()->user()->branch_id);
                              })
                              ->sum('quantity');

        // Get unit information
        $unit = Unit::find($request->unit_id);
        $unitQuantity = $unit ? $unit->quantity : 1;

        // Calculate available quantity in the selected unit
        $availableQuantity = floor($totalBaseStock / $unitQuantity);

        return response()->json([
            'available_quantity' => $availableQuantity,
            'base_stock' => $totalBaseStock,
            'unit_quantity' => $unitQuantity,
            'unit_name' => $unit ? $unit->name : 'Unknown'
        ]);
    }

    /**
     * Approve stock adjustment
     */
    public function approveAdjustment(Request $request)
    {
        $request->validate([
            'adjustment_id' => 'required|tenant_exists:stocks,id'
        ]);

        $adjustment = Stock::findOrFail($request->adjustment_id);

        $this->authorize('update', $adjustment);

        if ($adjustment->approved_by) {
            return response()->json([
                'success' => false,
                'message' => 'This adjustment has already been approved.'
            ], 400);
        }

        try {
            $adjustment->update([
                'approved_by' => auth()->id(),
                'approved_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Stock adjustment approved successfully.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Approval failed: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Download Stock Taking Sheet CSV
     */
    public function downloadTakingSheet()
    {
        $this->authorize('view-any', Stock::class);

        // Get products with their total available stock (balance)
        // Use the same logic as the stock index page
        $products = Product::with(['category', 'unit', 'stocks' => function($query) {
                                if (!auth()->user()->isSuperAdmin()) {
                                    $query->where('branch_id', auth()->user()->branch_id);
                                }
                            }])
                           ->get()
                           ->filter(function($product) {
                               return $product->total_stock > 0; // Only products with available stock
                           });

        // Group by warehouse if needed
        $stockData = collect();

        foreach ($products as $product) {
            // Get stock by warehouse
            $stocksByWarehouse = $product->stocks->groupBy('warehouse_id');

            foreach ($stocksByWarehouse as $warehouseId => $warehouseStocks) {
                $totalBalance = $warehouseStocks->sum('quantity');

                if ($totalBalance > 0) {
                    $warehouse = Warehouse::find($warehouseId);

                    $stockData->push([
                        'product' => $product,
                        'warehouse' => $warehouse,
                        'total_balance' => $totalBalance,
                        'stock_records' => $warehouseStocks->count(),
                        'earliest_expiry' => $warehouseStocks->whereNotNull('expires_at')->min('expires_at'),
                        'latest_expiry' => $warehouseStocks->whereNotNull('expires_at')->max('expires_at'),
                        'avg_buying_price' => $warehouseStocks->where('quantity', '>', 0)->avg('buying_price'),
                        'avg_selling_price' => $warehouseStocks->where('quantity', '>', 0)->avg('selling_price'),
                    ]);
                }
            }
        }

        // Format data for CSV
        $enrichedData = $stockData->map(function($item) {
            $product = $item['product'];
            $warehouse = $item['warehouse'];

            return [
                'Product Code' => $product->barcode ?? 'N/A',
                'Product Name' => $product->name,
                'Description' => $product->description ?? '',
                'Category' => $product->category->name ?? 'Uncategorized',
                'Unit' => $product->unit->name ?? 'Unit',
                'Warehouse' => $warehouse->name ?? 'Unknown',
                'Branch' => $warehouse->branch->name ?? 'Unknown',
                'System Stock' => number_format($item['total_balance'], 2),
                'Physical Stock' => '', // Empty for manual entry
                'Difference' => '', // Empty for calculation
                'Variance %' => '', // Empty for calculation
                'Earliest Expiry' => $item['earliest_expiry'] ? date('Y-m-d', strtotime($item['earliest_expiry'])) : 'N/A',
                'Latest Expiry' => $item['latest_expiry'] ? date('Y-m-d', strtotime($item['latest_expiry'])) : 'N/A',
                'Stock Records' => $item['stock_records'],
                'Avg Buying Price' => number_format($item['avg_buying_price'] ?? 0, 2),
                'Avg Selling Price' => number_format($item['avg_selling_price'] ?? 0, 2),
                'Notes' => '', // Empty for manual notes
                'Verified By' => '', // Empty for signature
                'Date Verified' => '', // Empty for date entry
            ];
        });

        // Check if we have data
        if ($enrichedData->isEmpty()) {
            // If no data, create a simple message CSV
            $debugData = [
                [
                    'Message' => 'No stock data available',
                    'Total Products' => $products->count(),
                    'Products with Stock' => $products->filter(function($p) { return $p->total_stock > 0; })->count(),
                    'User Branch ID' => auth()->user()->branch_id ?? 'N/A',
                    'Is Super Admin' => auth()->user()->isSuperAdmin() ? 'Yes' : 'No',
                    'Instructions' => 'Please ensure you have products with positive stock in your branch'
                ]
            ];

            $csvContent = $this->generateCSV($debugData);
        } else {
            // Generate CSV content with actual data
            $csvContent = $this->generateCSV($enrichedData->toArray());
        }

        // Generate filename with current date and time
        $filename = 'stock_taking_sheet_' . date('Y-m-d_H-i-s') . '.csv';

        // Return CSV download response
        return response($csvContent)
                ->header('Content-Type', 'text/csv')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');
    }

    /**
     * Generate CSV content from array data
     */
    private function generateCSV($data)
    {
        if (empty($data)) {
            return "No stock data available\n";
        }

        $output = fopen('php://temp', 'r+');

        // Add CSV headers
        fputcsv($output, array_keys($data[0]));

        // Add data rows
        foreach ($data as $row) {
            fputcsv($output, $row);
        }

        rewind($output);
        $csvContent = stream_get_contents($output);
        fclose($output);

        return $csvContent;
    }
}
