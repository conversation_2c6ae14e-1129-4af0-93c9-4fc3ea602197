@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
          <div class="col-sm mb-2 mb-sm-0">
            <h1 class="page-header-title">
                New Leave Request
                <a href="{{ route('leave-requests.index') }}" class="mr-4 float-right">
                    Back
                </a>
            </h1>
          </div>
          <!-- End Col -->
        </div>
    </div>
    <!-- End Page Header -->


    <x-form
        method="POST"
        action="{{ route('leave-requests.store') }}"
        has-files
        class="mt-4"
    >
        @include('app.leave-requests.form-inputs')

        <div class="mt-4">
            <a
                href="{{ route('leave-requests.index') }}"
                class="btn btn-light"
            >
                @lang('crud.common.back')
            </a>

            <button type="submit" class="btn btn-primary float-right">
                <!-- <i class="icon ion-md-save"></i> -->
                @lang('crud.common.create')
            </button>
        </div>
    </x-form>
   
</div>
@endsection


@push('scripts')

    <script type="text/javascript">
        $(".date-change").change(function(){
            var dateFrom = $('input[name="date_from"]').val();
            var dateTo = $('input[name="date_to"]').val();
            dateFrom = moment(dateFrom);
            dateTo = moment(dateTo);
            var days = dateTo.diff(dateFrom, 'days');
            $(".days").val(days + 1);
            console.log(dateFrom);
            console.log(dateTo);
            console.log(days);
        })
    </script>

@endpush