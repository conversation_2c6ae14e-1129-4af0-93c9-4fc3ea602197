<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LocationStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'code' => ['nullable', 'string', 'max:50', 'tenant_unique:locations,code'],
            'description' => ['nullable', 'string', 'max:1000'],
            'status_id' => ['required', 'tenant_exists:statuses,id'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => 'location name',
            'code' => 'location code',
            'description' => 'description',
            'status_id' => 'status',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'The location name is required.',
            'name.max' => 'The location name may not be greater than 255 characters.',
            'code.max' => 'The location code may not be greater than 50 characters.',
            'code.unique' => 'The location code has already been taken.',
            'description.max' => 'The description may not be greater than 1000 characters.',
            'status_id.required' => 'Please select a status.',
            'status_id.exists' => 'The selected status is invalid.',
        ];
    }
}
