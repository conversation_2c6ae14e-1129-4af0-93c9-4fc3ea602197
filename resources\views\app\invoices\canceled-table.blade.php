
<!-- Canceled Invoices Table -->
<div class="card-body p-0">
    <div class="table-responsive">
        <table id="canceledTable" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
            <thead class="thead-light">
                <tr>
                    <th class="text-left">Invoice #</th>
                    <th class="text-left">@lang('crud.customers.singular')</th>
                    <th class="text-left">Date</th>
                    <th class="text-left">Amount</th>
                    <th class="text-left">Reason</th>
                    <th class="text-left">Canceled By</th>
                    <th class="text-left">Created By</th>
                    <th class="text-center">Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse($canceledInvoices as $invoice)
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-soft-danger me-2">
                                <i class="bi bi-x-circle me-1"></i>Canceled
                            </span>
                            <span class="text-decoration-line-through">#{{ $invoice->invoice_id ?? '-' }}</span>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar avatar-xs avatar-circle">
                                    <span class="avatar-initials">{{ substr($invoice->customer->name ?? 'C', 0, 1) }}</span>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <h6 class="mb-0 text-decoration-line-through">{{ $invoice->customer->name ?? '-' }}</h6>
                                <small class="text-muted">{{ $invoice->customer->phone ?? '' }}</small>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex flex-column">
                            <span>{{ optional($invoice->created_at)->format('M d, Y') }}</span>
                            <small class="text-muted">Canceled: {{ optional($invoice->updated_at)->format('M d, Y') }}</small>
                        </div>
                    </td>
                    <td>
                        <span class="text-decoration-line-through">{{ $invoice->amount_total ? _money($invoice->amount_total) : '-' }}</span>
                    </td>
                    <td>
                        <div class="d-inline-block text-truncate" style="max-width: 150px;" title="{{ $invoice->description ?? 'No reason provided' }}">
                            {{ $invoice->description ?? 'No reason provided' }}
                        </div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar avatar-xs avatar-soft-danger avatar-circle">
                                    <span class="avatar-initials">{{ substr($invoice->canceledBy->name ?? 'A', 0, 1) }}</span>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <span class="d-block">{{ $invoice->canceledBy->name ?? 'System' }}</span>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar avatar-xs avatar-soft-secondary avatar-circle">
                                    <span class="avatar-initials">{{ substr($invoice->createdBy->name ?? 'U', 0, 1) }}</span>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <span class="d-block">{{ $invoice->createdBy->name ?? '-' }}</span>
                            </div>
                        </div>
                    </td>
                    <td class="text-center" style="width: 100px;">
                        <div class="btn-group" role="group" aria-label="Row Actions">
                            @can('view', $invoice)
                            <button type="button" class="btn btn-soft-secondary btn-sm preview-invoice-btn"
                                    data-bs-toggle="modal" data-id="{{ $invoice->id }}" data-bs-target=".preview-invoice-modal"
                                    title="View Canceled Invoice">
                                <i class="bi bi-eye"></i>
                            </button>

                            <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-soft-secondary btn-sm"
                               title="Invoice Details">
                                <i class="bi bi-file-earmark-text"></i>
                            </a>
                            @endcan

                            @if(auth()->user()->can('restore invoices'))
                            <button type="button" class="btn btn-soft-success btn-sm"
                                    data-bs-toggle="modal" data-bs-target="#restoreInvoice{{ $invoice->id }}"
                                    title="Restore Invoice">
                                <i class="bi bi-arrow-counterclockwise"></i>
                            </button>

                            <!-- Restore Modal -->
                            <div class="modal fade" id="restoreInvoice{{ $invoice->id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Restore Invoice</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Are you sure you want to restore Invoice #{{ $invoice->invoice_id }}?</p>
                                            <p class="text-muted mb-0">This will reactivate the invoice and make it available again.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                                            <a href="#" class="btn btn-success">Restore Invoice</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="8" class="text-center p-5">
                        <div class="avatar avatar-lg avatar-soft-success avatar-circle mb-3 mx-auto">
                            <span class="avatar-initials"><i class="bi bi-check-circle"></i></span>
                        </div>
                        <h5>No Canceled Invoices</h5>
                        <p class="mb-0 text-muted">There are no canceled invoices in the system.</p>
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination and Summary Footer -->
@if(count($canceledInvoices) > 0)
<div class="card-footer bg-light">
    <div class="row align-items-center">
        <div class="col-md-6 mb-2 mb-md-0">
            <span>Showing {{ count($canceledInvoices) }} canceled invoices</span>
        </div>
        <div class="col-md-6">
            <div class="d-flex justify-content-md-end">
                <!-- Pagination would go here if needed -->
            </div>
        </div>
    </div>
</div>
@endif

