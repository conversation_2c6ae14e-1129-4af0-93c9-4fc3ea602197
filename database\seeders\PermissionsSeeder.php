<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\PermissionRegistrar;

class PermissionsSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        // Create all roles
        $this->createRoles();

        // Create all permissions by category
        $this->createGeneralPermissions();
        $this->createInventoryPermissions();
        $this->createSalesPermissions();
        $this->createPurchasingPermissions();
        $this->createAccountingPermissions();
        $this->createManufacturingPermissions();
        $this->createHRPermissions();
        $this->createAdminPermissions();

        // Assign permissions to roles
        $this->assignPermissionsToRoles();

        // Assign super-admin role to default admin user
        $user = \App\Models\User::whereEmail('<EMAIL>')->first();
        if ($user) {
            $user->assignRole('super-admin');
        }
    }

    /**
     * Create all roles
     */
    private function createRoles()
    {
        $roles = [
            'super-admin',
            'staff',
            'supervisor',
            'auditor',
            'accountant',
            'finance-manager',
            'asset-manager',
            'tax-manager',
            'budget-manager',
            'production-manager',
            'warehouse-manager',
            'sales-manager'
        ];

        foreach ($roles as $role) {
            Role::firstOrCreate(['name' => $role]);
        }
    }

    /**
     * Create general permissions
     */
    private function createGeneralPermissions()
    {
        // Approval permissions
        $this->createPermission('approve discount');
        $this->createPermission('approve transfer');
        $this->createPermission('approve stock');
        $this->createPermission('approve invoice');
        $this->createPermission('approve order');

        // Branch permissions
        $this->createResourcePermissions('branches');

        // Business Type permissions
        $this->createResourcePermissions('businesstypes');

        // Category permissions
        $this->createResourcePermissions('categories');

        // Customer permissions
        $this->createResourcePermissions('customers');

        // Status permissions
        $this->createResourcePermissions('statuses');

        // Supplier permissions
        $this->createResourcePermissions('suppliers');

        // Unit permissions
        $this->createResourcePermissions('units');

        // Debtor permissions
        $this->createResourcePermissions('debtors');

        // Expense permissions
        $this->createResourcePermissions('expenses');

        // Location permissions
        $this->createResourcePermissions('locations');

        // Transfer permissions
        $this->createResourcePermissions('transfers');

        // Stock Adjustment permissions
        $this->createResourcePermissions('stock-adjustments');
    }

    /**
     * Create inventory permissions
     */
    private function createInventoryPermissions()
    {
        // Product permissions (also create 'items' alias for backward compatibility)
        $this->createResourcePermissions('products');
        $this->createResourcePermissions('items');

        // Stock permissions
        $this->createResourcePermissions('stocks');

        // Stock Item permissions
        $this->createResourcePermissions('stock-items');

        // Warehouse permissions - standardized
        $this->createResourcePermissions('warehouses');
    }

    /**
     * Create sales permissions
     */
    private function createSalesPermissions()
    {
        // Invoice permissions with extended actions
        $this->createExtendedResourcePermissions('invoices', ['cancel', 'view canceled']);

        // Sales permissions
        $this->createResourcePermissions('sales');

        // Quotation permissions
        $this->createResourcePermissions('quotations');

        // Sales Order permissions (if different from sales)
        $this->createResourcePermissions('sales-orders');

        // Sales Return permissions
        $this->createResourcePermissions('sales-returns');
    }

    /**
     * Create purchasing permissions
     */
    private function createPurchasingPermissions()
    {
        // Order permissions with extended actions
        $this->createExtendedResourcePermissions('orders', ['cancel', 'view canceled']);
    }

    /**
     * Create accounting permissions
     */
    private function createAccountingPermissions()
    {
        // Chart of Accounts
        $this->createResourcePermissions('account-types');
        $this->createResourcePermissions('account-categories');
        $this->createResourcePermissions('accounts');

        // General Ledger
        $this->createExtendedResourcePermissions('fiscal-years', ['close', 'reopen']);
        $this->createExtendedResourcePermissions('fiscal-periods', ['close', 'reopen']);
        $this->createExtendedResourcePermissions('journal-entries', ['post', 'unpost']);

        // Financial Reports
        $this->createResourcePermissions('financial-reports');

        // Shipments
        $this->createResourcePermissions('shipments');

        // Fixed Assets
        $this->createResourcePermissions('asset-categories');
        $this->createExtendedResourcePermissions('assets', ['dispose']);
        $this->createResourcePermissions('asset-depreciations');

        // Banking
        $this->createResourcePermissions('bank-accounts');
        $this->createExtendedResourcePermissions('bank-reconciliations', ['complete']);

        // Tax Management
        $this->createResourcePermissions('tax-types');
        $this->createResourcePermissions('tax-transactions');

        // Budgeting
        $this->createResourcePermissions('budgets');

        // Multi-Currency Support
        $this->createResourcePermissions('currencies');
        $this->createResourcePermissions('exchange-rates');

        // Payment permissions
        $this->createResourcePermissions('payments');

        // Transaction permissions
        $this->createResourcePermissions('transactions');

        // Recurring Entry permissions
        $this->createResourcePermissions('recurring-entries');

        // Document Type permissions
        $this->createResourcePermissions('document-types');
    }

    /**
     * Create manufacturing permissions
     */
    private function createManufacturingPermissions()
    {
        // BOM permissions - standardized
        $this->createResourcePermissions('boms');

        // BOM Item permissions
        $this->createResourcePermissions('bom-items');

        // Production Orders
        $this->createExtendedResourcePermissions('production-orders', ['complete']);

        // Production Order Items
        $this->createResourcePermissions('production-order-items');

        // Work Order permissions (alias for production orders)
        $this->createResourcePermissions('work-orders');
    }

    /**
     * Create HR permissions
     */
    private function createHRPermissions()
    {
        // Employee permissions
        $this->createResourcePermissions('employees');

        // Employee Task permissions
        $this->createResourcePermissions('employee-tasks');

        // Employee Time Record permissions
        $this->createResourcePermissions('employee-time-records');

        // Employee Deduction Contribution permissions
        $this->createResourcePermissions('employee-deduction-contributions');

        // Employee Loan permissions
        $this->createResourcePermissions('employee-loans');

        // Leave permissions
        $this->createResourcePermissions('leaves');

        // Leave Request permissions
        $this->createResourcePermissions('leave-requests');

        // Monthly Payroll permissions
        $this->createResourcePermissions('monthly-payrolls');

        // Payroll Item permissions
        $this->createResourcePermissions('payroll-items');

        // Contribution permissions
        $this->createResourcePermissions('contributions');

        // Deduction permissions
        $this->createResourcePermissions('deductions');

        // Deduction Contribution permissions
        $this->createResourcePermissions('deduction-contributions');

        // Tax Bracket permissions
        $this->createResourcePermissions('tax-brackets');

        // Work Hours Configuration permissions
        $this->createResourcePermissions('work-hours-configurations');
    }

    /**
     * Create admin permissions
     */
    private function createAdminPermissions()
    {
        // Role permissions
        $this->createResourcePermissions('roles');

        // Permission permissions
        $this->createResourcePermissions('permissions');

        // User permissions
        $this->createResourcePermissions('users');

        // User Invitation permissions
        $this->createResourcePermissions('user-invitations');

        // Tenant permissions
        $this->createResourcePermissions('tenants');

        // User Tenant permissions
        $this->createResourcePermissions('user-tenants');
    }

    /**
     * Create a single permission if it doesn't exist
     */
    private function createPermission($name)
    {
        Permission::firstOrCreate(['name' => $name]);
    }

    /**
     * Create standard CRUD permissions for a resource
     * Standard permissions: list, view, create, update, delete
     */
    private function createResourcePermissions($resource)
    {
        $standardPermissions = ['list', 'view', 'create', 'update', 'delete'];

        foreach ($standardPermissions as $action) {
            $this->createPermission("{$action} {$resource}");
        }
    }

    /**
     * Create extended permissions for a resource with additional actions
     */
    private function createExtendedResourcePermissions($resource, array $additionalActions = [])
    {
        // Create standard CRUD permissions first
        $this->createResourcePermissions($resource);

        // Create additional permissions
        foreach ($additionalActions as $action) {
            $this->createPermission("{$action} {$resource}");
        }
    }

    /**
     * Assign permissions to roles
     */
    private function assignPermissionsToRoles()
    {
        // Get all roles
        $superAdmin = Role::where('name', 'super-admin')->first();
        $staff = Role::where('name', 'staff')->first();
        $supervisor = Role::where('name', 'supervisor')->first();
        $accountant = Role::where('name', 'accountant')->first();
        $financeManager = Role::where('name', 'finance-manager')->first();
        $assetManager = Role::where('name', 'asset-manager')->first();
        $taxManager = Role::where('name', 'tax-manager')->first();
        $budgetManager = Role::where('name', 'budget-manager')->first();
        $productionManager = Role::where('name', 'production-manager')->first();
        $warehouseManager = Role::where('name', 'warehouse-manager')->first();
        $salesManager = Role::where('name', 'sales-manager')->first();

        // Super Admin gets all permissions
        $superAdmin->givePermissionTo(Permission::all());

        // Staff gets basic permissions
        $staffPermissions = Permission::whereNotIn('name', [
            'list roles', 'view roles', 'create roles', 'update roles', 'delete roles',
            'list permissions', 'view permissions', 'create permissions', 'update permissions', 'delete permissions',
            'list users', 'view users', 'create users', 'update users', 'delete users',
        ])->get();
        $staff->givePermissionTo($staffPermissions);

        // Supervisor gets all permissions that staff has plus approval permissions
        $supervisor->givePermissionTo(Permission::all());

        // Accountant permissions
        $accountantPermissions = [
            'list accounts', 'view accounts', 'create accounts', 'update accounts',
            'list journal-entries', 'view journal-entries', 'create journal-entries', 'update journal-entries',
            'post journal-entries',
            'list financial-reports', 'view financial-reports',
            'list bank-accounts', 'view bank-accounts',
            'list bank-reconciliations', 'view bank-reconciliations', 'create bank-reconciliations', 'update bank-reconciliations',
            'complete bank-reconciliations',
            'list tax-transactions', 'view tax-transactions',
            'list warehouses', 'view warehouses',
            'list boms', 'view boms',
        ];
        $accountant->givePermissionTo($accountantPermissions);

        // Finance Manager permissions
        $financeManagerPermissions = [
            'list account-types', 'view account-types', 'create account-types', 'update account-types',
            'list account-categories', 'view account-categories', 'create account-categories', 'update account-categories',
            'list accounts', 'view accounts', 'create accounts', 'update accounts', 'delete accounts',
            'list fiscal-years', 'view fiscal-years', 'create fiscal-years', 'update fiscal-years',
            'close fiscal-years', 'reopen fiscal-years',
            'list fiscal-periods', 'view fiscal-periods', 'create fiscal-periods', 'update fiscal-periods',
            'close fiscal-periods', 'reopen fiscal-periods',
            'list journal-entries', 'view journal-entries', 'create journal-entries', 'update journal-entries', 'delete journal-entries',
            'post journal-entries', 'unpost journal-entries',
            'list financial-reports', 'view financial-reports',
            'list currencies', 'view currencies', 'create currencies', 'update currencies',
            'list exchange-rates', 'view exchange-rates', 'create exchange-rates', 'update exchange-rates',
            'list bank-accounts', 'view bank-accounts', 'create bank-accounts', 'update bank-accounts', 'delete bank-accounts',
            'list bank-reconciliations', 'view bank-reconciliations', 'create bank-reconciliations', 'update bank-reconciliations', 'delete bank-reconciliations',
            'complete bank-reconciliations',
        ];
        $financeManager->givePermissionTo($financeManagerPermissions);

        // Asset Manager permissions
        $assetManagerPermissions = [
            'list asset-categories', 'view asset-categories', 'create asset-categories', 'update asset-categories',
            'list assets', 'view assets', 'create assets', 'update assets', 'delete assets', 'dispose assets',
            'list asset-depreciations', 'view asset-depreciations', 'create asset-depreciations',
            'list journal-entries', 'view journal-entries',
            'list financial-reports', 'view financial-reports',
        ];
        $assetManager->givePermissionTo($assetManagerPermissions);

        // Tax Manager permissions
        $taxManagerPermissions = [
            'list tax-types', 'view tax-types', 'create tax-types', 'update tax-types', 'delete tax-types',
            'list tax-transactions', 'view tax-transactions',
            'list journal-entries', 'view journal-entries',
            'list financial-reports', 'view financial-reports',
        ];
        $taxManager->givePermissionTo($taxManagerPermissions);

        // Budget Manager permissions
        $budgetManagerPermissions = [
            'list budgets', 'view budgets', 'create budgets', 'update budgets', 'delete budgets',
            'list accounts', 'view accounts',
            'list financial-reports', 'view financial-reports',
        ];
        $budgetManager->givePermissionTo($budgetManagerPermissions);

        // Production Manager permissions
        $productionManagerPermissions = [
            'list boms', 'view boms', 'create boms', 'update boms', 'delete boms',
            'list bom-items', 'view bom-items', 'create bom-items', 'update bom-items', 'delete bom-items',
            'list production-orders', 'view production-orders', 'create production-orders', 'update production-orders', 'delete production-orders',
            'complete production-orders',
            'list production-order-items', 'view production-order-items', 'create production-order-items', 'update production-order-items', 'delete production-order-items',
            'list work-orders', 'view work-orders', 'create work-orders', 'update work-orders', 'delete work-orders',
            'list journal-entries', 'view journal-entries',
        ];
        $productionManager->givePermissionTo($productionManagerPermissions);

        // Warehouse Manager permissions
        $warehouseManagerPermissions = [
            'list warehouses', 'view warehouses', 'create warehouses', 'update warehouses', 'delete warehouses',
            'list stocks', 'view stocks', 'create stocks', 'update stocks', 'delete stocks',
            'list stock-items', 'view stock-items', 'create stock-items', 'update stock-items', 'delete stock-items',
            'list transfers', 'view transfers', 'create transfers', 'update transfers', 'delete transfers',
            'approve transfer', 'approve stock',
            'list stock-adjustments', 'view stock-adjustments', 'create stock-adjustments', 'update stock-adjustments', 'delete stock-adjustments',
            'list locations', 'view locations', 'create locations', 'update locations', 'delete locations',
        ];
        $warehouseManager->givePermissionTo($warehouseManagerPermissions);

        // Sales Manager permissions
        $salesManagerPermissions = [
            'list invoices', 'view invoices', 'create invoices', 'update invoices', 'delete invoices',
            'view canceled invoices', 'cancel invoices', 'approve invoice',
            'list sales', 'view sales', 'create sales', 'update sales', 'delete sales',
            'list quotations', 'view quotations', 'create quotations', 'update quotations', 'delete quotations',
            'list sales-orders', 'view sales-orders', 'create sales-orders', 'update sales-orders', 'delete sales-orders',
            'list sales-returns', 'view sales-returns', 'create sales-returns', 'update sales-returns', 'delete sales-returns',
            'list customers', 'view customers', 'create customers', 'update customers', 'delete customers',
            'list products', 'view products', 'list items', 'view items',
            'list stocks', 'view stocks',
            'list shipments', 'view shipments', 'create shipments', 'update shipments', 'delete shipments',
        ];
        $salesManager->givePermissionTo($salesManagerPermissions);
    }
}
