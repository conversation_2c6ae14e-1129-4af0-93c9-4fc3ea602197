@extends('layouts.app')

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">building</x-slot>
        <x-slot name="title">{{ $supplier->name }}</x-slot>
        <x-slot name="subtitle">Supplier payables and outstanding bills</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('payables.index') }}">Payables</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ $supplier->name }}</li>
        </x-slot>
        <x-slot name="controls">
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-primary btn-sm"
                        onclick="openPaymentModal({
                            paymentType: 'payable',
                            entityId: '{{ $supplier->id }}'
                        })">
                    <i class="bi bi-credit-card me-1"></i>Make Payment
                </button>
                <a href="{{ route('payables.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Payables
                </a>
            </div>
        </x-slot>
    </x-page-header>

    <div class="row">
        <!-- Supplier Information -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-building me-2"></i>Supplier Information
                    </h4>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-4">
                        <div class="avatar avatar-lg avatar-circle me-3">
                            <span class="avatar-initials bg-warning text-white h4 mb-0">
                                {{ substr($supplier->name, 0, 1) }}
                            </span>
                        </div>
                        <div>
                            <h5 class="mb-1">{{ $supplier->name }}</h5>
                            @if($supplier->company)
                                <p class="text-muted mb-0">{{ $supplier->company }}</p>
                            @endif
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            @if($supplier->email)
                            <div class="mb-3">
                                <label class="form-label text-muted">Email</label>
                                <div class="fw-semibold">{{ $supplier->email }}</div>
                            </div>
                            @endif

                            @if($supplier->phone)
                            <div class="mb-3">
                                <label class="form-label text-muted">Phone</label>
                                <div class="fw-semibold">{{ $supplier->phone }}</div>
                            </div>
                            @endif

                            @if($supplier->address)
                            <div class="mb-3">
                                <label class="form-label text-muted">Address</label>
                                <div class="fw-semibold">{{ $supplier->address }}</div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Outstanding Summary -->
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-graph-up me-2"></i>Outstanding Summary
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <div class="h4 text-danger mb-1">{{ _money($supplierTotals['total_outstanding']) }}</div>
                                <div class="text-muted small">Outstanding</div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <div class="h6 mb-1">{{ $supplierTotals['order_count'] }}</div>
                                <div class="text-muted small">Bills</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-3">
                                <div class="h6 mb-1">{{ _money($supplierTotals['total_ordered']) }}</div>
                                <div class="text-muted small">Total Ordered</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-3">
                                <div class="h6 mb-1 text-success">{{ _money($supplierTotals['total_paid']) }}</div>
                                <div class="text-muted small">Total Paid</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Outstanding Bills -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="row justify-content-between align-items-center flex-grow-1">
                        <div class="col-md">
                            <h4 class="card-header-title">
                                <i class="bi bi-receipt me-2"></i>Outstanding Bills
                            </h4>
                        </div>
                        <div class="col-auto">
                            <span class="text-muted">{{ $supplier->orders->count() }} outstanding bill(s)</span>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                            <thead class="thead-light">
                                <tr>
                                    <th style="width: 15%">Order #</th>
                                    <th style="width: 12%">Date</th>
                                    <th style="width: 12%">Due Date</th>
                                    <th style="width: 15%">Total</th>
                                    <th style="width: 15%">Paid</th>
                                    <th style="width: 15%">Balance</th>
                                    <th style="width: 10%">Status</th>
                                    <th style="width: 10%" class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($supplier->orders as $order)
                                <tr>
                                    <!-- Order Number -->
                                    <td>
                                        <span class="fw-semibold">#{{ $order->id }}</span>
                                        @if($order->order_number)
                                            <small class="text-muted d-block">{{ $order->order_number }}</small>
                                        @endif
                                    </td>
                                    
                                    <!-- Date -->
                                    <td>
                                        <span class="fw-semibold">{{ $order->created_at->format('M d, Y') }}</span>
                                    </td>
                                    
                                    <!-- Due Date -->
                                    <td>
                                        @if(isset($order->due_date) && $order->due_date)
                                            <span class="fw-semibold {{ $order->due_date->isPast() ? 'text-danger' : '' }}">
                                                {{ $order->due_date->format('M d, Y') }}
                                            </span>
                                            @if($order->due_date->isPast())
                                                <small class="text-danger d-block">{{ $order->due_date->diffForHumans() }}</small>
                                            @endif
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    
                                    <!-- Total -->
                                    <td>
                                        <span class="fw-semibold">{{ _money($order->amount_total) }}</span>
                                    </td>
                                    
                                    <!-- Paid -->
                                    <td>
                                        <span class="fw-semibold text-success">{{ _money($order->amount_paid) }}</span>
                                    </td>
                                    
                                    <!-- Balance -->
                                    <td>
                                        <span class="fw-semibold text-danger">{{ _money($order->balance) }}</span>
                                    </td>
                                    
                                    <!-- Status -->
                                    <td>
                                        @if($order->balance <= 0)
                                            <span class="badge bg-success">Paid</span>
                                        @elseif(isset($order->due_date) && $order->due_date && $order->due_date->isPast())
                                            <span class="badge bg-danger">Overdue</span>
                                        @elseif($order->amount_paid > 0)
                                            <span class="badge bg-warning">Partial</span>
                                        @else
                                            <span class="badge bg-info">Outstanding</span>
                                        @endif
                                    </td>
                                    
                                    <!-- Actions -->
                                    <td class="text-center">
                                        <div class="d-flex gap-1 justify-content-center">
                                            <a href="{{ route('orders.show', $order) }}"
                                               class="btn btn-outline-primary btn-sm"
                                               title="View Order">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <button type="button"
                                                    class="btn btn-outline-warning btn-sm"
                                                    title="Make Payment"
                                                    onclick="openPaymentModal({
                                                        paymentType: 'payable',
                                                        entityId: '{{ $supplier->id }}',
                                                        invoiceId: '{{ $order->id }}'
                                                    })">
                                                <i class="bi bi-credit-card"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center p-5">
                                        <x-empty-state
                                            icon="receipt"
                                            title="No outstanding bills"
                                            description="This supplier has no outstanding bills."
                                        />
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Recent Payments -->
            @if($supplier->orders->flatMap->payments->count() > 0)
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-credit-card me-2"></i>Recent Payments
                    </h4>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                            <thead class="thead-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Order</th>
                                    <th>Amount</th>
                                    <th>Reference</th>
                                    <th>Method</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($supplier->orders->flatMap->payments->sortByDesc('created_at')->take(5) as $payment)
                                <tr>
                                    <td>{{ $payment->created_at->format('M d, Y') }}</td>
                                    <td>#{{ $payment->paymentable_id }}</td>
                                    <td><span class="fw-semibold text-success">{{ _money($payment->amount) }}</span></td>
                                    <td>{{ $payment->reference_no ?? '-' }}</td>
                                    <td>{{ $payment->payment_method ?? '-' }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Include Payment Modal -->
<x-payment-modal />
@endsection
