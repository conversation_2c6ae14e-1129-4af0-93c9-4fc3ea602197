@extends('layouts.app')

@section('content')
<div class="container">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-header-title">@lang('crud.warehouses.edit_title')</h1>
                <p class="page-header-text">Update warehouse information</p>
            </div>
            <div class="col-auto">
                <div class="btn-group" role="group">
                    <a href="{{ route('warehouses.index') }}" class="btn btn-ghost-secondary">
                        <i class="bi-arrow-left me-1"></i> @lang('crud.common.back')
                    </a>
                    <a href="{{ route('warehouses.show', $warehouse) }}" class="btn btn-ghost-secondary">
                        <i class="bi-eye me-1"></i> View
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mb-3 mb-lg-0">
            <!-- Card -->
            <div class="card mb-3 mb-lg-5">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Warehouse Information</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <x-form
                        method="PUT"
                        action="{{ route('warehouses.update', $warehouse) }}"
                        has-files
                        id="warehouseForm"
                    >
                        @include('app.warehouses.form-inputs')
                    </x-form>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>

        <div class="col-lg-4">
            <!-- Card -->
            <div class="card">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Actions</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <!-- Form Group -->
                        <div class="mb-4">
                            <label class="form-label">Last Updated</label>
                            <div class="mb-2">
                                <span class="text-body">{{ $warehouse->updated_at->format('F d, Y H:i') }}</span>
                            </div>
                        </div>
                        <!-- End Form Group -->
                        
                        <div class="d-flex justify-content-end">
                            <a href="{{ route('warehouses.index') }}" class="btn btn-white me-2">Cancel</a>
                            <button type="submit" form="warehouseForm" class="btn btn-primary">
                                <i class="bi-check-circle me-1"></i> @lang('crud.common.update')
                            </button>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="mt-2">
                            <a href="{{ route('warehouses.create') }}" class="btn btn-outline-primary btn-sm">
                                <i class="bi-plus-circle me-1"></i> @lang('crud.common.create')
                            </a>
                            
                            @can('delete', $warehouse)
                            <form action="{{ route('warehouses.destroy', $warehouse) }}" 
                                  method="POST" 
                                  class="mt-2"
                                  onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')">
                                @csrf @method('DELETE')
                                <button type="submit" class="btn btn-outline-danger btn-sm">
                                    <i class="bi-trash me-1"></i> @lang('crud.common.delete')
                                </button>
                            </form>
                            @endcan
                        </div>
                    </div>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>
    </div>
</div>
@endsection
