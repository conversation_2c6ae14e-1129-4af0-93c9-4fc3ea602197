@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-sm mb-2 mb-sm-0">
                <h1 class="page-header-title">
                    <i class="bi-truck me-2"></i>
                    Shipments
                    <span class="badge bg-soft-dark text-dark ms-2">{{ $shipments->total() }}</span>
                </h1>
            </div>
            <div class="col-sm-auto">
                @can('create', App\Models\Shipment::class)
                <a href="{{ route('shipments.create') }}" class="btn btn-primary">
                    <i class="bi-plus-lg me-1"></i>
                    New Shipment
                </a>
                @endcan
            </div>
        </div>
    </div>
    <!-- End Page Header -->

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h4 class="card-title">
                <i class="bi-funnel me-2"></i>
                Filters
            </h4>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('shipments.index') }}">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ $search }}" placeholder="Customer, tracking, reference...">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Statuses</option>
                            @foreach($statuses as $statusId => $statusName)
                                <option value="{{ $statusId }}" {{ $status == $statusId ? 'selected' : '' }}>
                                    {{ $statusName }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="customer" class="form-label">Customer</label>
                        <select class="form-select" id="customer" name="customer">
                            <option value="">All Customers</option>
                            @foreach($customers as $customerId => $customerName)
                                <option value="{{ $customerId }}" {{ $customer == $customerId ? 'selected' : '' }}>
                                    {{ $customerName }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="{{ $dateFrom }}">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="{{ $dateTo }}">
                    </div>
                    <div class="col-md-1 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi-search"></i>
                        </button>
                        <a href="{{ route('shipments.index') }}" class="btn btn-outline-secondary">
                            <i class="bi-arrow-clockwise"></i>
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- End Filters -->

    <!-- Shipments Table -->
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                <i class="bi-table me-2"></i>
                Shipments List
            </h4>
        </div>
        <div class="table-responsive table-dropdown-space">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                <thead class="thead-light">
                    <tr>
                        <th>Shipment #</th>
                        <th>@lang('crud.customers.singular')</th>
                        <th>Status</th>
                        <th>Tracking</th>
                        <th>Amount</th>
                        <th>Created By</th>
                        <th>Date</th>
                        <th class="text-end">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($shipments as $shipment)
                    <tr>
                        <td>
                            <button type="button" class="btn btn-link p-0 text-decoration-none preview-shipment-btn"
                                    data-bs-toggle="modal"
                                    data-bs-target="#previewShipmentModal"
                                    data-id="{{ $shipment->id }}">
                                <span class="fw-semibold text-primary">#{{ $shipment->shipment_id }}</span>
                            </button>
                            @if($shipment->reference_no)
                                <br><small class="text-muted">Ref: {{ $shipment->reference_no }}</small>
                            @endif
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-sm avatar-circle me-2">
                                    <span class="avatar-initials avatar-initials-sm bg-soft-primary text-primary">
                                        {{ substr($shipment->customer_name ?? 'N/A', 0, 1) }}
                                    </span>
                                </div>
                                <div>
                                    <span class="fw-semibold">{{ $shipment->customer_name ?? 'N/A' }}</span>
                                    @if($shipment->customer?->email)
                                        <br><small class="text-muted">{{ $shipment->customer->email }}</small>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge {{ $shipment->status_badge_class }}">
                                {{ $shipment->status_name }}
                            </span>
                        </td>
                        <td>
                            @if($shipment->tracking_number)
                                <span class="fw-semibold">{{ $shipment->tracking_number }}</span>
                                @if($shipment->carrier)
                                    <br><small class="text-muted">{{ $shipment->carrier }}</small>
                                @endif
                            @else
                                <span class="text-muted">-</span>
                            @endif
                        </td>
                        <td>
                            <span class="fw-semibold">{{ _money($shipment->amount_total) }}</span>
                            @if($shipment->discount > 0)
                                <br><small class="text-muted">Discount: {{ _money($shipment->discount) }}</small>
                            @endif
                        </td>
                        <td>
                            <span>{{ $shipment->createdBy->name ?? 'N/A' }}</span>
                            <br><small class="text-muted">{{ $shipment->created_at->format('M d, Y') }}</small>
                        </td>
                        <td>
                            <span>{{ $shipment->created_at->format('M d, Y') }}</span>
                            <br><small class="text-muted">{{ $shipment->created_at->format('h:i A') }}</small>
                        </td>
                        <td class="text-end">
                            <div class="btn-group" role="group">
                                @can('view', $shipment)
                                <button type="button" class="btn btn-sm btn-outline-primary preview-shipment-btn"
                                        data-bs-toggle="modal"
                                        data-bs-target="#previewShipmentModal"
                                        data-id="{{ $shipment->id }}">
                                    <i class="bi-eye"></i>
                                </button>
                                @endcan

                                @if(!$shipment->approved_by)
                                    @can('update', $shipment)
                                    <a href="{{ route('shipments.edit', $shipment) }}" class="btn btn-sm btn-outline-secondary">
                                        <i class="bi-pencil"></i>
                                    </a>
                                    @endcan
                                @endif

                                <!-- Dropdown for more actions -->
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        @if($shipment->invoice_id)
                                            <li>
                                                <a href="{{ route('invoices.show', $shipment->invoice_id) }}" class="dropdown-item">
                                                    <i class="bi-receipt me-2"></i>View Invoice #{{ str_pad($shipment->invoice_id, 5, '0', STR_PAD_LEFT) }}
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                        @elseif(in_array($shipment->status_id, ['11', '12', '13']) && $shipment->sales->count() > 0)
                                            @can('update', $shipment)
                                            <li>
                                                <form action="{{ route('shipments.convert-to-invoice', $shipment) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="dropdown-item" onclick="return confirm('Convert this shipment to invoice? This will create a new invoice with all shipment items.')">
                                                        <i class="bi-receipt me-2"></i>Convert to Invoice
                                                    </button>
                                                </form>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            @endcan
                                        @endif

                                        <li>
                                            <button type="button" class="dropdown-item preview-shipment-btn"
                                                    data-bs-toggle="modal"
                                                    data-bs-target=".preview-shipment-modal"
                                                    data-id="{{ $shipment->id }}">
                                                <i class="bi-printer me-2"></i>Print
                                            </button>
                                        </li>
                                        <li>
                                            <a href="{{ route('shipments.show', $shipment) }}" class="dropdown-item" onclick="setTimeout(() => createPDFfromHTML('shipment'), 1000)">
                                                <i class="bi-file-earmark-pdf me-2"></i>Download PDF
                                            </a>
                                        </li>

                                        @can('create', App\Models\Shipment::class)
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <form action="{{ route('shipments.duplicate', $shipment) }}" method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="dropdown-item">
                                                    <i class="bi-files me-2"></i>Duplicate
                                                </button>
                                            </form>
                                        </li>
                                        @endcan

                                        @if(!$shipment->approved_by)
                                            @can('delete', $shipment)
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <form action="{{ route('shipments.destroy', $shipment) }}" method="POST" class="d-inline">
                                                    @csrf @method('DELETE')
                                                    <button type="submit" class="dropdown-item text-danger" onclick="return confirm('Are you sure you want to delete this shipment?')">
                                                        <i class="bi-trash me-2"></i>Delete
                                                    </button>
                                                </form>
                                            </li>
                                            @endcan
                                        @endif
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="text-center">
                                <i class="bi-truck display-4 text-muted"></i>
                                <h5 class="mt-2">No shipments found</h5>
                                <p class="text-muted">Try adjusting your search criteria or create a new shipment.</p>
                                @can('create', App\Models\Shipment::class)
                                <a href="{{ route('shipments.create') }}" class="btn btn-primary">
                                    <i class="bi-plus-lg me-1"></i>Create Shipment
                                </a>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($shipments->hasPages())
        <div class="card-footer">
            {{ $shipments->links() }}
        </div>
        @endif
    </div>

    <!-- Shipment Preview Modal -->
    <div class="modal fade" id="previewShipmentModal" tabindex="-1" aria-labelledby="previewShipmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="previewShipmentModalLabel">
                        <i class="bi-truck me-2"></i>
                        Shipment Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="shipmentModalContent">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading shipment details...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="#" id="viewFullShipmentBtn" class="btn btn-primary">
                        <i class="bi-arrow-right me-1"></i>
                        View Full Details
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- End Shipment Preview Modal -->

</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Handle shipment preview modal
    $('.preview-shipment-btn').on('click', function() {
        const shipmentId = $(this).data('id');
        const modalContent = $('#shipmentModalContent');
        const viewFullBtn = $('#viewFullShipmentBtn');

        // Reset modal content
        modalContent.html(`
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading shipment details...</p>
            </div>
        `);

        // Update view full button link
        viewFullBtn.attr('href', `/shipments/${shipmentId}`);

        // Fetch shipment data
        $.ajax({
            url: `/ajax-shipment/${shipmentId}`,
            method: 'GET',
            success: function(shipment) {
                // Update modal title
                $('#previewShipmentModalLabel').html(`
                    <i class="bi-truck me-2"></i>
                    Shipment #${shipment.shipment_id}
                    <span class="badge ${getStatusBadgeClass(shipment.status_id)} ms-2">${getStatusName(shipment.status_id)}</span>
                `);

                // Build modal content
                let content = `
                    <div class="row">
                        <div class="col-lg-8">
                            <!-- Shipment Info -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="bi-info-circle me-2"></i>
                                        Shipment Information
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <dl class="row">
                                                <dt class="col-sm-5">Customer:</dt>
                                                <dd class="col-sm-7">${shipment.customer_name || 'N/A'}</dd>

                                                <dt class="col-sm-5">Reference:</dt>
                                                <dd class="col-sm-7">${shipment.reference_no || '-'}</dd>

                                                <dt class="col-sm-5">Tracking:</dt>
                                                <dd class="col-sm-7">
                                                    ${shipment.tracking_number ?
                                                        `<span class="badge bg-soft-primary text-primary">${shipment.tracking_number}</span>` :
                                                        '<span class="text-muted">Not assigned</span>'
                                                    }
                                                </dd>

                                                <dt class="col-sm-5">Carrier:</dt>
                                                <dd class="col-sm-7">${shipment.carrier || '-'}</dd>
                                            </dl>
                                        </div>
                                        <div class="col-md-6">
                                            <dl class="row">
                                                <dt class="col-sm-5">Method:</dt>
                                                <dd class="col-sm-7">${shipment.shipping_method ? shipment.shipping_method.charAt(0).toUpperCase() + shipment.shipping_method.slice(1) : '-'}</dd>

                                                <dt class="col-sm-5">Weight:</dt>
                                                <dd class="col-sm-7">${shipment.weight ? shipment.weight + ' kg' : '-'}</dd>

                                                <dt class="col-sm-5">Dimensions:</dt>
                                                <dd class="col-sm-7">${shipment.dimensions || '-'}</dd>

                                                <dt class="col-sm-5">Insurance:</dt>
                                                <dd class="col-sm-7">${shipment.insurance_value ? formatMoney(shipment.insurance_value) : '-'}</dd>
                                            </dl>
                                        </div>
                                    </div>

                                    ${shipment.shipping_address ? `
                                        <div class="row mt-3">
                                            <div class="col-12">
                                                <dt>Shipping Address:</dt>
                                                <dd class="mt-1">
                                                    <div class="bg-soft-light p-3 rounded">
                                                        ${shipment.shipping_address}
                                                    </div>
                                                </dd>
                                            </div>
                                        </div>
                                    ` : ''}

                                    ${shipment.special_instructions ? `
                                        <div class="row mt-3">
                                            <div class="col-12">
                                                <dt>Special Instructions:</dt>
                                                <dd class="mt-1">
                                                    <div class="alert alert-warning mb-0">
                                                        ${shipment.special_instructions}
                                                    </div>
                                                </dd>
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>

                            <!-- Items -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="bi-list-ul me-2"></i>
                                        Shipment Items
                                    </h6>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-borderless table-nowrap table-align-middle mb-0">
                                        <thead class="thead-light">
                                            <tr>
                                                <th>Item</th>
                                                <th>Unit</th>
                                                <th class="text-end">Qty</th>
                                                <th class="text-end">Rate</th>
                                                <th class="text-end">Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>`;

                if (shipment.sales && shipment.sales.length > 0) {
                    shipment.sales.forEach(function(sale) {
                        content += `
                            <tr>
                                <td>
                                    <div>
                                        <span class="fw-semibold">${sale.product_name}</span>
                                        ${sale.product && sale.product.description ?
                                            `<br><small class="text-muted">${sale.product.description}</small>` : ''
                                        }
                                    </div>
                                </td>
                                <td>${sale.unit_name || '-'}</td>
                                <td class="text-end">${parseFloat(sale.quantity).toFixed(2)}</td>
                                <td class="text-end">${formatMoney(sale.selling_price)}</td>
                                <td class="text-end fw-semibold">${formatMoney(sale.selling_price * sale.quantity)}</td>
                            </tr>
                        `;
                    });
                } else {
                    content += `
                        <tr>
                            <td colspan="5" class="text-center py-4">
                                <span class="text-muted">No items in this shipment</span>
                            </td>
                        </tr>
                    `;
                }

                content += `
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <!-- Summary -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="bi-calculator me-2"></i>
                                        Summary
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <dl class="row">
                                        <dt class="col-sm-6">Subtotal:</dt>
                                        <dd class="col-sm-6 text-end">${formatMoney(shipment.sub_total)}</dd>

                                        ${shipment.discount > 0 ? `
                                            <dt class="col-sm-6">Discount:</dt>
                                            <dd class="col-sm-6 text-end text-danger">-${formatMoney(shipment.discount)}</dd>
                                        ` : ''}

                                        ${shipment.vat > 0 ? `
                                            <dt class="col-sm-6">VAT:</dt>
                                            <dd class="col-sm-6 text-end">${formatMoney(shipment.vat)}</dd>
                                        ` : ''}

                                        <dt class="col-sm-6 border-top pt-2"><strong>Total:</strong></dt>
                                        <dd class="col-sm-6 text-end border-top pt-2"><strong>${formatMoney(shipment.amount_total)}</strong></dd>

                                        ${shipment.amount_paid > 0 ? `
                                            <dt class="col-sm-6">Paid:</dt>
                                            <dd class="col-sm-6 text-end text-success">${formatMoney(shipment.amount_paid)}</dd>

                                            <dt class="col-sm-6">Balance:</dt>
                                            <dd class="col-sm-6 text-end ${(shipment.amount_total - shipment.amount_paid) > 0 ? 'text-danger' : 'text-success'}">
                                                ${formatMoney(shipment.amount_total - shipment.amount_paid)}
                                            </dd>
                                        ` : ''}
                                    </dl>
                                </div>
                            </div>

                            <!-- Timeline -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="bi-clock-history me-2"></i>
                                        Timeline
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item px-0">
                                            <div class="d-flex">
                                                <div class="flex-shrink-0">
                                                    <span class="badge bg-soft-primary text-primary">Created</span>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <div class="fw-semibold">${formatDate(shipment.created_at)}</div>
                                                    <div class="text-muted">by ${shipment.created_by ? shipment.created_by.name : 'System'}</div>
                                                </div>
                                            </div>
                                        </li>

                                        ${shipment.estimated_delivery ? `
                                            <li class="list-group-item px-0">
                                                <div class="d-flex">
                                                    <div class="flex-shrink-0">
                                                        <span class="badge bg-soft-info text-info">Est. Delivery</span>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <div class="fw-semibold">${formatDate(shipment.estimated_delivery)}</div>
                                                    </div>
                                                </div>
                                            </li>
                                        ` : ''}

                                        ${shipment.actual_delivery ? `
                                            <li class="list-group-item px-0">
                                                <div class="d-flex">
                                                    <div class="flex-shrink-0">
                                                        <span class="badge bg-soft-success text-success">Delivered</span>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <div class="fw-semibold">${formatDate(shipment.actual_delivery)}</div>
                                                    </div>
                                                </div>
                                            </li>
                                        ` : ''}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                modalContent.html(content);
            },
            error: function() {
                modalContent.html(`
                    <div class="text-center py-5">
                        <i class="bi-exclamation-triangle display-4 text-danger"></i>
                        <h5 class="mt-2">Error Loading Shipment</h5>
                        <p class="text-muted">Unable to load shipment details. Please try again.</p>
                    </div>
                `);
            }
        });
    });

    // Helper functions
    function getStatusBadgeClass(statusId) {
        const statusClasses = {
            '10': 'bg-soft-secondary text-secondary',
            '11': 'bg-soft-info text-info',
            '12': 'bg-soft-warning text-warning',
            '13': 'bg-soft-success text-success',
            '14': 'bg-soft-danger text-danger',
            '15': 'bg-soft-primary text-primary'
        };
        return statusClasses[statusId] || 'bg-soft-secondary text-secondary';
    }

    function getStatusName(statusId) {
        const statusNames = {
            '10': 'Draft',
            '11': 'Pending',
            '12': 'In Transit',
            '13': 'Delivered',
            '14': 'Cancelled',
            '15': 'Returned'
        };
        return statusNames[statusId] || 'Unknown';
    }

    function formatMoney(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount || 0);
    }

    function formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
});
</script>
@endpush

@endsection
