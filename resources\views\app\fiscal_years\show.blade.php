@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">
                <a href="{{ route('fiscal-years.index') }}" class="mr-4"
                    ><i class="icon ion-md-arrow-back"></i
                ></a>
                @lang('crud.fiscal_years.show_title')
            </h4>

            <div class="mt-4">
                <div class="mb-4">
                    <h5>@lang('crud.fiscal_years.inputs.name')</h5>
                    <span>{{ $fiscalYear->name ?? '-' }}</span>
                </div>
                <div class="mb-4">
                    <h5>@lang('crud.fiscal_years.inputs.start_date')</h5>
                    <span>{{ $fiscalYear->start_date->format('Y-m-d') ?? '-' }}</span>
                </div>
                <div class="mb-4">
                    <h5>@lang('crud.fiscal_years.inputs.end_date')</h5>
                    <span>{{ $fiscalYear->end_date->format('Y-m-d') ?? '-' }}</span>
                </div>
                <div class="mb-4">
                    <h5>@lang('crud.fiscal_years.inputs.status')</h5>
                    <span class="badge 
                        @if($fiscalYear->status == 'open') badge-success 
                        @elseif($fiscalYear->status == 'closed') badge-danger 
                        @else badge-warning 
                        @endif">
                        {{ ucfirst($fiscalYear->status) }}
                    </span>
                </div>
                <div class="mb-4">
                    <h5>@lang('crud.fiscal_years.inputs.is_active')</h5>
                    <span class="badge {{ $fiscalYear->is_active ? 'badge-success' : 'badge-danger' }}">
                        {{ $fiscalYear->is_active ? 'Active' : 'Inactive' }}
                    </span>
                </div>
                <div class="mb-4">
                    <h5>@lang('crud.fiscal_years.inputs.description')</h5>
                    <span>{{ $fiscalYear->description ?? '-' }}</span>
                </div>
            </div>

            <div class="mt-4">
                <div class="mb-4">
                    <h5>@lang('crud.common.created_at')</h5>
                    <span>{{ $fiscalYear->created_at ?? '-' }}</span>
                </div>
                <div class="mb-4">
                    <h5>@lang('crud.common.updated_at')</h5>
                    <span>{{ $fiscalYear->updated_at ?? '-' }}</span>
                </div>
            </div>

            <div class="mt-4">
                <a
                    href="{{ route('fiscal-years.index') }}"
                    class="btn btn-light"
                >
                    <i class="icon ion-md-return-left"></i>
                    @lang('crud.common.back')
                </a>

                @can('create', App\Models\FiscalYear::class)
                <a
                    href="{{ route('fiscal-years.create') }}"
                    class="btn btn-light"
                >
                    <i class="icon ion-md-add"></i> @lang('crud.common.create')
                </a>
                @endcan

                @if($fiscalYear->status == 'open')
                @can('update', $fiscalYear)
                <form
                    action="{{ route('fiscal-years.close', $fiscalYear) }}"
                    method="POST"
                    class="d-inline"
                    onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                >
                    @csrf
                    <button type="submit" class="btn btn-danger">
                        <i class="icon ion-md-lock"></i> Close Fiscal Year
                    </button>
                </form>
                @endcan
                @endif
            </div>
        </div>
    </div>

    @can('view-any', App\Models\FiscalPeriod::class)
    <div class="card mt-4">
        <div class="card-body">
            <h4 class="card-title w-100 mb-2">Fiscal Periods</h4>

            <livewire:fiscal-year-fiscal-periods-detail :fiscalYear="$fiscalYear" />
        </div>
    </div>
    @endcan
</div>
@endsection
