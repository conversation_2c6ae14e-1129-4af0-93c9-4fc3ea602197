<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OrderUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            // Order fields
            'supplier_id' => ['nullable', 'tenant_exists:suppliers,id'],
            'description' => ['nullable', 'max:1000', 'string'],
            'sub_total' => ['nullable', 'numeric', 'min:0'],
            'vat' => ['nullable', 'numeric', 'min:0'],
            'discount' => ['nullable', 'numeric', 'min:0'],
            'amount_total' => ['nullable', 'numeric', 'min:0'],
            'amount_paid' => ['nullable', 'numeric', 'min:0'],

            // Order items
            'items' => ['nullable', 'array'],
            'items.*.product_id' => ['required_with:items.*', 'tenant_exists:products,id'],
            'items.*.unit_id' => ['nullable', 'tenant_exists:units,id'],
            'items.*.supplier_id' => ['nullable', 'tenant_exists:suppliers,id'],
            'items.*.location' => ['nullable'],
            'items.*.quantity' => ['required_with:items.*', 'numeric', 'min:0.01'],
            'items.*.buying_price' => ['nullable', 'numeric', 'min:0'],
            'items.*.discount' => ['nullable', 'numeric', 'min:0'],
            'items.*.expires_at' => ['nullable', 'date'],
        ];
    }
}
