@php $editing = isset($branch) @endphp

<div class="row">
    <!-- Basic Information Card -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-header-title">
                    <i class="bi bi-info-circle me-2"></i>Basic Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-12 col-lg-6 mb-4">
                        <x-inputs.text
                            name="name"
                            label="Branch Name"
                            :value="old('name', ($editing ? $branch->name : ''))"
                            maxlength="255"
                            placeholder="Enter branch name"
                            required
                        ></x-inputs.text>
                    </div>
                    
                    <div class="col-sm-12 col-lg-6 mb-4">
                        <x-inputs.select name="status_id" label="Status">
                            @php $selected = old('status_id', ($editing ? $branch->status_id : '')) @endphp
                            <option disabled {{ empty($selected) ? 'selected' : '' }}>Please select the Status</option>
                            @foreach($statuses as $value => $label)
                            <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }} >{{ $label }}</option>
                            @endforeach
                        </x-inputs.select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Contact Information Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-header-title">
                    <i class="bi bi-telephone me-2"></i>Contact Information
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <x-inputs.text
                        name="phone"
                        label="Phone Number"
                        :value="old('phone', ($editing ? $branch->phone : ''))"
                        maxlength="255"
                        placeholder="Enter phone number"
                    >
                        <div slot="prepend">
                            <span class="input-group-text">
                                <i class="bi bi-telephone"></i>
                            </span>
                        </div>
                    </x-inputs.text>
                </div>
                
                <div>
                    <x-inputs.email
                        name="email"
                        label="Email Address"
                        :value="old('email', ($editing ? $branch->email : ''))"
                        maxlength="255"
                        placeholder="Enter email address"
                    >
                        <div slot="prepend">
                            <span class="input-group-text">
                                <i class="bi bi-envelope"></i>
                            </span>
                        </div>
                    </x-inputs.email>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Location Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-header-title">
                    <i class="bi bi-geo-alt me-2"></i>Location
                </h5>
            </div>
            <div class="card-body">
                <div>
                    <label for="address" class="form-label">Address</label>
                    <textarea 
                        name="address" 
                        id="address" 
                        class="form-control" 
                        rows="5" 
                        placeholder="Enter branch address"
                    >{{ old('address', ($editing ? $branch->address : '')) }}</textarea>
                    <small class="form-text text-muted">Include street address, city, state/province, and postal code</small>
                </div>
            </div>
        </div>
    </div>
</div>
