<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipments', function (Blueprint $table) {
            $table->id();
            $table->date('date_from')->nullable();
            $table->date('date_to')->nullable();
            $table->decimal('vat', 20, 2)->default(0);
            $table->text('description')->nullable();
            $table->decimal('sub_total', 20, 2)->default(0);
            $table->decimal('amount_total', 20, 2)->default(0);
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->string('customer_name')->nullable();
            $table->decimal('amount_paid', 20, 2)->default(0);
            $table->unsignedBigInteger('branch_id')->nullable();
            $table->decimal('discount', 20, 2)->default(0);
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->unsignedBigInteger('status_id')->default(10);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unsignedBigInteger('business_type_id')->nullable();
            $table->datetime('expires_at')->nullable();
            $table->unsignedBigInteger('currency_id')->nullable();
            $table->string('reference_no')->nullable();
            
            // Shipment-specific fields
            $table->string('tracking_number')->nullable();
            $table->string('carrier')->nullable();
            $table->string('shipping_method')->nullable();
            $table->text('shipping_address')->nullable();
            $table->date('estimated_delivery')->nullable();
            $table->date('actual_delivery')->nullable();
            $table->decimal('weight', 10, 2)->nullable();
            $table->string('dimensions')->nullable();
            $table->decimal('insurance_value', 20, 2)->nullable();
            $table->text('special_instructions')->nullable();
            
            $table->timestamps();
            $table->softDeletes();

            // Add indexes
            $table->index(['customer_id']);
            $table->index(['status_id']);
            $table->index(['created_by']);
            $table->index(['branch_id']);
            $table->index(['business_type_id']);
            $table->index(['tracking_number']);
            $table->index(['carrier']);
            $table->index(['estimated_delivery']);
            $table->index(['actual_delivery']);

            // Add foreign key constraints
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('set null');
            $table->foreign('status_id')->references('id')->on('statuses')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('branch_id')->references('id')->on('branches')->onDelete('set null');
            $table->foreign('business_type_id')->references('id')->on('business_types')->onDelete('set null');
            $table->foreign('currency_id')->references('id')->on('currencies')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipments');
    }
};
