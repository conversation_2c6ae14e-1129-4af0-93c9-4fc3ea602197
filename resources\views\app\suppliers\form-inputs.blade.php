@php $editing = isset($supplier) @endphp

<!-- Form -->
<div class="row mb-4">
    <div class="col-sm-6">
        <!-- Form -->
        <div class="mb-4">
            <label for="name" class="form-label">Supplier Name <i class="bi-asterisk text-danger small"></i></label>
            <input type="text" class="form-control form-control-lg @error('name') is-invalid @enderror"
                name="name" id="name" placeholder="Enter supplier name"
                value="{{ old('name', ($editing ? $supplier->name : '')) }}"
                required maxlength="255">
            @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <!-- End Form -->
    </div>

    <div class="col-sm-6">
        <!-- Form -->
        <div class="mb-4">
            <label for="status_id" class="form-label">Status <i class="bi-asterisk text-danger small"></i></label>
            <div class="tom-select-custom">
                <select class="js-select form-select @error('status_id') is-invalid @enderror"
                    name="status_id" id="status_id" required
                    data-hs-tom-select-options='{
                        "placeholder": "Select status"
                    }'>
                    @php $selected = old('status_id', ($editing ? $supplier->status_id : '')) @endphp
                    <option value="" disabled {{ empty($selected) ? 'selected' : '' }}>Choose supplier status</option>
                    @foreach($statuses as $value => $label)
                    <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }}>{{ $label }}</option>
                    @endforeach
                </select>
            </div>
            @error('status_id')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <!-- End Form -->
    </div>
</div>
<!-- End Form -->

<!-- Form -->
<div class="row mb-4">
    <div class="col-sm-6">
        <!-- Form -->
        <div class="mb-4">
            <label for="email" class="form-label">Email</label>
            <input type="email" class="form-control @error('email') is-invalid @enderror"
                name="email" id="email" placeholder="Enter email address"
                value="{{ old('email', ($editing ? $supplier->email : '')) }}"
                maxlength="255">
            @error('email')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <!-- End Form -->
    </div>

    <div class="col-sm-6">
        <!-- Form -->
        <div class="mb-4">
            <label for="phone" class="form-label">Phone</label>
            <input type="text" class="form-control @error('phone') is-invalid @enderror"
                name="phone" id="phone" placeholder="Enter phone number"
                value="{{ old('phone', ($editing ? $supplier->phone : '')) }}"
                maxlength="255">
            @error('phone')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <!-- End Form -->
    </div>
</div>
<!-- End Form -->

<!-- Form -->
<div class="mb-4">
    <label for="address" class="form-label">Address</label>
    <textarea class="form-control @error('address') is-invalid @enderror"
        name="address" id="address" rows="3"
        placeholder="Enter supplier address">{{ old('address', ($editing ? $supplier->address : '')) }}</textarea>
    @error('address')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
</div>
<!-- End Form -->
