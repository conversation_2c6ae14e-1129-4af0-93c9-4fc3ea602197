<?php

namespace App\Http\Controllers;

use App\Models\Shipment;
use App\Models\Customer;
use App\Models\Product;
use Illuminate\Http\Request;
use App\Http\Requests\ShipmentStoreRequest;
use App\Http\Requests\ShipmentUpdateRequest;
use App\Libraries\ProductHandler;
use App\Libraries\InvoiceHandler;

class ShipmentController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Shipment::class);

        $search = $request->get('search', '');
        $status = $request->get('status', '');
        $customer = $request->get('customer', '');
        $dateFrom = $request->get('date_from', '');
        $dateTo = $request->get('date_to', '');

        $shipments = Shipment::with(['customer', 'createdBy', 'status'])
            ->when($search, function ($query, $search) {
                return $query->where(function ($q) use ($search) {
                    $q->where('customer_name', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhere('reference_no', 'like', "%{$search}%")
                      ->orWhere('tracking_number', 'like', "%{$search}%")
                      ->orWhereHas('customer', function ($customerQuery) use ($search) {
                          $customerQuery->where('name', 'like', "%{$search}%");
                      });
                });
            })
            ->when($status, function ($query, $status) {
                return $query->where('status_id', $status);
            })
            ->when($customer, function ($query, $customer) {
                return $query->where('customer_id', $customer);
            })
            ->when($dateFrom, function ($query, $dateFrom) {
                return $query->whereDate('created_at', '>=', $dateFrom);
            })
            ->when($dateTo, function ($query, $dateTo) {
                return $query->whereDate('created_at', '<=', $dateTo);
            })
            ->latest()
            ->paginate(15)
            ->withQueryString();

        $customers = Customer::orderBy('name')->pluck('name', 'id');
        $statuses = [
            '10' => 'Draft',
            '11' => 'Pending',
            '12' => 'In Transit',
            '13' => 'Delivered',
            '14' => 'Cancelled',
            '15' => 'Returned'
        ];

        return view('app.shipments.index', compact(
            'shipments',
            'search',
            'status',
            'customer',
            'dateFrom',
            'dateTo',
            'customers',
            'statuses'
        ));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Shipment::class);

        $products = Product::with('units')->get();

        return view('app.shipments.create', compact('products'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', Shipment::class);

        $validated = $request->validate([
            'customer_id' => 'required|tenant_exists:customers,id',
            'sub_total' => 'required|numeric|min:0',
            'amount_total' => 'required|numeric|min:0',
            'amount_paid' => 'nullable|numeric|min:0',
            'vat' => 'nullable|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
        ]);

        if (!auth()->user()->branch_id) {
            return redirect()->back()->with("error", "Please Select Branch first");
        }

        $data = json_decode($request->shipment, true) ?? [];

        if ($request->customer_id) {
            $customer = \App\Models\Customer::find($request->customer_id);
            $customer = $customer ?: \App\Models\Customer::create(["name" => $request->customer_id]);
            $data["customer_id"] = $customer->id ?? '';
            $data["customer_name"] = $customer->name ?? '';
        }

        $data['created_by'] = auth()->id();
        $data['branch_id'] = auth()->user()->branch_id;
        $data['business_type_id'] = auth()->user()->business_type_id;
        $data['status_id'] = 10; // Draft status

        $shipment = Shipment::create($data);

        // Handle shipment items (sales)
        $sales = $data['sales'] ?? [];
        foreach ($sales as $saleData) {
            if (isset($saleData['product_id']) && $saleData['product_id']) {
                $unit = \App\Models\Unit::find($saleData['unit_id'] ?? null);
                $saleParams = [
                    'product_id' => $saleData['product_id'],
                    'product_name' => $saleData['product']['name'] ?? '',
                    'unit_id' => $saleData['unit_id'],
                    'unit_name' => $unit->name ?? '',
                    'unit_quantity' => $unit->quantity ?? 1,
                    'quantity' => $saleData['quantity'],
                    'selling_price' => $saleData['selling_price'],
                    'buying_price' => $saleData['buying_price'],
                    'discount' => $saleData['discount'] ?? 0,
                    'vat' => $saleData['vat'] ?? 0,
                    'created_by' => auth()->id(),
                    'branch_id' => auth()->user()->branch_id,
                ];
                $shipment->sales()->create($saleParams);
            }
        }

        return redirect()
            ->route('shipments.show', $shipment)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Shipment $shipment
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Shipment $shipment)
    {
        $this->authorize('view', $shipment);

        $shipment->load(['customer', 'createdBy', 'approvedBy', 'sales.product', 'status']);

        return view('app.shipments.show', compact('shipment'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Shipment $shipment
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Shipment $shipment)
    {
        $this->authorize('update', $shipment);

        $products = Product::with('units')->get();
        $shipment->load('sales.product.units');

        return view('app.shipments.edit', compact('shipment', 'products'));
    }

    /**
     * @param \App\Http\Requests\ShipmentUpdateRequest $request
     * @param \App\Models\Shipment $shipment
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Shipment $shipment)
    {
        $this->authorize('update', $shipment);

        $validated = $request->validate([
            'customer_id' => 'required|tenant_exists:customers,id',
            'sub_total' => 'required|numeric|min:0',
            'amount_total' => 'required|numeric|min:0',
            'amount_paid' => 'nullable|numeric|min:0',
            'vat' => 'nullable|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
        ]);

        if (!auth()->user()->branch_id) {
            return redirect()->back()->with("error", "Please Select Branch first");
        }

        $data = json_decode($request->shipment, true) ?? [];
        unset($data['created_by']);

        if ($request->customer_id) {
            $customer = \App\Models\Customer::find($request->customer_id);
            $customer = $customer ?: \App\Models\Customer::create(["name" => $request->customer_id]);
            $data["customer_id"] = $customer->id ?? '';
            $data["customer_name"] = $customer->name ?? '';
        }

        $data['updated_by'] = auth()->id();
        $shipment->update($data);

        // Handle shipment items update
        $sales = $data['sales'] ?? [];

        if (count($sales)) {
            $shipment->update(["approved_by" => null]);
        }

        foreach ($sales as $saleParams) {
            if (isset($saleParams['product_id']) && $saleParams['product_id']) {
                $unit = \App\Models\Unit::find($saleParams['unit_id'] ?? null);
                $sale = \App\Models\Sale::where("id", $saleParams['id'] ?? null)->first();

                if ($unit) {
                    $saleParams['unit_name'] = $unit->name;
                    $saleParams['unit_quantity'] = $unit->quantity;
                }

                if ($sale) {
                    $sale->update($saleParams);
                } else {
                    $shipment->sales()->create($saleParams);
                }
            }
        }

        return redirect()
            ->route('shipments.show', $shipment)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Shipment $shipment
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Shipment $shipment)
    {
        $this->authorize('delete', $shipment);

        $shipment->delete();

        return redirect()
            ->route('shipments.index')
            ->withSuccess(__('crud.common.removed'));
    }

    /**
     * Approve shipment
     */
    public function approve(Request $request, Shipment $shipment)
    {
        $this->authorize('update', $shipment);

        if ($request->status_id == 14) { // Cancelled
            $shipment->update(['status_id' => $request->status_id]);
        } else {
            $shipment->update([
                'status_id' => $request->status_id,
                'approved_by' => auth()->id()
            ]);
        }

        return redirect()->back()->withSuccess(__('crud.common.approved'));
    }

    /**
     * Duplicate shipment
     */
    public function duplicate(Request $request, Shipment $shipment)
    {
        $this->authorize('create', Shipment::class);

        try {
            $newShipmentData = $shipment->toArray();
            unset($newShipmentData['id']);
            unset($newShipmentData['created_at']);
            unset($newShipmentData['updated_at']);
            unset($newShipmentData['deleted_at']);

            $newShipmentData['created_by'] = auth()->id();
            $newShipmentData['status_id'] = 10; // Draft status
            $newShipmentData['amount_paid'] = 0;
            $newShipmentData['approved_by'] = null;
            $newShipmentData['tracking_number'] = null;
            $newShipmentData['actual_delivery'] = null;

            $newShipment = Shipment::create($newShipmentData);

            // Copy sales items
            foreach ($shipment->sales as $sale) {
                $newSaleData = $sale->toArray();
                unset($newSaleData['id']);
                unset($newSaleData['created_at']);
                unset($newSaleData['updated_at']);
                unset($newSaleData['deleted_at']);

                $newSaleData['created_by'] = auth()->id();
                $newShipment->sales()->create($newSaleData);
            }

            return redirect()
                ->route('shipments.edit', $newShipment)
                ->withSuccess('Shipment duplicated successfully.');

        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withError('Error duplicating shipment: ' . $e->getMessage());
        }
    }

    /**
     * Convert shipment to invoice
     */
    public function convertToInvoice(Request $request, Shipment $shipment)
    {
        $this->authorize('update', $shipment);

        try {
            // Create invoice from shipment
            $invoiceData = [
                'customer_id' => $shipment->customer_id,
                'customer_name' => $shipment->customer_name,
                'sub_total' => $shipment->sub_total,
                'amount_total' => $shipment->amount_total,
                'amount_paid' => 0, // Reset payment for invoice
                'vat' => $shipment->vat,
                'discount' => $shipment->discount,
                'description' => $shipment->description,
                'reference_no' => 'SHIP-' . $shipment->shipment_id,
                'status_id' => 11, // Pending status for invoice (like quotation conversion)
                'created_by' => auth()->id(),
                'branch_id' => auth()->user()->branch_id,
                'business_type_id' => $shipment->business_type_id,
                'currency_id' => $shipment->currency_id,
                'date_from' => $shipment->date_from,
                'date_to' => $shipment->date_to,
            ];

            $invoice = \App\Models\Invoice::create($invoiceData);

            // Copy sales items with proper structure
            foreach ($shipment->sales as $sale) {
                $invoice->sales()->create([
                    'product_id' => $sale->product_id,
                    'product_name' => $sale->product_name,
                    'unit_id' => $sale->unit_id,
                    'unit_name' => $sale->unit_name,
                    'unit_quantity' => $sale->unit_quantity ?? 1,
                    'quantity' => $sale->quantity,
                    'selling_price' => $sale->selling_price,
                    'buying_price' => $sale->buying_price,
                    'discount' => $sale->discount ?? 0,
                    'vat' => $sale->vat ?? 0,
                    'created_by' => auth()->id(),
                    'branch_id' => auth()->user()->branch_id,
                ]);
            }

            // Add payment record if there's an amount paid
            if ($invoice->amount_paid > 0) {
                \App\Libraries\InvoiceHandler::addPayment($invoice, [
                    "amount" => $invoice->amount_paid,
                    "balance" => $invoice->amount_total - $invoice->amount_paid
                ]);
            }

            // Update shipment status to delivered/invoiced (status 13) and link to invoice
            $shipment->update([
                'status_id' => 13, // Delivered status
                'approved_by' => auth()->id(),
                'invoice_id' => $invoice->id
            ]);

            return redirect()
                ->route('invoices.show', $invoice)
                ->withSuccess('Shipment converted to invoice successfully.');

        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withError('Error converting shipment: ' . $e->getMessage());
        }
    }

    /**
     * Get shipment data for AJAX requests
     */
    public function ajaxShipment(Shipment $shipment)
    {
        $this->authorize('view', $shipment);

        $shipment->load(['customer', 'createdBy', 'sales.product']);

        return response()->json($shipment);
    }
}
