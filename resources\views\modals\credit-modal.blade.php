<form action="/add-credit" method="POST">
	@csrf
	<x-lg-modal>
		<x-slot name="class">credit-modal</x-slot>
		<x-slot name="title">ADD CREDIT</x-slot>


	    <div class="" id="add-credit-el">

	      <div class="justify-content-md-between mb-4">

					<div class="mb-2">
						<label>Customer:</label>
				    <select name="customer_id" v-model="customer_id" class="form-control  js-select form-select" @change="getCustomerBalance" required data-hs-tom-select-options='{
                    "placeholder": "Select a customer..."
                  }'  required>
				        <option value="" disabled selected>Please select the customer</option>
				        @foreach( App\Models\Customer::pluck('name', 'id') as $value => $label)
				        <option value="{{ $value }}">{{ $label }}</option>
				        @endforeach
				    </select>			
					</div>					

					<div class="mb-2">
						<label>Amount Paid:</label>
				    <input type="number" value="" step="0.01" class="form-control" :max="customer ? customer.balance : 0" name="amount_paid" required>	
					</div>

					<div class="mb-2">
						<label>Note:</label>
						<textarea name="comment" class="form-control" placeholder="Add comment." required></textarea>
					</div>


          <!-- List Group -->
          <ul class="list-group mt-4" v-if="customer">
            <li class="list-group-item d-flex align-items-center bg-light">
              <!-- <i class="bi-house list-group-icon"></i>  -->
              <span v-text="customer.name"></span>
              <h2 class="badge bg-primary rounded-pill ms-auto" style="font-size:20px" >
              	Balance: 
              	<span v-text="customer.balance"></span>
              </h2>
            </li>
          </ul>
          <!-- End List Group -->

	      </div>
	      <!-- End Row -->
	     </div>

		<x-slot name="footer">
			<button type="submit" class="btn btn-primary"> Save </button>
		</x-slot>
	</x-lg-modal>


</form>




@push('scripts')

  <script type="text/javascript">
    
    new Vue({
      el: "#add-credit-el",
      data(){
        return{
          customer_id: null,
          customer: null,
        }
      },

      methods: {

        getCustomerBalance() {
        	axios.get('/ajax-customer-balance/' + this.customer_id ).then( res => {
        		this.customer = res.data;
            console.log(this.customer)
        	});
        }


      },


      created(){

      	// this.getProducts();
      }

    })

  </script>

@endpush
