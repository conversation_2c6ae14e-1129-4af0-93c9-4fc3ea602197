@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">pencil-square</x-slot>
        <x-slot name="title">Edit Role: {{ _formatText($role->name) }}</x-slot>
        <x-slot name="subtitle">Modify role details and permissions</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('roles.index') }}">Roles</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('roles.index') }}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-left me-1"></i> Back to Roles
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Role Edit Form -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="card-header-title">
                        <i class="bi bi-pencil-square me-2"></i>Edit Role: {{ _formatText($role->name) }}
                    </h5>
                    <p class="text-muted mb-0">Modify role permissions and settings</p>
                </div>
                <div class="col-auto">
                    <div class="d-flex gap-2">
                        <div class="d-flex align-items-center me-3">
                            <i class="bi bi-people me-2 text-muted"></i>
                            <span class="badge bg-soft-primary">{{ $role->users->count() }} users</span>
                        </div>
                        <div class="d-flex align-items-center me-3">
                            <i class="bi bi-shield-check me-2 text-muted"></i>
                            <span class="badge bg-soft-success">{{ $role->permissions->count() }} permissions</span>
                        </div>
                        <a href="{{ route('roles.show', $role) }}" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-eye me-1"></i> View Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            @if($role->name == 'super-admin')
            <div class="alert alert-warning">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                    <div class="flex-grow-1 ms-2">
                        <h6>System Role</h6>
                        <p class="mb-0">This is a system role with full access. Some settings cannot be modified.</p>
                    </div>
                </div>
            </div>
            @endif

            <x-form method="PUT" action="{{ route('roles.update', $role) }}">
                @include('app.roles.form-inputs')

                <div class="d-flex justify-content-between mt-4 pt-4 border-top">
                    <div>
                        <a href="{{ route('roles.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i> Back to Roles
                        </a>

                        <a href="{{ route('roles.create') }}" class="btn btn-outline-primary ms-2">
                            <i class="bi bi-plus-circle me-1"></i> Create New Role
                        </a>
                    </div>

                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-1"></i> Save Changes
                    </button>
                </div>
            </x-form>
        </div>
    </div>

    <!-- Role Statistics -->
    <div class="card mt-4">
        <div class="card-body">
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center">
                        <i class="bi bi-calendar-check text-info me-2" style="font-size: 1.5rem;"></i>
                        <div>
                            <h6 class="mb-0">Created</h6>
                            <small class="text-muted">{{ $role->created_at->format('M d, Y') }}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center">
                        <i class="bi bi-clock text-warning me-2" style="font-size: 1.5rem;"></i>
                        <div>
                            <h6 class="mb-0">Last Updated</h6>
                            <small class="text-muted">{{ $role->updated_at->diffForHumans() }}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center">
                        <i class="bi bi-people text-primary me-2" style="font-size: 1.5rem;"></i>
                        <div>
                            <h6 class="mb-0">Active Users</h6>
                            <small class="text-muted">{{ $role->users->count() }} assigned</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center">
                        <i class="bi bi-shield-check text-success me-2" style="font-size: 1.5rem;"></i>
                        <div>
                            <h6 class="mb-0">Permissions</h6>
                            <small class="text-muted">{{ $role->permissions->count() }} granted</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
