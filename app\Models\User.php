<?php

namespace App\Models;

use <PERSON><PERSON>\Sanctum\HasApiTokens;
use App\Models\Scopes\Searchable;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\ActiveConfigTrait;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;
use App\Models\UserTenant;
use App\Models\UserInvitation;

class User extends Authenticatable implements HasMedia
{
    use HasRoles;
    use Notifiable;
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use HasApiTokens;
    use CreatedUpdatedTrait;
    use ActiveConfigTrait;
    use InteractsWithMedia;
    use UsesTenantConnection;


    protected $fillable = [
        'name',
        'phone',
        'email',
        'password',
        'status_id',
        'created_by',
        'updated_by',
        'branch_id',
        'warehouse_id',
        'business_type_id',
        'avatar',
        'address',
        'bio',
    ];

    protected $searchableFields = ['*'];

    protected $hidden = ['password', 'remember_token'];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];
    protected $appends = ['image'];

    public function getImageAttribute(){
        $file = $this->file();
        return $file ? $file->getUrl() : '/assets/img/160x160/img1.jpg';
    }

    public function file($collection = "image"){
        return $this->getMedia($collection)->first();
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function employee()
    {
        return $this->hasOne(Employee::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function payments()
    {
        return $this->morphMany(Payment::class, 'paymentable');
    }

    public function businessType()
    {
        return $this->belongsTo(BusinessType::class);
    }

    public function activeConfig(){
        return $this->hasMany(ActiveConfig::class, 'created_by');
    }

    public function leaveRequests(){
        return $this->hasMany(LeaveRequest::class, 'created_by');
    }

    public function businessTypes()
    {
        return $this->belongsToMany(BusinessType::class);
    }

    public function isSuperAdmin()
    {
        return $this->hasRole('super-admin');
    }

    /**
     * Get user's tenant relationships
     */
    public function userTenants()
    {
        return UserTenant::with("tenant")->where('user_email', $this->email)
                        ->where('is_active', true)
                        ->orderBy('is_primary', 'desc')
                        ->orderBy('joined_at', 'asc')
                        ->get();
    }

    /**
     * Get user's available tenants
     */
    public function getAvailableTenants()
    {
        return UserTenant::getUserTenants($this->email);
    }

    /**
     * Get user's primary tenant
     */
    public function getPrimaryTenant()
    {
        return UserTenant::getPrimaryTenant($this->email);
    }

    /**
     * Check if user has access to specific tenant
     */
    public function hasAccessToTenant($tenantId)
    {
        return UserTenant::hasAccess($this->email, $tenantId);
    }

    /**
     * Switch to different tenant
     */
    public function switchToTenant($tenantId)
    {
        if (!$this->hasAccessToTenant($tenantId)) {
            throw new \Exception('User does not have access to this tenant.');
        }

        // Update session with new tenant
        session(['current_tenant' => $tenantId]);

        return true;
    }

    /**
     * Get current tenant from session or primary tenant
     */
    public function getCurrentTenant()
    {
        $currentTenant = session('current_tenant');

        if ($currentTenant && $this->hasAccessToTenant($currentTenant)) {
            return $currentTenant;
        }

        // Fall back to primary tenant
        $primaryTenant = $this->getPrimaryTenant();
        if ($primaryTenant) {
            session(['current_tenant' => $primaryTenant->tenant_id]);
            return $primaryTenant->tenant_id;
        }

        return null;
    }

    /**
     * Set primary tenant
     */
    public function setPrimaryTenant($tenantId)
    {
        if (!$this->hasAccessToTenant($tenantId)) {
            throw new \Exception('User does not have access to this tenant.');
        }

        UserTenant::setPrimaryTenant($this->email, $tenantId);

        return true;
    }

    /**
     * Get invitations sent by this user
     */
    public function sentInvitations()
    {
        return $this->hasMany(UserInvitation::class, 'invited_by');
    }

    /**
     * Get invitations accepted by this user
     */
    public function acceptedInvitations()
    {
        return $this->hasMany(UserInvitation::class, 'accepted_by');
    }

    /**
     * Check if user can invite others to current tenant
     */
    public function canInviteUsers()
    {
        return $this->isSuperAdmin() || $this->hasRole(['admin', 'manager']);
    }
}
