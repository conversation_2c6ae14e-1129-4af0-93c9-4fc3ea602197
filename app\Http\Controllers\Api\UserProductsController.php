<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\ProductResource;
use App\Http\Resources\ProductCollection;

class UserProductsController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, User $user)
    {
        $this->authorize('view', $user);

        $search = $request->get('search', '');

        $products = $user
            ->products2()
            ->search($search)
            ->latest()
            ->paginate();

        return new ProductCollection($products);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, User $user)
    {
        $this->authorize('create', Product::class);

        $validated = $request->validate([
            'name' => ['required', 'max:255', 'string'],
            'price' => ['nullable', 'numeric'],
            'unit_id' => ['nullable', 'exists:units,id'],
            'status_id' => ['nullable', 'exists:statuses,id'],
            'discount' => ['nullable', 'numeric'],
            'category_id' => ['nullable', 'exists:categories,id'],
            'sell_type' => ['nullable', 'in:{IMPLODED_OPTIONS}'],
        ]);

        $product = $user->products2()->create($validated);

        return new ProductResource($product);
    }
}
