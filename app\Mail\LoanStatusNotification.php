<?php

namespace App\Mail;

use App\Models\EmployeeLoan;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Barryvdh\DomPDF\Facade\Pdf;

class LoanStatusNotification extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $loan;
    public $status;
    public $pdfPath;

    /**
     * Create a new message instance.
     *
     * @param \App\Models\EmployeeLoan $loan
     * @param string $status
     * @return void
     */
    public function __construct(EmployeeLoan $loan, $status)
    {
        $this->loan = $loan;
        $this->status = $status;
        
        if (in_array($status, ['approved', 'completed'])) {
            $this->generateLoanPDF();
        }
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $subject = $this->getSubject();
        
        $mail = $this->subject($subject)
                     ->view('emails.loan-status-notification')
                     ->with([
                         'loan' => $this->loan,
                         'employee' => $this->loan->employee,
                         'status' => $this->status,
                         'statusMessage' => $this->getStatusMessage(),
                     ]);

        // Attach PDF for approved or completed loans
        if ($this->pdfPath && file_exists($this->pdfPath)) {
            $mail->attach($this->pdfPath, [
                'as' => 'loan_' . $this->loan->loan_reference . '_' . $this->status . '.pdf',
                'mime' => 'application/pdf',
            ]);
        }

        return $mail;
    }

    /**
     * Get email subject based on status
     *
     * @return string
     */
    private function getSubject()
    {
        switch ($this->status) {
            case 'approved':
                return 'Loan Application Approved - ' . $this->loan->loan_reference;
            case 'rejected':
            case 'cancelled':
                return 'Loan Application ' . ucfirst($this->status) . ' - ' . $this->loan->loan_reference;
            case 'completed':
                return 'Loan Fully Paid - ' . $this->loan->loan_reference;
            default:
                return 'Loan Status Update - ' . $this->loan->loan_reference;
        }
    }

    /**
     * Get status message for email
     *
     * @return string
     */
    private function getStatusMessage()
    {
        switch ($this->status) {
            case 'approved':
                return 'Your loan application has been approved and is now active. Monthly deductions will begin from your next payroll.';
            case 'rejected':
                return 'Unfortunately, your loan application has been rejected. Please contact HR for more information.';
            case 'cancelled':
                return 'Your loan application has been cancelled.';
            case 'completed':
                return 'Congratulations! Your loan has been fully paid. No further deductions will be made.';
            default:
                return 'Your loan status has been updated.';
        }
    }

    /**
     * Generate PDF for loan document
     *
     * @return void
     */
    private function generateLoanPDF()
    {
        $this->loan->load('employee', 'approvedBy', 'createdBy');
        
        $pdf = PDF::loadView('emails.loan-document-pdf', ['loan' => $this->loan]);
        
        // Create storage directory if it doesn't exist
        $directory = storage_path('app/temp/loans');
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }
        
        $filename = 'loan_' . $this->loan->id . '_' . $this->status . '_' . time() . '.pdf';
        $this->pdfPath = $directory . '/' . $filename;
        
        $pdf->save($this->pdfPath);
    }

    /**
     * Clean up the temporary PDF file after sending
     *
     * @return void
     */
    public function __destruct()
    {
        if ($this->pdfPath && file_exists($this->pdfPath)) {
            unlink($this->pdfPath);
        }
    }
}
