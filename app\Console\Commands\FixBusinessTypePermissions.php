<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class FixBusinessTypePermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:businesstype-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix missing business type permissions';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Fixing business type permissions...');

        try {
            // Check if permissions table exists
            if (!\Schema::hasTable('permissions')) {
                $this->error('Permissions table does not exist. Please run migrations first.');
                return 1;
            }

            $permissions = [
                'list businesstypes',
                'view businesstypes', 
                'create businesstypes',
                'update businesstypes',
                'delete businesstypes'
            ];

            $this->info('Creating business type permissions...');
            foreach ($permissions as $permission) {
                $perm = Permission::firstOrCreate(['name' => $permission]);
                $this->line("✓ Permission '{$permission}' " . ($perm->wasRecentlyCreated ? 'created' : 'already exists'));
            }

            // Assign permissions to super-admin role if it exists
            $superAdminRole = Role::where('name', 'super-admin')->first();
            if ($superAdminRole) {
                $superAdminRole->givePermissionTo($permissions);
                $this->info('✓ Assigned permissions to super-admin role');
            }

            // Assign permissions to admin role if it exists
            $adminRole = Role::where('name', 'admin')->first();
            if ($adminRole) {
                $adminRole->givePermissionTo($permissions);
                $this->info('✓ Assigned permissions to admin role');
            }

            // Clear permission cache
            app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
            $this->info('✓ Cleared permission cache');

            $this->info('Business type permissions fixed successfully!');
            return 0;

        } catch (\Exception $e) {
            $this->error('Error fixing permissions: ' . $e->getMessage());
            return 1;
        }
    }
}
