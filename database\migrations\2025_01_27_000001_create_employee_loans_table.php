<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employee_loans', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('employee_id');
            $table->string('loan_reference')->unique();
            $table->decimal('loan_amount', 20, 2);
            $table->decimal('installment_amount', 20, 2);
            $table->decimal('remaining_balance', 20, 2);
            $table->decimal('total_paid', 20, 2)->default(0);
            $table->decimal('total_interest_paid', 20, 2)->default(0);
            $table->integer('total_installments');
            $table->integer('total_interest_due')->default(0);
            $table->integer('installments_paid')->default(0);
            $table->date('loan_date');
            $table->date('first_deduction_date');
            $table->date('expected_completion_date')->nullable();
            $table->date('actual_completion_date')->nullable();
            $table->enum('status', ['pending', 'approved', 'active', 'completed', 'cancelled'])->default('pending');
            $table->text('purpose')->nullable();
            $table->text('terms_conditions')->nullable();
            $table->decimal('interest_rate', 5, 2)->default(0);
            $table->enum('interest_type', ['simple', 'compound'])->default('simple');
            $table->text('approval_notes')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Foreign key constraints
            $table->foreign('employee_id')->references('id')->on('employees')->onDelete('cascade');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['employee_id', 'status']);
            $table->index('loan_reference');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_loans');
    }
};
