@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">Production Orders</x-slot>
        <x-slot name="controls">
            @can('create', App\Models\ProductionOrder::class)
            <a href="{{ route('production-orders.create') }}" class="btn btn-info btn-sm">
                @lang('crud.common.create') Production Order
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="searchbar mt-4 mb-5">
        <form class="d-flex" id="filter">
            <div class="me-3">
                <input
                    type="text"
                    name="search"
                    value="{{ $search ?? '' }}"
                    class="form-control"
                    placeholder="{{ __('crud.common.search') }}"
                    autocomplete="off"
                />
            </div>
            <div class="me-3">
                <select name="product_id" class="form-select">
                    <option value="">All Products</option>
                    @foreach($products as $id => $name)
                    <option value="{{ $id }}" {{ $id == $productId ? 'selected' : '' }}>
                        {{ $name }}
                    </option>
                    @endforeach
                </select>
            </div>
            <div class="me-3">
                <select name="status" class="form-select">
                    <option value="">All Statuses</option>
                    <option value="draft" {{ $status == 'draft' ? 'selected' : '' }}>Draft</option>
                    <option value="planned" {{ $status == 'planned' ? 'selected' : '' }}>Planned</option>
                    <option value="in_progress" {{ $status == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                    <option value="completed" {{ $status == 'completed' ? 'selected' : '' }}>Completed</option>
                    <option value="cancelled" {{ $status == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                </select>
            </div>
            <div class="me-3">
                <select name="branch_id" class="form-select">
                    <option value="">All Branches</option>
                    @foreach($branches as $id => $name)
                    <option value="{{ $id }}" {{ $id == $branchId ? 'selected' : '' }}>
                        {{ $name }}
                    </option>
                    @endforeach
                </select>
            </div>
            <div class="me-3">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-search"></i>
                </button>
            </div>
        </form>
    </div>

    <div class="card card-table">
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            @lang('crud.production_orders.inputs.order_number')
                        </th>
                        <th class="text-left">
                            @lang('crud.production_orders.inputs.product_id')
                        </th>
                        <th class="text-left">
                            @lang('crud.production_orders.inputs.quantity')
                        </th>
                        <th class="text-left">
                            @lang('crud.production_orders.inputs.planned_start_date')
                        </th>
                        <th class="text-left">
                            @lang('crud.production_orders.inputs.planned_end_date')
                        </th>
                        <th class="text-center">
                            Status
                        </th>
                        <th class="text-center">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($productionOrders ?? [] as $productionOrder)
                    <tr>
                        <td>{{ $productionOrder->order_number ?? '-' }}</td>
                        <td>{{ optional($productionOrder->product)->name ?? '-' }}</td>
                        <td>{{ $productionOrder->quantity ?? '-' }} {{ $productionOrder->unit_of_measure ?? '' }}</td>
                        <td>{{ $productionOrder->planned_start_date ? $productionOrder->planned_start_date->format('Y-m-d') : '-' }}</td>
                        <td>{{ $productionOrder->planned_end_date ? $productionOrder->planned_end_date->format('Y-m-d') : '-' }}</td>
                        <td class="text-center">
                            <span class="badge {{ 
                                $productionOrder->status == 'draft' ? 'bg-secondary' : 
                                ($productionOrder->status == 'planned' ? 'bg-info' : 
                                ($productionOrder->status == 'in_progress' ? 'bg-primary' : 
                                ($productionOrder->status == 'completed' ? 'bg-success' : 'bg-danger'))) 
                            }}">
                                {{ ucfirst(str_replace('_', ' ', $productionOrder->status ?? '-')) }}
                            </span>
                        </td>
                        <td class="text-center" style="width: 134px;">
                            <div
                                role="group"
                                aria-label="Row Actions"
                                class="btn-group"
                            >
                                @can('update', $productionOrder)
                                <a
                                    href="{{ route('production-orders.edit', $productionOrder) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        edit
                                    </button>
                                </a>
                                @endcan @can('view', $productionOrder)
                                <a
                                    href="{{ route('production-orders.show', $productionOrder) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        view
                                    </button>
                                </a>
                                @endcan @can('delete', $productionOrder)
                                <form
                                    action="{{ route('production-orders.destroy', $productionOrder) }}"
                                    method="POST"
                                    onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                >
                                    @csrf @method('DELETE')
                                    <button
                                        type="submit"
                                        class="btn btn-light text-danger m-1"
                                    >
                                        del
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center">
                            @lang('crud.common.no_items_found')
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="card-footer d-flex justify-content-center">
            @if(isset($productionOrders))
            {!! $productionOrders->render() !!}
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
  $("document").ready(function () {
    dataTableBtn()
  });
</script>
@endpush
