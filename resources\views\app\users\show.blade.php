@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
          <div class="col-sm mb-2 mb-sm-0">
            <h1 class="page-header-title">
                User
                <a href="{{ route('users.index') }}" class="mr-4 float-right"
                    ><i class="icon ion-md-arrow-back"></i> Back
                </a>
            </h1>
          </div>
          <!-- End Col -->
        </div>
    </div>
    <!-- End Page Header -->
<div class="mt-4">
    <div class="card mb-4">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <x-partials.thumbnail
                    src="{{ $user->image ?? '' }}"
                    size="150"
                    class="rounded-circle mr-3"
                />
                <div>
                    <h4 class="mb-0">{{ $user->name ?? '-' }}</h4>
<p class="text-muted mb-0">Email: {{ $user->email ?? '-' }}</p>
<p class="text-muted mb-0">Phone: {{ $user->phone ?? '-' }}</p>
                </div>
            </div>
        </div>
    </div>
    <div class="mb-4">
<h5><i class="icon ion-md-checkmark-circle-outline"></i> Status</h5>
        <span>{{ optional($user->status)->name ?? '-' }}</span>
    </div>
    <div class="mb-4">
<h5><i class="icon ion-md-business"></i> Branch</h5>
        <span>{{ optional($user->branch)->name ?? '-' }}</span>
    </div>
</div>

    <div class="mt-4">
        <div class="mb-4">
<h5>Roles</h5>
            <div>
                @forelse ($user->roles as $role)
<div class="badge badge-pill badge-info">{{ $role->name }}</div>
                <br />
                @empty - @endforelse
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ route('users.index') }}" class="btn btn-light">
            <!-- <i class="icon ion-md-return-left"></i> -->
Back
        </a>

        @can('create', App\Models\User::class)
        <a href="{{ route('users.create') }}" class="btn btn-light">
            <!-- <i class="icon ion-md-add"></i> -->
Create
        </a>
        @endcan
    </div>
</div>
@endsection
