<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\UserTenant;

class UserInvitationMail extends Mailable
{
    use Queueable, SerializesModels;

    public $invitation;
    public $inviterName;
    public $tenantName;
    public $acceptUrl;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(UserTenant $invitation, $inviterName, $tenantName)
    {
        $this->invitation = $invitation;
        $this->inviterName = $inviterName;
        $this->tenantName = $tenantName;
        $this->acceptUrl = url('/invitation/accept/' . $invitation->invitation_token);
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('You\'re invited to join ' . $this->tenantName)
                    ->view('emails.user-invitation')
                    ->with([
                        'invitation' => $this->invitation,
                        'inviterName' => $this->inviterName,
                        'tenantName' => $this->tenantName,
                        'acceptUrl' => $this->acceptUrl,
                    ]);
    }
}
