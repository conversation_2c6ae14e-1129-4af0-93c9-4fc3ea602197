@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
          <div class="col-sm mb-2 mb-sm-0">
            <h1 class="page-header-title">
                Credit Report
            </h1>
          </div>
          <!-- End Col -->
        </div>
    </div>
    <!-- End Page Header -->

    <div class="searchbar mt-4 mb-5">
        <div class="row">
            <div class="col-md-9 col-sm-9">
                <form class="row" id="filter">

<!--                     <div class=" col-sm-3">
                          <input type="hidden" name="from">
                        <input type="hidden" name="to">
                        <button type="button" id="js-daterangepicker-predefined" type="button" style="width:100%" class="btn btn-white">
                          <i class="bi-calendar-week me-1"></i>
                          <span class="js-daterangepicker-predefined-preview"></span>
                        </button>
                    </div> -->
                        <input type="hidden" name="from" value="{{ request()->from }}">
                        <input type="hidden" name="to" value="{{ request()->to }}">

                    <div class="col-sm-3">
                        <div class="tom-select-custom">
                          <select class="js-select form-select product-filter" name="product_id" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a product..."
                          }'>
                          <option></option>
                          @foreach( App\Models\Product::get() as $product)
                            <option value="{{ $product->id }}" @if($product->id == request()->product_id ) selected @endif>
                              {{ $product->name }} ~ {{ $product->description }}
                            </option>
                          @endforeach
                          </select>
                        </div>
                    </div>

                    <div class="col-sm-3">
                        <div class="tom-select-custom">
                          <select class="js-select form-select branch-filter" name="branch_id" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a branch..."
                          }'>
                          <option></option>
                          @foreach( App\Models\Branch::get() as $branch)
                            <option value="{{ $branch->id }}" @if($branch->id == request()->branch_id ) selected @endif>
                              {{ $branch->name }}
                            </option>
                          @endforeach
                          </select>
                        </div>
                    </div>


                    <div class="col-sm-3">
                        <div class="tom-select-custom">
                          <select class="js-select form-select category-filter" name="category_id" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a category..."
                          }'>
                          <option></option>
                          @foreach( App\Models\Category::get() as $category)
                            <option value="{{ $category->id }}" @if($category->id == request()->category_id ) selected @endif>
                              {{ $category->name }}
                            </option>
                          @endforeach
                          </select>
                        </div>
                    </div>

                    <div class="col-sm-2">
                        <div class="input-group-append">
                            <button type="submit" class="btn btn-primary">
                                Search
                            </button>
                        </div>
                    </div>


                </form>
            </div>
            <div class="col-md-3 text-right col-sm-3">
                @can('create', App\Models\Invoice::class)
                <a href="{{ route('invoices.create') }}" class="btn btn-primary" style="float:right">
                    <!-- <i class="icon ion-md-add"></i> -->
                    @lang('crud.common.create') Customer Credit
                </a>
                @endcan
            </div>
        </div>
    </div>




    <div class="row justify-content-between align-items-center flex-grow-1 mb-2">
        <div class="col-md">
            <h4 class="card-header-title"></h4>
        </div>

        <div class="col-auto">
            <!-- Filter -->
            <form>
              <!-- Search -->
              <div class="input-group input-group-merge input-group-flush">
                <div class="input-group-prepend input-group-text">
                  <i class="bi-search"></i>
                </div>
                <input id="SearchInput" type="search" class="form-control" placeholder="Search users" aria-label="Search users">
              </div>
              <!-- End Search -->
            </form>
            <!-- End Filter -->
          </div>

          <div class="col-auto">


            <!-- Dropdown -->
            <div class="dropdown me-2">
              <button type="button" class="btn btn-white btn-sm dropdown-toggle" id="orderExportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi-download me-2"></i> Export
              </button>

              <div class="dropdown-menu dropdown-menu-sm-end" aria-labelledby="orderExportDropdown">
                <span class="dropdown-header">Options</span>
                <a id="export-copy" class="dropdown-item" href="javascript:;">
                  <img class="avatar avatar-xss avatar-4x3 me-2" src="{{ asset('assets/svg/illustrations/copy-icon.svg') }}" alt="Image Description">
                  Copy
                </a>
                <a id="export-print" class="dropdown-item" href="javascript:;">
                  <img class="avatar avatar-xss avatar-4x3 me-2" src="{{ asset('assets/svg/illustrations/print-icon.svg') }}" alt="Image Description">
                  Print
                </a>
                <div class="dropdown-divider"></div>
                <span class="dropdown-header">Download options</span>
                <a id="export-excel" class="dropdown-item" href="javascript:;">
                  <img class="avatar avatar-xss avatar-4x3 me-2" src="{{ asset('assets/svg/brands/excel-icon.svg') }}" alt="Image Description">
                  Excel
                </a>
                <a id="export-csv" class="dropdown-item" href="javascript:;">
                  <img class="avatar avatar-xss avatar-4x3 me-2" src="{{ asset('assets/svg/components/placeholder-csv-format.svg') }}" alt="Image Description">
                  .CSV
                </a>
                <a id="export-pdf" class="dropdown-item" href="javascript:;">
                  <img class="avatar avatar-xss avatar-4x3 me-2" src="{{ asset('assets/svg/brands/pdf-icon.svg') }}" alt="Image Description">
                  PDF
                </a>
              </div>
            </div>
            <!-- End Dropdown -->
        </div>
    </div>

    <!-- Table -->
    <div class="table-responsive datatable-custom">
        <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table"
               data-hs-datatables-options='{
                "dom": "Bfrtip",
                "search": "#SearchInput",
                "buttons": [
                  {
                    "extend": "copy",
                    "className": "d-none"
                  },
                  {
                    "extend": "excel",
                    "className": "d-none"
                  },
                  {
                    "extend": "csv",
                    "className": "d-none"
                  },
                  {
                    "extend": "pdf",
                    "className": "d-none"
                  },
                  {
                    "extend": "print",
                    "className": "d-none"
                  }
               ],
               "order": []
             }'>

            <thead class="thead-light">
                <tr>
                    <th class="text-left"> # </th>
                    <th class="text-left">
                        @lang('crud.customers.singular')
                    </th>
                    <th class="text-left">
                        Value
                    </th>
                    <th class="text-left">
                        Due Balance
                    </th>
                </tr>
            </thead>
            <tbody>
                @forelse($customers as $key => $customer)
                @php $sale = $customer->getInvoice(); @endphp
                <tr>
                    <td>{{ $key + 1 }}</td>
                    <td>{{ $customer->name  ?? '-' }}</td>
                    <td>{{ _money( _from($sale, 'value') ) ?? '-' }}</td>
                    <td>{{ _money( _from($sale, 'due_balance') ) ?? '-' }}</td>
                </tr>
                @empty
                <tr>
                    <td colspan="5">
                        @lang('crud.common.no_items_found')
                    </td>
                </tr>
                @endforelse
            </tbody>

        </table>

    </div>




</div>
@endsection





@push('scripts')
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
    HSCore.components.HSDatatables.init('#data-table')

    // $('.js-datatable').DataTable();
    const dataTable = HSCore.components.HSDatatables.getItem('data-table');

    document.getElementById('export-copy').addEventListener('click', function () {
      dataTable.button('.buttons-copy').trigger()
    })

    document.getElementById('export-excel').addEventListener('click', function () {
      dataTable.button('.buttons-excel').trigger()
    })

    document.getElementById('export-csv').addEventListener('click', function () {
      dataTable.button('.buttons-csv').trigger()
    })

    document.getElementById('export-pdf').addEventListener('click', function () {
      dataTable.button('.buttons-pdf').trigger()
    })

    document.getElementById('export-print').addEventListener('click', function () {
      dataTable.button('.buttons-print').trigger()
    })



    // HSCore.components.HSDatatables.init('#pendingTable')
    // const pendingTable = HSCore.components.HSDatatables.getItem('pendingTable')

    // document.getElementById('pending-export-copy').addEventListener('click', function () {
    //   pendingTable.button('.buttons-copy').trigger()
    // })

    // document.getElementById('pending-export-excel').addEventListener('click', function () {
    //   pendingTable.button('.buttons-excel').trigger()
    // })

    // document.getElementById('pending-export-csv').addEventListener('click', function () {
    //   pendingTable.button('.buttons-csv').trigger()
    // })

    // document.getElementById('pending-export-pdf').addEventListener('click', function () {
    //   pendingTable.button('.buttons-pdf').trigger()
    // })

    // document.getElementById('pending-export-print').addEventListener('click', function () {
    //   pendingTable.button('.buttons-print').trigger()
    // })


  });
</script>

<script>

    $(document).on('ready', function () {
      // INITIALIZATION OF DATERANGEPICKER

      $(".year").change( function(){
        var params  = @json( request()->all() );
        var search  = "?";
        params.year = this.value;
        for( para in params){
          search += "&" + para + "=" + params[para];
        }

        window.location.href = search;
      })

      // =======================================================
      $('.js-daterangepicker').daterangepicker();

      $('.js-daterangepicker-times').daterangepicker({
        timePicker: true,
        startDate: moment().startOf('hour'),
        endDate: moment().startOf('hour').add(32, 'hour'),
        locale: {
          format: 'M/DD hh:mm A'
        }
      });

      var start = @json( request()->from );
      var end = @json( request()->to );
      start = (start) ? moment(start, "YYYY-MM-DD HH:mm:ss") : moment();
      end = (end) ? moment(end, "YYYY-MM-DD HH:mm:ss") : moment();
      $('#js-daterangepicker-predefined .js-daterangepicker-predefined-preview').html(start.format('MMM D') + ' - ' + end.format('MMM D, YYYY'));

      function cb(start, end) {

        var params  = @json( request()->all() );
        var search  = "?";
        params.from = start.format("YYYY-MM-DD HH:mm:ss");
        params.to   = end.format("YYYY-MM-DD HH:mm:ss");
        $('input[name="from"]').val(params.from);
        $('input[name="to"]').val(params.to);
        for( para in params){
          search += "&" + para + "=" + params[para];
        }


        // window.location.href = search;
        // $('#js-daterangepicker-predefined .js-daterangepicker-predefined-preview').html(start.format('MMM D') + ' - ' + end.format('MMM D, YYYY'));
      }

      $('#js-daterangepicker-predefined').daterangepicker({
        startDate: start,
        endDate: end,
        ranges: {
          'Today': [moment(), moment()],
          'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
          'Last 7 Days': [moment().subtract(6, 'days'), moment()],
          'Last 30 Days': [moment().subtract(29, 'days'), moment()],
          'This Month': [moment().startOf('month'), moment().endOf('month')],
          'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        }
      }, cb);

      // cb(start, end);
    });
</script>

@endpush