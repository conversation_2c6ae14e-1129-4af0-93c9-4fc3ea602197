@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">building-add</x-slot>
        <x-slot name="title">Create New Branch</x-slot>
        <x-slot name="subtitle">Add a new branch location to your business</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('branches.index') }}">Branches</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('branches.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Branches
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Form Card -->
    <div class="card">
        <div class="card-body">
            <x-form
                method="POST"
                action="{{ route('branches.store') }}"
                class="mt-2"
            >
                @include('app.branches.form-inputs')

                <div class="d-flex justify-content-end mt-4 border-top pt-4">
                    <a href="{{ route('branches.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-x-circle me-1"></i> Cancel
                    </a>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-1"></i> Create Branch
                    </button>
                </div>
            </x-form>
        </div>
    </div>
</div>
@endsection
