<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

use Facades\App\Cache\Repo;


class Product extends Model implements HasMedia
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use InteractsWithMedia;
    use BusinessTypeTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'barcode',
        'buying_price',
        'selling_price',
        'unit_id',
        'description',
        'status_id',
        'discount',
        'created_by',
        'updated_by',
        'category_id',
        'sell_type',
        'business_type_id',
        'vat_applied'
    ];

    protected $searchableFields = ['*'];

    protected $appends = ['image', 'total_stock', 'total_sales', 'unit_stock'];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];
    
    public function getTotalStockAttribute(){
        
        $stocks = $this->stocks()->select(\DB::raw("balance * unit_quantity AS quantity") );

        $stocks->whereHas("stockable", function($q){
            $q->whereNotNull("approved_by")->whereIn("status_id", [10]);
        });

        if( ! auth()->user()->isSuperAdmin() ){
            $stocks->where('warehouse_id', auth()->user()->warehouse_id );
        }

        if( request()->warehouse_id ) {
            $stocks->where("warehouse_id", request()->warehouse_id );
        }

        if( request()->warehouse_from ) {
            $stocks->where("warehouse_id", request()->warehouse_from );
        }

        $stocks = $stocks->get();
        return $stocks->sum("quantity") ;
    }    

    public function getUnitStockAttribute(){
        $stocks = $this->stockItems()->whereNull("invoice_id");
        // if( ! auth()->user()->isSuperAdmin() ){
        //     $stocks->where('branch_id', auth()->user()->branch_id );
        // }

        $qty = $this->unit->quantity ?? 1;
        $totalStock = $stocks->count(); 
        $totalStock = ( $totalStock ) ? $totalStock * $qty : 0 ;
        $totalStock = $totalStock > 0 ? $totalStock : 0;
        
        return $totalStock;
    }

    public function getTotalSalesAttribute(){
        $sales = $this->stockItems()->whereNotNull('invoice_id')
                    ->whereBetween('created_at', [ \Facades\App\Cache\Repo::dateFrom(), \Facades\App\Cache\Repo::dateTo() ]);
        $sales->where("product_id", $this->id );
        return $sales->sum('selling_price') - $sales->sum('discount'); 
    }

    public function getSellingPriceAttribute($value){
        return $this->unit->selling_price ?? 0;
    }

    public function getBuyingPriceAttribute($value){
        return $this->unit->buying_price ?? 0;
    }

    public function getImageAttribute(){
        $file = $this->file();
        return $file ? $file->getUrl() : '/img/logo.webp';
    }

    public function file($collection = "image"){
        return $this->getMedia($collection)->first();
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function stocks()
    {
        return $this->hasMany(Stock::class);
    }

    public function units()
    {
        return $this->hasMany(Unit::class);
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    public function stockItems()
    {
        return $this->hasMany(StockItem::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function getStock() {
        request()->merge(["product_id" => $this->id]);
        return Repo::getStock();
    }    

    public function getSale() {
        request()->merge(["product_id" => $this->id]);
        return Repo::getSale();
    }

    public function getOrder() {
        request()->merge(["product_id" => $this->id]);
        return Repo::getOrder();
    }


}
