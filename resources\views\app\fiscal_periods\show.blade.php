@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-calendar3 me-2"></i>{{ $fiscalPeriod->name }}
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group" role="group">
                <a href="{{ route('fiscal-periods.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Periods
                </a>
                @can('update', $fiscalPeriod)
                @if(!$fiscalPeriod->is_closed)
                <a href="{{ route('fiscal-periods.edit', $fiscalPeriod) }}" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-pencil me-1"></i>Edit
                </a>
                @endif
                @endcan
                @can('create', App\Models\FiscalPeriod::class)
                <a href="{{ route('fiscal-periods.create') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>New Period
                </a>
                @endcan
                <button type="button" class="btn btn-outline-info btn-sm" onclick="window.print()">
                    <i class="bi bi-printer me-1"></i>Print
                </button>
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <!-- Fiscal Period Overview -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Period Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Period Name:</strong></td>
                                    <td>{{ $fiscalPeriod->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Start Date:</strong></td>
                                    <td>{{ $fiscalPeriod->start_date->format('M d, Y') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>End Date:</strong></td>
                                    <td>{{ $fiscalPeriod->end_date->format('M d, Y') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Duration:</strong></td>
                                    <td>
                                        @php
                                            $duration = $fiscalPeriod->start_date->diffInDays($fiscalPeriod->end_date) + 1;
                                        @endphp
                                        {{ $duration }} days
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Fiscal Year:</strong></td>
                                    <td>
                                        <a href="{{ route('fiscal-years.show', $fiscalPeriod->fiscalYear) }}" class="text-decoration-none">
                                            {{ $fiscalPeriod->fiscalYear->name }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @if($fiscalPeriod->is_closed)
                                            <span class="badge bg-danger">Closed</span>
                                        @else
                                            <span class="badge bg-success">Open</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Active:</strong></td>
                                    <td>
                                        @if($fiscalPeriod->is_active)
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>Active
                                            </span>
                                        @else
                                            <span class="badge bg-secondary">
                                                <i class="bi bi-x-circle me-1"></i>Inactive
                                            </span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Period Number:</strong></td>
                                    <td>{{ $fiscalPeriod->period_number ?? 'N/A' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($fiscalPeriod->description)
                    <div class="mt-3">
                        <h6>Description:</h6>
                        <p class="text-muted">{{ $fiscalPeriod->description }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Journal Entries -->
            @if(isset($journalEntries) && $journalEntries->count() > 0)
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-journal-text me-2"></i>Journal Entries
                    </h5>
                    <span class="badge bg-secondary">{{ $journalEntries->total() }} entries</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Entry Number</th>
                                    <th>Date</th>
                                    <th>Description</th>
                                    <th class="text-end">Debit</th>
                                    <th class="text-end">Credit</th>
                                    <th class="text-center">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($journalEntries as $entry)
                                <tr>
                                    <td>
                                        <a href="{{ route('journal-entries.show', $entry) }}" class="text-decoration-none">
                                            {{ $entry->entry_number }}
                                        </a>
                                    </td>
                                    <td>{{ $entry->entry_date->format('M d, Y') }}</td>
                                    <td>{{ $entry->description ?: 'No description' }}</td>
                                    <td class="text-end">
                                        <span class="fw-bold text-success">
                                            ${{ number_format($entry->journalEntryLines->sum('debit'), 2) }}
                                        </span>
                                    </td>
                                    <td class="text-end">
                                        <span class="fw-bold text-danger">
                                            ${{ number_format($entry->journalEntryLines->sum('credit'), 2) }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        @switch($entry->status)
                                            @case('draft')
                                                <span class="badge bg-secondary">Draft</span>
                                                @break
                                            @case('posted')
                                                <span class="badge bg-success">Posted</span>
                                                @break
                                            @default
                                                <span class="badge bg-warning">{{ ucfirst($entry->status) }}</span>
                                        @endswitch
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    @if($journalEntries->hasPages())
                    <div class="mt-3">
                        {{ $journalEntries->links() }}
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <!-- Fiscal Period Actions -->
        <div class="col-lg-4">
            <!-- Status Card -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-gear me-2"></i>Period Status
                    </h6>
                </div>
                <div class="card-body text-center">
                    @if($fiscalPeriod->is_closed)
                        <i class="bi bi-lock display-4 text-danger d-block mb-2"></i>
                        <h5 class="text-danger">Closed</h5>
                        <p class="text-muted small">Period is closed. No new transactions allowed.</p>
                        @if($fiscalPeriod->closed_date)
                            <small class="text-muted">
                                Closed on {{ $fiscalPeriod->closed_date->format('M d, Y') }}
                            </small>
                        @endif
                    @else
                        <i class="bi bi-unlock display-4 text-success d-block mb-2"></i>
                        <h5 class="text-success">Open</h5>
                        <p class="text-muted small">Period is open for transactions.</p>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-lightning me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @can('update', $fiscalPeriod)
                        @if(!$fiscalPeriod->is_closed)
                        <a href="{{ route('fiscal-periods.edit', $fiscalPeriod) }}" class="btn btn-primary btn-sm">
                            <i class="bi bi-pencil me-1"></i>Edit Period
                        </a>
                        
                        <form action="{{ route('fiscal-periods.close', $fiscalPeriod) }}" method="POST" 
                              onsubmit="return confirm('Are you sure you want to close this fiscal period? This action cannot be undone.')">
                            @csrf
                            <button type="submit" class="btn btn-warning btn-sm w-100">
                                <i class="bi bi-lock me-1"></i>Close Period
                            </button>
                        </form>
                        @endif
                        @endcan

                        <a href="{{ route('journal-entries.index') }}?fiscal_period_id={{ $fiscalPeriod->id }}" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-journal-text me-1"></i>View Entries
                        </a>

                        <button type="button" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-download me-1"></i>Export Data
                        </button>

                        @can('delete', $fiscalPeriod)
                        @if(!$fiscalPeriod->is_closed)
                        <form method="POST" action="{{ route('fiscal-periods.destroy', $fiscalPeriod) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this fiscal period?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                <i class="bi bi-trash me-1"></i>Delete Period
                            </button>
                        </form>
                        @endif
                        @endcan
                    </div>
                </div>
            </div>

            <!-- Period Statistics -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-bar-chart me-2"></i>Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-1">{{ $journalEntries->total() ?? 0 }}</h4>
                                <small class="text-muted">Entries</small>
                            </div>
                        </div>
                        <div class="col-6">
                            @php
                                $totalAmount = $journalEntries->flatMap->journalEntryLines->sum('debit') ?? 0;
                            @endphp
                            <h4 class="text-success mb-1">${{ number_format($totalAmount, 0) }}</h4>
                            <small class="text-muted">Total Value</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Period Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Period Information
                    </h6>
                </div>
                <div class="card-body">
                    <small class="text-muted d-block mb-2">
                        <strong>Period ID:</strong> {{ $fiscalPeriod->id }}
                    </small>
                    <small class="text-muted d-block mb-2">
                        <strong>Created:</strong> {{ $fiscalPeriod->created_at->format('M d, Y H:i') }}
                        @if($fiscalPeriod->createdBy)
                            by {{ $fiscalPeriod->createdBy->name }}
                        @endif
                    </small>
                    @if($fiscalPeriod->updated_at != $fiscalPeriod->created_at)
                    <small class="text-muted d-block mb-2">
                        <strong>Last Updated:</strong> {{ $fiscalPeriod->updated_at->format('M d, Y H:i') }}
                        @if($fiscalPeriod->updatedBy)
                            by {{ $fiscalPeriod->updatedBy->name }}
                        @endif
                    </small>
                    @endif
                    @if($fiscalPeriod->closed_date)
                    <small class="text-muted d-block">
                        <strong>Closed:</strong> {{ $fiscalPeriod->closed_date->format('M d, Y H:i') }}
                        @if($fiscalPeriod->closedBy)
                            by {{ $fiscalPeriod->closedBy->name }}
                        @endif
                    </small>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@if(session('success'))
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            {{ session('success') }}
        </div>
    </div>
</div>
@endif

@endsection

@push('styles')
<style>
@media print {
    .btn, .card-header .btn-group {
        display: none !important;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
@endpush
