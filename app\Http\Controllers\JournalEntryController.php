<?php

namespace App\Http\Controllers;

use App\Models\Account;
use App\Models\JournalEntry;
use App\Models\JournalEntryLine;
use App\Models\FiscalYear;
use App\Models\FiscalPeriod;
use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class JournalEntryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', JournalEntry::class);

        $search = $request->get('search', '');
        $status = $request->get('status', '');
        $fromDate = $request->get('from_date', '');
        $toDate = $request->get('to_date', '');
        $fiscalYearId = $request->get('fiscal_year_id', '');
        $fiscalPeriodId = $request->get('fiscal_period_id', '');

        $journalEntries = JournalEntry::search($search);
        
        if ($status) {
            $journalEntries->where('status', $status);
        }
        
        if ($fromDate) {
            $journalEntries->where('entry_date', '>=', $fromDate);
        }
        
        if ($toDate) {
            $journalEntries->where('entry_date', '<=', $toDate);
        }
        
        if ($fiscalYearId) {
            $journalEntries->where('fiscal_year_id', $fiscalYearId);
        }
        
        if ($fiscalPeriodId) {
            $journalEntries->where('fiscal_period_id', $fiscalPeriodId);
        }
        
        $journalEntries = $journalEntries->latest()
            ->paginate()
            ->withQueryString();

        $fiscalYears = FiscalYear::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');
            
        $fiscalPeriods = FiscalPeriod::where('is_active', true)
            ->when($fiscalYearId, function ($query) use ($fiscalYearId) {
                return $query->where('fiscal_year_id', $fiscalYearId);
            })
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.journal_entries.index', compact(
            'journalEntries', 
            'search', 
            'status',
            'fromDate',
            'toDate',
            'fiscalYears',
            'fiscalPeriods',
            'fiscalYearId',
            'fiscalPeriodId'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', JournalEntry::class);

        $accounts = Account::where('is_active', true)
            ->where('allows_manual_entries', true)
            ->orderBy('code')
            ->get()
            ->pluck('full_name', 'id');
            
        $fiscalYears = FiscalYear::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');
            
        $fiscalPeriods = FiscalPeriod::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');
            
        $currencies = Currency::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.journal_entries.create', compact(
            'accounts',
            'fiscalYears',
            'fiscalPeriods',
            'currencies'
        ));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', JournalEntry::class);

        $validated = $request->validate([
            'entry_date' => 'required|date',
            'reference_number' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'fiscal_year_id' => 'required|exists:fiscal_years,id',
            'fiscal_period_id' => 'required|exists:fiscal_periods,id',
            'currency_id' => 'nullable|exists:currencies,id',
            'exchange_rate' => 'nullable|numeric|min:0',
            'lines' => 'required|array|min:2',
            'lines.*.account_id' => 'required|exists:accounts,id',
            'lines.*.description' => 'nullable|string',
            'lines.*.debit' => 'nullable|numeric|min:0',
            'lines.*.credit' => 'nullable|numeric|min:0',
        ]);

        // Validate that debits equal credits
        $totalDebit = 0;
        $totalCredit = 0;
        
        foreach ($validated['lines'] as $line) {
            $totalDebit += $line['debit'] ?? 0;
            $totalCredit += $line['credit'] ?? 0;
        }
        
        if (abs($totalDebit - $totalCredit) > 0.01) {
            return redirect()
                ->back()
                ->withInput()
                ->withError('Total debits must equal total credits.');
        }

        // Generate entry number
        $latestEntry = JournalEntry::latest()->first();
        $entryNumber = 'JE-' . date('Y') . '-' . sprintf('%06d', $latestEntry ? ($latestEntry->id + 1) : 1);

        DB::beginTransaction();
        
        try {
            // Create journal entry
            $journalEntry = JournalEntry::create([
                'entry_number' => $entryNumber,
                'entry_date' => $validated['entry_date'],
                'reference_number' => $validated['reference_number'],
                'description' => $validated['description'],
                'entry_type' => 'manual',
                'status' => 'draft',
                'fiscal_year_id' => $validated['fiscal_year_id'],
                'fiscal_period_id' => $validated['fiscal_period_id'],
                'currency_id' => $validated['currency_id'],
                'exchange_rate' => $validated['exchange_rate'] ?? 1,
            ]);
            
            // Create journal entry lines
            foreach ($validated['lines'] as $line) {
                $journalEntry->journalEntryLines()->create([
                    'account_id' => $line['account_id'],
                    'description' => $line['description'] ?? null,
                    'debit' => $line['debit'] ?? 0,
                    'credit' => $line['credit'] ?? 0,
                    'foreign_debit' => isset($line['debit']) && $line['debit'] > 0 ? $line['debit'] * $journalEntry->exchange_rate : 0,
                    'foreign_credit' => isset($line['credit']) && $line['credit'] > 0 ? $line['credit'] * $journalEntry->exchange_rate : 0,
                ]);
            }
            
            DB::commit();
            
            return redirect()
                ->route('journal-entries.show', $journalEntry)
                ->withSuccess(__('crud.common.created'));
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\JournalEntry  $journalEntry
     * @return \Illuminate\Http\Response
     */
    public function show(JournalEntry $journalEntry)
    {
        $this->authorize('view', $journalEntry);

        $journalEntryLines = $journalEntry->journalEntryLines()
            ->with('account')
            ->get();

        return view('app.journal_entries.show', compact('journalEntry', 'journalEntryLines'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\JournalEntry  $journalEntry
     * @return \Illuminate\Http\Response
     */
    public function edit(JournalEntry $journalEntry)
    {
        $this->authorize('update', $journalEntry);
        
        if ($journalEntry->status !== 'draft') {
            return redirect()
                ->route('journal-entries.show', $journalEntry)
                ->withError('Only draft journal entries can be edited.');
        }

        $accounts = Account::where('is_active', true)
            ->where('allows_manual_entries', true)
            ->orderBy('code')
            ->get()
            ->pluck('full_name', 'id');
            
        $fiscalYears = FiscalYear::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');
            
        $fiscalPeriods = FiscalPeriod::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');
            
        $currencies = Currency::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');
            
        $journalEntryLines = $journalEntry->journalEntryLines()
            ->with('account')
            ->get();

        return view('app.journal_entries.edit', compact(
            'journalEntry',
            'journalEntryLines',
            'accounts',
            'fiscalYears',
            'fiscalPeriods',
            'currencies'
        ));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\JournalEntry  $journalEntry
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, JournalEntry $journalEntry)
    {
        $this->authorize('update', $journalEntry);
        
        if ($journalEntry->status !== 'draft') {
            return redirect()
                ->route('journal-entries.show', $journalEntry)
                ->withError('Only draft journal entries can be edited.');
        }

        $validated = $request->validate([
            'entry_date' => 'required|date',
            'reference_number' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'fiscal_year_id' => 'required|exists:fiscal_years,id',
            'fiscal_period_id' => 'required|exists:fiscal_periods,id',
            'currency_id' => 'nullable|exists:currencies,id',
            'exchange_rate' => 'nullable|numeric|min:0',
            'lines' => 'required|array|min:2',
            'lines.*.id' => 'nullable|exists:journal_entry_lines,id',
            'lines.*.account_id' => 'required|exists:accounts,id',
            'lines.*.description' => 'nullable|string',
            'lines.*.debit' => 'nullable|numeric|min:0',
            'lines.*.credit' => 'nullable|numeric|min:0',
        ]);

        // Validate that debits equal credits
        $totalDebit = 0;
        $totalCredit = 0;
        
        foreach ($validated['lines'] as $line) {
            $totalDebit += $line['debit'] ?? 0;
            $totalCredit += $line['credit'] ?? 0;
        }
        
        if (abs($totalDebit - $totalCredit) > 0.01) {
            return redirect()
                ->back()
                ->withInput()
                ->withError('Total debits must equal total credits.');
        }

        DB::beginTransaction();
        
        try {
            // Update journal entry
            $journalEntry->update([
                'entry_date' => $validated['entry_date'],
                'reference_number' => $validated['reference_number'],
                'description' => $validated['description'],
                'fiscal_year_id' => $validated['fiscal_year_id'],
                'fiscal_period_id' => $validated['fiscal_period_id'],
                'currency_id' => $validated['currency_id'],
                'exchange_rate' => $validated['exchange_rate'] ?? 1,
            ]);
            
            // Get existing line IDs
            $existingLineIds = $journalEntry->journalEntryLines()->pluck('id')->toArray();
            $updatedLineIds = [];
            
            // Update or create journal entry lines
            foreach ($validated['lines'] as $line) {
                if (isset($line['id'])) {
                    // Update existing line
                    $journalEntryLine = JournalEntryLine::find($line['id']);
                    $journalEntryLine->update([
                        'account_id' => $line['account_id'],
                        'description' => $line['description'] ?? null,
                        'debit' => $line['debit'] ?? 0,
                        'credit' => $line['credit'] ?? 0,
                        'foreign_debit' => isset($line['debit']) ? $line['debit'] * $journalEntry->exchange_rate : 0,
                        'foreign_credit' => isset($line['credit']) ? $line['credit'] * $journalEntry->exchange_rate : 0,
                    ]);
                    
                    $updatedLineIds[] = $line['id'];
                } else {
                    // Create new line
                    $journalEntryLine = $journalEntry->journalEntryLines()->create([
                        'account_id' => $line['account_id'],
                        'description' => $line['description'] ?? null,
                        'debit' => $line['debit'] ?? 0,
                        'credit' => $line['credit'] ?? 0,
                        'foreign_debit' => isset($line['debit']) ? $line['debit'] * $journalEntry->exchange_rate : 0,
                        'foreign_credit' => isset($line['credit']) ? $line['credit'] * $journalEntry->exchange_rate : 0,
                    ]);
                    
                    $updatedLineIds[] = $journalEntryLine->id;
                }
            }
            
            // Delete removed lines
            $linesToDelete = array_diff($existingLineIds, $updatedLineIds);
            JournalEntryLine::whereIn('id', $linesToDelete)->delete();
            
            DB::commit();
            
            return redirect()
                ->route('journal-entries.show', $journalEntry)
                ->withSuccess(__('crud.common.saved'));
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\JournalEntry  $journalEntry
     * @return \Illuminate\Http\Response
     */
    public function destroy(JournalEntry $journalEntry)
    {
        $this->authorize('delete', $journalEntry);
        
        if ($journalEntry->status !== 'draft') {
            return redirect()
                ->route('journal-entries.index')
                ->withError('Only draft journal entries can be deleted.');
        }

        DB::beginTransaction();
        
        try {
            // Delete journal entry lines
            $journalEntry->journalEntryLines()->delete();
            
            // Delete journal entry
            $journalEntry->delete();
            
            DB::commit();
            
            return redirect()
                ->route('journal-entries.index')
                ->withSuccess(__('crud.common.removed'));
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
    
    /**
     * Post the journal entry.
     *
     * @param  \App\Models\JournalEntry  $journalEntry
     * @return \Illuminate\Http\Response
     */
    public function post(JournalEntry $journalEntry)
    {
        $this->authorize('update', $journalEntry);
        
        if ($journalEntry->status !== 'draft') {
            return redirect()
                ->route('journal-entries.show', $journalEntry)
                ->withError('Only draft journal entries can be posted.');
        }
        
        // Check if the fiscal period is closed
        $fiscalPeriod = $journalEntry->fiscalPeriod;
        if ($fiscalPeriod->is_closed) {
            return redirect()
                ->route('journal-entries.show', $journalEntry)
                ->withError('Cannot post to a closed fiscal period.');
        }
        
        // Check if the journal entry is balanced
        if (!$journalEntry->is_balanced) {
            return redirect()
                ->route('journal-entries.show', $journalEntry)
                ->withError('Journal entry is not balanced.');
        }
        
        $journalEntry->update([
            'status' => 'posted',
            'posted_by' => auth()->id(),
            'posted_at' => now(),
        ]);
        
        return redirect()
            ->route('journal-entries.show', $journalEntry)
            ->withSuccess('Journal entry has been posted successfully.');
    }
    
    /**
     * Unpost the journal entry.
     *
     * @param  \App\Models\JournalEntry  $journalEntry
     * @return \Illuminate\Http\Response
     */
    public function unpost(JournalEntry $journalEntry)
    {
        $this->authorize('update', $journalEntry);
        
        if ($journalEntry->status !== 'posted') {
            return redirect()
                ->route('journal-entries.show', $journalEntry)
                ->withError('Only posted journal entries can be unposted.');
        }
        
        // Check if the fiscal period is closed
        $fiscalPeriod = $journalEntry->fiscalPeriod;
        if ($fiscalPeriod->is_closed) {
            return redirect()
                ->route('journal-entries.show', $journalEntry)
                ->withError('Cannot unpost from a closed fiscal period.');
        }
        
        $journalEntry->update([
            'status' => 'draft',
            'posted_by' => null,
            'posted_at' => null,
        ]);
        
        return redirect()
            ->route('journal-entries.show', $journalEntry)
            ->withSuccess('Journal entry has been unposted successfully.');
    }
    
    /**
     * Get fiscal periods for a fiscal year.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getFiscalPeriods(Request $request)
    {
        $fiscalYearId = $request->get('fiscal_year_id');
        
        $fiscalPeriods = FiscalPeriod::where('is_active', true)
            ->where('fiscal_year_id', $fiscalYearId)
            ->orderBy('name')
            ->get(['id', 'name']);
            
        return response()->json($fiscalPeriods);
    }
}
