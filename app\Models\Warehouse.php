<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;
use App\Traits\BranchTrait;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Warehouse extends Model implements HasMedia
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;
    use BranchTrait;
    use InteractsWithMedia;

    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'code',
        'address',
        'phone',
        'email',
        'manager_id',
        'description',
        'is_active',
        'branch_id',
        'created_by',
        'updated_by',
        'business_type_id',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];

    protected $appends = ['image', 'total_stock_items', 'total_products', 'stock_value'];

    public function getImageAttribute(){
        $file = $this->file();
        return $file ? $file->getUrl() : '';
    }

    public function file($collection = "image"){
        return $this->getMedia($collection)->first();
    }
    
    /**
     * Get the total number of stock items in this warehouse
     */
    public function getTotalStockItemsAttribute()
    {
        return $this->stockItems()->count();
    }
    
    /**
     * Get the total number of unique products in this warehouse
     */
    public function getTotalProductsAttribute()
    {
        return $this->stockItems()->distinct('product_id')->count('product_id');
    }
    
    /**
     * Get the total value of stock in this warehouse
     */
    public function getStockValueAttribute()
    {
        return $this->stockItems()->sum(\DB::raw('buying_price'));
    }

    /**
     * Get stock levels for a specific product
     */
    public function getProductStock($productId)
    {
        return $this->stockItems()
            ->where('product_id', $productId)
            ->whereNull('invoice_id')
            ->count();
    }
    
    /**
     * Check if a product is available in this warehouse
     */
    public function hasProduct($productId, $quantity = 1)
    {
        $availableQuantity = $this->getProductStock($productId);
        return $availableQuantity >= $quantity;
    }
    
    /**
     * Get low stock products (below threshold)
     */
    public function getLowStockProducts($threshold = 5)
    {
        $products = Product::join('stock_items', 'products.id', '=', 'stock_items.product_id')
            ->select('products.id', 'products.name', 'products.sku', \DB::raw('COUNT(stock_items.id) as stock_count'))
            ->where('stock_items.branch_id', $this->branch_id)
            ->whereNull('stock_items.invoice_id')
            ->groupBy('products.id', 'products.name')
            ->having('stock_count', '<', $threshold)
            ->get();
            
        return $products;
    }

    public function manager()
    {
        return $this->belongsTo(User::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }


    public function stocks()
    {
        return $this->hasMany(Stock::class);
    }

    public function stockItems()
    {
        return $this->hasMany(StockItem::class, 'branch_id', 'branch_id');
    }
    
    public function products()
    {
        return $this->belongsToMany(Product::class, 'stock_items', 'branch_id', 'product_id')
            ->withPivot('buying_price', 'selling_price')
            ->distinct();
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
    
    /**
     * Scope a query to only include active warehouses.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}

