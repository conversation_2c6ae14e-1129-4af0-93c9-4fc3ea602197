<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;
use App\Traits\BranchTrait;
use App\Traits\WarehouseTrait;

class Sale extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use BusinessTypeTrait;
    use BranchTrait;
    use CreatedUpdatedTrait;
    use WarehouseTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'product_name',
        'product_id',
        'unit_name',
        'unit_id',
        'unit_quantity',
        'selling_price',
        'supplier_id',
        'buying_price',
        'created_by',
        'updated_by',
        'branch_id',
        'discount',
        'vat',
        'quantity',
        'returned_quantity',
        'available_for_return',
        'saleable_id',
        'saleable_type',
        'business_type_id',
        'expires_at',
        'description',
        'currency_id',
        'warehouse_id',
        'location',
    ];

    protected $searchableFields = ['*'];


    protected $casts = [
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
        'expires_at' => 'datetime',
        'quantity' => 'decimal:2',
        'returned_quantity' => 'decimal:2',
        'available_for_return' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'buying_price' => 'decimal:2',
        'discount' => 'decimal:2',
        'vat' => 'decimal:2',
    ];

    protected $appends = ['selling_amount', 'buying_amount', 'units', 'selling_amount_total', 'buying_amount_total'];

    public function getSellingAmountAttribute(){
        return $this->selling_price * $this->quantity;
    }

    public function getUnitNameAttribute($value){
        return $this->value ?? $this->unit->name;
    }

    public function getBuyingAmountAttribute(){
        return $this->buying_price * $this->quantity;
    }

    public function getSellingAmountTotalAttribute(){
        $selling_amount = $this->selling_amount - $this->discount;
        if( $this->vat ) {
            $selling_amount = $selling_amount + ( $this->selling_amount * $this->vat / 100 );
        }
        return $selling_amount;
    }

    public function getBuyingAmountTotalAttribute(){
        $buying_amount = $this->buying_amount;
        // - $this->discount;
        if( $this->vat ) {
            $buying_amount = $buying_amount + ( $this->buying_amount * $this->vat / 100 );
        }
        return $buying_amount;
    }

    public function getProductNameAttribute(){
        return $this->product->name ?? '';
    }

    public function getUnitsAttribute(){
        return $this->product->units ?? [];
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }


    public function payments()
    {
        return $this->morphMany(Payment::class, 'paymentable');
    }

    public function saleable()
    {
        return $this->morphTo();
    }
}
