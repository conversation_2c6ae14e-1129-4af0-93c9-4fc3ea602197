<?php

namespace App\Http\Controllers;

use App\Models\TaxBracket;
use Illuminate\Http\Request;
use App\Http\Requests\TaxBracketStoreRequest;
use App\Http\Requests\TaxBracketUpdateRequest;

class TaxBracketController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', TaxBracket::class);

        $search = $request->get('search', '');

        $taxBrackets = TaxBracket::search($search)
            ->latest()
            ->paginate(15)
            ->withQueryString();

        return view('app.tax-brackets.index', compact('taxBrackets', 'search'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', TaxBracket::class);

        return view('app.tax-brackets.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(TaxBracketStoreRequest $request)
    {
        $this->authorize('create', TaxBracket::class);

        $validated = $request->validated();
        $validated['created_by'] = auth()->id();

        $taxBracket = TaxBracket::create($validated);

        return redirect()
            ->route('tax-brackets.show', $taxBracket)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, TaxBracket $taxBracket)
    {
        $this->authorize('view', $taxBracket);

        return view('app.tax-brackets.show', compact('taxBracket'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Request $request, TaxBracket $taxBracket)
    {
        $this->authorize('update', $taxBracket);

        return view('app.tax-brackets.edit', compact('taxBracket'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(TaxBracketUpdateRequest $request, TaxBracket $taxBracket)
    {
        $this->authorize('update', $taxBracket);

        $validated = $request->validated();
        $validated['updated_by'] = auth()->id();

        $taxBracket->update($validated);

        return redirect()
            ->route('tax-brackets.show', $taxBracket)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, TaxBracket $taxBracket)
    {
        $this->authorize('delete', $taxBracket);

        $taxBracket->delete();

        return redirect()
            ->route('tax-brackets.index')
            ->withSuccess(__('crud.common.removed'));
    }

    /**
     * Calculate tax for a given amount (AJAX endpoint)
     */
    public function calculateTax(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0'
        ]);

        $amount = $request->input('amount');
        $breakdown = TaxBracket::getTaxBreakdown($amount);

        return response()->json($breakdown);
    }

    /**
     * Preview tax calculation
     */
    public function preview(Request $request)
    {
        $this->authorize('view-any', TaxBracket::class);

        $amount = $request->get('amount', 0);
        $breakdown = null;

        if ($amount > 0) {
            $breakdown = TaxBracket::getTaxBreakdown($amount);
        }

        $taxBrackets = TaxBracket::getActiveBrackets();

        return view('app.tax-brackets.preview', compact('amount', 'breakdown', 'taxBrackets'));
    }
}
