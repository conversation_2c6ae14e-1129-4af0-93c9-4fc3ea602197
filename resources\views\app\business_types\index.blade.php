@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid" id="business-type-el">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="title">@lang('crud.business_types.name')</x-slot>
        <x-slot name="controls">
            @can('create', App\Models\BusinessType::class)
            <a href="business-types/create" class="btn btn-info btn-sm">
                <!-- <i class="icon ion-md-add"></i> -->
                @lang('crud.business_types.create_new_business_type')
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="card card-table">
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            @lang('crud.business_types.inputs.name')
                        </th>
                        <th class="text-left">
                            @lang('crud.business_types.inputs.status_id')
                        </th>
                        <th class="text-left">
                            @lang('crud.business_types.inputs.description')
                        </th>
                        <th class="text-center">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($businessTypes as $businessType)
                    <tr>
                        <td>{{ $businessType->name ?? '-' }}</td>
                        <td>
                            <div class="form-check form-switch">
                                <form action="" method="GET" class="switch-form"></form>
                                <input class="form-check-input" value="{{ $businessType->id ?? ''}}" type="checkbox" @change="changeSwitchBtn($event)" @if($businessType->id == auth()->user()->business_type_id ) checked @endif id="business-type">
                                <label class="form-check-label" for="business-type"></label>
                            </div>
                        </td>
                        <td>{{ $businessType->description ?? '-' }}</td>
                        <td class="text-center" style="width: 134px;">
                            <div
                                role="group"
                                aria-label="Row Actions"
                                class="btn-group"
                            >
                                @can('update', $businessType)
                                <a
                                    href="{{ route('business-types.edit', $businessType) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        <!-- <i class="icon ion-md-create"></i> --> @lang('crud.common.edit')
                                    </button>
                                </a>
                                @endcan @can('view', $businessType)
                                <a
                                    href="{{ route('business-types.show', $businessType) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        <!-- <i class="icon ion-md-eye"></i> --> @lang('crud.common.show')
                                    </button>
                                </a>
                                @endcan @can('delete', $businessType)
                                <form
                                    action="{{ route('business-types.destroy', $businessType) }}"
                                    method="POST"
                                    onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                >
                                    @csrf @method('DELETE')
                                    <button
                                        type="submit"
                                        class="btn btn-light text-danger m-1"
                                    >
                                        <!-- <i class="icon ion-md-trash"></i> --> @lang('crud.common.delete')
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="4">
                            @lang('crud.common.no_items_found')
                        </td>
                    </tr>
                    @endforelse
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="4">
                            {!! $businessTypes->render() !!}
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>
@endsection


@push('scripts')

    <script type="text/javascript">

        new Vue({
            el: "#business-type-el",

            data(){
                return {

                }
            },

            methods: {
                changeSwitchBtn(e){
                    $(".switch-form").attr("action", "/set-business-type/" + e.target.value);
                    $(".switch-form").submit();
                }
            },

            created(){
                console.log( $ )
            }

        });

    </script>

@endpush
