@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">pencil-square</x-slot>
        <x-slot name="title">Edit Category: {{ $category->name }}</x-slot>
        <x-slot name="subtitle">Update category information</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('categories.index') }}">Categories</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('categories.show', $category) }}" class="btn btn-outline-info me-2">
                <i class="bi bi-eye me-1"></i> View Category
            </a>
            <a href="{{ route('categories.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Categories
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <div class="col-lg-8 mb-3 mb-lg-0">
            <!-- Card -->
            <div class="card mb-3 mb-lg-5">
                <!-- Header -->
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-header-title">Category Information</h4>
                        <span class="badge bg-primary">ID: {{ $category->id }}</span>
                    </div>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <x-form
                        method="PUT"
                        action="{{ route('categories.update', $category) }}"
                        id="categoryForm"
                    >
                        @include('app.categories.form-inputs')
                    </x-form>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>

        <div class="col-lg-4">
            <!-- Card -->
            <div class="card">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Actions</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <!-- Form Group -->
                        <div class="mb-4">
                            <label class="form-label">Last Updated</label>
                            <div class="mb-2">
                                <span class="text-body">{{ $category->updated_at->format('F d, Y H:i') }}</span>
                            </div>
                        </div>
                        <!-- End Form Group -->
                        
                        <div class="d-flex justify-content-end">
                            <a href="{{ route('categories.index') }}" class="btn btn-white me-2">Cancel</a>
                            <button type="submit" form="categoryForm" class="btn btn-primary">
                                <i class="bi-check-circle me-1"></i> @lang('crud.common.update')
                            </button>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="mt-2">
                            <a href="{{ route('categories.create') }}" class="btn btn-outline-primary btn-sm">
                                <i class="bi-plus-circle me-1"></i> @lang('crud.common.create')
                            </a>
                            
                            @can('delete', $category)
                            <form action="{{ route('categories.destroy', $category) }}" 
                                  method="POST" 
                                  class="mt-2"
                                  onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')">
                                @csrf @method('DELETE')
                                <button type="submit" class="btn btn-outline-danger btn-sm">
                                    <i class="bi-trash me-1"></i> @lang('crud.common.delete')
                                </button>
                            </form>
                            @endcan
                        </div>
                    </div>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->

            <!-- Card -->
            <div class="card mt-3">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Category Usage</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <span class="flex-shrink-0 me-3">
                            <i class="bi bi-box fs-2 text-primary"></i>
                        </span>
                        <div class="flex-grow-1">
                            <h5 class="mb-0">{{ count($category->products ?? []) }} Products</h5>
                            <small class="text-muted">Using this category</small>
                        </div>
                    </div>
                    
                    <a href="{{ route('categories.show', $category) }}" class="btn btn-outline-primary btn-sm w-100">
                        <i class="bi-eye me-1"></i> View Details
                    </a>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>
    </div>
</div>
@endsection
