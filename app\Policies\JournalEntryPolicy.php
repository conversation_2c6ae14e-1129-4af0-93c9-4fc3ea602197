<?php

namespace App\Policies;

use App\Models\User;
use App\Models\JournalEntry;
use Illuminate\Auth\Access\HandlesAuthorization;

class JournalEntryPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo('list journal-entries');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\JournalEntry  $journalEntry
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, JournalEntry $journalEntry)
    {
        return $user->hasPermissionTo('view journal-entries');
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo('create journal-entries');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\JournalEntry  $journalEntry
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, JournalEntry $journalEntry)
    {
        return $user->hasPermissionTo('update journal-entries') && 
            $journalEntry->status !== 'posted' && 
            $journalEntry->fiscalPeriod->status === 'open';
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\JournalEntry  $journalEntry
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, JournalEntry $journalEntry)
    {
        return $user->hasPermissionTo('delete journal-entries') && 
            $journalEntry->status !== 'posted' && 
            $journalEntry->fiscalPeriod->status === 'open';
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\JournalEntry  $journalEntry
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, JournalEntry $journalEntry)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\JournalEntry  $journalEntry
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, JournalEntry $journalEntry)
    {
        return false;
    }

    /**
     * Determine whether the user can post the journal entry.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\JournalEntry  $journalEntry
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function post(User $user, JournalEntry $journalEntry)
    {
        return $user->hasPermissionTo('post journal-entries') && 
            $journalEntry->status === 'draft' && 
            $journalEntry->fiscalPeriod->status === 'open';
    }

    /**
     * Determine whether the user can unpost the journal entry.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\JournalEntry  $journalEntry
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function unpost(User $user, JournalEntry $journalEntry)
    {
        return $user->hasPermissionTo('unpost journal-entries') && 
            $journalEntry->status === 'posted' && 
            $journalEntry->fiscalPeriod->status === 'open' && 
            $journalEntry->entry_type !== 'system';
    }
}
