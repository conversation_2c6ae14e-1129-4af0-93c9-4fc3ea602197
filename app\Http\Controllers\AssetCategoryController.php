<?php

namespace App\Http\Controllers;

use App\Models\Account;
use App\Models\AssetCategory;
use Illuminate\Http\Request;

class AssetCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', AssetCategory::class);

        $search = $request->get('search', '');

        $assetCategories = AssetCategory::search($search)
            ->latest()
            ->paginate()
            ->withQueryString();

        return view('app.asset_categories.index', compact('assetCategories', 'search'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', AssetCategory::class);

        $accounts = Account::where('is_active', true)
            ->orderBy('code')
            ->get()
            ->pluck('full_name', 'id');

        return view('app.asset_categories.create', compact('accounts'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', AssetCategory::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:255|unique:asset_categories,code',
            'description' => 'nullable|string',
            'asset_account_id' => 'required|exists:accounts,id',
            'accumulated_depreciation_account_id' => 'required|exists:accounts,id',
            'depreciation_expense_account_id' => 'required|exists:accounts,id',
            'gain_loss_account_id' => 'required|exists:accounts,id',
            'useful_life_years' => 'required|integer|min:1',
            'depreciation_rate' => 'required|numeric|min:0|max:1',
            'depreciation_method' => 'required|in:straight_line,declining_balance,sum_of_years_digits',
            'is_active' => 'boolean',
        ]);

        $assetCategory = AssetCategory::create($validated);

        return redirect()
            ->route('asset-categories.edit', $assetCategory)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\AssetCategory  $assetCategory
     * @return \Illuminate\Http\Response
     */
    public function show(AssetCategory $assetCategory)
    {
        $this->authorize('view', $assetCategory);

        return view('app.asset_categories.show', compact('assetCategory'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\AssetCategory  $assetCategory
     * @return \Illuminate\Http\Response
     */
    public function edit(AssetCategory $assetCategory)
    {
        $this->authorize('update', $assetCategory);

        $accounts = Account::where('is_active', true)
            ->orderBy('code')
            ->get()
            ->pluck('full_name', 'id');

        return view('app.asset_categories.edit', compact('assetCategory', 'accounts'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\AssetCategory  $assetCategory
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, AssetCategory $assetCategory)
    {
        $this->authorize('update', $assetCategory);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:255|unique:asset_categories,code,' . $assetCategory->id,
            'description' => 'nullable|string',
            'asset_account_id' => 'required|exists:accounts,id',
            'accumulated_depreciation_account_id' => 'required|exists:accounts,id',
            'depreciation_expense_account_id' => 'required|exists:accounts,id',
            'gain_loss_account_id' => 'required|exists:accounts,id',
            'useful_life_years' => 'required|integer|min:1',
            'depreciation_rate' => 'required|numeric|min:0|max:1',
            'depreciation_method' => 'required|in:straight_line,declining_balance,sum_of_years_digits',
            'is_active' => 'boolean',
        ]);

        $assetCategory->update($validated);

        return redirect()
            ->route('asset-categories.edit', $assetCategory)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\AssetCategory  $assetCategory
     * @return \Illuminate\Http\Response
     */
    public function destroy(AssetCategory $assetCategory)
    {
        $this->authorize('delete', $assetCategory);

        if ($assetCategory->assets()->count() > 0) {
            return redirect()
                ->route('asset-categories.index')
                ->withError('Asset category has assets and cannot be deleted.');
        }

        $assetCategory->delete();

        return redirect()
            ->route('asset-categories.index')
            ->withSuccess(__('crud.common.removed'));
    }
}
