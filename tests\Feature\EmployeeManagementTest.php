<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Employee;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Spatie\Permission\Models\Permission;

class EmployeeManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create permissions
        Permission::create(['name' => 'list employees']);
        Permission::create(['name' => 'view employees']);
        Permission::create(['name' => 'create employees']);
        Permission::create(['name' => 'update employees']);
        Permission::create(['name' => 'delete employees']);
        
        // Create a user with employee permissions
        $this->user = User::factory()->create();
        $this->user->givePermissionTo([
            'list employees',
            'view employees', 
            'create employees',
            'update employees',
            'delete employees'
        ]);
    }

    /** @test */
    public function user_can_view_employees_index()
    {
        $this->actingAs($this->user);
        
        $response = $this->get(route('employees.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('app.employees.index');
    }

    /** @test */
    public function user_can_create_employee()
    {
        $this->actingAs($this->user);
        
        $employeeData = [
            'name' => $this->faker->name,
            'email' => $this->faker->unique()->safeEmail,
            'phone' => $this->faker->phoneNumber,
            'basic_pay' => 50000.00,
            'grade' => 'Manager',
            'description' => 'Test employee description',
        ];
        
        $response = $this->post(route('employees.store'), $employeeData);
        
        $this->assertDatabaseHas('employees', [
            'name' => $employeeData['name'],
            'email' => $employeeData['email'],
            'basic_pay' => $employeeData['basic_pay'],
        ]);
        
        $employee = Employee::where('email', $employeeData['email'])->first();
        $response->assertRedirect(route('employees.show', $employee));
    }

    /** @test */
    public function user_can_view_employee_details()
    {
        $this->actingAs($this->user);
        
        $employee = Employee::factory()->create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'basic_pay' => 60000.00,
        ]);
        
        $response = $this->get(route('employees.show', $employee));
        
        $response->assertStatus(200);
        $response->assertViewIs('app.employees.show');
        $response->assertSee('John Doe');
        $response->assertSee('<EMAIL>');
    }

    /** @test */
    public function user_can_update_employee()
    {
        $this->actingAs($this->user);
        
        $employee = Employee::factory()->create([
            'name' => 'Jane Doe',
            'basic_pay' => 55000.00,
        ]);
        
        $updateData = [
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'basic_pay' => 65000.00,
            'grade' => 'Senior Manager',
        ];
        
        $response = $this->put(route('employees.update', $employee), $updateData);
        
        $this->assertDatabaseHas('employees', [
            'id' => $employee->id,
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'basic_pay' => 65000.00,
        ]);
        
        $response->assertRedirect(route('employees.show', $employee));
    }

    /** @test */
    public function user_can_delete_employee_without_payroll_records()
    {
        $this->actingAs($this->user);
        
        $employee = Employee::factory()->create();
        
        $response = $this->delete(route('employees.destroy', $employee));
        
        $this->assertSoftDeleted('employees', ['id' => $employee->id]);
        $response->assertRedirect(route('employees.index'));
    }

    /** @test */
    public function user_cannot_delete_employee_with_payroll_records()
    {
        $this->actingAs($this->user);
        
        $employee = Employee::factory()->create();
        
        // Create a payroll record for the employee
        $employee->monthlyPayrolls()->create([
            'name' => 'Test Payroll',
            'basic_pay' => 50000,
            'net_pay' => 45000,
            'date_from' => now()->startOfMonth(),
            'date_to' => now()->endOfMonth(),
            'created_by' => $this->user->id,
        ]);
        
        $response = $this->delete(route('employees.destroy', $employee));
        
        $this->assertDatabaseHas('employees', ['id' => $employee->id]);
        $response->assertRedirect();
        $response->assertSessionHas('error');
    }

    /** @test */
    public function employee_validation_works()
    {
        $this->actingAs($this->user);
        
        // Test required fields
        $response = $this->post(route('employees.store'), []);
        
        $response->assertSessionHasErrors(['name', 'email', 'basic_pay']);
        
        // Test unique email
        $existingEmployee = Employee::factory()->create(['email' => '<EMAIL>']);
        
        $response = $this->post(route('employees.store'), [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'basic_pay' => 50000,
        ]);
        
        $response->assertSessionHasErrors(['email']);
    }

    /** @test */
    public function user_without_permissions_cannot_access_employees()
    {
        $unauthorizedUser = User::factory()->create();
        $this->actingAs($unauthorizedUser);
        
        $response = $this->get(route('employees.index'));
        $response->assertStatus(403);
        
        $response = $this->get(route('employees.create'));
        $response->assertStatus(403);
    }
}
