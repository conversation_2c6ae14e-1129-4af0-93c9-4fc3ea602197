@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">person-add</x-slot>
        <x-slot name="title">@lang('crud.customers.create_new_customer')</x-slot>
        <x-slot name="subtitle">Add a new customer to your system</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('customers.index') }}">@lang('crud.customers.name')</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('customers.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> @lang('crud.customers.back_to_customers')
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <div class="col-lg-9 mb-3 mb-lg-0">
            <!-- Card -->
            <div class="card mb-3 mb-lg-5">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">@lang('crud.customers.customer_details')</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <x-form
                        method="POST"
                        action="{{ route('customers.store') }}"
                        has-files
                        id="customerForm"
                    >
                        @include('app.customers.form-inputs')
                    </x-form>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>

        <div class="col-lg-3">
            <!-- Card -->
            <div class="card">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Actions</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <div class="d-flex justify-content-end">
                            <a href="{{ route('customers.index') }}" class="btn btn-white me-2">Cancel</a>
                            <button type="submit" form="customerForm" class="btn btn-primary">
                                <i class="bi-check-circle me-1"></i> @lang('crud.common.create')
                            </button>
                        </div>
                    </div>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->

            <!-- Card -->
            <div class="card mt-3">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Tips</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <ul class="list-unstyled list-py-2 mb-0">
                        <li>
                            <i class="bi-check-circle text-success me-2"></i>
                            Use the full name of the customer
                        </li>
                        <li>
                            <i class="bi-check-circle text-success me-2"></i>
                            Include complete contact information
                        </li>
                        <li>
                            <i class="bi-check-circle text-success me-2"></i>
                            Add a photo to easily identify customers
                        </li>
                        <li>
                            <i class="bi-check-circle text-success me-2"></i>
                            Include notes about preferences or special requirements
                        </li>
                    </ul>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>
    </div>
</div>
@endsection
