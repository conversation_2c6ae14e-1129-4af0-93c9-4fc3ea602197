<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StockStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Required fields
            'product_id' => ['required', 'tenant_exists:products,id'],
            'quantity' => ['required', 'numeric', 'min:0'],

            // Optional fields
            'unit_id' => ['nullable', 'tenant_exists:units,id'],
            'supplier_id' => ['nullable', 'tenant_exists:suppliers,id'],
            'location_id' => ['nullable', 'tenant_exists:locations,id'],
            'branch_id' => ['nullable', 'tenant_exists:branches,id'],

            // Pricing
            'buying_price' => ['nullable', 'numeric', 'min:0'],
            'selling_price' => ['nullable', 'numeric', 'min:0'],
            'discount' => ['nullable', 'numeric', 'min:0'],
            'vat' => ['nullable', 'numeric', 'min:0'],

            // Dates
            'expires_at' => ['nullable', 'date', 'after:today'],

            // Text fields
            'description' => ['nullable', 'string', 'max:1000'],
            'location' => ['nullable', 'string', 'max:255'],

            // System fields
            'stock_id' => ['nullable', 'tenant_exists:stocks,id'],
            'warehouse_id' => ['nullable', 'tenant_exists:warehouses,id'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'product_id.required' => 'Please select a product.',
            'product_id.tenant_exists' => 'The selected product is invalid.',
            'quantity.required' => 'Quantity is required.',
            'quantity.min' => 'Quantity must be at least 0.',
            'buying_price.min' => 'Buying price must be at least 0.',
            'selling_price.min' => 'Selling price must be at least 0.',
            'expires_at.after' => 'Expiry date must be in the future.',
        ];
    }

    /**
     * Get custom attribute names.
     */
    public function attributes(): array
    {
        return [
            'product_id' => 'product',
            'supplier_id' => 'supplier',
            'location_id' => 'location',
            'branch_id' => 'branch',
            'unit_id' => 'unit',
            'buying_price' => 'buying price',
            'selling_price' => 'selling price',
            'expires_at' => 'expiry date',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default branch if not provided
        if (!$this->has('branch_id') && auth()->user()) {
            $this->merge([
                'branch_id' => auth()->user()->branch_id
            ]);
        }

        // Calculate balance from quantity
        if ($this->has('quantity')) {
            $this->merge([
                'balance' => $this->quantity
            ]);
        }
    }
}
