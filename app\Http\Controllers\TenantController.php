<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UserTenant;
use App\Models\Tenant;

class TenantController extends Controller
{
    /**
     * Switch to a different tenant
     */
    public function switch(Request $request, $tenantId)
    {
        $user = auth()->user();

        // Validate that user has access to this tenant
        if (!$user->hasAccessToTenant($tenantId)) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have access to this tenant.'
            ], 403);
        }

        try {
            // Switch to the new tenant
            $tenant = Tenant::where("email", auth()->user()->email)->first();
            $tenant->update(['current_tenant' => $tenantId]);
            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tenant not found.'
                ], 404);
            }

            // Make the tenant current
            $tenant->switching();

            // Get tenant info for response
            $tenantInfo = [
                'id' => $tenantId,
                'name' => ucfirst(str_replace('_', ' ', $tenantId))
            ];

            return response()->json([
                'success' => true,
                'message' => 'Successfully switched to ' . $tenantInfo['name'],
                'tenant' => $tenantInfo,
                'redirect_url' => route('dashboard')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to switch tenant: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current user's available tenants
     */
    public function getAvailableTenants()
    {
        $user = auth()->user();
        $tenants = $user->getAvailableTenants();

        return response()->json([
            'success' => true,
            'tenants' => $tenants,
            'current_tenant' => $user->getCurrentTenant()
        ]);
    }

    /**
     * Set primary tenant for user
     */
    public function setPrimary(Request $request, $tenantId)
    {
        $user = auth()->user();

        // Validate that user has access to this tenant
        if (!$user->hasAccessToTenant($tenantId)) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have access to this tenant.'
            ], 403);
        }

        try {
            $user->setPrimaryTenant($tenantId);

            return response()->json([
                'success' => true,
                'message' => 'Primary tenant updated successfully.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update primary tenant: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show tenant setup page
     */
    public function setup()
    {
        return view('app.users.tenant-setup');
    }

    /**
     * Add current user to a tenant (for testing)
     */
    public function addUserToTenant(Request $request)
    {
        $request->validate([
            'tenant_id' => 'required|string|max:255',
            'role' => 'required|string|in:user,manager,admin'
        ]);

        $user = auth()->user();

        try {
            // Check if user is already in this tenant
            $existing = UserTenant::where('user_email', $user->email)
                                 ->where('tenant_id', $request->tenant_id)
                                 ->first();

            if ($existing) {
                return redirect()->back()->with('error', 'You are already a member of this tenant.');
            }

            // Add user to tenant
            UserTenant::addUserToTenant(
                $user->email,
                $request->tenant_id,
                $request->role,
                $user->id
            );

            return redirect()->back()->with('success',
                'Successfully added to tenant: ' . $request->tenant_id . ' as ' . $request->role);

        } catch (\Exception $e) {
            return redirect()->back()->with('error',
                'Failed to add to tenant: ' . $e->getMessage());
        }
    }
}
