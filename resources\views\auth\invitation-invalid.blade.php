@extends('layouts.guest')

@section('content')
<style>
    .login-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .login-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
        background-size: cover;
        opacity: 0.3;
    }

    .login-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        position: relative;
        z-index: 2;
    }

    .login-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 35px 70px rgba(0, 0, 0, 0.2);
    }
</style>

<div class="login-container">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="login-card">
                <div class="card-body p-5 text-center">
                    <!-- Error Icon -->
                    <div class="mb-4">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                    </div>

                    <!-- Error Message -->
                    <h2 class="h4 mb-3 text-danger">Invalid Invitation</h2>
                    <p class="text-muted mb-4">
                        {{ $message ?? 'This invitation link is not valid or has expired.' }}
                    </p>

                    <!-- Action Buttons -->
                    <div class="d-grid gap-2">
                        <a href="{{ route('login') }}" class="btn btn-primary">
                            <i class="bi bi-box-arrow-in-right me-2"></i>Go to Login
                        </a>
                        <a href="{{ route('register') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-person-plus me-2"></i>Create New Account
                        </a>
                    </div>

                    <!-- Help Text -->
                    <div class="mt-4">
                        <p class="text-muted small">
                            If you believe this is an error, please contact the person who sent you this invitation 
                            or reach out to our support team.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
