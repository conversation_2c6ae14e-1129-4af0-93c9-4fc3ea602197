@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-pencil me-2"></i>Edit Fiscal Year
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group" role="group">
                <a href="{{ route('fiscal-years.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Fiscal Years
                </a>
                <a href="{{ route('fiscal-years.show', $fiscalYear) }}" class="btn btn-outline-info btn-sm">
                    <i class="bi bi-eye me-1"></i>View Details
                </a>
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar-range me-2"></i>Edit: {{ $fiscalYear->name }}
                    </h5>
                </div>

                <form method="POST" action="{{ route('fiscal-years.update', $fiscalYear) }}">
                    @csrf
                    @method('PUT')
                    <div class="card-body">

                        <!-- Error Display -->
                        @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <h6 class="alert-heading">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Please fix the following errors:
                            </h6>
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        @endif

                        <!-- Current Status Display -->
                        <div class="alert alert-info">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Current Status:</strong>
                                    @switch($fiscalYear->status)
                                        @case('open')
                                            <span class="badge bg-success">Open</span>
                                            @break
                                        @case('closed')
                                            <span class="badge bg-danger">Closed</span>
                                            @break
                                        @case('locked')
                                            <span class="badge bg-warning">Locked</span>
                                            @break
                                    @endswitch
                                </div>
                                <div class="col-md-6">
                                    <strong>Active:</strong>
                                    @if($fiscalYear->is_active)
                                        <span class="badge bg-success">Yes</span>
                                    @else
                                        <span class="badge bg-secondary">No</span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Basic Information -->
                        <h6 class="text-primary mb-3">
                            <i class="bi bi-info-circle me-2"></i>Basic Information
                        </h6>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label class="form-label">Name <span class="text-danger">*</span></label>
                                <input type="text" name="name" class="form-control @error('name') is-invalid @enderror"
                                       placeholder="e.g., FY 2024-2025" value="{{ old('name', $fiscalYear->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Enter a descriptive name for the fiscal year</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Start Date <span class="text-danger">*</span></label>
                                <input type="date" name="start_date" class="form-control @error('start_date') is-invalid @enderror"
                                       value="{{ old('start_date', $fiscalYear->start_date->format('Y-m-d')) }}" required>
                                @error('start_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">End Date <span class="text-danger">*</span></label>
                                <input type="date" name="end_date" class="form-control @error('end_date') is-invalid @enderror"
                                       value="{{ old('end_date', $fiscalYear->end_date->format('Y-m-d')) }}" required>
                                @error('end_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Status Settings -->
                        <h6 class="text-primary mb-3 mt-4">
                            <i class="bi bi-gear me-2"></i>Status Settings
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Status</label>
                                <select name="status" class="form-select @error('status') is-invalid @enderror">
                                    <option value="open" {{ old('status', $fiscalYear->status) == 'open' ? 'selected' : '' }}>Open</option>
                                    <option value="closed" {{ old('status', $fiscalYear->status) == 'closed' ? 'selected' : '' }}>Closed</option>
                                    <option value="locked" {{ old('status', $fiscalYear->status) == 'locked' ? 'selected' : '' }}>Locked</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Open: Allow transactions | Closed: No new transactions | Locked: Audit mode</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input type="checkbox" name="is_active" class="form-check-input" id="is_active"
                                           value="1" {{ old('is_active', $fiscalYear->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        <strong>Active Fiscal Year</strong>
                                    </label>
                                </div>
                                <div class="form-text">Set as the current active fiscal year</div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea name="description" class="form-control @error('description') is-invalid @enderror"
                                      rows="3" placeholder="Optional description for this fiscal year">{{ old('description', $fiscalYear->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Warning for Closed Years -->
                        @if($fiscalYear->status === 'closed')
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> This fiscal year is closed. Changing the status to "Open" will allow new transactions to be posted to this year.
                        </div>
                        @endif

                        <!-- Audit Information -->
                        <div class="alert alert-light">
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>Created:</strong> {{ $fiscalYear->created_at->format('M d, Y H:i') }}
                                        @if($fiscalYear->createdBy)
                                            by {{ $fiscalYear->createdBy->name }}
                                        @endif
                                    </small>
                                </div>
                                @if($fiscalYear->updated_at != $fiscalYear->created_at)
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>Last Updated:</strong> {{ $fiscalYear->updated_at->format('M d, Y H:i') }}
                                        @if($fiscalYear->updatedBy)
                                            by {{ $fiscalYear->updatedBy->name }}
                                        @endif
                                    </small>
                                </div>
                                @endif
                            </div>
                        </div>

                    </div>

                    <div class="card-footer d-flex justify-content-between">
                        <a href="{{ route('fiscal-years.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>Update Fiscal Year
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
