@php $editing = isset($currency) @endphp

<div class="row">
    <x-inputs.group class="col-sm-6 mb-4">
        <x-inputs.text
            name="name"
            label="Name"
            :value="old('name', ($editing ? $currency->name : ''))"
            maxlength="255"
            placeholder="Currency Name"
            required
        ></x-inputs.text>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6 mb-4">
        <x-inputs.text
            name="code"
            label="Code"
            :value="old('code', ($editing ? $currency->code : ''))"
            maxlength="3"
            placeholder="Currency Code (e.g., USD)"
            required
        ></x-inputs.text>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6 mb-4">
        <x-inputs.text
            name="symbol"
            label="Symbol"
            :value="old('symbol', ($editing ? $currency->symbol : ''))"
            maxlength="10"
            placeholder="Currency Symbol (e.g., $)"
            required
        ></x-inputs.text>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6 mb-4">
        <x-inputs.number
            name="decimal_places"
            label="Decimal Places"
            :value="old('decimal_places', ($editing ? $currency->decimal_places : 2))"
            min="0"
            max="6"
            required
        ></x-inputs.number>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6 mb-4">
        <x-inputs.number
            name="exchange_rate"
            label="Exchange Rate"
            :value="old('exchange_rate', ($editing ? $currency->exchange_rate : 1))"
            min="0"
            step="0.000001"
            required
        ></x-inputs.number>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6 mb-4">
        <x-inputs.checkbox
            name="is_base_currency"
            label="Is Base Currency"
            :checked="old('is_base_currency', ($editing ? $currency->is_base_currency : false))"
        ></x-inputs.checkbox>
        <div class="form-text text-muted">
            Only one currency can be the base currency. Setting this will update other currencies.
        </div>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6 mb-4">
        <x-inputs.checkbox
            name="is_default"
            label="Is Default"
            :checked="old('is_default', ($editing ? $currency->is_default : false))"
        ></x-inputs.checkbox>
        <div class="form-text text-muted">
            Default currency for new transactions.
        </div>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6 mb-4">
        <x-inputs.checkbox
            name="is_active"
            label="Is Active"
            :checked="old('is_active', ($editing ? $currency->is_active : true))"
        ></x-inputs.checkbox>
    </x-inputs.group>
</div>
