<?php

namespace App\Http\Controllers;

use App\Models\Location;
use App\Models\Status;
use Illuminate\Http\Request;
use App\Http\Requests\LocationStoreRequest;
use App\Http\Requests\LocationUpdateRequest;

class LocationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Location::class);

        $search = $request->get('search', '');
        $status = $request->get('status', '');

        $query = Location::with(['status', 'createdBy'])
            ->search($search)
            ->latest();

        if ($status) {
            $query->where('status_id', $status);
        }

        $locations = $query->paginate(15)->withQueryString();

        $statuses = Status::pluck('name', 'id');

        return view('app.locations.index', compact('locations', 'search', 'status', 'statuses'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', Location::class);

        $statuses = Status::pluck('name', 'id');

        return view('app.locations.create', compact('statuses'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\LocationStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(LocationStoreRequest $request)
    {
        $this->authorize('create', Location::class);

        $validated = $request->validated();
        $validated['created_by'] = auth()->id();
        $validated['business_type_id'] = auth()->user()->business_type_id;

        $location = Location::create($validated);

        return redirect()
            ->route('locations.show', $location)
            ->with('success', 'Location created successfully!');
    }

    /**
     * Display the specified resource.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Location $location
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Location $location)
    {
        $this->authorize('view', $location);

        $location->load(['status', 'createdBy', 'updatedBy']);

        return view('app.locations.show', compact('location'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Location $location
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Location $location)
    {
        $this->authorize('update', $location);

        $statuses = Status::pluck('name', 'id');

        return view('app.locations.edit', compact('location', 'statuses'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \App\Http\Requests\LocationUpdateRequest $request
     * @param \App\Models\Location $location
     * @return \Illuminate\Http\Response
     */
    public function update(LocationUpdateRequest $request, Location $location)
    {
        $this->authorize('update', $location);

        try {
            $validated = $request->validated();
            $validated['updated_by'] = auth()->id();
            $validated['business_type_id'] = auth()->user()->business_type_id;

            $location->update($validated);

            return redirect()
                ->route('locations.show', $location)
                ->with('success', 'Location updated successfully!');

        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to update location: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Location $location
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Location $location)
    {
        $this->authorize('delete', $location);

        $location->delete();

        return redirect()
            ->route('locations.index')
            ->withSuccess(__('crud.common.removed'));
    }
}
