<?php

namespace App\Http\Controllers;

use App\Models\Unit;
use App\Models\User;
use App\Models\Customer;
use App\Models\Quotation;
use App\Models\Sale;
use App\Models\Product;
use Illuminate\Http\Request;
use App\Http\Requests\QuotationStoreRequest;
use App\Http\Requests\QuotationUpdateRequest;

use Facades\App\Libraries\ProductHandler;
use Facades\App\Libraries\InvoiceHandler;
use Facades\App\Cache\Repo;

class QuotationController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Quotation::class);

        $search = $request->get('search', '');
        $status = $request->get('status', '');
        $customer = $request->get('customer', '');
        $dateFrom = $request->get('date_from', '');
        $dateTo = $request->get('date_to', '');

        $quotations = Quotation::with(['customer', 'createdBy', 'status'])
            ->when($search, function ($query, $search) {
                return $query->where(function ($q) use ($search) {
                    $q->where('customer_name', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhere('reference_no', 'like', "%{$search}%")
                      ->orWhereHas('customer', function ($customerQuery) use ($search) {
                          $customerQuery->where('name', 'like', "%{$search}%");
                      });
                });
            })
            ->when($status, function ($query, $status) {
                return $query->where('status_id', $status);
            })
            ->when($customer, function ($query, $customer) {
                return $query->where('customer_id', $customer);
            })
            ->when($dateFrom, function ($query, $dateFrom) {
                return $query->whereDate('created_at', '>=', $dateFrom);
            })
            ->when($dateTo, function ($query, $dateTo) {
                return $query->whereDate('created_at', '<=', $dateTo);
            })
            ->latest()
            ->paginate(15)
            ->withQueryString();

        $customers = Customer::orderBy('name')->pluck('name', 'id');
        $statuses = [
            '10' => 'Draft',
            '11' => 'Sent',
            '12' => 'Accepted',
            '13' => 'Rejected',
            '14' => 'Expired'
        ];

        return view('app.quotations.index', compact(
            'quotations',
            'search',
            'status',
            'customer',
            'dateFrom',
            'dateTo',
            'customers',
            'statuses'
        ));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Quotation::class);

        $users = User::pluck('name', 'id');
        $customers = Customer::pluck('name', 'id');
        $products = Product::with("unit", "units")->get();

        return view('app.quotations.create', compact('users','customers', 'products'));
    }

    /**
     * @param \App\Http\Requests\QuotationStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(QuotationStoreRequest $request)
    {
        $this->authorize('create', Quotation::class);

        $validated = $request->validated();

        if( ! auth()->user()->branch_id ) return redirect()->back()->with("error", "Please Select Branch first");

        $data = _array( $request->quotation );

        if( request()->customer_id ){
            $customer = Customer::find( request()->customer_id );
            $customer = ( $customer ) ? $customer : Customer::create( ["name" => request()->customer_id] );
            $data["customer_id"] = $customer->id ?? '';
            $data["customer_name"] = $customer->name ?? '';
        }

        $quotation = Quotation::create( $data );
        $sales  = _array( _from($data, "sales") );

        foreach ($sales as $sale) {
            if( _from($sale, "product_id") ) {
                $unit = Unit::find(_from($sale, "unit_id") );
                if( $unit ) {
                    $sale['unit_name'] = $unit->name;
                    $sale['unit_quantity'] = $unit->quantity;
                }
                $quotation->sales()->create($sale);
            }
        }

        return redirect()
            ->route('quotations.show', $quotation)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Quotation $quotation
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Quotation $quotation)
    {
        $this->authorize('view', $quotation);
        return view('app.quotations.show', compact('quotation'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Quotation $quotation
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Quotation $quotation)
    {
        $this->authorize('update', $quotation);

        $customers = Customer::pluck('name', 'id');
        $products = Product::with("unit", "units")->get();
        $quotation->sales;

        return view('app.quotations.edit', compact('quotation', 'customers', 'products'));
    }

    /**
     * @param \App\Http\Requests\QuotationUpdateRequest $request
     * @param \App\Models\Quotation $quotation
     * @return \Illuminate\Http\Response
     */
    public function update(QuotationUpdateRequest $request, Quotation $quotation)
    {
        $this->authorize('update', $quotation);

        $validated = $request->validated();

        if( ! auth()->user()->branch_id ) return redirect()->back()->with("error", "Please Select Branch first");

        $data = _array( $request->quotation );
        unset($data['created_by']);
        unset($data['payments']);

        if( request()->customer_id ){
            $customer = Customer::find( request()->customer_id );
            $customer = ( $customer ) ? $customer : Customer::create( ["name" => request()->customer_id] );
            $data["customer_id"] = $customer->id ?? '';
            $data["customer_name"] = $customer->name ?? '';
        }

        $quotation->update($data);
        $quotation->payments()->delete();
        InvoiceHandler::addPayment($quotation, [
            "amount" => $quotation->amount_paid,
            "balance" => $quotation->amount_total - $quotation->amount_paid
        ]);


        $sales  = _array( _from($data, "sales") );

        if( count( $sales ) ) $quotation->update(["approved_by" => null]);
        foreach ($sales as $params) {

            if( _from($params, "product_id") ) {
                $unit = Unit::find(_from($params, "unit_id") );
                $sale = Sale::where("id", _from($params, "id") )->first();
                if( $unit ) {
                    $params['unit_name'] = $unit->name;
                    $params['unit_quantity'] = $unit->quantity;
                }


                // unset($params['created_by']);
                // unset($params['sales']);
                // unset($params['payments']);

                if ( $sale) {
                    $sale->update($params);
                } else {
                    $quotation->sales()->create($params);
                }
            }
        }

        return redirect()
            ->route('quotations.show', $quotation)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Quotation $quotation
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Quotation $quotation)
    {
        $this->authorize('delete', $quotation);

        $quotation->sales()->delete();
        $quotation->delete();

        return redirect()
            ->route('quotations.index')
            ->withSuccess(__('crud.common.removed'));
    }

    public function approve(Request $request, Quotation $quotation){
        $this->authorize('update', $quotation);

        if( $request->status_id == 13) {
            $quotation->update(['status_id' => $request->status_id ]);
        } else {
            ProductHandler::approveQuotation($quotation);
        }
        return redirect()->back()->withSuccess(__('crud.common.approved'));
    }

    /**
     * Convert quotation to invoice
     */
    public function convertToInvoice(Request $request, Quotation $quotation)
    {
        $this->authorize('update', $quotation);

        try {
            // Create invoice from quotation
            $invoiceData = [
                'customer_id' => $quotation->customer_id,
                'customer_name' => $quotation->customer_name,
                'sub_total' => $quotation->sub_total,
                'amount_total' => $quotation->amount_total,
                'amount_paid' => 0, // Reset payment for invoice
                'vat' => $quotation->vat,
                'discount' => $quotation->discount,
                'description' => $quotation->description,
                'reference_no' => 'QUO-' . $quotation->quotation_id,
                'status_id' => 10, // Draft status for invoice
                'created_by' => auth()->id(),
                'branch_id' => auth()->user()->branch_id,
                'business_type_id' => $quotation->business_type_id,
            ];

            $invoice = \App\Models\Invoice::create($invoiceData);

            // Copy sales items
            foreach ($quotation->sales as $sale) {
                $invoice->sales()->create([
                    'product_id' => $sale->product_id,
                    'product_name' => $sale->product_name,
                    'unit_id' => $sale->unit_id,
                    'unit_name' => $sale->unit_name,
                    'unit_quantity' => $sale->unit_quantity,
                    'quantity' => $sale->quantity,
                    'selling_price' => $sale->selling_price,
                    'buying_price' => $sale->buying_price,
                    'discount' => $sale->discount,
                    'vat' => $sale->vat,
                    'created_by' => auth()->id(),
                    'branch_id' => auth()->user()->branch_id,
                ]);
            }

            // Update quotation status to accepted
            $quotation->update(['status_id' => 12]);

            return redirect()
                ->route('invoices.show', $invoice)
                ->withSuccess('Quotation converted to invoice successfully.');

        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withError('Error converting quotation: ' . $e->getMessage());
        }
    }

    /**
     * Duplicate quotation
     */
    public function duplicate(Request $request, Quotation $quotation)
    {
        $this->authorize('create', Quotation::class);

        try {
            $newQuotationData = $quotation->toArray();
            unset($newQuotationData['id']);
            unset($newQuotationData['created_at']);
            unset($newQuotationData['updated_at']);
            unset($newQuotationData['deleted_at']);

            $newQuotationData['created_by'] = auth()->id();
            $newQuotationData['status_id'] = 10; // Draft status
            $newQuotationData['amount_paid'] = 0;
            $newQuotationData['approved_by'] = null;

            $newQuotation = Quotation::create($newQuotationData);

            // Copy sales items
            foreach ($quotation->sales as $sale) {
                $newSaleData = $sale->toArray();
                unset($newSaleData['id']);
                unset($newSaleData['created_at']);
                unset($newSaleData['updated_at']);
                unset($newSaleData['deleted_at']);

                $newSaleData['created_by'] = auth()->id();
                $newQuotation->sales()->create($newSaleData);
            }

            return redirect()
                ->route('quotations.edit', $newQuotation)
                ->withSuccess('Quotation duplicated successfully.');

        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withError('Error duplicating quotation: ' . $e->getMessage());
        }
    }

    public function ajaxQuotation(Request $request, Quotation $quotation){
        $quotation->payments;
        $quotation->sales;
        $quotation->customer;
        return response( $quotation );
    }

    public function importQuotation(Request $request){

        \Excel::import(new \App\Imports\ImportQuotation, $request->products);

        $quotations = Quotation::where("sub_total", 0)->get();

        foreach($quotations as $quotation){

            if( $quotation ) {

                $sub_total = $quotation->sales()->get()->sum("buying_amount");
                $discount = $quotation->sales()->get()->sum("discount");
                $amount_total = $sub_total - $discount;
                $data = [];
                $data["sub_total"] = $sub_total;
                $data["amount_total"] = $amount_total;
                $data["amount_paid"] = $amount_total;
                $data["discount"] = $discount;

                $quotation->update($data);
                $quotation->payments()->delete();
                InvoiceHandler::addPayment($quotation, [
                    "amount" => $quotation->amount_paid,
                    "balance" => $quotation->amount_total - $quotation->amount_paid
                ]);
            }

        }

        return redirect()
            ->back()
            ->withSuccess("Products Imported Successfully");
    }


    public function stockAdjustmentSave(QuotationStoreRequest $request) {
        $this->authorize('create', Quotation::class);
        ProductHandler::stockAdjustment();
        return redirect()->back()->withSuccess("Stock Adjusted Successfully.");
    }

    public function stockAdjustment(Request $request) {
        $this->authorize('create', Quotation::class);

        $users = User::pluck('name', 'id');
        $customers = Customer::pluck('name', 'id');
        $products = Product::with("unit", "units")->get();

        return view(
            'app.quotations.stock-adjustment',
            compact('users','customers', 'products')
        );
    }





}
