@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="bi bi-pencil-square me-2"></i>Edit Payroll
                        </h4>
                        <a href="{{ route('payroll.show', $payroll) }}" class="btn btn-light btn-sm">
                            <i class="bi bi-arrow-left me-1"></i>Back to View
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <h6><i class="bi bi-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('payroll.update', $payroll) }}" method="POST" id="payroll-form">
                        @csrf
                        @method('PUT')

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="bi bi-person me-2"></i>Employee Information</h6>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-borderless mb-0">
                                            <tr>
                                                <th width="30%">Name:</th>
                                                <td>{{ $payroll->employee->name ?? '-' }}</td>
                                            </tr>
                                            <tr>
                                                <th>Employee ID:</th>
                                                <td>{{ $payroll->employee->id ?? '-' }}</td>
                                            </tr>
                                            <tr>
                                                <th>Email:</th>
                                                <td>{{ $payroll->employee->email ?? '-' }}</td>
                                            </tr>
                                            <tr>
                                                <th>Period:</th>
                                                <td>{{ $payroll->date_from ? $payroll->date_from->format('F Y') : '-' }}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="bi bi-cash me-2"></i>Basic Pay</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="basic_pay" class="form-label">Basic Pay Amount (K)</label>
                                            <div class="input-group">
                                                <span class="input-group-text">K</span>
                                                <input type="number"
                                                       name="basic_pay"
                                                       id="basic_pay"
                                                       class="form-control @error('basic_pay') is-invalid @enderror"
                                                       value="{{ old('basic_pay', number_format($payroll->basic_pay, 2, '.', '')) }}"
                                                       step="0.01"
                                                       min="0"
                                                       placeholder="0.00"
                                                       required>
                                            </div>
                                            @error('basic_pay')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Enter the employee's basic monthly salary</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0"><i class="bi bi-plus-circle me-2"></i>Contributions (Earnings)</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm" id="contributions-table">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>Description</th>
                                                        <th width="120px">Amount (K)</th>
                                                        <th width="60px">Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($payroll->contributions as $index => $contribution)
                                                    <tr>
                                                        <td>
                                                            <input type="hidden" name="items[{{ $index }}][id]" value="{{ $contribution->id }}">
                                                            <input type="hidden" name="items[{{ $index }}][type]" value="CONTRIBUTION">
                                                            <input type="text"
                                                                   name="items[{{ $index }}][name]"
                                                                   class="form-control form-control-sm"
                                                                   value="{{ old('items.'.$index.'.name', $contribution->name) }}"
                                                                   placeholder="e.g., Housing Allowance"
                                                                   required>
                                                        </td>
                                                        <td>
                                                            <div class="input-group input-group-sm">
                                                                <input type="number"
                                                                       name="items[{{ $index }}][amount]"
                                                                       class="form-control"
                                                                       value="{{ old('items.'.$index.'.amount', number_format($contribution->amount, 2, '.', '')) }}"
                                                                       step="0.01"
                                                                       min="0"
                                                                       placeholder="0.00"
                                                                       required>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-outline-danger remove-row" title="Remove">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                                <tfoot>
                                                    <tr>
                                                        <td colspan="3" class="text-center py-2">
                                                            <button type="button" class="btn btn-sm btn-success" id="add-contribution">
                                                                <i class="bi bi-plus-circle me-1"></i>Add Contribution
                                                            </button>
                                                        </td>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                        <small class="text-muted">
                                            <i class="bi bi-info-circle me-1"></i>
                                            Contributions are added to basic pay (allowances, bonuses, etc.)
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-danger text-white">
                                        <h6 class="mb-0"><i class="bi bi-dash-circle me-2"></i>Deductions</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm" id="deductions-table">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>Description</th>
                                                        <th width="120px">Amount (K)</th>
                                                        <th width="60px">Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($payroll->deductions as $index => $deduction)
                                                    <tr>
                                                        <td>
                                                            <input type="hidden" name="items[{{ $index + count($payroll->contributions) }}][id]" value="{{ $deduction->id }}">
                                                            <input type="hidden" name="items[{{ $index + count($payroll->contributions) }}][type]" value="DEDUCTION">
                                                            <input type="text"
                                                                   name="items[{{ $index + count($payroll->contributions) }}][name]"
                                                                   class="form-control form-control-sm"
                                                                   value="{{ old('items.'.($index + count($payroll->contributions)).'.name', $deduction->name) }}"
                                                                   placeholder="e.g., Loan Repayment"
                                                                   required>
                                                        </td>
                                                        <td>
                                                            <div class="input-group input-group-sm">
                                                                <input type="number"
                                                                       name="items[{{ $index + count($payroll->contributions) }}][amount]"
                                                                       class="form-control"
                                                                       value="{{ old('items.'.($index + count($payroll->contributions)).'.amount', number_format($deduction->amount, 2, '.', '')) }}"
                                                                       step="0.01"
                                                                       min="0"
                                                                       placeholder="0.00"
                                                                       required>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-outline-danger remove-row" title="Remove">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                                <tfoot>
                                                    <tr>
                                                        <td colspan="3" class="text-center py-2">
                                                            <button type="button" class="btn btn-sm btn-danger" id="add-deduction">
                                                                <i class="bi bi-plus-circle me-1"></i>Add Deduction
                                                            </button>
                                                        </td>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                        <small class="text-muted">
                                            <i class="bi bi-info-circle me-1"></i>
                                            Deductions are subtracted from gross pay (loans, advances, etc.)
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-body bg-light">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-info-circle text-primary me-2"></i>
                                            <small class="text-muted">
                                                Changes will recalculate gross pay, tax, and net pay automatically.
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex justify-content-end gap-2">
                                            <a href="{{ route('payroll.show', $payroll) }}" class="btn btn-outline-secondary">
                                                <i class="bi bi-x-circle me-1"></i>Cancel
                                            </a>
                                            <button type="submit" class="btn btn-primary" id="save-btn">
                                                <i class="bi bi-check-circle me-1"></i>Save Changes
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get current counts
    let contributionCount = {{ count($payroll->contributions) }};
    let deductionCount = {{ count($payroll->deductions) }};
    let totalItemCount = contributionCount + deductionCount;

    // Function to get next available index
    function getNextIndex() {
        return totalItemCount++;
    }

    // Add contribution
    document.getElementById('add-contribution').addEventListener('click', function() {
        const index = getNextIndex();
        const html = `
            <tr>
                <td>
                    <input type="hidden" name="items[${index}][type]" value="CONTRIBUTION">
                    <input type="text"
                           name="items[${index}][name]"
                           class="form-control form-control-sm"
                           placeholder="e.g., Housing Allowance"
                           required>
                </td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="number"
                               name="items[${index}][amount]"
                               class="form-control"
                               value="0"
                               step="0.01"
                               min="0"
                               placeholder="0.00"
                               required>
                    </div>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-row" title="Remove">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        document.querySelector('#contributions-table tbody').insertAdjacentHTML('beforeend', html);
    });

    // Add deduction
    document.getElementById('add-deduction').addEventListener('click', function() {
        const index = getNextIndex();
        const html = `
            <tr>
                <td>
                    <input type="hidden" name="items[${index}][type]" value="DEDUCTION">
                    <input type="text"
                           name="items[${index}][name]"
                           class="form-control form-control-sm"
                           placeholder="e.g., Loan Repayment"
                           required>
                </td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="number"
                               name="items[${index}][amount]"
                               class="form-control"
                               value="0"
                               step="0.01"
                               min="0"
                               placeholder="0.00"
                               required>
                    </div>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-row" title="Remove">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        document.querySelector('#deductions-table tbody').insertAdjacentHTML('beforeend', html);
    });

    // Remove row (using event delegation)
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-row')) {
            e.preventDefault();
            const row = e.target.closest('tr');
            if (row) {
                // Add confirmation for existing items
                const hasId = row.querySelector('input[name*="[id]"]');
                if (hasId && hasId.value) {
                    if (confirm('Are you sure you want to remove this item? This action cannot be undone.')) {
                        row.remove();
                    }
                } else {
                    row.remove();
                }
            }
        }
    });

    // Form validation before submit
    document.querySelector('form').addEventListener('submit', function(e) {
        const contributionRows = document.querySelectorAll('#contributions-table tbody tr');
        const deductionRows = document.querySelectorAll('#deductions-table tbody tr');

        let hasError = false;

        // Validate contribution rows
        contributionRows.forEach(function(row) {
            const nameInput = row.querySelector('input[name*="[name]"]');
            const amountInput = row.querySelector('input[name*="[amount]"]');

            if (nameInput && nameInput.value.trim() === '') {
                nameInput.classList.add('is-invalid');
                hasError = true;
            } else if (nameInput) {
                nameInput.classList.remove('is-invalid');
            }

            if (amountInput && (amountInput.value === '' || parseFloat(amountInput.value) < 0)) {
                amountInput.classList.add('is-invalid');
                hasError = true;
            } else if (amountInput) {
                amountInput.classList.remove('is-invalid');
            }
        });

        // Validate deduction rows
        deductionRows.forEach(function(row) {
            const nameInput = row.querySelector('input[name*="[name]"]');
            const amountInput = row.querySelector('input[name*="[amount]"]');

            if (nameInput && nameInput.value.trim() === '') {
                nameInput.classList.add('is-invalid');
                hasError = true;
            } else if (nameInput) {
                nameInput.classList.remove('is-invalid');
            }

            if (amountInput && (amountInput.value === '' || parseFloat(amountInput.value) < 0)) {
                amountInput.classList.add('is-invalid');
                hasError = true;
            } else if (amountInput) {
                amountInput.classList.remove('is-invalid');
            }
        });

        if (hasError) {
            e.preventDefault();
            alert('Please fill in all required fields with valid values.');
        }
    });

    // Real-time validation
    document.addEventListener('input', function(e) {
        if (e.target.matches('input[name*="[name]"]')) {
            if (e.target.value.trim() !== '') {
                e.target.classList.remove('is-invalid');
            }
        }

        if (e.target.matches('input[name*="[amount]"]')) {
            if (e.target.value !== '' && parseFloat(e.target.value) >= 0) {
                e.target.classList.remove('is-invalid');
            }
        }
    });
});
</script>
@endpush
@endsection
