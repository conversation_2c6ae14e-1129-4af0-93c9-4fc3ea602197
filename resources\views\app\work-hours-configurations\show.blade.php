@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{{ route('work-hours-configurations.index') }}">Work Hours Configurations</a>
                        </li>
                        <li class="breadcrumb-item active">{{ $workHoursConfiguration->name }}</li>
                    </ol>
                </nav>
                <h1 class="page-header-title">{{ $workHoursConfiguration->name }}</h1>
                @if($workHoursConfiguration->is_active)
                    <span class="badge bg-success">Active Configuration</span>
                @endif
            </div>
            <div class="col-auto">
                <div class="d-flex gap-2">
                    <a href="{{ route('work-hours-configurations.edit', $workHoursConfiguration) }}" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> Edit
                    </a>
                    @if(!$workHoursConfiguration->is_active)
                    <form method="POST" action="{{ route('work-hours-configurations.activate', $workHoursConfiguration) }}" style="display: inline;">
                        @csrf
                        <button type="submit" class="btn btn-success"
                            onclick="return confirm('Activate this configuration? This will deactivate the current active configuration.')">
                            <i class="bi bi-check-circle"></i> Activate
                        </button>
                    </form>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Configuration Details</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Configuration Name</label>
                                <p class="fw-bold">{{ $workHoursConfiguration->name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <p>
                                    @if($workHoursConfiguration->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Work Hours</label>
                                <p class="fw-bold">
                                    {{ date('H:i', strtotime($workHoursConfiguration->start_time)) }} - 
                                    {{ date('H:i', strtotime($workHoursConfiguration->end_time)) }}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Standard Hours per Day</label>
                                <p class="fw-bold">{{ $workHoursConfiguration->standard_hours_per_day }} hours</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Overtime Rate</label>
                                <p class="fw-bold">{{ $workHoursConfiguration->overtime_rate }}x regular rate</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Created</label>
                                <p>{{ $workHoursConfiguration->created_at->format('M j, Y \a\t H:i') }}</p>
                            </div>
                        </div>
                    </div>

                    @if($workHoursConfiguration->description)
                    <div class="mb-3">
                        <label class="form-label text-muted">Description</label>
                        <p>{{ $workHoursConfiguration->description }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Configuration Summary</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="text-muted">Daily Work Window:</span>
                        <span class="fw-bold">
                            {{ \Carbon\Carbon::parse($workHoursConfiguration->start_time)->diffInHours(\Carbon\Carbon::parse($workHoursConfiguration->end_time)) }} hours
                        </span>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="text-muted">Standard Hours:</span>
                        <span class="fw-bold">{{ $workHoursConfiguration->standard_hours_per_day }}h</span>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="text-muted">Overtime Multiplier:</span>
                        <span class="fw-bold">{{ $workHoursConfiguration->overtime_rate }}x</span>
                    </div>
                    
                    <hr>
                    
                    <h6 class="mb-3">Example Calculations</h6>
                    
                    <div class="small text-muted mb-2">
                        <strong>8 hours worked:</strong><br>
                        Regular: 8h | Overtime: 0h
                    </div>
                    
                    <div class="small text-muted mb-2">
                        <strong>10 hours worked:</strong><br>
                        Regular: 8h | Overtime: 2h ({{ $workHoursConfiguration->overtime_rate }}x rate)
                    </div>
                    
                    <div class="small text-muted">
                        <strong>6 hours worked:</strong><br>
                        Regular: 6h | Overtime: 0h
                    </div>
                </div>
            </div>

            @if(!$workHoursConfiguration->is_active)
            <div class="card mt-3">
                <div class="card-body text-center">
                    <i class="bi bi-exclamation-triangle text-warning fs-1 mb-2"></i>
                    <h6>Inactive Configuration</h6>
                    <p class="text-muted small">
                        This configuration is not currently active. 
                        Activate it to use for overtime calculations.
                    </p>
                    <form method="POST" action="{{ route('work-hours-configurations.activate', $workHoursConfiguration) }}">
                        @csrf
                        <button type="submit" class="btn btn-success btn-sm"
                            onclick="return confirm('Activate this configuration?')">
                            <i class="bi bi-check-circle"></i> Activate Now
                        </button>
                    </form>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
