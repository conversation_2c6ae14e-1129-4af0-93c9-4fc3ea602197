<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class AccountingPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions for Chart of Accounts
        $this->createPermissions('account-types');
        $this->createPermissions('account-categories');
        $this->createPermissions('accounts');

        // Create permissions for General Ledger
        $this->createPermissions('fiscal-years');
        $this->createPermissions('fiscal-periods');
        $this->createPermissions('journal-entries');

        // Create permissions for Financial Reports
        $this->createPermissions('financial-reports');

        // Create permissions for Fixed Assets
        $this->createPermissions('asset-categories');
        $this->createPermissions('assets');
        $this->createPermissions('asset-depreciations');

        // Create permissions for Banking
        $this->createPermissions('bank-accounts');
        $this->createPermissions('bank-reconciliations');

        // Create permissions for Tax Management
        $this->createPermissions('tax-types');
        $this->createPermissions('tax-transactions');

        // Create permissions for Budgeting
        $this->createPermissions('budgets');

        // Create permissions for Manufacturing
        $this->createPermissions('boms');
        $this->createPermissions('production-orders');

        // Create permissions for Multi-Currency Support
        $this->createPermissions('currencies');
        $this->createPermissions('exchange-rates');

        // Assign permissions to roles
        $this->assignPermissionsToRoles();
    }

    /**
     * Create permissions for a resource.
     *
     * @param string $resource
     * @return void
     */
    private function createPermissions($resource)
    {
        // Create basic CRUD permissions
        Permission::create(['name' => "list {$resource}"]);
        Permission::create(['name' => "view {$resource}"]);
        Permission::create(['name' => "create {$resource}"]);
        Permission::create(['name' => "update {$resource}"]);
        Permission::create(['name' => "delete {$resource}"]);

        // Create additional permissions based on resource
        switch ($resource) {
            case 'journal-entries':
                Permission::create(['name' => "post {$resource}"]);
                Permission::create(['name' => "unpost {$resource}"]);
                break;
            case 'fiscal-years':
            case 'fiscal-periods':
                Permission::create(['name' => "close {$resource}"]);
                Permission::create(['name' => "reopen {$resource}"]);
                break;
            case 'assets':
                Permission::create(['name' => "dispose {$resource}"]);
                break;
            case 'bank-reconciliations':
                Permission::create(['name' => "complete {$resource}"]);
                break;
            case 'production-orders':
                Permission::create(['name' => "complete {$resource}"]);
                break;
        }
    }

    /**
     * Assign permissions to roles.
     *
     * @return void
     */
    private function assignPermissionsToRoles()
    {
        // Get or create roles
        $superAdmin = Role::firstOrCreate(['name' => 'super-admin']);
        $accountant = Role::firstOrCreate(['name' => 'accountant']);
        $financeManager = Role::firstOrCreate(['name' => 'finance-manager']);
        $assetManager = Role::firstOrCreate(['name' => 'asset-manager']);
        $taxManager = Role::firstOrCreate(['name' => 'tax-manager']);
        $budgetManager = Role::firstOrCreate(['name' => 'budget-manager']);
        $productionManager = Role::firstOrCreate(['name' => 'production-manager']);

        // Super Admin gets all permissions
        $superAdmin->givePermissionTo(Permission::all());

        // Accountant permissions
        $accountantPermissions = [
            'list accounts', 'view accounts', 'create accounts', 'update accounts',
            'list journal-entries', 'view journal-entries', 'create journal-entries', 'update journal-entries',
            'post journal-entries',
            'list financial-reports', 'view financial-reports',
            'list bank-accounts', 'view bank-accounts',
            'list bank-reconciliations', 'view bank-reconciliations', 'create bank-reconciliations', 'update bank-reconciliations',
            'complete bank-reconciliations',
            'list tax-transactions', 'view tax-transactions',
        ];
        $accountant->givePermissionTo($accountantPermissions);

        // Finance Manager permissions
        $financeManagerPermissions = [
            'list account-types', 'view account-types', 'create account-types', 'update account-types',
            'list account-categories', 'view account-categories', 'create account-categories', 'update account-categories',
            'list accounts', 'view accounts', 'create accounts', 'update accounts', 'delete accounts',
            'list fiscal-years', 'view fiscal-years', 'create fiscal-years', 'update fiscal-years',
            'close fiscal-years', 'reopen fiscal-years',
            'list fiscal-periods', 'view fiscal-periods', 'create fiscal-periods', 'update fiscal-periods',
            'close fiscal-periods', 'reopen fiscal-periods',
            'list journal-entries', 'view journal-entries', 'create journal-entries', 'update journal-entries', 'delete journal-entries',
            'post journal-entries', 'unpost journal-entries',
            'list financial-reports', 'view financial-reports',
            'list currencies', 'view currencies', 'create currencies', 'update currencies',
            'list exchange-rates', 'view exchange-rates', 'create exchange-rates', 'update exchange-rates',
            'list bank-accounts', 'view bank-accounts', 'create bank-accounts', 'update bank-accounts', 'delete bank-accounts',
            'list bank-reconciliations', 'view bank-reconciliations', 'create bank-reconciliations', 'update bank-reconciliations', 'delete bank-reconciliations',
            'complete bank-reconciliations',
        ];
        $financeManager->givePermissionTo($financeManagerPermissions);

        // Asset Manager permissions
        $assetManagerPermissions = [
            'list asset-categories', 'view asset-categories', 'create asset-categories', 'update asset-categories',
            'list assets', 'view assets', 'create assets', 'update assets', 'delete assets', 'dispose assets',
            'list asset-depreciations', 'view asset-depreciations', 'create asset-depreciations',
            'list journal-entries', 'view journal-entries',
            'list financial-reports', 'view financial-reports',
        ];
        $assetManager->givePermissionTo($assetManagerPermissions);

        // Tax Manager permissions
        $taxManagerPermissions = [
            'list tax-types', 'view tax-types', 'create tax-types', 'update tax-types', 'delete tax-types',
            'list tax-transactions', 'view tax-transactions',
            'list journal-entries', 'view journal-entries',
            'list financial-reports', 'view financial-reports',
        ];
        $taxManager->givePermissionTo($taxManagerPermissions);

        // Budget Manager permissions
        $budgetManagerPermissions = [
            'list budgets', 'view budgets', 'create budgets', 'update budgets', 'delete budgets',
            'list accounts', 'view accounts',
            'list financial-reports', 'view financial-reports',
        ];
        $budgetManager->givePermissionTo($budgetManagerPermissions);

        // Production Manager permissions
        $productionManagerPermissions = [
            'list boms', 'view boms', 'create boms', 'update boms', 'delete boms',
            'list production-orders', 'view production-orders', 'create production-orders', 'update production-orders', 'delete production-orders',
            'complete production-orders',
            'list journal-entries', 'view journal-entries',
        ];
        $productionManager->givePermissionTo($productionManagerPermissions);
    }
}
