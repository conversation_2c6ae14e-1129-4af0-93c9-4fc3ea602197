@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-eye text-primary"></i> Loan <PERSON>ails
            </h1>
            <p class="text-muted mb-0">{{ $employeeLoan->loan_reference }} - {{ $employeeLoan->employee->name }}</p>
        </div>
        <div>
            <a href="{{ route('employee-loans.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Loans
            </a>
            @can('update', $employeeLoan)
            @if($employeeLoan->status === 'pending')
            <a href="{{ route('employee-loans.edit', $employeeLoan) }}" class="btn btn-outline-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            @endif
            @endcan
        </div>
    </div>

    <div class="row">
        <!-- Left Column -->
        <div class="col-lg-8">
            <!-- Loan Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> Loan Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Loan Reference:</strong></td>
                                    <td>{{ $employeeLoan->loan_reference }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Employee:</strong></td>
                                    <td>{{ $employeeLoan->employee->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Loan Amount:</strong></td>
                                    <td><strong class="text-success">K{{ number_format($employeeLoan->loan_amount, 2) }}</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>Monthly Installment:</strong></td>
                                    <td>K{{ number_format($employeeLoan->installment_amount, 2) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Installments:</strong></td>
                                    <td>{{ $employeeLoan->total_installments }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Application Date:</strong></td>
                                    <td>{{ $employeeLoan->loan_date->format('M d, Y') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>First Deduction:</strong></td>
                                    <td>{{ $employeeLoan->first_deduction_date->format('M d, Y') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge bg-{{ 
                                            $employeeLoan->status === 'pending' ? 'warning' : 
                                            ($employeeLoan->status === 'approved' ? 'info' : 
                                            ($employeeLoan->status === 'active' ? 'primary' : 
                                            ($employeeLoan->status === 'completed' ? 'success' : 'danger'))) 
                                        }}">
                                            {{ ucfirst($employeeLoan->status) }}
                                        </span>
                                    </td>
                                </tr>
                                @if($employeeLoan->interest_rate > 0)
                                <tr>
                                    <td><strong>Interest Rate:</strong></td>
                                    <td>{{ $employeeLoan->interest_rate }}% ({{ ucfirst($employeeLoan->interest_type) }})</td>
                                </tr>
                                @endif
                                @if($employeeLoan->expected_completion_date)
                                <tr>
                                    <td><strong>Expected Completion:</strong></td>
                                    <td>{{ $employeeLoan->expected_completion_date->format('M d, Y') }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>
                    </div>

                    @if($employeeLoan->purpose)
                    <div class="mt-3">
                        <h6><strong>Loan Purpose:</strong></h6>
                        <p class="text-muted">{{ $employeeLoan->purpose }}</p>
                    </div>
                    @endif

                    @if($employeeLoan->terms_conditions)
                    <div class="mt-3">
                        <h6><strong>Terms and Conditions:</strong></h6>
                        <p class="text-muted">{{ $employeeLoan->terms_conditions }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Payment Progress -->
            @if(in_array($employeeLoan->status, ['active', 'completed']))
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line"></i> Payment Progress
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Total Paid:</strong></td>
                                    <td><strong class="text-success">K{{ number_format($employeeLoan->total_paid, 2) }}</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>Remaining Balance:</strong></td>
                                    <td><strong class="text-warning">K{{ number_format($employeeLoan->remaining_balance, 2) }}</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>Installments Paid:</strong></td>
                                    <td>{{ $employeeLoan->installments_paid }} of {{ $employeeLoan->total_installments }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Progress:</strong></td>
                                    <td>{{ $employeeLoan->progress_percentage }}%</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label"><strong>Payment Progress</strong></label>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-{{ $employeeLoan->status === 'completed' ? 'success' : 'primary' }}" 
                                         style="width: {{ $employeeLoan->progress_percentage }}%">
                                        {{ $employeeLoan->progress_percentage }}%
                                    </div>
                                </div>
                            </div>
                            @if($employeeLoan->status === 'completed' && $employeeLoan->actual_completion_date)
                            <p class="text-success">
                                <i class="fas fa-check-circle"></i> 
                                Completed on {{ $employeeLoan->actual_completion_date->format('M d, Y') }}
                            </p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Approval Information -->
            @if($employeeLoan->approved_at && $employeeLoan->approvedBy)
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-check-circle"></i> Approval Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Approved By:</strong></td>
                                    <td>{{ $employeeLoan->approvedBy->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Approval Date:</strong></td>
                                    <td>{{ $employeeLoan->approved_at->format('M d, Y \a\t H:i:s') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            @if($employeeLoan->approval_notes)
                            <div>
                                <h6><strong>Approval Notes:</strong></h6>
                                <p class="text-muted">{{ $employeeLoan->approval_notes }}</p>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Right Column -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs"></i> Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @can('approve', $employeeLoan)
                        @if($employeeLoan->status === 'pending')
                        <button type="button" class="btn btn-success" onclick="approveLoan()">
                            <i class="fas fa-check"></i> Approve Loan
                        </button>
                        @endif
                        @endcan

                        @can('cancel', $employeeLoan)
                        @if(in_array($employeeLoan->status, ['pending', 'approved', 'active']))
                        <button type="button" class="btn btn-danger" onclick="cancelLoan()">
                            <i class="fas fa-times"></i> Cancel Loan
                        </button>
                        @endif
                        @endcan

                        @can('update', $employeeLoan)
                        @if($employeeLoan->status === 'pending')
                        <a href="{{ route('employee-loans.edit', $employeeLoan) }}" class="btn btn-outline-primary">
                            <i class="fas fa-edit"></i> Edit Loan
                        </a>
                        @endif
                        @endcan

                        <button type="button" class="btn btn-outline-info" onclick="printLoan()">
                            <i class="fas fa-print"></i> Print Details
                        </button>
                    </div>
                </div>
            </div>

            <!-- Employee Information -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user"></i> Employee Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar avatar-lg mx-auto mb-2">
                            <span class="avatar-initial bg-primary rounded-circle fs-2">
                                {{ substr($employeeLoan->employee->name, 0, 1) }}
                            </span>
                        </div>
                        <h6 class="mb-1">{{ $employeeLoan->employee->name }}</h6>
                        @if($employeeLoan->employee->email)
                        <p class="text-muted small">{{ $employeeLoan->employee->email }}</p>
                        @endif
                    </div>
                    
                    <table class="table table-borderless table-sm">
                        @if($employeeLoan->employee->employee_id)
                        <tr>
                            <td><strong>Employee ID:</strong></td>
                            <td>{{ $employeeLoan->employee->employee_id }}</td>
                        </tr>
                        @endif
                        @if($employeeLoan->employee->position)
                        <tr>
                            <td><strong>Position:</strong></td>
                            <td>{{ $employeeLoan->employee->position }}</td>
                        </tr>
                        @endif
                        @if($employeeLoan->employee->department)
                        <tr>
                            <td><strong>Department:</strong></td>
                            <td>{{ $employeeLoan->employee->department }}</td>
                        </tr>
                        @endif
                        <tr>
                            <td><strong>Basic Pay:</strong></td>
                            <td>K{{ number_format($employeeLoan->employee->basic_pay, 2) }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Approve Loan Application</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('employee-loans.approve', $employeeLoan) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="approval_notes" class="form-label">Approval Notes (Optional)</label>
                        <textarea class="form-control" id="approval_notes" name="approval_notes" rows="3"
                                  placeholder="Add any notes about this approval..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> Approve Loan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Cancel Modal -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Loan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('employee-loans.cancel', $employeeLoan) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Are you sure you want to cancel this loan? This action cannot be undone.
                    </div>
                    <div class="mb-3">
                        <label for="cancellation_reason" class="form-label">Cancellation Reason <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="cancellation_reason" name="cancellation_reason" rows="3"
                                  placeholder="Please provide a reason for cancelling this loan..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Keep Loan</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times"></i> Cancel Loan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function approveLoan() {
    const modal = new bootstrap.Modal(document.getElementById('approvalModal'));
    modal.show();
}

function cancelLoan() {
    const modal = new bootstrap.Modal(document.getElementById('cancelModal'));
    modal.show();
}

function printLoan() {
    window.print();
}
</script>
@endpush
@endsection
