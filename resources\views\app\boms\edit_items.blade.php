@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">
                <a href="{{ route('boms.show', $bom) }}" class="mr-4"
                    ><i class="icon ion-md-arrow-back"></i
                ></a>
                Edit BOM Items
            </h4>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="mb-4">
                        <h5>BOM Number</h5>
                        <span>{{ $bom->bom_number }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Name</h5>
                        <span>{{ $bom->name }}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-4">
                        <h5>Product</h5>
                        <span>{{ optional($bom->product)->name }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Quantity</h5>
                        <span>{{ number_format($bom->quantity, 2) }} {{ $bom->unit_of_measure }}</span>
                    </div>
                </div>
            </div>

            <hr>

            <form action="{{ route('boms.update-items', $bom) }}" method="POST" id="bom-items-form">
                @csrf
                
                <div class="mb-3">
                    <button type="button" class="btn btn-success" id="add-item-btn">
                        <i class="icon ion-md-add"></i> Add Item
                    </button>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-bordered" id="items-table">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Quantity</th>
                                <th>Unit</th>
                                <th>Cost Per Unit</th>
                                <th>Total Cost</th>
                                <th>Type</th>
                                <th>Scrap</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($bomItems as $index => $item)
                            <tr class="item-row">
                                <td>
                                    <input type="hidden" name="items[{{ $index }}][id]" value="{{ $item->id }}">
                                    <select name="items[{{ $index }}][product_id]" class="form-control product-select" required>
                                        <option value="">Select Product</option>
                                        @foreach($products as $id => $name)
                                            <option value="{{ $id }}" {{ $item->product_id == $id ? 'selected' : '' }}>
                                                {{ $name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </td>
                                <td>
                                    <input type="number" name="items[{{ $index }}][quantity]" class="form-control quantity-input" 
                                        value="{{ $item->quantity }}" min="0.01" step="0.01" required>
                                </td>
                                <td>
                                    <div class="input-group">
                                        <select name="items[{{ $index }}][unit_id]" class="form-control">
                                            <option value="">Select Unit</option>
                                            @foreach($units as $id => $name)
                                                <option value="{{ $id }}" {{ $item->unit_id == $id ? 'selected' : '' }}>
                                                    {{ $name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <input type="text" name="items[{{ $index }}][unit_of_measure]" class="form-control" 
                                            value="{{ $item->unit_of_measure }}" placeholder="UOM" required>
                                    </div>
                                </td>
                                <td>
                                    <input type="number" name="items[{{ $index }}][cost_per_unit]" class="form-control cost-input" 
                                        value="{{ $item->cost_per_unit }}" min="0" step="0.01" required>
                                </td>
                                <td>
                                    <input type="number" name="items[{{ $index }}][total_cost]" class="form-control total-cost" 
                                        value="{{ $item->total_cost }}" readonly>
                                </td>
                                <td>
                                    <select name="items[{{ $index }}][item_type]" class="form-control" required>
                                        <option value="material" {{ $item->item_type == 'material' ? 'selected' : '' }}>Material</option>
                                        <option value="labor" {{ $item->item_type == 'labor' ? 'selected' : '' }}>Labor</option>
                                        <option value="overhead" {{ $item->item_type == 'overhead' ? 'selected' : '' }}>Overhead</option>
                                    </select>
                                </td>
                                <td>
                                    <div class="form-check">
                                        <input type="checkbox" name="items[{{ $index }}][is_scrap]" class="form-check-input scrap-check" 
                                            value="1" {{ $item->is_scrap ? 'checked' : '' }}>
                                        <input type="number" name="items[{{ $index }}][scrap_percentage]" class="form-control scrap-percentage" 
                                            value="{{ $item->scrap_percentage }}" min="0" max="100" step="0.01" 
                                            {{ $item->is_scrap ? '' : 'disabled' }}>
                                    </div>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-danger remove-item-btn">
                                        <i class="icon ion-md-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="4" class="text-right">Total:</th>
                                <th id="grand-total">{{ number_format($bom->total_cost, 2) }}</th>
                                <th colspan="3"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                
                <div class="mt-4">
                    <a href="{{ route('boms.show', $bom) }}" class="btn btn-light">
                        <i class="icon ion-md-return-left text-primary"></i>
                        Cancel
                    </a>
                    
                    <button type="submit" class="btn btn-primary float-right">
                        <i class="icon ion-md-save"></i>
                        Save Items
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Item Template (Hidden) -->
<template id="item-template">
    <tr class="item-row">
        <td>
            <input type="hidden" name="items[INDEX][id]" value="">
            <select name="items[INDEX][product_id]" class="form-control product-select" required>
                <option value="">Select Product</option>
                @foreach($products as $id => $name)
                    <option value="{{ $id }}">{{ $name }}</option>
                @endforeach
            </select>
        </td>
        <td>
            <input type="number" name="items[INDEX][quantity]" class="form-control quantity-input" 
                value="1" min="0.01" step="0.01" required>
        </td>
        <td>
            <div class="input-group">
                <select name="items[INDEX][unit_id]" class="form-control">
                    <option value="">Select Unit</option>
                    @foreach($units as $id => $name)
                        <option value="{{ $id }}">{{ $name }}</option>
                    @endforeach
                </select>
                <input type="text" name="items[INDEX][unit_of_measure]" class="form-control" 
                    value="unit" placeholder="UOM" required>
            </div>
        </td>
        <td>
            <input type="number" name="items[INDEX][cost_per_unit]" class="form-control cost-input" 
                value="0" min="0" step="0.01" required>
        </td>
        <td>
            <input type="number" name="items[INDEX][total_cost]" class="form-control total-cost" 
                value="0" readonly>
        </td>
        <td>
            <select name="items[INDEX][item_type]" class="form-control" required>
                <option value="material" selected>Material</option>
                <option value="labor">Labor</option>
                <option value="overhead">Overhead</option>
            </select>
        </td>
        <td>
            <div class="form-check">
                <input type="checkbox" name="items[INDEX][is_scrap]" class="form-check-input scrap-check" value="1">
                <input type="number" name="items[INDEX][scrap_percentage]" class="form-control scrap-percentage" 
                    value="0" min="0" max="100" step="0.01" disabled>
            </div>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-danger remove-item-btn">
                <i class="icon ion-md-trash"></i>
            </button>
        </td>
    </tr>
</template>

@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Add new item
        $('#add-item-btn').click(function() {
            const template = document.getElementById('item-template').innerHTML;
            const index = $('.item-row').length;
            const newRow = template.replace(/INDEX/g, index);
            $('#items-table tbody').append(newRow);
            
            // Initialize the new row's events
            initRowEvents($('#items-table tbody tr:last'));
            
            // Update totals
            updateTotals();
        });
        
        // Initialize existing rows
        $('.item-row').each(function() {
            initRowEvents($(this));
        });
        
        // Initialize row events
        function initRowEvents(row) {
            // Remove item
            row.find('.remove-item-btn').click(function() {
                $(this).closest('tr').remove();
                updateTotals();
                reindexRows();
            });
            
            // Calculate total cost
            row.find('.quantity-input, .cost-input').on('input', function() {
                calculateRowTotal(row);
                updateTotals();
            });
            
            // Toggle scrap percentage
            row.find('.scrap-check').change(function() {
                const scrapPercentage = $(this).closest('td').find('.scrap-percentage');
                scrapPercentage.prop('disabled', !this.checked);
            });
        }
        
        // Calculate row total
        function calculateRowTotal(row) {
            const quantity = parseFloat(row.find('.quantity-input').val()) || 0;
            const costPerUnit = parseFloat(row.find('.cost-input').val()) || 0;
            const totalCost = quantity * costPerUnit;
            row.find('.total-cost').val(totalCost.toFixed(2));
        }
        
        // Update grand total
        function updateTotals() {
            let grandTotal = 0;
            $('.total-cost').each(function() {
                grandTotal += parseFloat($(this).val()) || 0;
            });
            $('#grand-total').text(grandTotal.toFixed(2));
        }
        
        // Reindex rows after deletion
        function reindexRows() {
            $('.item-row').each(function(index) {
                const row = $(this);
                row.find('input, select').each(function() {
                    const name = $(this).attr('name');
                    if (name) {
                        const newName = name.replace(/items\[\d+\]/, `items[${index}]`);
                        $(this).attr('name', newName);
                    }
                });
            });
        }
    });
</script>
@endpush
