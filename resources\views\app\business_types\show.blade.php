@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">building</x-slot>
        <x-slot name="title">{{ $businessType->name }}</x-slot>
        <x-slot name="subtitle">@lang('crud.business_types.business_type_details')</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('business-types.index') }}">@lang('crud.business_types.name')</a></li>
            <li class="breadcrumb-item active" aria-current="page">View</li>
        </x-slot>
        <x-slot name="controls">
            @can('update', $businessType)
            <a href="{{ route('business-types.edit', $businessType) }}" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> @lang('crud.business_types.edit_business_type')
            </a>
            @endcan
            <a href="{{ route('business-types.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> @lang('crud.business_types.back_to_business_types')
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <div class="col-lg-8 mb-3 mb-lg-0">
            <!-- Card -->
            <div class="card mb-3 mb-lg-5">
                <!-- Header -->
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-building me-2"></i>Business Type Overview
                    </h5>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6 mb-4">
                            <h6 class="text-cap">@lang('crud.business_types.inputs.name')</h6>
                            <span class="fs-5">{{ $businessType->name ?? '-' }}</span>
                        </div>

                        <div class="col-sm-6 mb-4">
                            <h6 class="text-cap">@lang('crud.business_types.inputs.status_id')</h6>
                            @if($businessType->status)
                                <span class="badge bg-{{ $businessType->status->id == 1 ? 'success' : 'secondary' }}">
                                    {{ $businessType->status->name }}
                                </span>
                            @else
                                <span class="text-muted">-</span>
                            @endif
                        </div>

                        <div class="col-sm-12 mb-4">
                            <h6 class="text-cap">@lang('crud.business_types.inputs.description')</h6>
                            <p class="mb-0">{{ $businessType->description ?? 'No description provided.' }}</p>
                        </div>
                    </div>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>

        <div class="col-lg-4">
            <!-- Card -->
            <div class="card">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Actions</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="d-grid gap-3">
                        @can('update', $businessType)
                        <a href="{{ route('business-types.edit', $businessType) }}" class="btn btn-primary">
                            <i class="bi-pencil-square me-1"></i> Edit Business Type
                        </a>
                        @endcan

                        @can('create', App\Models\BusinessType::class)
                        <a href="{{ route('business-types.create') }}" class="btn btn-outline-primary">
                            <i class="bi-plus-circle me-1"></i> @lang('crud.common.create') New
                        </a>
                        @endcan
                    </div>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->

            <!-- Card -->
            <div class="card mt-3">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Information</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <ul class="list-unstyled list-py-2 mb-0">
                        <li class="d-flex justify-content-between">
                            <span>ID:</span>
                            <span class="text-muted">#{{ $businessType->id }}</span>
                        </li>
                        <li class="d-flex justify-content-between">
                            <span>Created:</span>
                            <span class="text-muted">{{ $businessType->created_at->format('M d, Y') }}</span>
                        </li>
                        <li class="d-flex justify-content-between">
                            <span>Updated:</span>
                            <span class="text-muted">{{ $businessType->updated_at->format('M d, Y') }}</span>
                        </li>
                        @if($businessType->createdBy)
                        <li class="d-flex justify-content-between">
                            <span>Created by:</span>
                            <span class="text-muted">{{ $businessType->createdBy->name }}</span>
                        </li>
                        @endif
                        @if($businessType->updatedBy)
                        <li class="d-flex justify-content-between">
                            <span>Updated by:</span>
                            <span class="text-muted">{{ $businessType->updatedBy->name }}</span>
                        </li>
                        @endif
                    </ul>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>
    </div>
</div>
@endsection
