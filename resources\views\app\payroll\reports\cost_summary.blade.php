@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title mb-0">
                Employee Cost Summary Report - {{ $monthName }} {{ $year }}
            </h4>
            <div>
                <form action="{{ route('payroll-reports.cost-summary') }}" method="POST" class="d-inline">
                    @csrf
                    <input type="hidden" name="month" value="{{ $month }}">
                    <input type="hidden" name="year" value="{{ $year }}">
                    <input type="hidden" name="format" value="pdf">
                    <button type="submit" class="btn btn-sm btn-secondary">
                        <i class="bi bi-file-earmark-pdf"></i> PDF
                    </button>
                </form>
                <form action="{{ route('payroll-reports.cost-summary') }}" method="POST" class="d-inline">
                    @csrf
                    <input type="hidden" name="month" value="{{ $month }}">
                    <input type="hidden" name="year" value="{{ $year }}">
                    <input type="hidden" name="format" value="csv">
                    <button type="submit" class="btn btn-sm btn-secondary">
                        <i class="bi bi-file-earmark-excel"></i> CSV
                    </button>
                </form>
                <a href="{{ route('payroll-reports.cost-summary-form') }}" class="btn btn-sm btn-light">
                    <i class="bi bi-arrow-left"></i> Back
                </a>
            </div>
        </div>

        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Employee</th>
                            <th>Basic Pay</th>
                            <th>Employer Contributions</th>
                            <th>Total Cost</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($employeeCosts as $cost)
                        <tr>
                            <td>{{ $cost['employee']->name }}</td>
                            <td class="text-right">{{ number_format($cost['basic_pay'], 2) }}</td>
                            <td class="text-right">{{ number_format($cost['contributions'], 2) }}</td>
                            <td class="text-right">{{ number_format($cost['total_cost'], 2) }}</td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="4" class="text-center">No employee cost data found for this period.</td>
                        </tr>
                        @endforelse
                    </tbody>
                    <tfoot>
                        <tr>
                            <th>Total</th>
                            <th class="text-right">{{ number_format(array_sum(array_column($employeeCosts, 'basic_pay')), 2) }}</th>
                            <th class="text-right">{{ number_format(array_sum(array_column($employeeCosts, 'contributions')), 2) }}</th>
                            <th class="text-right">{{ number_format($totalCost, 2) }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class="mt-4">
                <h5>Cost Analysis</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Cost Breakdown</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>Basic Pay</th>
                                            <td class="text-right">{{ number_format(array_sum(array_column($employeeCosts, 'basic_pay')), 2) }}</td>
                                            <td class="text-right">{{ number_format(array_sum(array_column($employeeCosts, 'basic_pay')) / $totalCost * 100, 2) }}%</td>
                                        </tr>
                                        <tr>
                                            <th>Employer Contributions</th>
                                            <td class="text-right">{{ number_format(array_sum(array_column($employeeCosts, 'contributions')), 2) }}</td>
                                            <td class="text-right">{{ number_format(array_sum(array_column($employeeCosts, 'contributions')) / $totalCost * 100, 2) }}%</td>
                                        </tr>
                                        <tr>
                                            <th>Total</th>
                                            <td class="text-right">{{ number_format($totalCost, 2) }}</td>
                                            <td class="text-right">100.00%</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Average Cost per Employee</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>Average Basic Pay</th>
                                            <td class="text-right">{{ number_format(count($employeeCosts) > 0 ? array_sum(array_column($employeeCosts, 'basic_pay')) / count($employeeCosts) : 0, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <th>Average Contributions</th>
                                            <td class="text-right">{{ number_format(count($employeeCosts) > 0 ? array_sum(array_column($employeeCosts, 'contributions')) / count($employeeCosts) : 0, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <th>Average Total Cost</th>
                                            <td class="text-right">{{ number_format(count($employeeCosts) > 0 ? $totalCost / count($employeeCosts) : 0, 2) }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
