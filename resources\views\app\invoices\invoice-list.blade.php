<!-- List Group -->
<ul class="list-group list-group-flush list-group-no-gutters">
  <!-- List Items -->
  @forelse($invoices as $invoice)
  <li class="list-group-item">
    <div class="row align-items-center">
      <div class="col-md-8">
        <div class="mb-2">
          <div class="d-flex align-items-center">
            <div class="flex-shrink-0">
              <div class="avatar avatar-sm avatar-circle">
                <span class="avatar-initials bg-soft-primary text-primary">
                  {{ substr($invoice->customer->name ?? 'C', 0, 1) }}
                </span>
              </div>
            </div>
            <div class="flex-grow-1 ms-3">
              <h5 class="mb-0">Invoice #{{ $invoice->invoice_number ?? $invoice->id }}</h5>
              <span class="d-block text-body">{{ $invoice->customer->name ?? 'Customer' }}</span>
            </div>
          </div>
        </div>
        
        <div class="d-flex flex-wrap">
          <div class="me-3">
            <span class="text-body fs-6">Items:</span>
            <span class="fw-semibold">{{ count($invoice->items ?? []) }}</span>
          </div>
          <div class="me-3">
            <span class="text-body fs-6">Date:</span>
            <span class="fw-semibold">{{ $invoice->created_at->format('M d, Y') }}</span>
          </div>
          <div>
            <span class="text-body fs-6">Status:</span>
            <span class="badge bg-success">{{ $invoice->status }}</span>
          </div>
        </div>
      </div>
      
      <div class="col-md-4 text-md-end mt-3 mt-md-0">
        <div class="mb-2">
          <span class="h4 text-primary">{{ _money($invoice->total_amount) }}</span>
        </div>
        
        <div class="btn-group">
          <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-white btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="View Invoice">
            <i class="bi-eye"></i>
          </a>
          
          @can('update', $invoice)
          <a href="{{ route('invoices.edit', $invoice) }}" class="btn btn-white btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit Invoice">
            <i class="bi-pencil"></i>
          </a>
          @endcan
          
          @can('delete', $invoice)
          <div class="btn-group">
            <button type="button" class="btn btn-white btn-icon btn-sm dropdown-toggle dropdown-toggle-empty" id="invoiceDropdown{{ $invoice->id }}" data-bs-toggle="dropdown" aria-expanded="false"></button>
            <div class="dropdown-menu dropdown-menu-end mt-1" aria-labelledby="invoiceDropdown{{ $invoice->id }}">
              <form action="{{ route('invoices.destroy', $invoice) }}" method="POST" onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')">
                @csrf @method('DELETE')
                <button type="submit" class="dropdown-item text-danger">
                  <i class="bi-trash dropdown-item-icon"></i> Delete
                </button>
              </form>
            </div>
          </div>
          @endcan
        </div>
      </div>
    </div>
  </li>
  @empty
  <li class="list-group-item py-5">
    <div class="d-flex flex-column align-items-center justify-content-center">
      <i class="bi bi-receipt text-muted" style="font-size: 3rem;"></i>
      <h5 class="mt-4">No invoices found</h5>
      <p class="text-muted">No invoices match your current filters</p>
      @can('create', App\Models\Invoice::class)
      <a href="{{ route('invoices.create') }}" class="btn btn-primary mt-2">
        <i class="bi bi-plus-circle me-1"></i> Create New Invoice
      </a>
      @endcan
    </div>
  </li>
  @endforelse
  <!-- End List Items -->
</ul>
<!-- End List Group -->