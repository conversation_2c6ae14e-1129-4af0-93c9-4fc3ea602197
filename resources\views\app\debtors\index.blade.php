@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">Debtors</x-slot>
        <x-slot name="controls">
            
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="searchbar mt-4 mb-5">
        <form class="d-flex" id="filter">

          <div class="me-3">
            <label class="form-label">Type</label>
            <div class="tom-select-custom _200px">
              <select class="js-select form-select branch-filter" name="type" autocomplete="off" data-hs-tom-select-options='{
                "placeholder": "Select a Debtor Type..."
              }'>
              <option value="0">Both Customers/Suppliers</option>
                <option value="customers" @if('customers' == request()->type ) selected @endif> Customers </option>
                <option value="suppliers" @if('suppliers' == request()->type ) selected @endif> Suppliers </option>
              </select>
            </div>
          </div>

          <div class="me-3 _200px">
            <input type="hidden" name="from" value="{{ request()->from }}">
            <input type="hidden" name="to" value="{{ request()->to }}">
            <label class="form-label">Dates</label>
            <button id="js-daterangepicker-predefined" type="button" class="btn btn-white _100">
              <i class="bi-calendar-week me-1"></i>
              <span class="js-daterangepicker-predefined-preview"></span>
            </button>
          </div>


          <div class="me-3">
            <label class="form-label">Branch</label>
              <div class="tom-select-custom _200px">
                <select class="js-select form-select branch-filter" name="branch_id" autocomplete="off" data-hs-tom-select-options='{
                  "placeholder": "Select a branch..."
                }'>
                <option></option>
                @foreach( App\Models\Branch::get() as $branch)
                  <option value="{{ $branch->id }}" @if($branch->id == request()->branch_id ) selected @endif>
                    {{ $branch->name }}
                  </option>
                @endforeach
                </select>
              </div>
          </div>

          <div class="me-3">
            <label class="form-label">Category</label>
            <div class="tom-select-custom _200px">
              <select class="js-select form-select category-filter" name="category" autocomplete="off" data-hs-tom-select-options='{
                "placeholder": "Select a category..."
              }'>
              <option value="0">All Categories</option>
              @foreach( App\Models\Category::where("applied_to", "debtors")->get() as $category)
                <option value="{{ $category->name }}" @if($category->name == request()->category ) selected @endif>
                  {{ $category->name }}
                </option>
              @endforeach
              </select>
            </div>
          </div>


          <div class="">
              <label class="form-label transparent">_</label>
              <div class="input-group-append">
                  <button type="submit" class="btn btn-primary">
                      Search
                  </button>
              </div>
          </div>


        </form>
    </div>


    <!-- Table -->
    <div class="card card-table">
        <div class="table-responsive mt-3">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">

                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            #
                        </th>                      
                        <th class="text-left">
                            Operator
                        </th>                                         
                        <th class="text-left">
                            Date
                        </th>                 
                        <th class="text-left">
                            Reference Name
                        </th>
                        <th class="text-left">
                            Branch
                        </th>
                        <th class="text-left">
                            Category
                        </th>
                        <th class="text-left">
                            Total Amount
                        </th>                      
                        <th class="text-left">
                            Balance
                        </th>                            
                        <th class="text-left">
                            Description
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($debtors as $key => $debtor)
                    <tr>
                        <td>{{ $key + 1 ?? '-' }}</td>
                        <td>{{ $debtor->createdBy->name ?? '-' }}</td>
                        <td>{{ $debtor->created_at ?? '-' }}</td>
                        <td>{{ $debtor->debtable->name ?? '-' }}</td>
                        <td>{{ $debtor->branch->name ?? '-' }}</td>
                        <td>{{ $debtor->category ?? '-' }}</td>
                        <td>
                          {{ $debtor->amount_total ?? '-' }}
                        </td>
                        <td>
                          {{ $debtor->balance ?? '-' }}
                        </td>
                        <td>
                            {{ $debtor->description ?? '-' }}
                            @can('update', $debtor)
                            <a href="{{ route('debts.show', $debtor) }}">
                                <button type="button" class="btn btn-light mr-2">
                                    Details
                                </button>
                            </a>
                            @endcan 
                        </td>
                    </tr>
                    @empty
                    @endforelse

                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td>
                            <span class="h4 text-info"> {{ _money( $debtors->sum('amount_total') ) }} </span>
                        </td>                    
                        <td>
                            <span class="h4 text-info"> {{ _money( $debtors->sum('amount_total') - $debtors->sum('amount_paid') ) }} </span>
                        </td>
                        <td></td>
                    </tr>

                </tbody>

            </table>

        </div>
    </div>



</div>
@endsection





@push('scripts')
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
   dataTableBtn()
  });
</script>


@endpush