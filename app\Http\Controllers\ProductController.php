<?php

namespace App\Http\Controllers;

use App\Models\Unit;
use App\Models\Status;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use App\Http\Requests\ProductStoreRequest;
use App\Http\Requests\ProductUpdateRequest;

use Facades\App\Libraries\ProductHandler;

class ProductController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Product::class);

        $products = Product::search($request->search)->latest()->get();

        return view('app.products.index', compact('products') );
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Product::class);

        $units = Unit::pluck('name', 'id');
        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');
        $categories = Category::where("applied_to", "products")->pluck('name', 'id');

        return view(
            'app.products.create',
            compact('units', 'statuses', 'categories')
        );
    }

    /**
     * @param \App\Http\Requests\ProductStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(ProductStoreRequest $request)
    {
        $this->authorize('create', Product::class);

        $validated = $request->validated();

        $product = Product::create($validated);

        ProductHandler::settingUnits($product);

        if(request()->image) {
            $product->clearMediaCollection('image');
            // foreach (request()->image as $file) {
            if(request()->image) $product->addMedia(request()->image)->toMediaCollection("image");
            // }
        }

        return redirect()
            ->route('products.edit', $product)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product $product
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Product $product)
    {
        $this->authorize('view', $product);

        return view('app.products.show', compact('product'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product $product
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Product $product)
    {
        $this->authorize('update', $product);

        $units = Unit::where("product_id",$product->id)->pluck('name', 'id');
        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');
        $categories = Category::where("applied_to", "products")->pluck('name', 'id');

        return view(
            'app.products.edit',
            compact('product', 'units', 'statuses', 'categories')
        );
    }

    /**
     * @param \App\Http\Requests\ProductUpdateRequest $request
     * @param \App\Models\Product $product
     * @return \Illuminate\Http\Response
     */
    public function update(ProductUpdateRequest $request, Product $product)
    {
        $this->authorize('update', $product);

        $validated = $request->validated();

        $product->update($validated);

        ProductHandler::settingUnits($product);

        // $product->stockItems()->whereNull("invoice_id")->update( ["selling_price" => $product->selling_price ]);

        if(request()->image) {
            $product->clearMediaCollection('image');
            // foreach (request()->image as $file) {
            if(request()->image) $product->addMedia(request()->image)->toMediaCollection("image");
            // }
        }

        return redirect()
            ->route('products.index')
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product $product
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Product $product)
    {
        $this->authorize('delete', $product);

        $product->delete();

        return redirect()
            ->route('products.index')
            ->withSuccess(__('crud.common.removed'));
    }


    public function ajaxProducts(Request $request){
        $products = Product::with("unit", "units")->search($request->search ?? '')->paginate();
        return response($products);
    }

    public function ajaxProductSearch(Request $request){
        $products = Product::with("unit", "units")->search($request->search ?? '')->paginate();
        return response($products);
    }

    public function ajaxProductByBarcode(Request $request){
        $product = Product::with("unit", "units")->where("barcode", $request->barcode)->first();
        return response($product);
    }

    public function ajaxProduct(Request $request, Product $product){
        $product->units;
        $product->unit;
        return response($product);
    }

    public function importProducts(Request $request) {

        \Excel::import(new \App\Imports\ImportProducts, $request->products);

        return redirect()
            ->back()
            ->withSuccess("Products Imported Successfully");    
    }

    public function productTemplate(Request $request) {
        return \Excel::download(new \App\Exports\ExportProducts, 'products.xlsx');
    }


    // public function importExams(Request $request) {

    //     \Excel::import(new \App\Imports\ImportResults, $request->results);
    //     return redirect()->back();
    // }


}
