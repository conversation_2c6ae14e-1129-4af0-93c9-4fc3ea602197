@extends('layouts.app')

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="title">
            {{ $debt->debtable->name ?? ''}}
            (Debtor)
        </x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Invoice::class)
                <a href="{{ url()->previous() }}" class="btn btn-info btn-sm d-inline-flex align-items-center rounded-pill shadow px-3 py-1 gap-2" style="font-weight: 500;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-left" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 1-.5.5H2.707l3.147 3.146a.5.5 0 0 1-.708.708l-4-4a.5.5 0 0 1 0-.708l4-4a.5.5 0 1 1 .708.708L2.707 7.5H14.5A.5.5 0 0 1 15 8z"/>
                    </svg>
                    Back
                </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="mt-4">



      <div class="row">
        <div class="col-lg-4 mb-3 mb-lg-5">
          <!-- Card -->
          <div class="card h-100">
            <!-- Header -->
            <div class="card-header card-header-content-between">
              <h4 class="card-header-title"> {{ $debt->description }}</h4>
            </div>
            <!-- End Header -->

            <!-- Body -->
            <div class="card-body text-center">
              <!-- Badge -->
              @if( $debt->amount_paid < $debt->amount_total)
              <div class="h3">
                <span class="badge bg-soft-info text-info rounded-pill">
                  <i class="bi-check-circle-fill me-1"></i> Pending
                </span>
              </div>
              @endif
              <!-- End Badge -->

              <!-- Chart Half -->
              <div class="chartjs-doughnut-custom" style="height: 12rem;">
                <canvas id="doughnutHalfChart" class="js-chartjs-doughnut-half" data-hs-chartjs-options='{
                        "type": "doughnut",
                        "data": {
                          "labels": ["Paid Total", "Balance"],
                          "datasets": [{
                            "data": [{{ $debt->amount_paid }}, {{ $debt->balance }}],
                            "backgroundColor": ["#377dff", "rgba(55,125,255,.35)"],
                            "borderWidth": 4,
                            "borderColor": "#fff",
                            "hoverBorderColor": "#ffffff"
                          }]
                        }
                      }'></canvas>
                <div class="chartjs-doughnut-custom-stat">
                  <small class="text-cap">Balance</small>
                  <span class="h1"> {{ _money( $debt->balance ?? 0) }} </span>
                </div>
              </div>
              <!-- End Chart Half -->

              <hr>

              <div class="row col-divider">
                <div class="col text-end">
                  <span class="d-block h4 mb-0">{{ _money( $debt->amount_total  ) }}</span>
                  <span class="d-block">Total Amount </span>
                </div>

                <div class="col text-start">
                  <span class="d-block h4 mb-0">{{ _money( $debt->amount_paid  ) }}</span>
                  <span class="d-block">Paid Amount</span>
                </div>
              </div>
              <!-- End Row -->
            </div>
            <!-- End Body -->
          </div>
          <!-- End Card -->
        </div>

        <div class="col-lg-8 mb-3 mb-lg-5">
          <!-- Card -->
          <div class="card h-100">
            <!-- Header -->
            <div class="card-header card-header-content-between">
              <h4 class="card-header-title">Transactions</h4>

              <!-- Dropdown -->
              <div class="dropdown">
                <button type="button" class="btn btn-ghost-secondary btn-icon btn-sm rounded-circle" id="reportsOverviewDropdown3" data-bs-toggle="dropdown" aria-expanded="false">
                  <i class="bi-three-dots-vertical"></i>
                </button>
              </div>
              <!-- End Dropdown -->
            </div>
            <!-- End Header -->

            <!-- Body -->
            <div class="card-body card-body-height">
              <ul class="list-group list-group-flush list-group-no-gutters">
                <!-- List Item -->
                @foreach( $debt->payments as $payment)
                <li class="list-group-item">
                  <div class="d-flex">

                    <div class="flex-grow-1 ms-3">
                      <div class="row">
                        <div class="col-7 col-md-5 order-md-1">
                          <h5 class="mb-0">{{ $payment->createdBy->name ?? ''}}</h5>
                          <span class="fs-6 text-body">{{ $payment->comment ?? ''}}</span>
                        </div>

                        <div class="col-5 col-md-4 order-md-3 text-end mt-2 mt-md-0">
                          <h5 class="mb-0">
                              {{ _money($payment->amount) ?? ''}}
                          </h5>
                          <span class="fs-6 text-body">
                              {{ $payment->created_at ?? ''}}
                          </span>
                        </div>
                      </div>
                      <!-- End Row -->
                    </div>
                  </div>
                </li>
                @endforeach
                <!-- End List Item -->
              </ul>
            </div>
            <!-- End Body -->
          </div>
          <!-- End Card -->
        </div>
      </div>
      <!-- End Row -->


    </div>


</div>

@endsection

@push("scripts")
    
    <script type="text/javascript">
        
        $("document").ready( function(){

            // INITIALIZATION OF CHARTJS
            // =======================================================
            HSCore.components.HSChartJS.init(document.querySelector('.js-chartjs-doughnut-half'), {
              options: {
                plugins: {
                  tooltip: {
                    postfix: "%"
                  }
                },
                cutout: '85%',
                rotation: '270',
                circumference: '180'
              }
            });
        });

    </script>
    
@endpush