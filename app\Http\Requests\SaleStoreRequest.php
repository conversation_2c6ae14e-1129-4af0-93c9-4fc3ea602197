<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SaleStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'product_name' => ['required', 'max:255', 'string'],
            'product_id' => ['required', 'tenant_exists:products,id'],
            'supplier_id' => ['nullable'],
            'unit_name' => ['nullable', 'max:255', 'string'],
            'unit_id' => ['nullable', 'tenant_exists:units,id'],
            'price' => ['nullable', 'numeric'],
            'discount' => ['nullable', 'numeric'],
            'quantity' => ['nullable', 'numeric'],
            'expires_at' => ['nullable', 'date', 'after:today'],
            'description' => ['nullable', 'string', 'max:255'],
            'reference_no' => ['nullable', 'string', 'max:255'],
        ];
    }
}
