<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sales', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('product_name');
            $table->unsignedBigInteger('product_id');
            $table->string('unit_name')->nullable();
            $table->unsignedBigInteger('supplier_id')->nullable();
            $table->unsignedBigInteger('unit_id')->nullable();
            $table
                ->decimal('selling_price', 12, 2)
                ->default(0)
                ->nullable();                  
            $table
                ->decimal('unit_quantity', 12, 2)
                ->default(0)
                ->nullable();            
            $table
                ->decimal('buying_price', 12, 2)
                ->default(0)
                ->nullable();
            $table
                ->decimal('discount', 12, 2)
                ->default(0)
                ->nullable();
            $table->decimal('quantity', 12, 2)->nullable();
            $table->decimal('vat', 12, 2)->nullable();
            $table->unsignedBigInteger('saleable_id');
            $table->string('saleable_type');
            $table->unsignedBigInteger('branch_id')->nullable();
            $table->unsignedBigInteger('warehouse_id')->nullable();
            $table->string('location')->nullable();
            $table->unsignedBigInteger('currency_id')->nullable();
            $table->text('description')->nullable();
            $table->datetime('expires_at')->nullable();
            $table->index('saleable_id');
            $table->index('saleable_type');
            $table->unsignedBigInteger('business_type_id')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sales');
    }
};
