<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\BusinessType;

use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BusinessTypeUsersTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create(['email' => '<EMAIL>']);

        Sanctum::actingAs($user, [], 'web');

        $this->seed(\Database\Seeders\PermissionsSeeder::class);

        $this->withoutExceptionHandling();
    }

    /**
     * @test
     */
    public function it_gets_business_type_users()
    {
        $businessType = BusinessType::factory()->create();
        $user = User::factory()->create();

        $businessType->users()->attach($user);

        $response = $this->getJson(
            route('api.business-types.users.index', $businessType)
        );

        $response->assertOk()->assertSee($user->name);
    }

    /**
     * @test
     */
    public function it_can_attach_users_to_business_type()
    {
        $businessType = BusinessType::factory()->create();
        $user = User::factory()->create();

        $response = $this->postJson(
            route('api.business-types.users.store', [$businessType, $user])
        );

        $response->assertNoContent();

        $this->assertTrue(
            $businessType
                ->users()
                ->where('users.id', $user->id)
                ->exists()
        );
    }

    /**
     * @test
     */
    public function it_can_detach_users_from_business_type()
    {
        $businessType = BusinessType::factory()->create();
        $user = User::factory()->create();

        $response = $this->deleteJson(
            route('api.business-types.users.store', [$businessType, $user])
        );

        $response->assertNoContent();

        $this->assertFalse(
            $businessType
                ->users()
                ->where('users.id', $user->id)
                ->exists()
        );
    }
}
