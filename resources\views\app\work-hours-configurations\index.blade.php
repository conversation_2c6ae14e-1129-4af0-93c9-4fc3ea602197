@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-header-title">Work Hours Configurations</h1>
                <p class="page-header-text">Manage standard work hours and overtime settings</p>
            </div>
            <div class="col-auto">
                <a href="{{ route('work-hours-configurations.create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> Add Configuration
                </a>
            </div>
        </div>
    </div>

    <!-- Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('work-hours-configurations.index') }}">
                        <div class="row">
                            <div class="col-md-10">
                                <input type="text" name="search" class="form-control" 
                                    placeholder="Search configurations..." value="{{ $search }}">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-search"></i> Search
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Configurations List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive" style="min-height: 200px;">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Work Hours</th>
                                    <th>Standard Hours/Day</th>
                                    <th>Overtime Rate</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($configurations as $config)
                                <tr>
                                    <td>
                                        <strong>{{ $config->name }}</strong>
                                        @if($config->is_active)
                                            <span class="badge bg-success ms-2">Active</span>
                                        @endif
                                    </td>
                                    <td>
                                        {{ date('H:i', strtotime($config->start_time)) }} - 
                                        {{ date('H:i', strtotime($config->end_time)) }}
                                    </td>
                                    <td>{{ $config->standard_hours_per_day }}h</td>
                                    <td>{{ $config->overtime_rate }}x</td>
                                    <td>
                                        @if($config->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-secondary">Inactive</span>
                                        @endif
                                    </td>
                                    <td>{{ $config->created_at->format('M j, Y') }}</td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                type="button" data-bs-toggle="dropdown">
                                                Actions
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('work-hours-configurations.show', $config) }}">
                                                        <i class="bi bi-eye"></i> View
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('work-hours-configurations.edit', $config) }}">
                                                        <i class="bi bi-pencil"></i> Edit
                                                    </a>
                                                </li>
                                                @if(!$config->is_active)
                                                <li>
                                                    <form method="POST" action="{{ route('work-hours-configurations.activate', $config) }}" 
                                                        style="display: inline;">
                                                        @csrf
                                                        <button type="submit" class="dropdown-item text-success"
                                                            onclick="return confirm('Activate this configuration? This will deactivate the current active configuration.')">
                                                            <i class="bi bi-check-circle"></i> Activate
                                                        </button>
                                                    </form>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <form method="POST" action="{{ route('work-hours-configurations.destroy', $config) }}" 
                                                        style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="dropdown-item text-danger"
                                                            onclick="return confirm('Are you sure you want to delete this configuration?')">
                                                            <i class="bi bi-trash"></i> Delete
                                                        </button>
                                                    </form>
                                                </li>
                                                @endif
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="bi bi-clock-history fs-1 d-block mb-2"></i>
                                        No work hours configurations found
                                        <br>
                                        <a href="{{ route('work-hours-configurations.create') }}" class="btn btn-primary mt-2">
                                            Create First Configuration
                                        </a>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($configurations->hasPages())
                    <div class="d-flex justify-content-center mt-4">
                        {{ $configurations->links() }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
