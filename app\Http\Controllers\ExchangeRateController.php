<?php

namespace App\Http\Controllers;

use App\Models\Currency;
use App\Models\ExchangeRate;
use Illuminate\Http\Request;

class ExchangeRateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', ExchangeRate::class);

        $search = $request->get('search', '');
        $currencyId = $request->get('currency_id', '');
        $fromDate = $request->get('from_date', '');
        $toDate = $request->get('to_date', '');

        $exchangeRates = ExchangeRate::search($search);
        
        if ($currencyId) {
            $exchangeRates->where('currency_id', $currencyId);
        }
        
        if ($fromDate) {
            $exchangeRates->where('rate_date', '>=', $fromDate);
        }
        
        if ($toDate) {
            $exchangeRates->where('rate_date', '<=', $toDate);
        }
        
        $exchangeRates = $exchangeRates->latest('rate_date')
            ->paginate()
            ->withQueryString();

        $currencies = Currency::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.exchange_rates.index', compact(
            'exchangeRates', 
            'search', 
            'currencies',
            'currencyId',
            'fromDate',
            'toDate'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', ExchangeRate::class);

        $currencies = Currency::where('is_active', true)
            ->where('is_base_currency', false)
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.exchange_rates.create', compact('currencies'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', ExchangeRate::class);

        $validated = $request->validate([
            'currency_id' => 'required|exists:currencies,id',
            'rate_date' => 'required|date',
            'rate' => 'required|numeric|min:0',
            'description' => 'nullable|string',
        ]);

        // Check if an exchange rate already exists for this currency and date
        $existingRate = ExchangeRate::where('currency_id', $validated['currency_id'])
            ->where('rate_date', $validated['rate_date'])
            ->first();
            
        if ($existingRate) {
            return redirect()
                ->back()
                ->withInput()
                ->withError('An exchange rate already exists for this currency and date.');
        }

        $exchangeRate = ExchangeRate::create($validated);
        
        // Update the currency's current exchange rate
        $currency = Currency::find($validated['currency_id']);
        $currency->update(['exchange_rate' => $validated['rate']]);

        return redirect()
            ->route('exchange-rates.index')
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ExchangeRate  $exchangeRate
     * @return \Illuminate\Http\Response
     */
    public function show(ExchangeRate $exchangeRate)
    {
        $this->authorize('view', $exchangeRate);

        return view('app.exchange_rates.show', compact('exchangeRate'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\ExchangeRate  $exchangeRate
     * @return \Illuminate\Http\Response
     */
    public function edit(ExchangeRate $exchangeRate)
    {
        $this->authorize('update', $exchangeRate);

        $currencies = Currency::where('is_active', true)
            ->where('is_base_currency', false)
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.exchange_rates.edit', compact('exchangeRate', 'currencies'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ExchangeRate  $exchangeRate
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ExchangeRate $exchangeRate)
    {
        $this->authorize('update', $exchangeRate);

        $validated = $request->validate([
            'currency_id' => 'required|exists:currencies,id',
            'rate_date' => 'required|date',
            'rate' => 'required|numeric|min:0',
            'description' => 'nullable|string',
        ]);

        // Check if an exchange rate already exists for this currency and date
        $existingRate = ExchangeRate::where('currency_id', $validated['currency_id'])
            ->where('rate_date', $validated['rate_date'])
            ->where('id', '!=', $exchangeRate->id)
            ->first();
            
        if ($existingRate) {
            return redirect()
                ->back()
                ->withInput()
                ->withError('An exchange rate already exists for this currency and date.');
        }

        $exchangeRate->update($validated);
        
        // Update the currency's current exchange rate if this is the latest rate
        $latestRate = ExchangeRate::where('currency_id', $validated['currency_id'])
            ->latest('rate_date')
            ->first();
            
        if ($latestRate && $latestRate->id === $exchangeRate->id) {
            $currency = Currency::find($validated['currency_id']);
            $currency->update(['exchange_rate' => $validated['rate']]);
        }

        return redirect()
            ->route('exchange-rates.index')
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ExchangeRate  $exchangeRate
     * @return \Illuminate\Http\Response
     */
    public function destroy(ExchangeRate $exchangeRate)
    {
        $this->authorize('delete', $exchangeRate);

        $exchangeRate->delete();
        
        // Update the currency's current exchange rate to the latest available
        $latestRate = ExchangeRate::where('currency_id', $exchangeRate->currency_id)
            ->latest('rate_date')
            ->first();
            
        if ($latestRate) {
            $currency = Currency::find($exchangeRate->currency_id);
            $currency->update(['exchange_rate' => $latestRate->rate]);
        }

        return redirect()
            ->route('exchange-rates.index')
            ->withSuccess(__('crud.common.removed'));
    }
}
