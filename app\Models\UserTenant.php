<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class UserTenant extends Model
{
    protected $connection = 'landlord'; // Use landlord database

    protected $fillable = [
        'user_email',
        'user_id',
        'tenant_id',
        'role',
        'permissions',
        'is_active',
        'is_primary',
        'invitation_token',
        'invited_at',
        'expires_at',
        'accepted_at',
        'joined_at',
        'invited_by',
        'invitation_message',
        'warehouse_id',
        'is_invitation_used',
        'status'
    ];

    protected $appends  = ['name'];

    protected $casts = [
        'permissions' => 'array',
        'is_active' => 'boolean',
        'is_primary' => 'boolean',
        'is_invitation_used' => 'boolean',
        'invited_at' => 'datetime',
        'expires_at' => 'datetime',
        'accepted_at' => 'datetime',
        'joined_at' => 'datetime'
    ];

    /**
     * Get user's tenants by email
     */
    public static function getUserTenants($email)
    {
        $ids = self::where('user_email', $email)
                  ->orderBy('is_primary', 'desc')
                  ->orderBy('joined_at', 'asc')
                  ->pluck("tenant_id");
        $ids[] = Tenant::where("email", $email)->first()->id ?? 0;
        $tenants = Tenant::whereIn("id", $ids)->get();
        return $tenants;
    }

    public function getNameAttribute() {
        return $this->tenant->name ?? '';
    }

    /**
     * Get user's tenants by user ID
     */
    public static function getUserTenantsById($userId)
    {
        return self::where('user_id', $userId)
                  ->where('is_active', true)
                  ->orderBy('is_primary', 'desc')
                  ->orderBy('joined_at', 'asc')
                  ->get();
    }

    /**
     * Check if user has access to tenant
     */
    public static function hasAccess($userEmail, $tenantId)
    {
        return self::where('user_email', $userEmail)
                  ->where('tenant_id', $tenantId)
                  ->where('is_active', true)
                  ->exists() || Tenant::where("email", $userEmail)->first();
    }

    /**
     * Get user's primary tenant
     */
    public static function getPrimaryTenant($userEmail)
    {
        return self::where('user_email', $userEmail)
                  ->where('is_primary', true)
                  ->where('is_active', true)
                  ->first();
    }

    /**
     * Set primary tenant for user
     */
    public static function setPrimaryTenant($userEmail, $tenantId)
    {
        DB::connection('landlord')->transaction(function () use ($userEmail, $tenantId) {
            // Remove primary flag from all user's tenants
            self::where('user_email', $userEmail)->update(['is_primary' => false]);
            
            // Set new primary tenant
            self::where('user_email', $userEmail)
                ->where('tenant_id', $tenantId)
                ->update(['is_primary' => true]);
        });
    }

    /**
     * Add user to tenant
     */
    public static function addUserToTenant($userEmail, $tenantId, $role = 'staff', $invitedBy = null)
    {
        // Check if there's an existing invitation record
        $existingRecord = self::where('user_email', $userEmail)
                             ->where('tenant_id', $tenantId)
                             ->first();

        if ($existingRecord && $existingRecord->invitation_token) {
            // This is an invitation record, don't overwrite it
            // Just update the necessary fields for direct user creation
            $existingRecord->update([
                'role' => $role,
                'is_active' => true,
                'joined_at' => now(),
                'invited_by' => $invitedBy ?? $existingRecord->invited_by
            ]);
            return $existingRecord;
        }

        return self::updateOrCreate(
            [
                'user_email' => $userEmail,
                'tenant_id' => $tenantId
            ],
            [
                'role' => $role,
                'is_active' => true,
                'is_primary' => self::where('user_email', $userEmail)->count() === 0, // First tenant is primary
                'invited_at' => now(),
                'joined_at' => now(),
                'invited_by' => $invitedBy
            ]
        );
    }

    /**
     * Remove user from tenant
     */
    public static function removeUserFromTenant($userEmail, $tenantId)
    {
        return self::where('user_email', $userEmail)
                  ->where('tenant_id', $tenantId)
                  ->delete();
    }

    /**
     * Update user ID after account creation
     */
    public static function updateUserId($userEmail, $userId)
    {
        return self::where('user_email', $userEmail)
                  ->whereNull('user_id')
                  ->update(['user_id' => $userId]);
    }

    /**
     * Get tenant information (you might want to create a Tenant model)
     */
    public function getTenantInfo()
    {
        // This would typically fetch from a tenants table
        // For now, return basic info based on tenant_id
        return [
            'id' => $this->tenant_id,
            'name' => ucfirst(str_replace('_', ' ', $this->tenant_id)),
            'database' => $this->tenant_id
        ];
    }

    /**
     * Scope for active user-tenant relationships
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific tenant
     */
    public function scopeForTenant($query, $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

        /**
     * Get invitations sent by this user
     */
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }


    /**
     * Scope for specific user email
     */
    public function scopeForUser($query, $userEmail)
    {
        return $query->where('user_email', $userEmail);
    }

    // ========== INVITATION METHODS ==========

    /**
     * Generate a unique invitation token
     */
    public static function generateInvitationToken()
    {
        do {
            $token = \Str::random(64);
        } while (self::where('invitation_token', $token)->exists());

        return $token;
    }

    /**
     * Check if invitation is expired
     */
    public function isExpired()
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if invitation is valid (not expired and not used)
     */
    public function isValidInvitation()
    {
        return !$this->is_invitation_used &&
               !$this->isExpired() &&
               $this->status === 'pending';
    }

    /**
     * Mark invitation as used/accepted
     */
    public function markAsAccepted($userId = null)
    {
        $this->update([
            'is_invitation_used' => true,
            'status' => 'accepted',
            'accepted_at' => now(),
            'joined_at' => now(),
            'user_id' => $userId
        ]);
    }

    /**
     * Cancel invitation
     */
    public function cancelInvitation()
    {
        $this->update([
            'status' => 'cancelled',
            'is_invitation_used' => true
        ]);
    }

    /**
     * Extend invitation expiry
     */
    public function extendExpiry($days = 7)
    {
        $this->update([
            'expires_at' => now()->addDays($days)
        ]);
    }

    /**
     * Get pending invitations for a tenant
     */
    public static function getPendingInvitations($tenantId)
    {
        return self::where('tenant_id', $tenantId)
                  ->where('status', 'pending')
                  ->where('is_invitation_used', false)
                  ->whereNull('user_id')
                  ->with(['inviter'])
                  ->orderBy('invited_at', 'desc')
                  ->get();
    }

    /**
     * Create invitation
     */
    public static function createInvitation($data)
    {
        $invitationData = [
            'user_email' => $data['email'],
            'tenant_id' => $data['tenant_id'],
            'role' => $data['role'],
            'warehouse_id' => $data['warehouse_id'] ?? null,
            'invitation_token' => self::generateInvitationToken(),
            'invited_at' => now(),
            'expires_at' => now()->addDays(7),
            'invited_by' => $data['invited_by'],
            'invitation_message' => $data['message'] ?? null,
            'status' => 'pending',
            'is_invitation_used' => false,
            'is_active' => false // Will be activated when accepted
        ];

        \Log::info('Creating invitation with data:', $invitationData);

        $invitation = self::create($invitationData);

        \Log::info('Created invitation:', [
            'id' => $invitation->id,
            'token' => $invitation->invitation_token,
            'status' => $invitation->status,
            'is_invitation_used' => $invitation->is_invitation_used,
            'expires_at' => $invitation->expires_at
        ]);

        return $invitation;
    }

    // ========== RELATIONSHIPS ==========

    /**
     * Get the user who sent the invitation
     */
    public function inviter()
    {
        return $this->belongsTo(User::class, 'invited_by');
    }

    /**
     * Get the warehouse for this invitation
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

}
