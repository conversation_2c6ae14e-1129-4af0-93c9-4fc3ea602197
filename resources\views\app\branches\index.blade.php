@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">building</x-slot>
        <x-slot name="title">Branches</x-slot>
        <x-slot name="subtitle">Manage your business locations</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Branches</li>
        </x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Branch::class)
            <a href="{{ route('branches.create') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i> Add Branch
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Search & Filters -->
    <div class="card mb-3">
        <div class="card-body">
            <form action="{{ route('branches.index') }}" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search Branches</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="search" name="search" placeholder="Name, email, phone..." value="{{ request('search') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select js-select" id="status" name="status_id">
                        <option value="">All Statuses</option>
                        @foreach(\App\Models\Status::orderBy('name')->get() as $status)
                        <option value="{{ $status->id }}" {{ request('status_id') == $status->id ? 'selected' : '' }}>{{ $status->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-filter me-1"></i> Filter
                    </button>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <a href="{{ route('branches.index') }}" class="btn btn-outline-secondary w-100">
                        <i class="bi bi-x-circle me-1"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Branches Table -->
    <div class="card">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">Branch Locations</h4>
                </div>
                <div class="col-auto">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-download me-1"></i> Export
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="exportDropdown">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-excel me-1"></i> Excel</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-pdf me-1"></i> PDF</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-text me-1"></i> CSV</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>@lang('crud.branches.inputs.name')</th>
                        <th>@lang('crud.branches.inputs.phone')</th>
                        <th>@lang('crud.branches.inputs.email')</th>
                        <th>@lang('crud.branches.inputs.status_id')</th>
                        <th>@lang('crud.branches.inputs.address')</th>
                        <th class="text-center">@lang('crud.common.actions')</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($branches as $branch)
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar avatar-sm avatar-circle">
                                        <span class="avatar-initials">{{ substr($branch->name, 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5 class="mb-0">{{ $branch->name ?? '-' }}</h5>
                                </div>
                            </div>
                        </td>
                        <td>
                            <a href="tel:{{ $branch->phone }}" class="text-body">
                                <i class="bi bi-telephone me-1"></i> {{ $branch->phone ?? '-' }}
                            </a>
                        </td>
                        <td>
                            <a href="mailto:{{ $branch->email }}" class="text-body">
                                <i class="bi bi-envelope me-1"></i> {{ $branch->email ?? '-' }}
                            </a>
                        </td>
                        <td>
                            @if(optional($branch->status)->name == 'Active')
                                <span class="badge bg-soft-success text-success">
                                    <i class="bi bi-check-circle me-1"></i> Active
                                </span>
                            @elseif(optional($branch->status)->name == 'Inactive')
                                <span class="badge bg-soft-danger text-danger">
                                    <i class="bi bi-x-circle me-1"></i> Inactive
                                </span>
                            @else
                                <span class="badge bg-soft-secondary">
                                    {{ optional($branch->status)->name ?? 'N/A' }}
                                </span>
                            @endif
                        </td>
                        <td>
                            <span class="d-block text-truncate" style="max-width: 200px;">
                                <i class="bi bi-geo-alt me-1"></i> {{ $branch->address ?? '-' }}
                            </span>
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group" aria-label="Row Actions">
                                @can('update', $branch)
                                <a href="{{ route('branches.edit', $branch) }}" class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit">
                                    <i class="bi bi-pencil-square"></i>
                                </a>
                                @endcan 
                                
                                @can('view', $branch)
                                <a href="{{ route('branches.show', $branch) }}" class="btn btn-outline-info btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="View">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @endcan 
                                
                                @can('delete', $branch)
                                <form action="{{ route('branches.destroy', $branch) }}" method="POST" onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')">
                                    @csrf @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="text-center p-4">
                            <div class="d-flex flex-column align-items-center justify-content-center py-5">
                                <i class="bi bi-building text-muted" style="font-size: 3rem;"></i>
                                <h5 class="mt-4">No branches found</h5>
                                <p class="text-muted">Try adjusting your search or filter to find what you're looking for.</p>
                                @can('create', App\Models\Branch::class)
                                <a href="{{ route('branches.create') }}" class="btn btn-primary mt-2">
                                    <i class="bi bi-plus-circle me-1"></i> Add New Branch
                                </a>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        @if(isset($branches) && $branches->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-center">
                {{ $branches->links() }}
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
@endpush
