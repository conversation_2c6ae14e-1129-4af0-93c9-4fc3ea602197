@php $editing = isset($warehouse) @endphp

<!-- Form -->
<div class="row mb-4">
    <div class="col-sm-6">
        <!-- Form -->
        <div class="mb-4">
            <label for="name" class="form-label">Name <i class="bi-asterisk text-danger small"></i></label>
            <input type="text" class="form-control form-control-lg @error('name') is-invalid @enderror" 
                name="name" id="name" placeholder="Enter warehouse name"
                value="{{ old('name', ($editing ? $warehouse->name : '')) }}" 
                required maxlength="255">
            @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <!-- End Form -->
    </div>

    <div class="col-sm-6">
        <!-- Form -->
        <div class="mb-4">
            <label for="code" class="form-label">Code <i class="bi-asterisk text-danger small"></i></label>
            <input type="text" class="form-control form-control-lg @error('code') is-invalid @enderror" 
                name="code" id="code" placeholder="Enter warehouse code"
                value="{{ old('code', ($editing ? $warehouse->code : '')) }}" 
                required maxlength="50">
            @error('code')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <!-- End Form -->
    </div>
</div>
<!-- End Form -->

<!-- Form -->
<div class="row mb-4">
    <div class="col-sm-6">
        <!-- Form -->
        <div class="mb-4">
            <label for="phone" class="form-label">Phone</label>
            <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                name="phone" id="phone" placeholder="Enter phone number"
                value="{{ old('phone', ($editing ? $warehouse->phone : '')) }}" 
                maxlength="20">
            @error('phone')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <!-- End Form -->
    </div>

    <div class="col-sm-6">
        <!-- Form -->
        <div class="mb-4">
            <label for="email" class="form-label">Email</label>
            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                name="email" id="email" placeholder="Enter email address"
                value="{{ old('email', ($editing ? $warehouse->email : '')) }}" 
                maxlength="255">
            @error('email')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <!-- End Form -->
    </div>
</div>
<!-- End Form -->

<!-- Form -->
<div class="row mb-4">

    <div class="col-sm-6">
        <!-- Form -->
        <div class="mb-4">
            <label for="manager_id" class="form-label">Manager <i class="bi-asterisk text-danger small"></i></label>
            <select class="form-select @error('manager_id') is-invalid @enderror" 
                name="manager_id" id="manager_id" required>
                @php $selected = old('manager_id', ($editing ? $warehouse->manager_id : '')) @endphp
                <option value="" disabled {{ empty($selected) ? 'selected' : '' }}>Please select the manager</option>
                @foreach($managers as $value => $label)
                <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }}>{{ $label }}</option>
                @endforeach
            </select>
            @error('branch_id')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <!-- End Form -->
    </div>

    <div class="col-sm-6">
        <!-- Form -->
        <div class="mb-4">
            <label for="branch_id" class="form-label">Branch <i class="bi-asterisk text-danger small"></i></label>
            <select class="form-select @error('branch_id') is-invalid @enderror" 
                name="branch_id" id="branch_id" required>
                @php $selected = old('branch_id', ($editing ? $warehouse->branch_id : '')) @endphp
                <option value="" disabled {{ empty($selected) ? 'selected' : '' }}>Please select the Branch</option>
                @foreach($branches as $value => $label)
                <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }}>{{ $label }}</option>
                @endforeach
            </select>
            @error('branch_id')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <!-- End Form -->
    </div>
</div>
<!-- End Form -->

<!-- Form -->
<div class="mb-4">
    <label for="address" class="form-label">Address</label>
    <input type="text" class="form-control @error('address') is-invalid @enderror" 
        name="address" id="address" placeholder="Enter warehouse address"
        value="{{ old('address', ($editing ? $warehouse->address : '')) }}" 
        maxlength="255">
    @error('address')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
</div>
<!-- End Form -->

<!-- Form -->
<div class="mb-4">
    <label for="description" class="form-label">Description</label>
    <textarea class="form-control @error('description') is-invalid @enderror" 
        name="description" id="description" rows="4" 
        placeholder="Enter warehouse description">{{ old('description', ($editing ? $warehouse->description : '')) }}</textarea>
    @error('description')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
</div>
<!-- End Form -->

<!-- Form -->
<div class="mb-4">
    <div class="form-check form-switch">
        <input type="checkbox" class="form-check-input" value="1"
            name="is_active" id="is_active" 
            {{ old('is_active', ($editing ? $warehouse->is_active : 1)) ? 'checked' : '' }}>
        <label class="form-check-label" for="is_active">Active</label>
    </div>
</div>
<!-- End Form -->

<!-- Form -->
<div class="mb-4">
    <label class="form-label">Warehouse Image</label>
    
    <div class="mb-3">
        <!-- File Attachment Input -->
        <div class="input-group">
            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                name="image" id="image">
        </div>
        <!-- End File Attachment Input -->
        
        @error('image')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
        
        <small class="text-muted">Upload a warehouse image (optional)</small>
    </div>
    
    @if($editing && $warehouse->image)
    <div class="mb-2">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Current Image</h5>
                <img src="{{ \Storage::url($warehouse->image) }}" class="img-fluid rounded" style="max-height: 200px;">
            </div>
        </div>
    </div>
    @endif
</div>
<!-- End Form -->
