<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Status;
use App\Models\Tenant;
use App\Models\Branch;
use Illuminate\Http\Request;
use App\Models\Role;
use App\Models\Warehouse;
use Illuminate\Support\Facades\Hash;
use App\Http\Requests\UserStoreRequest;
use Illuminate\Support\Facades\Storage;
use App\Http\Requests\UserUpdateRequest;
use Facades\App\Libraries\UserHandler;

use App\Models\UserTenant;
use App\Notifications\UserInvitationNotification;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\UserInvitationMail;
use Carbon\Carbon;

class UserController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', User::class);

        $search = $request->get('search', '');

        $users = User::search($search)->latest()->get();
        $warehouses = Warehouse::pluck('name', 'id');

        $roles = Role::get();


        // Get pending invitations for current tenant
        $currentTenant = Tenant::current();
        $pendingInvitations = collect();

        if ($currentTenant) {
            $pendingInvitations = UserTenant::getPendingInvitations($currentTenant->id);
        }

        return view('app.users.index', compact('users', 'search', 'roles', 'pendingInvitations', 'warehouses'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', User::class);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');
        $branches = Branch::pluck('name', 'id');
        $warehouses = Warehouse::pluck('name', 'id');

        $roles = Role::get();

        return view(
            'app.users.create',
            compact('statuses', 'branches', 'roles', 'warehouses')
        ); 
    }

    /**
     * @param \App\Http\Requests\UserStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(UserStoreRequest $request)
    {
        $this->authorize('create', User::class);

        $validated = $request->validated();

        // Check if this is an invitation or direct creation
        if ($request->creation_method === 'invitation') {
            return $this->sendInvitation($request);
        }

        // Direct user creation
        $validated['password'] = Hash::make($validated['password']);
        $user = User::create($validated);

        if(request()->image) {
            $user->clearMediaCollection('image');
            // foreach (request()->image as $file) {
            if(request()->image) $user->addMedia(request()->image)->toMediaCollection("image");
            // }
        }

        $user->syncRoles($request->roles);


        // Add user to current tenant
        $currentTenant = Tenant::current();
        if ($currentTenant) {
            UserTenant::addUserToTenant(
                $user->email,
                $currentTenant->id,
                'staff', // Default role
                auth()->id()
            );
        }

        return redirect()
            ->route('users.edit', $user)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Send invitation email to user
     */
    protected function sendInvitation(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'name' => 'required|string|max:255',
            'invitation_role' => 'required|string',
            'warehouse_id' => 'required|tenant_exists:warehouses,id',
            'invitation_message' => 'nullable|string|max:1000'
        ]);

        $currentTenant = Tenant::current();

        // Check if user already exists or has pending invitation
        $existingUser = User::where('email', $request->email)->first();

        if ($existingUser) {
            return redirect()->back()->with('error', 'A user with this email already exists.');
        }

        // Check for existing valid invitation
        $existingInvitation = UserTenant::where('user_email', $request->email)
                                       ->where('tenant_id', $currentTenant->id)
                                       ->where('status', 'pending')
                                       ->where('is_invitation_used', false)
                                       ->where('expires_at', '>', now())
                                       ->first();

        if ($existingInvitation) {
            return redirect()->back()->with('error', 'An invitation has already been sent to this email.');
        }

        // Cancel any old invitations for this user/tenant
        UserTenant::where('user_email', $request->email)
                  ->where('tenant_id', $currentTenant->id)
                  ->where('status', 'pending')
                  ->update(['status' => 'cancelled', 'is_invitation_used' => true]);

        // Create new invitation in user_tenants table
        $invitation = UserTenant::createInvitation([
            'email' => $request->email,
            'tenant_id' => $currentTenant->id,
            'role' => $request->invitation_role,
            'warehouse_id' => $request->warehouse_id,
            'invited_by' => auth()->id(),
            'message' => $request->invitation_message
        ]);

        // Send invitation email
        $tenantName = ucfirst(str_replace('_', ' ', $currentTenant->name));
        $inviterName = auth()->user()->name;

        try {
            Log::info('Attempting to send invitation email', [
                'email' => $request->email,
                'tenant' => $tenantName,
                'inviter' => $inviterName,
                'invitation_id' => $invitation->id
            ]);

            // Send email using Mail facade
            Mail::to($request->email)->send(new UserInvitationMail($invitation, $inviterName, $tenantName));

            Log::info('Invitation email sent successfully', ['email' => $request->email]);

            return redirect()
                ->route('users.index')
                ->withSuccess('Invitation sent successfully to ' . $request->email);

        } catch (\Exception $e) {
            Log::error('Failed to send invitation email', [
                'email' => $request->email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()
                ->route('users.index')
                ->withError('Failed to send invitation email: ' . $e->getMessage());
        }
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, User $user)
    {
        $this->authorize('view', $user);

        return view('app.users.show', compact('user'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, User $user)
    {
        $this->authorize('update', $user);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');
        $branches = Branch::pluck('name', 'id');
        $warehouses = Warehouse::pluck('name', 'id');

        $roles = Role::get();

        return view(
            'app.users.edit',
            compact('user', 'statuses', 'branches', 'roles', 'warehouses')
        );
    }

    /**
     * @param \App\Http\Requests\UserUpdateRequest $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function update(UserUpdateRequest $request, User $user)
    {
        $this->authorize('update', $user);

        $validated = $request->validated();

        if (empty($validated['password'])) {
            unset($validated['password']);
        } else {
            $validated['password'] = Hash::make($validated['password']);
        }

        $user->update($validated);

        if(request()->image) {
            $user->clearMediaCollection('image');
            // foreach (request()->image as $file) {
            if(request()->image) $user->addMedia(request()->image)->toMediaCollection("image");
            // }
        }

        $user->syncRoles($request->roles);

        return redirect()
            ->route('users.edit', $user)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, User $user)
    {
        $this->authorize('delete', $user);

        if ($user->name) {
            Storage::delete($user->name);
        }

        $user->delete();

        return redirect()
            ->route('users.index')
            ->withSuccess(__('crud.common.removed'));
    }

}
