<?php

namespace App\Http\Controllers\Api;

use App\Models\Status;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\OrderResource;
use App\Http\Resources\OrderCollection;

class StatusOrdersController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Status $status
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, Status $status)
    {
        $this->authorize('view', $status);

        $search = $request->get('search', '');

        $orders = $status
            ->orders()
            ->search($search)
            ->latest()
            ->paginate();

        return new OrderCollection($orders);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Status $status
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, Status $status)
    {
        $this->authorize('create', Order::class);

        $validated = $request->validate([
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date'],
            'amount_paid' => ['nullable', 'numeric'],
            'amount_total' => ['nullable', 'numeric'],
            'vat' => ['nullable', 'numeric'],
            'discount' => ['nullable', 'numeric'],
            'approved_by' => ['nullable', 'exists:users,id'],
            'description' => ['nullable', 'max:255', 'string'],
        ]);

        $order = $status->orders()->create($validated);

        return new OrderResource($order);
    }
}
