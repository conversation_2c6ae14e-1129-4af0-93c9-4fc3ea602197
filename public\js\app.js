/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./node_modules/alpinejs/dist/module.esm.js":
/*!**************************************************!*\
  !*** ./node_modules/alpinejs/dist/module.esm.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ module_default)\n/* harmony export */ });\n// packages/alpinejs/src/scheduler.js\nvar flushPending = false;\nvar flushing = false;\nvar queue = [];\nvar lastFlushedIndex = -1;\nfunction scheduler(callback) {\n  queueJob(callback);\n}\nfunction queueJob(job) {\n  if (!queue.includes(job))\n    queue.push(job);\n  queueFlush();\n}\nfunction dequeueJob(job) {\n  let index = queue.indexOf(job);\n  if (index !== -1 && index > lastFlushedIndex)\n    queue.splice(index, 1);\n}\nfunction queueFlush() {\n  if (!flushing && !flushPending) {\n    flushPending = true;\n    queueMicrotask(flushJobs);\n  }\n}\nfunction flushJobs() {\n  flushPending = false;\n  flushing = true;\n  for (let i = 0; i < queue.length; i++) {\n    queue[i]();\n    lastFlushedIndex = i;\n  }\n  queue.length = 0;\n  lastFlushedIndex = -1;\n  flushing = false;\n}\n\n// packages/alpinejs/src/reactivity.js\nvar reactive;\nvar effect;\nvar release;\nvar raw;\nvar shouldSchedule = true;\nfunction disableEffectScheduling(callback) {\n  shouldSchedule = false;\n  callback();\n  shouldSchedule = true;\n}\nfunction setReactivityEngine(engine) {\n  reactive = engine.reactive;\n  release = engine.release;\n  effect = (callback) => engine.effect(callback, { scheduler: (task) => {\n    if (shouldSchedule) {\n      scheduler(task);\n    } else {\n      task();\n    }\n  } });\n  raw = engine.raw;\n}\nfunction overrideEffect(override) {\n  effect = override;\n}\nfunction elementBoundEffect(el) {\n  let cleanup2 = () => {\n  };\n  let wrappedEffect = (callback) => {\n    let effectReference = effect(callback);\n    if (!el._x_effects) {\n      el._x_effects = /* @__PURE__ */ new Set();\n      el._x_runEffects = () => {\n        el._x_effects.forEach((i) => i());\n      };\n    }\n    el._x_effects.add(effectReference);\n    cleanup2 = () => {\n      if (effectReference === void 0)\n        return;\n      el._x_effects.delete(effectReference);\n      release(effectReference);\n    };\n    return effectReference;\n  };\n  return [wrappedEffect, () => {\n    cleanup2();\n  }];\n}\n\n// packages/alpinejs/src/utils/dispatch.js\nfunction dispatch(el, name, detail = {}) {\n  el.dispatchEvent(\n    new CustomEvent(name, {\n      detail,\n      bubbles: true,\n      // Allows events to pass the shadow DOM barrier.\n      composed: true,\n      cancelable: true\n    })\n  );\n}\n\n// packages/alpinejs/src/utils/walk.js\nfunction walk(el, callback) {\n  if (typeof ShadowRoot === \"function\" && el instanceof ShadowRoot) {\n    Array.from(el.children).forEach((el2) => walk(el2, callback));\n    return;\n  }\n  let skip = false;\n  callback(el, () => skip = true);\n  if (skip)\n    return;\n  let node = el.firstElementChild;\n  while (node) {\n    walk(node, callback, false);\n    node = node.nextElementSibling;\n  }\n}\n\n// packages/alpinejs/src/utils/warn.js\nfunction warn(message, ...args) {\n  console.warn(`Alpine Warning: ${message}`, ...args);\n}\n\n// packages/alpinejs/src/lifecycle.js\nvar started = false;\nfunction start() {\n  if (started)\n    warn(\"Alpine has already been initialized on this page. Calling Alpine.start() more than once can cause problems.\");\n  started = true;\n  if (!document.body)\n    warn(\"Unable to initialize. Trying to load Alpine before `<body>` is available. Did you forget to add `defer` in Alpine's `<script>` tag?\");\n  dispatch(document, \"alpine:init\");\n  dispatch(document, \"alpine:initializing\");\n  startObservingMutations();\n  onElAdded((el) => initTree(el, walk));\n  onElRemoved((el) => destroyTree(el));\n  onAttributesAdded((el, attrs) => {\n    directives(el, attrs).forEach((handle) => handle());\n  });\n  let outNestedComponents = (el) => !closestRoot(el.parentElement, true);\n  Array.from(document.querySelectorAll(allSelectors())).filter(outNestedComponents).forEach((el) => {\n    initTree(el);\n  });\n  dispatch(document, \"alpine:initialized\");\n}\nvar rootSelectorCallbacks = [];\nvar initSelectorCallbacks = [];\nfunction rootSelectors() {\n  return rootSelectorCallbacks.map((fn) => fn());\n}\nfunction allSelectors() {\n  return rootSelectorCallbacks.concat(initSelectorCallbacks).map((fn) => fn());\n}\nfunction addRootSelector(selectorCallback) {\n  rootSelectorCallbacks.push(selectorCallback);\n}\nfunction addInitSelector(selectorCallback) {\n  initSelectorCallbacks.push(selectorCallback);\n}\nfunction closestRoot(el, includeInitSelectors = false) {\n  return findClosest(el, (element) => {\n    const selectors = includeInitSelectors ? allSelectors() : rootSelectors();\n    if (selectors.some((selector) => element.matches(selector)))\n      return true;\n  });\n}\nfunction findClosest(el, callback) {\n  if (!el)\n    return;\n  if (callback(el))\n    return el;\n  if (el._x_teleportBack)\n    el = el._x_teleportBack;\n  if (!el.parentElement)\n    return;\n  return findClosest(el.parentElement, callback);\n}\nfunction isRoot(el) {\n  return rootSelectors().some((selector) => el.matches(selector));\n}\nvar initInterceptors = [];\nfunction interceptInit(callback) {\n  initInterceptors.push(callback);\n}\nfunction initTree(el, walker = walk, intercept = () => {\n}) {\n  deferHandlingDirectives(() => {\n    walker(el, (el2, skip) => {\n      intercept(el2, skip);\n      initInterceptors.forEach((i) => i(el2, skip));\n      directives(el2, el2.attributes).forEach((handle) => handle());\n      el2._x_ignore && skip();\n    });\n  });\n}\nfunction destroyTree(root) {\n  walk(root, (el) => {\n    cleanupAttributes(el);\n    cleanupElement(el);\n  });\n}\n\n// packages/alpinejs/src/mutation.js\nvar onAttributeAddeds = [];\nvar onElRemoveds = [];\nvar onElAddeds = [];\nfunction onElAdded(callback) {\n  onElAddeds.push(callback);\n}\nfunction onElRemoved(el, callback) {\n  if (typeof callback === \"function\") {\n    if (!el._x_cleanups)\n      el._x_cleanups = [];\n    el._x_cleanups.push(callback);\n  } else {\n    callback = el;\n    onElRemoveds.push(callback);\n  }\n}\nfunction onAttributesAdded(callback) {\n  onAttributeAddeds.push(callback);\n}\nfunction onAttributeRemoved(el, name, callback) {\n  if (!el._x_attributeCleanups)\n    el._x_attributeCleanups = {};\n  if (!el._x_attributeCleanups[name])\n    el._x_attributeCleanups[name] = [];\n  el._x_attributeCleanups[name].push(callback);\n}\nfunction cleanupAttributes(el, names) {\n  if (!el._x_attributeCleanups)\n    return;\n  Object.entries(el._x_attributeCleanups).forEach(([name, value]) => {\n    if (names === void 0 || names.includes(name)) {\n      value.forEach((i) => i());\n      delete el._x_attributeCleanups[name];\n    }\n  });\n}\nfunction cleanupElement(el) {\n  if (el._x_cleanups) {\n    while (el._x_cleanups.length)\n      el._x_cleanups.pop()();\n  }\n}\nvar observer = new MutationObserver(onMutate);\nvar currentlyObserving = false;\nfunction startObservingMutations() {\n  observer.observe(document, { subtree: true, childList: true, attributes: true, attributeOldValue: true });\n  currentlyObserving = true;\n}\nfunction stopObservingMutations() {\n  flushObserver();\n  observer.disconnect();\n  currentlyObserving = false;\n}\nvar recordQueue = [];\nvar willProcessRecordQueue = false;\nfunction flushObserver() {\n  recordQueue = recordQueue.concat(observer.takeRecords());\n  if (recordQueue.length && !willProcessRecordQueue) {\n    willProcessRecordQueue = true;\n    queueMicrotask(() => {\n      processRecordQueue();\n      willProcessRecordQueue = false;\n    });\n  }\n}\nfunction processRecordQueue() {\n  onMutate(recordQueue);\n  recordQueue.length = 0;\n}\nfunction mutateDom(callback) {\n  if (!currentlyObserving)\n    return callback();\n  stopObservingMutations();\n  let result = callback();\n  startObservingMutations();\n  return result;\n}\nvar isCollecting = false;\nvar deferredMutations = [];\nfunction deferMutations() {\n  isCollecting = true;\n}\nfunction flushAndStopDeferringMutations() {\n  isCollecting = false;\n  onMutate(deferredMutations);\n  deferredMutations = [];\n}\nfunction onMutate(mutations) {\n  if (isCollecting) {\n    deferredMutations = deferredMutations.concat(mutations);\n    return;\n  }\n  let addedNodes = [];\n  let removedNodes = [];\n  let addedAttributes = /* @__PURE__ */ new Map();\n  let removedAttributes = /* @__PURE__ */ new Map();\n  for (let i = 0; i < mutations.length; i++) {\n    if (mutations[i].target._x_ignoreMutationObserver)\n      continue;\n    if (mutations[i].type === \"childList\") {\n      mutations[i].addedNodes.forEach((node) => node.nodeType === 1 && addedNodes.push(node));\n      mutations[i].removedNodes.forEach((node) => node.nodeType === 1 && removedNodes.push(node));\n    }\n    if (mutations[i].type === \"attributes\") {\n      let el = mutations[i].target;\n      let name = mutations[i].attributeName;\n      let oldValue = mutations[i].oldValue;\n      let add2 = () => {\n        if (!addedAttributes.has(el))\n          addedAttributes.set(el, []);\n        addedAttributes.get(el).push({ name, value: el.getAttribute(name) });\n      };\n      let remove = () => {\n        if (!removedAttributes.has(el))\n          removedAttributes.set(el, []);\n        removedAttributes.get(el).push(name);\n      };\n      if (el.hasAttribute(name) && oldValue === null) {\n        add2();\n      } else if (el.hasAttribute(name)) {\n        remove();\n        add2();\n      } else {\n        remove();\n      }\n    }\n  }\n  removedAttributes.forEach((attrs, el) => {\n    cleanupAttributes(el, attrs);\n  });\n  addedAttributes.forEach((attrs, el) => {\n    onAttributeAddeds.forEach((i) => i(el, attrs));\n  });\n  for (let node of removedNodes) {\n    if (addedNodes.includes(node))\n      continue;\n    onElRemoveds.forEach((i) => i(node));\n    destroyTree(node);\n  }\n  addedNodes.forEach((node) => {\n    node._x_ignoreSelf = true;\n    node._x_ignore = true;\n  });\n  for (let node of addedNodes) {\n    if (removedNodes.includes(node))\n      continue;\n    if (!node.isConnected)\n      continue;\n    delete node._x_ignoreSelf;\n    delete node._x_ignore;\n    onElAddeds.forEach((i) => i(node));\n    node._x_ignore = true;\n    node._x_ignoreSelf = true;\n  }\n  addedNodes.forEach((node) => {\n    delete node._x_ignoreSelf;\n    delete node._x_ignore;\n  });\n  addedNodes = null;\n  removedNodes = null;\n  addedAttributes = null;\n  removedAttributes = null;\n}\n\n// packages/alpinejs/src/scope.js\nfunction scope(node) {\n  return mergeProxies(closestDataStack(node));\n}\nfunction addScopeToNode(node, data2, referenceNode) {\n  node._x_dataStack = [data2, ...closestDataStack(referenceNode || node)];\n  return () => {\n    node._x_dataStack = node._x_dataStack.filter((i) => i !== data2);\n  };\n}\nfunction closestDataStack(node) {\n  if (node._x_dataStack)\n    return node._x_dataStack;\n  if (typeof ShadowRoot === \"function\" && node instanceof ShadowRoot) {\n    return closestDataStack(node.host);\n  }\n  if (!node.parentNode) {\n    return [];\n  }\n  return closestDataStack(node.parentNode);\n}\nfunction mergeProxies(objects) {\n  let thisProxy = new Proxy({}, {\n    ownKeys: () => {\n      return Array.from(new Set(objects.flatMap((i) => Object.keys(i))));\n    },\n    has: (target, name) => {\n      return objects.some((obj) => obj.hasOwnProperty(name));\n    },\n    get: (target, name) => {\n      return (objects.find((obj) => {\n        if (obj.hasOwnProperty(name)) {\n          let descriptor = Object.getOwnPropertyDescriptor(obj, name);\n          if (descriptor.get && descriptor.get._x_alreadyBound || descriptor.set && descriptor.set._x_alreadyBound) {\n            return true;\n          }\n          if ((descriptor.get || descriptor.set) && descriptor.enumerable) {\n            let getter = descriptor.get;\n            let setter = descriptor.set;\n            let property = descriptor;\n            getter = getter && getter.bind(thisProxy);\n            setter = setter && setter.bind(thisProxy);\n            if (getter)\n              getter._x_alreadyBound = true;\n            if (setter)\n              setter._x_alreadyBound = true;\n            Object.defineProperty(obj, name, {\n              ...property,\n              get: getter,\n              set: setter\n            });\n          }\n          return true;\n        }\n        return false;\n      }) || {})[name];\n    },\n    set: (target, name, value) => {\n      let closestObjectWithKey = objects.find((obj) => obj.hasOwnProperty(name));\n      if (closestObjectWithKey) {\n        closestObjectWithKey[name] = value;\n      } else {\n        objects[objects.length - 1][name] = value;\n      }\n      return true;\n    }\n  });\n  return thisProxy;\n}\n\n// packages/alpinejs/src/interceptor.js\nfunction initInterceptors2(data2) {\n  let isObject2 = (val) => typeof val === \"object\" && !Array.isArray(val) && val !== null;\n  let recurse = (obj, basePath = \"\") => {\n    Object.entries(Object.getOwnPropertyDescriptors(obj)).forEach(([key, { value, enumerable }]) => {\n      if (enumerable === false || value === void 0)\n        return;\n      let path = basePath === \"\" ? key : `${basePath}.${key}`;\n      if (typeof value === \"object\" && value !== null && value._x_interceptor) {\n        obj[key] = value.initialize(data2, path, key);\n      } else {\n        if (isObject2(value) && value !== obj && !(value instanceof Element)) {\n          recurse(value, path);\n        }\n      }\n    });\n  };\n  return recurse(data2);\n}\nfunction interceptor(callback, mutateObj = () => {\n}) {\n  let obj = {\n    initialValue: void 0,\n    _x_interceptor: true,\n    initialize(data2, path, key) {\n      return callback(this.initialValue, () => get(data2, path), (value) => set(data2, path, value), path, key);\n    }\n  };\n  mutateObj(obj);\n  return (initialValue) => {\n    if (typeof initialValue === \"object\" && initialValue !== null && initialValue._x_interceptor) {\n      let initialize = obj.initialize.bind(obj);\n      obj.initialize = (data2, path, key) => {\n        let innerValue = initialValue.initialize(data2, path, key);\n        obj.initialValue = innerValue;\n        return initialize(data2, path, key);\n      };\n    } else {\n      obj.initialValue = initialValue;\n    }\n    return obj;\n  };\n}\nfunction get(obj, path) {\n  return path.split(\".\").reduce((carry, segment) => carry[segment], obj);\n}\nfunction set(obj, path, value) {\n  if (typeof path === \"string\")\n    path = path.split(\".\");\n  if (path.length === 1)\n    obj[path[0]] = value;\n  else if (path.length === 0)\n    throw error;\n  else {\n    if (obj[path[0]])\n      return set(obj[path[0]], path.slice(1), value);\n    else {\n      obj[path[0]] = {};\n      return set(obj[path[0]], path.slice(1), value);\n    }\n  }\n}\n\n// packages/alpinejs/src/magics.js\nvar magics = {};\nfunction magic(name, callback) {\n  magics[name] = callback;\n}\nfunction injectMagics(obj, el) {\n  Object.entries(magics).forEach(([name, callback]) => {\n    let memoizedUtilities = null;\n    function getUtilities() {\n      if (memoizedUtilities) {\n        return memoizedUtilities;\n      } else {\n        let [utilities, cleanup2] = getElementBoundUtilities(el);\n        memoizedUtilities = { interceptor, ...utilities };\n        onElRemoved(el, cleanup2);\n        return memoizedUtilities;\n      }\n    }\n    Object.defineProperty(obj, `$${name}`, {\n      get() {\n        return callback(el, getUtilities());\n      },\n      enumerable: false\n    });\n  });\n  return obj;\n}\n\n// packages/alpinejs/src/utils/error.js\nfunction tryCatch(el, expression, callback, ...args) {\n  try {\n    return callback(...args);\n  } catch (e) {\n    handleError(e, el, expression);\n  }\n}\nfunction handleError(error2, el, expression = void 0) {\n  Object.assign(error2, { el, expression });\n  console.warn(`Alpine Expression Error: ${error2.message}\n\n${expression ? 'Expression: \"' + expression + '\"\\n\\n' : \"\"}`, el);\n  setTimeout(() => {\n    throw error2;\n  }, 0);\n}\n\n// packages/alpinejs/src/evaluator.js\nvar shouldAutoEvaluateFunctions = true;\nfunction dontAutoEvaluateFunctions(callback) {\n  let cache = shouldAutoEvaluateFunctions;\n  shouldAutoEvaluateFunctions = false;\n  let result = callback();\n  shouldAutoEvaluateFunctions = cache;\n  return result;\n}\nfunction evaluate(el, expression, extras = {}) {\n  let result;\n  evaluateLater(el, expression)((value) => result = value, extras);\n  return result;\n}\nfunction evaluateLater(...args) {\n  return theEvaluatorFunction(...args);\n}\nvar theEvaluatorFunction = normalEvaluator;\nfunction setEvaluator(newEvaluator) {\n  theEvaluatorFunction = newEvaluator;\n}\nfunction normalEvaluator(el, expression) {\n  let overriddenMagics = {};\n  injectMagics(overriddenMagics, el);\n  let dataStack = [overriddenMagics, ...closestDataStack(el)];\n  let evaluator = typeof expression === \"function\" ? generateEvaluatorFromFunction(dataStack, expression) : generateEvaluatorFromString(dataStack, expression, el);\n  return tryCatch.bind(null, el, expression, evaluator);\n}\nfunction generateEvaluatorFromFunction(dataStack, func) {\n  return (receiver = () => {\n  }, { scope: scope2 = {}, params = [] } = {}) => {\n    let result = func.apply(mergeProxies([scope2, ...dataStack]), params);\n    runIfTypeOfFunction(receiver, result);\n  };\n}\nvar evaluatorMemo = {};\nfunction generateFunctionFromString(expression, el) {\n  if (evaluatorMemo[expression]) {\n    return evaluatorMemo[expression];\n  }\n  let AsyncFunction = Object.getPrototypeOf(async function() {\n  }).constructor;\n  let rightSideSafeExpression = /^[\\n\\s]*if.*\\(.*\\)/.test(expression.trim()) || /^(let|const)\\s/.test(expression.trim()) ? `(async()=>{ ${expression} })()` : expression;\n  const safeAsyncFunction = () => {\n    try {\n      return new AsyncFunction([\"__self\", \"scope\"], `with (scope) { __self.result = ${rightSideSafeExpression} }; __self.finished = true; return __self.result;`);\n    } catch (error2) {\n      handleError(error2, el, expression);\n      return Promise.resolve();\n    }\n  };\n  let func = safeAsyncFunction();\n  evaluatorMemo[expression] = func;\n  return func;\n}\nfunction generateEvaluatorFromString(dataStack, expression, el) {\n  let func = generateFunctionFromString(expression, el);\n  return (receiver = () => {\n  }, { scope: scope2 = {}, params = [] } = {}) => {\n    func.result = void 0;\n    func.finished = false;\n    let completeScope = mergeProxies([scope2, ...dataStack]);\n    if (typeof func === \"function\") {\n      let promise = func(func, completeScope).catch((error2) => handleError(error2, el, expression));\n      if (func.finished) {\n        runIfTypeOfFunction(receiver, func.result, completeScope, params, el);\n        func.result = void 0;\n      } else {\n        promise.then((result) => {\n          runIfTypeOfFunction(receiver, result, completeScope, params, el);\n        }).catch((error2) => handleError(error2, el, expression)).finally(() => func.result = void 0);\n      }\n    }\n  };\n}\nfunction runIfTypeOfFunction(receiver, value, scope2, params, el) {\n  if (shouldAutoEvaluateFunctions && typeof value === \"function\") {\n    let result = value.apply(scope2, params);\n    if (result instanceof Promise) {\n      result.then((i) => runIfTypeOfFunction(receiver, i, scope2, params)).catch((error2) => handleError(error2, el, value));\n    } else {\n      receiver(result);\n    }\n  } else if (typeof value === \"object\" && value instanceof Promise) {\n    value.then((i) => receiver(i));\n  } else {\n    receiver(value);\n  }\n}\n\n// packages/alpinejs/src/directives.js\nvar prefixAsString = \"x-\";\nfunction prefix(subject = \"\") {\n  return prefixAsString + subject;\n}\nfunction setPrefix(newPrefix) {\n  prefixAsString = newPrefix;\n}\nvar directiveHandlers = {};\nfunction directive(name, callback) {\n  directiveHandlers[name] = callback;\n  return {\n    before(directive2) {\n      if (!directiveHandlers[directive2]) {\n        console.warn(\n          \"Cannot find directive `${directive}`. `${name}` will use the default order of execution\"\n        );\n        return;\n      }\n      const pos = directiveOrder.indexOf(directive2);\n      directiveOrder.splice(pos >= 0 ? pos : directiveOrder.indexOf(\"DEFAULT\"), 0, name);\n    }\n  };\n}\nfunction directives(el, attributes, originalAttributeOverride) {\n  attributes = Array.from(attributes);\n  if (el._x_virtualDirectives) {\n    let vAttributes = Object.entries(el._x_virtualDirectives).map(([name, value]) => ({ name, value }));\n    let staticAttributes = attributesOnly(vAttributes);\n    vAttributes = vAttributes.map((attribute) => {\n      if (staticAttributes.find((attr) => attr.name === attribute.name)) {\n        return {\n          name: `x-bind:${attribute.name}`,\n          value: `\"${attribute.value}\"`\n        };\n      }\n      return attribute;\n    });\n    attributes = attributes.concat(vAttributes);\n  }\n  let transformedAttributeMap = {};\n  let directives2 = attributes.map(toTransformedAttributes((newName, oldName) => transformedAttributeMap[newName] = oldName)).filter(outNonAlpineAttributes).map(toParsedDirectives(transformedAttributeMap, originalAttributeOverride)).sort(byPriority);\n  return directives2.map((directive2) => {\n    return getDirectiveHandler(el, directive2);\n  });\n}\nfunction attributesOnly(attributes) {\n  return Array.from(attributes).map(toTransformedAttributes()).filter((attr) => !outNonAlpineAttributes(attr));\n}\nvar isDeferringHandlers = false;\nvar directiveHandlerStacks = /* @__PURE__ */ new Map();\nvar currentHandlerStackKey = Symbol();\nfunction deferHandlingDirectives(callback) {\n  isDeferringHandlers = true;\n  let key = Symbol();\n  currentHandlerStackKey = key;\n  directiveHandlerStacks.set(key, []);\n  let flushHandlers = () => {\n    while (directiveHandlerStacks.get(key).length)\n      directiveHandlerStacks.get(key).shift()();\n    directiveHandlerStacks.delete(key);\n  };\n  let stopDeferring = () => {\n    isDeferringHandlers = false;\n    flushHandlers();\n  };\n  callback(flushHandlers);\n  stopDeferring();\n}\nfunction getElementBoundUtilities(el) {\n  let cleanups = [];\n  let cleanup2 = (callback) => cleanups.push(callback);\n  let [effect3, cleanupEffect] = elementBoundEffect(el);\n  cleanups.push(cleanupEffect);\n  let utilities = {\n    Alpine: alpine_default,\n    effect: effect3,\n    cleanup: cleanup2,\n    evaluateLater: evaluateLater.bind(evaluateLater, el),\n    evaluate: evaluate.bind(evaluate, el)\n  };\n  let doCleanup = () => cleanups.forEach((i) => i());\n  return [utilities, doCleanup];\n}\nfunction getDirectiveHandler(el, directive2) {\n  let noop = () => {\n  };\n  let handler4 = directiveHandlers[directive2.type] || noop;\n  let [utilities, cleanup2] = getElementBoundUtilities(el);\n  onAttributeRemoved(el, directive2.original, cleanup2);\n  let fullHandler = () => {\n    if (el._x_ignore || el._x_ignoreSelf)\n      return;\n    handler4.inline && handler4.inline(el, directive2, utilities);\n    handler4 = handler4.bind(handler4, el, directive2, utilities);\n    isDeferringHandlers ? directiveHandlerStacks.get(currentHandlerStackKey).push(handler4) : handler4();\n  };\n  fullHandler.runCleanups = cleanup2;\n  return fullHandler;\n}\nvar startingWith = (subject, replacement) => ({ name, value }) => {\n  if (name.startsWith(subject))\n    name = name.replace(subject, replacement);\n  return { name, value };\n};\nvar into = (i) => i;\nfunction toTransformedAttributes(callback = () => {\n}) {\n  return ({ name, value }) => {\n    let { name: newName, value: newValue } = attributeTransformers.reduce((carry, transform) => {\n      return transform(carry);\n    }, { name, value });\n    if (newName !== name)\n      callback(newName, name);\n    return { name: newName, value: newValue };\n  };\n}\nvar attributeTransformers = [];\nfunction mapAttributes(callback) {\n  attributeTransformers.push(callback);\n}\nfunction outNonAlpineAttributes({ name }) {\n  return alpineAttributeRegex().test(name);\n}\nvar alpineAttributeRegex = () => new RegExp(`^${prefixAsString}([^:^.]+)\\\\b`);\nfunction toParsedDirectives(transformedAttributeMap, originalAttributeOverride) {\n  return ({ name, value }) => {\n    let typeMatch = name.match(alpineAttributeRegex());\n    let valueMatch = name.match(/:([a-zA-Z0-9\\-:]+)/);\n    let modifiers = name.match(/\\.[^.\\]]+(?=[^\\]]*$)/g) || [];\n    let original = originalAttributeOverride || transformedAttributeMap[name] || name;\n    return {\n      type: typeMatch ? typeMatch[1] : null,\n      value: valueMatch ? valueMatch[1] : null,\n      modifiers: modifiers.map((i) => i.replace(\".\", \"\")),\n      expression: value,\n      original\n    };\n  };\n}\nvar DEFAULT = \"DEFAULT\";\nvar directiveOrder = [\n  \"ignore\",\n  \"ref\",\n  \"data\",\n  \"id\",\n  \"bind\",\n  \"init\",\n  \"for\",\n  \"model\",\n  \"modelable\",\n  \"transition\",\n  \"show\",\n  \"if\",\n  DEFAULT,\n  \"teleport\"\n];\nfunction byPriority(a, b) {\n  let typeA = directiveOrder.indexOf(a.type) === -1 ? DEFAULT : a.type;\n  let typeB = directiveOrder.indexOf(b.type) === -1 ? DEFAULT : b.type;\n  return directiveOrder.indexOf(typeA) - directiveOrder.indexOf(typeB);\n}\n\n// packages/alpinejs/src/nextTick.js\nvar tickStack = [];\nvar isHolding = false;\nfunction nextTick(callback = () => {\n}) {\n  queueMicrotask(() => {\n    isHolding || setTimeout(() => {\n      releaseNextTicks();\n    });\n  });\n  return new Promise((res) => {\n    tickStack.push(() => {\n      callback();\n      res();\n    });\n  });\n}\nfunction releaseNextTicks() {\n  isHolding = false;\n  while (tickStack.length)\n    tickStack.shift()();\n}\nfunction holdNextTicks() {\n  isHolding = true;\n}\n\n// packages/alpinejs/src/utils/classes.js\nfunction setClasses(el, value) {\n  if (Array.isArray(value)) {\n    return setClassesFromString(el, value.join(\" \"));\n  } else if (typeof value === \"object\" && value !== null) {\n    return setClassesFromObject(el, value);\n  } else if (typeof value === \"function\") {\n    return setClasses(el, value());\n  }\n  return setClassesFromString(el, value);\n}\nfunction setClassesFromString(el, classString) {\n  let split = (classString2) => classString2.split(\" \").filter(Boolean);\n  let missingClasses = (classString2) => classString2.split(\" \").filter((i) => !el.classList.contains(i)).filter(Boolean);\n  let addClassesAndReturnUndo = (classes) => {\n    el.classList.add(...classes);\n    return () => {\n      el.classList.remove(...classes);\n    };\n  };\n  classString = classString === true ? classString = \"\" : classString || \"\";\n  return addClassesAndReturnUndo(missingClasses(classString));\n}\nfunction setClassesFromObject(el, classObject) {\n  let split = (classString) => classString.split(\" \").filter(Boolean);\n  let forAdd = Object.entries(classObject).flatMap(([classString, bool]) => bool ? split(classString) : false).filter(Boolean);\n  let forRemove = Object.entries(classObject).flatMap(([classString, bool]) => !bool ? split(classString) : false).filter(Boolean);\n  let added = [];\n  let removed = [];\n  forRemove.forEach((i) => {\n    if (el.classList.contains(i)) {\n      el.classList.remove(i);\n      removed.push(i);\n    }\n  });\n  forAdd.forEach((i) => {\n    if (!el.classList.contains(i)) {\n      el.classList.add(i);\n      added.push(i);\n    }\n  });\n  return () => {\n    removed.forEach((i) => el.classList.add(i));\n    added.forEach((i) => el.classList.remove(i));\n  };\n}\n\n// packages/alpinejs/src/utils/styles.js\nfunction setStyles(el, value) {\n  if (typeof value === \"object\" && value !== null) {\n    return setStylesFromObject(el, value);\n  }\n  return setStylesFromString(el, value);\n}\nfunction setStylesFromObject(el, value) {\n  let previousStyles = {};\n  Object.entries(value).forEach(([key, value2]) => {\n    previousStyles[key] = el.style[key];\n    if (!key.startsWith(\"--\")) {\n      key = kebabCase(key);\n    }\n    el.style.setProperty(key, value2);\n  });\n  setTimeout(() => {\n    if (el.style.length === 0) {\n      el.removeAttribute(\"style\");\n    }\n  });\n  return () => {\n    setStyles(el, previousStyles);\n  };\n}\nfunction setStylesFromString(el, value) {\n  let cache = el.getAttribute(\"style\", value);\n  el.setAttribute(\"style\", value);\n  return () => {\n    el.setAttribute(\"style\", cache || \"\");\n  };\n}\nfunction kebabCase(subject) {\n  return subject.replace(/([a-z])([A-Z])/g, \"$1-$2\").toLowerCase();\n}\n\n// packages/alpinejs/src/utils/once.js\nfunction once(callback, fallback = () => {\n}) {\n  let called = false;\n  return function() {\n    if (!called) {\n      called = true;\n      callback.apply(this, arguments);\n    } else {\n      fallback.apply(this, arguments);\n    }\n  };\n}\n\n// packages/alpinejs/src/directives/x-transition.js\ndirective(\"transition\", (el, { value, modifiers, expression }, { evaluate: evaluate2 }) => {\n  if (typeof expression === \"function\")\n    expression = evaluate2(expression);\n  if (expression === false)\n    return;\n  if (!expression || typeof expression === \"boolean\") {\n    registerTransitionsFromHelper(el, modifiers, value);\n  } else {\n    registerTransitionsFromClassString(el, expression, value);\n  }\n});\nfunction registerTransitionsFromClassString(el, classString, stage) {\n  registerTransitionObject(el, setClasses, \"\");\n  let directiveStorageMap = {\n    \"enter\": (classes) => {\n      el._x_transition.enter.during = classes;\n    },\n    \"enter-start\": (classes) => {\n      el._x_transition.enter.start = classes;\n    },\n    \"enter-end\": (classes) => {\n      el._x_transition.enter.end = classes;\n    },\n    \"leave\": (classes) => {\n      el._x_transition.leave.during = classes;\n    },\n    \"leave-start\": (classes) => {\n      el._x_transition.leave.start = classes;\n    },\n    \"leave-end\": (classes) => {\n      el._x_transition.leave.end = classes;\n    }\n  };\n  directiveStorageMap[stage](classString);\n}\nfunction registerTransitionsFromHelper(el, modifiers, stage) {\n  registerTransitionObject(el, setStyles);\n  let doesntSpecify = !modifiers.includes(\"in\") && !modifiers.includes(\"out\") && !stage;\n  let transitioningIn = doesntSpecify || modifiers.includes(\"in\") || [\"enter\"].includes(stage);\n  let transitioningOut = doesntSpecify || modifiers.includes(\"out\") || [\"leave\"].includes(stage);\n  if (modifiers.includes(\"in\") && !doesntSpecify) {\n    modifiers = modifiers.filter((i, index) => index < modifiers.indexOf(\"out\"));\n  }\n  if (modifiers.includes(\"out\") && !doesntSpecify) {\n    modifiers = modifiers.filter((i, index) => index > modifiers.indexOf(\"out\"));\n  }\n  let wantsAll = !modifiers.includes(\"opacity\") && !modifiers.includes(\"scale\");\n  let wantsOpacity = wantsAll || modifiers.includes(\"opacity\");\n  let wantsScale = wantsAll || modifiers.includes(\"scale\");\n  let opacityValue = wantsOpacity ? 0 : 1;\n  let scaleValue = wantsScale ? modifierValue(modifiers, \"scale\", 95) / 100 : 1;\n  let delay = modifierValue(modifiers, \"delay\", 0) / 1e3;\n  let origin = modifierValue(modifiers, \"origin\", \"center\");\n  let property = \"opacity, transform\";\n  let durationIn = modifierValue(modifiers, \"duration\", 150) / 1e3;\n  let durationOut = modifierValue(modifiers, \"duration\", 75) / 1e3;\n  let easing = `cubic-bezier(0.4, 0.0, 0.2, 1)`;\n  if (transitioningIn) {\n    el._x_transition.enter.during = {\n      transformOrigin: origin,\n      transitionDelay: `${delay}s`,\n      transitionProperty: property,\n      transitionDuration: `${durationIn}s`,\n      transitionTimingFunction: easing\n    };\n    el._x_transition.enter.start = {\n      opacity: opacityValue,\n      transform: `scale(${scaleValue})`\n    };\n    el._x_transition.enter.end = {\n      opacity: 1,\n      transform: `scale(1)`\n    };\n  }\n  if (transitioningOut) {\n    el._x_transition.leave.during = {\n      transformOrigin: origin,\n      transitionDelay: `${delay}s`,\n      transitionProperty: property,\n      transitionDuration: `${durationOut}s`,\n      transitionTimingFunction: easing\n    };\n    el._x_transition.leave.start = {\n      opacity: 1,\n      transform: `scale(1)`\n    };\n    el._x_transition.leave.end = {\n      opacity: opacityValue,\n      transform: `scale(${scaleValue})`\n    };\n  }\n}\nfunction registerTransitionObject(el, setFunction, defaultValue = {}) {\n  if (!el._x_transition)\n    el._x_transition = {\n      enter: { during: defaultValue, start: defaultValue, end: defaultValue },\n      leave: { during: defaultValue, start: defaultValue, end: defaultValue },\n      in(before = () => {\n      }, after = () => {\n      }) {\n        transition(el, setFunction, {\n          during: this.enter.during,\n          start: this.enter.start,\n          end: this.enter.end\n        }, before, after);\n      },\n      out(before = () => {\n      }, after = () => {\n      }) {\n        transition(el, setFunction, {\n          during: this.leave.during,\n          start: this.leave.start,\n          end: this.leave.end\n        }, before, after);\n      }\n    };\n}\nwindow.Element.prototype._x_toggleAndCascadeWithTransitions = function(el, value, show, hide) {\n  const nextTick2 = document.visibilityState === \"visible\" ? requestAnimationFrame : setTimeout;\n  let clickAwayCompatibleShow = () => nextTick2(show);\n  if (value) {\n    if (el._x_transition && (el._x_transition.enter || el._x_transition.leave)) {\n      el._x_transition.enter && (Object.entries(el._x_transition.enter.during).length || Object.entries(el._x_transition.enter.start).length || Object.entries(el._x_transition.enter.end).length) ? el._x_transition.in(show) : clickAwayCompatibleShow();\n    } else {\n      el._x_transition ? el._x_transition.in(show) : clickAwayCompatibleShow();\n    }\n    return;\n  }\n  el._x_hidePromise = el._x_transition ? new Promise((resolve, reject) => {\n    el._x_transition.out(() => {\n    }, () => resolve(hide));\n    el._x_transitioning.beforeCancel(() => reject({ isFromCancelledTransition: true }));\n  }) : Promise.resolve(hide);\n  queueMicrotask(() => {\n    let closest = closestHide(el);\n    if (closest) {\n      if (!closest._x_hideChildren)\n        closest._x_hideChildren = [];\n      closest._x_hideChildren.push(el);\n    } else {\n      nextTick2(() => {\n        let hideAfterChildren = (el2) => {\n          let carry = Promise.all([\n            el2._x_hidePromise,\n            ...(el2._x_hideChildren || []).map(hideAfterChildren)\n          ]).then(([i]) => i());\n          delete el2._x_hidePromise;\n          delete el2._x_hideChildren;\n          return carry;\n        };\n        hideAfterChildren(el).catch((e) => {\n          if (!e.isFromCancelledTransition)\n            throw e;\n        });\n      });\n    }\n  });\n};\nfunction closestHide(el) {\n  let parent = el.parentNode;\n  if (!parent)\n    return;\n  return parent._x_hidePromise ? parent : closestHide(parent);\n}\nfunction transition(el, setFunction, { during, start: start2, end } = {}, before = () => {\n}, after = () => {\n}) {\n  if (el._x_transitioning)\n    el._x_transitioning.cancel();\n  if (Object.keys(during).length === 0 && Object.keys(start2).length === 0 && Object.keys(end).length === 0) {\n    before();\n    after();\n    return;\n  }\n  let undoStart, undoDuring, undoEnd;\n  performTransition(el, {\n    start() {\n      undoStart = setFunction(el, start2);\n    },\n    during() {\n      undoDuring = setFunction(el, during);\n    },\n    before,\n    end() {\n      undoStart();\n      undoEnd = setFunction(el, end);\n    },\n    after,\n    cleanup() {\n      undoDuring();\n      undoEnd();\n    }\n  });\n}\nfunction performTransition(el, stages) {\n  let interrupted, reachedBefore, reachedEnd;\n  let finish = once(() => {\n    mutateDom(() => {\n      interrupted = true;\n      if (!reachedBefore)\n        stages.before();\n      if (!reachedEnd) {\n        stages.end();\n        releaseNextTicks();\n      }\n      stages.after();\n      if (el.isConnected)\n        stages.cleanup();\n      delete el._x_transitioning;\n    });\n  });\n  el._x_transitioning = {\n    beforeCancels: [],\n    beforeCancel(callback) {\n      this.beforeCancels.push(callback);\n    },\n    cancel: once(function() {\n      while (this.beforeCancels.length) {\n        this.beforeCancels.shift()();\n      }\n      ;\n      finish();\n    }),\n    finish\n  };\n  mutateDom(() => {\n    stages.start();\n    stages.during();\n  });\n  holdNextTicks();\n  requestAnimationFrame(() => {\n    if (interrupted)\n      return;\n    let duration = Number(getComputedStyle(el).transitionDuration.replace(/,.*/, \"\").replace(\"s\", \"\")) * 1e3;\n    let delay = Number(getComputedStyle(el).transitionDelay.replace(/,.*/, \"\").replace(\"s\", \"\")) * 1e3;\n    if (duration === 0)\n      duration = Number(getComputedStyle(el).animationDuration.replace(\"s\", \"\")) * 1e3;\n    mutateDom(() => {\n      stages.before();\n    });\n    reachedBefore = true;\n    requestAnimationFrame(() => {\n      if (interrupted)\n        return;\n      mutateDom(() => {\n        stages.end();\n      });\n      releaseNextTicks();\n      setTimeout(el._x_transitioning.finish, duration + delay);\n      reachedEnd = true;\n    });\n  });\n}\nfunction modifierValue(modifiers, key, fallback) {\n  if (modifiers.indexOf(key) === -1)\n    return fallback;\n  const rawValue = modifiers[modifiers.indexOf(key) + 1];\n  if (!rawValue)\n    return fallback;\n  if (key === \"scale\") {\n    if (isNaN(rawValue))\n      return fallback;\n  }\n  if (key === \"duration\" || key === \"delay\") {\n    let match = rawValue.match(/([0-9]+)ms/);\n    if (match)\n      return match[1];\n  }\n  if (key === \"origin\") {\n    if ([\"top\", \"right\", \"left\", \"center\", \"bottom\"].includes(modifiers[modifiers.indexOf(key) + 2])) {\n      return [rawValue, modifiers[modifiers.indexOf(key) + 2]].join(\" \");\n    }\n  }\n  return rawValue;\n}\n\n// packages/alpinejs/src/clone.js\nvar isCloning = false;\nfunction skipDuringClone(callback, fallback = () => {\n}) {\n  return (...args) => isCloning ? fallback(...args) : callback(...args);\n}\nfunction onlyDuringClone(callback) {\n  return (...args) => isCloning && callback(...args);\n}\nfunction cloneNode(from, to) {\n  if (from._x_dataStack) {\n    to._x_dataStack = from._x_dataStack;\n    to.setAttribute(\"data-has-alpine-state\", true);\n  }\n  isCloning = true;\n  dontRegisterReactiveSideEffects(() => {\n    initTree(to, (el, callback) => {\n      callback(el, () => {\n      });\n    });\n  });\n  isCloning = false;\n}\nvar isCloningLegacy = false;\nfunction clone(oldEl, newEl) {\n  if (!newEl._x_dataStack)\n    newEl._x_dataStack = oldEl._x_dataStack;\n  isCloning = true;\n  isCloningLegacy = true;\n  dontRegisterReactiveSideEffects(() => {\n    cloneTree(newEl);\n  });\n  isCloning = false;\n  isCloningLegacy = false;\n}\nfunction cloneTree(el) {\n  let hasRunThroughFirstEl = false;\n  let shallowWalker = (el2, callback) => {\n    walk(el2, (el3, skip) => {\n      if (hasRunThroughFirstEl && isRoot(el3))\n        return skip();\n      hasRunThroughFirstEl = true;\n      callback(el3, skip);\n    });\n  };\n  initTree(el, shallowWalker);\n}\nfunction dontRegisterReactiveSideEffects(callback) {\n  let cache = effect;\n  overrideEffect((callback2, el) => {\n    let storedEffect = cache(callback2);\n    release(storedEffect);\n    return () => {\n    };\n  });\n  callback();\n  overrideEffect(cache);\n}\nfunction shouldSkipRegisteringDataDuringClone(el) {\n  if (!isCloning)\n    return false;\n  if (isCloningLegacy)\n    return true;\n  return el.hasAttribute(\"data-has-alpine-state\");\n}\n\n// packages/alpinejs/src/utils/bind.js\nfunction bind(el, name, value, modifiers = []) {\n  if (!el._x_bindings)\n    el._x_bindings = reactive({});\n  el._x_bindings[name] = value;\n  name = modifiers.includes(\"camel\") ? camelCase(name) : name;\n  switch (name) {\n    case \"value\":\n      bindInputValue(el, value);\n      break;\n    case \"style\":\n      bindStyles(el, value);\n      break;\n    case \"class\":\n      bindClasses(el, value);\n      break;\n    case \"selected\":\n    case \"checked\":\n      bindAttributeAndProperty(el, name, value);\n      break;\n    default:\n      bindAttribute(el, name, value);\n      break;\n  }\n}\nfunction bindInputValue(el, value) {\n  if (el.type === \"radio\") {\n    if (el.attributes.value === void 0) {\n      el.value = value;\n    }\n    if (window.fromModel) {\n      el.checked = checkedAttrLooseCompare(el.value, value);\n    }\n  } else if (el.type === \"checkbox\") {\n    if (Number.isInteger(value)) {\n      el.value = value;\n    } else if (!Array.isArray(value) && typeof value !== \"boolean\" && ![null, void 0].includes(value)) {\n      el.value = String(value);\n    } else {\n      if (Array.isArray(value)) {\n        el.checked = value.some((val) => checkedAttrLooseCompare(val, el.value));\n      } else {\n        el.checked = !!value;\n      }\n    }\n  } else if (el.tagName === \"SELECT\") {\n    updateSelect(el, value);\n  } else {\n    if (el.value === value)\n      return;\n    el.value = value === void 0 ? \"\" : value;\n  }\n}\nfunction bindClasses(el, value) {\n  if (el._x_undoAddedClasses)\n    el._x_undoAddedClasses();\n  el._x_undoAddedClasses = setClasses(el, value);\n}\nfunction bindStyles(el, value) {\n  if (el._x_undoAddedStyles)\n    el._x_undoAddedStyles();\n  el._x_undoAddedStyles = setStyles(el, value);\n}\nfunction bindAttributeAndProperty(el, name, value) {\n  bindAttribute(el, name, value);\n  setPropertyIfChanged(el, name, value);\n}\nfunction bindAttribute(el, name, value) {\n  if ([null, void 0, false].includes(value) && attributeShouldntBePreservedIfFalsy(name)) {\n    el.removeAttribute(name);\n  } else {\n    if (isBooleanAttr(name))\n      value = name;\n    setIfChanged(el, name, value);\n  }\n}\nfunction setIfChanged(el, attrName, value) {\n  if (el.getAttribute(attrName) != value) {\n    el.setAttribute(attrName, value);\n  }\n}\nfunction setPropertyIfChanged(el, propName, value) {\n  if (el[propName] !== value) {\n    el[propName] = value;\n  }\n}\nfunction updateSelect(el, value) {\n  const arrayWrappedValue = [].concat(value).map((value2) => {\n    return value2 + \"\";\n  });\n  Array.from(el.options).forEach((option) => {\n    option.selected = arrayWrappedValue.includes(option.value);\n  });\n}\nfunction camelCase(subject) {\n  return subject.toLowerCase().replace(/-(\\w)/g, (match, char) => char.toUpperCase());\n}\nfunction checkedAttrLooseCompare(valueA, valueB) {\n  return valueA == valueB;\n}\nfunction isBooleanAttr(attrName) {\n  const booleanAttributes = [\n    \"disabled\",\n    \"checked\",\n    \"required\",\n    \"readonly\",\n    \"hidden\",\n    \"open\",\n    \"selected\",\n    \"autofocus\",\n    \"itemscope\",\n    \"multiple\",\n    \"novalidate\",\n    \"allowfullscreen\",\n    \"allowpaymentrequest\",\n    \"formnovalidate\",\n    \"autoplay\",\n    \"controls\",\n    \"loop\",\n    \"muted\",\n    \"playsinline\",\n    \"default\",\n    \"ismap\",\n    \"reversed\",\n    \"async\",\n    \"defer\",\n    \"nomodule\"\n  ];\n  return booleanAttributes.includes(attrName);\n}\nfunction attributeShouldntBePreservedIfFalsy(name) {\n  return ![\"aria-pressed\", \"aria-checked\", \"aria-expanded\", \"aria-selected\"].includes(name);\n}\nfunction getBinding(el, name, fallback) {\n  if (el._x_bindings && el._x_bindings[name] !== void 0)\n    return el._x_bindings[name];\n  return getAttributeBinding(el, name, fallback);\n}\nfunction extractProp(el, name, fallback, extract = true) {\n  if (el._x_bindings && el._x_bindings[name] !== void 0)\n    return el._x_bindings[name];\n  if (el._x_inlineBindings && el._x_inlineBindings[name] !== void 0) {\n    let binding = el._x_inlineBindings[name];\n    binding.extract = extract;\n    return dontAutoEvaluateFunctions(() => {\n      return evaluate(el, binding.expression);\n    });\n  }\n  return getAttributeBinding(el, name, fallback);\n}\nfunction getAttributeBinding(el, name, fallback) {\n  let attr = el.getAttribute(name);\n  if (attr === null)\n    return typeof fallback === \"function\" ? fallback() : fallback;\n  if (attr === \"\")\n    return true;\n  if (isBooleanAttr(name)) {\n    return !![name, \"true\"].includes(attr);\n  }\n  return attr;\n}\n\n// packages/alpinejs/src/utils/debounce.js\nfunction debounce(func, wait) {\n  var timeout;\n  return function() {\n    var context = this, args = arguments;\n    var later = function() {\n      timeout = null;\n      func.apply(context, args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n// packages/alpinejs/src/utils/throttle.js\nfunction throttle(func, limit) {\n  let inThrottle;\n  return function() {\n    let context = this, args = arguments;\n    if (!inThrottle) {\n      func.apply(context, args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n// packages/alpinejs/src/entangle.js\nfunction entangle({ get: outerGet, set: outerSet }, { get: innerGet, set: innerSet }) {\n  let firstRun = true;\n  let outerHash, innerHash, outerHashLatest, innerHashLatest;\n  let reference = effect(() => {\n    let outer, inner;\n    if (firstRun) {\n      outer = outerGet();\n      innerSet(JSON.parse(JSON.stringify(outer)));\n      inner = innerGet();\n      firstRun = false;\n    } else {\n      outer = outerGet();\n      inner = innerGet();\n      outerHashLatest = JSON.stringify(outer);\n      innerHashLatest = JSON.stringify(inner);\n      if (outerHashLatest !== outerHash) {\n        inner = innerGet();\n        innerSet(outer);\n        inner = outer;\n      } else {\n        outerSet(JSON.parse(innerHashLatest ?? null));\n        outer = inner;\n      }\n    }\n    outerHash = JSON.stringify(outer);\n    innerHash = JSON.stringify(inner);\n  });\n  return () => {\n    release(reference);\n  };\n}\n\n// packages/alpinejs/src/plugin.js\nfunction plugin(callback) {\n  let callbacks = Array.isArray(callback) ? callback : [callback];\n  callbacks.forEach((i) => i(alpine_default));\n}\n\n// packages/alpinejs/src/store.js\nvar stores = {};\nvar isReactive = false;\nfunction store(name, value) {\n  if (!isReactive) {\n    stores = reactive(stores);\n    isReactive = true;\n  }\n  if (value === void 0) {\n    return stores[name];\n  }\n  stores[name] = value;\n  if (typeof value === \"object\" && value !== null && value.hasOwnProperty(\"init\") && typeof value.init === \"function\") {\n    stores[name].init();\n  }\n  initInterceptors2(stores[name]);\n}\nfunction getStores() {\n  return stores;\n}\n\n// packages/alpinejs/src/binds.js\nvar binds = {};\nfunction bind2(name, bindings) {\n  let getBindings = typeof bindings !== \"function\" ? () => bindings : bindings;\n  if (name instanceof Element) {\n    return applyBindingsObject(name, getBindings());\n  } else {\n    binds[name] = getBindings;\n  }\n  return () => {\n  };\n}\nfunction injectBindingProviders(obj) {\n  Object.entries(binds).forEach(([name, callback]) => {\n    Object.defineProperty(obj, name, {\n      get() {\n        return (...args) => {\n          return callback(...args);\n        };\n      }\n    });\n  });\n  return obj;\n}\nfunction applyBindingsObject(el, obj, original) {\n  let cleanupRunners = [];\n  while (cleanupRunners.length)\n    cleanupRunners.pop()();\n  let attributes = Object.entries(obj).map(([name, value]) => ({ name, value }));\n  let staticAttributes = attributesOnly(attributes);\n  attributes = attributes.map((attribute) => {\n    if (staticAttributes.find((attr) => attr.name === attribute.name)) {\n      return {\n        name: `x-bind:${attribute.name}`,\n        value: `\"${attribute.value}\"`\n      };\n    }\n    return attribute;\n  });\n  directives(el, attributes, original).map((handle) => {\n    cleanupRunners.push(handle.runCleanups);\n    handle();\n  });\n  return () => {\n    while (cleanupRunners.length)\n      cleanupRunners.pop()();\n  };\n}\n\n// packages/alpinejs/src/datas.js\nvar datas = {};\nfunction data(name, callback) {\n  datas[name] = callback;\n}\nfunction injectDataProviders(obj, context) {\n  Object.entries(datas).forEach(([name, callback]) => {\n    Object.defineProperty(obj, name, {\n      get() {\n        return (...args) => {\n          return callback.bind(context)(...args);\n        };\n      },\n      enumerable: false\n    });\n  });\n  return obj;\n}\n\n// packages/alpinejs/src/alpine.js\nvar Alpine = {\n  get reactive() {\n    return reactive;\n  },\n  get release() {\n    return release;\n  },\n  get effect() {\n    return effect;\n  },\n  get raw() {\n    return raw;\n  },\n  version: \"3.13.0\",\n  flushAndStopDeferringMutations,\n  dontAutoEvaluateFunctions,\n  disableEffectScheduling,\n  startObservingMutations,\n  stopObservingMutations,\n  setReactivityEngine,\n  onAttributeRemoved,\n  onAttributesAdded,\n  closestDataStack,\n  skipDuringClone,\n  onlyDuringClone,\n  addRootSelector,\n  addInitSelector,\n  addScopeToNode,\n  deferMutations,\n  mapAttributes,\n  evaluateLater,\n  interceptInit,\n  setEvaluator,\n  mergeProxies,\n  extractProp,\n  findClosest,\n  onElRemoved,\n  closestRoot,\n  destroyTree,\n  interceptor,\n  // INTERNAL: not public API and is subject to change without major release.\n  transition,\n  // INTERNAL\n  setStyles,\n  // INTERNAL\n  mutateDom,\n  directive,\n  entangle,\n  throttle,\n  debounce,\n  evaluate,\n  initTree,\n  nextTick,\n  prefixed: prefix,\n  prefix: setPrefix,\n  plugin,\n  magic,\n  store,\n  start,\n  clone,\n  // INTERNAL\n  cloneNode,\n  // INTERNAL\n  bound: getBinding,\n  $data: scope,\n  walk,\n  data,\n  bind: bind2\n};\nvar alpine_default = Alpine;\n\n// node_modules/@vue/shared/dist/shared.esm-bundler.js\nfunction makeMap(str, expectsLowerCase) {\n  const map = /* @__PURE__ */ Object.create(null);\n  const list = str.split(\",\");\n  for (let i = 0; i < list.length; i++) {\n    map[list[i]] = true;\n  }\n  return expectsLowerCase ? (val) => !!map[val.toLowerCase()] : (val) => !!map[val];\n}\nvar specialBooleanAttrs = `itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly`;\nvar isBooleanAttr2 = /* @__PURE__ */ makeMap(specialBooleanAttrs + `,async,autofocus,autoplay,controls,default,defer,disabled,hidden,loop,open,required,reversed,scoped,seamless,checked,muted,multiple,selected`);\nvar EMPTY_OBJ =  true ? Object.freeze({}) : 0;\nvar EMPTY_ARR =  true ? Object.freeze([]) : 0;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar hasOwn = (val, key) => hasOwnProperty.call(val, key);\nvar isArray = Array.isArray;\nvar isMap = (val) => toTypeString(val) === \"[object Map]\";\nvar isString = (val) => typeof val === \"string\";\nvar isSymbol = (val) => typeof val === \"symbol\";\nvar isObject = (val) => val !== null && typeof val === \"object\";\nvar objectToString = Object.prototype.toString;\nvar toTypeString = (value) => objectToString.call(value);\nvar toRawType = (value) => {\n  return toTypeString(value).slice(8, -1);\n};\nvar isIntegerKey = (key) => isString(key) && key !== \"NaN\" && key[0] !== \"-\" && \"\" + parseInt(key, 10) === key;\nvar cacheStringFunction = (fn) => {\n  const cache = /* @__PURE__ */ Object.create(null);\n  return (str) => {\n    const hit = cache[str];\n    return hit || (cache[str] = fn(str));\n  };\n};\nvar camelizeRE = /-(\\w)/g;\nvar camelize = cacheStringFunction((str) => {\n  return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : \"\");\n});\nvar hyphenateRE = /\\B([A-Z])/g;\nvar hyphenate = cacheStringFunction((str) => str.replace(hyphenateRE, \"-$1\").toLowerCase());\nvar capitalize = cacheStringFunction((str) => str.charAt(0).toUpperCase() + str.slice(1));\nvar toHandlerKey = cacheStringFunction((str) => str ? `on${capitalize(str)}` : ``);\nvar hasChanged = (value, oldValue) => value !== oldValue && (value === value || oldValue === oldValue);\n\n// node_modules/@vue/reactivity/dist/reactivity.esm-bundler.js\nvar targetMap = /* @__PURE__ */ new WeakMap();\nvar effectStack = [];\nvar activeEffect;\nvar ITERATE_KEY = Symbol( true ? \"iterate\" : 0);\nvar MAP_KEY_ITERATE_KEY = Symbol( true ? \"Map key iterate\" : 0);\nfunction isEffect(fn) {\n  return fn && fn._isEffect === true;\n}\nfunction effect2(fn, options = EMPTY_OBJ) {\n  if (isEffect(fn)) {\n    fn = fn.raw;\n  }\n  const effect3 = createReactiveEffect(fn, options);\n  if (!options.lazy) {\n    effect3();\n  }\n  return effect3;\n}\nfunction stop(effect3) {\n  if (effect3.active) {\n    cleanup(effect3);\n    if (effect3.options.onStop) {\n      effect3.options.onStop();\n    }\n    effect3.active = false;\n  }\n}\nvar uid = 0;\nfunction createReactiveEffect(fn, options) {\n  const effect3 = function reactiveEffect() {\n    if (!effect3.active) {\n      return fn();\n    }\n    if (!effectStack.includes(effect3)) {\n      cleanup(effect3);\n      try {\n        enableTracking();\n        effectStack.push(effect3);\n        activeEffect = effect3;\n        return fn();\n      } finally {\n        effectStack.pop();\n        resetTracking();\n        activeEffect = effectStack[effectStack.length - 1];\n      }\n    }\n  };\n  effect3.id = uid++;\n  effect3.allowRecurse = !!options.allowRecurse;\n  effect3._isEffect = true;\n  effect3.active = true;\n  effect3.raw = fn;\n  effect3.deps = [];\n  effect3.options = options;\n  return effect3;\n}\nfunction cleanup(effect3) {\n  const { deps } = effect3;\n  if (deps.length) {\n    for (let i = 0; i < deps.length; i++) {\n      deps[i].delete(effect3);\n    }\n    deps.length = 0;\n  }\n}\nvar shouldTrack = true;\nvar trackStack = [];\nfunction pauseTracking() {\n  trackStack.push(shouldTrack);\n  shouldTrack = false;\n}\nfunction enableTracking() {\n  trackStack.push(shouldTrack);\n  shouldTrack = true;\n}\nfunction resetTracking() {\n  const last = trackStack.pop();\n  shouldTrack = last === void 0 ? true : last;\n}\nfunction track(target, type, key) {\n  if (!shouldTrack || activeEffect === void 0) {\n    return;\n  }\n  let depsMap = targetMap.get(target);\n  if (!depsMap) {\n    targetMap.set(target, depsMap = /* @__PURE__ */ new Map());\n  }\n  let dep = depsMap.get(key);\n  if (!dep) {\n    depsMap.set(key, dep = /* @__PURE__ */ new Set());\n  }\n  if (!dep.has(activeEffect)) {\n    dep.add(activeEffect);\n    activeEffect.deps.push(dep);\n    if (activeEffect.options.onTrack) {\n      activeEffect.options.onTrack({\n        effect: activeEffect,\n        target,\n        type,\n        key\n      });\n    }\n  }\n}\nfunction trigger(target, type, key, newValue, oldValue, oldTarget) {\n  const depsMap = targetMap.get(target);\n  if (!depsMap) {\n    return;\n  }\n  const effects = /* @__PURE__ */ new Set();\n  const add2 = (effectsToAdd) => {\n    if (effectsToAdd) {\n      effectsToAdd.forEach((effect3) => {\n        if (effect3 !== activeEffect || effect3.allowRecurse) {\n          effects.add(effect3);\n        }\n      });\n    }\n  };\n  if (type === \"clear\") {\n    depsMap.forEach(add2);\n  } else if (key === \"length\" && isArray(target)) {\n    depsMap.forEach((dep, key2) => {\n      if (key2 === \"length\" || key2 >= newValue) {\n        add2(dep);\n      }\n    });\n  } else {\n    if (key !== void 0) {\n      add2(depsMap.get(key));\n    }\n    switch (type) {\n      case \"add\":\n        if (!isArray(target)) {\n          add2(depsMap.get(ITERATE_KEY));\n          if (isMap(target)) {\n            add2(depsMap.get(MAP_KEY_ITERATE_KEY));\n          }\n        } else if (isIntegerKey(key)) {\n          add2(depsMap.get(\"length\"));\n        }\n        break;\n      case \"delete\":\n        if (!isArray(target)) {\n          add2(depsMap.get(ITERATE_KEY));\n          if (isMap(target)) {\n            add2(depsMap.get(MAP_KEY_ITERATE_KEY));\n          }\n        }\n        break;\n      case \"set\":\n        if (isMap(target)) {\n          add2(depsMap.get(ITERATE_KEY));\n        }\n        break;\n    }\n  }\n  const run = (effect3) => {\n    if (effect3.options.onTrigger) {\n      effect3.options.onTrigger({\n        effect: effect3,\n        target,\n        key,\n        type,\n        newValue,\n        oldValue,\n        oldTarget\n      });\n    }\n    if (effect3.options.scheduler) {\n      effect3.options.scheduler(effect3);\n    } else {\n      effect3();\n    }\n  };\n  effects.forEach(run);\n}\nvar isNonTrackableKeys = /* @__PURE__ */ makeMap(`__proto__,__v_isRef,__isVue`);\nvar builtInSymbols = new Set(Object.getOwnPropertyNames(Symbol).map((key) => Symbol[key]).filter(isSymbol));\nvar get2 = /* @__PURE__ */ createGetter();\nvar readonlyGet = /* @__PURE__ */ createGetter(true);\nvar arrayInstrumentations = /* @__PURE__ */ createArrayInstrumentations();\nfunction createArrayInstrumentations() {\n  const instrumentations = {};\n  [\"includes\", \"indexOf\", \"lastIndexOf\"].forEach((key) => {\n    instrumentations[key] = function(...args) {\n      const arr = toRaw(this);\n      for (let i = 0, l = this.length; i < l; i++) {\n        track(arr, \"get\", i + \"\");\n      }\n      const res = arr[key](...args);\n      if (res === -1 || res === false) {\n        return arr[key](...args.map(toRaw));\n      } else {\n        return res;\n      }\n    };\n  });\n  [\"push\", \"pop\", \"shift\", \"unshift\", \"splice\"].forEach((key) => {\n    instrumentations[key] = function(...args) {\n      pauseTracking();\n      const res = toRaw(this)[key].apply(this, args);\n      resetTracking();\n      return res;\n    };\n  });\n  return instrumentations;\n}\nfunction createGetter(isReadonly = false, shallow = false) {\n  return function get3(target, key, receiver) {\n    if (key === \"__v_isReactive\") {\n      return !isReadonly;\n    } else if (key === \"__v_isReadonly\") {\n      return isReadonly;\n    } else if (key === \"__v_raw\" && receiver === (isReadonly ? shallow ? shallowReadonlyMap : readonlyMap : shallow ? shallowReactiveMap : reactiveMap).get(target)) {\n      return target;\n    }\n    const targetIsArray = isArray(target);\n    if (!isReadonly && targetIsArray && hasOwn(arrayInstrumentations, key)) {\n      return Reflect.get(arrayInstrumentations, key, receiver);\n    }\n    const res = Reflect.get(target, key, receiver);\n    if (isSymbol(key) ? builtInSymbols.has(key) : isNonTrackableKeys(key)) {\n      return res;\n    }\n    if (!isReadonly) {\n      track(target, \"get\", key);\n    }\n    if (shallow) {\n      return res;\n    }\n    if (isRef(res)) {\n      const shouldUnwrap = !targetIsArray || !isIntegerKey(key);\n      return shouldUnwrap ? res.value : res;\n    }\n    if (isObject(res)) {\n      return isReadonly ? readonly(res) : reactive2(res);\n    }\n    return res;\n  };\n}\nvar set2 = /* @__PURE__ */ createSetter();\nfunction createSetter(shallow = false) {\n  return function set3(target, key, value, receiver) {\n    let oldValue = target[key];\n    if (!shallow) {\n      value = toRaw(value);\n      oldValue = toRaw(oldValue);\n      if (!isArray(target) && isRef(oldValue) && !isRef(value)) {\n        oldValue.value = value;\n        return true;\n      }\n    }\n    const hadKey = isArray(target) && isIntegerKey(key) ? Number(key) < target.length : hasOwn(target, key);\n    const result = Reflect.set(target, key, value, receiver);\n    if (target === toRaw(receiver)) {\n      if (!hadKey) {\n        trigger(target, \"add\", key, value);\n      } else if (hasChanged(value, oldValue)) {\n        trigger(target, \"set\", key, value, oldValue);\n      }\n    }\n    return result;\n  };\n}\nfunction deleteProperty(target, key) {\n  const hadKey = hasOwn(target, key);\n  const oldValue = target[key];\n  const result = Reflect.deleteProperty(target, key);\n  if (result && hadKey) {\n    trigger(target, \"delete\", key, void 0, oldValue);\n  }\n  return result;\n}\nfunction has(target, key) {\n  const result = Reflect.has(target, key);\n  if (!isSymbol(key) || !builtInSymbols.has(key)) {\n    track(target, \"has\", key);\n  }\n  return result;\n}\nfunction ownKeys(target) {\n  track(target, \"iterate\", isArray(target) ? \"length\" : ITERATE_KEY);\n  return Reflect.ownKeys(target);\n}\nvar mutableHandlers = {\n  get: get2,\n  set: set2,\n  deleteProperty,\n  has,\n  ownKeys\n};\nvar readonlyHandlers = {\n  get: readonlyGet,\n  set(target, key) {\n    if (true) {\n      console.warn(`Set operation on key \"${String(key)}\" failed: target is readonly.`, target);\n    }\n    return true;\n  },\n  deleteProperty(target, key) {\n    if (true) {\n      console.warn(`Delete operation on key \"${String(key)}\" failed: target is readonly.`, target);\n    }\n    return true;\n  }\n};\nvar toReactive = (value) => isObject(value) ? reactive2(value) : value;\nvar toReadonly = (value) => isObject(value) ? readonly(value) : value;\nvar toShallow = (value) => value;\nvar getProto = (v) => Reflect.getPrototypeOf(v);\nfunction get$1(target, key, isReadonly = false, isShallow = false) {\n  target = target[\n    \"__v_raw\"\n    /* RAW */\n  ];\n  const rawTarget = toRaw(target);\n  const rawKey = toRaw(key);\n  if (key !== rawKey) {\n    !isReadonly && track(rawTarget, \"get\", key);\n  }\n  !isReadonly && track(rawTarget, \"get\", rawKey);\n  const { has: has2 } = getProto(rawTarget);\n  const wrap = isShallow ? toShallow : isReadonly ? toReadonly : toReactive;\n  if (has2.call(rawTarget, key)) {\n    return wrap(target.get(key));\n  } else if (has2.call(rawTarget, rawKey)) {\n    return wrap(target.get(rawKey));\n  } else if (target !== rawTarget) {\n    target.get(key);\n  }\n}\nfunction has$1(key, isReadonly = false) {\n  const target = this[\n    \"__v_raw\"\n    /* RAW */\n  ];\n  const rawTarget = toRaw(target);\n  const rawKey = toRaw(key);\n  if (key !== rawKey) {\n    !isReadonly && track(rawTarget, \"has\", key);\n  }\n  !isReadonly && track(rawTarget, \"has\", rawKey);\n  return key === rawKey ? target.has(key) : target.has(key) || target.has(rawKey);\n}\nfunction size(target, isReadonly = false) {\n  target = target[\n    \"__v_raw\"\n    /* RAW */\n  ];\n  !isReadonly && track(toRaw(target), \"iterate\", ITERATE_KEY);\n  return Reflect.get(target, \"size\", target);\n}\nfunction add(value) {\n  value = toRaw(value);\n  const target = toRaw(this);\n  const proto = getProto(target);\n  const hadKey = proto.has.call(target, value);\n  if (!hadKey) {\n    target.add(value);\n    trigger(target, \"add\", value, value);\n  }\n  return this;\n}\nfunction set$1(key, value) {\n  value = toRaw(value);\n  const target = toRaw(this);\n  const { has: has2, get: get3 } = getProto(target);\n  let hadKey = has2.call(target, key);\n  if (!hadKey) {\n    key = toRaw(key);\n    hadKey = has2.call(target, key);\n  } else if (true) {\n    checkIdentityKeys(target, has2, key);\n  }\n  const oldValue = get3.call(target, key);\n  target.set(key, value);\n  if (!hadKey) {\n    trigger(target, \"add\", key, value);\n  } else if (hasChanged(value, oldValue)) {\n    trigger(target, \"set\", key, value, oldValue);\n  }\n  return this;\n}\nfunction deleteEntry(key) {\n  const target = toRaw(this);\n  const { has: has2, get: get3 } = getProto(target);\n  let hadKey = has2.call(target, key);\n  if (!hadKey) {\n    key = toRaw(key);\n    hadKey = has2.call(target, key);\n  } else if (true) {\n    checkIdentityKeys(target, has2, key);\n  }\n  const oldValue = get3 ? get3.call(target, key) : void 0;\n  const result = target.delete(key);\n  if (hadKey) {\n    trigger(target, \"delete\", key, void 0, oldValue);\n  }\n  return result;\n}\nfunction clear() {\n  const target = toRaw(this);\n  const hadItems = target.size !== 0;\n  const oldTarget =  true ? isMap(target) ? new Map(target) : new Set(target) : 0;\n  const result = target.clear();\n  if (hadItems) {\n    trigger(target, \"clear\", void 0, void 0, oldTarget);\n  }\n  return result;\n}\nfunction createForEach(isReadonly, isShallow) {\n  return function forEach(callback, thisArg) {\n    const observed = this;\n    const target = observed[\n      \"__v_raw\"\n      /* RAW */\n    ];\n    const rawTarget = toRaw(target);\n    const wrap = isShallow ? toShallow : isReadonly ? toReadonly : toReactive;\n    !isReadonly && track(rawTarget, \"iterate\", ITERATE_KEY);\n    return target.forEach((value, key) => {\n      return callback.call(thisArg, wrap(value), wrap(key), observed);\n    });\n  };\n}\nfunction createIterableMethod(method, isReadonly, isShallow) {\n  return function(...args) {\n    const target = this[\n      \"__v_raw\"\n      /* RAW */\n    ];\n    const rawTarget = toRaw(target);\n    const targetIsMap = isMap(rawTarget);\n    const isPair = method === \"entries\" || method === Symbol.iterator && targetIsMap;\n    const isKeyOnly = method === \"keys\" && targetIsMap;\n    const innerIterator = target[method](...args);\n    const wrap = isShallow ? toShallow : isReadonly ? toReadonly : toReactive;\n    !isReadonly && track(rawTarget, \"iterate\", isKeyOnly ? MAP_KEY_ITERATE_KEY : ITERATE_KEY);\n    return {\n      // iterator protocol\n      next() {\n        const { value, done } = innerIterator.next();\n        return done ? { value, done } : {\n          value: isPair ? [wrap(value[0]), wrap(value[1])] : wrap(value),\n          done\n        };\n      },\n      // iterable protocol\n      [Symbol.iterator]() {\n        return this;\n      }\n    };\n  };\n}\nfunction createReadonlyMethod(type) {\n  return function(...args) {\n    if (true) {\n      const key = args[0] ? `on key \"${args[0]}\" ` : ``;\n      console.warn(`${capitalize(type)} operation ${key}failed: target is readonly.`, toRaw(this));\n    }\n    return type === \"delete\" ? false : this;\n  };\n}\nfunction createInstrumentations() {\n  const mutableInstrumentations2 = {\n    get(key) {\n      return get$1(this, key);\n    },\n    get size() {\n      return size(this);\n    },\n    has: has$1,\n    add,\n    set: set$1,\n    delete: deleteEntry,\n    clear,\n    forEach: createForEach(false, false)\n  };\n  const shallowInstrumentations2 = {\n    get(key) {\n      return get$1(this, key, false, true);\n    },\n    get size() {\n      return size(this);\n    },\n    has: has$1,\n    add,\n    set: set$1,\n    delete: deleteEntry,\n    clear,\n    forEach: createForEach(false, true)\n  };\n  const readonlyInstrumentations2 = {\n    get(key) {\n      return get$1(this, key, true);\n    },\n    get size() {\n      return size(this, true);\n    },\n    has(key) {\n      return has$1.call(this, key, true);\n    },\n    add: createReadonlyMethod(\n      \"add\"\n      /* ADD */\n    ),\n    set: createReadonlyMethod(\n      \"set\"\n      /* SET */\n    ),\n    delete: createReadonlyMethod(\n      \"delete\"\n      /* DELETE */\n    ),\n    clear: createReadonlyMethod(\n      \"clear\"\n      /* CLEAR */\n    ),\n    forEach: createForEach(true, false)\n  };\n  const shallowReadonlyInstrumentations2 = {\n    get(key) {\n      return get$1(this, key, true, true);\n    },\n    get size() {\n      return size(this, true);\n    },\n    has(key) {\n      return has$1.call(this, key, true);\n    },\n    add: createReadonlyMethod(\n      \"add\"\n      /* ADD */\n    ),\n    set: createReadonlyMethod(\n      \"set\"\n      /* SET */\n    ),\n    delete: createReadonlyMethod(\n      \"delete\"\n      /* DELETE */\n    ),\n    clear: createReadonlyMethod(\n      \"clear\"\n      /* CLEAR */\n    ),\n    forEach: createForEach(true, true)\n  };\n  const iteratorMethods = [\"keys\", \"values\", \"entries\", Symbol.iterator];\n  iteratorMethods.forEach((method) => {\n    mutableInstrumentations2[method] = createIterableMethod(method, false, false);\n    readonlyInstrumentations2[method] = createIterableMethod(method, true, false);\n    shallowInstrumentations2[method] = createIterableMethod(method, false, true);\n    shallowReadonlyInstrumentations2[method] = createIterableMethod(method, true, true);\n  });\n  return [\n    mutableInstrumentations2,\n    readonlyInstrumentations2,\n    shallowInstrumentations2,\n    shallowReadonlyInstrumentations2\n  ];\n}\nvar [mutableInstrumentations, readonlyInstrumentations, shallowInstrumentations, shallowReadonlyInstrumentations] = /* @__PURE__ */ createInstrumentations();\nfunction createInstrumentationGetter(isReadonly, shallow) {\n  const instrumentations = shallow ? isReadonly ? shallowReadonlyInstrumentations : shallowInstrumentations : isReadonly ? readonlyInstrumentations : mutableInstrumentations;\n  return (target, key, receiver) => {\n    if (key === \"__v_isReactive\") {\n      return !isReadonly;\n    } else if (key === \"__v_isReadonly\") {\n      return isReadonly;\n    } else if (key === \"__v_raw\") {\n      return target;\n    }\n    return Reflect.get(hasOwn(instrumentations, key) && key in target ? instrumentations : target, key, receiver);\n  };\n}\nvar mutableCollectionHandlers = {\n  get: /* @__PURE__ */ createInstrumentationGetter(false, false)\n};\nvar readonlyCollectionHandlers = {\n  get: /* @__PURE__ */ createInstrumentationGetter(true, false)\n};\nfunction checkIdentityKeys(target, has2, key) {\n  const rawKey = toRaw(key);\n  if (rawKey !== key && has2.call(target, rawKey)) {\n    const type = toRawType(target);\n    console.warn(`Reactive ${type} contains both the raw and reactive versions of the same object${type === `Map` ? ` as keys` : ``}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`);\n  }\n}\nvar reactiveMap = /* @__PURE__ */ new WeakMap();\nvar shallowReactiveMap = /* @__PURE__ */ new WeakMap();\nvar readonlyMap = /* @__PURE__ */ new WeakMap();\nvar shallowReadonlyMap = /* @__PURE__ */ new WeakMap();\nfunction targetTypeMap(rawType) {\n  switch (rawType) {\n    case \"Object\":\n    case \"Array\":\n      return 1;\n    case \"Map\":\n    case \"Set\":\n    case \"WeakMap\":\n    case \"WeakSet\":\n      return 2;\n    default:\n      return 0;\n  }\n}\nfunction getTargetType(value) {\n  return value[\n    \"__v_skip\"\n    /* SKIP */\n  ] || !Object.isExtensible(value) ? 0 : targetTypeMap(toRawType(value));\n}\nfunction reactive2(target) {\n  if (target && target[\n    \"__v_isReadonly\"\n    /* IS_READONLY */\n  ]) {\n    return target;\n  }\n  return createReactiveObject(target, false, mutableHandlers, mutableCollectionHandlers, reactiveMap);\n}\nfunction readonly(target) {\n  return createReactiveObject(target, true, readonlyHandlers, readonlyCollectionHandlers, readonlyMap);\n}\nfunction createReactiveObject(target, isReadonly, baseHandlers, collectionHandlers, proxyMap) {\n  if (!isObject(target)) {\n    if (true) {\n      console.warn(`value cannot be made reactive: ${String(target)}`);\n    }\n    return target;\n  }\n  if (target[\n    \"__v_raw\"\n    /* RAW */\n  ] && !(isReadonly && target[\n    \"__v_isReactive\"\n    /* IS_REACTIVE */\n  ])) {\n    return target;\n  }\n  const existingProxy = proxyMap.get(target);\n  if (existingProxy) {\n    return existingProxy;\n  }\n  const targetType = getTargetType(target);\n  if (targetType === 0) {\n    return target;\n  }\n  const proxy = new Proxy(target, targetType === 2 ? collectionHandlers : baseHandlers);\n  proxyMap.set(target, proxy);\n  return proxy;\n}\nfunction toRaw(observed) {\n  return observed && toRaw(observed[\n    \"__v_raw\"\n    /* RAW */\n  ]) || observed;\n}\nfunction isRef(r) {\n  return Boolean(r && r.__v_isRef === true);\n}\n\n// packages/alpinejs/src/magics/$nextTick.js\nmagic(\"nextTick\", () => nextTick);\n\n// packages/alpinejs/src/magics/$dispatch.js\nmagic(\"dispatch\", (el) => dispatch.bind(dispatch, el));\n\n// packages/alpinejs/src/magics/$watch.js\nmagic(\"watch\", (el, { evaluateLater: evaluateLater2, effect: effect3 }) => (key, callback) => {\n  let evaluate2 = evaluateLater2(key);\n  let firstTime = true;\n  let oldValue;\n  let effectReference = effect3(() => evaluate2((value) => {\n    JSON.stringify(value);\n    if (!firstTime) {\n      queueMicrotask(() => {\n        callback(value, oldValue);\n        oldValue = value;\n      });\n    } else {\n      oldValue = value;\n    }\n    firstTime = false;\n  }));\n  el._x_effects.delete(effectReference);\n});\n\n// packages/alpinejs/src/magics/$store.js\nmagic(\"store\", getStores);\n\n// packages/alpinejs/src/magics/$data.js\nmagic(\"data\", (el) => scope(el));\n\n// packages/alpinejs/src/magics/$root.js\nmagic(\"root\", (el) => closestRoot(el));\n\n// packages/alpinejs/src/magics/$refs.js\nmagic(\"refs\", (el) => {\n  if (el._x_refs_proxy)\n    return el._x_refs_proxy;\n  el._x_refs_proxy = mergeProxies(getArrayOfRefObject(el));\n  return el._x_refs_proxy;\n});\nfunction getArrayOfRefObject(el) {\n  let refObjects = [];\n  let currentEl = el;\n  while (currentEl) {\n    if (currentEl._x_refs)\n      refObjects.push(currentEl._x_refs);\n    currentEl = currentEl.parentNode;\n  }\n  return refObjects;\n}\n\n// packages/alpinejs/src/ids.js\nvar globalIdMemo = {};\nfunction findAndIncrementId(name) {\n  if (!globalIdMemo[name])\n    globalIdMemo[name] = 0;\n  return ++globalIdMemo[name];\n}\nfunction closestIdRoot(el, name) {\n  return findClosest(el, (element) => {\n    if (element._x_ids && element._x_ids[name])\n      return true;\n  });\n}\nfunction setIdRoot(el, name) {\n  if (!el._x_ids)\n    el._x_ids = {};\n  if (!el._x_ids[name])\n    el._x_ids[name] = findAndIncrementId(name);\n}\n\n// packages/alpinejs/src/magics/$id.js\nmagic(\"id\", (el) => (name, key = null) => {\n  let root = closestIdRoot(el, name);\n  let id = root ? root._x_ids[name] : findAndIncrementId(name);\n  return key ? `${name}-${id}-${key}` : `${name}-${id}`;\n});\n\n// packages/alpinejs/src/magics/$el.js\nmagic(\"el\", (el) => el);\n\n// packages/alpinejs/src/magics/index.js\nwarnMissingPluginMagic(\"Focus\", \"focus\", \"focus\");\nwarnMissingPluginMagic(\"Persist\", \"persist\", \"persist\");\nfunction warnMissingPluginMagic(name, magicName, slug) {\n  magic(magicName, (el) => warn(`You can't use [$${directiveName}] without first installing the \"${name}\" plugin here: https://alpinejs.dev/plugins/${slug}`, el));\n}\n\n// packages/alpinejs/src/directives/x-modelable.js\ndirective(\"modelable\", (el, { expression }, { effect: effect3, evaluateLater: evaluateLater2, cleanup: cleanup2 }) => {\n  let func = evaluateLater2(expression);\n  let innerGet = () => {\n    let result;\n    func((i) => result = i);\n    return result;\n  };\n  let evaluateInnerSet = evaluateLater2(`${expression} = __placeholder`);\n  let innerSet = (val) => evaluateInnerSet(() => {\n  }, { scope: { \"__placeholder\": val } });\n  let initialValue = innerGet();\n  innerSet(initialValue);\n  queueMicrotask(() => {\n    if (!el._x_model)\n      return;\n    el._x_removeModelListeners[\"default\"]();\n    let outerGet = el._x_model.get;\n    let outerSet = el._x_model.set;\n    let releaseEntanglement = entangle(\n      {\n        get() {\n          return outerGet();\n        },\n        set(value) {\n          outerSet(value);\n        }\n      },\n      {\n        get() {\n          return innerGet();\n        },\n        set(value) {\n          innerSet(value);\n        }\n      }\n    );\n    cleanup2(releaseEntanglement);\n  });\n});\n\n// packages/alpinejs/src/directives/x-teleport.js\nvar teleportContainerDuringClone = document.createElement(\"div\");\ndirective(\"teleport\", (el, { modifiers, expression }, { cleanup: cleanup2 }) => {\n  if (el.tagName.toLowerCase() !== \"template\")\n    warn(\"x-teleport can only be used on a <template> tag\", el);\n  let target = skipDuringClone(() => {\n    return document.querySelector(expression);\n  }, () => {\n    return teleportContainerDuringClone;\n  })();\n  if (!target)\n    warn(`Cannot find x-teleport element for selector: \"${expression}\"`);\n  let clone2 = el.content.cloneNode(true).firstElementChild;\n  el._x_teleport = clone2;\n  clone2._x_teleportBack = el;\n  if (el._x_forwardEvents) {\n    el._x_forwardEvents.forEach((eventName) => {\n      clone2.addEventListener(eventName, (e) => {\n        e.stopPropagation();\n        el.dispatchEvent(new e.constructor(e.type, e));\n      });\n    });\n  }\n  addScopeToNode(clone2, {}, el);\n  mutateDom(() => {\n    if (modifiers.includes(\"prepend\")) {\n      target.parentNode.insertBefore(clone2, target);\n    } else if (modifiers.includes(\"append\")) {\n      target.parentNode.insertBefore(clone2, target.nextSibling);\n    } else {\n      target.appendChild(clone2);\n    }\n    initTree(clone2);\n    clone2._x_ignore = true;\n  });\n  cleanup2(() => clone2.remove());\n});\n\n// packages/alpinejs/src/directives/x-ignore.js\nvar handler = () => {\n};\nhandler.inline = (el, { modifiers }, { cleanup: cleanup2 }) => {\n  modifiers.includes(\"self\") ? el._x_ignoreSelf = true : el._x_ignore = true;\n  cleanup2(() => {\n    modifiers.includes(\"self\") ? delete el._x_ignoreSelf : delete el._x_ignore;\n  });\n};\ndirective(\"ignore\", handler);\n\n// packages/alpinejs/src/directives/x-effect.js\ndirective(\"effect\", (el, { expression }, { effect: effect3 }) => effect3(evaluateLater(el, expression)));\n\n// packages/alpinejs/src/utils/on.js\nfunction on(el, event, modifiers, callback) {\n  let listenerTarget = el;\n  let handler4 = (e) => callback(e);\n  let options = {};\n  let wrapHandler = (callback2, wrapper) => (e) => wrapper(callback2, e);\n  if (modifiers.includes(\"dot\"))\n    event = dotSyntax(event);\n  if (modifiers.includes(\"camel\"))\n    event = camelCase2(event);\n  if (modifiers.includes(\"passive\"))\n    options.passive = true;\n  if (modifiers.includes(\"capture\"))\n    options.capture = true;\n  if (modifiers.includes(\"window\"))\n    listenerTarget = window;\n  if (modifiers.includes(\"document\"))\n    listenerTarget = document;\n  if (modifiers.includes(\"debounce\")) {\n    let nextModifier = modifiers[modifiers.indexOf(\"debounce\") + 1] || \"invalid-wait\";\n    let wait = isNumeric(nextModifier.split(\"ms\")[0]) ? Number(nextModifier.split(\"ms\")[0]) : 250;\n    handler4 = debounce(handler4, wait);\n  }\n  if (modifiers.includes(\"throttle\")) {\n    let nextModifier = modifiers[modifiers.indexOf(\"throttle\") + 1] || \"invalid-wait\";\n    let wait = isNumeric(nextModifier.split(\"ms\")[0]) ? Number(nextModifier.split(\"ms\")[0]) : 250;\n    handler4 = throttle(handler4, wait);\n  }\n  if (modifiers.includes(\"prevent\"))\n    handler4 = wrapHandler(handler4, (next, e) => {\n      e.preventDefault();\n      next(e);\n    });\n  if (modifiers.includes(\"stop\"))\n    handler4 = wrapHandler(handler4, (next, e) => {\n      e.stopPropagation();\n      next(e);\n    });\n  if (modifiers.includes(\"self\"))\n    handler4 = wrapHandler(handler4, (next, e) => {\n      e.target === el && next(e);\n    });\n  if (modifiers.includes(\"away\") || modifiers.includes(\"outside\")) {\n    listenerTarget = document;\n    handler4 = wrapHandler(handler4, (next, e) => {\n      if (el.contains(e.target))\n        return;\n      if (e.target.isConnected === false)\n        return;\n      if (el.offsetWidth < 1 && el.offsetHeight < 1)\n        return;\n      if (el._x_isShown === false)\n        return;\n      next(e);\n    });\n  }\n  if (modifiers.includes(\"once\")) {\n    handler4 = wrapHandler(handler4, (next, e) => {\n      next(e);\n      listenerTarget.removeEventListener(event, handler4, options);\n    });\n  }\n  handler4 = wrapHandler(handler4, (next, e) => {\n    if (isKeyEvent(event)) {\n      if (isListeningForASpecificKeyThatHasntBeenPressed(e, modifiers)) {\n        return;\n      }\n    }\n    next(e);\n  });\n  listenerTarget.addEventListener(event, handler4, options);\n  return () => {\n    listenerTarget.removeEventListener(event, handler4, options);\n  };\n}\nfunction dotSyntax(subject) {\n  return subject.replace(/-/g, \".\");\n}\nfunction camelCase2(subject) {\n  return subject.toLowerCase().replace(/-(\\w)/g, (match, char) => char.toUpperCase());\n}\nfunction isNumeric(subject) {\n  return !Array.isArray(subject) && !isNaN(subject);\n}\nfunction kebabCase2(subject) {\n  if ([\" \", \"_\"].includes(\n    subject\n  ))\n    return subject;\n  return subject.replace(/([a-z])([A-Z])/g, \"$1-$2\").replace(/[_\\s]/, \"-\").toLowerCase();\n}\nfunction isKeyEvent(event) {\n  return [\"keydown\", \"keyup\"].includes(event);\n}\nfunction isListeningForASpecificKeyThatHasntBeenPressed(e, modifiers) {\n  let keyModifiers = modifiers.filter((i) => {\n    return ![\"window\", \"document\", \"prevent\", \"stop\", \"once\", \"capture\"].includes(i);\n  });\n  if (keyModifiers.includes(\"debounce\")) {\n    let debounceIndex = keyModifiers.indexOf(\"debounce\");\n    keyModifiers.splice(debounceIndex, isNumeric((keyModifiers[debounceIndex + 1] || \"invalid-wait\").split(\"ms\")[0]) ? 2 : 1);\n  }\n  if (keyModifiers.includes(\"throttle\")) {\n    let debounceIndex = keyModifiers.indexOf(\"throttle\");\n    keyModifiers.splice(debounceIndex, isNumeric((keyModifiers[debounceIndex + 1] || \"invalid-wait\").split(\"ms\")[0]) ? 2 : 1);\n  }\n  if (keyModifiers.length === 0)\n    return false;\n  if (keyModifiers.length === 1 && keyToModifiers(e.key).includes(keyModifiers[0]))\n    return false;\n  const systemKeyModifiers = [\"ctrl\", \"shift\", \"alt\", \"meta\", \"cmd\", \"super\"];\n  const selectedSystemKeyModifiers = systemKeyModifiers.filter((modifier) => keyModifiers.includes(modifier));\n  keyModifiers = keyModifiers.filter((i) => !selectedSystemKeyModifiers.includes(i));\n  if (selectedSystemKeyModifiers.length > 0) {\n    const activelyPressedKeyModifiers = selectedSystemKeyModifiers.filter((modifier) => {\n      if (modifier === \"cmd\" || modifier === \"super\")\n        modifier = \"meta\";\n      return e[`${modifier}Key`];\n    });\n    if (activelyPressedKeyModifiers.length === selectedSystemKeyModifiers.length) {\n      if (keyToModifiers(e.key).includes(keyModifiers[0]))\n        return false;\n    }\n  }\n  return true;\n}\nfunction keyToModifiers(key) {\n  if (!key)\n    return [];\n  key = kebabCase2(key);\n  let modifierToKeyMap = {\n    \"ctrl\": \"control\",\n    \"slash\": \"/\",\n    \"space\": \" \",\n    \"spacebar\": \" \",\n    \"cmd\": \"meta\",\n    \"esc\": \"escape\",\n    \"up\": \"arrow-up\",\n    \"down\": \"arrow-down\",\n    \"left\": \"arrow-left\",\n    \"right\": \"arrow-right\",\n    \"period\": \".\",\n    \"equal\": \"=\",\n    \"minus\": \"-\",\n    \"underscore\": \"_\"\n  };\n  modifierToKeyMap[key] = key;\n  return Object.keys(modifierToKeyMap).map((modifier) => {\n    if (modifierToKeyMap[modifier] === key)\n      return modifier;\n  }).filter((modifier) => modifier);\n}\n\n// packages/alpinejs/src/directives/x-model.js\ndirective(\"model\", (el, { modifiers, expression }, { effect: effect3, cleanup: cleanup2 }) => {\n  let scopeTarget = el;\n  if (modifiers.includes(\"parent\")) {\n    scopeTarget = el.parentNode;\n  }\n  let evaluateGet = evaluateLater(scopeTarget, expression);\n  let evaluateSet;\n  if (typeof expression === \"string\") {\n    evaluateSet = evaluateLater(scopeTarget, `${expression} = __placeholder`);\n  } else if (typeof expression === \"function\" && typeof expression() === \"string\") {\n    evaluateSet = evaluateLater(scopeTarget, `${expression()} = __placeholder`);\n  } else {\n    evaluateSet = () => {\n    };\n  }\n  let getValue = () => {\n    let result;\n    evaluateGet((value) => result = value);\n    return isGetterSetter(result) ? result.get() : result;\n  };\n  let setValue = (value) => {\n    let result;\n    evaluateGet((value2) => result = value2);\n    if (isGetterSetter(result)) {\n      result.set(value);\n    } else {\n      evaluateSet(() => {\n      }, {\n        scope: { \"__placeholder\": value }\n      });\n    }\n  };\n  if (typeof expression === \"string\" && el.type === \"radio\") {\n    mutateDom(() => {\n      if (!el.hasAttribute(\"name\"))\n        el.setAttribute(\"name\", expression);\n    });\n  }\n  var event = el.tagName.toLowerCase() === \"select\" || [\"checkbox\", \"radio\"].includes(el.type) || modifiers.includes(\"lazy\") ? \"change\" : \"input\";\n  let removeListener = isCloning ? () => {\n  } : on(el, event, modifiers, (e) => {\n    setValue(getInputValue(el, modifiers, e, getValue()));\n  });\n  if (modifiers.includes(\"fill\")) {\n    if ([null, \"\"].includes(getValue()) || el.type === \"checkbox\" && Array.isArray(getValue())) {\n      el.dispatchEvent(new Event(event, {}));\n    }\n  }\n  if (!el._x_removeModelListeners)\n    el._x_removeModelListeners = {};\n  el._x_removeModelListeners[\"default\"] = removeListener;\n  cleanup2(() => el._x_removeModelListeners[\"default\"]());\n  if (el.form) {\n    let removeResetListener = on(el.form, \"reset\", [], (e) => {\n      nextTick(() => el._x_model && el._x_model.set(el.value));\n    });\n    cleanup2(() => removeResetListener());\n  }\n  el._x_model = {\n    get() {\n      return getValue();\n    },\n    set(value) {\n      setValue(value);\n    }\n  };\n  el._x_forceModelUpdate = (value) => {\n    if (value === void 0 && typeof expression === \"string\" && expression.match(/\\./))\n      value = \"\";\n    window.fromModel = true;\n    mutateDom(() => bind(el, \"value\", value));\n    delete window.fromModel;\n  };\n  effect3(() => {\n    let value = getValue();\n    if (modifiers.includes(\"unintrusive\") && document.activeElement.isSameNode(el))\n      return;\n    el._x_forceModelUpdate(value);\n  });\n});\nfunction getInputValue(el, modifiers, event, currentValue) {\n  return mutateDom(() => {\n    if (event instanceof CustomEvent && event.detail !== void 0)\n      return event.detail ?? event.target.value;\n    else if (el.type === \"checkbox\") {\n      if (Array.isArray(currentValue)) {\n        let newValue = modifiers.includes(\"number\") ? safeParseNumber(event.target.value) : event.target.value;\n        return event.target.checked ? currentValue.concat([newValue]) : currentValue.filter((el2) => !checkedAttrLooseCompare2(el2, newValue));\n      } else {\n        return event.target.checked;\n      }\n    } else if (el.tagName.toLowerCase() === \"select\" && el.multiple) {\n      return modifiers.includes(\"number\") ? Array.from(event.target.selectedOptions).map((option) => {\n        let rawValue = option.value || option.text;\n        return safeParseNumber(rawValue);\n      }) : Array.from(event.target.selectedOptions).map((option) => {\n        return option.value || option.text;\n      });\n    } else {\n      let rawValue = event.target.value;\n      return modifiers.includes(\"number\") ? safeParseNumber(rawValue) : modifiers.includes(\"trim\") ? rawValue.trim() : rawValue;\n    }\n  });\n}\nfunction safeParseNumber(rawValue) {\n  let number = rawValue ? parseFloat(rawValue) : null;\n  return isNumeric2(number) ? number : rawValue;\n}\nfunction checkedAttrLooseCompare2(valueA, valueB) {\n  return valueA == valueB;\n}\nfunction isNumeric2(subject) {\n  return !Array.isArray(subject) && !isNaN(subject);\n}\nfunction isGetterSetter(value) {\n  return value !== null && typeof value === \"object\" && typeof value.get === \"function\" && typeof value.set === \"function\";\n}\n\n// packages/alpinejs/src/directives/x-cloak.js\ndirective(\"cloak\", (el) => queueMicrotask(() => mutateDom(() => el.removeAttribute(prefix(\"cloak\")))));\n\n// packages/alpinejs/src/directives/x-init.js\naddInitSelector(() => `[${prefix(\"init\")}]`);\ndirective(\"init\", skipDuringClone((el, { expression }, { evaluate: evaluate2 }) => {\n  if (typeof expression === \"string\") {\n    return !!expression.trim() && evaluate2(expression, {}, false);\n  }\n  return evaluate2(expression, {}, false);\n}));\n\n// packages/alpinejs/src/directives/x-text.js\ndirective(\"text\", (el, { expression }, { effect: effect3, evaluateLater: evaluateLater2 }) => {\n  let evaluate2 = evaluateLater2(expression);\n  effect3(() => {\n    evaluate2((value) => {\n      mutateDom(() => {\n        el.textContent = value;\n      });\n    });\n  });\n});\n\n// packages/alpinejs/src/directives/x-html.js\ndirective(\"html\", (el, { expression }, { effect: effect3, evaluateLater: evaluateLater2 }) => {\n  let evaluate2 = evaluateLater2(expression);\n  effect3(() => {\n    evaluate2((value) => {\n      mutateDom(() => {\n        el.innerHTML = value;\n        el._x_ignoreSelf = true;\n        initTree(el);\n        delete el._x_ignoreSelf;\n      });\n    });\n  });\n});\n\n// packages/alpinejs/src/directives/x-bind.js\nmapAttributes(startingWith(\":\", into(prefix(\"bind:\"))));\nvar handler2 = (el, { value, modifiers, expression, original }, { effect: effect3 }) => {\n  if (!value) {\n    let bindingProviders = {};\n    injectBindingProviders(bindingProviders);\n    let getBindings = evaluateLater(el, expression);\n    getBindings((bindings) => {\n      applyBindingsObject(el, bindings, original);\n    }, { scope: bindingProviders });\n    return;\n  }\n  if (value === \"key\")\n    return storeKeyForXFor(el, expression);\n  if (el._x_inlineBindings && el._x_inlineBindings[value] && el._x_inlineBindings[value].extract) {\n    return;\n  }\n  let evaluate2 = evaluateLater(el, expression);\n  effect3(() => evaluate2((result) => {\n    if (result === void 0 && typeof expression === \"string\" && expression.match(/\\./)) {\n      result = \"\";\n    }\n    mutateDom(() => bind(el, value, result, modifiers));\n  }));\n};\nhandler2.inline = (el, { value, modifiers, expression }) => {\n  if (!value)\n    return;\n  if (!el._x_inlineBindings)\n    el._x_inlineBindings = {};\n  el._x_inlineBindings[value] = { expression, extract: false };\n};\ndirective(\"bind\", handler2);\nfunction storeKeyForXFor(el, expression) {\n  el._x_keyExpression = expression;\n}\n\n// packages/alpinejs/src/directives/x-data.js\naddRootSelector(() => `[${prefix(\"data\")}]`);\ndirective(\"data\", (el, { expression }, { cleanup: cleanup2 }) => {\n  if (shouldSkipRegisteringDataDuringClone(el))\n    return;\n  expression = expression === \"\" ? \"{}\" : expression;\n  let magicContext = {};\n  injectMagics(magicContext, el);\n  let dataProviderContext = {};\n  injectDataProviders(dataProviderContext, magicContext);\n  let data2 = evaluate(el, expression, { scope: dataProviderContext });\n  if (data2 === void 0 || data2 === true)\n    data2 = {};\n  injectMagics(data2, el);\n  let reactiveData = reactive(data2);\n  initInterceptors2(reactiveData);\n  let undo = addScopeToNode(el, reactiveData);\n  reactiveData[\"init\"] && evaluate(el, reactiveData[\"init\"]);\n  cleanup2(() => {\n    reactiveData[\"destroy\"] && evaluate(el, reactiveData[\"destroy\"]);\n    undo();\n  });\n});\n\n// packages/alpinejs/src/directives/x-show.js\ndirective(\"show\", (el, { modifiers, expression }, { effect: effect3 }) => {\n  let evaluate2 = evaluateLater(el, expression);\n  if (!el._x_doHide)\n    el._x_doHide = () => {\n      mutateDom(() => {\n        el.style.setProperty(\"display\", \"none\", modifiers.includes(\"important\") ? \"important\" : void 0);\n      });\n    };\n  if (!el._x_doShow)\n    el._x_doShow = () => {\n      mutateDom(() => {\n        if (el.style.length === 1 && el.style.display === \"none\") {\n          el.removeAttribute(\"style\");\n        } else {\n          el.style.removeProperty(\"display\");\n        }\n      });\n    };\n  let hide = () => {\n    el._x_doHide();\n    el._x_isShown = false;\n  };\n  let show = () => {\n    el._x_doShow();\n    el._x_isShown = true;\n  };\n  let clickAwayCompatibleShow = () => setTimeout(show);\n  let toggle = once(\n    (value) => value ? show() : hide(),\n    (value) => {\n      if (typeof el._x_toggleAndCascadeWithTransitions === \"function\") {\n        el._x_toggleAndCascadeWithTransitions(el, value, show, hide);\n      } else {\n        value ? clickAwayCompatibleShow() : hide();\n      }\n    }\n  );\n  let oldValue;\n  let firstTime = true;\n  effect3(() => evaluate2((value) => {\n    if (!firstTime && value === oldValue)\n      return;\n    if (modifiers.includes(\"immediate\"))\n      value ? clickAwayCompatibleShow() : hide();\n    toggle(value);\n    oldValue = value;\n    firstTime = false;\n  }));\n});\n\n// packages/alpinejs/src/directives/x-for.js\ndirective(\"for\", (el, { expression }, { effect: effect3, cleanup: cleanup2 }) => {\n  let iteratorNames = parseForExpression(expression);\n  let evaluateItems = evaluateLater(el, iteratorNames.items);\n  let evaluateKey = evaluateLater(\n    el,\n    // the x-bind:key expression is stored for our use instead of evaluated.\n    el._x_keyExpression || \"index\"\n  );\n  el._x_prevKeys = [];\n  el._x_lookup = {};\n  effect3(() => loop(el, iteratorNames, evaluateItems, evaluateKey));\n  cleanup2(() => {\n    Object.values(el._x_lookup).forEach((el2) => el2.remove());\n    delete el._x_prevKeys;\n    delete el._x_lookup;\n  });\n});\nfunction loop(el, iteratorNames, evaluateItems, evaluateKey) {\n  let isObject2 = (i) => typeof i === \"object\" && !Array.isArray(i);\n  let templateEl = el;\n  evaluateItems((items) => {\n    if (isNumeric3(items) && items >= 0) {\n      items = Array.from(Array(items).keys(), (i) => i + 1);\n    }\n    if (items === void 0)\n      items = [];\n    let lookup = el._x_lookup;\n    let prevKeys = el._x_prevKeys;\n    let scopes = [];\n    let keys = [];\n    if (isObject2(items)) {\n      items = Object.entries(items).map(([key, value]) => {\n        let scope2 = getIterationScopeVariables(iteratorNames, value, key, items);\n        evaluateKey((value2) => keys.push(value2), { scope: { index: key, ...scope2 } });\n        scopes.push(scope2);\n      });\n    } else {\n      for (let i = 0; i < items.length; i++) {\n        let scope2 = getIterationScopeVariables(iteratorNames, items[i], i, items);\n        evaluateKey((value) => keys.push(value), { scope: { index: i, ...scope2 } });\n        scopes.push(scope2);\n      }\n    }\n    let adds = [];\n    let moves = [];\n    let removes = [];\n    let sames = [];\n    for (let i = 0; i < prevKeys.length; i++) {\n      let key = prevKeys[i];\n      if (keys.indexOf(key) === -1)\n        removes.push(key);\n    }\n    prevKeys = prevKeys.filter((key) => !removes.includes(key));\n    let lastKey = \"template\";\n    for (let i = 0; i < keys.length; i++) {\n      let key = keys[i];\n      let prevIndex = prevKeys.indexOf(key);\n      if (prevIndex === -1) {\n        prevKeys.splice(i, 0, key);\n        adds.push([lastKey, i]);\n      } else if (prevIndex !== i) {\n        let keyInSpot = prevKeys.splice(i, 1)[0];\n        let keyForSpot = prevKeys.splice(prevIndex - 1, 1)[0];\n        prevKeys.splice(i, 0, keyForSpot);\n        prevKeys.splice(prevIndex, 0, keyInSpot);\n        moves.push([keyInSpot, keyForSpot]);\n      } else {\n        sames.push(key);\n      }\n      lastKey = key;\n    }\n    for (let i = 0; i < removes.length; i++) {\n      let key = removes[i];\n      if (!!lookup[key]._x_effects) {\n        lookup[key]._x_effects.forEach(dequeueJob);\n      }\n      lookup[key].remove();\n      lookup[key] = null;\n      delete lookup[key];\n    }\n    for (let i = 0; i < moves.length; i++) {\n      let [keyInSpot, keyForSpot] = moves[i];\n      let elInSpot = lookup[keyInSpot];\n      let elForSpot = lookup[keyForSpot];\n      let marker = document.createElement(\"div\");\n      mutateDom(() => {\n        if (!elForSpot)\n          warn(`x-for \":key\" is undefined or invalid`, templateEl);\n        elForSpot.after(marker);\n        elInSpot.after(elForSpot);\n        elForSpot._x_currentIfEl && elForSpot.after(elForSpot._x_currentIfEl);\n        marker.before(elInSpot);\n        elInSpot._x_currentIfEl && elInSpot.after(elInSpot._x_currentIfEl);\n        marker.remove();\n      });\n      elForSpot._x_refreshXForScope(scopes[keys.indexOf(keyForSpot)]);\n    }\n    for (let i = 0; i < adds.length; i++) {\n      let [lastKey2, index] = adds[i];\n      let lastEl = lastKey2 === \"template\" ? templateEl : lookup[lastKey2];\n      if (lastEl._x_currentIfEl)\n        lastEl = lastEl._x_currentIfEl;\n      let scope2 = scopes[index];\n      let key = keys[index];\n      let clone2 = document.importNode(templateEl.content, true).firstElementChild;\n      let reactiveScope = reactive(scope2);\n      addScopeToNode(clone2, reactiveScope, templateEl);\n      clone2._x_refreshXForScope = (newScope) => {\n        Object.entries(newScope).forEach(([key2, value]) => {\n          reactiveScope[key2] = value;\n        });\n      };\n      mutateDom(() => {\n        lastEl.after(clone2);\n        initTree(clone2);\n      });\n      if (typeof key === \"object\") {\n        warn(\"x-for key cannot be an object, it must be a string or an integer\", templateEl);\n      }\n      lookup[key] = clone2;\n    }\n    for (let i = 0; i < sames.length; i++) {\n      lookup[sames[i]]._x_refreshXForScope(scopes[keys.indexOf(sames[i])]);\n    }\n    templateEl._x_prevKeys = keys;\n  });\n}\nfunction parseForExpression(expression) {\n  let forIteratorRE = /,([^,\\}\\]]*)(?:,([^,\\}\\]]*))?$/;\n  let stripParensRE = /^\\s*\\(|\\)\\s*$/g;\n  let forAliasRE = /([\\s\\S]*?)\\s+(?:in|of)\\s+([\\s\\S]*)/;\n  let inMatch = expression.match(forAliasRE);\n  if (!inMatch)\n    return;\n  let res = {};\n  res.items = inMatch[2].trim();\n  let item = inMatch[1].replace(stripParensRE, \"\").trim();\n  let iteratorMatch = item.match(forIteratorRE);\n  if (iteratorMatch) {\n    res.item = item.replace(forIteratorRE, \"\").trim();\n    res.index = iteratorMatch[1].trim();\n    if (iteratorMatch[2]) {\n      res.collection = iteratorMatch[2].trim();\n    }\n  } else {\n    res.item = item;\n  }\n  return res;\n}\nfunction getIterationScopeVariables(iteratorNames, item, index, items) {\n  let scopeVariables = {};\n  if (/^\\[.*\\]$/.test(iteratorNames.item) && Array.isArray(item)) {\n    let names = iteratorNames.item.replace(\"[\", \"\").replace(\"]\", \"\").split(\",\").map((i) => i.trim());\n    names.forEach((name, i) => {\n      scopeVariables[name] = item[i];\n    });\n  } else if (/^\\{.*\\}$/.test(iteratorNames.item) && !Array.isArray(item) && typeof item === \"object\") {\n    let names = iteratorNames.item.replace(\"{\", \"\").replace(\"}\", \"\").split(\",\").map((i) => i.trim());\n    names.forEach((name) => {\n      scopeVariables[name] = item[name];\n    });\n  } else {\n    scopeVariables[iteratorNames.item] = item;\n  }\n  if (iteratorNames.index)\n    scopeVariables[iteratorNames.index] = index;\n  if (iteratorNames.collection)\n    scopeVariables[iteratorNames.collection] = items;\n  return scopeVariables;\n}\nfunction isNumeric3(subject) {\n  return !Array.isArray(subject) && !isNaN(subject);\n}\n\n// packages/alpinejs/src/directives/x-ref.js\nfunction handler3() {\n}\nhandler3.inline = (el, { expression }, { cleanup: cleanup2 }) => {\n  let root = closestRoot(el);\n  if (!root._x_refs)\n    root._x_refs = {};\n  root._x_refs[expression] = el;\n  cleanup2(() => delete root._x_refs[expression]);\n};\ndirective(\"ref\", handler3);\n\n// packages/alpinejs/src/directives/x-if.js\ndirective(\"if\", (el, { expression }, { effect: effect3, cleanup: cleanup2 }) => {\n  let evaluate2 = evaluateLater(el, expression);\n  let show = () => {\n    if (el._x_currentIfEl)\n      return el._x_currentIfEl;\n    let clone2 = el.content.cloneNode(true).firstElementChild;\n    addScopeToNode(clone2, {}, el);\n    mutateDom(() => {\n      el.after(clone2);\n      initTree(clone2);\n    });\n    el._x_currentIfEl = clone2;\n    el._x_undoIf = () => {\n      walk(clone2, (node) => {\n        if (!!node._x_effects) {\n          node._x_effects.forEach(dequeueJob);\n        }\n      });\n      clone2.remove();\n      delete el._x_currentIfEl;\n    };\n    return clone2;\n  };\n  let hide = () => {\n    if (!el._x_undoIf)\n      return;\n    el._x_undoIf();\n    delete el._x_undoIf;\n  };\n  effect3(() => evaluate2((value) => {\n    value ? show() : hide();\n  }));\n  cleanup2(() => el._x_undoIf && el._x_undoIf());\n});\n\n// packages/alpinejs/src/directives/x-id.js\ndirective(\"id\", (el, { expression }, { evaluate: evaluate2 }) => {\n  let names = evaluate2(expression);\n  names.forEach((name) => setIdRoot(el, name));\n});\n\n// packages/alpinejs/src/directives/x-on.js\nmapAttributes(startingWith(\"@\", into(prefix(\"on:\"))));\ndirective(\"on\", skipDuringClone((el, { value, modifiers, expression }, { cleanup: cleanup2 }) => {\n  let evaluate2 = expression ? evaluateLater(el, expression) : () => {\n  };\n  if (el.tagName.toLowerCase() === \"template\") {\n    if (!el._x_forwardEvents)\n      el._x_forwardEvents = [];\n    if (!el._x_forwardEvents.includes(value))\n      el._x_forwardEvents.push(value);\n  }\n  let removeListener = on(el, value, modifiers, (e) => {\n    evaluate2(() => {\n    }, { scope: { \"$event\": e }, params: [e] });\n  });\n  cleanup2(() => removeListener());\n}));\n\n// packages/alpinejs/src/directives/index.js\nwarnMissingPluginDirective(\"Collapse\", \"collapse\", \"collapse\");\nwarnMissingPluginDirective(\"Intersect\", \"intersect\", \"intersect\");\nwarnMissingPluginDirective(\"Focus\", \"trap\", \"focus\");\nwarnMissingPluginDirective(\"Mask\", \"mask\", \"mask\");\nfunction warnMissingPluginDirective(name, directiveName2, slug) {\n  directive(directiveName2, (el) => warn(`You can't use [x-${directiveName2}] without first installing the \"${name}\" plugin here: https://alpinejs.dev/plugins/${slug}`, el));\n}\n\n// packages/alpinejs/src/index.js\nalpine_default.setEvaluator(normalEvaluator);\nalpine_default.setReactivityEngine({ reactive: reactive2, effect: effect2, release: stop, raw: toRaw });\nvar src_default = alpine_default;\n\n// packages/alpinejs/builds/module.js\nvar module_default = src_default;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/alpinejs/dist/module.esm.js\n");

/***/ }),

/***/ "./resources/js/app.js":
/*!*****************************!*\
  !*** ./resources/js/app.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var alpinejs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! alpinejs */ \"./node_modules/alpinejs/dist/module.esm.js\");\nObject(function webpackMissingModule() { var e = new Error(\"Cannot find module '@awcodes/alpine-floating-ui'\"); e.code = 'MODULE_NOT_FOUND'; throw e; }());\n/* harmony import */ var _vendor_filament_notifications_dist_module_esm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../vendor/filament/notifications/dist/module.esm */ \"./vendor/filament/notifications/dist/module.esm.js\");\n\n\n\nalpinejs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plugin(Object(function webpackMissingModule() { var e = new Error(\"Cannot find module '@awcodes/alpine-floating-ui'\"); e.code = 'MODULE_NOT_FOUND'; throw e; }()));\nalpinejs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plugin(_vendor_filament_notifications_dist_module_esm__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\nwindow.Alpine = alpinejs__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\nalpinejs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].start();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9yZXNvdXJjZXMvanMvYXBwLmpzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBNkI7QUFDNkI7QUFDaUM7QUFFM0ZBLHVEQUFhLENBQUNDLDBKQUFnQixDQUFDO0FBQy9CRCx1REFBYSxDQUFDRSxzRkFBeUIsQ0FBQztBQUV4Q0UsTUFBTSxDQUFDSixNQUFNLEdBQUdBLGdEQUFNO0FBRXRCQSxzREFBWSxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vcmVzb3VyY2VzL2pzL2FwcC5qcz9jZWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBBbHBpbmUgZnJvbSAnYWxwaW5lanMnXG5pbXBvcnQgQWxwaW5lRmxvYXRpbmdVSSBmcm9tICdAYXdjb2Rlcy9hbHBpbmUtZmxvYXRpbmctdWknXG5pbXBvcnQgTm90aWZpY2F0aW9uc0FscGluZVBsdWdpbiBmcm9tICcuLi8uLi92ZW5kb3IvZmlsYW1lbnQvbm90aWZpY2F0aW9ucy9kaXN0L21vZHVsZS5lc20nXG5cbkFscGluZS5wbHVnaW4oQWxwaW5lRmxvYXRpbmdVSSlcbkFscGluZS5wbHVnaW4oTm90aWZpY2F0aW9uc0FscGluZVBsdWdpbilcblxud2luZG93LkFscGluZSA9IEFscGluZVxuXG5BbHBpbmUuc3RhcnQoKVxuIl0sIm5hbWVzIjpbIkFscGluZSIsIkFscGluZUZsb2F0aW5nVUkiLCJOb3RpZmljYXRpb25zQWxwaW5lUGx1Z2luIiwicGx1Z2luIiwid2luZG93Iiwic3RhcnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./resources/js/app.js\n");

/***/ }),

/***/ "./vendor/filament/notifications/dist/module.esm.js":
/*!**********************************************************!*\
  !*** ./vendor/filament/notifications/dist/module.esm.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"Notification\": () => (/* binding */ Notification),\n/* harmony export */   \"NotificationAction\": () => (/* binding */ Action),\n/* harmony export */   \"NotificationActionGroup\": () => (/* binding */ ActionGroup),\n/* harmony export */   \"NotificationComponentAlpinePlugin\": () => (/* binding */ notification_default),\n/* harmony export */   \"default\": () => (/* binding */ js_default)\n/* harmony export */ });\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0) { ; } } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i[\"return\"] && (_r = _i[\"return\"](), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e2) { throw _e2; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e3) { didErr = true; err = _e3; }, f: function f() { try { if (!normalCompletion && it[\"return\"] != null) it[\"return\"](); } finally { if (didErr) throw err; } } }; }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __markAsModule = function __markAsModule(target) {\n  return __defProp(target, \"__esModule\", {\n    value: true\n  });\n};\nvar __commonJS = function __commonJS(callback, module) {\n  return function () {\n    if (!module) {\n      module = {\n        exports: {}\n      };\n      callback(module.exports, module);\n    }\n    return module.exports;\n  };\n};\nvar __exportStar = function __exportStar(target, module, desc) {\n  if (module && _typeof(module) === \"object\" || typeof module === \"function\") {\n    var _iterator = _createForOfIteratorHelper(__getOwnPropNames(module)),\n      _step;\n    try {\n      var _loop = function _loop() {\n        var key = _step.value;\n        if (!__hasOwnProp.call(target, key) && key !== \"default\") __defProp(target, key, {\n          get: function get() {\n            return module[key];\n          },\n          enumerable: !(desc = __getOwnPropDesc(module, key)) || desc.enumerable\n        });\n      };\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        _loop();\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n  }\n  return target;\n};\nvar __toModule = function __toModule(module) {\n  return __exportStar(__markAsModule(__defProp(module != null ? __create(__getProtoOf(module)) : {}, \"default\", module && module.__esModule && \"default\" in module ? {\n    get: function get() {\n      return module[\"default\"];\n    },\n    enumerable: true\n  } : {\n    value: module,\n    enumerable: true\n  })), module);\n};\n\n// node_modules/uuid-browser/lib/rng-browser.js\nvar require_rng_browser = __commonJS(function (exports, module) {\n  var rng;\n  var crypto = typeof __webpack_require__.g !== \"undefined\" && (__webpack_require__.g.crypto || __webpack_require__.g.msCrypto);\n  if (crypto && crypto.getRandomValues) {\n    rnds8 = new Uint8Array(16);\n    rng = function whatwgRNG() {\n      crypto.getRandomValues(rnds8);\n      return rnds8;\n    };\n  }\n  var rnds8;\n  if (!rng) {\n    rnds = new Array(16);\n    rng = function rng() {\n      for (var i = 0, r; i < 16; i++) {\n        if ((i & 3) === 0) r = Math.random() * 4294967296;\n        rnds[i] = r >>> ((i & 3) << 3) & 255;\n      }\n      return rnds;\n    };\n  }\n  var rnds;\n  module.exports = rng;\n});\n\n// node_modules/uuid-browser/lib/bytesToUuid.js\nvar require_bytesToUuid = __commonJS(function (exports, module) {\n  var byteToHex = [];\n  for (var i = 0; i < 256; ++i) {\n    byteToHex[i] = (i + 256).toString(16).substr(1);\n  }\n  function bytesToUuid(buf, offset) {\n    var i2 = offset || 0;\n    var bth = byteToHex;\n    return bth[buf[i2++]] + bth[buf[i2++]] + bth[buf[i2++]] + bth[buf[i2++]] + \"-\" + bth[buf[i2++]] + bth[buf[i2++]] + \"-\" + bth[buf[i2++]] + bth[buf[i2++]] + \"-\" + bth[buf[i2++]] + bth[buf[i2++]] + \"-\" + bth[buf[i2++]] + bth[buf[i2++]] + bth[buf[i2++]] + bth[buf[i2++]] + bth[buf[i2++]] + bth[buf[i2++]];\n  }\n  module.exports = bytesToUuid;\n});\n\n// node_modules/uuid-browser/v1.js\nvar require_v1 = __commonJS(function (exports, module) {\n  var rng = require_rng_browser();\n  var bytesToUuid = require_bytesToUuid();\n  var _seedBytes = rng();\n  var _nodeId = [_seedBytes[0] | 1, _seedBytes[1], _seedBytes[2], _seedBytes[3], _seedBytes[4], _seedBytes[5]];\n  var _clockseq = (_seedBytes[6] << 8 | _seedBytes[7]) & 16383;\n  var _lastMSecs = 0;\n  var _lastNSecs = 0;\n  function v1(options, buf, offset) {\n    var i = buf && offset || 0;\n    var b = buf || [];\n    options = options || {};\n    var clockseq = options.clockseq !== void 0 ? options.clockseq : _clockseq;\n    var msecs = options.msecs !== void 0 ? options.msecs : new Date().getTime();\n    var nsecs = options.nsecs !== void 0 ? options.nsecs : _lastNSecs + 1;\n    var dt = msecs - _lastMSecs + (nsecs - _lastNSecs) / 1e4;\n    if (dt < 0 && options.clockseq === void 0) {\n      clockseq = clockseq + 1 & 16383;\n    }\n    if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === void 0) {\n      nsecs = 0;\n    }\n    if (nsecs >= 1e4) {\n      throw new Error(\"uuid.v1(): Can't create more than 10M uuids/sec\");\n    }\n    _lastMSecs = msecs;\n    _lastNSecs = nsecs;\n    _clockseq = clockseq;\n    msecs += 122192928e5;\n    var tl = ((msecs & 268435455) * 1e4 + nsecs) % 4294967296;\n    b[i++] = tl >>> 24 & 255;\n    b[i++] = tl >>> 16 & 255;\n    b[i++] = tl >>> 8 & 255;\n    b[i++] = tl & 255;\n    var tmh = msecs / 4294967296 * 1e4 & 268435455;\n    b[i++] = tmh >>> 8 & 255;\n    b[i++] = tmh & 255;\n    b[i++] = tmh >>> 24 & 15 | 16;\n    b[i++] = tmh >>> 16 & 255;\n    b[i++] = clockseq >>> 8 | 128;\n    b[i++] = clockseq & 255;\n    var node = options.node || _nodeId;\n    for (var n = 0; n < 6; ++n) {\n      b[i + n] = node[n];\n    }\n    return buf ? buf : bytesToUuid(b);\n  }\n  module.exports = v1;\n});\n\n// node_modules/uuid-browser/v4.js\nvar require_v4 = __commonJS(function (exports, module) {\n  var rng = require_rng_browser();\n  var bytesToUuid = require_bytesToUuid();\n  function v4(options, buf, offset) {\n    var i = buf && offset || 0;\n    if (typeof options == \"string\") {\n      buf = options == \"binary\" ? new Array(16) : null;\n      options = null;\n    }\n    options = options || {};\n    var rnds = options.random || (options.rng || rng)();\n    rnds[6] = rnds[6] & 15 | 64;\n    rnds[8] = rnds[8] & 63 | 128;\n    if (buf) {\n      for (var ii = 0; ii < 16; ++ii) {\n        buf[i + ii] = rnds[ii];\n      }\n    }\n    return buf || bytesToUuid(rnds);\n  }\n  module.exports = v4;\n});\n\n// node_modules/uuid-browser/index.js\nvar require_uuid_browser = __commonJS(function (exports, module) {\n  var v1 = require_v1();\n  var v4 = require_v4();\n  var uuid2 = v4;\n  uuid2.v1 = v1;\n  uuid2.v4 = v4;\n  module.exports = uuid2;\n});\n\n// node_modules/alpinejs/src/mutation.js\nvar onAttributeAddeds = [];\nvar onElRemoveds = [];\nvar onElAddeds = [];\nfunction cleanupAttributes(el, names) {\n  if (!el._x_attributeCleanups) return;\n  Object.entries(el._x_attributeCleanups).forEach(function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      name = _ref2[0],\n      value = _ref2[1];\n    if (names === void 0 || names.includes(name)) {\n      value.forEach(function (i) {\n        return i();\n      });\n      delete el._x_attributeCleanups[name];\n    }\n  });\n}\nvar observer = new MutationObserver(onMutate);\nvar currentlyObserving = false;\nfunction startObservingMutations() {\n  observer.observe(document, {\n    subtree: true,\n    childList: true,\n    attributes: true,\n    attributeOldValue: true\n  });\n  currentlyObserving = true;\n}\nfunction stopObservingMutations() {\n  flushObserver();\n  observer.disconnect();\n  currentlyObserving = false;\n}\nvar recordQueue = [];\nvar willProcessRecordQueue = false;\nfunction flushObserver() {\n  recordQueue = recordQueue.concat(observer.takeRecords());\n  if (recordQueue.length && !willProcessRecordQueue) {\n    willProcessRecordQueue = true;\n    queueMicrotask(function () {\n      processRecordQueue();\n      willProcessRecordQueue = false;\n    });\n  }\n}\nfunction processRecordQueue() {\n  onMutate(recordQueue);\n  recordQueue.length = 0;\n}\nfunction mutateDom(callback) {\n  if (!currentlyObserving) return callback();\n  stopObservingMutations();\n  var result = callback();\n  startObservingMutations();\n  return result;\n}\nvar isCollecting = false;\nvar deferredMutations = [];\nfunction onMutate(mutations) {\n  if (isCollecting) {\n    deferredMutations = deferredMutations.concat(mutations);\n    return;\n  }\n  var addedNodes = [];\n  var removedNodes = [];\n  var addedAttributes = new Map();\n  var removedAttributes = new Map();\n  for (var i = 0; i < mutations.length; i++) {\n    if (mutations[i].target._x_ignoreMutationObserver) continue;\n    if (mutations[i].type === \"childList\") {\n      mutations[i].addedNodes.forEach(function (node) {\n        return node.nodeType === 1 && addedNodes.push(node);\n      });\n      mutations[i].removedNodes.forEach(function (node) {\n        return node.nodeType === 1 && removedNodes.push(node);\n      });\n    }\n    if (mutations[i].type === \"attributes\") {\n      (function () {\n        var el = mutations[i].target;\n        var name = mutations[i].attributeName;\n        var oldValue = mutations[i].oldValue;\n        var add = function add() {\n          if (!addedAttributes.has(el)) addedAttributes.set(el, []);\n          addedAttributes.get(el).push({\n            name: name,\n            value: el.getAttribute(name)\n          });\n        };\n        var remove = function remove() {\n          if (!removedAttributes.has(el)) removedAttributes.set(el, []);\n          removedAttributes.get(el).push(name);\n        };\n        if (el.hasAttribute(name) && oldValue === null) {\n          add();\n        } else if (el.hasAttribute(name)) {\n          remove();\n          add();\n        } else {\n          remove();\n        }\n      })();\n    }\n  }\n  removedAttributes.forEach(function (attrs, el) {\n    cleanupAttributes(el, attrs);\n  });\n  addedAttributes.forEach(function (attrs, el) {\n    onAttributeAddeds.forEach(function (i) {\n      return i(el, attrs);\n    });\n  });\n  var _loop2 = function _loop2() {\n    var node = _removedNodes[_i2];\n    if (addedNodes.includes(node)) return \"continue\";\n    onElRemoveds.forEach(function (i) {\n      return i(node);\n    });\n    if (node._x_cleanups) {\n      while (node._x_cleanups.length) {\n        node._x_cleanups.pop()();\n      }\n    }\n  };\n  for (var _i2 = 0, _removedNodes = removedNodes; _i2 < _removedNodes.length; _i2++) {\n    var _ret = _loop2();\n    if (_ret === \"continue\") continue;\n  }\n  addedNodes.forEach(function (node) {\n    node._x_ignoreSelf = true;\n    node._x_ignore = true;\n  });\n  var _loop3 = function _loop3() {\n    var node = _addedNodes[_i3];\n    if (removedNodes.includes(node)) return \"continue\";\n    if (!node.isConnected) return \"continue\";\n    delete node._x_ignoreSelf;\n    delete node._x_ignore;\n    onElAddeds.forEach(function (i) {\n      return i(node);\n    });\n    node._x_ignore = true;\n    node._x_ignoreSelf = true;\n  };\n  for (var _i3 = 0, _addedNodes = addedNodes; _i3 < _addedNodes.length; _i3++) {\n    var _ret2 = _loop3();\n    if (_ret2 === \"continue\") continue;\n  }\n  addedNodes.forEach(function (node) {\n    delete node._x_ignoreSelf;\n    delete node._x_ignore;\n  });\n  addedNodes = null;\n  removedNodes = null;\n  addedAttributes = null;\n  removedAttributes = null;\n}\n\n// node_modules/alpinejs/src/utils/once.js\nfunction once(callback) {\n  var fallback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : function () {};\n  var called = false;\n  return function () {\n    if (!called) {\n      called = true;\n      callback.apply(this, arguments);\n    } else {\n      fallback.apply(this, arguments);\n    }\n  };\n}\n\n// packages/notifications/resources/js/components/notification.js\nvar notification_default = function notification_default(Alpine) {\n  Alpine.data(\"notificationComponent\", function (_ref3) {\n    var notification = _ref3.notification;\n    return {\n      isShown: false,\n      computedStyle: null,\n      init: function init() {\n        var _this = this;\n        this.computedStyle = window.getComputedStyle(this.$el);\n        this.configureTransitions();\n        this.configureAnimations();\n        if (notification.duration && notification.duration !== \"persistent\") {\n          setTimeout(function () {\n            return _this.close();\n          }, notification.duration);\n        }\n        this.isShown = true;\n      },\n      configureTransitions: function configureTransitions() {\n        var _this2 = this;\n        var display = this.computedStyle.display;\n        var show = function show() {\n          mutateDom(function () {\n            _this2.$el.style.setProperty(\"display\", display);\n            _this2.$el.style.setProperty(\"visibility\", \"visible\");\n          });\n          _this2.$el._x_isShown = true;\n        };\n        var hide = function hide() {\n          mutateDom(function () {\n            _this2.$el._x_isShown ? _this2.$el.style.setProperty(\"visibility\", \"hidden\") : _this2.$el.style.setProperty(\"display\", \"none\");\n          });\n        };\n        var toggle = once(function (value) {\n          return value ? show() : hide();\n        }, function (value) {\n          _this2.$el._x_toggleAndCascadeWithTransitions(_this2.$el, value, show, hide);\n        });\n        Alpine.effect(function () {\n          return toggle(_this2.isShown);\n        });\n      },\n      configureAnimations: function configureAnimations() {\n        var _this3 = this;\n        var animation;\n        Livewire.hook(\"message.received\", function (_, component) {\n          if (!component.serverMemo.data.isFilamentNotificationsComponent) {\n            return;\n          }\n          var getTop = function getTop() {\n            return _this3.$el.getBoundingClientRect().top;\n          };\n          var oldTop = getTop();\n          animation = function animation() {\n            _this3.$el.animate([{\n              transform: \"translateY(\".concat(oldTop - getTop(), \"px)\")\n            }, {\n              transform: \"translateY(0px)\"\n            }], {\n              duration: _this3.getTransitionDuration(),\n              easing: _this3.computedStyle.transitionTimingFunction\n            });\n          };\n          _this3.$el.getAnimations().forEach(function (animation2) {\n            return animation2.finish();\n          });\n        });\n        Livewire.hook(\"message.processed\", function (_, component) {\n          if (!component.serverMemo.data.isFilamentNotificationsComponent) {\n            return;\n          }\n          if (!_this3.isShown) {\n            return;\n          }\n          animation();\n        });\n      },\n      close: function close() {\n        this.isShown = false;\n        setTimeout(function () {\n          return Livewire.emit(\"notificationClosed\", notification.id);\n        }, this.getTransitionDuration());\n      },\n      getTransitionDuration: function getTransitionDuration() {\n        return parseFloat(this.computedStyle.transitionDuration) * 1e3;\n      }\n    };\n  });\n};\n\n// packages/notifications/resources/js/Notification.js\nvar import_uuid_browser = __toModule(require_uuid_browser());\nvar Notification = /*#__PURE__*/function () {\n  function Notification() {\n    _classCallCheck(this, Notification);\n    this.id((0, import_uuid_browser.v4)());\n    return this;\n  }\n  _createClass(Notification, [{\n    key: \"id\",\n    value: function id(_id) {\n      this.id = _id;\n      return this;\n    }\n  }, {\n    key: \"title\",\n    value: function title(_title) {\n      this.title = _title;\n      return this;\n    }\n  }, {\n    key: \"body\",\n    value: function body(_body) {\n      this.body = _body;\n      return this;\n    }\n  }, {\n    key: \"actions\",\n    value: function actions(_actions) {\n      this.actions = _actions;\n      return this;\n    }\n  }, {\n    key: \"status\",\n    value: function status(_status) {\n      switch (_status) {\n        case \"success\":\n          this.success();\n          break;\n        case \"warning\":\n          this.warning();\n          break;\n        case \"danger\":\n          this.danger();\n          break;\n      }\n      return this;\n    }\n  }, {\n    key: \"icon\",\n    value: function icon(_icon) {\n      this.icon = _icon;\n      return this;\n    }\n  }, {\n    key: \"iconColor\",\n    value: function iconColor(color) {\n      this.iconColor = color;\n      return this;\n    }\n  }, {\n    key: \"duration\",\n    value: function duration(_duration) {\n      this.duration = _duration;\n      return this;\n    }\n  }, {\n    key: \"seconds\",\n    value: function seconds(_seconds) {\n      this.duration(_seconds * 1e3);\n      return this;\n    }\n  }, {\n    key: \"persistent\",\n    value: function persistent() {\n      this.duration(\"persistent\");\n      return this;\n    }\n  }, {\n    key: \"success\",\n    value: function success() {\n      this.icon(\"heroicon-o-check-circle\");\n      this.iconColor(\"success\");\n      return this;\n    }\n  }, {\n    key: \"warning\",\n    value: function warning() {\n      this.icon(\"heroicon-o-exclamation-circle\");\n      this.iconColor(\"warning\");\n      return this;\n    }\n  }, {\n    key: \"danger\",\n    value: function danger() {\n      this.icon(\"heroicon-o-x-circle\");\n      this.iconColor(\"danger\");\n      return this;\n    }\n  }, {\n    key: \"view\",\n    value: function view(_view) {\n      this.view = _view;\n      return this;\n    }\n  }, {\n    key: \"viewData\",\n    value: function viewData(_viewData) {\n      this.viewData = _viewData;\n      return this;\n    }\n  }, {\n    key: \"send\",\n    value: function send() {\n      Livewire.emit(\"notificationSent\", this);\n      return this;\n    }\n  }]);\n  return Notification;\n}();\nvar Action = /*#__PURE__*/function () {\n  function Action(name) {\n    _classCallCheck(this, Action);\n    this.name(name);\n    return this;\n  }\n  _createClass(Action, [{\n    key: \"name\",\n    value: function name(_name) {\n      this.name = _name;\n      return this;\n    }\n  }, {\n    key: \"color\",\n    value: function color(_color) {\n      this.color = _color;\n      return this;\n    }\n  }, {\n    key: \"emit\",\n    value: function emit(event, data) {\n      this.event(event);\n      this.eventData(data);\n      return this;\n    }\n  }, {\n    key: \"emitSelf\",\n    value: function emitSelf(event, data) {\n      this.emit(event, data);\n      this.emitDirection = \"self\";\n      return this;\n    }\n  }, {\n    key: \"emitTo\",\n    value: function emitTo(component, event, data) {\n      this.emit(event, data);\n      this.emitDirection = \"to\";\n      this.emitToComponent = component;\n      return this;\n    }\n  }, {\n    key: \"emitUp\",\n    value: function emitUp(event, data) {\n      this.emit(event, data);\n      this.emitDirection = \"up\";\n      return this;\n    }\n  }, {\n    key: \"emitDirection\",\n    value: function emitDirection(_emitDirection) {\n      this.emitDirection = _emitDirection;\n      return this;\n    }\n  }, {\n    key: \"emitToComponent\",\n    value: function emitToComponent(component) {\n      this.emitToComponent = component;\n      return this;\n    }\n  }, {\n    key: \"event\",\n    value: function event(_event) {\n      this.event = _event;\n      return this;\n    }\n  }, {\n    key: \"eventData\",\n    value: function eventData(data) {\n      this.eventData = data;\n      return this;\n    }\n  }, {\n    key: \"extraAttributes\",\n    value: function extraAttributes(attributes) {\n      this.extraAttributes = attributes;\n      return this;\n    }\n  }, {\n    key: \"icon\",\n    value: function icon(_icon2) {\n      this.icon = _icon2;\n      return this;\n    }\n  }, {\n    key: \"iconPosition\",\n    value: function iconPosition(position) {\n      this.iconPosition = position;\n      return this;\n    }\n  }, {\n    key: \"outlined\",\n    value: function outlined() {\n      var condition = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      this.isOutlined = condition;\n      return this;\n    }\n  }, {\n    key: \"disabled\",\n    value: function disabled() {\n      var condition = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      this.isDisabled = condition;\n      return this;\n    }\n  }, {\n    key: \"label\",\n    value: function label(_label) {\n      this.label = _label;\n      return this;\n    }\n  }, {\n    key: \"close\",\n    value: function close() {\n      var condition = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      this.shouldCloseNotification = condition;\n      return this;\n    }\n  }, {\n    key: \"openUrlInNewTab\",\n    value: function openUrlInNewTab() {\n      var condition = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      this.shouldOpenUrlInNewTab = condition;\n      return this;\n    }\n  }, {\n    key: \"size\",\n    value: function size(_size) {\n      this.size = _size;\n      return this;\n    }\n  }, {\n    key: \"url\",\n    value: function url(_url) {\n      this.url = _url;\n      return this;\n    }\n  }, {\n    key: \"view\",\n    value: function view(_view2) {\n      this.view = _view2;\n      return this;\n    }\n  }, {\n    key: \"button\",\n    value: function button() {\n      this.view(\"notifications::actions.button-action\");\n      return this;\n    }\n  }, {\n    key: \"grouped\",\n    value: function grouped() {\n      this.view(\"notifications::actions.grouped-action\");\n      return this;\n    }\n  }, {\n    key: \"link\",\n    value: function link() {\n      this.view(\"notifications::actions.link-action\");\n      return this;\n    }\n  }]);\n  return Action;\n}();\nvar ActionGroup = /*#__PURE__*/function () {\n  function ActionGroup(actions) {\n    _classCallCheck(this, ActionGroup);\n    this.actions(actions);\n    return this;\n  }\n  _createClass(ActionGroup, [{\n    key: \"actions\",\n    value: function actions(_actions2) {\n      this.actions = _actions2.map(function (action) {\n        return action.grouped();\n      });\n      return this;\n    }\n  }, {\n    key: \"color\",\n    value: function color(_color2) {\n      this.color = _color2;\n      return this;\n    }\n  }, {\n    key: \"icon\",\n    value: function icon(_icon3) {\n      this.icon = _icon3;\n      return this;\n    }\n  }, {\n    key: \"iconPosition\",\n    value: function iconPosition(position) {\n      this.iconPosition = position;\n      return this;\n    }\n  }, {\n    key: \"label\",\n    value: function label(_label2) {\n      this.label = _label2;\n      return this;\n    }\n  }, {\n    key: \"tooltip\",\n    value: function tooltip(_tooltip) {\n      this.tooltip = _tooltip;\n      return this;\n    }\n  }]);\n  return ActionGroup;\n}();\n\n// packages/notifications/resources/js/index.js\nwindow.NotificationAction = Action;\nwindow.NotificationActionGroup = ActionGroup;\nwindow.Notification = Notification;\nvar js_default = function js_default(Alpine) {\n  Alpine.plugin(notification_default);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi92ZW5kb3IvZmlsYW1lbnQvbm90aWZpY2F0aW9ucy9kaXN0L21vZHVsZS5lc20uanMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsSUFBSUEsUUFBUSxHQUFHQyxNQUFNLENBQUNDLE1BQU07QUFDNUIsSUFBSUMsU0FBUyxHQUFHRixNQUFNLENBQUNHLGNBQWM7QUFDckMsSUFBSUMsWUFBWSxHQUFHSixNQUFNLENBQUNLLGNBQWM7QUFDeEMsSUFBSUMsWUFBWSxHQUFHTixNQUFNLENBQUNPLFNBQVMsQ0FBQ0MsY0FBYztBQUNsRCxJQUFJQyxpQkFBaUIsR0FBR1QsTUFBTSxDQUFDVSxtQkFBbUI7QUFDbEQsSUFBSUMsZ0JBQWdCLEdBQUdYLE1BQU0sQ0FBQ1ksd0JBQXdCO0FBQ3RELElBQUlDLGNBQWMsR0FBRyxTQUFqQkEsY0FBYyxDQUFJQyxNQUFNO0VBQUEsT0FBS1osU0FBUyxDQUFDWSxNQUFNLEVBQUUsWUFBWSxFQUFFO0lBQUNDLEtBQUssRUFBRTtFQUFJLENBQUMsQ0FBQztBQUFBO0FBQy9FLElBQUlDLFVBQVUsR0FBRyxTQUFiQSxVQUFVLENBQUlDLFFBQVEsRUFBRUMsTUFBTTtFQUFBLE9BQUssWUFBTTtJQUMzQyxJQUFJLENBQUNBLE1BQU0sRUFBRTtNQUNYQSxNQUFNLEdBQUc7UUFBQ0MsT0FBTyxFQUFFLENBQUM7TUFBQyxDQUFDO01BQ3RCRixRQUFRLENBQUNDLE1BQU0sQ0FBQ0MsT0FBTyxFQUFFRCxNQUFNLENBQUM7SUFDbEM7SUFDQSxPQUFPQSxNQUFNLENBQUNDLE9BQU87RUFDdkIsQ0FBQztBQUFBO0FBQ0QsSUFBSUMsWUFBWSxHQUFHLFNBQWZBLFlBQVksQ0FBSU4sTUFBTSxFQUFFSSxNQUFNLEVBQUVHLElBQUksRUFBSztFQUMzQyxJQUFJSCxNQUFNLElBQUksUUFBT0EsTUFBTSxNQUFLLFFBQVEsSUFBSSxPQUFPQSxNQUFNLEtBQUssVUFBVSxFQUFFO0lBQUEsMkNBQ3hEVCxpQkFBaUIsQ0FBQ1MsTUFBTSxDQUFDO01BQUE7SUFBQTtNQUFBO1FBQUEsSUFBaENJLEdBQUc7UUFDVixJQUFJLENBQUNoQixZQUFZLENBQUNpQixJQUFJLENBQUNULE1BQU0sRUFBRVEsR0FBRyxDQUFDLElBQUlBLEdBQUcsS0FBSyxTQUFTLEVBQ3REcEIsU0FBUyxDQUFDWSxNQUFNLEVBQUVRLEdBQUcsRUFBRTtVQUFDRSxHQUFHLEVBQUU7WUFBQSxPQUFNTixNQUFNLENBQUNJLEdBQUcsQ0FBQztVQUFBO1VBQUVHLFVBQVUsRUFBRSxFQUFFSixJQUFJLEdBQUdWLGdCQUFnQixDQUFDTyxNQUFNLEVBQUVJLEdBQUcsQ0FBQyxDQUFDLElBQUlELElBQUksQ0FBQ0k7UUFBVSxDQUFDLENBQUM7TUFBQztNQUY3SCxvREFDRTtRQUFBO01BQzBIO0lBQUM7TUFBQTtJQUFBO01BQUE7SUFBQTtFQUMvSDtFQUNBLE9BQU9YLE1BQU07QUFDZixDQUFDO0FBQ0QsSUFBSVksVUFBVSxHQUFHLFNBQWJBLFVBQVUsQ0FBSVIsTUFBTSxFQUFLO0VBQzNCLE9BQU9FLFlBQVksQ0FBQ1AsY0FBYyxDQUFDWCxTQUFTLENBQUNnQixNQUFNLElBQUksSUFBSSxHQUFHbkIsUUFBUSxDQUFDSyxZQUFZLENBQUNjLE1BQU0sQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsU0FBUyxFQUFFQSxNQUFNLElBQUlBLE1BQU0sQ0FBQ1MsVUFBVSxJQUFJLFNBQVMsSUFBSVQsTUFBTSxHQUFHO0lBQUNNLEdBQUcsRUFBRTtNQUFBLE9BQU1OLE1BQU0sV0FBUTtJQUFBO0lBQUVPLFVBQVUsRUFBRTtFQUFJLENBQUMsR0FBRztJQUFDVixLQUFLLEVBQUVHLE1BQU07SUFBRU8sVUFBVSxFQUFFO0VBQUksQ0FBQyxDQUFDLENBQUMsRUFBRVAsTUFBTSxDQUFDO0FBQ2pRLENBQUM7O0FBRUQ7QUFDQSxJQUFJVSxtQkFBbUIsR0FBR1osVUFBVSxDQUFDLFVBQUNHLE9BQU8sRUFBRUQsTUFBTSxFQUFLO0VBQ3hELElBQUlXLEdBQUc7RUFDUCxJQUFJQyxNQUFNLEdBQUcsT0FBT0MscUJBQU0sS0FBSyxXQUFXLEtBQUtBLHFCQUFNLENBQUNELE1BQU0sSUFBSUMscUJBQU0sQ0FBQ0MsUUFBUSxDQUFDO0VBQ2hGLElBQUlGLE1BQU0sSUFBSUEsTUFBTSxDQUFDRyxlQUFlLEVBQUU7SUFDcENDLEtBQUssR0FBRyxJQUFJQyxVQUFVLENBQUMsRUFBRSxDQUFDO0lBQzFCTixHQUFHLEdBQUcsU0FBU08sU0FBUyxHQUFHO01BQ3pCTixNQUFNLENBQUNHLGVBQWUsQ0FBQ0MsS0FBSyxDQUFDO01BQzdCLE9BQU9BLEtBQUs7SUFDZCxDQUFDO0VBQ0g7RUFDQSxJQUFJQSxLQUFLO0VBQ1QsSUFBSSxDQUFDTCxHQUFHLEVBQUU7SUFDUlEsSUFBSSxHQUFHLElBQUlDLEtBQUssQ0FBQyxFQUFFLENBQUM7SUFDcEJULEdBQUcsR0FBRyxlQUFXO01BQ2YsS0FBSyxJQUFJVSxDQUFDLEdBQUcsQ0FBQyxFQUFFQyxDQUFDLEVBQUVELENBQUMsR0FBRyxFQUFFLEVBQUVBLENBQUMsRUFBRSxFQUFFO1FBQzlCLElBQUksQ0FBQ0EsQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLEVBQ2ZDLENBQUMsR0FBR0MsSUFBSSxDQUFDQyxNQUFNLEVBQUUsR0FBRyxVQUFVO1FBQ2hDTCxJQUFJLENBQUNFLENBQUMsQ0FBQyxHQUFHQyxDQUFDLE1BQU0sQ0FBQ0QsQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsR0FBRyxHQUFHO01BQ3RDO01BQ0EsT0FBT0YsSUFBSTtJQUNiLENBQUM7RUFDSDtFQUNBLElBQUlBLElBQUk7RUFDUm5CLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHVSxHQUFHO0FBQ3RCLENBQUMsQ0FBQzs7QUFFRjtBQUNBLElBQUljLG1CQUFtQixHQUFHM0IsVUFBVSxDQUFDLFVBQUNHLE9BQU8sRUFBRUQsTUFBTSxFQUFLO0VBQ3hELElBQUkwQixTQUFTLEdBQUcsRUFBRTtFQUNsQixLQUFLLElBQUlMLENBQUMsR0FBRyxDQUFDLEVBQUVBLENBQUMsR0FBRyxHQUFHLEVBQUUsRUFBRUEsQ0FBQyxFQUFFO0lBQzVCSyxTQUFTLENBQUNMLENBQUMsQ0FBQyxHQUFHLENBQUNBLENBQUMsR0FBRyxHQUFHLEVBQUVNLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQ0MsTUFBTSxDQUFDLENBQUMsQ0FBQztFQUNqRDtFQUNBLFNBQVNDLFdBQVcsQ0FBQ0MsR0FBRyxFQUFFQyxNQUFNLEVBQUU7SUFDaEMsSUFBSUMsRUFBRSxHQUFHRCxNQUFNLElBQUksQ0FBQztJQUNwQixJQUFJRSxHQUFHLEdBQUdQLFNBQVM7SUFDbkIsT0FBT08sR0FBRyxDQUFDSCxHQUFHLENBQUNFLEVBQUUsRUFBRSxDQUFDLENBQUMsR0FBR0MsR0FBRyxDQUFDSCxHQUFHLENBQUNFLEVBQUUsRUFBRSxDQUFDLENBQUMsR0FBR0MsR0FBRyxDQUFDSCxHQUFHLENBQUNFLEVBQUUsRUFBRSxDQUFDLENBQUMsR0FBR0MsR0FBRyxDQUFDSCxHQUFHLENBQUNFLEVBQUUsRUFBRSxDQUFDLENBQUMsR0FBRyxHQUFHLEdBQUdDLEdBQUcsQ0FBQ0gsR0FBRyxDQUFDRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLEdBQUdDLEdBQUcsQ0FBQ0gsR0FBRyxDQUFDRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLEdBQUcsR0FBRyxHQUFHQyxHQUFHLENBQUNILEdBQUcsQ0FBQ0UsRUFBRSxFQUFFLENBQUMsQ0FBQyxHQUFHQyxHQUFHLENBQUNILEdBQUcsQ0FBQ0UsRUFBRSxFQUFFLENBQUMsQ0FBQyxHQUFHLEdBQUcsR0FBR0MsR0FBRyxDQUFDSCxHQUFHLENBQUNFLEVBQUUsRUFBRSxDQUFDLENBQUMsR0FBR0MsR0FBRyxDQUFDSCxHQUFHLENBQUNFLEVBQUUsRUFBRSxDQUFDLENBQUMsR0FBRyxHQUFHLEdBQUdDLEdBQUcsQ0FBQ0gsR0FBRyxDQUFDRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLEdBQUdDLEdBQUcsQ0FBQ0gsR0FBRyxDQUFDRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLEdBQUdDLEdBQUcsQ0FBQ0gsR0FBRyxDQUFDRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLEdBQUdDLEdBQUcsQ0FBQ0gsR0FBRyxDQUFDRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLEdBQUdDLEdBQUcsQ0FBQ0gsR0FBRyxDQUFDRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLEdBQUdDLEdBQUcsQ0FBQ0gsR0FBRyxDQUFDRSxFQUFFLEVBQUUsQ0FBQyxDQUFDO0VBQzlTO0VBQ0FoQyxNQUFNLENBQUNDLE9BQU8sR0FBRzRCLFdBQVc7QUFDOUIsQ0FBQyxDQUFDOztBQUVGO0FBQ0EsSUFBSUssVUFBVSxHQUFHcEMsVUFBVSxDQUFDLFVBQUNHLE9BQU8sRUFBRUQsTUFBTSxFQUFLO0VBQy9DLElBQUlXLEdBQUcsR0FBR0QsbUJBQW1CLEVBQUU7RUFDL0IsSUFBSW1CLFdBQVcsR0FBR0osbUJBQW1CLEVBQUU7RUFDdkMsSUFBSVUsVUFBVSxHQUFHeEIsR0FBRyxFQUFFO0VBQ3RCLElBQUl5QixPQUFPLEdBQUcsQ0FDWkQsVUFBVSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsRUFDakJBLFVBQVUsQ0FBQyxDQUFDLENBQUMsRUFDYkEsVUFBVSxDQUFDLENBQUMsQ0FBQyxFQUNiQSxVQUFVLENBQUMsQ0FBQyxDQUFDLEVBQ2JBLFVBQVUsQ0FBQyxDQUFDLENBQUMsRUFDYkEsVUFBVSxDQUFDLENBQUMsQ0FBQyxDQUNkO0VBQ0QsSUFBSUUsU0FBUyxHQUFHLENBQUNGLFVBQVUsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUdBLFVBQVUsQ0FBQyxDQUFDLENBQUMsSUFBSSxLQUFLO0VBQzVELElBQUlHLFVBQVUsR0FBRyxDQUFDO0VBQ2xCLElBQUlDLFVBQVUsR0FBRyxDQUFDO0VBQ2xCLFNBQVNDLEVBQUUsQ0FBQ0MsT0FBTyxFQUFFWCxHQUFHLEVBQUVDLE1BQU0sRUFBRTtJQUNoQyxJQUFJVixDQUFDLEdBQUdTLEdBQUcsSUFBSUMsTUFBTSxJQUFJLENBQUM7SUFDMUIsSUFBSVcsQ0FBQyxHQUFHWixHQUFHLElBQUksRUFBRTtJQUNqQlcsT0FBTyxHQUFHQSxPQUFPLElBQUksQ0FBQyxDQUFDO0lBQ3ZCLElBQUlFLFFBQVEsR0FBR0YsT0FBTyxDQUFDRSxRQUFRLEtBQUssS0FBSyxDQUFDLEdBQUdGLE9BQU8sQ0FBQ0UsUUFBUSxHQUFHTixTQUFTO0lBQ3pFLElBQUlPLEtBQUssR0FBR0gsT0FBTyxDQUFDRyxLQUFLLEtBQUssS0FBSyxDQUFDLEdBQUdILE9BQU8sQ0FBQ0csS0FBSyxHQUFHLElBQUlDLElBQUksRUFBRSxDQUFDQyxPQUFPLEVBQUU7SUFDM0UsSUFBSUMsS0FBSyxHQUFHTixPQUFPLENBQUNNLEtBQUssS0FBSyxLQUFLLENBQUMsR0FBR04sT0FBTyxDQUFDTSxLQUFLLEdBQUdSLFVBQVUsR0FBRyxDQUFDO0lBQ3JFLElBQUlTLEVBQUUsR0FBR0osS0FBSyxHQUFHTixVQUFVLEdBQUcsQ0FBQ1MsS0FBSyxHQUFHUixVQUFVLElBQUksR0FBRztJQUN4RCxJQUFJUyxFQUFFLEdBQUcsQ0FBQyxJQUFJUCxPQUFPLENBQUNFLFFBQVEsS0FBSyxLQUFLLENBQUMsRUFBRTtNQUN6Q0EsUUFBUSxHQUFHQSxRQUFRLEdBQUcsQ0FBQyxHQUFHLEtBQUs7SUFDakM7SUFDQSxJQUFJLENBQUNLLEVBQUUsR0FBRyxDQUFDLElBQUlKLEtBQUssR0FBR04sVUFBVSxLQUFLRyxPQUFPLENBQUNNLEtBQUssS0FBSyxLQUFLLENBQUMsRUFBRTtNQUM5REEsS0FBSyxHQUFHLENBQUM7SUFDWDtJQUNBLElBQUlBLEtBQUssSUFBSSxHQUFHLEVBQUU7TUFDaEIsTUFBTSxJQUFJRSxLQUFLLENBQUMsaURBQWlELENBQUM7SUFDcEU7SUFDQVgsVUFBVSxHQUFHTSxLQUFLO0lBQ2xCTCxVQUFVLEdBQUdRLEtBQUs7SUFDbEJWLFNBQVMsR0FBR00sUUFBUTtJQUNwQkMsS0FBSyxJQUFJLFdBQVc7SUFDcEIsSUFBSU0sRUFBRSxHQUFHLENBQUMsQ0FBQ04sS0FBSyxHQUFHLFNBQVMsSUFBSSxHQUFHLEdBQUdHLEtBQUssSUFBSSxVQUFVO0lBQ3pETCxDQUFDLENBQUNyQixDQUFDLEVBQUUsQ0FBQyxHQUFHNkIsRUFBRSxLQUFLLEVBQUUsR0FBRyxHQUFHO0lBQ3hCUixDQUFDLENBQUNyQixDQUFDLEVBQUUsQ0FBQyxHQUFHNkIsRUFBRSxLQUFLLEVBQUUsR0FBRyxHQUFHO0lBQ3hCUixDQUFDLENBQUNyQixDQUFDLEVBQUUsQ0FBQyxHQUFHNkIsRUFBRSxLQUFLLENBQUMsR0FBRyxHQUFHO0lBQ3ZCUixDQUFDLENBQUNyQixDQUFDLEVBQUUsQ0FBQyxHQUFHNkIsRUFBRSxHQUFHLEdBQUc7SUFDakIsSUFBSUMsR0FBRyxHQUFHUCxLQUFLLEdBQUcsVUFBVSxHQUFHLEdBQUcsR0FBRyxTQUFTO0lBQzlDRixDQUFDLENBQUNyQixDQUFDLEVBQUUsQ0FBQyxHQUFHOEIsR0FBRyxLQUFLLENBQUMsR0FBRyxHQUFHO0lBQ3hCVCxDQUFDLENBQUNyQixDQUFDLEVBQUUsQ0FBQyxHQUFHOEIsR0FBRyxHQUFHLEdBQUc7SUFDbEJULENBQUMsQ0FBQ3JCLENBQUMsRUFBRSxDQUFDLEdBQUc4QixHQUFHLEtBQUssRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFO0lBQzdCVCxDQUFDLENBQUNyQixDQUFDLEVBQUUsQ0FBQyxHQUFHOEIsR0FBRyxLQUFLLEVBQUUsR0FBRyxHQUFHO0lBQ3pCVCxDQUFDLENBQUNyQixDQUFDLEVBQUUsQ0FBQyxHQUFHc0IsUUFBUSxLQUFLLENBQUMsR0FBRyxHQUFHO0lBQzdCRCxDQUFDLENBQUNyQixDQUFDLEVBQUUsQ0FBQyxHQUFHc0IsUUFBUSxHQUFHLEdBQUc7SUFDdkIsSUFBSVMsSUFBSSxHQUFHWCxPQUFPLENBQUNXLElBQUksSUFBSWhCLE9BQU87SUFDbEMsS0FBSyxJQUFJaUIsQ0FBQyxHQUFHLENBQUMsRUFBRUEsQ0FBQyxHQUFHLENBQUMsRUFBRSxFQUFFQSxDQUFDLEVBQUU7TUFDMUJYLENBQUMsQ0FBQ3JCLENBQUMsR0FBR2dDLENBQUMsQ0FBQyxHQUFHRCxJQUFJLENBQUNDLENBQUMsQ0FBQztJQUNwQjtJQUNBLE9BQU92QixHQUFHLEdBQUdBLEdBQUcsR0FBR0QsV0FBVyxDQUFDYSxDQUFDLENBQUM7RUFDbkM7RUFDQTFDLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHdUMsRUFBRTtBQUNyQixDQUFDLENBQUM7O0FBRUY7QUFDQSxJQUFJYyxVQUFVLEdBQUd4RCxVQUFVLENBQUMsVUFBQ0csT0FBTyxFQUFFRCxNQUFNLEVBQUs7RUFDL0MsSUFBSVcsR0FBRyxHQUFHRCxtQkFBbUIsRUFBRTtFQUMvQixJQUFJbUIsV0FBVyxHQUFHSixtQkFBbUIsRUFBRTtFQUN2QyxTQUFTOEIsRUFBRSxDQUFDZCxPQUFPLEVBQUVYLEdBQUcsRUFBRUMsTUFBTSxFQUFFO0lBQ2hDLElBQUlWLENBQUMsR0FBR1MsR0FBRyxJQUFJQyxNQUFNLElBQUksQ0FBQztJQUMxQixJQUFJLE9BQU9VLE9BQU8sSUFBSSxRQUFRLEVBQUU7TUFDOUJYLEdBQUcsR0FBR1csT0FBTyxJQUFJLFFBQVEsR0FBRyxJQUFJckIsS0FBSyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUk7TUFDaERxQixPQUFPLEdBQUcsSUFBSTtJQUNoQjtJQUNBQSxPQUFPLEdBQUdBLE9BQU8sSUFBSSxDQUFDLENBQUM7SUFDdkIsSUFBSXRCLElBQUksR0FBR3NCLE9BQU8sQ0FBQ2pCLE1BQU0sSUFBSSxDQUFDaUIsT0FBTyxDQUFDOUIsR0FBRyxJQUFJQSxHQUFHLEdBQUc7SUFDbkRRLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBR0EsSUFBSSxDQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUUsR0FBRyxFQUFFO0lBQzNCQSxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUdBLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRyxFQUFFLEdBQUcsR0FBRztJQUM1QixJQUFJVyxHQUFHLEVBQUU7TUFDUCxLQUFLLElBQUkwQixFQUFFLEdBQUcsQ0FBQyxFQUFFQSxFQUFFLEdBQUcsRUFBRSxFQUFFLEVBQUVBLEVBQUUsRUFBRTtRQUM5QjFCLEdBQUcsQ0FBQ1QsQ0FBQyxHQUFHbUMsRUFBRSxDQUFDLEdBQUdyQyxJQUFJLENBQUNxQyxFQUFFLENBQUM7TUFDeEI7SUFDRjtJQUNBLE9BQU8xQixHQUFHLElBQUlELFdBQVcsQ0FBQ1YsSUFBSSxDQUFDO0VBQ2pDO0VBQ0FuQixNQUFNLENBQUNDLE9BQU8sR0FBR3NELEVBQUU7QUFDckIsQ0FBQyxDQUFDOztBQUVGO0FBQ0EsSUFBSUUsb0JBQW9CLEdBQUczRCxVQUFVLENBQUMsVUFBQ0csT0FBTyxFQUFFRCxNQUFNLEVBQUs7RUFDekQsSUFBSXdDLEVBQUUsR0FBR04sVUFBVSxFQUFFO0VBQ3JCLElBQUlxQixFQUFFLEdBQUdELFVBQVUsRUFBRTtFQUNyQixJQUFJSSxLQUFLLEdBQUdILEVBQUU7RUFDZEcsS0FBSyxDQUFDbEIsRUFBRSxHQUFHQSxFQUFFO0VBQ2JrQixLQUFLLENBQUNILEVBQUUsR0FBR0EsRUFBRTtFQUNidkQsTUFBTSxDQUFDQyxPQUFPLEdBQUd5RCxLQUFLO0FBQ3hCLENBQUMsQ0FBQzs7QUFFRjtBQUNBLElBQUlDLGlCQUFpQixHQUFHLEVBQUU7QUFDMUIsSUFBSUMsWUFBWSxHQUFHLEVBQUU7QUFDckIsSUFBSUMsVUFBVSxHQUFHLEVBQUU7QUFDbkIsU0FBU0MsaUJBQWlCLENBQUNDLEVBQUUsRUFBRUMsS0FBSyxFQUFFO0VBQ3BDLElBQUksQ0FBQ0QsRUFBRSxDQUFDRSxvQkFBb0IsRUFDMUI7RUFDRm5GLE1BQU0sQ0FBQ29GLE9BQU8sQ0FBQ0gsRUFBRSxDQUFDRSxvQkFBb0IsQ0FBQyxDQUFDRSxPQUFPLENBQUMsZ0JBQW1CO0lBQUE7TUFBakJDLElBQUk7TUFBRXZFLEtBQUs7SUFDM0QsSUFBSW1FLEtBQUssS0FBSyxLQUFLLENBQUMsSUFBSUEsS0FBSyxDQUFDSyxRQUFRLENBQUNELElBQUksQ0FBQyxFQUFFO01BQzVDdkUsS0FBSyxDQUFDc0UsT0FBTyxDQUFDLFVBQUM5QyxDQUFDO1FBQUEsT0FBS0EsQ0FBQyxFQUFFO01BQUEsRUFBQztNQUN6QixPQUFPMEMsRUFBRSxDQUFDRSxvQkFBb0IsQ0FBQ0csSUFBSSxDQUFDO0lBQ3RDO0VBQ0YsQ0FBQyxDQUFDO0FBQ0o7QUFDQSxJQUFJRSxRQUFRLEdBQUcsSUFBSUMsZ0JBQWdCLENBQUNDLFFBQVEsQ0FBQztBQUM3QyxJQUFJQyxrQkFBa0IsR0FBRyxLQUFLO0FBQzlCLFNBQVNDLHVCQUF1QixHQUFHO0VBQ2pDSixRQUFRLENBQUNLLE9BQU8sQ0FBQ0MsUUFBUSxFQUFFO0lBQUNDLE9BQU8sRUFBRSxJQUFJO0lBQUVDLFNBQVMsRUFBRSxJQUFJO0lBQUVDLFVBQVUsRUFBRSxJQUFJO0lBQUVDLGlCQUFpQixFQUFFO0VBQUksQ0FBQyxDQUFDO0VBQ3ZHUCxrQkFBa0IsR0FBRyxJQUFJO0FBQzNCO0FBQ0EsU0FBU1Esc0JBQXNCLEdBQUc7RUFDaENDLGFBQWEsRUFBRTtFQUNmWixRQUFRLENBQUNhLFVBQVUsRUFBRTtFQUNyQlYsa0JBQWtCLEdBQUcsS0FBSztBQUM1QjtBQUNBLElBQUlXLFdBQVcsR0FBRyxFQUFFO0FBQ3BCLElBQUlDLHNCQUFzQixHQUFHLEtBQUs7QUFDbEMsU0FBU0gsYUFBYSxHQUFHO0VBQ3ZCRSxXQUFXLEdBQUdBLFdBQVcsQ0FBQ0UsTUFBTSxDQUFDaEIsUUFBUSxDQUFDaUIsV0FBVyxFQUFFLENBQUM7RUFDeEQsSUFBSUgsV0FBVyxDQUFDSSxNQUFNLElBQUksQ0FBQ0gsc0JBQXNCLEVBQUU7SUFDakRBLHNCQUFzQixHQUFHLElBQUk7SUFDN0JJLGNBQWMsQ0FBQyxZQUFNO01BQ25CQyxrQkFBa0IsRUFBRTtNQUNwQkwsc0JBQXNCLEdBQUcsS0FBSztJQUNoQyxDQUFDLENBQUM7RUFDSjtBQUNGO0FBQ0EsU0FBU0ssa0JBQWtCLEdBQUc7RUFDNUJsQixRQUFRLENBQUNZLFdBQVcsQ0FBQztFQUNyQkEsV0FBVyxDQUFDSSxNQUFNLEdBQUcsQ0FBQztBQUN4QjtBQUNBLFNBQVNHLFNBQVMsQ0FBQzVGLFFBQVEsRUFBRTtFQUMzQixJQUFJLENBQUMwRSxrQkFBa0IsRUFDckIsT0FBTzFFLFFBQVEsRUFBRTtFQUNuQmtGLHNCQUFzQixFQUFFO0VBQ3hCLElBQUlXLE1BQU0sR0FBRzdGLFFBQVEsRUFBRTtFQUN2QjJFLHVCQUF1QixFQUFFO0VBQ3pCLE9BQU9rQixNQUFNO0FBQ2Y7QUFDQSxJQUFJQyxZQUFZLEdBQUcsS0FBSztBQUN4QixJQUFJQyxpQkFBaUIsR0FBRyxFQUFFO0FBQzFCLFNBQVN0QixRQUFRLENBQUN1QixTQUFTLEVBQUU7RUFDM0IsSUFBSUYsWUFBWSxFQUFFO0lBQ2hCQyxpQkFBaUIsR0FBR0EsaUJBQWlCLENBQUNSLE1BQU0sQ0FBQ1MsU0FBUyxDQUFDO0lBQ3ZEO0VBQ0Y7RUFDQSxJQUFJQyxVQUFVLEdBQUcsRUFBRTtFQUNuQixJQUFJQyxZQUFZLEdBQUcsRUFBRTtFQUNyQixJQUFJQyxlQUFlLEdBQUcsSUFBSUMsR0FBRyxFQUFFO0VBQy9CLElBQUlDLGlCQUFpQixHQUFHLElBQUlELEdBQUcsRUFBRTtFQUNqQyxLQUFLLElBQUk5RSxDQUFDLEdBQUcsQ0FBQyxFQUFFQSxDQUFDLEdBQUcwRSxTQUFTLENBQUNQLE1BQU0sRUFBRW5FLENBQUMsRUFBRSxFQUFFO0lBQ3pDLElBQUkwRSxTQUFTLENBQUMxRSxDQUFDLENBQUMsQ0FBQ3pCLE1BQU0sQ0FBQ3lHLHlCQUF5QixFQUMvQztJQUNGLElBQUlOLFNBQVMsQ0FBQzFFLENBQUMsQ0FBQyxDQUFDaUYsSUFBSSxLQUFLLFdBQVcsRUFBRTtNQUNyQ1AsU0FBUyxDQUFDMUUsQ0FBQyxDQUFDLENBQUMyRSxVQUFVLENBQUM3QixPQUFPLENBQUMsVUFBQ2YsSUFBSTtRQUFBLE9BQUtBLElBQUksQ0FBQ21ELFFBQVEsS0FBSyxDQUFDLElBQUlQLFVBQVUsQ0FBQ1EsSUFBSSxDQUFDcEQsSUFBSSxDQUFDO01BQUEsRUFBQztNQUN2RjJDLFNBQVMsQ0FBQzFFLENBQUMsQ0FBQyxDQUFDNEUsWUFBWSxDQUFDOUIsT0FBTyxDQUFDLFVBQUNmLElBQUk7UUFBQSxPQUFLQSxJQUFJLENBQUNtRCxRQUFRLEtBQUssQ0FBQyxJQUFJTixZQUFZLENBQUNPLElBQUksQ0FBQ3BELElBQUksQ0FBQztNQUFBLEVBQUM7SUFDN0Y7SUFDQSxJQUFJMkMsU0FBUyxDQUFDMUUsQ0FBQyxDQUFDLENBQUNpRixJQUFJLEtBQUssWUFBWSxFQUFFO01BQUE7UUFDdEMsSUFBSXZDLEVBQUUsR0FBR2dDLFNBQVMsQ0FBQzFFLENBQUMsQ0FBQyxDQUFDekIsTUFBTTtRQUM1QixJQUFJd0UsSUFBSSxHQUFHMkIsU0FBUyxDQUFDMUUsQ0FBQyxDQUFDLENBQUNvRixhQUFhO1FBQ3JDLElBQUlDLFFBQVEsR0FBR1gsU0FBUyxDQUFDMUUsQ0FBQyxDQUFDLENBQUNxRixRQUFRO1FBQ3BDLElBQUlDLEdBQUcsR0FBRyxTQUFOQSxHQUFHLEdBQVM7VUFDZCxJQUFJLENBQUNULGVBQWUsQ0FBQ1UsR0FBRyxDQUFDN0MsRUFBRSxDQUFDLEVBQzFCbUMsZUFBZSxDQUFDVyxHQUFHLENBQUM5QyxFQUFFLEVBQUUsRUFBRSxDQUFDO1VBQzdCbUMsZUFBZSxDQUFDNUYsR0FBRyxDQUFDeUQsRUFBRSxDQUFDLENBQUN5QyxJQUFJLENBQUM7WUFBQ3BDLElBQUksRUFBSkEsSUFBSTtZQUFFdkUsS0FBSyxFQUFFa0UsRUFBRSxDQUFDK0MsWUFBWSxDQUFDMUMsSUFBSTtVQUFDLENBQUMsQ0FBQztRQUNwRSxDQUFDO1FBQ0QsSUFBSTJDLE1BQU0sR0FBRyxTQUFUQSxNQUFNLEdBQVM7VUFDakIsSUFBSSxDQUFDWCxpQkFBaUIsQ0FBQ1EsR0FBRyxDQUFDN0MsRUFBRSxDQUFDLEVBQzVCcUMsaUJBQWlCLENBQUNTLEdBQUcsQ0FBQzlDLEVBQUUsRUFBRSxFQUFFLENBQUM7VUFDL0JxQyxpQkFBaUIsQ0FBQzlGLEdBQUcsQ0FBQ3lELEVBQUUsQ0FBQyxDQUFDeUMsSUFBSSxDQUFDcEMsSUFBSSxDQUFDO1FBQ3RDLENBQUM7UUFDRCxJQUFJTCxFQUFFLENBQUNpRCxZQUFZLENBQUM1QyxJQUFJLENBQUMsSUFBSXNDLFFBQVEsS0FBSyxJQUFJLEVBQUU7VUFDOUNDLEdBQUcsRUFBRTtRQUNQLENBQUMsTUFBTSxJQUFJNUMsRUFBRSxDQUFDaUQsWUFBWSxDQUFDNUMsSUFBSSxDQUFDLEVBQUU7VUFDaEMyQyxNQUFNLEVBQUU7VUFDUkosR0FBRyxFQUFFO1FBQ1AsQ0FBQyxNQUFNO1VBQ0xJLE1BQU0sRUFBRTtRQUNWO01BQUM7SUFDSDtFQUNGO0VBQ0FYLGlCQUFpQixDQUFDakMsT0FBTyxDQUFDLFVBQUM4QyxLQUFLLEVBQUVsRCxFQUFFLEVBQUs7SUFDdkNELGlCQUFpQixDQUFDQyxFQUFFLEVBQUVrRCxLQUFLLENBQUM7RUFDOUIsQ0FBQyxDQUFDO0VBQ0ZmLGVBQWUsQ0FBQy9CLE9BQU8sQ0FBQyxVQUFDOEMsS0FBSyxFQUFFbEQsRUFBRSxFQUFLO0lBQ3JDSixpQkFBaUIsQ0FBQ1EsT0FBTyxDQUFDLFVBQUM5QyxDQUFDO01BQUEsT0FBS0EsQ0FBQyxDQUFDMEMsRUFBRSxFQUFFa0QsS0FBSyxDQUFDO0lBQUEsRUFBQztFQUNoRCxDQUFDLENBQUM7RUFBQztJQUNFLElBQUk3RCxJQUFJO0lBQ1gsSUFBSTRDLFVBQVUsQ0FBQzNCLFFBQVEsQ0FBQ2pCLElBQUksQ0FBQyxFQUMzQjtJQUNGUSxZQUFZLENBQUNPLE9BQU8sQ0FBQyxVQUFDOUMsQ0FBQztNQUFBLE9BQUtBLENBQUMsQ0FBQytCLElBQUksQ0FBQztJQUFBLEVBQUM7SUFDcEMsSUFBSUEsSUFBSSxDQUFDOEQsV0FBVyxFQUFFO01BQ3BCLE9BQU85RCxJQUFJLENBQUM4RCxXQUFXLENBQUMxQixNQUFNO1FBQzVCcEMsSUFBSSxDQUFDOEQsV0FBVyxDQUFDQyxHQUFHLEVBQUUsRUFBRTtNQUFDO0lBQzdCO0VBQUM7RUFQSCxrQ0FBaUJsQixZQUFZLHFDQUFFO0lBQUE7SUFBQSx5QkFFM0I7RUFNSjtFQUNBRCxVQUFVLENBQUM3QixPQUFPLENBQUMsVUFBQ2YsSUFBSSxFQUFLO0lBQzNCQSxJQUFJLENBQUNnRSxhQUFhLEdBQUcsSUFBSTtJQUN6QmhFLElBQUksQ0FBQ2lFLFNBQVMsR0FBRyxJQUFJO0VBQ3ZCLENBQUMsQ0FBQztFQUFDO0lBQ0UsSUFBSWpFLElBQUk7SUFDWCxJQUFJNkMsWUFBWSxDQUFDNUIsUUFBUSxDQUFDakIsSUFBSSxDQUFDLEVBQzdCO0lBQ0YsSUFBSSxDQUFDQSxJQUFJLENBQUNrRSxXQUFXLEVBQ25CO0lBQ0YsT0FBT2xFLElBQUksQ0FBQ2dFLGFBQWE7SUFDekIsT0FBT2hFLElBQUksQ0FBQ2lFLFNBQVM7SUFDckJ4RCxVQUFVLENBQUNNLE9BQU8sQ0FBQyxVQUFDOUMsQ0FBQztNQUFBLE9BQUtBLENBQUMsQ0FBQytCLElBQUksQ0FBQztJQUFBLEVBQUM7SUFDbENBLElBQUksQ0FBQ2lFLFNBQVMsR0FBRyxJQUFJO0lBQ3JCakUsSUFBSSxDQUFDZ0UsYUFBYSxHQUFHLElBQUk7RUFBQztFQVQ1QixnQ0FBaUJwQixVQUFVLG1DQUFFO0lBQUE7SUFBQSwwQkFJekI7RUFNSjtFQUNBQSxVQUFVLENBQUM3QixPQUFPLENBQUMsVUFBQ2YsSUFBSSxFQUFLO0lBQzNCLE9BQU9BLElBQUksQ0FBQ2dFLGFBQWE7SUFDekIsT0FBT2hFLElBQUksQ0FBQ2lFLFNBQVM7RUFDdkIsQ0FBQyxDQUFDO0VBQ0ZyQixVQUFVLEdBQUcsSUFBSTtFQUNqQkMsWUFBWSxHQUFHLElBQUk7RUFDbkJDLGVBQWUsR0FBRyxJQUFJO0VBQ3RCRSxpQkFBaUIsR0FBRyxJQUFJO0FBQzFCOztBQUVBO0FBQ0EsU0FBU21CLElBQUksQ0FBQ3hILFFBQVEsRUFDbkI7RUFBQSxJQURxQnlILFFBQVEsdUVBQUcsWUFBTSxDQUN6QyxDQUFDO0VBQ0MsSUFBSUMsTUFBTSxHQUFHLEtBQUs7RUFDbEIsT0FBTyxZQUFXO0lBQ2hCLElBQUksQ0FBQ0EsTUFBTSxFQUFFO01BQ1hBLE1BQU0sR0FBRyxJQUFJO01BQ2IxSCxRQUFRLENBQUMySCxLQUFLLENBQUMsSUFBSSxFQUFFQyxTQUFTLENBQUM7SUFDakMsQ0FBQyxNQUFNO01BQ0xILFFBQVEsQ0FBQ0UsS0FBSyxDQUFDLElBQUksRUFBRUMsU0FBUyxDQUFDO0lBQ2pDO0VBQ0YsQ0FBQztBQUNIOztBQUVBO0FBQ0EsSUFBSUMsb0JBQW9CLEdBQUcsU0FBdkJBLG9CQUFvQixDQUFJQyxNQUFNLEVBQUs7RUFDckNBLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLHVCQUF1QixFQUFFO0lBQUEsSUFBRUMsWUFBWSxTQUFaQSxZQUFZO0lBQUEsT0FBTztNQUN4REMsT0FBTyxFQUFFLEtBQUs7TUFDZEMsYUFBYSxFQUFFLElBQUk7TUFDbkJDLElBQUksRUFBRSxnQkFBVztRQUFBO1FBQ2YsSUFBSSxDQUFDRCxhQUFhLEdBQUdFLE1BQU0sQ0FBQ0MsZ0JBQWdCLENBQUMsSUFBSSxDQUFDQyxHQUFHLENBQUM7UUFDdEQsSUFBSSxDQUFDQyxvQkFBb0IsRUFBRTtRQUMzQixJQUFJLENBQUNDLG1CQUFtQixFQUFFO1FBQzFCLElBQUlSLFlBQVksQ0FBQ1MsUUFBUSxJQUFJVCxZQUFZLENBQUNTLFFBQVEsS0FBSyxZQUFZLEVBQUU7VUFDbkVDLFVBQVUsQ0FBQztZQUFBLE9BQU0sS0FBSSxDQUFDQyxLQUFLLEVBQUU7VUFBQSxHQUFFWCxZQUFZLENBQUNTLFFBQVEsQ0FBQztRQUN2RDtRQUNBLElBQUksQ0FBQ1IsT0FBTyxHQUFHLElBQUk7TUFDckIsQ0FBQztNQUNETSxvQkFBb0IsRUFBRSxnQ0FBVztRQUFBO1FBQy9CLElBQU1LLE9BQU8sR0FBRyxJQUFJLENBQUNWLGFBQWEsQ0FBQ1UsT0FBTztRQUMxQyxJQUFNQyxJQUFJLEdBQUcsU0FBUEEsSUFBSSxHQUFTO1VBQ2pCakQsU0FBUyxDQUFDLFlBQU07WUFDZCxNQUFJLENBQUMwQyxHQUFHLENBQUNRLEtBQUssQ0FBQ0MsV0FBVyxDQUFDLFNBQVMsRUFBRUgsT0FBTyxDQUFDO1lBQzlDLE1BQUksQ0FBQ04sR0FBRyxDQUFDUSxLQUFLLENBQUNDLFdBQVcsQ0FBQyxZQUFZLEVBQUUsU0FBUyxDQUFDO1VBQ3JELENBQUMsQ0FBQztVQUNGLE1BQUksQ0FBQ1QsR0FBRyxDQUFDVSxVQUFVLEdBQUcsSUFBSTtRQUM1QixDQUFDO1FBQ0QsSUFBTUMsSUFBSSxHQUFHLFNBQVBBLElBQUksR0FBUztVQUNqQnJELFNBQVMsQ0FBQyxZQUFNO1lBQ2QsTUFBSSxDQUFDMEMsR0FBRyxDQUFDVSxVQUFVLEdBQUcsTUFBSSxDQUFDVixHQUFHLENBQUNRLEtBQUssQ0FBQ0MsV0FBVyxDQUFDLFlBQVksRUFBRSxRQUFRLENBQUMsR0FBRyxNQUFJLENBQUNULEdBQUcsQ0FBQ1EsS0FBSyxDQUFDQyxXQUFXLENBQUMsU0FBUyxFQUFFLE1BQU0sQ0FBQztVQUMxSCxDQUFDLENBQUM7UUFDSixDQUFDO1FBQ0QsSUFBTUcsTUFBTSxHQUFHMUIsSUFBSSxDQUFDLFVBQUMxSCxLQUFLO1VBQUEsT0FBS0EsS0FBSyxHQUFHK0ksSUFBSSxFQUFFLEdBQUdJLElBQUksRUFBRTtRQUFBLEdBQUUsVUFBQ25KLEtBQUssRUFBSztVQUNqRSxNQUFJLENBQUN3SSxHQUFHLENBQUNhLGtDQUFrQyxDQUFDLE1BQUksQ0FBQ2IsR0FBRyxFQUFFeEksS0FBSyxFQUFFK0ksSUFBSSxFQUFFSSxJQUFJLENBQUM7UUFDMUUsQ0FBQyxDQUFDO1FBQ0ZuQixNQUFNLENBQUNzQixNQUFNLENBQUM7VUFBQSxPQUFNRixNQUFNLENBQUMsTUFBSSxDQUFDakIsT0FBTyxDQUFDO1FBQUEsRUFBQztNQUMzQyxDQUFDO01BQ0RPLG1CQUFtQixFQUFFLCtCQUFXO1FBQUE7UUFDOUIsSUFBSWEsU0FBUztRQUNiQyxRQUFRLENBQUNDLElBQUksQ0FBQyxrQkFBa0IsRUFBRSxVQUFDQyxDQUFDLEVBQUVDLFNBQVMsRUFBSztVQUNsRCxJQUFJLENBQUNBLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDM0IsSUFBSSxDQUFDNEIsZ0NBQWdDLEVBQUU7WUFDL0Q7VUFDRjtVQUNBLElBQU1DLE1BQU0sR0FBRyxTQUFUQSxNQUFNO1lBQUEsT0FBUyxNQUFJLENBQUN0QixHQUFHLENBQUN1QixxQkFBcUIsRUFBRSxDQUFDQyxHQUFHO1VBQUE7VUFDekQsSUFBTUMsTUFBTSxHQUFHSCxNQUFNLEVBQUU7VUFDdkJQLFNBQVMsR0FBRyxxQkFBTTtZQUNoQixNQUFJLENBQUNmLEdBQUcsQ0FBQzBCLE9BQU8sQ0FBQyxDQUNmO2NBQUNDLFNBQVMsdUJBQWdCRixNQUFNLEdBQUdILE1BQU0sRUFBRTtZQUFLLENBQUMsRUFDakQ7Y0FBQ0ssU0FBUyxFQUFFO1lBQWlCLENBQUMsQ0FDL0IsRUFBRTtjQUNEeEIsUUFBUSxFQUFFLE1BQUksQ0FBQ3lCLHFCQUFxQixFQUFFO2NBQ3RDQyxNQUFNLEVBQUUsTUFBSSxDQUFDakMsYUFBYSxDQUFDa0M7WUFDN0IsQ0FBQyxDQUFDO1VBQ0osQ0FBQztVQUNELE1BQUksQ0FBQzlCLEdBQUcsQ0FBQytCLGFBQWEsRUFBRSxDQUFDakcsT0FBTyxDQUFDLFVBQUNrRyxVQUFVO1lBQUEsT0FBS0EsVUFBVSxDQUFDQyxNQUFNLEVBQUU7VUFBQSxFQUFDO1FBQ3ZFLENBQUMsQ0FBQztRQUNGakIsUUFBUSxDQUFDQyxJQUFJLENBQUMsbUJBQW1CLEVBQUUsVUFBQ0MsQ0FBQyxFQUFFQyxTQUFTLEVBQUs7VUFDbkQsSUFBSSxDQUFDQSxTQUFTLENBQUNDLFVBQVUsQ0FBQzNCLElBQUksQ0FBQzRCLGdDQUFnQyxFQUFFO1lBQy9EO1VBQ0Y7VUFDQSxJQUFJLENBQUMsTUFBSSxDQUFDMUIsT0FBTyxFQUFFO1lBQ2pCO1VBQ0Y7VUFDQW9CLFNBQVMsRUFBRTtRQUNiLENBQUMsQ0FBQztNQUNKLENBQUM7TUFDRFYsS0FBSyxFQUFFLGlCQUFXO1FBQ2hCLElBQUksQ0FBQ1YsT0FBTyxHQUFHLEtBQUs7UUFDcEJTLFVBQVUsQ0FBQztVQUFBLE9BQU1ZLFFBQVEsQ0FBQ2tCLElBQUksQ0FBQyxvQkFBb0IsRUFBRXhDLFlBQVksQ0FBQ3lDLEVBQUUsQ0FBQztRQUFBLEdBQUUsSUFBSSxDQUFDUCxxQkFBcUIsRUFBRSxDQUFDO01BQ3RHLENBQUM7TUFDREEscUJBQXFCLEVBQUUsaUNBQVc7UUFDaEMsT0FBT1EsVUFBVSxDQUFDLElBQUksQ0FBQ3hDLGFBQWEsQ0FBQ3lDLGtCQUFrQixDQUFDLEdBQUcsR0FBRztNQUNoRTtJQUNGLENBQUM7RUFBQSxDQUFDLENBQUM7QUFDTCxDQUFDOztBQUVEO0FBQ0EsSUFBSUMsbUJBQW1CLEdBQUduSyxVQUFVLENBQUNpRCxvQkFBb0IsRUFBRSxDQUFDO0FBQzVELElBQUltSCxZQUFZO0VBQ2Qsd0JBQWM7SUFBQTtJQUNaLElBQUksQ0FBQ0osRUFBRSxDQUFDLENBQUMsQ0FBQyxFQUFFRyxtQkFBbUIsQ0FBQ3BILEVBQUUsR0FBRyxDQUFDO0lBQ3RDLE9BQU8sSUFBSTtFQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0QsWUFBR2lILEdBQUUsRUFBRTtNQUNMLElBQUksQ0FBQ0EsRUFBRSxHQUFHQSxHQUFFO01BQ1osT0FBTyxJQUFJO0lBQ2I7RUFBQztJQUFBO0lBQUEsT0FDRCxlQUFNSyxNQUFLLEVBQUU7TUFDWCxJQUFJLENBQUNBLEtBQUssR0FBR0EsTUFBSztNQUNsQixPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELGNBQUtDLEtBQUksRUFBRTtNQUNULElBQUksQ0FBQ0EsSUFBSSxHQUFHQSxLQUFJO01BQ2hCLE9BQU8sSUFBSTtJQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0QsaUJBQVFDLFFBQU8sRUFBRTtNQUNmLElBQUksQ0FBQ0EsT0FBTyxHQUFHQSxRQUFPO01BQ3RCLE9BQU8sSUFBSTtJQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0QsZ0JBQU9DLE9BQU0sRUFBRTtNQUNiLFFBQVFBLE9BQU07UUFDWixLQUFLLFNBQVM7VUFDWixJQUFJLENBQUNDLE9BQU8sRUFBRTtVQUNkO1FBQ0YsS0FBSyxTQUFTO1VBQ1osSUFBSSxDQUFDQyxPQUFPLEVBQUU7VUFDZDtRQUNGLEtBQUssUUFBUTtVQUNYLElBQUksQ0FBQ0MsTUFBTSxFQUFFO1VBQ2I7TUFBTTtNQUVWLE9BQU8sSUFBSTtJQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0QsY0FBS0MsS0FBSSxFQUFFO01BQ1QsSUFBSSxDQUFDQSxJQUFJLEdBQUdBLEtBQUk7TUFDaEIsT0FBTyxJQUFJO0lBQ2I7RUFBQztJQUFBO0lBQUEsT0FDRCxtQkFBVUMsS0FBSyxFQUFFO01BQ2YsSUFBSSxDQUFDQyxTQUFTLEdBQUdELEtBQUs7TUFDdEIsT0FBTyxJQUFJO0lBQ2I7RUFBQztJQUFBO0lBQUEsT0FDRCxrQkFBUzdDLFNBQVEsRUFBRTtNQUNqQixJQUFJLENBQUNBLFFBQVEsR0FBR0EsU0FBUTtNQUN4QixPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELGlCQUFRK0MsUUFBTyxFQUFFO01BQ2YsSUFBSSxDQUFDL0MsUUFBUSxDQUFDK0MsUUFBTyxHQUFHLEdBQUcsQ0FBQztNQUM1QixPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELHNCQUFhO01BQ1gsSUFBSSxDQUFDL0MsUUFBUSxDQUFDLFlBQVksQ0FBQztNQUMzQixPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELG1CQUFVO01BQ1IsSUFBSSxDQUFDNEMsSUFBSSxDQUFDLHlCQUF5QixDQUFDO01BQ3BDLElBQUksQ0FBQ0UsU0FBUyxDQUFDLFNBQVMsQ0FBQztNQUN6QixPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELG1CQUFVO01BQ1IsSUFBSSxDQUFDRixJQUFJLENBQUMsK0JBQStCLENBQUM7TUFDMUMsSUFBSSxDQUFDRSxTQUFTLENBQUMsU0FBUyxDQUFDO01BQ3pCLE9BQU8sSUFBSTtJQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0Qsa0JBQVM7TUFDUCxJQUFJLENBQUNGLElBQUksQ0FBQyxxQkFBcUIsQ0FBQztNQUNoQyxJQUFJLENBQUNFLFNBQVMsQ0FBQyxRQUFRLENBQUM7TUFDeEIsT0FBTyxJQUFJO0lBQ2I7RUFBQztJQUFBO0lBQUEsT0FDRCxjQUFLRSxLQUFJLEVBQUU7TUFDVCxJQUFJLENBQUNBLElBQUksR0FBR0EsS0FBSTtNQUNoQixPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELGtCQUFTQyxTQUFRLEVBQUU7TUFDakIsSUFBSSxDQUFDQSxRQUFRLEdBQUdBLFNBQVE7TUFDeEIsT0FBTyxJQUFJO0lBQ2I7RUFBQztJQUFBO0lBQUEsT0FDRCxnQkFBTztNQUNMcEMsUUFBUSxDQUFDa0IsSUFBSSxDQUFDLGtCQUFrQixFQUFFLElBQUksQ0FBQztNQUN2QyxPQUFPLElBQUk7SUFDYjtFQUFDO0VBQUE7QUFBQSxHQUNGO0FBQ0QsSUFBSW1CLE1BQU07RUFDUixnQkFBWXRILElBQUksRUFBRTtJQUFBO0lBQ2hCLElBQUksQ0FBQ0EsSUFBSSxDQUFDQSxJQUFJLENBQUM7SUFDZixPQUFPLElBQUk7RUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELGNBQUtBLEtBQUksRUFBRTtNQUNULElBQUksQ0FBQ0EsSUFBSSxHQUFHQSxLQUFJO01BQ2hCLE9BQU8sSUFBSTtJQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0QsZUFBTWlILE1BQUssRUFBRTtNQUNYLElBQUksQ0FBQ0EsS0FBSyxHQUFHQSxNQUFLO01BQ2xCLE9BQU8sSUFBSTtJQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0QsY0FBS00sS0FBSyxFQUFFN0QsSUFBSSxFQUFFO01BQ2hCLElBQUksQ0FBQzZELEtBQUssQ0FBQ0EsS0FBSyxDQUFDO01BQ2pCLElBQUksQ0FBQ0MsU0FBUyxDQUFDOUQsSUFBSSxDQUFDO01BQ3BCLE9BQU8sSUFBSTtJQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0Qsa0JBQVM2RCxLQUFLLEVBQUU3RCxJQUFJLEVBQUU7TUFDcEIsSUFBSSxDQUFDeUMsSUFBSSxDQUFDb0IsS0FBSyxFQUFFN0QsSUFBSSxDQUFDO01BQ3RCLElBQUksQ0FBQytELGFBQWEsR0FBRyxNQUFNO01BQzNCLE9BQU8sSUFBSTtJQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0QsZ0JBQU9yQyxTQUFTLEVBQUVtQyxLQUFLLEVBQUU3RCxJQUFJLEVBQUU7TUFDN0IsSUFBSSxDQUFDeUMsSUFBSSxDQUFDb0IsS0FBSyxFQUFFN0QsSUFBSSxDQUFDO01BQ3RCLElBQUksQ0FBQytELGFBQWEsR0FBRyxJQUFJO01BQ3pCLElBQUksQ0FBQ0MsZUFBZSxHQUFHdEMsU0FBUztNQUNoQyxPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELGdCQUFPbUMsS0FBSyxFQUFFN0QsSUFBSSxFQUFFO01BQ2xCLElBQUksQ0FBQ3lDLElBQUksQ0FBQ29CLEtBQUssRUFBRTdELElBQUksQ0FBQztNQUN0QixJQUFJLENBQUMrRCxhQUFhLEdBQUcsSUFBSTtNQUN6QixPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELHVCQUFjQSxjQUFhLEVBQUU7TUFDM0IsSUFBSSxDQUFDQSxhQUFhLEdBQUdBLGNBQWE7TUFDbEMsT0FBTyxJQUFJO0lBQ2I7RUFBQztJQUFBO0lBQUEsT0FDRCx5QkFBZ0JyQyxTQUFTLEVBQUU7TUFDekIsSUFBSSxDQUFDc0MsZUFBZSxHQUFHdEMsU0FBUztNQUNoQyxPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELGVBQU1tQyxNQUFLLEVBQUU7TUFDWCxJQUFJLENBQUNBLEtBQUssR0FBR0EsTUFBSztNQUNsQixPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELG1CQUFVN0QsSUFBSSxFQUFFO01BQ2QsSUFBSSxDQUFDOEQsU0FBUyxHQUFHOUQsSUFBSTtNQUNyQixPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELHlCQUFnQi9DLFVBQVUsRUFBRTtNQUMxQixJQUFJLENBQUNnSCxlQUFlLEdBQUdoSCxVQUFVO01BQ2pDLE9BQU8sSUFBSTtJQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0QsY0FBS3FHLE1BQUksRUFBRTtNQUNULElBQUksQ0FBQ0EsSUFBSSxHQUFHQSxNQUFJO01BQ2hCLE9BQU8sSUFBSTtJQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0Qsc0JBQWFZLFFBQVEsRUFBRTtNQUNyQixJQUFJLENBQUNDLFlBQVksR0FBR0QsUUFBUTtNQUM1QixPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELG9CQUEyQjtNQUFBLElBQWxCRSxTQUFTLHVFQUFHLElBQUk7TUFDdkIsSUFBSSxDQUFDQyxVQUFVLEdBQUdELFNBQVM7TUFDM0IsT0FBTyxJQUFJO0lBQ2I7RUFBQztJQUFBO0lBQUEsT0FDRCxvQkFBMkI7TUFBQSxJQUFsQkEsU0FBUyx1RUFBRyxJQUFJO01BQ3ZCLElBQUksQ0FBQ0UsVUFBVSxHQUFHRixTQUFTO01BQzNCLE9BQU8sSUFBSTtJQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0QsZUFBTUcsTUFBSyxFQUFFO01BQ1gsSUFBSSxDQUFDQSxLQUFLLEdBQUdBLE1BQUs7TUFDbEIsT0FBTyxJQUFJO0lBQ2I7RUFBQztJQUFBO0lBQUEsT0FDRCxpQkFBd0I7TUFBQSxJQUFsQkgsU0FBUyx1RUFBRyxJQUFJO01BQ3BCLElBQUksQ0FBQ0ksdUJBQXVCLEdBQUdKLFNBQVM7TUFDeEMsT0FBTyxJQUFJO0lBQ2I7RUFBQztJQUFBO0lBQUEsT0FDRCwyQkFBa0M7TUFBQSxJQUFsQkEsU0FBUyx1RUFBRyxJQUFJO01BQzlCLElBQUksQ0FBQ0sscUJBQXFCLEdBQUdMLFNBQVM7TUFDdEMsT0FBTyxJQUFJO0lBQ2I7RUFBQztJQUFBO0lBQUEsT0FDRCxjQUFLTSxLQUFJLEVBQUU7TUFDVCxJQUFJLENBQUNBLElBQUksR0FBR0EsS0FBSTtNQUNoQixPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELGFBQUlDLElBQUcsRUFBRTtNQUNQLElBQUksQ0FBQ0EsR0FBRyxHQUFHQSxJQUFHO01BQ2QsT0FBTyxJQUFJO0lBQ2I7RUFBQztJQUFBO0lBQUEsT0FDRCxjQUFLakIsTUFBSSxFQUFFO01BQ1QsSUFBSSxDQUFDQSxJQUFJLEdBQUdBLE1BQUk7TUFDaEIsT0FBTyxJQUFJO0lBQ2I7RUFBQztJQUFBO0lBQUEsT0FDRCxrQkFBUztNQUNQLElBQUksQ0FBQ0EsSUFBSSxDQUFDLHNDQUFzQyxDQUFDO01BQ2pELE9BQU8sSUFBSTtJQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0QsbUJBQVU7TUFDUixJQUFJLENBQUNBLElBQUksQ0FBQyx1Q0FBdUMsQ0FBQztNQUNsRCxPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELGdCQUFPO01BQ0wsSUFBSSxDQUFDQSxJQUFJLENBQUMsb0NBQW9DLENBQUM7TUFDL0MsT0FBTyxJQUFJO0lBQ2I7RUFBQztFQUFBO0FBQUEsR0FDRjtBQUNELElBQUlrQixXQUFXO0VBQ2IscUJBQVkzQixPQUFPLEVBQUU7SUFBQTtJQUNuQixJQUFJLENBQUNBLE9BQU8sQ0FBQ0EsT0FBTyxDQUFDO0lBQ3JCLE9BQU8sSUFBSTtFQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0QsaUJBQVFBLFNBQU8sRUFBRTtNQUNmLElBQUksQ0FBQ0EsT0FBTyxHQUFHQSxTQUFPLENBQUM0QixHQUFHLENBQUMsVUFBQ0MsTUFBTTtRQUFBLE9BQUtBLE1BQU0sQ0FBQ0MsT0FBTyxFQUFFO01BQUEsRUFBQztNQUN4RCxPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELGVBQU14QixPQUFLLEVBQUU7TUFDWCxJQUFJLENBQUNBLEtBQUssR0FBR0EsT0FBSztNQUNsQixPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELGNBQUtELE1BQUksRUFBRTtNQUNULElBQUksQ0FBQ0EsSUFBSSxHQUFHQSxNQUFJO01BQ2hCLE9BQU8sSUFBSTtJQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0Qsc0JBQWFZLFFBQVEsRUFBRTtNQUNyQixJQUFJLENBQUNDLFlBQVksR0FBR0QsUUFBUTtNQUM1QixPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUE7SUFBQSxPQUNELGVBQU1LLE9BQUssRUFBRTtNQUNYLElBQUksQ0FBQ0EsS0FBSyxHQUFHQSxPQUFLO01BQ2xCLE9BQU8sSUFBSTtJQUNiO0VBQUM7SUFBQTtJQUFBLE9BQ0QsaUJBQVFTLFFBQU8sRUFBRTtNQUNmLElBQUksQ0FBQ0EsT0FBTyxHQUFHQSxRQUFPO01BQ3RCLE9BQU8sSUFBSTtJQUNiO0VBQUM7RUFBQTtBQUFBLEdBQ0Y7O0FBRUQ7QUFDQTNFLE1BQU0sQ0FBQzRFLGtCQUFrQixHQUFHckIsTUFBTTtBQUNsQ3ZELE1BQU0sQ0FBQzZFLHVCQUF1QixHQUFHTixXQUFXO0FBQzVDdkUsTUFBTSxDQUFDeUMsWUFBWSxHQUFHQSxZQUFZO0FBQ2xDLElBQUlxQyxVQUFVLEdBQUcsU0FBYkEsVUFBVSxDQUFJcEYsTUFBTSxFQUFLO0VBQzNCQSxNQUFNLENBQUNxRixNQUFNLENBQUN0RixvQkFBb0IsQ0FBQztBQUNyQyxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vdmVuZG9yL2ZpbGFtZW50L25vdGlmaWNhdGlvbnMvZGlzdC9tb2R1bGUuZXNtLmpzPzY5N2UiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIF9fY3JlYXRlID0gT2JqZWN0LmNyZWF0ZTtcbnZhciBfX2RlZlByb3AgPSBPYmplY3QuZGVmaW5lUHJvcGVydHk7XG52YXIgX19nZXRQcm90b09mID0gT2JqZWN0LmdldFByb3RvdHlwZU9mO1xudmFyIF9faGFzT3duUHJvcCA9IE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHk7XG52YXIgX19nZXRPd25Qcm9wTmFtZXMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlOYW1lcztcbnZhciBfX2dldE93blByb3BEZXNjID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcjtcbnZhciBfX21hcmtBc01vZHVsZSA9ICh0YXJnZXQpID0+IF9fZGVmUHJvcCh0YXJnZXQsIFwiX19lc01vZHVsZVwiLCB7dmFsdWU6IHRydWV9KTtcbnZhciBfX2NvbW1vbkpTID0gKGNhbGxiYWNrLCBtb2R1bGUpID0+ICgpID0+IHtcbiAgaWYgKCFtb2R1bGUpIHtcbiAgICBtb2R1bGUgPSB7ZXhwb3J0czoge319O1xuICAgIGNhbGxiYWNrKG1vZHVsZS5leHBvcnRzLCBtb2R1bGUpO1xuICB9XG4gIHJldHVybiBtb2R1bGUuZXhwb3J0cztcbn07XG52YXIgX19leHBvcnRTdGFyID0gKHRhcmdldCwgbW9kdWxlLCBkZXNjKSA9PiB7XG4gIGlmIChtb2R1bGUgJiYgdHlwZW9mIG1vZHVsZSA9PT0gXCJvYmplY3RcIiB8fCB0eXBlb2YgbW9kdWxlID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICBmb3IgKGxldCBrZXkgb2YgX19nZXRPd25Qcm9wTmFtZXMobW9kdWxlKSlcbiAgICAgIGlmICghX19oYXNPd25Qcm9wLmNhbGwodGFyZ2V0LCBrZXkpICYmIGtleSAhPT0gXCJkZWZhdWx0XCIpXG4gICAgICAgIF9fZGVmUHJvcCh0YXJnZXQsIGtleSwge2dldDogKCkgPT4gbW9kdWxlW2tleV0sIGVudW1lcmFibGU6ICEoZGVzYyA9IF9fZ2V0T3duUHJvcERlc2MobW9kdWxlLCBrZXkpKSB8fCBkZXNjLmVudW1lcmFibGV9KTtcbiAgfVxuICByZXR1cm4gdGFyZ2V0O1xufTtcbnZhciBfX3RvTW9kdWxlID0gKG1vZHVsZSkgPT4ge1xuICByZXR1cm4gX19leHBvcnRTdGFyKF9fbWFya0FzTW9kdWxlKF9fZGVmUHJvcChtb2R1bGUgIT0gbnVsbCA/IF9fY3JlYXRlKF9fZ2V0UHJvdG9PZihtb2R1bGUpKSA6IHt9LCBcImRlZmF1bHRcIiwgbW9kdWxlICYmIG1vZHVsZS5fX2VzTW9kdWxlICYmIFwiZGVmYXVsdFwiIGluIG1vZHVsZSA/IHtnZXQ6ICgpID0+IG1vZHVsZS5kZWZhdWx0LCBlbnVtZXJhYmxlOiB0cnVlfSA6IHt2YWx1ZTogbW9kdWxlLCBlbnVtZXJhYmxlOiB0cnVlfSkpLCBtb2R1bGUpO1xufTtcblxuLy8gbm9kZV9tb2R1bGVzL3V1aWQtYnJvd3Nlci9saWIvcm5nLWJyb3dzZXIuanNcbnZhciByZXF1aXJlX3JuZ19icm93c2VyID0gX19jb21tb25KUygoZXhwb3J0cywgbW9kdWxlKSA9PiB7XG4gIHZhciBybmc7XG4gIHZhciBjcnlwdG8gPSB0eXBlb2YgZ2xvYmFsICE9PSBcInVuZGVmaW5lZFwiICYmIChnbG9iYWwuY3J5cHRvIHx8IGdsb2JhbC5tc0NyeXB0byk7XG4gIGlmIChjcnlwdG8gJiYgY3J5cHRvLmdldFJhbmRvbVZhbHVlcykge1xuICAgIHJuZHM4ID0gbmV3IFVpbnQ4QXJyYXkoMTYpO1xuICAgIHJuZyA9IGZ1bmN0aW9uIHdoYXR3Z1JORygpIHtcbiAgICAgIGNyeXB0by5nZXRSYW5kb21WYWx1ZXMocm5kczgpO1xuICAgICAgcmV0dXJuIHJuZHM4O1xuICAgIH07XG4gIH1cbiAgdmFyIHJuZHM4O1xuICBpZiAoIXJuZykge1xuICAgIHJuZHMgPSBuZXcgQXJyYXkoMTYpO1xuICAgIHJuZyA9IGZ1bmN0aW9uKCkge1xuICAgICAgZm9yICh2YXIgaSA9IDAsIHI7IGkgPCAxNjsgaSsrKSB7XG4gICAgICAgIGlmICgoaSAmIDMpID09PSAwKVxuICAgICAgICAgIHIgPSBNYXRoLnJhbmRvbSgpICogNDI5NDk2NzI5NjtcbiAgICAgICAgcm5kc1tpXSA9IHIgPj4+ICgoaSAmIDMpIDw8IDMpICYgMjU1O1xuICAgICAgfVxuICAgICAgcmV0dXJuIHJuZHM7XG4gICAgfTtcbiAgfVxuICB2YXIgcm5kcztcbiAgbW9kdWxlLmV4cG9ydHMgPSBybmc7XG59KTtcblxuLy8gbm9kZV9tb2R1bGVzL3V1aWQtYnJvd3Nlci9saWIvYnl0ZXNUb1V1aWQuanNcbnZhciByZXF1aXJlX2J5dGVzVG9VdWlkID0gX19jb21tb25KUygoZXhwb3J0cywgbW9kdWxlKSA9PiB7XG4gIHZhciBieXRlVG9IZXggPSBbXTtcbiAgZm9yICh2YXIgaSA9IDA7IGkgPCAyNTY7ICsraSkge1xuICAgIGJ5dGVUb0hleFtpXSA9IChpICsgMjU2KS50b1N0cmluZygxNikuc3Vic3RyKDEpO1xuICB9XG4gIGZ1bmN0aW9uIGJ5dGVzVG9VdWlkKGJ1Ziwgb2Zmc2V0KSB7XG4gICAgdmFyIGkyID0gb2Zmc2V0IHx8IDA7XG4gICAgdmFyIGJ0aCA9IGJ5dGVUb0hleDtcbiAgICByZXR1cm4gYnRoW2J1ZltpMisrXV0gKyBidGhbYnVmW2kyKytdXSArIGJ0aFtidWZbaTIrK11dICsgYnRoW2J1ZltpMisrXV0gKyBcIi1cIiArIGJ0aFtidWZbaTIrK11dICsgYnRoW2J1ZltpMisrXV0gKyBcIi1cIiArIGJ0aFtidWZbaTIrK11dICsgYnRoW2J1ZltpMisrXV0gKyBcIi1cIiArIGJ0aFtidWZbaTIrK11dICsgYnRoW2J1ZltpMisrXV0gKyBcIi1cIiArIGJ0aFtidWZbaTIrK11dICsgYnRoW2J1ZltpMisrXV0gKyBidGhbYnVmW2kyKytdXSArIGJ0aFtidWZbaTIrK11dICsgYnRoW2J1ZltpMisrXV0gKyBidGhbYnVmW2kyKytdXTtcbiAgfVxuICBtb2R1bGUuZXhwb3J0cyA9IGJ5dGVzVG9VdWlkO1xufSk7XG5cbi8vIG5vZGVfbW9kdWxlcy91dWlkLWJyb3dzZXIvdjEuanNcbnZhciByZXF1aXJlX3YxID0gX19jb21tb25KUygoZXhwb3J0cywgbW9kdWxlKSA9PiB7XG4gIHZhciBybmcgPSByZXF1aXJlX3JuZ19icm93c2VyKCk7XG4gIHZhciBieXRlc1RvVXVpZCA9IHJlcXVpcmVfYnl0ZXNUb1V1aWQoKTtcbiAgdmFyIF9zZWVkQnl0ZXMgPSBybmcoKTtcbiAgdmFyIF9ub2RlSWQgPSBbXG4gICAgX3NlZWRCeXRlc1swXSB8IDEsXG4gICAgX3NlZWRCeXRlc1sxXSxcbiAgICBfc2VlZEJ5dGVzWzJdLFxuICAgIF9zZWVkQnl0ZXNbM10sXG4gICAgX3NlZWRCeXRlc1s0XSxcbiAgICBfc2VlZEJ5dGVzWzVdXG4gIF07XG4gIHZhciBfY2xvY2tzZXEgPSAoX3NlZWRCeXRlc1s2XSA8PCA4IHwgX3NlZWRCeXRlc1s3XSkgJiAxNjM4MztcbiAgdmFyIF9sYXN0TVNlY3MgPSAwO1xuICB2YXIgX2xhc3ROU2VjcyA9IDA7XG4gIGZ1bmN0aW9uIHYxKG9wdGlvbnMsIGJ1Ziwgb2Zmc2V0KSB7XG4gICAgdmFyIGkgPSBidWYgJiYgb2Zmc2V0IHx8IDA7XG4gICAgdmFyIGIgPSBidWYgfHwgW107XG4gICAgb3B0aW9ucyA9IG9wdGlvbnMgfHwge307XG4gICAgdmFyIGNsb2Nrc2VxID0gb3B0aW9ucy5jbG9ja3NlcSAhPT0gdm9pZCAwID8gb3B0aW9ucy5jbG9ja3NlcSA6IF9jbG9ja3NlcTtcbiAgICB2YXIgbXNlY3MgPSBvcHRpb25zLm1zZWNzICE9PSB2b2lkIDAgPyBvcHRpb25zLm1zZWNzIDogbmV3IERhdGUoKS5nZXRUaW1lKCk7XG4gICAgdmFyIG5zZWNzID0gb3B0aW9ucy5uc2VjcyAhPT0gdm9pZCAwID8gb3B0aW9ucy5uc2VjcyA6IF9sYXN0TlNlY3MgKyAxO1xuICAgIHZhciBkdCA9IG1zZWNzIC0gX2xhc3RNU2VjcyArIChuc2VjcyAtIF9sYXN0TlNlY3MpIC8gMWU0O1xuICAgIGlmIChkdCA8IDAgJiYgb3B0aW9ucy5jbG9ja3NlcSA9PT0gdm9pZCAwKSB7XG4gICAgICBjbG9ja3NlcSA9IGNsb2Nrc2VxICsgMSAmIDE2MzgzO1xuICAgIH1cbiAgICBpZiAoKGR0IDwgMCB8fCBtc2VjcyA+IF9sYXN0TVNlY3MpICYmIG9wdGlvbnMubnNlY3MgPT09IHZvaWQgMCkge1xuICAgICAgbnNlY3MgPSAwO1xuICAgIH1cbiAgICBpZiAobnNlY3MgPj0gMWU0KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJ1dWlkLnYxKCk6IENhbid0IGNyZWF0ZSBtb3JlIHRoYW4gMTBNIHV1aWRzL3NlY1wiKTtcbiAgICB9XG4gICAgX2xhc3RNU2VjcyA9IG1zZWNzO1xuICAgIF9sYXN0TlNlY3MgPSBuc2VjcztcbiAgICBfY2xvY2tzZXEgPSBjbG9ja3NlcTtcbiAgICBtc2VjcyArPSAxMjIxOTI5MjhlNTtcbiAgICB2YXIgdGwgPSAoKG1zZWNzICYgMjY4NDM1NDU1KSAqIDFlNCArIG5zZWNzKSAlIDQyOTQ5NjcyOTY7XG4gICAgYltpKytdID0gdGwgPj4+IDI0ICYgMjU1O1xuICAgIGJbaSsrXSA9IHRsID4+PiAxNiAmIDI1NTtcbiAgICBiW2krK10gPSB0bCA+Pj4gOCAmIDI1NTtcbiAgICBiW2krK10gPSB0bCAmIDI1NTtcbiAgICB2YXIgdG1oID0gbXNlY3MgLyA0Mjk0OTY3Mjk2ICogMWU0ICYgMjY4NDM1NDU1O1xuICAgIGJbaSsrXSA9IHRtaCA+Pj4gOCAmIDI1NTtcbiAgICBiW2krK10gPSB0bWggJiAyNTU7XG4gICAgYltpKytdID0gdG1oID4+PiAyNCAmIDE1IHwgMTY7XG4gICAgYltpKytdID0gdG1oID4+PiAxNiAmIDI1NTtcbiAgICBiW2krK10gPSBjbG9ja3NlcSA+Pj4gOCB8IDEyODtcbiAgICBiW2krK10gPSBjbG9ja3NlcSAmIDI1NTtcbiAgICB2YXIgbm9kZSA9IG9wdGlvbnMubm9kZSB8fCBfbm9kZUlkO1xuICAgIGZvciAodmFyIG4gPSAwOyBuIDwgNjsgKytuKSB7XG4gICAgICBiW2kgKyBuXSA9IG5vZGVbbl07XG4gICAgfVxuICAgIHJldHVybiBidWYgPyBidWYgOiBieXRlc1RvVXVpZChiKTtcbiAgfVxuICBtb2R1bGUuZXhwb3J0cyA9IHYxO1xufSk7XG5cbi8vIG5vZGVfbW9kdWxlcy91dWlkLWJyb3dzZXIvdjQuanNcbnZhciByZXF1aXJlX3Y0ID0gX19jb21tb25KUygoZXhwb3J0cywgbW9kdWxlKSA9PiB7XG4gIHZhciBybmcgPSByZXF1aXJlX3JuZ19icm93c2VyKCk7XG4gIHZhciBieXRlc1RvVXVpZCA9IHJlcXVpcmVfYnl0ZXNUb1V1aWQoKTtcbiAgZnVuY3Rpb24gdjQob3B0aW9ucywgYnVmLCBvZmZzZXQpIHtcbiAgICB2YXIgaSA9IGJ1ZiAmJiBvZmZzZXQgfHwgMDtcbiAgICBpZiAodHlwZW9mIG9wdGlvbnMgPT0gXCJzdHJpbmdcIikge1xuICAgICAgYnVmID0gb3B0aW9ucyA9PSBcImJpbmFyeVwiID8gbmV3IEFycmF5KDE2KSA6IG51bGw7XG4gICAgICBvcHRpb25zID0gbnVsbDtcbiAgICB9XG4gICAgb3B0aW9ucyA9IG9wdGlvbnMgfHwge307XG4gICAgdmFyIHJuZHMgPSBvcHRpb25zLnJhbmRvbSB8fCAob3B0aW9ucy5ybmcgfHwgcm5nKSgpO1xuICAgIHJuZHNbNl0gPSBybmRzWzZdICYgMTUgfCA2NDtcbiAgICBybmRzWzhdID0gcm5kc1s4XSAmIDYzIHwgMTI4O1xuICAgIGlmIChidWYpIHtcbiAgICAgIGZvciAodmFyIGlpID0gMDsgaWkgPCAxNjsgKytpaSkge1xuICAgICAgICBidWZbaSArIGlpXSA9IHJuZHNbaWldO1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gYnVmIHx8IGJ5dGVzVG9VdWlkKHJuZHMpO1xuICB9XG4gIG1vZHVsZS5leHBvcnRzID0gdjQ7XG59KTtcblxuLy8gbm9kZV9tb2R1bGVzL3V1aWQtYnJvd3Nlci9pbmRleC5qc1xudmFyIHJlcXVpcmVfdXVpZF9icm93c2VyID0gX19jb21tb25KUygoZXhwb3J0cywgbW9kdWxlKSA9PiB7XG4gIHZhciB2MSA9IHJlcXVpcmVfdjEoKTtcbiAgdmFyIHY0ID0gcmVxdWlyZV92NCgpO1xuICB2YXIgdXVpZDIgPSB2NDtcbiAgdXVpZDIudjEgPSB2MTtcbiAgdXVpZDIudjQgPSB2NDtcbiAgbW9kdWxlLmV4cG9ydHMgPSB1dWlkMjtcbn0pO1xuXG4vLyBub2RlX21vZHVsZXMvYWxwaW5lanMvc3JjL211dGF0aW9uLmpzXG52YXIgb25BdHRyaWJ1dGVBZGRlZHMgPSBbXTtcbnZhciBvbkVsUmVtb3ZlZHMgPSBbXTtcbnZhciBvbkVsQWRkZWRzID0gW107XG5mdW5jdGlvbiBjbGVhbnVwQXR0cmlidXRlcyhlbCwgbmFtZXMpIHtcbiAgaWYgKCFlbC5feF9hdHRyaWJ1dGVDbGVhbnVwcylcbiAgICByZXR1cm47XG4gIE9iamVjdC5lbnRyaWVzKGVsLl94X2F0dHJpYnV0ZUNsZWFudXBzKS5mb3JFYWNoKChbbmFtZSwgdmFsdWVdKSA9PiB7XG4gICAgaWYgKG5hbWVzID09PSB2b2lkIDAgfHwgbmFtZXMuaW5jbHVkZXMobmFtZSkpIHtcbiAgICAgIHZhbHVlLmZvckVhY2goKGkpID0+IGkoKSk7XG4gICAgICBkZWxldGUgZWwuX3hfYXR0cmlidXRlQ2xlYW51cHNbbmFtZV07XG4gICAgfVxuICB9KTtcbn1cbnZhciBvYnNlcnZlciA9IG5ldyBNdXRhdGlvbk9ic2VydmVyKG9uTXV0YXRlKTtcbnZhciBjdXJyZW50bHlPYnNlcnZpbmcgPSBmYWxzZTtcbmZ1bmN0aW9uIHN0YXJ0T2JzZXJ2aW5nTXV0YXRpb25zKCkge1xuICBvYnNlcnZlci5vYnNlcnZlKGRvY3VtZW50LCB7c3VidHJlZTogdHJ1ZSwgY2hpbGRMaXN0OiB0cnVlLCBhdHRyaWJ1dGVzOiB0cnVlLCBhdHRyaWJ1dGVPbGRWYWx1ZTogdHJ1ZX0pO1xuICBjdXJyZW50bHlPYnNlcnZpbmcgPSB0cnVlO1xufVxuZnVuY3Rpb24gc3RvcE9ic2VydmluZ011dGF0aW9ucygpIHtcbiAgZmx1c2hPYnNlcnZlcigpO1xuICBvYnNlcnZlci5kaXNjb25uZWN0KCk7XG4gIGN1cnJlbnRseU9ic2VydmluZyA9IGZhbHNlO1xufVxudmFyIHJlY29yZFF1ZXVlID0gW107XG52YXIgd2lsbFByb2Nlc3NSZWNvcmRRdWV1ZSA9IGZhbHNlO1xuZnVuY3Rpb24gZmx1c2hPYnNlcnZlcigpIHtcbiAgcmVjb3JkUXVldWUgPSByZWNvcmRRdWV1ZS5jb25jYXQob2JzZXJ2ZXIudGFrZVJlY29yZHMoKSk7XG4gIGlmIChyZWNvcmRRdWV1ZS5sZW5ndGggJiYgIXdpbGxQcm9jZXNzUmVjb3JkUXVldWUpIHtcbiAgICB3aWxsUHJvY2Vzc1JlY29yZFF1ZXVlID0gdHJ1ZTtcbiAgICBxdWV1ZU1pY3JvdGFzaygoKSA9PiB7XG4gICAgICBwcm9jZXNzUmVjb3JkUXVldWUoKTtcbiAgICAgIHdpbGxQcm9jZXNzUmVjb3JkUXVldWUgPSBmYWxzZTtcbiAgICB9KTtcbiAgfVxufVxuZnVuY3Rpb24gcHJvY2Vzc1JlY29yZFF1ZXVlKCkge1xuICBvbk11dGF0ZShyZWNvcmRRdWV1ZSk7XG4gIHJlY29yZFF1ZXVlLmxlbmd0aCA9IDA7XG59XG5mdW5jdGlvbiBtdXRhdGVEb20oY2FsbGJhY2spIHtcbiAgaWYgKCFjdXJyZW50bHlPYnNlcnZpbmcpXG4gICAgcmV0dXJuIGNhbGxiYWNrKCk7XG4gIHN0b3BPYnNlcnZpbmdNdXRhdGlvbnMoKTtcbiAgbGV0IHJlc3VsdCA9IGNhbGxiYWNrKCk7XG4gIHN0YXJ0T2JzZXJ2aW5nTXV0YXRpb25zKCk7XG4gIHJldHVybiByZXN1bHQ7XG59XG52YXIgaXNDb2xsZWN0aW5nID0gZmFsc2U7XG52YXIgZGVmZXJyZWRNdXRhdGlvbnMgPSBbXTtcbmZ1bmN0aW9uIG9uTXV0YXRlKG11dGF0aW9ucykge1xuICBpZiAoaXNDb2xsZWN0aW5nKSB7XG4gICAgZGVmZXJyZWRNdXRhdGlvbnMgPSBkZWZlcnJlZE11dGF0aW9ucy5jb25jYXQobXV0YXRpb25zKTtcbiAgICByZXR1cm47XG4gIH1cbiAgbGV0IGFkZGVkTm9kZXMgPSBbXTtcbiAgbGV0IHJlbW92ZWROb2RlcyA9IFtdO1xuICBsZXQgYWRkZWRBdHRyaWJ1dGVzID0gbmV3IE1hcCgpO1xuICBsZXQgcmVtb3ZlZEF0dHJpYnV0ZXMgPSBuZXcgTWFwKCk7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgbXV0YXRpb25zLmxlbmd0aDsgaSsrKSB7XG4gICAgaWYgKG11dGF0aW9uc1tpXS50YXJnZXQuX3hfaWdub3JlTXV0YXRpb25PYnNlcnZlcilcbiAgICAgIGNvbnRpbnVlO1xuICAgIGlmIChtdXRhdGlvbnNbaV0udHlwZSA9PT0gXCJjaGlsZExpc3RcIikge1xuICAgICAgbXV0YXRpb25zW2ldLmFkZGVkTm9kZXMuZm9yRWFjaCgobm9kZSkgPT4gbm9kZS5ub2RlVHlwZSA9PT0gMSAmJiBhZGRlZE5vZGVzLnB1c2gobm9kZSkpO1xuICAgICAgbXV0YXRpb25zW2ldLnJlbW92ZWROb2Rlcy5mb3JFYWNoKChub2RlKSA9PiBub2RlLm5vZGVUeXBlID09PSAxICYmIHJlbW92ZWROb2Rlcy5wdXNoKG5vZGUpKTtcbiAgICB9XG4gICAgaWYgKG11dGF0aW9uc1tpXS50eXBlID09PSBcImF0dHJpYnV0ZXNcIikge1xuICAgICAgbGV0IGVsID0gbXV0YXRpb25zW2ldLnRhcmdldDtcbiAgICAgIGxldCBuYW1lID0gbXV0YXRpb25zW2ldLmF0dHJpYnV0ZU5hbWU7XG4gICAgICBsZXQgb2xkVmFsdWUgPSBtdXRhdGlvbnNbaV0ub2xkVmFsdWU7XG4gICAgICBsZXQgYWRkID0gKCkgPT4ge1xuICAgICAgICBpZiAoIWFkZGVkQXR0cmlidXRlcy5oYXMoZWwpKVxuICAgICAgICAgIGFkZGVkQXR0cmlidXRlcy5zZXQoZWwsIFtdKTtcbiAgICAgICAgYWRkZWRBdHRyaWJ1dGVzLmdldChlbCkucHVzaCh7bmFtZSwgdmFsdWU6IGVsLmdldEF0dHJpYnV0ZShuYW1lKX0pO1xuICAgICAgfTtcbiAgICAgIGxldCByZW1vdmUgPSAoKSA9PiB7XG4gICAgICAgIGlmICghcmVtb3ZlZEF0dHJpYnV0ZXMuaGFzKGVsKSlcbiAgICAgICAgICByZW1vdmVkQXR0cmlidXRlcy5zZXQoZWwsIFtdKTtcbiAgICAgICAgcmVtb3ZlZEF0dHJpYnV0ZXMuZ2V0KGVsKS5wdXNoKG5hbWUpO1xuICAgICAgfTtcbiAgICAgIGlmIChlbC5oYXNBdHRyaWJ1dGUobmFtZSkgJiYgb2xkVmFsdWUgPT09IG51bGwpIHtcbiAgICAgICAgYWRkKCk7XG4gICAgICB9IGVsc2UgaWYgKGVsLmhhc0F0dHJpYnV0ZShuYW1lKSkge1xuICAgICAgICByZW1vdmUoKTtcbiAgICAgICAgYWRkKCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZW1vdmUoKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmVtb3ZlZEF0dHJpYnV0ZXMuZm9yRWFjaCgoYXR0cnMsIGVsKSA9PiB7XG4gICAgY2xlYW51cEF0dHJpYnV0ZXMoZWwsIGF0dHJzKTtcbiAgfSk7XG4gIGFkZGVkQXR0cmlidXRlcy5mb3JFYWNoKChhdHRycywgZWwpID0+IHtcbiAgICBvbkF0dHJpYnV0ZUFkZGVkcy5mb3JFYWNoKChpKSA9PiBpKGVsLCBhdHRycykpO1xuICB9KTtcbiAgZm9yIChsZXQgbm9kZSBvZiByZW1vdmVkTm9kZXMpIHtcbiAgICBpZiAoYWRkZWROb2Rlcy5pbmNsdWRlcyhub2RlKSlcbiAgICAgIGNvbnRpbnVlO1xuICAgIG9uRWxSZW1vdmVkcy5mb3JFYWNoKChpKSA9PiBpKG5vZGUpKTtcbiAgICBpZiAobm9kZS5feF9jbGVhbnVwcykge1xuICAgICAgd2hpbGUgKG5vZGUuX3hfY2xlYW51cHMubGVuZ3RoKVxuICAgICAgICBub2RlLl94X2NsZWFudXBzLnBvcCgpKCk7XG4gICAgfVxuICB9XG4gIGFkZGVkTm9kZXMuZm9yRWFjaCgobm9kZSkgPT4ge1xuICAgIG5vZGUuX3hfaWdub3JlU2VsZiA9IHRydWU7XG4gICAgbm9kZS5feF9pZ25vcmUgPSB0cnVlO1xuICB9KTtcbiAgZm9yIChsZXQgbm9kZSBvZiBhZGRlZE5vZGVzKSB7XG4gICAgaWYgKHJlbW92ZWROb2Rlcy5pbmNsdWRlcyhub2RlKSlcbiAgICAgIGNvbnRpbnVlO1xuICAgIGlmICghbm9kZS5pc0Nvbm5lY3RlZClcbiAgICAgIGNvbnRpbnVlO1xuICAgIGRlbGV0ZSBub2RlLl94X2lnbm9yZVNlbGY7XG4gICAgZGVsZXRlIG5vZGUuX3hfaWdub3JlO1xuICAgIG9uRWxBZGRlZHMuZm9yRWFjaCgoaSkgPT4gaShub2RlKSk7XG4gICAgbm9kZS5feF9pZ25vcmUgPSB0cnVlO1xuICAgIG5vZGUuX3hfaWdub3JlU2VsZiA9IHRydWU7XG4gIH1cbiAgYWRkZWROb2Rlcy5mb3JFYWNoKChub2RlKSA9PiB7XG4gICAgZGVsZXRlIG5vZGUuX3hfaWdub3JlU2VsZjtcbiAgICBkZWxldGUgbm9kZS5feF9pZ25vcmU7XG4gIH0pO1xuICBhZGRlZE5vZGVzID0gbnVsbDtcbiAgcmVtb3ZlZE5vZGVzID0gbnVsbDtcbiAgYWRkZWRBdHRyaWJ1dGVzID0gbnVsbDtcbiAgcmVtb3ZlZEF0dHJpYnV0ZXMgPSBudWxsO1xufVxuXG4vLyBub2RlX21vZHVsZXMvYWxwaW5lanMvc3JjL3V0aWxzL29uY2UuanNcbmZ1bmN0aW9uIG9uY2UoY2FsbGJhY2ssIGZhbGxiYWNrID0gKCkgPT4ge1xufSkge1xuICBsZXQgY2FsbGVkID0gZmFsc2U7XG4gIHJldHVybiBmdW5jdGlvbigpIHtcbiAgICBpZiAoIWNhbGxlZCkge1xuICAgICAgY2FsbGVkID0gdHJ1ZTtcbiAgICAgIGNhbGxiYWNrLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGZhbGxiYWNrLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gICAgfVxuICB9O1xufVxuXG4vLyBwYWNrYWdlcy9ub3RpZmljYXRpb25zL3Jlc291cmNlcy9qcy9jb21wb25lbnRzL25vdGlmaWNhdGlvbi5qc1xudmFyIG5vdGlmaWNhdGlvbl9kZWZhdWx0ID0gKEFscGluZSkgPT4ge1xuICBBbHBpbmUuZGF0YShcIm5vdGlmaWNhdGlvbkNvbXBvbmVudFwiLCAoe25vdGlmaWNhdGlvbn0pID0+ICh7XG4gICAgaXNTaG93bjogZmFsc2UsXG4gICAgY29tcHV0ZWRTdHlsZTogbnVsbCxcbiAgICBpbml0OiBmdW5jdGlvbigpIHtcbiAgICAgIHRoaXMuY29tcHV0ZWRTdHlsZSA9IHdpbmRvdy5nZXRDb21wdXRlZFN0eWxlKHRoaXMuJGVsKTtcbiAgICAgIHRoaXMuY29uZmlndXJlVHJhbnNpdGlvbnMoKTtcbiAgICAgIHRoaXMuY29uZmlndXJlQW5pbWF0aW9ucygpO1xuICAgICAgaWYgKG5vdGlmaWNhdGlvbi5kdXJhdGlvbiAmJiBub3RpZmljYXRpb24uZHVyYXRpb24gIT09IFwicGVyc2lzdGVudFwiKSB7XG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4gdGhpcy5jbG9zZSgpLCBub3RpZmljYXRpb24uZHVyYXRpb24pO1xuICAgICAgfVxuICAgICAgdGhpcy5pc1Nob3duID0gdHJ1ZTtcbiAgICB9LFxuICAgIGNvbmZpZ3VyZVRyYW5zaXRpb25zOiBmdW5jdGlvbigpIHtcbiAgICAgIGNvbnN0IGRpc3BsYXkgPSB0aGlzLmNvbXB1dGVkU3R5bGUuZGlzcGxheTtcbiAgICAgIGNvbnN0IHNob3cgPSAoKSA9PiB7XG4gICAgICAgIG11dGF0ZURvbSgoKSA9PiB7XG4gICAgICAgICAgdGhpcy4kZWwuc3R5bGUuc2V0UHJvcGVydHkoXCJkaXNwbGF5XCIsIGRpc3BsYXkpO1xuICAgICAgICAgIHRoaXMuJGVsLnN0eWxlLnNldFByb3BlcnR5KFwidmlzaWJpbGl0eVwiLCBcInZpc2libGVcIik7XG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLiRlbC5feF9pc1Nob3duID0gdHJ1ZTtcbiAgICAgIH07XG4gICAgICBjb25zdCBoaWRlID0gKCkgPT4ge1xuICAgICAgICBtdXRhdGVEb20oKCkgPT4ge1xuICAgICAgICAgIHRoaXMuJGVsLl94X2lzU2hvd24gPyB0aGlzLiRlbC5zdHlsZS5zZXRQcm9wZXJ0eShcInZpc2liaWxpdHlcIiwgXCJoaWRkZW5cIikgOiB0aGlzLiRlbC5zdHlsZS5zZXRQcm9wZXJ0eShcImRpc3BsYXlcIiwgXCJub25lXCIpO1xuICAgICAgICB9KTtcbiAgICAgIH07XG4gICAgICBjb25zdCB0b2dnbGUgPSBvbmNlKCh2YWx1ZSkgPT4gdmFsdWUgPyBzaG93KCkgOiBoaWRlKCksICh2YWx1ZSkgPT4ge1xuICAgICAgICB0aGlzLiRlbC5feF90b2dnbGVBbmRDYXNjYWRlV2l0aFRyYW5zaXRpb25zKHRoaXMuJGVsLCB2YWx1ZSwgc2hvdywgaGlkZSk7XG4gICAgICB9KTtcbiAgICAgIEFscGluZS5lZmZlY3QoKCkgPT4gdG9nZ2xlKHRoaXMuaXNTaG93bikpO1xuICAgIH0sXG4gICAgY29uZmlndXJlQW5pbWF0aW9uczogZnVuY3Rpb24oKSB7XG4gICAgICBsZXQgYW5pbWF0aW9uO1xuICAgICAgTGl2ZXdpcmUuaG9vayhcIm1lc3NhZ2UucmVjZWl2ZWRcIiwgKF8sIGNvbXBvbmVudCkgPT4ge1xuICAgICAgICBpZiAoIWNvbXBvbmVudC5zZXJ2ZXJNZW1vLmRhdGEuaXNGaWxhbWVudE5vdGlmaWNhdGlvbnNDb21wb25lbnQpIHtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgZ2V0VG9wID0gKCkgPT4gdGhpcy4kZWwuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCkudG9wO1xuICAgICAgICBjb25zdCBvbGRUb3AgPSBnZXRUb3AoKTtcbiAgICAgICAgYW5pbWF0aW9uID0gKCkgPT4ge1xuICAgICAgICAgIHRoaXMuJGVsLmFuaW1hdGUoW1xuICAgICAgICAgICAge3RyYW5zZm9ybTogYHRyYW5zbGF0ZVkoJHtvbGRUb3AgLSBnZXRUb3AoKX1weClgfSxcbiAgICAgICAgICAgIHt0cmFuc2Zvcm06IFwidHJhbnNsYXRlWSgwcHgpXCJ9XG4gICAgICAgICAgXSwge1xuICAgICAgICAgICAgZHVyYXRpb246IHRoaXMuZ2V0VHJhbnNpdGlvbkR1cmF0aW9uKCksXG4gICAgICAgICAgICBlYXNpbmc6IHRoaXMuY29tcHV0ZWRTdHlsZS50cmFuc2l0aW9uVGltaW5nRnVuY3Rpb25cbiAgICAgICAgICB9KTtcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy4kZWwuZ2V0QW5pbWF0aW9ucygpLmZvckVhY2goKGFuaW1hdGlvbjIpID0+IGFuaW1hdGlvbjIuZmluaXNoKCkpO1xuICAgICAgfSk7XG4gICAgICBMaXZld2lyZS5ob29rKFwibWVzc2FnZS5wcm9jZXNzZWRcIiwgKF8sIGNvbXBvbmVudCkgPT4ge1xuICAgICAgICBpZiAoIWNvbXBvbmVudC5zZXJ2ZXJNZW1vLmRhdGEuaXNGaWxhbWVudE5vdGlmaWNhdGlvbnNDb21wb25lbnQpIHtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCF0aGlzLmlzU2hvd24pIHtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgYW5pbWF0aW9uKCk7XG4gICAgICB9KTtcbiAgICB9LFxuICAgIGNsb3NlOiBmdW5jdGlvbigpIHtcbiAgICAgIHRoaXMuaXNTaG93biA9IGZhbHNlO1xuICAgICAgc2V0VGltZW91dCgoKSA9PiBMaXZld2lyZS5lbWl0KFwibm90aWZpY2F0aW9uQ2xvc2VkXCIsIG5vdGlmaWNhdGlvbi5pZCksIHRoaXMuZ2V0VHJhbnNpdGlvbkR1cmF0aW9uKCkpO1xuICAgIH0sXG4gICAgZ2V0VHJhbnNpdGlvbkR1cmF0aW9uOiBmdW5jdGlvbigpIHtcbiAgICAgIHJldHVybiBwYXJzZUZsb2F0KHRoaXMuY29tcHV0ZWRTdHlsZS50cmFuc2l0aW9uRHVyYXRpb24pICogMWUzO1xuICAgIH1cbiAgfSkpO1xufTtcblxuLy8gcGFja2FnZXMvbm90aWZpY2F0aW9ucy9yZXNvdXJjZXMvanMvTm90aWZpY2F0aW9uLmpzXG52YXIgaW1wb3J0X3V1aWRfYnJvd3NlciA9IF9fdG9Nb2R1bGUocmVxdWlyZV91dWlkX2Jyb3dzZXIoKSk7XG52YXIgTm90aWZpY2F0aW9uID0gY2xhc3Mge1xuICBjb25zdHJ1Y3RvcigpIHtcbiAgICB0aGlzLmlkKCgwLCBpbXBvcnRfdXVpZF9icm93c2VyLnY0KSgpKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICBpZChpZCkge1xuICAgIHRoaXMuaWQgPSBpZDtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICB0aXRsZSh0aXRsZSkge1xuICAgIHRoaXMudGl0bGUgPSB0aXRsZTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICBib2R5KGJvZHkpIHtcbiAgICB0aGlzLmJvZHkgPSBib2R5O1xuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIGFjdGlvbnMoYWN0aW9ucykge1xuICAgIHRoaXMuYWN0aW9ucyA9IGFjdGlvbnM7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgc3RhdHVzKHN0YXR1cykge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlIFwic3VjY2Vzc1wiOlxuICAgICAgICB0aGlzLnN1Y2Nlc3MoKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIFwid2FybmluZ1wiOlxuICAgICAgICB0aGlzLndhcm5pbmcoKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIFwiZGFuZ2VyXCI6XG4gICAgICAgIHRoaXMuZGFuZ2VyKCk7XG4gICAgICAgIGJyZWFrO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICBpY29uKGljb24pIHtcbiAgICB0aGlzLmljb24gPSBpY29uO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIGljb25Db2xvcihjb2xvcikge1xuICAgIHRoaXMuaWNvbkNvbG9yID0gY29sb3I7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgZHVyYXRpb24oZHVyYXRpb24pIHtcbiAgICB0aGlzLmR1cmF0aW9uID0gZHVyYXRpb247XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgc2Vjb25kcyhzZWNvbmRzKSB7XG4gICAgdGhpcy5kdXJhdGlvbihzZWNvbmRzICogMWUzKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICBwZXJzaXN0ZW50KCkge1xuICAgIHRoaXMuZHVyYXRpb24oXCJwZXJzaXN0ZW50XCIpO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIHN1Y2Nlc3MoKSB7XG4gICAgdGhpcy5pY29uKFwiaGVyb2ljb24tby1jaGVjay1jaXJjbGVcIik7XG4gICAgdGhpcy5pY29uQ29sb3IoXCJzdWNjZXNzXCIpO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIHdhcm5pbmcoKSB7XG4gICAgdGhpcy5pY29uKFwiaGVyb2ljb24tby1leGNsYW1hdGlvbi1jaXJjbGVcIik7XG4gICAgdGhpcy5pY29uQ29sb3IoXCJ3YXJuaW5nXCIpO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIGRhbmdlcigpIHtcbiAgICB0aGlzLmljb24oXCJoZXJvaWNvbi1vLXgtY2lyY2xlXCIpO1xuICAgIHRoaXMuaWNvbkNvbG9yKFwiZGFuZ2VyXCIpO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIHZpZXcodmlldykge1xuICAgIHRoaXMudmlldyA9IHZpZXc7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgdmlld0RhdGEodmlld0RhdGEpIHtcbiAgICB0aGlzLnZpZXdEYXRhID0gdmlld0RhdGE7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgc2VuZCgpIHtcbiAgICBMaXZld2lyZS5lbWl0KFwibm90aWZpY2F0aW9uU2VudFwiLCB0aGlzKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxufTtcbnZhciBBY3Rpb24gPSBjbGFzcyB7XG4gIGNvbnN0cnVjdG9yKG5hbWUpIHtcbiAgICB0aGlzLm5hbWUobmFtZSk7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgbmFtZShuYW1lKSB7XG4gICAgdGhpcy5uYW1lID0gbmFtZTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICBjb2xvcihjb2xvcikge1xuICAgIHRoaXMuY29sb3IgPSBjb2xvcjtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICBlbWl0KGV2ZW50LCBkYXRhKSB7XG4gICAgdGhpcy5ldmVudChldmVudCk7XG4gICAgdGhpcy5ldmVudERhdGEoZGF0YSk7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgZW1pdFNlbGYoZXZlbnQsIGRhdGEpIHtcbiAgICB0aGlzLmVtaXQoZXZlbnQsIGRhdGEpO1xuICAgIHRoaXMuZW1pdERpcmVjdGlvbiA9IFwic2VsZlwiO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIGVtaXRUbyhjb21wb25lbnQsIGV2ZW50LCBkYXRhKSB7XG4gICAgdGhpcy5lbWl0KGV2ZW50LCBkYXRhKTtcbiAgICB0aGlzLmVtaXREaXJlY3Rpb24gPSBcInRvXCI7XG4gICAgdGhpcy5lbWl0VG9Db21wb25lbnQgPSBjb21wb25lbnQ7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgZW1pdFVwKGV2ZW50LCBkYXRhKSB7XG4gICAgdGhpcy5lbWl0KGV2ZW50LCBkYXRhKTtcbiAgICB0aGlzLmVtaXREaXJlY3Rpb24gPSBcInVwXCI7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgZW1pdERpcmVjdGlvbihlbWl0RGlyZWN0aW9uKSB7XG4gICAgdGhpcy5lbWl0RGlyZWN0aW9uID0gZW1pdERpcmVjdGlvbjtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICBlbWl0VG9Db21wb25lbnQoY29tcG9uZW50KSB7XG4gICAgdGhpcy5lbWl0VG9Db21wb25lbnQgPSBjb21wb25lbnQ7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgZXZlbnQoZXZlbnQpIHtcbiAgICB0aGlzLmV2ZW50ID0gZXZlbnQ7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgZXZlbnREYXRhKGRhdGEpIHtcbiAgICB0aGlzLmV2ZW50RGF0YSA9IGRhdGE7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgZXh0cmFBdHRyaWJ1dGVzKGF0dHJpYnV0ZXMpIHtcbiAgICB0aGlzLmV4dHJhQXR0cmlidXRlcyA9IGF0dHJpYnV0ZXM7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgaWNvbihpY29uKSB7XG4gICAgdGhpcy5pY29uID0gaWNvbjtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICBpY29uUG9zaXRpb24ocG9zaXRpb24pIHtcbiAgICB0aGlzLmljb25Qb3NpdGlvbiA9IHBvc2l0aW9uO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIG91dGxpbmVkKGNvbmRpdGlvbiA9IHRydWUpIHtcbiAgICB0aGlzLmlzT3V0bGluZWQgPSBjb25kaXRpb247XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgZGlzYWJsZWQoY29uZGl0aW9uID0gdHJ1ZSkge1xuICAgIHRoaXMuaXNEaXNhYmxlZCA9IGNvbmRpdGlvbjtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICBsYWJlbChsYWJlbCkge1xuICAgIHRoaXMubGFiZWwgPSBsYWJlbDtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICBjbG9zZShjb25kaXRpb24gPSB0cnVlKSB7XG4gICAgdGhpcy5zaG91bGRDbG9zZU5vdGlmaWNhdGlvbiA9IGNvbmRpdGlvbjtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICBvcGVuVXJsSW5OZXdUYWIoY29uZGl0aW9uID0gdHJ1ZSkge1xuICAgIHRoaXMuc2hvdWxkT3BlblVybEluTmV3VGFiID0gY29uZGl0aW9uO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIHNpemUoc2l6ZSkge1xuICAgIHRoaXMuc2l6ZSA9IHNpemU7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgdXJsKHVybCkge1xuICAgIHRoaXMudXJsID0gdXJsO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIHZpZXcodmlldykge1xuICAgIHRoaXMudmlldyA9IHZpZXc7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgYnV0dG9uKCkge1xuICAgIHRoaXMudmlldyhcIm5vdGlmaWNhdGlvbnM6OmFjdGlvbnMuYnV0dG9uLWFjdGlvblwiKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICBncm91cGVkKCkge1xuICAgIHRoaXMudmlldyhcIm5vdGlmaWNhdGlvbnM6OmFjdGlvbnMuZ3JvdXBlZC1hY3Rpb25cIik7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgbGluaygpIHtcbiAgICB0aGlzLnZpZXcoXCJub3RpZmljYXRpb25zOjphY3Rpb25zLmxpbmstYWN0aW9uXCIpO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG59O1xudmFyIEFjdGlvbkdyb3VwID0gY2xhc3Mge1xuICBjb25zdHJ1Y3RvcihhY3Rpb25zKSB7XG4gICAgdGhpcy5hY3Rpb25zKGFjdGlvbnMpO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIGFjdGlvbnMoYWN0aW9ucykge1xuICAgIHRoaXMuYWN0aW9ucyA9IGFjdGlvbnMubWFwKChhY3Rpb24pID0+IGFjdGlvbi5ncm91cGVkKCkpO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIGNvbG9yKGNvbG9yKSB7XG4gICAgdGhpcy5jb2xvciA9IGNvbG9yO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIGljb24oaWNvbikge1xuICAgIHRoaXMuaWNvbiA9IGljb247XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgaWNvblBvc2l0aW9uKHBvc2l0aW9uKSB7XG4gICAgdGhpcy5pY29uUG9zaXRpb24gPSBwb3NpdGlvbjtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICBsYWJlbChsYWJlbCkge1xuICAgIHRoaXMubGFiZWwgPSBsYWJlbDtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICB0b29sdGlwKHRvb2x0aXApIHtcbiAgICB0aGlzLnRvb2x0aXAgPSB0b29sdGlwO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG59O1xuXG4vLyBwYWNrYWdlcy9ub3RpZmljYXRpb25zL3Jlc291cmNlcy9qcy9pbmRleC5qc1xud2luZG93Lk5vdGlmaWNhdGlvbkFjdGlvbiA9IEFjdGlvbjtcbndpbmRvdy5Ob3RpZmljYXRpb25BY3Rpb25Hcm91cCA9IEFjdGlvbkdyb3VwO1xud2luZG93Lk5vdGlmaWNhdGlvbiA9IE5vdGlmaWNhdGlvbjtcbnZhciBqc19kZWZhdWx0ID0gKEFscGluZSkgPT4ge1xuICBBbHBpbmUucGx1Z2luKG5vdGlmaWNhdGlvbl9kZWZhdWx0KTtcbn07XG5leHBvcnQge1xuICBOb3RpZmljYXRpb24sXG4gIEFjdGlvbiBhcyBOb3RpZmljYXRpb25BY3Rpb24sXG4gIEFjdGlvbkdyb3VwIGFzIE5vdGlmaWNhdGlvbkFjdGlvbkdyb3VwLFxuICBub3RpZmljYXRpb25fZGVmYXVsdCBhcyBOb3RpZmljYXRpb25Db21wb25lbnRBbHBpbmVQbHVnaW4sXG4gIGpzX2RlZmF1bHQgYXMgZGVmYXVsdFxufTtcbiJdLCJuYW1lcyI6WyJfX2NyZWF0ZSIsIk9iamVjdCIsImNyZWF0ZSIsIl9fZGVmUHJvcCIsImRlZmluZVByb3BlcnR5IiwiX19nZXRQcm90b09mIiwiZ2V0UHJvdG90eXBlT2YiLCJfX2hhc093blByb3AiLCJwcm90b3R5cGUiLCJoYXNPd25Qcm9wZXJ0eSIsIl9fZ2V0T3duUHJvcE5hbWVzIiwiZ2V0T3duUHJvcGVydHlOYW1lcyIsIl9fZ2V0T3duUHJvcERlc2MiLCJnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IiLCJfX21hcmtBc01vZHVsZSIsInRhcmdldCIsInZhbHVlIiwiX19jb21tb25KUyIsImNhbGxiYWNrIiwibW9kdWxlIiwiZXhwb3J0cyIsIl9fZXhwb3J0U3RhciIsImRlc2MiLCJrZXkiLCJjYWxsIiwiZ2V0IiwiZW51bWVyYWJsZSIsIl9fdG9Nb2R1bGUiLCJfX2VzTW9kdWxlIiwicmVxdWlyZV9ybmdfYnJvd3NlciIsInJuZyIsImNyeXB0byIsImdsb2JhbCIsIm1zQ3J5cHRvIiwiZ2V0UmFuZG9tVmFsdWVzIiwicm5kczgiLCJVaW50OEFycmF5Iiwid2hhdHdnUk5HIiwicm5kcyIsIkFycmF5IiwiaSIsInIiLCJNYXRoIiwicmFuZG9tIiwicmVxdWlyZV9ieXRlc1RvVXVpZCIsImJ5dGVUb0hleCIsInRvU3RyaW5nIiwic3Vic3RyIiwiYnl0ZXNUb1V1aWQiLCJidWYiLCJvZmZzZXQiLCJpMiIsImJ0aCIsInJlcXVpcmVfdjEiLCJfc2VlZEJ5dGVzIiwiX25vZGVJZCIsIl9jbG9ja3NlcSIsIl9sYXN0TVNlY3MiLCJfbGFzdE5TZWNzIiwidjEiLCJvcHRpb25zIiwiYiIsImNsb2Nrc2VxIiwibXNlY3MiLCJEYXRlIiwiZ2V0VGltZSIsIm5zZWNzIiwiZHQiLCJFcnJvciIsInRsIiwidG1oIiwibm9kZSIsIm4iLCJyZXF1aXJlX3Y0IiwidjQiLCJpaSIsInJlcXVpcmVfdXVpZF9icm93c2VyIiwidXVpZDIiLCJvbkF0dHJpYnV0ZUFkZGVkcyIsIm9uRWxSZW1vdmVkcyIsIm9uRWxBZGRlZHMiLCJjbGVhbnVwQXR0cmlidXRlcyIsImVsIiwibmFtZXMiLCJfeF9hdHRyaWJ1dGVDbGVhbnVwcyIsImVudHJpZXMiLCJmb3JFYWNoIiwibmFtZSIsImluY2x1ZGVzIiwib2JzZXJ2ZXIiLCJNdXRhdGlvbk9ic2VydmVyIiwib25NdXRhdGUiLCJjdXJyZW50bHlPYnNlcnZpbmciLCJzdGFydE9ic2VydmluZ011dGF0aW9ucyIsIm9ic2VydmUiLCJkb2N1bWVudCIsInN1YnRyZWUiLCJjaGlsZExpc3QiLCJhdHRyaWJ1dGVzIiwiYXR0cmlidXRlT2xkVmFsdWUiLCJzdG9wT2JzZXJ2aW5nTXV0YXRpb25zIiwiZmx1c2hPYnNlcnZlciIsImRpc2Nvbm5lY3QiLCJyZWNvcmRRdWV1ZSIsIndpbGxQcm9jZXNzUmVjb3JkUXVldWUiLCJjb25jYXQiLCJ0YWtlUmVjb3JkcyIsImxlbmd0aCIsInF1ZXVlTWljcm90YXNrIiwicHJvY2Vzc1JlY29yZFF1ZXVlIiwibXV0YXRlRG9tIiwicmVzdWx0IiwiaXNDb2xsZWN0aW5nIiwiZGVmZXJyZWRNdXRhdGlvbnMiLCJtdXRhdGlvbnMiLCJhZGRlZE5vZGVzIiwicmVtb3ZlZE5vZGVzIiwiYWRkZWRBdHRyaWJ1dGVzIiwiTWFwIiwicmVtb3ZlZEF0dHJpYnV0ZXMiLCJfeF9pZ25vcmVNdXRhdGlvbk9ic2VydmVyIiwidHlwZSIsIm5vZGVUeXBlIiwicHVzaCIsImF0dHJpYnV0ZU5hbWUiLCJvbGRWYWx1ZSIsImFkZCIsImhhcyIsInNldCIsImdldEF0dHJpYnV0ZSIsInJlbW92ZSIsImhhc0F0dHJpYnV0ZSIsImF0dHJzIiwiX3hfY2xlYW51cHMiLCJwb3AiLCJfeF9pZ25vcmVTZWxmIiwiX3hfaWdub3JlIiwiaXNDb25uZWN0ZWQiLCJvbmNlIiwiZmFsbGJhY2siLCJjYWxsZWQiLCJhcHBseSIsImFyZ3VtZW50cyIsIm5vdGlmaWNhdGlvbl9kZWZhdWx0IiwiQWxwaW5lIiwiZGF0YSIsIm5vdGlmaWNhdGlvbiIsImlzU2hvd24iLCJjb21wdXRlZFN0eWxlIiwiaW5pdCIsIndpbmRvdyIsImdldENvbXB1dGVkU3R5bGUiLCIkZWwiLCJjb25maWd1cmVUcmFuc2l0aW9ucyIsImNvbmZpZ3VyZUFuaW1hdGlvbnMiLCJkdXJhdGlvbiIsInNldFRpbWVvdXQiLCJjbG9zZSIsImRpc3BsYXkiLCJzaG93Iiwic3R5bGUiLCJzZXRQcm9wZXJ0eSIsIl94X2lzU2hvd24iLCJoaWRlIiwidG9nZ2xlIiwiX3hfdG9nZ2xlQW5kQ2FzY2FkZVdpdGhUcmFuc2l0aW9ucyIsImVmZmVjdCIsImFuaW1hdGlvbiIsIkxpdmV3aXJlIiwiaG9vayIsIl8iLCJjb21wb25lbnQiLCJzZXJ2ZXJNZW1vIiwiaXNGaWxhbWVudE5vdGlmaWNhdGlvbnNDb21wb25lbnQiLCJnZXRUb3AiLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJ0b3AiLCJvbGRUb3AiLCJhbmltYXRlIiwidHJhbnNmb3JtIiwiZ2V0VHJhbnNpdGlvbkR1cmF0aW9uIiwiZWFzaW5nIiwidHJhbnNpdGlvblRpbWluZ0Z1bmN0aW9uIiwiZ2V0QW5pbWF0aW9ucyIsImFuaW1hdGlvbjIiLCJmaW5pc2giLCJlbWl0IiwiaWQiLCJwYXJzZUZsb2F0IiwidHJhbnNpdGlvbkR1cmF0aW9uIiwiaW1wb3J0X3V1aWRfYnJvd3NlciIsIk5vdGlmaWNhdGlvbiIsInRpdGxlIiwiYm9keSIsImFjdGlvbnMiLCJzdGF0dXMiLCJzdWNjZXNzIiwid2FybmluZyIsImRhbmdlciIsImljb24iLCJjb2xvciIsImljb25Db2xvciIsInNlY29uZHMiLCJ2aWV3Iiwidmlld0RhdGEiLCJBY3Rpb24iLCJldmVudCIsImV2ZW50RGF0YSIsImVtaXREaXJlY3Rpb24iLCJlbWl0VG9Db21wb25lbnQiLCJleHRyYUF0dHJpYnV0ZXMiLCJwb3NpdGlvbiIsImljb25Qb3NpdGlvbiIsImNvbmRpdGlvbiIsImlzT3V0bGluZWQiLCJpc0Rpc2FibGVkIiwibGFiZWwiLCJzaG91bGRDbG9zZU5vdGlmaWNhdGlvbiIsInNob3VsZE9wZW5VcmxJbk5ld1RhYiIsInNpemUiLCJ1cmwiLCJBY3Rpb25Hcm91cCIsIm1hcCIsImFjdGlvbiIsImdyb3VwZWQiLCJ0b29sdGlwIiwiTm90aWZpY2F0aW9uQWN0aW9uIiwiTm90aWZpY2F0aW9uQWN0aW9uR3JvdXAiLCJqc19kZWZhdWx0IiwicGx1Z2luIiwiTm90aWZpY2F0aW9uQ29tcG9uZW50QWxwaW5lUGx1Z2luIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./vendor/filament/notifications/dist/module.esm.js\n");

/***/ }),

/***/ "./resources/sass/app.scss":
/*!*********************************!*\
  !*** ./resources/sass/app.scss ***!
  \*********************************/
/***/ (() => {

throw new Error("Module build failed (from ./node_modules/mini-css-extract-plugin/dist/loader.js):\nModuleBuildError: Module build failed (from ./node_modules/postcss-loader/dist/cjs.js):\nC:\\xampp\\htdocs\\vtigo\\sales-pro\\postcss.config.js:1\nexport default {\n^^^^^^\n\nSyntaxError: Unexpected token 'export'\n    at internalCompileFunction (node:internal/vm:73:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1176:20)\n    at Module._compile (node:internal/modules/cjs/loader:1218:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1308:10)\n    at Module.load (node:internal/modules/cjs/loader:1117:32)\n    at Module._load (node:internal/modules/cjs/loader:958:12)\n    at Module.require (node:internal/modules/cjs/loader:1141:19)\n    at module.exports (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\import-fresh\\index.js:32:59)\n    at loadJs (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\cosmiconfig\\dist\\loaders.js:16:18)\n    at Explorer.loadFileContent (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\cosmiconfig\\dist\\Explorer.js:84:32)\n    at Explorer.createCosmiconfigResult (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\cosmiconfig\\dist\\Explorer.js:89:36)\n    at Explorer.loadSearchPlace (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\cosmiconfig\\dist\\Explorer.js:70:31)\n    at async Explorer.searchDirectory (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\cosmiconfig\\dist\\Explorer.js:55:27)\n    at async run (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\cosmiconfig\\dist\\Explorer.js:35:22)\n    at async cacheWrapper (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\cosmiconfig\\dist\\cacheWrapper.js:16:18)\n    at async cacheWrapper (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\cosmiconfig\\dist\\cacheWrapper.js:16:18)\n    at async cacheWrapper (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\cosmiconfig\\dist\\cacheWrapper.js:16:18)\n    at async Explorer.search (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\cosmiconfig\\dist\\Explorer.js:27:20)\n    at async loadConfig (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\postcss-loader\\dist\\utils.js:68:16)\n    at async Object.loader (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\postcss-loader\\dist\\index.js:54:22)\n    at processResult (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\webpack\\lib\\NormalModule.js:758:19)\n    at C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\webpack\\lib\\NormalModule.js:860:5\n    at C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\loader-runner\\lib\\LoaderRunner.js:400:11\n    at C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\loader-runner\\lib\\LoaderRunner.js:252:18\n    at context.callback (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\loader-runner\\lib\\LoaderRunner.js:124:13)\n    at Object.loader (C:\\xampp\\htdocs\\vtigo\\sales-pro\\node_modules\\postcss-loader\\dist\\index.js:56:7)");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/global */
/******/ 	(() => {
/******/ 		__webpack_require__.g = (function() {
/******/ 			if (typeof globalThis === 'object') return globalThis;
/******/ 			try {
/******/ 				return this || new Function('return this')();
/******/ 			} catch (e) {
/******/ 				if (typeof window === 'object') return window;
/******/ 			}
/******/ 		})();
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	__webpack_require__("./resources/js/app.js");
/******/ 	var __webpack_exports__ = __webpack_require__("./resources/sass/app.scss");
/******/ 	
/******/ })()
;