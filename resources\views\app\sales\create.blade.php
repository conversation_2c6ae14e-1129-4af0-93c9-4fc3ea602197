@extends('layouts.fullscreen')

@section('content')
<!-- Fullscreen Header -->
<div class="fullscreen-header">
    <div class="d-flex justify-content-between align-items-center w-100">
        <div>
            <h1 class="h4 mb-0">
                <i class="bi bi-plus-circle text-primary me-2"></i>Create New Sale
            </h1>
            <small class="text-muted">
                <a href="{{ route('sales.index') }}" class="text-decoration-none">Sales</a> / Create New Sale
            </small>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('sales.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-list me-1"></i>View All Sales
            </a>
        </div>
    </div>
</div>

<!-- Fullscreen Content -->
<div class="fullscreen-content">
    <x-form method="POST" action="{{ route('sales.store') }}">
        @include('app.sales.form-inputs')

        <div class="mt-4 d-flex justify-content-between">
            <a href="{{ route('sales.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>Back to Sales
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-check-circle me-1"></i>Create Sale
            </button>
        </div>
    </x-form>
</div>
@endsection
