@extends('layouts.app')

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">credit-card</x-slot>
        <x-slot name="title">Payment Details</x-slot>
        <x-slot name="subtitle">View payment information and related records</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('payments.index') }}">Payments</a></li>
            <li class="breadcrumb-item active" aria-current="page">Payment #{{ $payment->id }}</li>
        </x-slot>
        <x-slot name="controls">
            <div class="d-flex gap-2">
                @can('update', $payment)
                <a href="{{ route('payments.edit', $payment) }}" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-pencil me-1"></i>Edit Payment
                </a>
                @endcan
                <a href="{{ route('payments.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Payments
                </a>
            </div>
        </x-slot>
    </x-page-header>

    <div class="row">
        <!-- Payment Information -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-credit-card me-2"></i>Payment Information
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label class="form-label text-muted">Payment ID</label>
                                <div class="fw-bold">#{{ $payment->id }}</div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label text-muted">Amount</label>
                                <div class="fw-bold h4 text-success">{{ _money($payment->amount) }}</div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label text-muted">Payment Type</label>
                                <div>
                                    <span class="badge bg-primary">{{ $payment->payment_type }}</span>
                                    @if($payment->paymentable)
                                        <span class="text-muted ms-2">#{{ $payment->paymentable_id }}</span>
                                    @endif
                                </div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label text-muted">Reference Number</label>
                                <div class="fw-semibold">
                                    {{ $payment->reference_no ?? '-' }}
                                </div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label text-muted">Currency</label>
                                <div>
                                    @if($payment->currency)
                                        <span class="badge bg-light text-dark">{{ $payment->currency->code }}</span>
                                        <span class="text-muted ms-2">{{ $payment->currency->name }}</span>
                                    @else
                                        <span class="text-muted">Default Currency</span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-4">
                                <label class="form-label text-muted">Date & Time</label>
                                <div class="fw-semibold">{{ $payment->created_at->format('M d, Y h:i A') }}</div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label text-muted">Balance After Payment</label>
                                <div class="fw-semibold">{{ _money($payment->balance) }}</div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label text-muted">Created By</label>
                                <div class="d-flex align-items-center">
                                    @if($payment->createdBy)
                                        <div class="avatar avatar-xs avatar-circle me-2">
                                            <span class="avatar-initials bg-primary text-white">
                                                {{ substr($payment->createdBy->name, 0, 1) }}
                                            </span>
                                        </div>
                                        <span class="fw-semibold">{{ $payment->createdBy->name }}</span>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </div>
                            </div>

                            @if($payment->updatedBy && $payment->updated_at != $payment->created_at)
                            <div class="mb-4">
                                <label class="form-label text-muted">Last Updated By</label>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-xs avatar-circle me-2">
                                        <span class="avatar-initials bg-secondary text-white">
                                            {{ substr($payment->updatedBy->name, 0, 1) }}
                                        </span>
                                    </div>
                                    <div>
                                        <div class="fw-semibold">{{ $payment->updatedBy->name }}</div>
                                        <small class="text-muted">{{ $payment->updated_at->format('M d, Y h:i A') }}</small>
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    @if($payment->description)
                    <div class="mb-4">
                        <label class="form-label text-muted">Description</label>
                        <div class="fw-semibold">{{ $payment->description }}</div>
                    </div>
                    @endif

                    @if($payment->comment)
                    <div class="mb-4">
                        <label class="form-label text-muted">Internal Comments</label>
                        <div class="fw-semibold">{{ $payment->comment }}</div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Related Record Information -->
            @if($payment->paymentable)
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-link-45deg me-2"></i>Related {{ $payment->payment_type }}
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ $payment->payment_type }} ID</label>
                                <div class="fw-bold">#{{ $payment->paymentable->id }}</div>
                            </div>

                            @if($payment->paymentable->customer)
                            <div class="mb-3">
                                <label class="form-label text-muted">Customer</label>
                                <div class="fw-semibold">{{ $payment->paymentable->customer->name }}</div>
                            </div>
                            @endif

                            <div class="mb-3">
                                <label class="form-label text-muted">Total Amount</label>
                                <div class="fw-semibold">{{ _money($payment->paymentable->amount_total) }}</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Amount Paid</label>
                                <div class="fw-semibold text-success">{{ _money($payment->paymentable->amount_paid) }}</div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">Outstanding Balance</label>
                                <div class="fw-semibold {{ $payment->paymentable->balance > 0 ? 'text-danger' : 'text-success' }}">
                                    {{ _money($payment->paymentable->balance) }}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <div>
                                    @if($payment->paymentable->balance <= 0)
                                        <span class="badge bg-success">Fully Paid</span>
                                    @elseif($payment->paymentable->amount_paid > 0)
                                        <span class="badge bg-warning">Partially Paid</span>
                                    @else
                                        <span class="badge bg-danger">Unpaid</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end">
                        @if($payment->paymentable_type === 'App\Models\Invoice')
                            <a href="{{ route('invoices.show', $payment->paymentable) }}" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-eye me-1"></i>View Invoice
                            </a>
                        @elseif($payment->paymentable_type === 'App\Models\Order')
                            <a href="{{ route('orders.show', $payment->paymentable) }}" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-eye me-1"></i>View Order
                            </a>
                        @endif
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Actions Sidebar -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-gear me-2"></i>Actions
                    </h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @can('update', $payment)
                        <a href="{{ route('payments.edit', $payment) }}" class="btn btn-outline-primary">
                            <i class="bi bi-pencil me-2"></i>Edit Payment
                        </a>
                        @endcan

                        @if($payment->paymentable)
                            @if($payment->paymentable_type === 'App\Models\Invoice')
                                <a href="{{ route('invoices.show', $payment->paymentable) }}" class="btn btn-outline-info">
                                    <i class="bi bi-receipt me-2"></i>View Invoice
                                </a>
                            @elseif($payment->paymentable_type === 'App\Models\Order')
                                <a href="{{ route('orders.show', $payment->paymentable) }}" class="btn btn-outline-info">
                                    <i class="bi bi-cart me-2"></i>View Order
                                </a>
                            @endif
                        @endif

                        <a href="{{ route('payments.create') }}" class="btn btn-outline-success">
                            <i class="bi bi-plus-circle me-2"></i>Record New Payment
                        </a>

                        @can('delete', $payment)
                        <hr>
                        <form action="{{ route('payments.destroy', $payment) }}" method="POST" 
                              onsubmit="return confirm('Are you sure you want to delete this payment? This action cannot be undone and will affect the related invoice/order balance.')">
                            @csrf @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="bi bi-trash me-2"></i>Delete Payment
                            </button>
                        </form>
                        @endcan
                    </div>
                </div>
            </div>

            <!-- Payment Summary -->
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-info-circle me-2"></i>Payment Summary
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <div class="border rounded p-3">
                                <div class="h4 text-success mb-1">{{ _money($payment->amount) }}</div>
                                <div class="text-muted small">Payment Amount</div>
                            </div>
                        </div>
                        @if($payment->paymentable)
                        <div class="col-6">
                            <div class="border rounded p-3">
                                <div class="h6 mb-1">{{ _money($payment->paymentable->amount_paid) }}</div>
                                <div class="text-muted small">Total Paid</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-3">
                                <div class="h6 mb-1 {{ $payment->paymentable->balance > 0 ? 'text-danger' : 'text-success' }}">
                                    {{ _money($payment->paymentable->balance) }}
                                </div>
                                <div class="text-muted small">Remaining</div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
