
@extends('layouts.app')

@section('content')

@php $editing = isset($customer) @endphp

<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
          <div class="col-sm mb-2 mb-sm-0">
            <h1 class="page-header-title">
                Edit {{ ucfirst( $type ) }} Debtor
            </h1>
          </div>
          <!-- End Col -->
        </div>
    </div>
    <!-- End Page Header -->
    <x-form
        method="PUT"
        action="{{ route('debts.update', $debt) }}"
        has-files
        class="mt-4"
    >

	  <div class="" id="supplier-debtor-modal-el">
	  	<input type="hidden" value="{{ $debt->type }}" name="type" required>
	    <div class="row mb-4">

	    	@if( $type == 'customer')
				<div class=" mb-4 col-sm-6">
					 <div class="tom-select-custom">
	        	<label>Customer</label>
	          <select class="js-select form-select branch-filter" required name="customer_id" autocomplete="off" data-hs-tom-select-options='{
	            "placeholder": "Select a customer..."
	          }'>
	          @foreach( App\Models\Customer::get() as $customer)
	            <option value="{{ $customer->id }}" @if($customer->id  == $debt->debtable_id) selected @endif>
	              {{ $customer->name }}
	            </option>
	          @endforeach
	          </select>
	        </div>
				</div>
				@endif

	    	@if( $type == 'supplier')
				<div class=" mb-4 col-sm-6">
					 <div class="tom-select-custom">
	        	<label>Supplier</label>
	          <select class="js-select form-select branch-filter" required name="supplier_id" autocomplete="off" data-hs-tom-select-options='{
	            "placeholder": "Select a supplier..."
	          }'>
	          @foreach( App\Models\Supplier::get() as $supplier)
	            <option value="{{ $supplier->id }}" @if($supplier->id  == $debt->debtable_id) selected @endif>
	              {{ $supplier->name }}
	            </option>
	          @endforeach
	          </select>
	        </div>
				</div>
				@endif


				<div class=" mb-4 col-sm-6">
					<div class="tom-select-custom">
	          	<label>Category</label>
	            <select class="js-select form-select branch-filter" required name="category" autocomplete="off" data-hs-tom-select-options='{
	              "placeholder": "Select a category..."
	            }'>
	            @foreach( App\Models\Category::where("applied_to", 'debtors')->get() as $category)
	              <option value="{{ $category->name }}"  @if($category->name  == $debt->category) selected @endif>
	                {{ $category->name }}
	              </option>
	            @endforeach
	            </select>
	        	</div>
				</div>

				@if( auth()->user()->isSuperAdmin())
				<div class=" mb-4 col-sm-6">
					<div class="tom-select-custom">
	        	<label>Branch</label>
	          <select class="js-select form-select branch-filter" required name="branch_id" required autocomplete="off" data-hs-tom-select-options='{
	            "placeholder": "Select a branch..."
	          }'>
	          @foreach( App\Models\Branch::get() as $branch)
	            <option value="{{ $branch->id }}" @if($branch->id == $debt->branch_id ) selected @endif>
	              {{ $branch->name }}
	            </option>
	          @endforeach
	          </select>
	      	</div>
				</div>
	      @endif

				<div class="col-sm-6 mb-4">
					<label>Amount:</label>
			    <input type="number" value="{{ $debt->amount_total }}" class="form-control" name="amount_total" required>	
				</div>

				<div class="col-sm-12 mb-4">
					<label>Note:</label>
					<textarea name="description" class="form-control" placeholder="Add comment." required>{{ $debt->description }}</textarea>
				</div>

	    </div>
	  </div>


    <div class="mt-4">
        <a
            href="{{ url()->previous() }}"
            class="btn btn-light"
        >
            Back
        </a>


        <button type="submit" class="btn btn-primary float-right" style="float:right;">
            <!-- <i class="icon ion-md-save"></i> -->
            @lang('crud.common.update')
        </button>
    </div>
	</x-form>

</div>
@endsection



@push('scripts')

  <script type="text/javascript">
    
    new Vue({
      el: "#supplier-debtor-modal-el",
      data(){
        return{
          customer_id: null,
          customer: null,
        }
      },

      methods: {

        getCustomerBalance() {
        	axios.get('/ajax-customer-balance/' + this.customer_id ).then( res => {
        		this.customer = res.data;
            console.log(this.customer)
        	});
        }


      },


      created(){

      	// this.getProducts();
      }

    })

  </script>

@endpush
