<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class TaxBracketUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => ['nullable', 'string', 'max:255'],
            'min_amount' => ['required', 'numeric', 'min:0'],
            'max_amount' => ['nullable', 'numeric', 'gt:min_amount'],
            'rate' => ['required', 'numeric', 'min:0', 'max:1'],
            'order' => ['required', 'integer', 'min:0'],
            'is_active' => ['boolean'],
            'description' => ['nullable', 'string'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'min_amount.required' => 'The minimum amount is required.',
            'min_amount.numeric' => 'The minimum amount must be a number.',
            'min_amount.min' => 'The minimum amount must be at least 0.',
            'max_amount.gt' => 'The maximum amount must be greater than the minimum amount.',
            'rate.required' => 'The tax rate is required.',
            'rate.numeric' => 'The tax rate must be a number.',
            'rate.min' => 'The tax rate must be at least 0.',
            'rate.max' => 'The tax rate cannot exceed 100% (1.0).',
            'order.required' => 'The order is required.',
            'order.integer' => 'The order must be an integer.',
            'order.min' => 'The order must be at least 0.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert percentage to decimal if needed
        if ($this->has('rate') && $this->rate > 1) {
            $this->merge([
                'rate' => $this->rate / 100
            ]);
        }

        // Set default values
        $this->merge([
            'is_active' => $this->boolean('is_active', true),
        ]);
    }
}
