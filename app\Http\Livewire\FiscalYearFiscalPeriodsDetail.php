<?php

namespace App\Http\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\FiscalYear;
use App\Models\FiscalPeriod;

class FiscalYearFiscalPeriodsDetail extends Component
{
    use WithPagination;

    public FiscalYear $fiscalYear;
    public $fiscalPeriodName;
    public $fiscalPeriodStartDate;
    public $fiscalPeriodEndDate;
    public $fiscalPeriodIsActive = true;
    public $fiscalPeriodIsClosed = false;
    public $fiscalPeriodDescription;

    public $showingModal = false;
    public $modalTitle = 'New Fiscal Period';

    protected $rules = [
        'fiscalPeriodName' => ['required', 'max:255', 'string'],
        'fiscalPeriodStartDate' => ['required', 'date'],
        'fiscalPeriodEndDate' => ['required', 'date', 'after_or_equal:fiscalPeriodStartDate'],
        'fiscalPeriodIsActive' => ['boolean'],
        'fiscalPeriodIsClosed' => ['boolean'],
        'fiscalPeriodDescription' => ['nullable', 'max:255', 'string'],
    ];

    public function mount(FiscalYear $fiscalYear)
    {
        $this->fiscalYear = $fiscalYear;
    }

    public function render()
    {
        $fiscalPeriods = $this->fiscalYear->fiscalPeriods()->paginate(20);

        return view('livewire.fiscal-year-fiscal-periods-detail', [
            'fiscalPeriods' => $fiscalPeriods,
        ]);
    }

    public function newFiscalPeriod()
    {
        $this->modalTitle = trans('crud.fiscal_periods.create_title');
        $this->resetFiscalPeriodData();

        $this->showingModal = true;
    }

    public function resetFiscalPeriodData()
    {
        $this->fiscalPeriodName = '';
        $this->fiscalPeriodStartDate = '';
        $this->fiscalPeriodEndDate = '';
        $this->fiscalPeriodIsActive = true;
        $this->fiscalPeriodIsClosed = false;
        $this->fiscalPeriodDescription = '';
    }

    public function createFiscalPeriod()
    {
        $this->validate();

        $fiscalPeriod = $this->fiscalYear->fiscalPeriods()->create([
            'name' => $this->fiscalPeriodName,
            'start_date' => $this->fiscalPeriodStartDate,
            'end_date' => $this->fiscalPeriodEndDate,
            'is_active' => $this->fiscalPeriodIsActive,
            'is_closed' => $this->fiscalPeriodIsClosed,
            'description' => $this->fiscalPeriodDescription,
        ]);

        $this->showingModal = false;

        $this->emit('refreshParent');
        $this->resetFiscalPeriodData();
    }
}
