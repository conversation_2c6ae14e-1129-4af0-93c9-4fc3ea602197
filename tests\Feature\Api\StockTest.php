<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Stock;

use App\Models\Status;
use App\Models\Product;
use App\Models\Supplier;

use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class StockTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create(['email' => '<EMAIL>']);

        Sanctum::actingAs($user, [], 'web');

        $this->seed(\Database\Seeders\PermissionsSeeder::class);

        $this->withoutExceptionHandling();
    }

    /**
     * @test
     */
    public function it_gets_stocks_list()
    {
        $stocks = Stock::factory()
            ->count(5)
            ->create();

        $response = $this->getJson(route('api.stocks.index'));

        $response->assertOk()->assertSee($stocks[0]->description);
    }

    /**
     * @test
     */
    public function it_stores_the_stock()
    {
        $data = Stock::factory()
            ->make()
            ->toArray();

        $response = $this->postJson(route('api.stocks.store'), $data);

        unset($data['created_by']);
        unset($data['updated_by']);
        unset($data['status_id']);

        $this->assertDatabaseHas('stocks', $data);

        $response->assertStatus(201)->assertJsonFragment($data);
    }

    /**
     * @test
     */
    public function it_updates_the_stock()
    {
        $stock = Stock::factory()->create();

        $product = Product::factory()->create();
        $user = User::factory()->create();
        $user = User::factory()->create();
        $status = Status::factory()->create();
        $supplier = Supplier::factory()->create();
        $user = User::factory()->create();
        $stock = Stock::factory()->create();

        $data = [
            'quantity' => $this->faker->randomNumber,
            'balance' => $this->faker->randomNumber(0),
            'description' => $this->faker->sentence(15),
            'product_id' => $product->id,
            'created_by' => $user->id,
            'updated_by' => $user->id,
            'status_id' => $status->id,
            'supplier_id' => $supplier->id,
            'approved_by' => $user->id,
            'stock_id' => $stock->id,
        ];

        $response = $this->putJson(route('api.stocks.update', $stock), $data);

        unset($data['created_by']);
        unset($data['updated_by']);
        unset($data['status_id']);

        $data['id'] = $stock->id;

        $this->assertDatabaseHas('stocks', $data);

        $response->assertOk()->assertJsonFragment($data);
    }

    /**
     * @test
     */
    public function it_deletes_the_stock()
    {
        $stock = Stock::factory()->create();

        $response = $this->deleteJson(route('api.stocks.destroy', $stock));

        $this->assertSoftDeleted($stock);

        $response->assertNoContent();
    }
}
