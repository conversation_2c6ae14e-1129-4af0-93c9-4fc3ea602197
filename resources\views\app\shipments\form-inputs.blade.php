@php $editing = isset($shipment) @endphp

    <div class="content container-fluid" id="shipment-el">
      <div class="row">
        <div class="col-lg-12 mb-5 mb-lg-0">

          <div class=" mb-5 shipment" id="shipment">

            <div class="-body">

              <div class="row justify-content-md-between mb-3">

                <div class="col-sm-6">
                  <div class="row p-select">
                    <div class="col-sm-12">
                      <h2>Add Product:</h2>
                      <select class="form-control js-select form-select fs-4" @change="addItem($event)"data-hs-tom-select-options='{
                        "placeholder": "Product..."
                      }'>
                        <option value="" selected disabled>Select new Product</option>
                        <option v-for="(product, i) in products" :value="product.id" v-text="product.name + ' ~ ' + product.description"></option>
                      </select>
                    </div>
                  </div>
                </div>
                <!-- End Col -->
                <div class="col-sm-6">
                  <h2>Customer:</h2>

                  <select class="js-select form-select form-control" autocomplete="off"  name="customer_id" required
                    data-hs-tom-select-options='{
                      "create": true,
                      "placeholder": "Create New..."
                    }'>
                    @foreach( App\Models\Customer::get() as $customer)
                    <option value="{{ $customer->id }}"  {{ $editing && $customer->id == $shipment->customer_id ? 'selected' : ''  }} >
                      {{ $customer->name ?? ''}}
                    </option>
                    @endforeach
                  </select>

                </div>

              </div>
              <!-- End Row -->

              <!-- Shipping Information -->
              <div class="card mb-4">
                <div class="card-header">
                  <h4 class="card-title">
                    <i class="bi-truck me-2"></i>
                    Shipping Information
                  </h4>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="tracking_number" class="form-label">Tracking Number</label>
                      <input type="text" class="form-control" id="tracking_number" name="tracking_number"
                             value="{{ $editing ? $shipment->tracking_number : '' }}" placeholder="Enter tracking number">
                    </div>
                    <div class="col-md-6 mb-3">
                      <label for="carrier" class="form-label">Carrier</label>
                      <input type="text" class="form-control" id="carrier" name="carrier"
                             value="{{ $editing ? $shipment->carrier : '' }}" placeholder="e.g., FedEx, UPS, DHL">
                    </div>
                    <div class="col-md-6 mb-3">
                      <label for="shipping_method" class="form-label">Shipping Method</label>
                      <select class="form-select" id="shipping_method" name="shipping_method">
                        <option value="">Select shipping method</option>
                        <option value="standard" {{ $editing && $shipment->shipping_method == 'standard' ? 'selected' : '' }}>Standard</option>
                        <option value="express" {{ $editing && $shipment->shipping_method == 'express' ? 'selected' : '' }}>Express</option>
                        <option value="overnight" {{ $editing && $shipment->shipping_method == 'overnight' ? 'selected' : '' }}>Overnight</option>
                        <option value="ground" {{ $editing && $shipment->shipping_method == 'ground' ? 'selected' : '' }}>Ground</option>
                      </select>
                    </div>
                    <div class="col-md-6 mb-3">
                      <label for="estimated_delivery" class="form-label">Estimated Delivery</label>
                      <input type="date" class="form-control" id="estimated_delivery" name="estimated_delivery"
                             value="{{ $editing && $shipment->estimated_delivery ? $shipment->estimated_delivery->format('Y-m-d') : '' }}">
                    </div>
                    <div class="col-md-12 mb-3">
                      <label for="shipping_address" class="form-label">Shipping Address</label>
                      <textarea class="form-control" id="shipping_address" name="shipping_address" rows="3"
                                placeholder="Enter complete shipping address">{{ $editing ? $shipment->shipping_address : '' }}</textarea>
                    </div>
                    <div class="col-md-4 mb-3">
                      <label for="weight" class="form-label">Weight (kg)</label>
                      <input type="number" step="0.01" class="form-control" id="weight" name="weight"
                             value="{{ $editing ? $shipment->weight : '' }}" placeholder="0.00">
                    </div>
                    <div class="col-md-4 mb-3">
                      <label for="dimensions" class="form-label">Dimensions (L x W x H)</label>
                      <input type="text" class="form-control" id="dimensions" name="dimensions"
                             value="{{ $editing ? $shipment->dimensions : '' }}" placeholder="e.g., 30x20x15 cm">
                    </div>
                    <div class="col-md-4 mb-3">
                      <label for="insurance_value" class="form-label">Insurance Value</label>
                      <input type="number" step="0.01" class="form-control" id="insurance_value" name="insurance_value"
                             value="{{ $editing ? $shipment->insurance_value : '' }}" placeholder="0.00">
                    </div>
                    <div class="col-md-12 mb-3">
                      <label for="special_instructions" class="form-label">Special Instructions</label>
                      <textarea class="form-control" id="special_instructions" name="special_instructions" rows="2"
                                placeholder="Any special handling instructions">{{ $editing ? $shipment->special_instructions : '' }}</textarea>
                    </div>
                  </div>
                </div>
              </div>
              <!-- End Shipping Information -->

              <br>

              <!-- Table -->
              <div class="table-responsive" style="min-height: 300px;">
                <table class="table table-borderless table-nowrap table-align-middle">
                  <thead class="thead-light">
                    <tr>
                      <th class="">Item</th>
                      <th class="">Unit</th>
                      <th class="">Quantity</th>
                      <th class="">Discount</th>
                      <th class="">Selling Price</th>
                      <th class="">Buying Price</th>
                      <th class="table-text-end ">Amount</th>
                      <th></th>
                    </tr>
                  </thead>

                  <tbody>
                    <tr v-for="(sale, index) in shipment.sales">
                      <td>
                        <span class="d-block fs-4 fw-semibold mb-0" v-text="sale.product.name"></span>
                        <span class="d-block" v-text="sale.product.description"></span>
                      </td>
                      <td>
                        <select name="units[]" v-model.lazy="sale.unit_id" class="form-control fs-4 fw-semibold"  @change="setSaleMax(index)" required>
                          <option class="" value="" selected disabled>Please select unit</option>
                          <option :value="unit.id" v-for="(unit, index) in sale.units" v-text="unit.name"></option>
                        </select>
                      </td>
                      <td><input type="number" step="0.01" name="quantities[]" class="form-control w-100 fw-semibold fs-4" v-model.lazy="sale.quantity" @change="calculate" required> </td>
                      <td>
                        <input type="number" name="discounts[]" min="0" step="0.00" class="form-control w-100 fw-semibold fs-4" v-model.lazy="sale.discount" @change="calculate" :max="sale?.amount" required>
                        <input type="hidden" :value="shipment.discount" name="discount">
                      </td>
                      <td><span class="fw-semibold fs-4" v-text="formatNumber(sale.selling_price)"></span> </td>
                      <td><span class="fw-semibold fs-4" v-text="formatNumber(sale.buying_price)"></span> </td>
                      <td class="table-text-end fw-semibold fs-4"> <span v-text="formatNumber(sale?.selling_price * sale.quantity)"></span></td>
                      <td> <span @click="removeItem(index)" class="btn btn-outline-danger btn-sm"> del</span> </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <!-- End Table -->

              <hr class="my-5">

              <div class="row justify-content-md-end mb-3">
                <div class="col-md-8 col-lg-7">
                  <dl class="row text-sm-end">

                    <dt class="col-sm-6 fs-3">Subtotal:</dt>
                    <dd class="col-sm-6 fs-3">
                      <span v-text="formatNumber(shipment.sub_total)"> </span>
                      <input type="hidden" name="sub_total" v-model.lazy="shipment.sub_total">
                    </dd>

                    <dt class="col-sm-6 fs-3">Vat:</dt>
                    <dd class="col-sm-6 fs-3">
                      <span v-text="formatNumber(shipment.vat)"> </span>
                      <input type="hidden" name="vat" v-model.lazy="shipment.vat">
                    </dd>

                    <dt class="col-sm-6 fs-3">Total:</dt>
                    <dd class="col-sm-6 fs-3">
                      <span v-text="formatNumber(shipment.amount_total)"> </span>
                      <input type="hidden" name="amount_total" v-model.lazy="shipment.amount_total">

                    </dd>

                    <dt class="col-sm-6 fs-3">Discount:</dt>
                    <dd class="col-sm-6 fs-3">
                      <span v-text="formatNumber(shipment.discount)"> </span>
                    </dd>

                    <dt class="col-sm-6 fs-3">Amount paid:</dt>
                    <dd class="col-sm-6 fs-3">
                      <input type="number" step="0.01" v-model.lazy="shipment.amount_paid" step="0.01" class="form-control fw-semibold fs-3 text-danger" name="amount_paid"/>
                    </dd>
                  </dl>
                  <!-- End Row -->
                </div>
              </div>

            </div>
          </div>
          <!-- End Card -->

          <!-- Footer -->
          <div v-if="isReady" class="d-flex justify-content-end d-print-none gap-3">
            <button class="btn btn-primary" type="submit">
              <i class="bi-save me-1"></i> Save and Exit
            </button>
          </div>
          <div v-if="!isReady" class="d-flex justify-content-end d-print-none gap-3">
            <span class="btn btn-primary" @click="getReady" type="button">
              <i class="bi-save me-1"></i> Calculate
            </span>
          </div>
          <!-- End Footer -->

          <textarea name="shipment" :value="JSON.stringify( shipment )" style="opacity:0; width: 0; margin: 0;"></textarea>
          <!-- End Row -->
        </div>

      </div>
      <!-- End Row -->
    </div>
    <!-- End Content -->


@push('scripts')

  <script type="text/javascript">

    new Vue({
      el: "#shipment-el",
      data(){
        return{
          isReady:false,
          shipment: {
            sales: [],
            sub_total: 0,
            amount_total: 0,
            amount_paid: 0,
            customer_id: null,
            discount: 0,
            vat: 0,
          },
          vat: @json( auth()->user()->getGlobal('vat') ?? 0),
          units: [],
          products: @json( $products),
        }
      },

      methods: {

        getShipment(){
          if( ! this.shipment.id) { return false; }
          axios.get('/shipment-ajax/' + this.shipment.id).then( res => {
            this.shipment = res.data;
          });
        },

        save(){
          axios.post('/save-shipment', this.shipment).then( res => {
            this.shipment = res.data;
          });
        },

        removeItem(index){
          this.shipment.sales.splice(index, 1);
          this.calculate();
        },

        addItem(){
          var sale = {
            product_id: null,
            unit_id: null,
            quantity: 1,
            buying_price:0,
            selling_price:0,
            amount_total:0,
            units:[],
            vat: 0,
            unit_name: "",
          }
          this.shipment.sales.push(sale);
        },

        getReady(){
          this.shipment.sub_total = 0;
          this.shipment.amount_total = 0;
          this.shipment.discount = 0;
          this.shipment.vat = 0;

          for( var sale of this.shipment.sales){
            var amount = eval(sale.quantity * sale.selling_price );
            this.shipment.discount += Number( sale.discount );
            this.shipment.sub_total +=  eval(sale.quantity * sale.selling_price);
            if( sale.vat > 0 ) {
              this.shipment.vat +=  eval( amount * sale.vat / 100 );
            }
          }

          this.shipment.amount_total = eval(this.shipment.sub_total + this.shipment.vat - this.shipment.discount ).toFixed(2);
          this.shipment.sub_total = this.shipment.sub_total.toFixed(2);
          this.shipment.discount = this.shipment.discount.toFixed(2);
          this.shipment.vat = this.shipment.vat.toFixed(2);
          this.shipment.amount_paid = this.shipment.amount_total;
          this.isReady = true;
        },

        addItem(e){

          var product_id = e.target.value;
          var check = this.shipment.sales.filter( item => item.product_id == product_id);
          if( ! product_id || check.length > 0 ) return;

          var products = this.products.filter( item => item.id == product_id);
          if( products.length > 0) {
            var product = products[0];
            var sale = {};
            sale.product = product;
            sale.quantity = 1;
            sale.selling_price = product.selling_price;
            sale.buying_price  = product.buying_price;
            sale.product_id  = product.id;
            sale.total_stock    = product.total_stock;
            sale.discount    = product.discount;
            sale.unit_name   = product?.unit?.name;
            sale.unit_id   = product?.unit_id;
            sale.vat   = product.vat_applied ? this.vat : 0 ;
            sale.units = product.units;
            sale.max   = 0;
            this.shipment.sales.push(sale);
            $(e.target).val("");
          }
          this.calculate();
        },

        setSaleMax(index) {
          var sale = this.shipment.sales[index];
          var products = this.products.filter( item => item.id == sale.product_id);
          if( products.length > 0 ) {
            var product = products[0];
            var units = product.units.filter( item => item.id == sale.unit_id);
            if( units.length > 0 ) {
              var unit = units[0];
              sale.max = eval( product.total_stock / unit.quantity);
              sale.buying_price = unit.buying_price;
              sale.selling_price = unit.selling_price;
            }

          }
          this.calculate();
        },

        formatNumber (num) {
            return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,");
        },

        calculate(){
          this.isReady = false;
        },

      },

      created(){
          var shipment = @json( isset($shipment) ? $shipment : null );
          if( shipment){
            this.shipment = shipment;
            for(index in this.shipment.sales) {
              this.setSaleMax(index)
            }
            this.getReady();
          }

      }

    });

    $(document).on("keydown", "form", function(event) {
        return event.key != "Enter";
    });

  </script>

@endpush
