<?php

namespace App\Http\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\AccountType;
use App\Models\AccountCategory;

class AccountTypeAccountCategoriesDetail extends Component
{
    use WithPagination;

    public AccountType $accountType;
    public $accountCategoryName;
    public $accountCategoryCode;
    public $accountCategoryDescription;
    public $accountCategoryIsActive = true;

    public $showingModal = false;
    public $modalTitle = 'New Account Category';

    protected $rules = [
        'accountCategoryName' => ['required', 'max:255', 'string'],
        'accountCategoryCode' => ['required', 'max:255', 'string'],
        'accountCategoryDescription' => ['nullable', 'max:255', 'string'],
        'accountCategoryIsActive' => ['boolean'],
    ];

    public function mount(AccountType $accountType)
    {
        $this->accountType = $accountType;
    }

    public function render()
    {
        $accountCategories = $this->accountType->categories()->paginate(20);

        return view('livewire.account-type-account-categories-detail', [
            'accountCategories' => $accountCategories,
        ]);
    }

    public function newAccountCategory()
    {
        $this->modalTitle = trans('crud.account_categories.create_title');
        $this->resetAccountCategoryData();

        $this->showingModal = true;
    }

    public function resetAccountCategoryData()
    {
        $this->accountCategoryName = '';
        $this->accountCategoryCode = '';
        $this->accountCategoryDescription = '';
        $this->accountCategoryIsActive = true;
    }

    public function createAccountCategory()
    {
        $this->validate();

        $accountCategory = $this->accountType->categories()->create([
            'name' => $this->accountCategoryName,
            'code' => $this->accountCategoryCode,
            'description' => $this->accountCategoryDescription,
            'is_active' => $this->accountCategoryIsActive,
        ]);

        $this->showingModal = false;

        $this->emit('refreshParent');
        $this->resetAccountCategoryData();
    }
}
