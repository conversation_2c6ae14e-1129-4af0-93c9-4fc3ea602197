<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 24.1.2, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="b07e16be-2c10-4721-ba95-38a6ee556ecd"
	 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 979 585"
	 style="enable-background:new 0 0 979 585;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#E7EAF3;}
	.st1{opacity:0.8;fill:#BDC5D1;enable-background:new    ;}
	.st2{fill:none;stroke:#E7EAF3;stroke-miterlimit:10;}
	.st3{fill:#FFFFFF;}
	.st4{opacity:0.7;fill:#377dff;}
	.st5{opacity:0.3;fill:#FFFFFF;}
	.st6{clip-path:url(#SVGID_13_);}
	.st7{clip-path:url(#SVGID_15_);}
	.st8{fill:#EDB98A;}
	.st9{fill:#E6E6E6;}
	.st10{clip-path:url(#SVGID_17_);fill:#21325B;}
	.st11{clip-path:url(#SVGID_19_);fill:#FFFFFF;}
	.st12{clip-path:url(#SVGID_21_);fill:#FF4F6D;}
	.st13{clip-path:url(#SVGID_23_);fill:#FF4F6D;}
	.st14{clip-path:url(#SVGID_25_);fill:#724133;}
	.st15{fill:#252C2F;}
	.st16{clip-path:url(#SVGID_27_);fill:#4A312C;}
	.st17{opacity:0.5;fill:#00C9A7;}
	.st18{clip-path:url(#SVGID_29_);}
	.st19{clip-path:url(#SVGID_31_);}
	.st20{fill:#AE5D29;}
	.st21{clip-path:url(#SVGID_33_);fill:#E6E6E6;}
	.st22{clip-path:url(#SVGID_35_);fill:#FFFFFF;}
	.st23{clip-path:url(#SVGID_37_);fill:#FF4F6D;}
	.st24{clip-path:url(#SVGID_39_);fill:#FF4F6D;}
	.st25{clip-path:url(#SVGID_41_);fill:#1F333C;}
	.st26{clip-path:url(#SVGID_43_);fill:#1F333C;}
	.st27{clip-path:url(#SVGID_45_);fill:#15232A;}
	.st28{fill:url(#Shade-3_2_);}
	.st29{fill:url(#Shade-6_2_);}
	.st30{clip-path:url(#SVGID_47_);}
	.st31{clip-path:url(#SVGID_49_);}
	.st32{fill:#EA9A59;}
	.st33{fill:#B7C1DB;}
	.st34{clip-path:url(#SVGID_51_);}
	.st35{fill:#65C9FF;}
	.st36{fill:#F4F4F4;}
	.st37{fill:#314756;}
	.st38{clip-path:url(#SVGID_53_);fill:#2C1B18;}
	.st39{fill:#92D9FF;}
	.st40{opacity:0.5;fill:#F5CA99;}
	.st41{clip-path:url(#SVGID_55_);}
	.st42{clip-path:url(#SVGID_57_);}
	.st43{clip-path:url(#SVGID_59_);fill:#21325B;}
	.st44{clip-path:url(#SVGID_61_);fill:#FFFFFF;}
	.st45{clip-path:url(#SVGID_63_);fill:#FF4F6D;}
	.st46{clip-path:url(#SVGID_65_);fill:#FF4F6D;}
	.st47{clip-path:url(#SVGID_67_);fill:#724133;}
	.st48{clip-path:url(#SVGID_69_);fill:#4A312C;}
	.st49{clip-path:url(#SVGID_71_);}
	.st50{clip-path:url(#SVGID_73_);}
	.st51{clip-path:url(#SVGID_75_);fill:#21325B;}
	.st52{clip-path:url(#SVGID_77_);fill:#FFFFFF;}
	.st53{clip-path:url(#SVGID_79_);fill:#FF4F6D;}
	.st54{clip-path:url(#SVGID_81_);fill:#FF4F6D;}
	.st55{clip-path:url(#SVGID_83_);fill:#724133;}
	.st56{clip-path:url(#SVGID_85_);fill:#4A312C;}
	.st57{opacity:0.5;fill:#132144;}
	.st58{clip-path:url(#SVGID_87_);}
	.st59{clip-path:url(#SVGID_89_);}
	.st60{clip-path:url(#SVGID_91_);fill:#E6E6E6;}
	.st61{clip-path:url(#SVGID_93_);fill:#FFFFFF;}
	.st62{clip-path:url(#SVGID_95_);fill:#FF4F6D;}
	.st63{clip-path:url(#SVGID_97_);fill:#FF4F6D;}
	.st64{clip-path:url(#SVGID_99_);fill:#1F333C;}
	.st65{clip-path:url(#SVGID_101_);fill:#1F333C;}
	.st66{clip-path:url(#SVGID_103_);fill:#15232A;}
	.st67{fill:url(#Shade-3_3_);}
	.st68{fill:url(#Shade-6_3_);}
	.st69{opacity:0.7;fill:#ED4C78;}
	.st70{clip-path:url(#SVGID_105_);}
	.st71{clip-path:url(#SVGID_107_);}
	.st72{clip-path:url(#SVGID_109_);}
	.st73{clip-path:url(#SVGID_111_);fill:#2C1B18;}
	.st74{display:none;fill:#377dff;}
	.st75{opacity:0.7;fill:none;stroke:#377dff;stroke-width:3;stroke-miterlimit:10;}
</style>
<g>
	<path class="st0" d="M970.4,0H8.6C3.8,0,0,3.8,0,8.6v24.9h979V8.6C979,3.8,975.2,0,970.4,0z"/>
	<circle class="st1" cx="16" cy="16.5" r="4"/>
	<circle class="st1" cx="29" cy="16.5" r="4"/>
	<circle class="st1" cx="42" cy="16.5" r="4"/>
</g>
<g>
	<rect x="0.6" y="33.5" class="st2" width="977.9" height="551"/>
	<rect x="128.2" y="33.5" class="st2" width="722.6" height="551"/>
	<rect x="255.9" y="33.5" class="st2" width="467.3" height="551"/>
	<rect x="383.5" y="33.5" class="st2" width="212" height="551"/>
	<line class="st2" x1="489.5" y1="33.5" x2="489.5" y2="584.5"/>
	<rect x="0.6" y="113.5" class="st2" width="977.9" height="391.1"/>
	<rect x="0.6" y="192.3" class="st2" width="977.9" height="233.4"/>
	<rect x="0.6" y="271.2" class="st2" width="977.9" height="75.6"/>
</g>
<g>
	<path class="st3" d="M275,325.7H108.8c-9.1,0-16.6-7.4-16.6-16.6v0c0-9.1,7.4-16.6,16.6-16.6H275c9.1,0,16.6,7.4,16.6,16.6v0
		C291.6,318.3,284.2,325.7,275,325.7z"/>
	<path class="st4" d="M275,325.7H108.8c-9.1,0-16.6-7.4-16.6-16.6v0c0-9.1,7.4-16.6,16.6-16.6H275c9.1,0,16.6,7.4,16.6,16.6v0
		C291.6,318.3,284.2,325.7,275,325.7z"/>
	<path class="st5" d="M272.4,311.7h-136c-1.4,0-2.5-1.1-2.5-2.5l0,0c0-1.4,1.1-2.5,2.5-2.5h136c1.4,0,2.5,1.1,2.5,2.5l0,0
		C274.9,310.5,273.8,311.7,272.4,311.7z"/>
	<circle id="SVGID_4_" class="st3" cx="109.1" cy="309.2" r="13.6"/>
	<g>
		<defs>
			<circle id="SVGID_12_" cx="109.1" cy="309.2" r="13.6"/>
		</defs>
		<clipPath id="SVGID_13_">
			<use xlink:href="#SVGID_12_"  style="overflow:visible;"/>
		</clipPath>
		<g class="st6">
			<g>
				<g>
					<g>
						<defs>
							<path id="SVGID_14_" d="M99.6,322.8V322c0-3.8,3.1-6.8,6.8-6.8h0.4v-1.7c-1.7-0.8-2.8-2.4-3-4.2c-0.6-0.1-1-0.6-1-1.1v-1.3
								c0-0.6,0.4-1,0.9-1.1V305c0-2.9,2.4-5.3,5.3-5.3c2.9,0,5.3,2.4,5.3,5.3v0.6c0.5,0.1,0.9,0.6,0.9,1.1v1.3c0,0.6-0.4,1-1,1.1
								c-0.2,1.8-1.3,3.4-3,4.2v1.7h0.4c3.8,0,6.8,3.1,6.8,6.8v0.9H99.6L99.6,322.8z"/>
						</defs>
						<clipPath id="SVGID_15_">
							<use xlink:href="#SVGID_14_"  style="overflow:visible;"/>
						</clipPath>
						<g id="Body-64_3_" class="st7">
							<rect id="Color-28_3_" x="100.2" y="299.7" class="st8" width="18.3" height="19.4"/>
							<path id="Neck_Shadow-16_3_" d="M109.1,314.7c-1.4,0-2.8-0.6-3.8-1.6c-1-1-1.6-2.3-1.6-3.8v-0.7c0,2.9,2.4,5.3,5.3,5.3
								c2.9,0,5.3-2.4,5.3-5.3v0.8c0,2.1-1.3,4.1-3.2,4.9C110.5,314.6,109.8,314.7,109.1,314.7z"/>
						</g>
					</g>
				</g>
			</g>
			<path id="Clothes-28_3_" class="st9" d="M118.6,322.8h-19V322c0-3.6,2.8-6.6,6.4-6.8c0,0.1,0,0.2,0,0.3c0,0.5,0.3,1.1,0.9,1.4
				s1.4,0.6,2.2,0.6c0.8,0,1.6-0.2,2.2-0.6c0.6-0.4,0.9-0.9,0.9-1.4c0-0.1,0-0.2,0-0.3c3.6,0.3,6.3,3.2,6.3,6.8V322.8L118.6,322.8z"
				/>
			<g>
				<g>
					<g>
						<defs>
							<path id="SVGID_16_" d="M118.6,322.8h-19V322c0-3.6,2.8-6.6,6.4-6.8c0,0.1,0,0.2,0,0.3c0,0.5,0.3,1.1,0.9,1.4
								s1.4,0.6,2.2,0.6c0.8,0,1.6-0.2,2.2-0.6c0.6-0.4,0.9-0.9,0.9-1.4c0-0.1,0-0.2,0-0.3c3.6,0.3,6.3,3.2,6.3,6.8V322.8
								L118.6,322.8z"/>
						</defs>
						<clipPath id="SVGID_17_">
							<use xlink:href="#SVGID_16_"  style="overflow:visible;"/>
						</clipPath>
						<rect id="_Color-19_3_" x="96.6" y="312.4" class="st10" width="25" height="10.4"/>
					</g>
				</g>
			</g>
			<path id="Mouth-23_3_" d="M107.3,310.4c0.1,0.9,0.9,1.6,1.8,1.6s1.7-0.7,1.8-1.6c0-0.1-0.1-0.2-0.2-0.2h-3.2
				C107.4,310.2,107.3,310.3,107.3,310.4z"/>
			<g>
				<g>
					<g>
						<defs>
							<path id="SVGID_18_" d="M107.3,310.4c0.1,0.9,0.9,1.6,1.8,1.6s1.7-0.7,1.8-1.6c0-0.1-0.1-0.2-0.2-0.2h-3.2
								C107.4,310.2,107.3,310.3,107.3,310.4z"/>
						</defs>
						<clipPath id="SVGID_19_">
							<use xlink:href="#SVGID_18_"  style="overflow:visible;"/>
						</clipPath>
						<path id="Teeth-6_3_" class="st11" d="M108.1,309.2h2c0.3,0,0.5,0.2,0.5,0.5v0.6c0,0.3-0.2,0.5-0.5,0.5h-2
							c-0.3,0-0.5-0.2-0.5-0.5v-0.6C107.7,309.4,107.9,309.2,108.1,309.2z"/>
					</g>
				</g>
				<g>
					<g>
						<defs>
							<path id="SVGID_20_" d="M107.3,310.4c0.1,0.9,0.9,1.6,1.8,1.6s1.7-0.7,1.8-1.6c0-0.1-0.1-0.2-0.2-0.2h-3.2
								C107.4,310.2,107.3,310.3,107.3,310.4z"/>
						</defs>
						<clipPath id="SVGID_21_">
							<use xlink:href="#SVGID_20_"  style="overflow:visible;"/>
						</clipPath>
						<circle id="Tongue-11_3_" class="st12" cx="108.6" cy="312.3" r="1"/>
					</g>
				</g>
				<g>
					<g>
						<defs>
							<path id="SVGID_22_" d="M107.3,310.4c0.1,0.9,0.9,1.6,1.8,1.6s1.7-0.7,1.8-1.6c0-0.1-0.1-0.2-0.2-0.2h-3.2
								C107.4,310.2,107.3,310.3,107.3,310.4z"/>
						</defs>
						<clipPath id="SVGID_23_">
							<use xlink:href="#SVGID_22_"  style="overflow:visible;"/>
						</clipPath>
						<circle id="Tongue-12_3_" class="st13" cx="109.5" cy="312.3" r="1"/>
					</g>
				</g>
			</g>
			<path id="Nose-16_3_" d="M107.9,308.6c0,0.4,0.5,0.8,1.1,0.8l0,0c0.6,0,1.1-0.3,1.1-0.8"/>
			<circle id="Eye-21_3_" cx="106.6" cy="306.9" r="0.6"/>
			<circle id="Eye-22_3_" cx="111.5" cy="306.9" r="0.6"/>
			<path id="Eyebrow-31_3_" d="M105.3,305.7c0.4-0.5,1.4-0.8,2.3-0.6c0.1,0,0.2,0,0.2-0.1c0-0.1,0-0.2-0.1-0.2
				c-1-0.3-2.2,0.1-2.7,0.7c-0.1,0.1,0,0.2,0.1,0.3C105.1,305.8,105.2,305.8,105.3,305.7z"/>
			<path id="Eyebrow-32_3_" d="M112.9,305.7c-0.4-0.5-1.4-0.8-2.3-0.6c-0.1,0-0.2,0-0.2-0.1c0-0.1,0-0.2,0.1-0.2l0,0
				c1-0.3,2.2,0.1,2.7,0.7c0.1,0.1,0,0.2,0,0.3l0,0C113.1,305.8,113,305.8,112.9,305.7z"/>
			<g>
				<g>
					<g>
						<defs>
							<path id="SVGID_24_" d="M109.1,317c-0.4,0-0.9-0.1-1.3-0.2c-0.4-0.1-0.7-0.3-1-0.5c-0.3,0-0.6-0.1-0.8-0.2
								c-0.3-0.1-0.5-0.3-0.7-0.4c-0.1-0.1-0.2-0.2-0.2-0.4c-0.1-0.1-0.1-0.2-0.2-0.3c0,0-0.1-0.1-0.1-0.1c-0.2-0.1-0.3-0.3-0.4-0.5
								c-0.2-0.3-0.3-0.7-0.4-1.1c-0.1-0.4-0.2-0.7-0.2-1.1c0-0.2,0-0.3,0-0.5c0-0.3,0-0.5,0-0.7c-0.1-0.3-0.1-0.6-0.2-0.8
								c-0.1-0.6-0.2-1.1-0.1-1.7c0-0.5,0.1-0.9,0.2-1.4c0-0.1,0-0.2,0-0.3c0,0,0-0.1,0-0.2c0.1-0.3,0.2-1,0.2-1
								c0,0.8,0.1,1.6,0.2,2.6c0,0,0,0.1,0,0.1c0,0.2,0.1,0.5,0.1,0.7c0,0.1,0,0.2,0,0.2c0,0.1,0,0.3,0,0.4c0.1,0.2,0.4,0.5,0.6,0.6
								c0.1,0,0.1,0,0.2,0c0.3,0,0.6-0.3,0.9-0.6c0.2-0.2,0.4-0.3,0.6-0.4c0.4-0.2,0.9-0.3,1.3-0.3c0.3,0,0.8,0.1,1.3,0.3
								c0.2-0.1,0.4-0.2,0.6-0.2c0.2-0.1,0.4-0.1,0.7-0.1c0.5,0,0.9,0.1,1.3,0.3c0.2,0.1,0.4,0.3,0.6,0.4c0.3,0.3,0.6,0.6,0.9,0.6
								c0.1,0,0.1,0,0.2,0c0.3-0.1,0.6-0.3,0.6-0.6c0-0.1,0-0.3,0-0.4c0-0.1,0-0.2,0-0.2c0-0.2,0-0.5,0.1-0.7l0,0c0,0,0-0.1,0-0.1
								c0.1-1,0.2-1.8,0.2-2.6c0.1,0,0.2,0.7,0.2,1c0,0.1,0,0.1,0,0.2c0,0.1,0,0.2,0,0.3c0.1,0.5,0.2,0.9,0.2,1.4
								c0,0.6,0,1.2-0.1,1.7c-0.1,0.2-0.1,0.5-0.2,0.8c0,0.2,0,0.4,0,0.7c0,0.2,0,0.3,0,0.5c-0.1,0.4-0.1,0.8-0.2,1.1
								c-0.1,0.4-0.2,0.7-0.4,1.1c-0.1,0.2-0.3,0.3-0.4,0.5c0,0-0.1,0.1-0.1,0.1c-0.1,0.1-0.2,0.2-0.2,0.3c-0.1,0.1-0.1,0.2-0.2,0.4
								c-0.2,0.2-0.4,0.3-0.7,0.4c-0.3,0.1-0.5,0.2-0.8,0.2c-0.3,0.2-0.6,0.4-1,0.5C110,316.9,109.6,317,109.1,317z M109.1,310
								c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0.1-0.4,0.1-0.8,0.2c-0.4,0.1-0.8,0.1-0.9,0.2c-0.2,0.2-0.4,0.5-0.3,0.8
								c0,0.3,0.1,0.5,0.3,0.7c0.2,0.3,0.6,0.5,1,0.5c0.5-0.5,1.2-0.5,1.7,0c0,0,0,0,0,0c0.4,0,0.7-0.2,1-0.5
								c0.2-0.2,0.3-0.4,0.3-0.7c0-0.3-0.1-0.6-0.3-0.8c-0.1-0.1-0.5-0.1-0.9-0.2c-0.3-0.1-0.7-0.1-0.8-0.2
								C109.2,310.1,109.2,310.1,109.1,310z"/>
						</defs>
						<clipPath id="SVGID_25_">
							<use xlink:href="#SVGID_24_"  style="overflow:visible;"/>
						</clipPath>
						<rect id="Color-29_3_" x="101.1" y="303.1" class="st14" width="15.9" height="14.4"/>
					</g>
				</g>
			</g>
			<path id="Left-3_3_" class="st15" d="M106.1,309h-0.3c-1.3,0-2.1-0.7-2.1-1.9c0-1,0.2-1.9,2.2-1.9h0.3c2.1,0,2.2,1,2.2,1.9
				C108.5,308.2,107.5,309,106.1,309z M106,305.7c-1.8,0-1.8,0.6-1.8,1.5c0,0.7,0.3,1.5,1.7,1.5h0.3c1.2,0,2-0.6,2-1.5
				c0-0.8,0-1.5-1.8-1.5H106z"/>
			<path id="Right-3_3_" class="st15" d="M112,309h-0.3c-1.3,0-2.1-0.7-2.1-1.9c0-1,0.2-1.9,2.2-1.9h0.3c2.1,0,2.2,1,2.2,1.9
				C114.4,308.2,113.4,309,112,309z M111.9,305.7c-1.8,0-1.8,0.6-1.8,1.5c0,0.7,0.3,1.5,1.7,1.5h0.3c1.2,0,2-0.6,2-1.5
				c0-0.8,0-1.5-1.8-1.5H111.9z"/>
			<path id="Stuff-3_3_" class="st15" d="M103.2,305.7c0.1-0.1,0.8-0.5,2.8-0.5c1.7,0,2,0.2,2.6,0.4l0,0c0.1,0.1,0.3,0.1,0.5,0.1
				c0.1,0,0.3,0,0.4-0.1c0.6-0.3,1.2-0.5,2.6-0.5c2,0,2.7,0.5,2.8,0.5c0.2,0,0.3,0.1,0.3,0.3l0,0v0.3c0,0.2-0.1,0.3-0.3,0.3l0,0
				c0,0-0.7,0-0.7,0.3s-0.2-0.4-0.2-0.5v-0.3c-0.6-0.3-1.3-0.4-2-0.4c-1.1,0-1.7,0.2-2.2,0.4l0,0v0.2l-0.2,0.5l-0.3-0.1
				c0,0-0.1,0-0.1,0c-0.2-0.1-0.4-0.1-0.6,0c-0.1,0-0.1,0-0.2,0.1l-0.3,0.1l-0.3-0.5l0-0.2l0,0c-0.4-0.2-0.8-0.4-2.2-0.4
				c-0.7,0-1.3,0.1-2,0.4v0.3c0,0.2-0.3,0.8-0.3,0.5s-0.6-0.3-0.6-0.3c-0.2,0-0.3-0.1-0.3-0.3l0,0v-0.3
				C102.9,305.8,103,305.7,103.2,305.7L103.2,305.7z"/>
			<g>
				<g>
					<g>
						<defs>
							<path id="SVGID_26_" d="M104.8,299.9c0.5-0.4,1.1-0.7,1.7-0.8c0.6-0.1,1-0.2,1.6,0c0.2,0,0.2,0.1,0.3,0
								c0.1-0.1,0.9-0.9,3.4-0.4c2.5,0.5,3.2,4.2,4.1,4.3c0.3,0.1,0.7,0,0.7-0.2c0.4,0.6,0.5,1.3,0.3,2c-0.1,0.6-0.4,1.1-0.9,1.4
								c-0.5,0.3-1,0.4-1.6,0.3c-0.2,0-0.5-0.1-0.7-0.2c-0.3-0.1-0.5-0.3-0.7-0.4c0.3,0.2,0.6,0.4,0.9,0.6c0.2,0.1,0.3,0.1,0.5,0.2
								c0.1,0,0.4,0.1,0.5,0.1c-0.7,0.1-1.5,0-2.1-0.3c-0.3-0.1-0.6-0.3-0.9-0.5c-0.3-0.2-0.6-0.4-0.8-0.7c0.1,0.1-0.1-0.1-0.1-0.1
								c-0.1-0.1-0.1-0.1-0.2-0.2c-0.1-0.1-0.2-0.2-0.3-0.3c-0.2-0.2-0.4-0.5-0.5-0.7c-0.2-0.3-0.8-1.3-1-1.6
								c0.2,0.5,0.4,1.1,0.6,1.6c-0.4-0.1-0.9-0.5-1.2-0.9c-0.3-0.4-0.5-0.9-0.6-1.4c-0.4,1-1.2,1.8-2.1,2.3c0.2-0.4,0.5-0.7,0.7-1
								c-0.9,0.9-2.3,1.3-2.7,2.6c-0.1-0.5-0.4-0.8-0.5-1.3c-0.1-0.5-0.2-1-0.2-1.6C103.1,301.7,103.9,300.6,104.8,299.9z"/>
						</defs>
						<clipPath id="SVGID_27_">
							<use xlink:href="#SVGID_26_"  style="overflow:visible;"/>
						</clipPath>
						<rect id="Color-30_3_" x="100.5" y="297.2" class="st16" width="17.1" height="12.6"/>
					</g>
				</g>
			</g>
		</g>
	</g>
</g>
<g>
	<path class="st3" d="M448.2,403.5h-82.8c-9.1,0-16.6-7.4-16.6-16.6v0c0-9.1,7.4-16.6,16.6-16.6h82.8c9.1,0,16.6,7.4,16.6,16.6v0
		C464.7,396.1,457.3,403.5,448.2,403.5z"/>
	<path class="st17" d="M448.2,403.5h-82.8c-9.1,0-16.6-7.4-16.6-16.6v0c0-9.1,7.4-16.6,16.6-16.6h82.8c9.1,0,16.6,7.4,16.6,16.6v0
		C464.7,396.1,457.3,403.5,448.2,403.5z"/>
	<path class="st5" d="M449.4,389.4h-55.7c-1.4,0-2.5-1.1-2.5-2.5l0,0c0-1.4,1.1-2.5,2.5-2.5h55.7c1.4,0,2.5,1.1,2.5,2.5l0,0
		C451.9,388.3,450.8,389.4,449.4,389.4z"/>
	<circle id="SVGID_5_" class="st3" cx="365.6" cy="387" r="13.6"/>
	<g>
		<circle id="SVGID_11_" class="st3" cx="365.5" cy="386.9" r="13.6"/>
		<g>
			<g>
				<defs>
					<circle id="SVGID_28_" cx="365.5" cy="386.9" r="13.6"/>
				</defs>
				<clipPath id="SVGID_29_">
					<use xlink:href="#SVGID_28_"  style="overflow:visible;"/>
				</clipPath>
				<g class="st18">
					<g>
						<g>
							<g>
								<defs>
									<path id="SVGID_30_" d="M356.4,400.6v-0.8c0-3.6,2.9-6.5,6.5-6.5h0.4v-1.7c-1.6-0.8-2.7-2.3-2.9-4.1
										c-0.5-0.1-0.9-0.5-0.9-1.1v-1.3c0-0.5,0.4-1,0.9-1.1v-0.6c0-2.8,2.3-5.1,5.1-5.1s5.1,2.3,5.1,5.1v0.6
										c0.5,0.1,0.9,0.5,0.9,1.1v1.3c0,0.5-0.4,1-0.9,1.1c-0.2,1.8-1.3,3.3-2.9,4.1v1.7h0.4c3.6,0,6.5,2.9,6.5,6.5v0.8H356.4z"/>
								</defs>
								<clipPath id="SVGID_31_">
									<use xlink:href="#SVGID_30_"  style="overflow:visible;"/>
								</clipPath>
								<g id="Body-22_1_" class="st19">
									<rect id="Color-3_1_" x="353.5" y="378.4" class="st20" width="24" height="22.2"/>
									<path id="Neck_Shadow-2_1_" d="M365.5,392.8c-1.4,0-2.6-0.5-3.6-1.5c-1-1-1.5-2.2-1.5-3.6V387c0,2.8,2.3,5.1,5.1,5.1
										c2.8,0,5.1-2.3,5.1-5.1v0.7c0,2-1.2,3.9-3.1,4.7C366.8,392.7,366.1,392.8,365.5,392.8z"/>
								</g>
							</g>
						</g>
					</g>
					<g>
						<g>
							<g>
								<defs>
									<path id="SVGID_32_" d="M374.5,400.6h-18.2v-0.8c0-2.9,1.9-5.4,4.7-6.3c0,0.1-0.1,0.3-0.1,0.4c0,0.7,0.5,1.4,1.3,1.9
										s2,0.8,3.2,0.8c1.2,0,2.3-0.3,3.2-0.8c0.8-0.5,1.3-1.2,1.3-1.9c0-0.1,0-0.2,0-0.4c2.7,0.9,4.6,3.4,4.6,6.2V400.6z"/>
								</defs>
								<clipPath id="SVGID_33_">
									<use xlink:href="#SVGID_32_"  style="overflow:visible;"/>
								</clipPath>
								<rect id="_Color-3_1_" x="353.5" y="390.6" class="st21" width="24" height="10"/>
							</g>
						</g>
					</g>
					<g id="_Mouth-2_1_" transform="translate(2 52)">
						<path id="Mouth-9_1_" d="M361.7,336.7c0.1,0.9,0.8,1.5,1.7,1.5c0.9,0,1.6-0.7,1.7-1.5c0-0.1-0.1-0.2-0.2-0.2h-3.1
							C361.8,336.5,361.7,336.6,361.7,336.7z"/>
						<g>
							<g>
								<g>
									<defs>
										<path id="SVGID_34_" d="M361.7,336.7c0.1,0.9,0.8,1.5,1.7,1.5c0.9,0,1.6-0.7,1.7-1.5c0-0.1-0.1-0.2-0.2-0.2h-3.1
											C361.8,336.5,361.7,336.6,361.7,336.7z"/>
									</defs>
									<clipPath id="SVGID_35_">
										<use xlink:href="#SVGID_34_"  style="overflow:visible;"/>
									</clipPath>
									<path id="Teeth_1_" class="st22" d="M362.5,335.5h1.9c0.3,0,0.5,0.2,0.5,0.5v0.5c0,0.3-0.2,0.5-0.5,0.5h-1.9
										c-0.3,0-0.5-0.2-0.5-0.5V336C362.1,335.7,362.3,335.5,362.5,335.5z"/>
								</g>
							</g>
							<g>
								<g>
									<defs>
										<path id="SVGID_36_" d="M361.7,336.7c0.1,0.9,0.8,1.5,1.7,1.5c0.9,0,1.6-0.7,1.7-1.5c0-0.1-0.1-0.2-0.2-0.2h-3.1
											C361.8,336.5,361.7,336.6,361.7,336.7z"/>
									</defs>
									<clipPath id="SVGID_37_">
										<use xlink:href="#SVGID_36_"  style="overflow:visible;"/>
									</clipPath>
									<circle id="Tongue-2_1_" class="st23" cx="363" cy="338.5" r="1"/>
								</g>
							</g>
							<g>
								<g>
									<defs>
										<path id="SVGID_38_" d="M361.7,336.7c0.1,0.9,0.8,1.5,1.7,1.5c0.9,0,1.6-0.7,1.7-1.5c0-0.1-0.1-0.2-0.2-0.2h-3.1
											C361.8,336.5,361.7,336.6,361.7,336.7z"/>
									</defs>
									<clipPath id="SVGID_39_">
										<use xlink:href="#SVGID_38_"  style="overflow:visible;"/>
									</clipPath>
									<circle id="Tongue-3_1_" class="st24" cx="363.9" cy="338.5" r="1"/>
								</g>
							</g>
						</g>
					</g>
					<path id="Nose-2_1_" d="M364.4,387c0,0.4,0.5,0.7,1.1,0.7l0,0c0.6,0,1.1-0.3,1.1-0.7"/>
					<circle id="Eye_1_" cx="363.1" cy="385.4" r="0.5"/>
					<circle id="Eye-2_1_" cx="367.8" cy="385.4" r="0.5"/>
					<path id="Eyebrow-3_1_" d="M361.8,384.2c0.4-0.5,1.3-0.8,2.2-0.6c0.1,0,0.2,0,0.2-0.1c0-0.1,0-0.2-0.1-0.2
						c-1-0.2-2.1,0.1-2.6,0.7c-0.1,0.1,0,0.2,0,0.3C361.6,384.3,361.7,384.3,361.8,384.2z"/>
					<path id="Eyebrow-4_1_" d="M369.1,384.2c-0.4-0.5-1.3-0.8-2.2-0.6c-0.1,0-0.2,0-0.2-0.1c0-0.1,0-0.2,0.1-0.2l0,0
						c1-0.2,2.1,0.1,2.6,0.7c0.1,0.1,0,0.2,0,0.3l0,0C369.3,384.3,369.2,384.3,369.1,384.2z"/>
					<g>
						<g>
							<g>
								<defs>
									<path id="SVGID_40_" d="M377.4,400.6h-2.9v-0.8c0-3.6-2.9-6.5-6.5-6.5h-0.4v-1.7c1.6-0.8,2.7-2.4,2.9-4.2
										c0.5-0.1,0.9-0.5,0.9-1.1v-1.2c0-0.5-0.4-1-0.9-1.1v-0.6c0-0.7-0.1-1.4-0.4-2c-0.2-0.4-0.8-0.6-1.7-0.8
										c-1-0.2-2-0.3-2.9-0.3c-1,0-2,0.1-3,0.3c-0.9,0.2-1.5,0.5-1.7,0.8c-0.3,0.6-0.4,1.3-0.4,2v0.6c-0.5,0.1-0.9,0.5-0.9,1.1
										v1.2c0,0.5,0.4,1,0.9,1.1c0.2,1.8,1.3,3.4,2.9,4.2v1.7h-0.4c-3.6,0-6.5,2.9-6.5,6.5v0.8h-2.9v-25.4h24V400.6z"/>
								</defs>
								<clipPath id="SVGID_41_">
									<use xlink:href="#SVGID_40_"  style="overflow:visible;"/>
								</clipPath>
								<path id="Hat-3_1_" class="st25" d="M361.1,378.2c0.4-1.7,1.8-2.9,3.6-2.9h1.7c1.7,0,3.2,1.2,3.6,2.9l0.8,3.8h-10.4
									L361.1,378.2z"/>
							</g>
						</g>
						<g>
							<g>
								<defs>
									<path id="SVGID_42_" d="M377.4,400.6h-2.9v-0.8c0-3.6-2.9-6.5-6.5-6.5h-0.4v-1.7c1.6-0.8,2.7-2.4,2.9-4.2
										c0.5-0.1,0.9-0.5,0.9-1.1v-1.2c0-0.5-0.4-1-0.9-1.1v-0.6c0-0.7-0.1-1.4-0.4-2c-0.2-0.4-0.8-0.6-1.7-0.8
										c-1-0.2-2-0.3-2.9-0.3c-1,0-2,0.1-3,0.3c-0.9,0.2-1.5,0.5-1.7,0.8c-0.3,0.6-0.4,1.3-0.4,2v0.6c-0.5,0.1-0.9,0.5-0.9,1.1
										v1.2c0,0.5,0.4,1,0.9,1.1c0.2,1.8,1.3,3.4,2.9,4.2v1.7h-0.4c-3.6,0-6.5,2.9-6.5,6.5v0.8h-2.9v-25.4h24V400.6z"/>
								</defs>
								<clipPath id="SVGID_43_">
									<use xlink:href="#SVGID_42_"  style="overflow:visible;"/>
								</clipPath>
								<ellipse id="Hipster_1_" class="st26" cx="365.5" cy="383.1" rx="11.1" ry="5.2"/>
							</g>
						</g>
						<g>
							<g>
								<defs>
									<path id="SVGID_44_" d="M377.4,400.6h-2.9v-0.8c0-3.6-2.9-6.5-6.5-6.5h-0.4v-1.7c1.6-0.8,2.7-2.4,2.9-4.2
										c0.5-0.1,0.9-0.5,0.9-1.1v-1.2c0-0.5-0.4-1-0.9-1.1v-0.6c0-0.7-0.1-1.4-0.4-2c-0.2-0.4-0.8-0.6-1.7-0.8
										c-1-0.2-2-0.3-2.9-0.3c-1,0-2,0.1-3,0.3c-0.9,0.2-1.5,0.5-1.7,0.8c-0.3,0.6-0.4,1.3-0.4,2v0.6c-0.5,0.1-0.9,0.5-0.9,1.1
										v1.2c0,0.5,0.4,1,0.9,1.1c0.2,1.8,1.3,3.4,2.9,4.2v1.7h-0.4c-3.6,0-6.5,2.9-6.5,6.5v0.8h-2.9v-25.4h24V400.6z"/>
								</defs>
								<clipPath id="SVGID_45_">
									<use xlink:href="#SVGID_44_"  style="overflow:visible;"/>
								</clipPath>
								<ellipse id="Very_1_" class="st27" cx="365.5" cy="382.6" rx="5.6" ry="2.3"/>
							</g>
						</g>
					</g>
					<path id="Shade-2_1_" d="M364.7,385.4c0,0.9-0.7,1.6-2,1.6h-0.3c-1.3,0-1.7-0.7-1.7-1.6l0,0c0-0.9,0.1-1.6,1.9-1.6h0.3
						C364.6,383.8,364.7,384.5,364.7,385.4z"/>
					
						<linearGradient id="Shade-3_2_" gradientUnits="userSpaceOnUse" x1="821.3809" y1="435.9853" x2="821.3809" y2="435.8944" gradientTransform="matrix(43.995 0 0 -35.142 -35773.9453 15705.1963)">
						<stop  offset="0" style="stop-color:#FFFFFF;stop-opacity:0.502"/>
						<stop  offset="0.705" style="stop-color:#000000;stop-opacity:0.502"/>
					</linearGradient>
					<path id="Shade-3_1_" class="st28" d="M364.7,385.4c0,0.9-0.7,1.6-2,1.6h-0.3c-1.3,0-1.7-0.7-1.7-1.6l0,0
						c0-0.9,0.1-1.6,1.9-1.6h0.3C364.6,383.8,364.7,384.5,364.7,385.4z"/>
					<path id="Shade-5_1_" d="M370.3,385.4c0,0.9-0.7,1.6-2,1.6H368c-1.3,0-1.7-0.7-1.7-1.6l0,0c0-0.9,0.1-1.6,1.9-1.6h0.3
						C370.2,383.8,370.3,384.5,370.3,385.4z"/>
					
						<linearGradient id="Shade-6_2_" gradientUnits="userSpaceOnUse" x1="820.1085" y1="435.9853" x2="820.1085" y2="435.8944" gradientTransform="matrix(43.995 0 0 -35.142 -35712.375 15705.1963)">
						<stop  offset="0" style="stop-color:#FFFFFF;stop-opacity:0.502"/>
						<stop  offset="0.705" style="stop-color:#000000;stop-opacity:0.502"/>
					</linearGradient>
					<path id="Shade-6_1_" class="st29" d="M370.3,385.4c0,0.9-0.7,1.6-2,1.6H368c-1.3,0-1.7-0.7-1.7-1.6l0,0c0-0.9,0.1-1.6,1.9-1.6
						h0.3C370.2,383.8,370.3,384.5,370.3,385.4z"/>
					<path id="Left_1_" class="st15" d="M362.7,387.3h-0.3c-1.3,0-2-0.7-2-1.9c0-0.9,0.2-1.9,2.1-1.9h0.3c2,0,2.1,0.9,2.1,1.9
						C365,386.5,364,387.3,362.7,387.3z M362.6,384.1c-1.6,0-1.6,0.6-1.6,1.3c0,0.6,0.3,1.3,1.5,1.3h0.3c1,0,1.7-0.5,1.7-1.3
						c0-0.7,0-1.3-1.6-1.3H362.6z"/>
					<path id="Right_1_" class="st15" d="M368.3,387.3H368c-1.3,0-2-0.7-2-1.9c0-0.9,0.2-1.9,2.1-1.9h0.3c2,0,2.1,0.9,2.1,1.9
						C370.6,386.5,369.6,387.3,368.3,387.3z M368.2,384.1c-1.6,0-1.6,0.6-1.6,1.3c0,0.6,0.3,1.3,1.5,1.3h0.3c1,0,1.7-0.5,1.7-1.3
						c0-0.7,0-1.3-1.6-1.3H368.2z"/>
					<path id="Stuff_1_" class="st15" d="M359.9,384.1c0.1-0.1,0.7-0.5,2.7-0.5c1.6,0,1.9,0.2,2.5,0.4l0,0c0.1,0.1,0.3,0.1,0.4,0.1
						c0.1,0,0.3,0,0.4-0.1c0.6-0.3,1.2-0.5,2.5-0.5c1.9,0,2.6,0.5,2.7,0.5c0.1,0,0.3,0.1,0.3,0.3l0,0v0.3c0,0.1-0.1,0.3-0.3,0.3l0,0
						c0,0-0.5,0-0.5,0.3s-0.3-0.4-0.3-0.5v-0.3c-0.3-0.1-0.9-0.3-1.9-0.3c-1.1,0-1.6,0.1-2.1,0.3l0,0l-0.2,0.1l0.2,0.1l-0.2,0.5
						l-0.2-0.1c0,0-0.1,0-0.1,0c-0.2-0.1-0.4-0.1-0.5,0c-0.1,0-0.1,0-0.2,0.1l-0.2,0.1l-0.2-0.5l0.2-0.1l0,0l-0.2-0.1l0,0
						c-0.4-0.2-0.8-0.3-2.1-0.3c-1,0-1.5,0.1-1.9,0.3v0.3c0,0.1-0.3,0.8-0.3,0.5s-0.5-0.3-0.5-0.3c-0.1,0-0.3-0.1-0.3-0.3v-0.3
						C359.6,384.2,359.8,384.1,359.9,384.1L359.9,384.1z"/>
				</g>
			</g>
		</g>
	</g>
</g>
<g>
	<path class="st3" d="M881.7,481h-82.8c-9.1,0-16.6-7.4-16.6-16.6v0c0-9.1,7.4-16.6,16.6-16.6h82.8c9.1,0,16.6,7.4,16.6,16.6v0
		C898.2,473.6,890.8,481,881.7,481z"/>
	<path class="st4" d="M881.7,481h-82.8c-9.1,0-16.6-7.4-16.6-16.6v0c0-9.1,7.4-16.6,16.6-16.6h82.8c9.1,0,16.6,7.4,16.6,16.6v0
		C898.2,473.6,890.8,481,881.7,481z"/>
	<path class="st5" d="M880.7,466.9h-56.3c-1.4,0-2.5-1.1-2.5-2.5l0,0c0-1.4,1.1-2.5,2.5-2.5h56.3c1.4,0,2.5,1.1,2.5,2.5l0,0
		C883.2,465.8,882.1,466.9,880.7,466.9z"/>
	<circle id="SVGID_7_" class="st3" cx="799" cy="464.5" r="13.6"/>
	<g>
		<circle id="SVGID_6_" class="st3" cx="799" cy="464.5" r="13.6"/>
		<g>
			<defs>
				<circle id="SVGID_46_" cx="799" cy="464.5" r="13.6"/>
			</defs>
			<clipPath id="SVGID_47_">
				<use xlink:href="#SVGID_46_"  style="overflow:visible;"/>
			</clipPath>
			<g class="st30">
				<g>
					<g>
						<g>
							<defs>
								<path id="SVGID_48_" d="M790.9,478.1v-0.8c0-3.3,2.7-6,6-6h0.3v-1.5c-1.5-0.7-2.5-2.1-2.7-3.8c-0.5-0.1-0.9-0.5-0.9-1v-1.2
									c0-0.5,0.4-0.9,0.8-1v-0.5c0-2.6,2.1-4.7,4.7-4.7s4.7,2.1,4.7,4.7v0.5c0.5,0.1,0.8,0.5,0.8,1v1.2c0,0.5-0.4,0.9-0.9,1
									c-0.2,1.6-1.2,3.1-2.7,3.8v1.5h0.3c3.3,0,6,2.7,6,6v0.8L790.9,478.1L790.9,478.1z"/>
							</defs>
							<clipPath id="SVGID_49_">
								<use xlink:href="#SVGID_48_"  style="overflow:visible;"/>
							</clipPath>
							<g id="Body-49" class="st31">
								<rect id="Color-19" x="788.2" y="457.6" class="st32" width="22.2" height="20.5"/>
								<path id="Neck_Shadow-11" d="M799.3,470.9c-1.2,0-2.4-0.5-3.3-1.4c-0.9-0.9-1.4-2.1-1.4-3.3v-0.6c0,2.6,2.1,4.7,4.7,4.7
									c2.6,0,4.7-2.1,4.7-4.7v0.7c0,1.9-1.1,3.6-2.9,4.3C800.5,470.7,799.9,470.9,799.3,470.9z"/>
							</g>
						</g>
					</g>
				</g>
				<path id="Hoodie-2" class="st33" d="M807.7,478.1h-16.8v-0.8c0-2.4,1.5-4.6,3.7-5.6c0-0.3,0.1-0.5,0.2-0.8
					c0.1-0.2,0.3-0.4,0.6-0.5c0.4-0.3,1.1-0.4,1.9-0.5v1.6c0,1.1,0.9,2,2,2s2-0.9,2-2v-1.6c0.8,0.1,1.5,0.3,1.9,0.5
					c0.2,0.1,0.4,0.3,0.6,0.5c0.1,0.2,0.2,0.5,0.2,0.8c2.2,0.9,3.7,3.1,3.7,5.6V478.1z"/>
				<g>
					<g>
						<g>
							<defs>
								<path id="SVGID_50_" d="M807.7,478.1h-16.8v-0.8c0-2.4,1.5-4.6,3.7-5.6c0-0.3,0.1-0.5,0.2-0.8c0.1-0.2,0.3-0.4,0.6-0.5
									c0.4-0.3,1.1-0.4,1.9-0.5v1.6c0,1.1,0.9,2,2,2s2-0.9,2-2v-1.6c0.8,0.1,1.5,0.3,1.9,0.5c0.2,0.1,0.4,0.3,0.6,0.5
									c0.1,0.2,0.2,0.5,0.2,0.8c2.2,0.9,3.7,3.1,3.7,5.6V478.1z"/>
							</defs>
							<clipPath id="SVGID_51_">
								<use xlink:href="#SVGID_50_"  style="overflow:visible;"/>
							</clipPath>
							<g id="Mask_Group_5" class="st34">
								<rect id="_Color-13" x="788.2" y="468.8" class="st35" width="22.2" height="9.2"/>
								<path id="Straps" class="st36" d="M796.8,478.1h-0.6v-4.4c0.2,0.1,0.4,0.2,0.6,0.3L796.8,478.1L796.8,478.1z M802.1,477.4
									c-0.2,0-0.3-0.1-0.3-0.3V474c0.2-0.1,0.4-0.2,0.6-0.3v3.4C802.4,477.3,802.3,477.4,802.1,477.4z"/>
								<path id="Shadow-6" d="M799.4,474.6L799.4,474.6c1.1,0,2.3-0.3,3.2-0.9c0.9-0.6,1.5-1.4,1.5-2.2c0-0.7-0.4-1.2-1.3-1.5
									c1.4,0.3,2.1,0.8,2.1,1.6c0,0.4-0.2,0.8-0.5,1.2c-0.4,0.4-0.8,0.7-1.3,1C802.1,474.2,800.7,474.5,799.4,474.6z M799.2,474.6
									c-1.3,0-2.7-0.3-3.8-0.9c-0.5-0.2-0.9-0.6-1.3-1c-0.3-0.4-0.5-0.8-0.5-1.2c0-0.8,0.7-1.4,2.1-1.6c-0.8,0.3-1.3,0.8-1.3,1.5
									c0,0.8,0.5,1.6,1.5,2.2C796.9,474.2,798.1,474.5,799.2,474.6L799.2,474.6z"/>
							</g>
						</g>
					</g>
				</g>
				<path id="Mouth-17" d="M798.1,467.2c0,0.4,0.5,0.8,1.2,0.8s1.2-0.3,1.2-0.8c0-0.1-0.1-0.2-0.2-0.2s-0.2,0.1-0.2,0.2c0,0,0,0,0,0
					c-0.1,0.2-0.4,0.4-0.8,0.4c-0.5,0-0.7-0.2-0.8-0.4c0-0.1-0.1-0.2-0.2-0.2c0,0,0,0,0,0C798.2,467,798.1,467.1,798.1,467.2z"/>
				<path id="Nose-11" d="M798.3,465.5c0,0.4,0.4,0.7,1,0.7l0,0c0.6,0,1-0.3,1-0.7"/>
				<path id="Closed_Eye-3" d="M796,464.4c0.2,0.3,0.5,0.6,0.9,0.5c0.4,0,0.7-0.2,0.9-0.5c0-0.1,0-0.2-0.1-0.1
					c-0.2,0.2-0.5,0.3-0.8,0.3c-0.3,0-0.6-0.1-0.8-0.3C796,464.3,795.9,464.3,796,464.4z"/>
				<path id="Closed_Eye-4" d="M800.8,464.4c0.2,0.3,0.5,0.6,0.9,0.5c0.4,0,0.7-0.2,0.9-0.5c0-0.1,0-0.2-0.1-0.1
					c-0.2,0.2-0.5,0.3-0.8,0.3c-0.3,0-0.6-0.1-0.8-0.3C800.8,464.3,800.8,464.3,800.8,464.4z"/>
				<path id="Eyebrow-21" d="M795.9,462.9c0.3-0.5,1.2-0.7,2-0.5c0.1,0,0.2,0,0.2-0.1c0-0.1,0-0.2-0.1-0.2c-0.9-0.2-1.9,0.1-2.3,0.7
					c-0.1,0.1,0,0.2,0,0.2C795.8,463,795.9,463,795.9,462.9z"/>
				<path id="Eyebrow-22" d="M802.7,462.9c-0.3-0.5-1.2-0.7-2-0.5c-0.1,0-0.2,0-0.2-0.1c0-0.1,0-0.2,0.1-0.2l0,0
					c0.9-0.2,1.9,0.1,2.4,0.7c0.1,0.1,0,0.2,0,0.2l0,0C802.8,463,802.7,463,802.7,462.9z"/>
				<path id="Hair-13" class="st37" d="M801.3,470.8v-1.1c1.5-0.7,2.5-2.2,2.7-3.8c0.5-0.1,0.9-0.5,0.9-1v-1.1c0-0.5-0.4-0.9-0.8-1
					v-0.5c0-0.4-0.1-0.9-0.2-1.3l0.1,1.1l-1-2l-3.8-1.6l-2.5,0.9l-1.8,1.7l0-0.2c-0.2,0.5-0.3,1-0.3,1.5v0.5c-0.5,0.1-0.8,0.5-0.8,1
					v1.1c0,0.5,0.4,0.9,0.9,1c0.1,1.7,1.2,3.1,2.7,3.8v0.9c-1.1,0.5-2.3,0.3-3.2-0.4c-0.2,0-0.3,0-0.5,0c-1.7,0-3.3-1-4-2.5
					c-0.3-0.2-0.6-0.5-0.8-0.8c-0.2-0.4-0.3-0.7-0.3-1.1c0-0.2,0-0.3,0.1-0.5c-0.6-1.5-0.3-3.3,0.6-4.6c0-0.1,0-0.1,0-0.2
					c0-0.9,0.5-1.8,1.4-2.2c0.7-1.6,2.3-2.6,4-2.7c0.4-0.4,1-0.7,1.7-0.7c0.2,0,0.4,0,0.6,0.1c1.6-0.6,3.4-0.6,5,0
					c0.1,0,0.2,0,0.3,0c0.6,0,1.2,0.2,1.7,0.7c1.7,0.1,3.3,1.1,4,2.7c0.8,0.4,1.4,1.2,1.4,2.2c0,0.1,0,0.1,0,0.2
					c1,1.3,1.2,3,0.6,4.6c0.1,0.4,0.1,0.8,0,1.1c0,0.2-0.1,0.4-0.2,0.5c-0.2,0.3-0.4,0.6-0.8,0.8c-0.8,1.7-2.6,2.7-4.5,2.5
					C803.4,470.9,802.3,471.1,801.3,470.8z"/>
				<g>
					<g>
						<g>
							<defs>
								<path id="SVGID_52_" d="M801.3,470.8v-1.1c1.5-0.7,2.5-2.2,2.7-3.8c0.5-0.1,0.9-0.5,0.9-1v-1.1c0-0.5-0.4-0.9-0.8-1v-0.5
									c0-0.4-0.1-0.9-0.2-1.3l0.1,1.1l-1-2l-3.8-1.6l-2.5,0.9l-1.8,1.7l0-0.2c-0.2,0.5-0.3,1-0.3,1.5v0.5c-0.5,0.1-0.8,0.5-0.8,1
									v1.1c0,0.5,0.4,0.9,0.9,1c0.1,1.7,1.2,3.1,2.7,3.8v0.9c-1.1,0.5-2.3,0.3-3.2-0.4c-0.2,0-0.3,0-0.5,0c-1.7,0-3.3-1-4-2.5
									c-0.3-0.2-0.6-0.5-0.8-0.8c-0.2-0.4-0.3-0.7-0.3-1.1c0-0.2,0-0.3,0.1-0.5c-0.6-1.5-0.3-3.3,0.6-4.6c0-0.1,0-0.1,0-0.2
									c0-0.9,0.5-1.8,1.4-2.2c0.7-1.6,2.3-2.6,4-2.7c0.4-0.4,1-0.7,1.7-0.7c0.2,0,0.4,0,0.6,0.1c1.6-0.6,3.4-0.6,5,0
									c0.1,0,0.2,0,0.3,0c0.6,0,1.2,0.2,1.7,0.7c1.7,0.1,3.3,1.1,4,2.7c0.8,0.4,1.4,1.2,1.4,2.2c0,0.1,0,0.1,0,0.2
									c1,1.3,1.2,3,0.6,4.6c0.1,0.4,0.1,0.8,0,1.1c0,0.2-0.1,0.4-0.2,0.5c-0.2,0.3-0.4,0.6-0.8,0.8c-0.8,1.7-2.6,2.7-4.5,2.5
									C803.4,470.9,802.3,471.1,801.3,470.8z"/>
							</defs>
							<clipPath id="SVGID_53_">
								<use xlink:href="#SVGID_52_"  style="overflow:visible;"/>
							</clipPath>
							<rect id="Color-20" x="788.2" y="454.6" class="st38" width="22.2" height="23.5"/>
						</g>
					</g>
				</g>
				<path id="Band-3" class="st39" d="M794.6,462.9L794.6,462.9c0-0.2-0.1-0.4-0.1-0.7c0-0.6,0.1-1.2,0.4-1.7c0.2-0.5,0.6-1,1-1.4
					c0.4-0.4,1-0.7,1.5-0.9c1.2-0.5,2.5-0.5,3.7,0c0.6,0.2,1.1,0.5,1.5,0.9c0.4,0.4,0.8,0.9,1,1.4c0.3,0.7,0.4,1.6,0.3,2.4
					c-0.3-2.1-2.4-3.7-4.7-3.7S794.9,460.8,794.6,462.9z"/>
				<path id="Lennon_Glasses-3" class="st15" d="M801.9,466c-1.1,0-2-0.9-2-2c0-0.1,0-0.2,0-0.4c0-0.4-0.3-0.6-0.6-0.7
					c-0.3,0-0.6,0.3-0.6,0.6c0,0.1,0,0.3,0,0.4c0,1.1-0.9,2-2,2c-1.1,0-2-0.9-2-2c0-0.2,0-0.4,0.1-0.6h-0.6c-0.1,0-0.2-0.1-0.2-0.2
					c0-0.1,0.1-0.2,0.2-0.2h0.7c0,0,0,0,0.1,0c0.6-1,1.8-1.3,2.7-0.8c0.3,0.2,0.6,0.5,0.8,0.8c0.3-0.4,0.9-0.6,1.3-0.3
					c0.1,0.1,0.2,0.2,0.3,0.3c0.5-1,1.7-1.4,2.7-0.9c0.4,0.2,0.7,0.5,0.9,0.8c0,0,0,0,0.1,0h0.7c0.1,0,0.2,0.1,0.2,0.2
					c0,0.1-0.1,0.2-0.2,0.2h-0.6c0.3,1.1-0.3,2.2-1.3,2.5C802.3,466,802.1,466,801.9,466z M801.9,462.3c-0.9,0-1.7,0.8-1.7,1.7
					c0,0.9,0.8,1.7,1.7,1.7c0.9,0,1.7-0.8,1.7-1.7C803.6,463.1,802.8,462.3,801.9,462.3z M796.7,462.3c-0.9,0-1.7,0.8-1.7,1.7
					c0,0.9,0.8,1.7,1.7,1.7c0.9,0,1.7-0.8,1.7-1.7C798.4,463.1,797.6,462.3,796.7,462.3z"/>
			</g>
		</g>
	</g>
</g>
<g>
	<path class="st3" d="M233.7,562.8h-83.5c-9.1,0-16.6-7.4-16.6-16.6l0,0c0-9.1,7.4-16.6,16.6-16.6h83.5c9.1,0,16.6,7.4,16.6,16.6
		l0,0C250.2,555.4,242.8,562.8,233.7,562.8z"/>
	<path class="st40" d="M233.7,562.8h-83.5c-9.1,0-16.6-7.4-16.6-16.6l0,0c0-9.1,7.4-16.6,16.6-16.6h83.5c9.1,0,16.6,7.4,16.6,16.6
		l0,0C250.2,555.4,242.8,562.8,233.7,562.8z"/>
	<path class="st5" d="M234.1,548.7h-55.6c-1.4,0-2.5-1.1-2.5-2.5l0,0c0-1.4,1.1-2.5,2.5-2.5h55.6c1.4,0,2.5,1.1,2.5,2.5l0,0
		C236.6,547.6,235.4,548.7,234.1,548.7z"/>
	<circle id="SVGID_1_" class="st3" cx="150.4" cy="546.3" r="13.6"/>
	<g>
		<defs>
			<circle id="SVGID_54_" cx="150.4" cy="546.3" r="13.6"/>
		</defs>
		<clipPath id="SVGID_55_">
			<use xlink:href="#SVGID_54_"  style="overflow:visible;"/>
		</clipPath>
		<g class="st41">
			<g>
				<g>
					<g>
						<defs>
							<path id="SVGID_56_" d="M141,559.9v-0.9c0-3.8,3.1-6.8,6.8-6.8h0.4v-1.7c-1.7-0.8-2.8-2.4-3-4.2c-0.6-0.1-1-0.6-1-1.1v-1.3
								c0-0.6,0.4-1,0.9-1.1v-0.6c0-2.9,2.4-5.3,5.3-5.3s5.3,2.4,5.3,5.3v0.6c0.5,0.1,0.9,0.6,0.9,1.1v1.3c0,0.6-0.4,1-1,1.1
								c-0.2,1.8-1.3,3.4-3,4.2v1.7h0.4c3.8,0,6.8,3.1,6.8,6.8v0.9H141L141,559.9z"/>
						</defs>
						<clipPath id="SVGID_57_">
							<use xlink:href="#SVGID_56_"  style="overflow:visible;"/>
						</clipPath>
						<g id="Body-64_1_" class="st42">
							<rect id="Color-28_1_" x="141.6" y="536.8" class="st8" width="18.3" height="19.4"/>
							<path id="Neck_Shadow-16_1_" d="M150.4,551.8c-1.4,0-2.8-0.6-3.8-1.6c-1-1-1.6-2.3-1.6-3.8v-0.7c0,2.9,2.4,5.3,5.3,5.3
								c2.9,0,5.3-2.4,5.3-5.3v0.8c0,2.1-1.3,4.1-3.2,4.9C151.9,551.6,151.2,551.8,150.4,551.8z"/>
						</g>
					</g>
				</g>
			</g>
			<path id="Clothes-28_1_" class="st9" d="M159.9,559.9h-19v-0.8c0-3.6,2.8-6.6,6.4-6.8c0,0.1,0,0.2,0,0.3c0,0.5,0.3,1.1,0.9,1.4
				c0.6,0.4,1.4,0.6,2.2,0.6s1.6-0.2,2.2-0.6c0.6-0.4,0.9-0.9,0.9-1.4c0-0.1,0-0.2,0-0.3c3.6,0.3,6.3,3.2,6.3,6.8V559.9L159.9,559.9
				z"/>
			<g>
				<g>
					<g>
						<defs>
							<path id="SVGID_58_" d="M159.9,559.9h-19v-0.8c0-3.6,2.8-6.6,6.4-6.8c0,0.1,0,0.2,0,0.3c0,0.5,0.3,1.1,0.9,1.4
								c0.6,0.4,1.4,0.6,2.2,0.6s1.6-0.2,2.2-0.6c0.6-0.4,0.9-0.9,0.9-1.4c0-0.1,0-0.2,0-0.3c3.6,0.3,6.3,3.2,6.3,6.8V559.9
								L159.9,559.9z"/>
						</defs>
						<clipPath id="SVGID_59_">
							<use xlink:href="#SVGID_58_"  style="overflow:visible;"/>
						</clipPath>
						<rect id="_Color-19_1_" x="137.9" y="549.5" class="st43" width="25" height="10.4"/>
					</g>
				</g>
			</g>
			<path id="Mouth-23_1_" d="M148.7,547.5c0.1,0.9,0.9,1.6,1.8,1.6s1.7-0.7,1.8-1.6c0-0.1-0.1-0.2-0.2-0.2h-3.2
				C148.7,547.3,148.7,547.4,148.7,547.5z"/>
			<g>
				<g>
					<g>
						<defs>
							<path id="SVGID_60_" d="M148.7,547.5c0.1,0.9,0.9,1.6,1.8,1.6s1.7-0.7,1.8-1.6c0-0.1-0.1-0.2-0.2-0.2h-3.2
								C148.7,547.3,148.7,547.4,148.7,547.5z"/>
						</defs>
						<clipPath id="SVGID_61_">
							<use xlink:href="#SVGID_60_"  style="overflow:visible;"/>
						</clipPath>
						<path id="Teeth-6_1_" class="st44" d="M149.5,546.3h2c0.3,0,0.5,0.2,0.5,0.5v0.6c0,0.3-0.2,0.5-0.5,0.5h-2
							c-0.3,0-0.5-0.2-0.5-0.5v-0.6C149,546.5,149.2,546.3,149.5,546.3z"/>
					</g>
				</g>
				<g>
					<g>
						<defs>
							<path id="SVGID_62_" d="M148.7,547.5c0.1,0.9,0.9,1.6,1.8,1.6s1.7-0.7,1.8-1.6c0-0.1-0.1-0.2-0.2-0.2h-3.2
								C148.7,547.3,148.7,547.4,148.7,547.5z"/>
						</defs>
						<clipPath id="SVGID_63_">
							<use xlink:href="#SVGID_62_"  style="overflow:visible;"/>
						</clipPath>
						<circle id="Tongue-11_1_" class="st45" cx="150" cy="549.4" r="1"/>
					</g>
				</g>
				<g>
					<g>
						<defs>
							<path id="SVGID_64_" d="M148.7,547.5c0.1,0.9,0.9,1.6,1.8,1.6s1.7-0.7,1.8-1.6c0-0.1-0.1-0.2-0.2-0.2h-3.2
								C148.7,547.3,148.7,547.4,148.7,547.5z"/>
						</defs>
						<clipPath id="SVGID_65_">
							<use xlink:href="#SVGID_64_"  style="overflow:visible;"/>
						</clipPath>
						<circle id="Tongue-12_1_" class="st46" cx="150.9" cy="549.4" r="1"/>
					</g>
				</g>
			</g>
			<path id="Nose-16_1_" d="M149.3,545.7c0,0.4,0.5,0.8,1.1,0.8l0,0c0.6,0,1.1-0.3,1.1-0.8"/>
			<circle id="Eye-21_1_" cx="148" cy="544" r="0.6"/>
			<circle id="Eye-22_1_" cx="152.9" cy="544" r="0.6"/>
			<path id="Eyebrow-31_1_" d="M146.6,542.8c0.4-0.5,1.4-0.8,2.3-0.6c0.1,0,0.2,0,0.2-0.1c0-0.1,0-0.2-0.1-0.2
				c-1-0.3-2.2,0.1-2.7,0.7c-0.1,0.1,0,0.2,0.1,0.3C146.4,542.9,146.6,542.9,146.6,542.8z"/>
			<path id="Eyebrow-32_1_" d="M154.3,542.8c-0.4-0.5-1.4-0.8-2.3-0.6c-0.1,0-0.2,0-0.2-0.1c0-0.1,0-0.2,0.1-0.2l0,0
				c1-0.3,2.2,0.1,2.7,0.7c0.1,0.1,0,0.2,0,0.3l0,0C154.4,542.9,154.3,542.9,154.3,542.8z"/>
			<g>
				<g>
					<g>
						<defs>
							<path id="SVGID_66_" d="M150.5,554.1c-0.4,0-0.9-0.1-1.3-0.2c-0.4-0.1-0.7-0.3-1-0.5c-0.3,0-0.6-0.1-0.8-0.2
								c-0.3-0.1-0.5-0.3-0.7-0.4c-0.1-0.1-0.2-0.2-0.2-0.4c-0.1-0.1-0.1-0.2-0.2-0.3c0,0-0.1-0.1-0.1-0.1c-0.2-0.1-0.3-0.3-0.4-0.5
								c-0.2-0.3-0.3-0.7-0.4-1.1c-0.1-0.4-0.2-0.7-0.2-1.1c0-0.2,0-0.3,0-0.5c0-0.3,0-0.5,0-0.7c-0.1-0.3-0.1-0.6-0.2-0.8
								c-0.1-0.6-0.2-1.1-0.1-1.7c0-0.5,0.1-0.9,0.2-1.4c0-0.1,0-0.2,0-0.3c0,0,0-0.1,0-0.2c0.1-0.3,0.2-1,0.2-1
								c0,0.8,0.1,1.6,0.2,2.6c0,0,0,0.1,0,0.1c0,0.2,0.1,0.5,0.1,0.7c0,0.1,0,0.2,0,0.2c0,0.1,0,0.3,0,0.4c0.1,0.2,0.4,0.5,0.6,0.6
								c0.1,0,0.1,0,0.2,0c0.3,0,0.6-0.3,0.9-0.6c0.2-0.2,0.4-0.3,0.6-0.4c0.4-0.2,0.9-0.3,1.3-0.3c0.3,0,0.8,0.1,1.3,0.3
								c0.2-0.1,0.4-0.2,0.6-0.2c0.2-0.1,0.4-0.1,0.7-0.1c0.5,0,0.9,0.1,1.3,0.3c0.2,0.1,0.4,0.3,0.6,0.4c0.3,0.3,0.6,0.6,0.9,0.6
								c0.1,0,0.1,0,0.2,0c0.3-0.1,0.6-0.3,0.6-0.6c0-0.1,0-0.3,0-0.4c0-0.1,0-0.2,0-0.2c0-0.2,0-0.5,0.1-0.7l0,0c0,0,0-0.1,0-0.1
								c0.1-1,0.2-1.8,0.2-2.6c0.1,0,0.2,0.7,0.2,1c0,0.1,0,0.1,0,0.2c0,0.1,0,0.2,0,0.3c0.1,0.5,0.2,0.9,0.2,1.4
								c0,0.6,0,1.2-0.1,1.7c-0.1,0.2-0.1,0.5-0.2,0.8c0,0.2,0,0.4,0,0.7c0,0.2,0,0.3,0,0.5c-0.1,0.4-0.1,0.8-0.2,1.1
								c-0.1,0.4-0.2,0.7-0.4,1.1c-0.1,0.2-0.3,0.3-0.4,0.5c0,0-0.1,0.1-0.1,0.1c-0.1,0.1-0.2,0.2-0.2,0.3c-0.1,0.1-0.1,0.2-0.2,0.4
								c-0.2,0.2-0.4,0.3-0.7,0.4c-0.3,0.1-0.5,0.2-0.8,0.2c-0.3,0.2-0.6,0.4-1,0.5C151.4,554,150.9,554.1,150.5,554.1z
								 M150.5,547.1c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0.1-0.4,0.1-0.8,0.2c-0.4,0.1-0.8,0.1-0.9,0.2c-0.2,0.2-0.4,0.5-0.3,0.8
								c0,0.3,0.1,0.5,0.3,0.7c0.2,0.3,0.6,0.5,1,0.5c0.5-0.5,1.2-0.5,1.7,0c0,0,0,0,0,0c0.4,0,0.7-0.2,1-0.5
								c0.2-0.2,0.3-0.4,0.3-0.7c0-0.3-0.1-0.6-0.3-0.8c-0.1-0.1-0.5-0.1-0.9-0.2c-0.3-0.1-0.7-0.1-0.8-0.2
								C150.6,547.2,150.5,547.2,150.5,547.1z"/>
						</defs>
						<clipPath id="SVGID_67_">
							<use xlink:href="#SVGID_66_"  style="overflow:visible;"/>
						</clipPath>
						<rect id="Color-29_1_" x="142.5" y="540.2" class="st47" width="15.9" height="14.4"/>
					</g>
				</g>
			</g>
			<path id="Left-3_1_" class="st15" d="M147.5,546.1h-0.3c-1.3,0-2.1-0.7-2.1-1.9c0-1,0.2-1.9,2.2-1.9h0.3c2.1,0,2.2,1,2.2,1.9
				C149.9,545.3,148.9,546.1,147.5,546.1z M147.4,542.8c-1.8,0-1.8,0.6-1.8,1.5c0,0.7,0.3,1.5,1.7,1.5h0.3c1.2,0,2-0.6,2-1.5
				c0-0.8,0-1.5-1.8-1.5H147.4z"/>
			<path id="Right-3_1_" class="st15" d="M153.4,546.1h-0.3c-1.3,0-2.1-0.7-2.1-1.9c0-1,0.2-1.9,2.2-1.9h0.3c2.1,0,2.2,1,2.2,1.9
				C155.8,545.3,154.8,546.1,153.4,546.1z M153.2,542.8c-1.8,0-1.8,0.6-1.8,1.5c0,0.7,0.3,1.5,1.7,1.5h0.3c1.2,0,2-0.6,2-1.5
				c0-0.8,0-1.5-1.8-1.5H153.2z"/>
			<path id="Stuff-3_1_" class="st15" d="M144.6,542.8c0.1-0.1,0.8-0.5,2.8-0.5c1.7,0,2,0.2,2.6,0.4l0,0c0.1,0.1,0.3,0.1,0.5,0.1
				c0.1,0,0.3,0,0.4-0.1c0.6-0.3,1.2-0.5,2.6-0.5c2,0,2.7,0.5,2.8,0.5c0.2,0,0.3,0.1,0.3,0.3l0,0v0.3c0,0.2-0.1,0.3-0.3,0.3l0,0
				c0,0-0.7,0-0.7,0.3c0,0.3-0.2-0.4-0.2-0.5V543c-0.6-0.3-1.3-0.4-2-0.4c-1.1,0-1.7,0.2-2.2,0.4l0,0v0.2l-0.2,0.5l-0.3-0.1
				c0,0-0.1,0-0.1,0c-0.2-0.1-0.4-0.1-0.6,0c-0.1,0-0.1,0-0.2,0.1l-0.3,0.1l-0.3-0.5l0-0.2l0,0c-0.4-0.2-0.8-0.4-2.2-0.4
				c-0.7,0-1.3,0.1-2,0.4v0.3c0,0.2-0.3,0.8-0.3,0.5c0-0.3-0.6-0.3-0.6-0.3c-0.2,0-0.3-0.1-0.3-0.3l0,0V543
				C144.3,542.9,144.4,542.8,144.6,542.8L144.6,542.8z"/>
			<g>
				<g>
					<g>
						<defs>
							<path id="SVGID_68_" d="M146.2,537c0.5-0.4,1.1-0.7,1.7-0.8c0.6-0.1,1-0.2,1.6,0c0.2,0,0.2,0.1,0.3,0
								c0.1-0.1,0.9-0.9,3.4-0.4c2.5,0.5,3.2,4.2,4.1,4.3c0.3,0.1,0.7,0,0.7-0.2c0.4,0.6,0.5,1.3,0.3,2c-0.1,0.6-0.4,1.1-0.9,1.4
								c-0.5,0.3-1,0.4-1.6,0.3c-0.2,0-0.5-0.1-0.7-0.2c-0.3-0.1-0.5-0.3-0.7-0.4c0.3,0.2,0.6,0.4,0.9,0.6c0.2,0.1,0.3,0.1,0.5,0.2
								c0.1,0,0.4,0.1,0.5,0.1c-0.7,0.1-1.5,0-2.1-0.3c-0.3-0.1-0.6-0.3-0.9-0.5c-0.3-0.2-0.6-0.4-0.8-0.7c0.1,0.1-0.1-0.1-0.1-0.1
								c-0.1-0.1-0.1-0.1-0.2-0.2c-0.1-0.1-0.2-0.2-0.3-0.3c-0.2-0.2-0.4-0.5-0.5-0.7c-0.2-0.3-0.8-1.3-1-1.6
								c0.2,0.5,0.4,1.1,0.6,1.6c-0.4-0.1-0.9-0.5-1.2-0.9c-0.3-0.4-0.5-0.9-0.6-1.4c-0.4,1-1.2,1.8-2.1,2.3c0.2-0.4,0.5-0.7,0.7-1
								c-0.9,0.9-2.3,1.3-2.7,2.6c-0.1-0.5-0.4-0.8-0.5-1.3c-0.1-0.5-0.2-1-0.2-1.6C144.5,538.7,145.3,537.7,146.2,537z"/>
						</defs>
						<clipPath id="SVGID_69_">
							<use xlink:href="#SVGID_68_"  style="overflow:visible;"/>
						</clipPath>
						<rect id="Color-30_1_" x="141.9" y="534.3" class="st48" width="17.1" height="12.6"/>
					</g>
				</g>
			</g>
		</g>
	</g>
</g>
<g>
	<path class="st3" d="M892.6,247.7h-83.5c-9.1,0-16.6-7.4-16.6-16.6v0c0-9.1,7.4-16.6,16.6-16.6h83.5c9.1,0,16.6,7.4,16.6,16.6v0
		C909.2,240.3,901.7,247.7,892.6,247.7z"/>
	<path class="st40" d="M892.6,247.7h-83.5c-9.1,0-16.6-7.4-16.6-16.6v0c0-9.1,7.4-16.6,16.6-16.6h83.5c9.1,0,16.6,7.4,16.6,16.6v0
		C909.2,240.3,901.7,247.7,892.6,247.7z"/>
	<path class="st5" d="M893.5,233.7h-59.6c-1.4,0-2.5-1.1-2.5-2.5v0c0-1.4,1.1-2.5,2.5-2.5h59.6c1.4,0,2.5,1.1,2.5,2.5v0
		C896,232.5,894.9,233.7,893.5,233.7z"/>
	<circle id="SVGID_9_" class="st3" cx="809.4" cy="231.2" r="13.6"/>
	<g>
		<defs>
			<circle id="SVGID_70_" cx="809.4" cy="231.2" r="13.6"/>
		</defs>
		<clipPath id="SVGID_71_">
			<use xlink:href="#SVGID_70_"  style="overflow:visible;"/>
		</clipPath>
		<g class="st49">
			<g>
				<g>
					<g>
						<defs>
							<path id="SVGID_72_" d="M799.9,244.8V244c0-3.8,3.1-6.8,6.8-6.8h0.4v-1.7c-1.7-0.8-2.8-2.4-3-4.2c-0.6-0.1-1-0.6-1-1.1v-1.3
								c0-0.6,0.4-1,0.9-1.1V227c0-2.9,2.4-5.3,5.3-5.3s5.3,2.4,5.3,5.3v0.6c0.5,0.1,0.9,0.6,0.9,1.1v1.3c0,0.6-0.4,1-1,1.1
								c-0.2,1.8-1.3,3.4-3,4.2v1.7h0.4c3.8,0,6.8,3.1,6.8,6.8v0.9H799.9L799.9,244.8z"/>
						</defs>
						<clipPath id="SVGID_73_">
							<use xlink:href="#SVGID_72_"  style="overflow:visible;"/>
						</clipPath>
						<g id="Body-64_9_" class="st50">
							<rect id="Color-28_9_" x="800.5" y="221.7" class="st8" width="18.3" height="19.4"/>
							<path id="Neck_Shadow-16_9_" d="M809.4,236.7c-1.4,0-2.8-0.6-3.8-1.6c-1-1-1.6-2.3-1.6-3.8v-0.7c0,2.9,2.4,5.3,5.3,5.3
								s5.3-2.4,5.3-5.3v0.8c0,2.1-1.3,4.1-3.2,4.9C810.8,236.6,810.1,236.7,809.4,236.7z"/>
						</g>
					</g>
				</g>
			</g>
			<path id="Clothes-28_9_" class="st9" d="M818.8,244.8h-19V244c0-3.6,2.8-6.6,6.4-6.8c0,0.1,0,0.2,0,0.3c0,0.5,0.3,1.1,0.9,1.4
				c0.6,0.4,1.4,0.6,2.2,0.6c0.8,0,1.6-0.2,2.2-0.6c0.6-0.4,0.9-0.9,0.9-1.4c0-0.1,0-0.2,0-0.3c3.6,0.3,6.3,3.2,6.3,6.8V244.8
				L818.8,244.8z"/>
			<g>
				<g>
					<g>
						<defs>
							<path id="SVGID_74_" d="M818.8,244.8h-19V244c0-3.6,2.8-6.6,6.4-6.8c0,0.1,0,0.2,0,0.3c0,0.5,0.3,1.1,0.9,1.4
								c0.6,0.4,1.4,0.6,2.2,0.6c0.8,0,1.6-0.2,2.2-0.6c0.6-0.4,0.9-0.9,0.9-1.4c0-0.1,0-0.2,0-0.3c3.6,0.3,6.3,3.2,6.3,6.8V244.8
								L818.8,244.8z"/>
						</defs>
						<clipPath id="SVGID_75_">
							<use xlink:href="#SVGID_74_"  style="overflow:visible;"/>
						</clipPath>
						<rect id="_Color-19_9_" x="796.8" y="234.4" class="st51" width="25" height="10.4"/>
					</g>
				</g>
			</g>
			<path id="Mouth-23_9_" d="M807.6,232.4c0.1,0.9,0.9,1.6,1.8,1.6c0.9,0,1.7-0.7,1.8-1.6c0-0.1-0.1-0.2-0.2-0.2h-3.2
				C807.6,232.2,807.6,232.3,807.6,232.4z"/>
			<g>
				<g>
					<g>
						<defs>
							<path id="SVGID_76_" d="M807.6,232.4c0.1,0.9,0.9,1.6,1.8,1.6c0.9,0,1.7-0.7,1.8-1.6c0-0.1-0.1-0.2-0.2-0.2h-3.2
								C807.6,232.2,807.6,232.3,807.6,232.4z"/>
						</defs>
						<clipPath id="SVGID_77_">
							<use xlink:href="#SVGID_76_"  style="overflow:visible;"/>
						</clipPath>
						<path id="Teeth-6_9_" class="st52" d="M808.4,231.2h2c0.3,0,0.5,0.2,0.5,0.5v0.6c0,0.3-0.2,0.5-0.5,0.5h-2
							c-0.3,0-0.5-0.2-0.5-0.5v-0.6C807.9,231.4,808.1,231.2,808.4,231.2z"/>
					</g>
				</g>
				<g>
					<g>
						<defs>
							<path id="SVGID_78_" d="M807.6,232.4c0.1,0.9,0.9,1.6,1.8,1.6c0.9,0,1.7-0.7,1.8-1.6c0-0.1-0.1-0.2-0.2-0.2h-3.2
								C807.6,232.2,807.6,232.3,807.6,232.4z"/>
						</defs>
						<clipPath id="SVGID_79_">
							<use xlink:href="#SVGID_78_"  style="overflow:visible;"/>
						</clipPath>
						<circle id="Tongue-11_9_" class="st53" cx="808.9" cy="234.3" r="1"/>
					</g>
				</g>
				<g>
					<g>
						<defs>
							<path id="SVGID_80_" d="M807.6,232.4c0.1,0.9,0.9,1.6,1.8,1.6c0.9,0,1.7-0.7,1.8-1.6c0-0.1-0.1-0.2-0.2-0.2h-3.2
								C807.6,232.2,807.6,232.3,807.6,232.4z"/>
						</defs>
						<clipPath id="SVGID_81_">
							<use xlink:href="#SVGID_80_"  style="overflow:visible;"/>
						</clipPath>
						<circle id="Tongue-12_9_" class="st54" cx="809.8" cy="234.3" r="1"/>
					</g>
				</g>
			</g>
			<path id="Nose-16_9_" d="M808.2,230.6c0,0.4,0.5,0.8,1.1,0.8l0,0c0.6,0,1.1-0.3,1.1-0.8"/>
			<circle id="Eye-21_9_" cx="806.9" cy="228.9" r="0.6"/>
			<circle id="Eye-22_9_" cx="811.8" cy="228.9" r="0.6"/>
			<path id="Eyebrow-31_9_" d="M805.5,227.7c0.4-0.5,1.4-0.8,2.3-0.6c0.1,0,0.2,0,0.2-0.1s0-0.2-0.1-0.2c-1-0.3-2.2,0.1-2.7,0.7
				c-0.1,0.1,0,0.2,0.1,0.3C805.4,227.8,805.5,227.8,805.5,227.7z"/>
			<path id="Eyebrow-32_9_" d="M813.2,227.7c-0.4-0.5-1.4-0.8-2.3-0.6c-0.1,0-0.2,0-0.2-0.1s0-0.2,0.1-0.2l0,0
				c1-0.3,2.2,0.1,2.7,0.7c0.1,0.1,0,0.2,0,0.3l0,0C813.4,227.8,813.2,227.8,813.2,227.7z"/>
			<g>
				<g>
					<g>
						<defs>
							<path id="SVGID_82_" d="M809.4,239c-0.4,0-0.9-0.1-1.3-0.2c-0.4-0.1-0.7-0.3-1-0.5c-0.3,0-0.6-0.1-0.8-0.2
								c-0.3-0.1-0.5-0.3-0.7-0.4c-0.1-0.1-0.2-0.2-0.2-0.4c-0.1-0.1-0.1-0.2-0.2-0.3c0,0-0.1-0.1-0.1-0.1c-0.2-0.1-0.3-0.3-0.4-0.5
								c-0.2-0.3-0.3-0.7-0.4-1.1c-0.1-0.4-0.2-0.7-0.2-1.1c0-0.2,0-0.3,0-0.5c0-0.3,0-0.5,0-0.7c-0.1-0.3-0.1-0.6-0.2-0.8
								c-0.1-0.6-0.2-1.1-0.1-1.7c0-0.5,0.1-0.9,0.2-1.4c0-0.1,0-0.2,0-0.3c0,0,0-0.1,0-0.2c0.1-0.3,0.2-1,0.2-1
								c0,0.8,0.1,1.6,0.2,2.6c0,0,0,0.1,0,0.1c0,0.2,0.1,0.5,0.1,0.7c0,0.1,0,0.2,0,0.2c0,0.1,0,0.3,0,0.4c0.1,0.2,0.4,0.5,0.6,0.6
								c0.1,0,0.1,0,0.2,0c0.3,0,0.6-0.3,0.9-0.6c0.2-0.2,0.4-0.3,0.6-0.4c0.4-0.2,0.9-0.3,1.3-0.3c0.3,0,0.8,0.1,1.3,0.3
								c0.2-0.1,0.4-0.2,0.6-0.2c0.2-0.1,0.4-0.1,0.7-0.1c0.5,0,0.9,0.1,1.3,0.3c0.2,0.1,0.4,0.3,0.6,0.4c0.3,0.3,0.6,0.6,0.9,0.6
								c0.1,0,0.1,0,0.2,0c0.3-0.1,0.6-0.3,0.6-0.6c0-0.1,0-0.3,0-0.4c0-0.1,0-0.2,0-0.2c0-0.2,0-0.5,0.1-0.7l0,0c0,0,0-0.1,0-0.1
								c0.1-1,0.2-1.8,0.2-2.6c0.1,0,0.2,0.7,0.2,1c0,0.1,0,0.1,0,0.2c0,0.1,0,0.2,0,0.3c0.1,0.5,0.2,0.9,0.2,1.4
								c0,0.6,0,1.2-0.1,1.7c-0.1,0.2-0.1,0.5-0.2,0.8c0,0.2,0,0.4,0,0.7c0,0.2,0,0.3,0,0.5c-0.1,0.4-0.1,0.8-0.2,1.1
								c-0.1,0.4-0.2,0.7-0.4,1.1c-0.1,0.2-0.3,0.3-0.4,0.5c0,0-0.1,0.1-0.1,0.1c-0.1,0.1-0.2,0.2-0.2,0.3c-0.1,0.1-0.1,0.2-0.2,0.4
								c-0.2,0.2-0.4,0.3-0.7,0.4c-0.3,0.1-0.5,0.2-0.8,0.2c-0.3,0.2-0.6,0.4-1,0.5C810.3,238.9,809.8,239,809.4,239z M809.4,232
								c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0.1-0.4,0.1-0.8,0.2c-0.4,0.1-0.8,0.1-0.9,0.2c-0.2,0.2-0.4,0.5-0.3,0.8
								c0,0.3,0.1,0.5,0.3,0.7c0.2,0.3,0.6,0.5,1,0.5c0.5-0.5,1.2-0.5,1.7,0c0,0,0,0,0,0c0.4,0,0.7-0.2,1-0.5
								c0.2-0.2,0.3-0.4,0.3-0.7c0-0.3-0.1-0.6-0.3-0.8c-0.1-0.1-0.5-0.1-0.9-0.2c-0.3-0.1-0.7-0.1-0.8-0.2
								C809.5,232.1,809.4,232.1,809.4,232z"/>
						</defs>
						<clipPath id="SVGID_83_">
							<use xlink:href="#SVGID_82_"  style="overflow:visible;"/>
						</clipPath>
						<rect id="Color-29_9_" x="801.4" y="225.1" class="st55" width="15.9" height="14.4"/>
					</g>
				</g>
			</g>
			<path id="Left-3_9_" class="st15" d="M806.4,231h-0.3c-1.3,0-2.1-0.7-2.1-1.9c0-1,0.2-1.9,2.2-1.9h0.3c2.1,0,2.2,1,2.2,1.9
				C808.8,230.2,807.8,231,806.4,231z M806.3,227.7c-1.8,0-1.8,0.6-1.8,1.5c0,0.7,0.3,1.5,1.7,1.5h0.3c1.2,0,2-0.6,2-1.5
				c0-0.8,0-1.5-1.8-1.5H806.3z"/>
			<path id="Right-3_9_" class="st15" d="M812.3,231H812c-1.3,0-2.1-0.7-2.1-1.9c0-1,0.2-1.9,2.2-1.9h0.3c2.1,0,2.2,1,2.2,1.9
				C814.7,230.2,813.7,231,812.3,231z M812.1,227.7c-1.8,0-1.8,0.6-1.8,1.5c0,0.7,0.3,1.5,1.7,1.5h0.3c1.2,0,2-0.6,2-1.5
				c0-0.8,0-1.5-1.8-1.5H812.1z"/>
			<path id="Stuff-3_9_" class="st15" d="M803.5,227.7c0.1-0.1,0.8-0.5,2.8-0.5c1.7,0,2,0.2,2.6,0.4l0,0c0.1,0.1,0.3,0.1,0.5,0.1
				c0.1,0,0.3,0,0.4-0.1c0.6-0.3,1.2-0.5,2.6-0.5c2,0,2.7,0.5,2.8,0.5c0.2,0,0.3,0.1,0.3,0.3l0,0v0.3c0,0.2-0.1,0.3-0.3,0.3l0,0
				c0,0-0.7,0-0.7,0.3s-0.2-0.4-0.2-0.5v-0.3c-0.6-0.3-1.3-0.4-2-0.4c-1.1,0-1.7,0.2-2.2,0.4l0,0v0.2l-0.2,0.5l-0.3-0.1
				c0,0-0.1,0-0.1,0c-0.2-0.1-0.4-0.1-0.6,0c-0.1,0-0.1,0-0.2,0.1l-0.3,0.1l-0.3-0.5l0-0.2l0,0c-0.4-0.2-0.8-0.4-2.2-0.4
				c-0.7,0-1.3,0.1-2,0.4v0.3c0,0.2-0.3,0.8-0.3,0.5s-0.6-0.3-0.6-0.3c-0.2,0-0.3-0.1-0.3-0.3l0,0v-0.3
				C803.2,227.8,803.3,227.7,803.5,227.7L803.5,227.7z"/>
			<g>
				<g>
					<g>
						<defs>
							<path id="SVGID_84_" d="M805.1,221.9c0.5-0.4,1.1-0.7,1.7-0.8c0.6-0.1,1-0.2,1.6,0c0.2,0,0.2,0.1,0.3,0
								c0.1-0.1,0.9-0.9,3.4-0.4c2.5,0.5,3.2,4.2,4.1,4.3c0.3,0.1,0.7,0,0.7-0.2c0.4,0.6,0.5,1.3,0.3,2c-0.1,0.6-0.4,1.1-0.9,1.4
								c-0.5,0.3-1,0.4-1.6,0.3c-0.2,0-0.5-0.1-0.7-0.2c-0.3-0.1-0.5-0.3-0.7-0.4c0.3,0.2,0.6,0.4,0.9,0.6c0.2,0.1,0.3,0.1,0.5,0.2
								c0.1,0,0.4,0.1,0.5,0.1c-0.7,0.1-1.5,0-2.1-0.3c-0.3-0.1-0.6-0.3-0.9-0.5c-0.3-0.2-0.6-0.4-0.8-0.7c0.1,0.1-0.1-0.1-0.1-0.1
								c-0.1-0.1-0.1-0.1-0.2-0.2c-0.1-0.1-0.2-0.2-0.3-0.3c-0.2-0.2-0.4-0.5-0.5-0.7c-0.2-0.3-0.8-1.3-1-1.6
								c0.2,0.5,0.4,1.1,0.6,1.6c-0.4-0.1-0.9-0.5-1.2-0.9c-0.3-0.4-0.5-0.9-0.6-1.4c-0.4,1-1.2,1.8-2.1,2.3c0.2-0.4,0.5-0.7,0.7-1
								c-0.9,0.9-2.3,1.3-2.7,2.6c-0.1-0.5-0.4-0.8-0.5-1.3c-0.1-0.5-0.2-1-0.2-1.6C803.4,223.7,804.2,222.6,805.1,221.9z"/>
						</defs>
						<clipPath id="SVGID_85_">
							<use xlink:href="#SVGID_84_"  style="overflow:visible;"/>
						</clipPath>
						<rect id="Color-30_9_" x="800.8" y="219.2" class="st56" width="17.1" height="12.6"/>
					</g>
				</g>
			</g>
		</g>
	</g>
</g>
<g>
	<path class="st3" d="M477.4,169.6H295.3c-9.1,0-16.6-7.4-16.6-16.6v0c0-9.1,7.4-16.6,16.6-16.6h182.1c9.1,0,16.6,7.4,16.6,16.6v0
		C494,162.2,486.6,169.6,477.4,169.6z"/>
	<path class="st57" d="M477.4,169.6H295.3c-9.1,0-16.6-7.4-16.6-16.6v0c0-9.1,7.4-16.6,16.6-16.6h182.1c9.1,0,16.6,7.4,16.6,16.6v0
		C494,162.2,486.6,169.6,477.4,169.6z"/>
	<path class="st5" d="M476.5,155.6H325.6c-1.4,0-2.5-1.1-2.5-2.5l0,0c0-1.4,1.1-2.5,2.5-2.5h150.9c1.4,0,2.5,1.1,2.5,2.5l0,0
		C479,154.4,477.9,155.6,476.5,155.6z"/>
	<circle id="SVGID_3_" class="st3" cx="295.6" cy="153.1" r="13.6"/>
	<g>
		<circle id="SVGID_2_" class="st3" cx="295.6" cy="153.1" r="13.6"/>
		<g>
			<g>
				<defs>
					<circle id="SVGID_86_" cx="295.6" cy="153.1" r="13.6"/>
				</defs>
				<clipPath id="SVGID_87_">
					<use xlink:href="#SVGID_86_"  style="overflow:visible;"/>
				</clipPath>
				<g class="st58">
					<g>
						<g>
							<g>
								<defs>
									<path id="SVGID_88_" d="M286.5,166.8v-0.8c0-3.6,2.9-6.5,6.5-6.5h0.4v-1.7c-1.6-0.8-2.7-2.3-2.9-4.1
										c-0.5-0.1-0.9-0.5-0.9-1.1v-1.3c0-0.5,0.4-1,0.9-1.1v-0.6c0-2.8,2.3-5.1,5.1-5.1c2.8,0,5.1,2.3,5.1,5.1v0.6
										c0.5,0.1,0.9,0.5,0.9,1.1v1.3c0,0.5-0.4,1-0.9,1.1c-0.2,1.8-1.3,3.3-2.9,4.1v1.7h0.4c3.6,0,6.5,2.9,6.5,6.5v0.8H286.5z"/>
								</defs>
								<clipPath id="SVGID_89_">
									<use xlink:href="#SVGID_88_"  style="overflow:visible;"/>
								</clipPath>
								<g id="Body-22" class="st59">
									<rect id="Color-3" x="283.6" y="144.6" class="st20" width="24" height="22.2"/>
									<path id="Neck_Shadow-2" d="M295.6,159c-1.4,0-2.6-0.5-3.6-1.5c-1-1-1.5-2.2-1.5-3.6v-0.7c0,2.8,2.3,5.1,5.1,5.1
										c2.8,0,5.1-2.3,5.1-5.1v0.7c0,2-1.2,3.9-3.1,4.7C296.9,158.8,296.2,159,295.6,159z"/>
								</g>
							</g>
						</g>
					</g>
					<g>
						<g>
							<g>
								<defs>
									<path id="SVGID_90_" d="M304.6,166.8h-18.2v-0.8c0-2.9,1.9-5.4,4.7-6.3c0,0.1-0.1,0.3-0.1,0.4c0,0.7,0.5,1.4,1.3,1.9
										c0.8,0.5,2,0.8,3.2,0.8c1.2,0,2.3-0.3,3.2-0.8c0.8-0.5,1.3-1.2,1.3-1.9c0-0.1,0-0.2,0-0.4c2.7,0.9,4.6,3.4,4.6,6.2V166.8z"
										/>
								</defs>
								<clipPath id="SVGID_91_">
									<use xlink:href="#SVGID_90_"  style="overflow:visible;"/>
								</clipPath>
								<rect id="_Color-3" x="283.6" y="156.8" class="st60" width="24" height="10"/>
							</g>
						</g>
					</g>
					<g id="_Mouth-2" transform="translate(2 52)">
						<path id="Mouth-9" d="M291.9,102.9c0.1,0.9,0.8,1.5,1.7,1.5c0.9,0,1.6-0.7,1.7-1.5c0-0.1-0.1-0.2-0.2-0.2H292
							C291.9,102.7,291.9,102.8,291.9,102.9z"/>
						<g>
							<g>
								<g>
									<defs>
										<path id="SVGID_92_" d="M291.9,102.9c0.1,0.9,0.8,1.5,1.7,1.5c0.9,0,1.6-0.7,1.7-1.5c0-0.1-0.1-0.2-0.2-0.2H292
											C291.9,102.7,291.9,102.8,291.9,102.9z"/>
									</defs>
									<clipPath id="SVGID_93_">
										<use xlink:href="#SVGID_92_"  style="overflow:visible;"/>
									</clipPath>
									<path id="Teeth" class="st61" d="M292.7,101.7h1.9c0.3,0,0.5,0.2,0.5,0.5v0.5c0,0.3-0.2,0.5-0.5,0.5h-1.9
										c-0.3,0-0.5-0.2-0.5-0.5v-0.5C292.2,101.9,292.4,101.7,292.7,101.7z"/>
								</g>
							</g>
							<g>
								<g>
									<defs>
										<path id="SVGID_94_" d="M291.9,102.9c0.1,0.9,0.8,1.5,1.7,1.5c0.9,0,1.6-0.7,1.7-1.5c0-0.1-0.1-0.2-0.2-0.2H292
											C291.9,102.7,291.9,102.8,291.9,102.9z"/>
									</defs>
									<clipPath id="SVGID_95_">
										<use xlink:href="#SVGID_94_"  style="overflow:visible;"/>
									</clipPath>
									<circle id="Tongue-2" class="st62" cx="293.1" cy="104.7" r="1"/>
								</g>
							</g>
							<g>
								<g>
									<defs>
										<path id="SVGID_96_" d="M291.9,102.9c0.1,0.9,0.8,1.5,1.7,1.5c0.9,0,1.6-0.7,1.7-1.5c0-0.1-0.1-0.2-0.2-0.2H292
											C291.9,102.7,291.9,102.8,291.9,102.9z"/>
									</defs>
									<clipPath id="SVGID_97_">
										<use xlink:href="#SVGID_96_"  style="overflow:visible;"/>
									</clipPath>
									<circle id="Tongue-3" class="st63" cx="294" cy="104.7" r="1"/>
								</g>
							</g>
						</g>
					</g>
					<path id="Nose-2" d="M294.5,153.1c0,0.4,0.5,0.7,1.1,0.7l0,0c0.6,0,1.1-0.3,1.1-0.7"/>
					<circle id="Eye" cx="293.2" cy="151.5" r="0.5"/>
					<circle id="Eye-2" cx="297.9" cy="151.5" r="0.5"/>
					<path id="Eyebrow-3" d="M291.9,150.3c0.4-0.5,1.3-0.8,2.2-0.6c0.1,0,0.2,0,0.2-0.1c0-0.1,0-0.2-0.1-0.2c-1-0.2-2.1,0.1-2.6,0.7
						c-0.1,0.1,0,0.2,0,0.3C291.7,150.4,291.8,150.4,291.9,150.3z"/>
					<path id="Eyebrow-4" d="M299.2,150.3c-0.4-0.5-1.3-0.8-2.2-0.6c-0.1,0-0.2,0-0.2-0.1c0-0.1,0-0.2,0.1-0.2l0,0
						c1-0.2,2.1,0.1,2.6,0.7c0.1,0.1,0,0.2,0,0.3l0,0C299.4,150.4,299.3,150.4,299.2,150.3z"/>
					<g>
						<g>
							<g>
								<defs>
									<path id="SVGID_98_" d="M307.6,166.8h-2.9v-0.8c0-3.6-2.9-6.5-6.5-6.5h-0.4v-1.7c1.6-0.8,2.7-2.4,2.9-4.2
										c0.5-0.1,0.9-0.5,0.9-1.1v-1.2c0-0.5-0.4-1-0.9-1.1v-0.6c0-0.7-0.1-1.4-0.4-2c-0.2-0.4-0.8-0.6-1.7-0.8
										c-1-0.2-2-0.3-2.9-0.3c-1,0-2,0.1-3,0.3c-0.9,0.2-1.5,0.5-1.7,0.8c-0.3,0.6-0.4,1.3-0.4,2v0.6c-0.5,0.1-0.9,0.5-0.9,1.1
										v1.2c0,0.5,0.4,1,0.9,1.1c0.2,1.8,1.3,3.4,2.9,4.2v1.7H293c-3.6,0-6.5,2.9-6.5,6.5v0.8h-2.9v-25.4h24V166.8z"/>
								</defs>
								<clipPath id="SVGID_99_">
									<use xlink:href="#SVGID_98_"  style="overflow:visible;"/>
								</clipPath>
								<path id="Hat-3" class="st64" d="M291.2,144.4c0.4-1.7,1.8-2.9,3.6-2.9h1.7c1.7,0,3.2,1.2,3.6,2.9l0.8,3.8h-10.4
									L291.2,144.4z"/>
							</g>
						</g>
						<g>
							<g>
								<defs>
									<path id="SVGID_100_" d="M307.6,166.8h-2.9v-0.8c0-3.6-2.9-6.5-6.5-6.5h-0.4v-1.7c1.6-0.8,2.7-2.4,2.9-4.2
										c0.5-0.1,0.9-0.5,0.9-1.1v-1.2c0-0.5-0.4-1-0.9-1.1v-0.6c0-0.7-0.1-1.4-0.4-2c-0.2-0.4-0.8-0.6-1.7-0.8
										c-1-0.2-2-0.3-2.9-0.3c-1,0-2,0.1-3,0.3c-0.9,0.2-1.5,0.5-1.7,0.8c-0.3,0.6-0.4,1.3-0.4,2v0.6c-0.5,0.1-0.9,0.5-0.9,1.1
										v1.2c0,0.5,0.4,1,0.9,1.1c0.2,1.8,1.3,3.4,2.9,4.2v1.7H293c-3.6,0-6.5,2.9-6.5,6.5v0.8h-2.9v-25.4h24V166.8z"/>
								</defs>
								<clipPath id="SVGID_101_">
									<use xlink:href="#SVGID_100_"  style="overflow:visible;"/>
								</clipPath>
								<ellipse id="Hipster" class="st65" cx="295.6" cy="149.3" rx="11.1" ry="5.2"/>
							</g>
						</g>
						<g>
							<g>
								<defs>
									<path id="SVGID_102_" d="M307.6,166.8h-2.9v-0.8c0-3.6-2.9-6.5-6.5-6.5h-0.4v-1.7c1.6-0.8,2.7-2.4,2.9-4.2
										c0.5-0.1,0.9-0.5,0.9-1.1v-1.2c0-0.5-0.4-1-0.9-1.1v-0.6c0-0.7-0.1-1.4-0.4-2c-0.2-0.4-0.8-0.6-1.7-0.8
										c-1-0.2-2-0.3-2.9-0.3c-1,0-2,0.1-3,0.3c-0.9,0.2-1.5,0.5-1.7,0.8c-0.3,0.6-0.4,1.3-0.4,2v0.6c-0.5,0.1-0.9,0.5-0.9,1.1
										v1.2c0,0.5,0.4,1,0.9,1.1c0.2,1.8,1.3,3.4,2.9,4.2v1.7H293c-3.6,0-6.5,2.9-6.5,6.5v0.8h-2.9v-25.4h24V166.8z"/>
								</defs>
								<clipPath id="SVGID_103_">
									<use xlink:href="#SVGID_102_"  style="overflow:visible;"/>
								</clipPath>
								<ellipse id="Very" class="st66" cx="295.6" cy="148.8" rx="5.6" ry="2.3"/>
							</g>
						</g>
					</g>
					<path id="Shade-2" d="M294.8,151.6c0,0.9-0.7,1.6-2,1.6h-0.3c-1.3,0-1.7-0.7-1.7-1.6l0,0c0-0.9,0.1-1.6,1.9-1.6h0.3
						C294.7,150,294.8,150.7,294.8,151.6z"/>
					
						<linearGradient id="Shade-3_3_" gradientUnits="userSpaceOnUse" x1="819.7923" y1="442.6394" x2="819.7923" y2="442.5486" gradientTransform="matrix(43.995 0 0 -35.142 -35773.9453 15705.1963)">
						<stop  offset="0" style="stop-color:#FFFFFF;stop-opacity:0.502"/>
						<stop  offset="0.705" style="stop-color:#000000;stop-opacity:0.502"/>
					</linearGradient>
					<path id="Shade-3" class="st67" d="M294.8,151.6c0,0.9-0.7,1.6-2,1.6h-0.3c-1.3,0-1.7-0.7-1.7-1.6l0,0c0-0.9,0.1-1.6,1.9-1.6
						h0.3C294.7,150,294.8,150.7,294.8,151.6z"/>
					<path id="Shade-5" d="M300.4,151.6c0,0.9-0.7,1.6-2,1.6h-0.3c-1.3,0-1.7-0.7-1.7-1.6l0,0c0-0.9,0.1-1.6,1.9-1.6h0.3
						C300.3,150,300.4,150.7,300.4,151.6z"/>
					
						<linearGradient id="Shade-6_3_" gradientUnits="userSpaceOnUse" x1="818.52" y1="442.6394" x2="818.52" y2="442.5486" gradientTransform="matrix(43.995 0 0 -35.142 -35712.375 15705.1963)">
						<stop  offset="0" style="stop-color:#FFFFFF;stop-opacity:0.502"/>
						<stop  offset="0.705" style="stop-color:#000000;stop-opacity:0.502"/>
					</linearGradient>
					<path id="Shade-6" class="st68" d="M300.4,151.6c0,0.9-0.7,1.6-2,1.6h-0.3c-1.3,0-1.7-0.7-1.7-1.6l0,0c0-0.9,0.1-1.6,1.9-1.6
						h0.3C300.3,150,300.4,150.7,300.4,151.6z"/>
					<path id="Left" class="st15" d="M292.8,153.4h-0.3c-1.3,0-2-0.7-2-1.9c0-0.9,0.2-1.9,2.1-1.9h0.3c2,0,2.1,0.9,2.1,1.9
						C295.1,152.7,294.1,153.4,292.8,153.4z M292.7,150.2c-1.6,0-1.6,0.6-1.6,1.3c0,0.6,0.3,1.3,1.5,1.3h0.3c1,0,1.7-0.5,1.7-1.3
						c0-0.7,0-1.3-1.6-1.3H292.7z"/>
					<path id="Right" class="st15" d="M298.4,153.4h-0.3c-1.3,0-2-0.7-2-1.9c0-0.9,0.2-1.9,2.1-1.9h0.3c2,0,2.1,0.9,2.1,1.9
						C300.7,152.7,299.7,153.4,298.4,153.4z M298.3,150.2c-1.6,0-1.6,0.6-1.6,1.3c0,0.6,0.3,1.3,1.5,1.3h0.3c1,0,1.7-0.5,1.7-1.3
						c0-0.7,0-1.3-1.6-1.3H298.3z"/>
					<path id="Stuff" class="st15" d="M290,150.2c0.1-0.1,0.7-0.5,2.7-0.5c1.6,0,1.9,0.2,2.5,0.4l0,0c0.1,0.1,0.3,0.1,0.4,0.1
						c0.1,0,0.3,0,0.4-0.1c0.6-0.3,1.2-0.5,2.5-0.5c1.9,0,2.6,0.5,2.7,0.5c0.1,0,0.3,0.1,0.3,0.3l0,0v0.3c0,0.1-0.1,0.3-0.3,0.3l0,0
						c0,0-0.5,0-0.5,0.3s-0.3-0.4-0.3-0.5v-0.3c-0.3-0.1-0.9-0.3-1.9-0.3c-1.1,0-1.6,0.1-2.1,0.3l0,0l-0.2,0.1l0.2,0.1l-0.2,0.5
						l-0.2-0.1c0,0-0.1,0-0.1,0c-0.2-0.1-0.4-0.1-0.5,0c-0.1,0-0.1,0-0.2,0.1l-0.2,0.1l-0.2-0.5l0.2-0.1l0,0l-0.2-0.1l0,0
						c-0.4-0.2-0.8-0.3-2.1-0.3c-1,0-1.5,0.1-1.9,0.3v0.3c0,0.1-0.3,0.8-0.3,0.5c0-0.3-0.5-0.3-0.5-0.3c-0.1,0-0.3-0.1-0.3-0.3v-0.3
						C289.8,150.3,289.9,150.2,290,150.2L290,150.2z"/>
				</g>
			</g>
		</g>
	</g>
</g>
<g>
	<path class="st3" d="M779.2,169.7H597.1c-9.1,0-16.6-7.4-16.6-16.6v0c0-9.1,7.4-16.6,16.6-16.6h182.1c9.1,0,16.6,7.4,16.6,16.6v0
		C795.7,162.3,788.3,169.7,779.2,169.7z"/>
	<path class="st69" d="M779.2,169.7H597.1c-9.1,0-16.6-7.4-16.6-16.6v0c0-9.1,7.4-16.6,16.6-16.6h182.1c9.1,0,16.6,7.4,16.6,16.6v0
		C795.7,162.3,788.3,169.7,779.2,169.7z"/>
	<path class="st5" d="M775.9,155.6H625.6c-1.4,0-2.5-1.1-2.5-2.5v0c0-1.4,1.1-2.5,2.5-2.5h150.3c1.4,0,2.5,1.1,2.5,2.5v0
		C778.4,154.5,777.3,155.6,775.9,155.6z"/>
	<circle id="SVGID_8_" class="st3" cx="597.3" cy="153.2" r="13.6"/>
	<g>
		<circle id="SVGID_10_" class="st3" cx="597.3" cy="153.1" r="13.6"/>
		<g>
			<defs>
				<circle id="SVGID_104_" cx="597.3" cy="153.1" r="13.6"/>
			</defs>
			<clipPath id="SVGID_105_">
				<use xlink:href="#SVGID_104_"  style="overflow:visible;"/>
			</clipPath>
			<g class="st70">
				<g>
					<g>
						<g>
							<defs>
								<path id="SVGID_106_" d="M589.2,166.7v-0.8c0-3.3,2.7-6,6-6h0.3v-1.5c-1.5-0.7-2.5-2.1-2.7-3.8c-0.5-0.1-0.9-0.5-0.9-1v-1.2
									c0-0.5,0.4-0.9,0.8-1v-0.5c0-2.6,2.1-4.7,4.7-4.7c2.6,0,4.7,2.1,4.7,4.7v0.5c0.5,0.1,0.8,0.5,0.8,1v1.2c0,0.5-0.4,0.9-0.9,1
									c-0.2,1.6-1.2,3.1-2.7,3.8v1.5h0.3c3.3,0,6,2.7,6,6v0.8L589.2,166.7L589.2,166.7z"/>
							</defs>
							<clipPath id="SVGID_107_">
								<use xlink:href="#SVGID_106_"  style="overflow:visible;"/>
							</clipPath>
							<g id="Body-49_1_" class="st71">
								<rect id="Color-19_1_" x="586.5" y="146.2" class="st32" width="22.2" height="20.5"/>
								<path id="Neck_Shadow-11_1_" d="M597.6,159.4c-1.2,0-2.4-0.5-3.3-1.4c-0.9-0.9-1.4-2.1-1.4-3.3v-0.6c0,2.6,2.1,4.7,4.7,4.7
									s4.7-2.1,4.7-4.7v0.7c0,1.9-1.1,3.6-2.9,4.3C598.8,159.3,598.2,159.4,597.6,159.4z"/>
							</g>
						</g>
					</g>
				</g>
				<path id="Hoodie-2_1_" class="st33" d="M606,166.7h-16.8v-0.8c0-2.4,1.5-4.6,3.7-5.6c0-0.3,0.1-0.5,0.2-0.8
					c0.1-0.2,0.3-0.4,0.6-0.5c0.4-0.3,1.1-0.4,1.9-0.5v1.6c0,1.1,0.9,2,2,2s2-0.9,2-2v-1.6c0.8,0.1,1.5,0.3,1.9,0.5
					c0.2,0.1,0.4,0.3,0.6,0.5c0.1,0.2,0.2,0.5,0.2,0.8c2.2,0.9,3.7,3.1,3.7,5.6V166.7z"/>
				<g>
					<g>
						<g>
							<defs>
								<path id="SVGID_108_" d="M606,166.7h-16.8v-0.8c0-2.4,1.5-4.6,3.7-5.6c0-0.3,0.1-0.5,0.2-0.8c0.1-0.2,0.3-0.4,0.6-0.5
									c0.4-0.3,1.1-0.4,1.9-0.5v1.6c0,1.1,0.9,2,2,2s2-0.9,2-2v-1.6c0.8,0.1,1.5,0.3,1.9,0.5c0.2,0.1,0.4,0.3,0.6,0.5
									c0.1,0.2,0.2,0.5,0.2,0.8c2.2,0.9,3.7,3.1,3.7,5.6V166.7z"/>
							</defs>
							<clipPath id="SVGID_109_">
								<use xlink:href="#SVGID_108_"  style="overflow:visible;"/>
							</clipPath>
							<g id="Mask_Group_5_1_" class="st72">
								<rect id="_Color-13_1_" x="586.5" y="157.4" class="st35" width="22.2" height="9.2"/>
								<path id="Straps_1_" class="st36" d="M595,166.7h-0.6v-4.4c0.2,0.1,0.4,0.2,0.6,0.3L595,166.7L595,166.7z M600.4,166
									c-0.2,0-0.3-0.1-0.3-0.3v-3.1c0.2-0.1,0.4-0.2,0.6-0.3v3.4C600.7,165.9,600.5,166,600.4,166z"/>
								<path id="Shadow-6_1_" d="M597.6,163.1L597.6,163.1c1.1,0,2.3-0.3,3.2-0.9c0.9-0.6,1.5-1.4,1.5-2.2c0-0.7-0.4-1.2-1.3-1.5
									c1.4,0.3,2.1,0.8,2.1,1.6c0,0.4-0.2,0.8-0.5,1.2c-0.4,0.4-0.8,0.7-1.3,1C600.4,162.8,599,163.1,597.6,163.1z M597.5,163.1
									c-1.3,0-2.7-0.3-3.8-0.9c-0.5-0.2-0.9-0.6-1.3-1c-0.3-0.4-0.5-0.8-0.5-1.2c0-0.8,0.7-1.4,2.1-1.6c-0.8,0.3-1.3,0.8-1.3,1.5
									c0,0.8,0.5,1.6,1.5,2.2C595.2,162.8,596.4,163.1,597.5,163.1L597.5,163.1z"/>
							</g>
						</g>
					</g>
				</g>
				<path id="Mouth-17_1_" d="M596.4,155.8c0,0.4,0.5,0.8,1.2,0.8s1.2-0.3,1.2-0.8c0-0.1-0.1-0.2-0.2-0.2c-0.1,0-0.2,0.1-0.2,0.2
					c0,0,0,0,0,0c-0.1,0.2-0.4,0.4-0.8,0.4c-0.5,0-0.7-0.2-0.8-0.4c0-0.1-0.1-0.2-0.2-0.2c0,0,0,0,0,0
					C596.5,155.6,596.4,155.7,596.4,155.8z"/>
				<path id="Nose-11_1_" d="M596.6,154.1c0,0.4,0.4,0.7,1,0.7l0,0c0.6,0,1-0.3,1-0.7"/>
				<path id="Closed_Eye-3_1_" d="M594.2,153c0.2,0.3,0.5,0.6,0.9,0.5c0.4,0,0.7-0.2,0.9-0.5c0-0.1,0-0.2-0.1-0.1
					c-0.2,0.2-0.5,0.3-0.8,0.3c-0.3,0-0.6-0.1-0.8-0.3C594.2,152.9,594.2,152.9,594.2,153z"/>
				<path id="Closed_Eye-4_1_" d="M599.1,153c0.2,0.3,0.5,0.6,0.9,0.5c0.4,0,0.7-0.2,0.9-0.5c0-0.1,0-0.2-0.1-0.1
					c-0.2,0.2-0.5,0.3-0.8,0.3c-0.3,0-0.6-0.1-0.8-0.3C599.1,152.9,599,152.9,599.1,153z"/>
				<path id="Eyebrow-21_1_" d="M594.2,151.5c0.3-0.5,1.2-0.7,2-0.5c0.1,0,0.2,0,0.2-0.1c0-0.1,0-0.2-0.1-0.2
					c-0.9-0.2-1.9,0.1-2.3,0.7c-0.1,0.1,0,0.2,0,0.2S594.1,151.6,594.2,151.5z"/>
				<path id="Eyebrow-22_1_" d="M601,151.5c-0.3-0.5-1.2-0.7-2-0.5c-0.1,0-0.2,0-0.2-0.1c0-0.1,0-0.2,0.1-0.2l0,0
					c0.9-0.2,1.9,0.1,2.4,0.7c0.1,0.1,0,0.2,0,0.2l0,0C601.1,151.6,601,151.6,601,151.5z"/>
				<path id="Hair-13_1_" class="st37" d="M599.6,159.4v-1.1c1.5-0.7,2.5-2.2,2.7-3.8c0.5-0.1,0.9-0.5,0.9-1v-1.1
					c0-0.5-0.4-0.9-0.8-1v-0.5c0-0.4-0.1-0.9-0.2-1.3l0.1,1.1l-1-2l-3.8-1.6l-2.5,0.9l-1.8,1.7l0-0.2c-0.2,0.5-0.3,1-0.3,1.5v0.5
					c-0.5,0.1-0.8,0.5-0.8,1v1.1c0,0.5,0.4,0.9,0.9,1c0.1,1.7,1.2,3.1,2.7,3.8v0.9c-1.1,0.5-2.3,0.3-3.2-0.4c-0.2,0-0.3,0-0.5,0
					c-1.7,0-3.3-1-4-2.5c-0.3-0.2-0.6-0.5-0.8-0.8c-0.2-0.4-0.3-0.7-0.3-1.1c0-0.2,0-0.3,0.1-0.5c-0.6-1.5-0.3-3.3,0.6-4.6
					c0-0.1,0-0.1,0-0.2c0-0.9,0.5-1.8,1.4-2.2c0.7-1.6,2.3-2.6,4-2.7c0.4-0.4,1-0.7,1.7-0.7c0.2,0,0.4,0,0.6,0.1
					c1.6-0.6,3.4-0.6,5,0c0.1,0,0.2,0,0.3,0c0.6,0,1.2,0.2,1.7,0.7c1.7,0.1,3.3,1.1,4,2.7c0.8,0.4,1.4,1.2,1.4,2.2
					c0,0.1,0,0.1,0,0.2c1,1.3,1.2,3,0.6,4.6c0.1,0.4,0.1,0.8,0,1.1c0,0.2-0.1,0.4-0.2,0.5c-0.2,0.3-0.4,0.6-0.8,0.8
					c-0.8,1.7-2.6,2.7-4.5,2.5C601.7,159.5,600.6,159.7,599.6,159.4z"/>
				<g>
					<g>
						<g>
							<defs>
								<path id="SVGID_110_" d="M599.6,159.4v-1.1c1.5-0.7,2.5-2.2,2.7-3.8c0.5-0.1,0.9-0.5,0.9-1v-1.1c0-0.5-0.4-0.9-0.8-1v-0.5
									c0-0.4-0.1-0.9-0.2-1.3l0.1,1.1l-1-2l-3.8-1.6l-2.5,0.9l-1.8,1.7l0-0.2c-0.2,0.5-0.3,1-0.3,1.5v0.5c-0.5,0.1-0.8,0.5-0.8,1
									v1.1c0,0.5,0.4,0.9,0.9,1c0.1,1.7,1.2,3.1,2.7,3.8v0.9c-1.1,0.5-2.3,0.3-3.2-0.4c-0.2,0-0.3,0-0.5,0c-1.7,0-3.3-1-4-2.5
									c-0.3-0.2-0.6-0.5-0.8-0.8c-0.2-0.4-0.3-0.7-0.3-1.1c0-0.2,0-0.3,0.1-0.5c-0.6-1.5-0.3-3.3,0.6-4.6c0-0.1,0-0.1,0-0.2
									c0-0.9,0.5-1.8,1.4-2.2c0.7-1.6,2.3-2.6,4-2.7c0.4-0.4,1-0.7,1.7-0.7c0.2,0,0.4,0,0.6,0.1c1.6-0.6,3.4-0.6,5,0
									c0.1,0,0.2,0,0.3,0c0.6,0,1.2,0.2,1.7,0.7c1.7,0.1,3.3,1.1,4,2.7c0.8,0.4,1.4,1.2,1.4,2.2c0,0.1,0,0.1,0,0.2
									c1,1.3,1.2,3,0.6,4.6c0.1,0.4,0.1,0.8,0,1.1c0,0.2-0.1,0.4-0.2,0.5c-0.2,0.3-0.4,0.6-0.8,0.8c-0.8,1.7-2.6,2.7-4.5,2.5
									C601.7,159.5,600.6,159.7,599.6,159.4z"/>
							</defs>
							<clipPath id="SVGID_111_">
								<use xlink:href="#SVGID_110_"  style="overflow:visible;"/>
							</clipPath>
							<rect id="Color-20_1_" x="586.5" y="143.2" class="st73" width="22.2" height="23.5"/>
						</g>
					</g>
				</g>
				<path id="Band-3_1_" class="st39" d="M592.8,151.5L592.8,151.5c0-0.2-0.1-0.4-0.1-0.7c0-0.6,0.1-1.2,0.4-1.7
					c0.2-0.5,0.6-1,1-1.4c0.4-0.4,1-0.7,1.5-0.9c1.2-0.5,2.5-0.5,3.7,0c0.6,0.2,1.1,0.5,1.5,0.9c0.4,0.4,0.8,0.9,1,1.4
					c0.3,0.7,0.4,1.6,0.3,2.4c-0.3-2.1-2.4-3.7-4.7-3.7S593.2,149.4,592.8,151.5z"/>
				<path id="Lennon_Glasses-3_1_" class="st15" d="M600.2,154.6c-1.1,0-2-0.9-2-2c0-0.1,0-0.2,0-0.4c0-0.4-0.3-0.6-0.6-0.7
					c-0.3,0-0.6,0.3-0.6,0.6c0,0.1,0,0.3,0,0.4c0,1.1-0.9,2-2,2c-1.1,0-2-0.9-2-2c0-0.2,0-0.4,0.1-0.6h-0.6c-0.1,0-0.2-0.1-0.2-0.2
					c0-0.1,0.1-0.2,0.2-0.2h0.7c0,0,0,0,0.1,0c0.6-1,1.8-1.3,2.7-0.8c0.3,0.2,0.6,0.5,0.8,0.8c0.3-0.4,0.9-0.6,1.3-0.3
					c0.1,0.1,0.2,0.2,0.3,0.3c0.5-1,1.7-1.4,2.7-0.9c0.4,0.2,0.7,0.5,0.9,0.8c0,0,0,0,0.1,0h0.7c0.1,0,0.2,0.1,0.2,0.2
					c0,0.1-0.1,0.2-0.2,0.2h-0.6c0.3,1.1-0.3,2.2-1.3,2.5C600.6,154.6,600.4,154.6,600.2,154.6z M600.2,150.9
					c-0.9,0-1.7,0.8-1.7,1.7c0,0.9,0.8,1.7,1.7,1.7c0.9,0,1.7-0.8,1.7-1.7C601.8,151.6,601.1,150.9,600.2,150.9z M595,150.9
					c-0.9,0-1.7,0.8-1.7,1.7s0.8,1.7,1.7,1.7c0.9,0,1.7-0.8,1.7-1.7C596.6,151.6,595.9,150.9,595,150.9z"/>
			</g>
		</g>
	</g>
</g>
<rect x="164.2" y="138.5" class="st74" width="66.9" height="33.1"/>
<path class="st75" d="M291.6,309.2c29-2.1,58.3-1.8,87.3,0.1c31.5,2,64.2,6.7,94.1,16.9c29.6,10,54.7,27,78.9,46.5
	c19.6,15.8,38.7,32.5,60,46.1c22.3,14.2,46.5,24.8,71.9,32.2c31.4,9.2,65.5,15.7,98.4,13.6"/>
</svg>
