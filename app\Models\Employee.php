<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

use App\Traits\CreatedUpdatedTrait;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;


class Employee extends Model implements HasMedia
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use InteractsWithMedia;

    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'address',
        'phone',
        'emergency_phone',
        'emergency_address',
        'email',
        'description',
        'basic_pay',
        'gender',
        'date_of_birth',
        'marital_status',
        'medical_description',
        'updated_by',
        'approved_by',
        'created_by',
        'user_id',
        'grade',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'basic_pay' => 'decimal:2',
    ];

    protected $appends = ['age','image'];
    
    public function file($collection = "image"){
        return $this->getMedia($collection)->first();
    }

    public function getImageAttribute(){
        $file = $this->file();
        return $file ? $file->getUrl() : '/assets/img/160x160/img1.jpg';
    }
    protected $searchableFields = ['*'];

    public function leaves()
    {
        return $this->hasMany(Leave::class);
    }


    public function bankDetails()
    {
        return $this->hasMany(BankDetail::class);
    }

    public function monthlyPayrolls()
    {
        return $this->hasMany(MonthlyPayroll::class);
    }

    public function employeeDeductionContributions()
    {
        return $this->hasMany(EmployeeDeductionContribution::class);
    }


    public function contributions()
    {
        return $this->hasMany(EmployeeDeductionContribution::class)->whereHas("deductionContribution", function($q){
            $q->where("apply_mode", "CONTRIBUTION");
        });
    }


    public function deductions()
    {
        return $this->hasMany(EmployeeDeductionContribution::class)->whereHas("deductionContribution", function($q){
            $q->where("apply_mode", "DEDUCTION");
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function leaveRequests()
    {
        return $this->hasMany(LeaveRequest::class);
    }

    public function timeRecords()
    {
        return $this->hasMany(EmployeeTimeRecord::class);
    }

    public function todayTimeRecord()
    {
        return $this->hasOne(EmployeeTimeRecord::class)->where('work_date', today());
    }

    public function loans()
    {
        return $this->hasMany(EmployeeLoan::class);
    }

    public function activeLoans()
    {
        return $this->hasMany(EmployeeLoan::class)->where('status', 'active');
    }

    public function completedLoans()
    {
        return $this->hasMany(EmployeeLoan::class)->where('status', 'completed');
    }

    /**
     * Get total overtime hours for a specific month.
     */
    public function getOvertimeHoursForMonth($year, $month)
    {
        return $this->timeRecords()
            ->whereYear('work_date', $year)
            ->whereMonth('work_date', $month)
            ->sum('overtime_hours');
    }

    /**
     * Get total regular hours for a specific month.
     */
    public function getRegularHoursForMonth($year, $month)
    {
        return $this->timeRecords()
            ->whereYear('work_date', $year)
            ->whereMonth('work_date', $month)
            ->sum('regular_hours');
    }

    /**
     * Get formatted date of birth for form inputs
     */
    public function getFormattedDateOfBirth()
    {
        if (!$this->date_of_birth) {
            return '';
        }

        // Handle both string and Carbon date objects
        if (is_string($this->date_of_birth)) {
            try {
                return \Carbon\Carbon::parse($this->date_of_birth)->format('Y-m-d');
            } catch (\Exception $e) {
                return '';
            }
        }

        return $this->date_of_birth->format('Y-m-d');
    }

    /**
     * Get age from date of birth
     */
    public function getAgeAttribute()
    {
        if (!$this->date_of_birth) {
            return null;
        }

        // Handle both string and Carbon date objects
        if (is_string($this->date_of_birth)) {
            try {
                return \Carbon\Carbon::parse($this->date_of_birth)->age;
            } catch (\Exception $e) {
                return null;
            }
        }

        return $this->date_of_birth->age;
    }

}
