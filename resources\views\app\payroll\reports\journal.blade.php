@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title mb-0">
                Payroll Journal Report - {{ $monthName }} {{ $year }}
            </h4>
            <div>
                <form action="{{ route('payroll-reports.journal') }}" method="POST" class="d-inline">
                    @csrf
                    <input type="hidden" name="month" value="{{ $month }}">
                    <input type="hidden" name="year" value="{{ $year }}">
                    <input type="hidden" name="format" value="pdf">
                    <button type="submit" class="btn btn-sm btn-secondary">
                        <i class="bi bi-file-earmark-pdf"></i> PDF
                    </button>
                </form>
                <form action="{{ route('payroll-reports.journal') }}" method="POST" class="d-inline">
                    @csrf
                    <input type="hidden" name="month" value="{{ $month }}">
                    <input type="hidden" name="year" value="{{ $year }}">
                    <input type="hidden" name="format" value="csv">
                    <button type="submit" class="btn btn-sm btn-secondary">
                        <i class="bi bi-file-earmark-excel"></i> CSV
                    </button>
                </form>
                <a href="{{ route('payroll-reports.journal-form') }}" class="btn btn-sm btn-light">
                    <i class="bi bi-arrow-left"></i> Back
                </a>
            </div>
        </div>

        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Employee</th>
                            <th>Basic Pay</th>
                            <th>Contributions</th>
                            <th>Gross Pay</th>
                            <th>PAYE Tax</th>
                            <th>Other Deductions</th>
                            <th>Total Deductions</th>
                            <th>Net Pay</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($payrolls as $payroll)
                        <tr>
                            <td>{{ $payroll->employee->name }}</td>
                            <td class="text-right">{{ number_format($payroll->basic_pay, 2) }}</td>
                            <td class="text-right">{{ number_format($payroll->contributions->sum('amount'), 2) }}</td>
                            <td class="text-right">{{ number_format($payroll->gross, 2) }}</td>
                            <td class="text-right">{{ number_format($payroll->payee, 2) }}</td>
                            <td class="text-right">{{ number_format($payroll->deductions->sum('amount'), 2) }}</td>
                            <td class="text-right">{{ number_format($payroll->payee + $payroll->deductions->sum('amount'), 2) }}</td>
                            <td class="text-right">{{ number_format($payroll->net_pay, 2) }}</td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center">No payroll records found for this period.</td>
                        </tr>
                        @endforelse
                    </tbody>
                    <tfoot>
                        <tr>
                            <th>Total</th>
                            <th class="text-right">{{ number_format($totalBasicPay, 2) }}</th>
                            <th class="text-right">{{ number_format($totalGrossPay - $totalBasicPay, 2) }}</th>
                            <th class="text-right">{{ number_format($totalGrossPay, 2) }}</th>
                            <th class="text-right">{{ number_format($payrolls->sum('payee'), 2) }}</th>
                            <th class="text-right">{{ number_format($totalDeductions - $payrolls->sum('payee'), 2) }}</th>
                            <th class="text-right">{{ number_format($totalDeductions, 2) }}</th>
                            <th class="text-right">{{ number_format($totalNetPay, 2) }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class="mt-4">
                <h5>Journal Entry Summary</h5>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Account</th>
                                <th>Debit</th>
                                <th>Credit</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Salary Expense</td>
                                <td class="text-right">{{ number_format($totalGrossPay, 2) }}</td>
                                <td class="text-right">-</td>
                            </tr>
                            <tr>
                                <td>PAYE Tax Payable</td>
                                <td class="text-right">-</td>
                                <td class="text-right">{{ number_format($payrolls->sum('payee'), 2) }}</td>
                            </tr>
                            <tr>
                                <td>Deductions Payable</td>
                                <td class="text-right">-</td>
                                <td class="text-right">{{ number_format($totalDeductions - $payrolls->sum('payee'), 2) }}</td>
                            </tr>
                            <tr>
                                <td>Cash/Bank</td>
                                <td class="text-right">-</td>
                                <td class="text-right">{{ number_format($totalNetPay, 2) }}</td>
                            </tr>
                            <tr>
                                <th>Total</th>
                                <th class="text-right">{{ number_format($totalGrossPay, 2) }}</th>
                                <th class="text-right">{{ number_format($totalGrossPay, 2) }}</th>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
