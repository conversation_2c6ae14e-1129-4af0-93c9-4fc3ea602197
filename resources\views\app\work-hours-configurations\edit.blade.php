@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{{ route('work-hours-configurations.index') }}">Work Hours Configurations</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('work-hours-configurations.show', $workHoursConfiguration) }}">{{ $workHoursConfiguration->name }}</a>
                        </li>
                        <li class="breadcrumb-item active">Edit</li>
                    </ol>
                </nav>
                <h1 class="page-header-title">Edit Work Hours Configuration</h1>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Configuration Details</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('work-hours-configurations.update', $workHoursConfiguration) }}">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Configuration Name</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                        id="name" name="name" value="{{ old('name', $workHoursConfiguration->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" 
                                            name="is_active" value="1" 
                                            {{ old('is_active', $workHoursConfiguration->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Set as Active Configuration
                                        </label>
                                    </div>
                                    <small class="text-muted">
                                        Only one configuration can be active at a time
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_time" class="form-label">Start Time</label>
                                    <input type="time" class="form-control @error('start_time') is-invalid @enderror" 
                                        id="start_time" name="start_time" 
                                        value="{{ old('start_time', date('H:i', strtotime($workHoursConfiguration->start_time))) }}" required>
                                    @error('start_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_time" class="form-label">End Time</label>
                                    <input type="time" class="form-control @error('end_time') is-invalid @enderror" 
                                        id="end_time" name="end_time" 
                                        value="{{ old('end_time', date('H:i', strtotime($workHoursConfiguration->end_time))) }}" required>
                                    @error('end_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="standard_hours_per_day" class="form-label">Standard Hours per Day</label>
                                    <input type="number" class="form-control @error('standard_hours_per_day') is-invalid @enderror" 
                                        id="standard_hours_per_day" name="standard_hours_per_day" 
                                        value="{{ old('standard_hours_per_day', $workHoursConfiguration->standard_hours_per_day) }}" 
                                        step="0.25" min="1" max="24" required>
                                    @error('standard_hours_per_day')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">
                                        Hours worked beyond this will be considered overtime
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="overtime_rate" class="form-label">Overtime Rate Multiplier</label>
                                    <input type="number" class="form-control @error('overtime_rate') is-invalid @enderror" 
                                        id="overtime_rate" name="overtime_rate" 
                                        value="{{ old('overtime_rate', $workHoursConfiguration->overtime_rate) }}" 
                                        step="0.25" min="1" max="5" required>
                                    @error('overtime_rate')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">
                                        e.g., 1.5 means overtime is paid at 1.5x regular rate
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description (Optional)</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                id="description" name="description" rows="3">{{ old('description', $workHoursConfiguration->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Configuration
                            </button>
                            <a href="{{ route('work-hours-configurations.show', $workHoursConfiguration) }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Current Settings</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">Work Hours:</span>
                        <span class="fw-bold">
                            {{ date('H:i', strtotime($workHoursConfiguration->start_time)) }} - 
                            {{ date('H:i', strtotime($workHoursConfiguration->end_time)) }}
                        </span>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">Standard Hours:</span>
                        <span class="fw-bold">{{ $workHoursConfiguration->standard_hours_per_day }}h</span>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">Overtime Rate:</span>
                        <span class="fw-bold">{{ $workHoursConfiguration->overtime_rate }}x</span>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">Status:</span>
                        <span>
                            @if($workHoursConfiguration->is_active)
                                <span class="badge bg-success">Active</span>
                            @else
                                <span class="badge bg-secondary">Inactive</span>
                            @endif
                        </span>
                    </div>
                </div>
            </div>

            @if($workHoursConfiguration->is_active)
            <div class="card mt-3">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-info-circle text-info me-2"></i>
                        <div>
                            <h6 class="mb-1">Active Configuration</h6>
                            <p class="text-muted small mb-0">
                                This configuration is currently being used for overtime calculations.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
