<?php

namespace App\Http\Controllers;

use App\Models\Debt;
use App\Models\Customer;
use App\Models\Supplier;
use Illuminate\Http\Request;

use Facades\App\Cache\Repo;
use Facades\App\Libraries\InvoiceHandler;

class DebtController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

        $debtors = Repo::getDebtCursor();

        if( request()->type ) {
            if( request()->type == 'customers') $debtors->where("debtable_type", "App\Models\Customer");
            if( request()->type == 'suppliers') $debtors->where("debtable_type", "App\Models\Supplier");
        } else {
            $debtors->where("debtable_type", "App\Models\Customer")->orWhere("debtable_type", "App\Models\Supplier");
        }

        $debtors = $debtors->get();
        return view('app.debtors.index', compact('debtors'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validated = $request->all();
        $debt = Debt::create($validated);
        return redirect()->back()->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Dept  $debt
     * @return \Illuminate\Http\Response
     */
    public function show(Debt $debt)
    {
        session(['back' => url()->previous() ]);
        return view('app.debtors.show', compact('debt'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Dept  $debt
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Debt $debt)
    {
        $type = $request->type;
        session(['back' => url()->previous() ]);
        return view('app.debtors.edit-debtor', compact('debt', 'type'));
    } 

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Dept  $debt
     * @return \Illuminate\Http\Response
     */

    public function editExpense(Request $request, Debt $debt)
    {
        session(['back' => url()->previous() ]);
        return view('app.debtors.edit-expense', compact('debt'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Dept  $debt
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Debt $debt)
    {
        $validated = $request->all();
        $debt = $debt->update($validated);
        return redirect(session('back') ?? '/')->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Dept  $debt
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Debt $debt)
    {
        $debt->delete();
        return redirect()->back();
    }

    public function customerDebtors(Request $request){
        $debtors = Repo::getDebtCursor()->where("debtable_type", "App\Models\Customer")->get();
        return view("app.debtors.customer-debtors", compact("debtors"));
    }

    public function supplierDebtors(Request $request){
        $debtors = Repo::getDebtCursor()->where("debtable_type", "App\Models\Supplier")->get();
        return view("app.debtors.supplier-debtors", compact("debtors"));
    }

    public function expense(Request $request){
        $debtors = Repo::getDebts("expenses");
        return view("app.debtors.expenses", compact("debtors"));
    }

    public function saveExpense(Request $request){
        $validated = $request->all();
        $validated['amount_paid'] = $request->amount_total;
        $debt = Debt::create($validated);
        return redirect()->back()->withSuccess(__('crud.common.saved'));
    }

    public function saveCustomerDebtors(Request $request) {

        $validated = $request->all();
        $customer = Customer::find($request->customer_id );

        if( $customer ) {
            $debtor = $customer->debtors()->find(request()->debtor_id);
            if( $debtor ) {
                $debtor->update($validated);
            } else {
                $debtor = $customer->debtors()->create($validated);
            }

        }

        return redirect()->back()->withSuccess(__('crud.common.saved'));
    }

    public function saveSupplierDebtors(Request $request) {

        $validated = $request->all();

        $supplier = Supplier::find($request->supplier_id );

        if( $supplier ) {

            $debtor = $supplier->debtors()->find(request()->debtor_id);

            if( $debtor ) {
                $debtor->update($validated);
            } else {
                $debtor = $supplier->debtors()->create($validated);
            }

        }

        return redirect()->back()->withSuccess(__('crud.common.saved'));
    }

    public function ajaxDebtor(Request $request, Debt $debt){
        $debt->debtable;
        return response($debt);
    }

    public function settleDebtor(Request $request){

        $debtor = Debt::find( $request->id);
        if($debtor) {

            if( $request->amount_paid > $debtor->balance) {
                $debtor->increment("amount_paid", $debtor->amount_total);
            } else {
                $debtor->increment("amount_paid", $request->amount_paid);
            }
            InvoiceHandler::addPayment($debtor, [
                "description" => $request->comment,
                "comment" => $request->comment,
                "amount" => $request->amount_paid,
                "balance" => $debtor->amount_total - $debtor->amount_paid,
            ]);
        }
        return redirect()->back()->withSuccess(__('crud.common.saved'));
    }



    public function accounts(Request $request) {

        $accounts = Debt::query();


        $type = request()->type ?? "customers";

        if( $type == 'customers') {
            $accounts->where("debtable_type", "App\Models\Customer");
            $debtables = Customer::get();
        }
        if( $type == 'suppliers') {
            $accounts->where("debtable_type", "App\Models\Supplier");
            $debtables = Supplier::get();
        }

        if( request()->debtable_id) $accounts->where("debtable_id", request()->debtable_id );

        $accounts->with("payments", "debtable");
        //  ->select([
        //     \DB::raw("sum(amount_total) as amount_total"),
        //     \DB::raw("sum(amount_paid) as amount_paid"),
        //     "description",
        //  ]);
        // $accounts->groupBy("debtable_id");

        $accounts = $accounts->get()->groupBy('debtable_id');

        // print_r($accounts[0]->amount_paid);
        // print_r($accounts[0]->amount_total);


        return view('app.debtors.accounts', compact('accounts', 'debtables'));
    }

    public function accountDetails(Request $request, Debt $debt) {
        session(['back' => url()->previous() ]);
        $debtable = $debt->debtable;
        return view('app.debtors.account-details', compact('debt', 'debtable'));
    }



}
