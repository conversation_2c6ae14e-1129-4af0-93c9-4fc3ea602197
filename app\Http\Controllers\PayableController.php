<?php

namespace App\Http\Controllers;

use App\Models\Supplier;
use App\Models\Order;
use App\Models\Payment;
use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Facades\App\Libraries\InvoiceHandler;

class PayableController extends Controller
{
    /**
     * Display supplier payables (creditors)
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Supplier::class);

        // Build query for suppliers with outstanding balances
        $query = Supplier::with(['orders' => function($q) {
            $q->whereRaw('amount_total > amount_paid');
        }])
        ->whereHas('orders', function($q) {
            $q->whereRaw('amount_total > amount_paid');
        })
        ->when(!auth()->user()->isSuperAdmin(), function($query) {
            $query->where('branch_id', auth()->user()->branch_id);
        });

        // Apply filters
        $this->applyFilters($query, $request);

        // Get suppliers with outstanding balances
        $suppliers = $query->orderBy('name')->paginate(20);

        // Calculate totals for each supplier
        $suppliers->getCollection()->transform(function ($supplier) {
            $supplier->total_outstanding = $supplier->orders->sum('balance');
            $supplier->total_orders = $supplier->orders->count();
            $supplier->oldest_order = $supplier->orders->min('created_at');
            return $supplier;
        });

        // Calculate analytics
        $analytics = $this->calculatePayableAnalytics($request);

        // Get filter options
        $filterOptions = $this->getFilterOptions();

        return view('app.payables.index', compact('suppliers', 'analytics', 'filterOptions'));
    }

    /**
     * Show supplier payable details
     */
    public function show(Supplier $supplier)
    {
        $this->authorize('view', $supplier);

        // Load supplier with outstanding orders and payments
        $supplier->load([
            'orders' => function($q) {
                $q->whereRaw('amount_total > amount_paid')->orderBy('created_at', 'desc');
            },
            'orders.payments',
            'orders.currency'
        ]);

        // Calculate supplier totals
        $supplierTotals = [
            'total_outstanding' => $supplier->orders->sum('balance'),
            'total_ordered' => $supplier->orders->sum('amount_total'),
            'total_paid' => $supplier->orders->sum('amount_paid'),
            'order_count' => $supplier->orders->count(),
            'oldest_order' => $supplier->orders->min('created_at'),
        ];

        return view('app.payables.show', compact('supplier', 'supplierTotals'));
    }

    /**
     * Show payment form for supplier payables
     */
    public function createPayment(Supplier $supplier)
    {
        $this->authorize('create', Payment::class);

        // Get supplier's outstanding orders/bills
        $orders = $supplier->orders()
                          ->whereRaw('amount_total > amount_paid')
                          ->orderBy('created_at', 'desc')
                          ->get();

        $currencies = Currency::orderBy('name')->get();

        return view('app.payables.create-payment', compact('supplier', 'orders', 'currencies'));
    }

    /**
     * Store payment for supplier payable
     */
    public function storePayment(Request $request, Supplier $supplier)
    {
        $this->authorize('create', Payment::class);

        $validated = $request->validate([
            'order_id' => 'required|tenant_exists:orders,id',
            'amount' => 'required|numeric|min:0.01',
            'reference_no' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'comment' => 'nullable|string|max:1000',
            'payment_method' => 'nullable|string|max:100',
            'currency_id' => 'nullable|tenant_exists:currencies,id',
        ]);

        DB::beginTransaction();
        try {
            // Get the order and validate it belongs to the supplier
            $order = Order::findOrFail($validated['order_id']);

            if ($order->supplier_id !== $supplier->id) {
                return back()
                    ->withInput()
                    ->with('error', 'Selected order does not belong to this supplier.');
            }

            // Validate payment amount doesn't exceed balance
            if ($validated['amount'] > $order->balance) {
                return back()
                    ->withInput()
                    ->with('error', 'Payment amount cannot exceed the outstanding balance of ' . _money($order->balance));
            }

            // Create payment
            $paymentData = [
                'amount' => $validated['amount'],
                'description' => $validated['description'] ?? 'Payment to ' . $supplier->name,
                'comment' => $validated['comment'],
                'reference_no' => $validated['reference_no'],
                'payment_method' => $validated['payment_method'],
                'currency_id' => $validated['currency_id'],
                'balance' => $order->balance - $validated['amount'],
                'payment_type' => 'payable',
            ];

            $payment = InvoiceHandler::addPayment($order, $paymentData);

            // Update the order balance
            $order->increment('amount_paid', $validated['amount']);

            DB::commit();

            return redirect()
                ->route('payables.show', $supplier)
                ->with('success', 'Payment recorded successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()
                ->withInput()
                ->with('error', 'Failed to record payment: ' . $e->getMessage());
        }
    }

    /**
     * Apply filters to supplier query
     */
    private function applyFilters($query, Request $request)
    {
        // Search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Outstanding amount range
        if ($request->filled('amount_from') || $request->filled('amount_to')) {
            $query->whereHas('orders', function($q) use ($request) {
                $q->whereRaw('amount_total > amount_paid');
                if ($request->filled('amount_from')) {
                    $q->havingRaw('SUM(amount_total - amount_paid) >= ?', [$request->amount_from]);
                }
                if ($request->filled('amount_to')) {
                    $q->havingRaw('SUM(amount_total - amount_paid) <= ?', [$request->amount_to]);
                }
            });
        }

        // Date range for oldest order
        if ($request->filled('date_from')) {
            $query->whereHas('orders', function($q) use ($request) {
                $q->where('created_at', '>=', $request->date_from);
            });
        }

        if ($request->filled('date_to')) {
            $query->whereHas('orders', function($q) use ($request) {
                $q->where('created_at', '<=', $request->date_to);
            });
        }
    }

    /**
     * Calculate payable analytics
     */
    private function calculatePayableAnalytics(Request $request)
    {
        $baseQuery = Supplier::whereHas('orders', function($q) {
            $q->whereRaw('amount_total > amount_paid');
        });

        // Apply same filters as main query
        $this->applyFilters($baseQuery, $request);

        return [
            'total_suppliers' => $baseQuery->count(),
            'total_outstanding' => Order::whereRaw('amount_total > amount_paid')->sum(DB::raw('amount_total - amount_paid')),
            'avg_outstanding' => Order::whereRaw('amount_total > amount_paid')->avg(DB::raw('amount_total - amount_paid')),
            'overdue_orders' => Order::whereRaw('amount_total > amount_paid')
                                    ->where('created_at', '<', now()->subDays(30))
                                    ->count(),
            'overdue_amount' => Order::whereRaw('amount_total > amount_paid')
                                    ->where('created_at', '<', now()->subDays(30))
                                    ->sum(DB::raw('amount_total - amount_paid')),
        ];
    }

    /**
     * Get filter options for dropdowns
     */
    private function getFilterOptions()
    {
        return [
            'currencies' => Currency::orderBy('name')->get(),
        ];
    }
}
