@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-plus-circle me-2"></i>Create Fiscal Period
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('fiscal-periods.index') }}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-left me-1"></i>Back to Fiscal Periods
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar3 me-2"></i>New Fiscal Period
                    </h5>
                </div>

                <form method="POST" action="{{ route('fiscal-periods.store') }}">
                    @csrf
                    <div class="card-body">
                        
                        <!-- Error Display -->
                        @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <h6 class="alert-heading">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Please fix the following errors:
                            </h6>
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        @endif

                        <!-- Basic Information -->
                        <h6 class="text-primary mb-3">
                            <i class="bi bi-info-circle me-2"></i>Basic Information
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Fiscal Year <span class="text-danger">*</span></label>
                                <select name="fiscal_year_id" class="form-select @error('fiscal_year_id') is-invalid @enderror" required>
                                    <option value="">Select Fiscal Year</option>
                                    @foreach($fiscalYears as $id => $name)
                                        <option value="{{ $id }}" {{ old('fiscal_year_id', request('fiscal_year_id')) == $id ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('fiscal_year_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Select the fiscal year this period belongs to</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Period Name <span class="text-danger">*</span></label>
                                <input type="text" name="name" class="form-control @error('name') is-invalid @enderror" 
                                       placeholder="e.g., January 2024, Q1 2024" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Enter a descriptive name for the period</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Start Date <span class="text-danger">*</span></label>
                                <input type="date" name="start_date" class="form-control @error('start_date') is-invalid @enderror" 
                                       value="{{ old('start_date') }}" required>
                                @error('start_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">End Date <span class="text-danger">*</span></label>
                                <input type="date" name="end_date" class="form-control @error('end_date') is-invalid @enderror" 
                                       value="{{ old('end_date') }}" required>
                                @error('end_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Status Settings -->
                        <h6 class="text-primary mb-3 mt-4">
                            <i class="bi bi-gear me-2"></i>Status Settings
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input type="checkbox" name="is_active" class="form-check-input" id="is_active" 
                                           value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        <strong>Active Period</strong>
                                    </label>
                                </div>
                                <div class="form-text">Set as an active fiscal period</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input type="checkbox" name="is_closed" class="form-check-input" id="is_closed" 
                                           value="1" {{ old('is_closed', false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_closed">
                                        <strong>Closed Period</strong>
                                    </label>
                                </div>
                                <div class="form-text">Close this period to prevent new transactions</div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea name="description" class="form-control @error('description') is-invalid @enderror" 
                                      rows="3" placeholder="Optional description for this fiscal period">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Quick Period Templates -->
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="bi bi-lightning me-2"></i>Quick Templates
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="setMonthlyPeriod()">
                                            <i class="bi bi-calendar-month me-1"></i>Monthly Period
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="setQuarterlyPeriod()">
                                            <i class="bi bi-calendar3 me-1"></i>Quarterly Period
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="setYearlyPeriod()">
                                            <i class="bi bi-calendar-range me-1"></i>Yearly Period
                                        </button>
                                    </div>
                                </div>
                                <small class="text-muted">Click a template to auto-fill period dates based on common patterns</small>
                            </div>
                        </div>

                        <!-- Information Alert -->
                        <div class="alert alert-info mt-3">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Note:</strong> Fiscal periods help organize transactions within a fiscal year. Ensure the period dates fall within the selected fiscal year's date range.
                        </div>

                    </div>

                    <div class="card-footer d-flex justify-content-between">
                        <a href="{{ route('fiscal-periods.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>Create Fiscal Period
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-calculate end date when start date changes
    const startDateInput = document.querySelector('input[name="start_date"]');
    const endDateInput = document.querySelector('input[name="end_date"]');
    
    startDateInput.addEventListener('change', function() {
        if (this.value && !endDateInput.value) {
            const startDate = new Date(this.value);
            // Default to end of month
            const endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
            endDateInput.value = endDate.toISOString().split('T')[0];
        }
    });
});

function setMonthlyPeriod() {
    const today = new Date();
    const startDate = new Date(today.getFullYear(), today.getMonth(), 1);
    const endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    document.querySelector('input[name="start_date"]').value = startDate.toISOString().split('T')[0];
    document.querySelector('input[name="end_date"]').value = endDate.toISOString().split('T')[0];
    document.querySelector('input[name="name"]').value = startDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
}

function setQuarterlyPeriod() {
    const today = new Date();
    const quarter = Math.floor(today.getMonth() / 3);
    const startDate = new Date(today.getFullYear(), quarter * 3, 1);
    const endDate = new Date(today.getFullYear(), (quarter + 1) * 3, 0);
    
    document.querySelector('input[name="start_date"]').value = startDate.toISOString().split('T')[0];
    document.querySelector('input[name="end_date"]').value = endDate.toISOString().split('T')[0];
    document.querySelector('input[name="name"]').value = `Q${quarter + 1} ${today.getFullYear()}`;
}

function setYearlyPeriod() {
    const today = new Date();
    const startDate = new Date(today.getFullYear(), 0, 1);
    const endDate = new Date(today.getFullYear(), 11, 31);
    
    document.querySelector('input[name="start_date"]').value = startDate.toISOString().split('T')[0];
    document.querySelector('input[name="end_date"]').value = endDate.toISOString().split('T')[0];
    document.querySelector('input[name="name"]').value = `Full Year ${today.getFullYear()}`;
}
</script>
@endpush
