@extends('layouts.app')

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">building</x-slot>
        <x-slot name="title">{{ $supplier->name }}</x-slot>
        <x-slot name="subtitle">Supplier details and information</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('suppliers.index') }}">Suppliers</a></li>
            <li class="breadcrumb-item active" aria-current="page">View</li>
        </x-slot>
        <x-slot name="controls">
            @can('update', $supplier)
            <a href="{{ route('suppliers.edit', $supplier) }}" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> Edit Supplier
            </a>
            @endcan
            <a href="{{ route('suppliers.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Suppliers
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <div class="col-lg-8 mb-4">
            <!-- Card -->
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-info-circle me-2"></i>Supplier Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-sm-6">
                            <label class="form-label">Name</label>
                            <div class="mb-2">
                                <h5>{{ $supplier->name ?? '-' }}</h5>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label">Status</label>
                            <div class="mb-2">
                                <span class="badge bg-{{ optional($supplier->status)->name == 'Active' ? 'success' : 'secondary' }}">
                                    {{ optional($supplier->status)->name ?? 'No Status' }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-sm-6">
                            <label class="form-label">Email</label>
                            <div class="mb-2">
                                @if($supplier->email)
                                    <a href="mailto:{{ $supplier->email }}" class="text-body">{{ $supplier->email }}</a>
                                @else
                                    <span class="text-muted">No email provided</span>
                                @endif
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label">Phone</label>
                            <div class="mb-2">
                                @if($supplier->phone)
                                    <a href="tel:{{ $supplier->phone }}" class="text-body">{{ $supplier->phone }}</a>
                                @else
                                    <span class="text-muted">No phone provided</span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="form-label">Address</label>
                        <div class="mb-2">
                            <p>{{ $supplier->address ?? 'No address provided' }}</p>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-sm-6">
                            <label class="form-label">Created</label>
                            <div class="mb-2">
                                <span>{{ $supplier->created_at->format('F d, Y') }}</span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label">Last Updated</label>
                            <div class="mb-2">
                                <span>{{ $supplier->updated_at->format('F d, Y') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Card -->
        </div>

        <div class="col-lg-4 mb-4">
            <!-- Card -->
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-gear me-2"></i>Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('suppliers.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i> Back to Suppliers
                        </a>

                        @can('update', $supplier)
                        <a href="{{ route('suppliers.edit', $supplier) }}" class="btn btn-primary">
                            <i class="bi bi-pencil-square me-1"></i> Edit Supplier
                        </a>
                        @endcan

                        @can('create', App\Models\Supplier::class)
                        <a href="{{ route('suppliers.create') }}" class="btn btn-outline-success">
                            <i class="bi bi-plus-circle me-1"></i> Create New Supplier
                        </a>
                        @endcan

                        @can('delete', $supplier)
                        <form action="{{ route('suppliers.destroy', $supplier) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this supplier?')">
                            @csrf @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="bi bi-trash me-1"></i> Delete Supplier
                            </button>
                        </form>
                        @endcan
                    </div>
                </div>
            </div>
            <!-- End Card -->
        </div>
    </div>

    <!-- Related Orders Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-header-title">
                <i class="bi bi-box-seam me-2"></i>Recent Orders
            </h5>
        </div>
        <div class="card-body">
            @if(count($supplier->orders ?? []) > 0)
                <div class="table-responsive">
                    <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                        <thead class="thead-light">
                            <tr>
                                <th>Order ID</th>
                                <th>Date</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($supplier->orders ?? [] as $order)
                            <tr>
                                <td>#{{ $order->id }}</td>
                                <td>{{ $order->created_at->format('M d, Y') }}</td>
                                <td>{{ _money($order->total_amount) }}</td>
                                <td>
                                    <span class="badge bg-success">
                                        {{ $order->status }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ route('orders.show', $order) }}" class="btn btn-white btn-sm">
                                        <i class="bi-eye me-1"></i> View
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="bi bi-box text-muted" style="font-size: 3rem;"></i>
                    <p class="mt-2">No orders found for this supplier.</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
