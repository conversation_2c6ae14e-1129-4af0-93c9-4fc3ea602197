<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ env("APP_NAME")}}</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet">

    <!-- Styles -->
    <!-- <link rel="stylesheet" href="{{ asset('css/app.css') }}"> -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/notyf@3/notyf.min.css">

    <!-- Custom Notification Styles -->
    <style>
        .notyf__toast {
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .notyf__toast--success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .notyf__toast--error {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        }
        .notyf__toast--warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
        }
        .notyf__toast--info {
            background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%);
        }
    </style>

    <!-- Icons -->
    <link href="https://unpkg.com/ionicons@4.5.10-0/dist/css/ionicons.min.css" rel="stylesheet">

    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <!-- Scripts -->
    <script src="{{ asset('js/app.js') }}"></script>
        <!-- Favicon -->
    <!-- <link rel="shortcut icon" href="favicon.ico"> -->
    <link rel="shortcut icon" href="{{ asset('img/logo.webp') }}">

    <link href="{{ asset('vue-img/print.min.css') }}" rel="stylesheet">
    <script src="{{ asset('vue-img/print.min.js') }}"></script>
    <link href="{{ asset('vue-img/vue-2-img.css') }}" rel="stylesheet">
    <script src="{{ asset('vue-img/vue-2-img.js') }}"></script>
    <script src="{{ asset('vue-img/html2canvas.min.js') }}"></script>
    <script src="https://superal.github.io/canvas2image/canvas2image.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.min.js"></script>

    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&amp;display=swap" rel="stylesheet">

    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="{{ asset('assets/css/vendor.min.css') }}">
    <link rel="stylesheet" href="{{ asset('css/custom.css') }}">
    <link rel="stylesheet" href="{{ asset('css/custom-ui.css') }}">

    <!-- CSS Front Template -->
    <link rel="stylesheet" href="{{ asset('assets/css/theme.minc619.css?v=1.0') }}">

    <link rel="preload" href="{{ asset('assets/css/theme.min.css') }}" data-hs-appearance="default" as="style">
    <link rel="preload" href="{{ asset('assets/css/theme-dark.min.css') }}" data-hs-appearance="dark" as="style">

    <script src="https://code.jquery.com/jquery-3.6.1.min.js" integrity="sha256-o88AwQnZB+VDvE9tvIXrMQaPlFFSUTR+nldQm1LuPXQ=" crossorigin="anonymous"></script>

    <script type="text/javascript" src="{{ asset('js/vue.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/axios.min.js') }}"></script>

    <link href="https://cdn.datatables.net/1.13.1/css/dataTables.bootstrap4.min.css" rel="stylesheet">

    <style data-hs-appearance-onload-styles>
      *
      {
        transition: unset !important;
      }

      .form-control{
        height: 40px !important;
      }

      .table-responsive{
        min-height: 200px !important;
      }

      .table-responsive.table-dropdown-space{
        min-height: 300px !important;
      }

      @page {
          size: auto;
          margin: 0;
      }
/*
      @media print {
        body,
        html,
        .print,
        * {
          width: 100%;
          margin: 0px;
          padding: 0px;
          border: 1px solid red;
        }

        .no-print {
          display: none;
        }
      }*/

  @media print {
    @page {
/*        size: A1|A4|A5 landscape; /* auto is default portrait; */*/
        background: red;
        height: 200px !important;
        size: landscape;
    }
}

/*    @media print {
       html, body {
        width: 80mm;
        height:10%;
        position:absolute;
       }
    }*/

        .dt-button-collection{

            display: block;
            height: 450px !important;
            overflow-y: scroll !important;
        }

      .p-select .js-select {
        font-size: 25px;
      }

      .table {
        min-height: 400px !important;
      }
    </style>

    <script type="module">
        // import hotwiredTurbo from 'https://cdn.skypack.dev/@hotwired/turbo';
    </script>
     @stack('styles')

    <script type="text/javascript" src="{{ asset('assets/js/data.js') }}"></script>
    @livewireStyles

  </head>
  <body class="has-navbar-vertical-aside navbar-vertical-aside-show-xl   footer-offset">
  <script src="{{ asset('assets/js/hs.theme-appearance.js') }}"></script>
  <script src="{{ asset('assets/vendor/hs-navbar-vertical-aside/dist/hs-navbar-vertical-aside-mini-cache.js') }}"></script>

  <!-- ========== HEADER ========== -->

    @include('partial.header')
    @include('partial.aside')



    <main id="content" role="main" class="main">
      @include('layouts.error')
      @yield('content')
      @include('partial.footer')
    </main>


    @include('modals.stock-adjustment-modal')
    @include('modals.customer-debtor-modal')
    @include('modals.supplier-debtor-modal')
    @include('modals.settle-debtor-modal')
    @include('modals.import-products-modal')
    @include('modals.import-order-modal')

    @include('modals.expense-modal')
    @include('modals.credit-modal')

    @include('modals.invoice')
    @include('modals.order')
    @include('modals.quotation')
    @include('modals.shipment')
    @include('modals.pending-invoice')
    @include('modals.pending-order')

    @include('partial.builder')
    @include('partial.js-build-layout')
    @include('partial.keyboard')
    @include('partial.activity')
    @include('partial.welcome-modal')

    @stack('modals')

    @livewireScripts


    <!-- End Welcome Message Modal -->
    <!-- ========== END SECONDARY CONTENTS ========== -->

    <!-- JS Implementing Plugins -->
    <script src="{{ asset('assets/js/vendor.min.js') }}"></script>

    <!-- JS Front -->
    <script src="{{ asset('assets/js/theme.min.js') }}"></script>
    <script src="{{ asset('assets/js/hs.theme-appearance-charts.js') }}"></script>


    <script src="https://cdn.jsdelivr.net/gh/livewire/turbolinks@v0.1.x/dist/livewire-turbolinks.js" data-turbolinks-eval="false" data-turbo-eval="false"></script>

    <script type="text/javascript" src="{{ asset('js/scripts.js') }}"></script>

    <script src="https://cdn.jsdelivr.net/npm/notyf@3/notyf.min.js"></script>

<!-- <script src="https://code.jquery.com/jquery-3.6.3.js" integrity="sha256-nQLuAZGRRcILA+6dMBOvcRh5Pe310sBpanc6+QBmyVM=" crossorigin="anonymous"></script> -->

    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    @if (session()->has('success'))
    <script>
        var notyf = new Notyf({dismissible: true})
        notyf.success('{{ session('success') }}')
    </script>
    @endif

    @if (session()->has('error'))
    <script>
        var notyf = new Notyf({dismissible: true})
        notyf.error('{{ session('error') }}')
    </script>
    @endif

    <script>
        /* Simple Alpine Image Viewer */
        document.addEventListener('alpine:init', () => {
            Alpine.data('imageViewer', (src = '') => {
                return {
                    imageUrl: src,

                    refreshUrl() {
                        this.imageUrl = this.$el.getAttribute("image-url")
                    },

                    fileChosen(event) {
                        this.fileToDataUrl(event, src => this.imageUrl = src)
                    },

                    fileToDataUrl(event, callback) {
                        if (! event.target.files.length) return

                        let file = event.target.files[0],
                            reader = new FileReader()

                        reader.readAsDataURL(file)
                        reader.onload = e => callback(e.target.result)
                    },
                }
            })
        });

        function formatNumber (num) {
            return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")
        }

    </script>


  <!-- JS Plugins Init. -->
  <script>
    (function() {
      window.onload = function () {



      // INITIALIZATION OF FORM SEARCH
      // =======================================================
      new HSFormSearch('.js-form-search')


      // INITIALIZATION OF BOOTSTRAP DROPDOWN
      // =======================================================
      HSBsDropdown.init()


      // INITIALIZATION OF SELECT
      // =======================================================
      HSCore.components.HSTomSelect.init('.js-select')


      // INITIALIZATION OF INPUT MASK
      // =======================================================
      HSCore.components.HSMask.init('.js-input-mask')


      // INITIALIZATION OF NAV SCROLLER
      // =======================================================
      new HsNavScroller('.js-nav-scroller')


      // INITIALIZATION OF COUNTER
      // =======================================================
      new HSCounter('.js-counter')


      // INITIALIZATION OF TOGGLE PASSWORD
      // =======================================================
      new HSTogglePassword('.js-toggle-password')


      // INITIALIZATION OF FILE ATTACHMENT
      // =======================================================
      new HSFileAttach('.js-file-attach')


        // INITIALIZATION OF CHARTJS
        // =======================================================
        HSCore.components.HSChartJS.init('.js-chart')


        // INITIALIZATION OF VECTOR MAP
        // =======================================================
        // setTimeout(() => {
        //   HSCore.components.HSJsVectorMap.init('.js-jsvectormap', {
        //     backgroundColor: HSThemeAppearance.getAppearance() === 'dark' ? '#25282a' : '#ffffff'
        //   })

        //   const vectorMap = HSCore.components.HSJsVectorMap.getItem(0)

        //   window.addEventListener('on-hs-appearance-change', e => {
        //     vectorMap.setBackgroundColor(e.detail === 'dark' ? '#25282a' : '#ffffff')
        //   })
        // }, 300)

      }
    })()
  </script>

  <!-- Style Switcher JS -->

  <script>
      (function () {
        // STYLE SWITCHER
        // =======================================================
        const $dropdownBtn = document.getElementById('selectThemeDropdown') // Dropdowon trigger
        const $variants = document.querySelectorAll(`[aria-labelledby="selectThemeDropdown"] [data-icon]`) // All items of the dropdown

        // Function to set active style in the dorpdown menu and set icon for dropdown trigger
        const setActiveStyle = function () {
          $variants.forEach($item => {
            if ($item.getAttribute('data-value') === HSThemeAppearance.getOriginalAppearance()) {
              $dropdownBtn.innerHTML = `<i class="${$item.getAttribute('data-icon')}" />`
              return $item.classList.add('active')
            }

            $item.classList.remove('active')
          })
        }

        // Add a click event to all items of the dropdown to set the style
        $variants.forEach(function ($item) {
          $item.addEventListener('click', function () {
            HSThemeAppearance.setAppearance($item.getAttribute('data-value'))
          })
        })

        // Call the setActiveStyle on load page
        setActiveStyle()

        // Add event listener on change style to call the setActiveStyle function
        window.addEventListener('on-hs-appearance-change', function () {
          setActiveStyle()
        })
      })()
    </script>



    <!-- JS Plugins Init. -->
    <script>
      $("document").ready(function() {
          // INITIALIZATION OF NAVBAR VERTICAL ASIDE
          // =======================================================
          new HSSideNav('.js-navbar-vertical-aside').init()
        // INITIALIZATION OF NAVBAR VERTICAL ASIDE
        // =======================================================
        // new HSSideNav('.js-navbar-vertical-aside').init()
      });
      </script>

     <script type="text/javascript">
        $(document).on('ready', function () {
          // INITIALIZATION OF DATERANGEPICKER



          // =======================================================
          $('.js-daterangepicker').daterangepicker();

          $('.js-daterangepicker-times').daterangepicker({
            timePicker: true,
            startDate: moment().startOf('hour'),
            endDate: moment().startOf('hour').add(32, 'hour'),
            locale: {
              format: 'M/DD hh:mm A'
            }
          });

          var start = @json( request()->from ?? Facades\App\Cache\Repo::dateFrom());
          var end = @json( request()->to );
          start = (start) ? moment(start, "YYYY-MM-DD HH:mm:ss") : moment();
          end = (end) ? moment(end, "YYYY-MM-DD HH:mm:ss") : moment();
          $('#js-daterangepicker-predefined .js-daterangepicker-predefined-preview').html(start.format('MMM D') + ' - ' + end.format('MMM D, YYYY'));

          function cb(start, end) {

            var params  = @json( request()->all() );
            var search  = "?";
            params.from = start.format("YYYY-MM-DD HH:mm:ss");
            params.to   = end.format("YYYY-MM-DD HH:mm:ss");
            $('input[name="from"]').val(params.from);
            $('input[name="to"]').val(params.to);
            // for( para in params){
            //   search += "&" + para + "=" + params[para];
            // }

            // window.location.href = search;
            $('#js-daterangepicker-predefined .js-daterangepicker-predefined-preview').html(start.format('MMM D') + ' - ' + end.format('MMM D, YYYY'));
          }

          $('#js-daterangepicker-predefined').daterangepicker({
            startDate: start,
            endDate: end,
            ranges: {
              'Today': [moment(), moment()],
              'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
              'Last 7 Days': [moment().subtract(6, 'days'), moment()],
              'Last 30 Days': [moment().subtract(29, 'days'), moment()],
              'This Month': [moment().startOf('month'), moment().endOf('month')],
              'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            }
          }, cb);

          // cb(start, end);
        });
     </script>

     @stack('scripts')

     <!-- Notyf Notification System -->
     <script src="https://cdn.jsdelivr.net/npm/notyf@3/notyf.min.js"></script>
     <script>
         // Initialize Notyf with custom configuration
         window.notyf = new Notyf({
             duration: 4000,
             position: {
                 x: 'right',
                 y: 'top'
             },
             types: [
                 {
                     type: 'warning',
                     background: 'linear-gradient(135deg, #ffc107 0%, #fd7e14 100%)',
                     icon: {
                         className: 'bi bi-exclamation-triangle',
                         tagName: 'i',
                         color: '#212529'
                     }
                 },
                 {
                     type: 'info',
                     background: 'linear-gradient(135deg, #17a2b8 0%, #007bff 100%)',
                     icon: {
                         className: 'bi bi-info-circle',
                         tagName: 'i',
                         color: '#fff'
                     }
                 }
             ]
         });

         // Global notification functions
         window.showNotification = {
             success: function(message) {
                 notyf.success(message);
             },
             error: function(message) {
                 notyf.error(message);
             },
             warning: function(message) {
                 notyf.open({
                     type: 'warning',
                     message: message
                 });
             },
             info: function(message) {
                 notyf.open({
                     type: 'info',
                     message: message
                 });
             }
         };

         // Show Laravel flash messages
         @if(session('success'))
             showNotification.success('{{ session('success') }}');
         @endif

         @if(session('error'))
             showNotification.error('{{ session('error') }}');
         @endif

         @if(session('warning'))
             showNotification.warning('{{ session('warning') }}');
         @endif

         @if(session('info'))
             showNotification.info('{{ session('info') }}');
         @endif
     </script>

  <!-- End Style Switcher JS -->
</body>
</html>

