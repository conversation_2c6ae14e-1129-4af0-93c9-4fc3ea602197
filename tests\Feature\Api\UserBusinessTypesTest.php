<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\BusinessType;

use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UserBusinessTypesTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create(['email' => '<EMAIL>']);

        Sanctum::actingAs($user, [], 'web');

        $this->seed(\Database\Seeders\PermissionsSeeder::class);

        $this->withoutExceptionHandling();
    }

    /**
     * @test
     */
    public function it_gets_user_business_types()
    {
        $user = User::factory()->create();
        $businessType = BusinessType::factory()->create();

        $user->businessTypes()->attach($businessType);

        $response = $this->getJson(
            route('api.users.business-types.index', $user)
        );

        $response->assertOk()->assertSee($businessType->name);
    }

    /**
     * @test
     */
    public function it_can_attach_business_types_to_user()
    {
        $user = User::factory()->create();
        $businessType = BusinessType::factory()->create();

        $response = $this->postJson(
            route('api.users.business-types.store', [$user, $businessType])
        );

        $response->assertNoContent();

        $this->assertTrue(
            $user
                ->businessTypes()
                ->where('business_types.id', $businessType->id)
                ->exists()
        );
    }

    /**
     * @test
     */
    public function it_can_detach_business_types_from_user()
    {
        $user = User::factory()->create();
        $businessType = BusinessType::factory()->create();

        $response = $this->deleteJson(
            route('api.users.business-types.store', [$user, $businessType])
        );

        $response->assertNoContent();

        $this->assertFalse(
            $user
                ->businessTypes()
                ->where('business_types.id', $businessType->id)
                ->exists()
        );
    }
}
