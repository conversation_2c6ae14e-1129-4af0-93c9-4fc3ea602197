<?php
namespace App\Http\Controllers\Api;

use App\Models\User;
use Illuminate\Http\Request;
use App\Models\BusinessType;
use App\Http\Controllers\Controller;
use App\Http\Resources\UserCollection;

class BusinessTypeUsersController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\BusinessType $businessType
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, BusinessType $businessType)
    {
        $this->authorize('view', $businessType);

        $search = $request->get('search', '');

        $users = $businessType
            ->users()
            ->search($search)
            ->latest()
            ->paginate();

        return new UserCollection($users);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\BusinessType $businessType
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function store(
        Request $request,
        BusinessType $businessType,
        User $user
    ) {
        $this->authorize('update', $businessType);

        $businessType->users()->syncWithoutDetaching([$user->id]);

        return response()->noContent();
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\BusinessType $businessType
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function destroy(
        Request $request,
        BusinessType $businessType,
        User $user
    ) {
        $this->authorize('update', $businessType);

        $businessType->users()->detach($user);

        return response()->noContent();
    }
}
