@extends('layouts.app')

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">credit-card</x-slot>
        <x-slot name="title">Edit Payment</x-slot>
        <x-slot name="subtitle">Update payment information and details</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('payments.index') }}">Payments</a></li>
            <li class="breadcrumb-item"><a href="{{ route('payments.show', $payment) }}">Payment #{{ $payment->id }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
        </x-slot>
        <x-slot name="controls">
            <div class="d-flex gap-2">
                <a href="{{ route('payments.show', $payment) }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-eye me-1"></i>View Payment
                </a>
                <a href="{{ route('payments.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Payments
                </a>
            </div>
        </x-slot>
    </x-page-header>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-pencil me-2"></i>Edit Payment Information
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Payment Summary (Read-only) -->
                    <div class="alert alert-info">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Payment ID:</strong> #{{ $payment->id }}<br>
                                <strong>Amount:</strong> {{ _money($payment->amount) }}<br>
                                <strong>Type:</strong> {{ $payment->payment_type }}
                            </div>
                            <div class="col-md-6">
                                <strong>Date:</strong> {{ $payment->created_at->format('M d, Y h:i A') }}<br>
                                @if($payment->paymentable)
                                <strong>Related:</strong> {{ $payment->payment_type }} #{{ $payment->paymentable_id }}
                                @endif
                            </div>
                        </div>
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            Note: Payment amount and related invoice/order cannot be changed. Only reference and description details can be updated.
                        </small>
                    </div>

                    <form action="{{ route('payments.update', $payment) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Reference and Description -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">Reference Number</label>
                                <input type="text" class="form-control @error('reference_no') is-invalid @enderror" 
                                       name="reference_no" value="{{ old('reference_no', $payment->reference_no) }}" 
                                       placeholder="Payment reference number">
                                @error('reference_no')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">Currency</label>
                                <div class="tom-select-custom">
                                    <select class="js-select form-select" name="currency_id" autocomplete="off" 
                                            data-hs-tom-select-options='{"placeholder": "Select currency..."}'>
                                        <option value="">Default Currency</option>
                                        @foreach($currencies as $currency)
                                            <option value="{{ $currency->id }}" 
                                                {{ old('currency_id', $payment->currency_id) == $currency->id ? 'selected' : '' }}>
                                                {{ $currency->name }} ({{ $currency->code }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                @error('currency_id')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <label class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      name="description" rows="3" 
                                      placeholder="Payment description or notes">{{ old('description', $payment->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Comments -->
                        <div class="mb-4">
                            <label class="form-label">Internal Comments</label>
                            <textarea class="form-control @error('comment') is-invalid @enderror" 
                                      name="comment" rows="2" 
                                      placeholder="Internal comments (not visible to customer)">{{ old('comment', $payment->comment) }}</textarea>
                            @error('comment')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="{{ route('payments.show', $payment) }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-1"></i>Update Payment
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Related Record Information (Read-only) -->
            @if($payment->paymentable)
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-link-45deg me-2"></i>Related {{ $payment->payment_type }} (Read-only)
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">{{ $payment->payment_type }} ID</label>
                                <div class="fw-bold">#{{ $payment->paymentable->id }}</div>
                            </div>

                            @if($payment->paymentable->customer)
                            <div class="mb-3">
                                <label class="form-label text-muted">Customer</label>
                                <div class="fw-semibold">{{ $payment->paymentable->customer->name }}</div>
                            </div>
                            @endif

                            <div class="mb-3">
                                <label class="form-label text-muted">Total Amount</label>
                                <div class="fw-semibold">{{ _money($payment->paymentable->amount_total) }}</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Amount Paid</label>
                                <div class="fw-semibold text-success">{{ _money($payment->paymentable->amount_paid) }}</div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">Outstanding Balance</label>
                                <div class="fw-semibold {{ $payment->paymentable->balance > 0 ? 'text-danger' : 'text-success' }}">
                                    {{ _money($payment->paymentable->balance) }}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <div>
                                    @if($payment->paymentable->balance <= 0)
                                        <span class="badge bg-success">Fully Paid</span>
                                    @elseif($payment->paymentable->amount_paid > 0)
                                        <span class="badge bg-warning">Partially Paid</span>
                                    @else
                                        <span class="badge bg-danger">Unpaid</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end">
                        @if($payment->paymentable_type === 'App\Models\Invoice')
                            <a href="{{ route('invoices.show', $payment->paymentable) }}" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-eye me-1"></i>View Invoice
                            </a>
                        @elseif($payment->paymentable_type === 'App\Models\Order')
                            <a href="{{ route('orders.show', $payment->paymentable) }}" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-eye me-1"></i>View Order
                            </a>
                        @endif
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize Tom Select dropdowns
    if (typeof HSCore !== 'undefined' && HSCore.components && HSCore.components.HSTomSelect) {
        HSCore.components.HSTomSelect.init('.js-select');
    }
});
</script>
@endpush
@endsection
