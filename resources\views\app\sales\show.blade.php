@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">receipt</x-slot>
        <x-slot name="title">Sale Details</x-slot>
        <x-slot name="subtitle">View complete information about this sale</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('sales.index') }}">Sales</a></li>
            <li class="breadcrumb-item active" aria-current="page">View Sale</li>
        </x-slot>
        <x-slot name="controls">
            @can('update', $sale)
            <a href="{{ route('sales.edit', $sale) }}" class="btn btn-primary btn-sm me-2">
                <i class="bi bi-pencil-square me-1"></i> Edit Sale
            </a>
            @endcan
            <a href="{{ route('sales.index') }}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-left me-1"></i> Back to Sales
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <!-- Sale Overview Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-info-circle me-2"></i>Sale Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar avatar-xl avatar-soft-primary avatar-circle mb-3">
                            <span class="avatar-initials">{{ substr($sale->product_name ?? 'S', 0, 1) }}</span>
                        </div>
                        <h4 class="mb-1">{{ $sale->product_name ?? '-' }}</h4>
                        <p class="text-muted">Sale ID: {{ $sale->id }}</p>
                    </div>

                    <div class="mb-4">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Date:</span>
                            <span>{{ $sale->created_at->format('M d, Y') }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Time:</span>
                            <span>{{ $sale->created_at->format('h:i A') }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Status:</span>
                            <span class="badge bg-success">Completed</span>
                        </div>
                    </div>

                    <hr>

                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                            <i class="bi bi-printer me-1"></i> Print Receipt
                        </button>
                        <button type="button" class="btn btn-outline-info" id="downloadPdfBtn">
                            <i class="bi bi-file-earmark-pdf me-1"></i> Download PDF
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Details Card -->
        <div class="col-lg-8 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-box-seam me-2"></i>Product & Pricing Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <h6 class="text-muted mb-3">Product Information</h6>
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-box text-primary" style="font-size: 1.5rem;"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-0">Product Name</h6>
                                    <p class="mb-0">{{ $sale->product_name ?? '-' }}</p>
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-tag text-primary" style="font-size: 1.5rem;"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-0">Product ID</h6>
                                    <p class="mb-0">{{ $sale->product_id ?? '-' }}</p>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-info-circle text-primary" style="font-size: 1.5rem;"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-0">Product Description</h6>
                                    <p class="mb-0">{{ optional($sale->product)->description ?? 'No description available' }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <h6 class="text-muted mb-3">Unit Information</h6>
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-rulers text-info" style="font-size: 1.5rem;"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-0">Unit Name</h6>
                                    <p class="mb-0">{{ $sale->unit_name ?? '-' }}</p>
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-123 text-info" style="font-size: 1.5rem;"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-0">Unit ID</h6>
                                    <p class="mb-0">{{ $sale->unit_id ?? '-' }}</p>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-boxes text-info" style="font-size: 1.5rem;"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-0">Quantity</h6>
                                    <p class="mb-0">{{ $sale->quantity ?? '-' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <h6 class="text-muted mb-3">Pricing Information</h6>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>Unit Price</th>
                                            <th>Quantity</th>
                                            <th>Subtotal</th>
                                            <th>Discount</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>{{ _money($sale->price) }}</td>
                                            <td>{{ $sale->quantity }}</td>
                                            <td>{{ _money($sale->price * $sale->quantity) }}</td>
                                            <td>{{ _money($sale->discount) }}</td>
                                            <td class="fw-bold">{{ _money(($sale->price * $sale->quantity) - $sale->discount) }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information Card -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-journal-text me-2"></i>Additional Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3 mb-md-0">
                            <h6 class="text-muted">Created By</h6>
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-circle">
                                    <span class="avatar-initials">{{ substr(optional($sale->createdBy)->name ?? 'U', 0, 1) }}</span>
                                </div>
                                <div class="ms-3">
                                    <h6 class="mb-0">{{ optional($sale->createdBy)->name ?? 'System' }}</h6>
                                    <span class="text-muted small">{{ $sale->created_at->diffForHumans() }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3 mb-md-0">
                            <h6 class="text-muted">Last Updated</h6>
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-soft-info avatar-circle">
                                    <span class="avatar-initials"><i class="bi bi-clock-history"></i></span>
                                </div>
                                <div class="ms-3">
                                    <h6 class="mb-0">{{ $sale->updated_at->format('M d, Y h:i A') }}</h6>
                                    <span class="text-muted small">{{ $sale->updated_at->diffForHumans() }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <h6 class="text-muted">Notes</h6>
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-soft-warning avatar-circle">
                                    <span class="avatar-initials"><i class="bi bi-sticky"></i></span>
                                </div>
                                <div class="ms-3">
                                    <p class="mb-0">{{ $sale->notes ?? 'No additional notes' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="d-flex justify-content-between mt-2 mb-4">
        <div>
            <a href="{{ route('sales.index') }}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left me-1"></i> Back to Sales
            </a>

            @can('create', App\Models\Sale::class)
            <a href="{{ route('sales.create') }}" class="btn btn-outline-primary">
                <i class="bi bi-plus-circle me-1"></i> Create New Sale
            </a>
            @endcan
        </div>

        <div>
            @can('update', $sale)
            <a href="{{ route('sales.edit', $sale) }}" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> Edit
            </a>
            @endcan
            
            @can('delete', $sale)
            <form action="{{ route('sales.destroy', $sale) }}" method="POST" class="d-inline" 
                  onsubmit="return confirm('Are you sure you want to delete this sale? This action cannot be undone.')">
                @csrf @method('DELETE')
                <button type="submit" class="btn btn-danger">
                    <i class="bi bi-trash me-1"></i> Delete
                </button>
            </form>
            @endcan
        </div>
    </div>
</div>
<!-- End Content -->

@push('scripts')
<script>
    // This is a placeholder for PDF generation functionality
    document.getElementById('downloadPdfBtn').addEventListener('click', function() {
        alert('PDF download functionality will be implemented here.');
    });
</script>
@endpush

@endsection
