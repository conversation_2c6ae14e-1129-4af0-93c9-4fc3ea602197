<?php

namespace Database\Factories;

use App\Models\Invoice;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class InvoiceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Invoice::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'date_from' => $this->faker->date,
            'date_to' => $this->faker->date,
            'amount_total' => $this->faker->randomNumber(1),
            'amount_paid' => $this->faker->randomNumber(1),
            'discount' => $this->faker->randomFloat(2, 0, 9999),
            'vat' => $this->faker->randomNumber(1),
            'description' => $this->faker->sentence(15),
            'created_by' => \App\Models\User::factory(),
            'updated_by' => \App\Models\User::factory(),
            'status_id' => \App\Models\Status::factory(),
            'approved_by' => \App\Models\User::factory(),
        ];
    }
}
