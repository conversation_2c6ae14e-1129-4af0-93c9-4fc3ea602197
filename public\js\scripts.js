function initDataTable(el){


    // INITIALIZATION OF DATATABLES
    // =======================================================
    HSCore.components.HSDatatables.init($('#datatable'), {
      select: {
        style: 'multi',
        selector: 'td:first-child input[type="checkbox"]',
        classMap: {
          checkAll: '#datatableCheckAll',
          counter: '#datatableCounter',
          counterInfo: '#datatableCounterInfo'
        }
      },
      language: {
        zeroRecords: `<div class="text-center p-4">
              <img class="mb-3" src="./assets/svg/illustrations/oc-error.svg" alt="Image Description" style="width: 10rem;" data-hs-theme-appearance="default">
              <img class="mb-3" src="./assets/svg/illustrations-light/oc-error.svg" alt="Image Description" style="width: 10rem;" data-hs-theme-appearance="dark">
            <p class="mb-0">No data to show</p>
            </div>`
      }
    });

    const datatable = HSCore.components.HSDatatables.getItem(0)

    document.querySelectorAll('.js-datatable-filter').forEach(function (item) {
      item.addEventListener('change',function(e) {
        const elVal = e.target.value,
    targetColumnIndex = e.target.getAttribute('data-target-column-index'),
    targetTable = e.target.getAttribute('data-target-table');

    HSCore.components.HSDatatables.getItem(targetTable).column(targetColumnIndex).search(elVal !== 'null' ? elVal : '').draw()
      })
    })

  // =======================================================
  // HSCore.components.HSDatatables.init($(el), {
  //   dom: 'Bfrtip',
  //   buttons: [
  //     {
  //       extend: 'copy',
  //       className: 'd-none'
  //     },
  //     {
  //       extend: 'excel',
  //       className: 'd-none'
  //     },
  //     {
  //       extend: 'csv',
  //       className: 'd-none'
  //     },
  //     {
  //       extend: 'pdf',
  //       className: 'd-none'
  //     },
  //     {
  //       extend: 'print',
  //       className: 'd-none'
  //     },
  //   ],
  //   select: {
  //     style: 'multi',
  //     selector: 'td:first-child input[type="checkbox"]',
  //     classMap: {
  //       checkAll: '#datatableCheckAll',
  //       counter: '#datatableCounter',
  //       counterInfo: '#datatableCounterInfo'
  //     }
  //   },
  //   language: {
  //     zeroRecords: `
  //     <div class="text-center p-4">
  //       <img class="mb-3" src="/assets/svg/illustrations/oc-error.svg" alt="Image Description" style="width: 10rem;" data-hs-theme-appearance="default">
  //       <img class="mb-3" src="/assets/svg/illustrations-light/oc-error.svg" alt="Image Description" style="width: 10rem;" data-hs-theme-appearance="dark">
  //       <p class="mb-0">No data to show</p>
  //     </div>`
  //   }
  // })

  // const datatable = HSCore.components.HSDatatables.getItem(0)

  // $('#export-copy').click(function() {
  //   datatable.button('.buttons-copy').trigger()
  // });

  // $('#export-excel').click(function() {
  //   datatable.button('.buttons-excel').trigger()
  // });

  // $('#export-csv').click(function() {
  //   datatable.button('.buttons-csv').trigger()
  // });

  // $('#export-pdf').click(function() {
  //   datatable.button('.buttons-pdf').trigger()
  // });

  // $('#export-print').click(function() {
  //   datatable.button('.buttons-print').trigger()
  // });

  // $('.js-datatable-filter').on('change', function() {
  //   var $this = $(this),
  //     elVal = $this.val(),
  //     targetColumnIndex = $this.data('target-column-index');

  //   if (elVal === 'null') elVal = ''

  //   datatable.column(targetColumnIndex).search(elVal).draw();
  // });
}

// $("document").ready( function() {
//   // INITIALIZATION OF DATATABLES
//   window.onload = function () {
    

//     // INITIALIZATION OF NAVBAR VERTICAL ASIDE
//     // =======================================================
//     new HSSideNav('.js-navbar-vertical-aside').init()


//     // INITIALIZATION OF FORM SEARCH
//     // =======================================================
//     new HSFormSearch('.js-form-search')


//     // INITIALIZATION OF BOOTSTRAP DROPDOWN
//     // =======================================================
//     HSBsDropdown.init()


//     // INITIALIZATION OF SELECT
//     // =======================================================
//     HSCore.components.HSTomSelect.init('.js-select')


//     // INITIALIZATION OF INPUT MASK
//     // =======================================================
//     HSCore.components.HSMask.init('.js-input-mask')


//     // INITIALIZATION OF NAV SCROLLER
//     // =======================================================
//     new HsNavScroller('.js-nav-scroller')


//     // INITIALIZATION OF COUNTER
//     // =======================================================
//     new HSCounter('.js-counter')


//     // INITIALIZATION OF TOGGLE PASSWORD
//     // =======================================================
//     new HSTogglePassword('.js-toggle-password')


//     // INITIALIZATION OF FILE ATTACHMENT
//     // =======================================================
//     new HSFileAttach('.js-file-attach')
//   }

//   // STYLE SWITCHER
//   // =======================================================
//   const $dropdownBtn = document.getElementById('selectThemeDropdown') // Dropdowon trigger
//   const $variants = document.querySelectorAll(`[aria-labelledby="selectThemeDropdown"] [data-icon]`) // All items of the dropdown

//   // Function to set active style in the dorpdown menu and set icon for dropdown trigger
//   const setActiveStyle = function () {
//     $variants.forEach($item => {
//       if ($item.getAttribute('data-value') === HSThemeAppearance.getOriginalAppearance()) {
//         $dropdownBtn.innerHTML = `<i class="${$item.getAttribute('data-icon')}" />`
//         return $item.classList.add('active')
//       }

//       $item.classList.remove('active')
//     })
//   }

//   // Add a click event to all items of the dropdown to set the style
//   $variants.forEach(function ($item) {
//     $item.addEventListener('click', function () {
//       HSThemeAppearance.setAppearance($item.getAttribute('data-value'))
//     })
//   })

//   // Call the setActiveStyle on load page
//   setActiveStyle()

//   // Add event listener on change style to call the setActiveStyle function
//   window.addEventListener('on-hs-appearance-change', function () {
//     setActiveStyle()
//   })

// });









function canvasPrint(id){

    $(".hide-print").hide();
    setTimeout(()=>{ 
        $(".hide-print").show(); 
    }, 2000);

    const screenshotTarget = document.getElementById(id);
    html2canvas(screenshotTarget, {
        // backgroundColor: "white",
        color:'grey', 
        removeContainer: true, 
        x: 0, y: 0,
        // width:320
    }).then((canvas) => {
        var base64image = canvas.toDataURL("image/png");
        return Canvas2Image.saveAsPNG(canvas,5000,5000);
        var img = Canvas2Image.convertToImage(canvas, 800, 800);
        // var doc = new jsPDF("p", "pt", "a4");
        // doc.addImage(base64image, 'PNG', 5, 0);
        // doc.save('codes.pdf');
    });

}


function createPDFfromHTML(name) {
    var HTML_Width = $("." + name ).width()*5
    var HTML_Height = $("." + name ).height()*5;
    var top_left_margin = 15;
    var PDF_Width = HTML_Width + (top_left_margin * 2);
    var PDF_Height = (PDF_Width * 1.5) + (top_left_margin * 2);
    var canvas_image_width = HTML_Width;
    var canvas_image_height = HTML_Height;
    var totalPDFPages = Math.ceil(HTML_Height / PDF_Height) - 1;
    html2canvas($("." + name )[0]).then(function (canvas) {
        var imgData = canvas.toDataURL("image/jpeg", 1.0);
        var pdf = new jsPDF('p', 'pt', [PDF_Width, PDF_Height]);
        pdf.addImage(imgData, 'JPG', top_left_margin, top_left_margin, canvas_image_width, canvas_image_height);
        for (var i = 1; i <= totalPDFPages; i++) { 
            pdf.addPage(PDF_Width, PDF_Height);
            pdf.addImage(imgData, 'JPG', top_left_margin, -(PDF_Height*i)+(top_left_margin*4),canvas_image_width,canvas_image_height);
        }
        pdf.save(name + ".pdf");
    });
}


function getImages(event){
    console.log(event)
    var file =  event.target.files[0];
    var src = URL.createObjectURL(file);
    $(".viewer").attr("src", src);
}


function elFilter(el){
    var value = ".el-" + el;
    value = value.toLowerCase();
    $(".el-" + el ).filter(function() {
      $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
    });
}


function extract(element, action="newWindow"){
      // Overide Defaults
      let pdfConfig = {
            target: 'body',
            pageTarget: element,
            captureHiddenClass: 'vti__hidden',
            captureShowClass: 'vti__show',
            captureActiveClass: 'vti__active',
            title: 'Transcript',
            author: 'Mary-Queen-Of-Peace',
            maxItems: 20,
            fileNameSuffix: new Date().getTime(),
            pageWrapper: '.container',
            padding: 5,
            devStyle: false,
            pageHeight: null, // 612 for letter
            pageWidth: null, // 792 for letter
            pageUnits: 'pt',
            returnAction: action // blob, base64, clipboard, newWindow, download
            // callback: (img) => { console.log(img) }
      }
      vue2img().pdf(pdfConfig);
}

function extractPrint(data, type='html'){
    
    $('.modal').modal('hide') // closes all active pop ups.
    $('.modal-backdrop').remove() // removes the grey overlay.

    printJS({
        printable: data,
        type: type,
        showModal:true,
        targetStyles: "*",
        style: `@page { margin: 5mm; font-size: 20px; size: 80m 200m; transform: scaleY(1.2); text-transform: uppercase!important; } @media print { body { font-size: 20px; transform: scaleY(1.2); text-transform: uppercase!important; } }`,
        // style: `@page { margin: 5mm; size: 80m 80m; } @media print { body { margin: 0mm; width: 80mm; height: 80mm } }`,
    });
      // printJS({
      //       printable: data,
      //       type: type,
      //       scanStyles: true,
      //       // base64: true,
      //       showModal:true,
      //       honorColor: true,
      //       header: '',
      //       targetStyles: "*",
      // });
}

function canvas2(id){
    const screenshotTarget = document.getElementById(id);
    html2canvas(screenshotTarget).then((canvas) => {
        const base64image = canvas.toDataURL("image/png");
        $("#preview").attr("href", base64image);
        $("#preview").click()
    });
}

function dataTableBtn(el = ".js-datatable", options = null, orderBy= [2, 'desc']) {

    var $options =  {
        dom: 'Bflrtip',
        "responsive": true, 
        "lengthChange": true, 
        "autoWidth": true,
        // "lengthMenu":[10,20,30, -1],
        "lengthMenu": [ [10, 25, 50,100, 500, -1], [10, 25, 50,100,500, "All"] ],
        "buttons": [
            {extend:"copy", className:"btn btn-sm btn-light", titleAttr: 'Copy', text: 'Copy', footer: true}, 
            {extend:"csv", className:"btn btn-sm btn-light", titleAttr: 'Export in csv', text: 'CSV', footer: true}, 
            {extend:"excel", className:"btn btn-sm btn-light", titleAttr: 'Export in excel', text: 'Excel', footer: true}, 
            {extend:"pdf", className:"btn btn-sm btn-light", titleAttr: 'Export in pdf', text: 'PDF', footer: true}, 
            {extend:"print", className:"btn btn-sm btn-light", titleAttr: 'print', text: 'Print', footer: true}, 
            // {extend:'colvis', postfixButtons: ['colvisRestore']},
            // "csv",  
            // "excel", 
            // "pdf", 
            // "print"
        ],
        // "dom": '<"top"f>rt<"bottom"lp><"clear">',
        order: [orderBy],
        "fnDrawCallback":function(){
            $("input[type='search']").attr("class", "form-control input-sm");
            $("select").attr("class", "form-control input-sm");
        }
    };

    // Overriding Defaults
    if( options ) {
        for(opt of options) {
            $options[opt] = options[opt];
        }        
    }

    var table = HSCore.components.HSDatatables.init(el, $options)   
    

    // $.noConflict();
    // $(el).DataTable({
    //     dom: 'Bflrtip',
    //     // dom: 'Blfrtip',
    //     "responsive": true, 
    //     "lengthChange": true, 
    //     "autoWidth": true,
    //     // "lengthMenu":[10,20,30, -1],
    //     "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
    //     // "buttons": ["copy", "csv", "excel", "pdf", "print"]
    // })
    // .buttons();
    // .appendTo('#example1_wrapper .col-md-6:eq(0)');

    // $('#example2').DataTable({
    //   "paging": true,
    //   "lengthChange": false,
    //   "searching": false,
    //   "ordering": true,
    //   "info": true,
    //   "autoWidth": false,
    //   "responsive": true,
    // });
};

function dataTable(el = ".js-datatable") {
    // $.noConflict();
    var table = $(el).DataTable({
        dom: 'Bfrtip',
        "responsive": true, 
        // "lengthChange": false, 
        // "autoWidth": false,
        // "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"]
    });

  
};
