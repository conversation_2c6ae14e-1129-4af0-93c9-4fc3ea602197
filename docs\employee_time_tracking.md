Module: Employee Time and Task Tracking

Objective: Create a system to track employee check-in and check-out times, record tasks performed, calculate regular and overtime hours based on configurable work hours, and allow employees to add comments for each day's record.

Functional Requirements:

Check-In and Check-Out:
Employees can log their check-in and check-out times for each workday.
The system records the exact timestamp (date and time) for both events.
Task Logging:
Employees can specify tasks they worked on during the day.
Each task entry includes a description and the time spent on it.
Work Hours Configuration:
<PERSON><PERSON> can configure standard work hours (e.g., 9:00 AM to 5:00 PM).
Hours worked outside this range are automatically classified as overtime.
Overtime Calculation:
The system calculates total hours worked as the difference between check-in and check-out times.
Regular hours are within the configured work hours; any additional hours are recorded as overtime.
Daily Comments:
Employees can add a comment to their daily record (e.g., notes about the day's work).
Data Storage:
The system stores check-in/check-out times, tasks, regular hours, overtime hours, and comments in a database.
Each record is associated with an employee ID and date.
User Interface:
Provide a simple interface (e.g., web or command-line) for employees to log check-in/check-out, tasks, and comments.
<PERSON><PERSON> can view reports of employee hours and tasks.
Validation:
Ensure check-out time is after check-in time.
Prevent duplicate check-in or check-out entries for the same day.
Validate that task descriptions are non-empty and time spent is positive.