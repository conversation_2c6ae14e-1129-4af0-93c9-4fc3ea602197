<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Tenant;
use App\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        Tenant::checkCurrent()
           ? $this->runTenantSpecificSeeders()
           : $this->runLandlordSpecificSeeders();
    }

    public function runTenantSpecificSeeders()
    {
        // run tenant specific seeders

        $current = Tenant::current();
        if(\App\Models\User::count() )  return false;
        // Adding an admin user
        $userData = [
            'name' => $current->name,
            'email' => $current->email,
            'password' => $current->password,
            'status_id' => 1,
            'branch_id' => 1,
            'phone' => $current->phone,
            'business_type_id' => 1,
            'warehouse_id' => 1,
            'password' => Hash::make($current->email),
        ];
        
        \App\Models\Status::create(['id' => 1, 'name' => 'Active']);
        \App\Models\Status::create(['id' => 2, 'name' => 'Inactive']);

        $this->call(PermissionsSeeder::class);

        $user = \App\Models\User::updateOrCreate($userData);
        $adminRole = Role::where("name",'super-admin')->first();
        if($adminRole) $user->assignRole($adminRole);
        // All permissions are now consolidated in PermissionsSeeder
        // $this->call(ChartOfAccountsSeeder::class);
        // $this->call(FiscalYearSeeder::class);
        // $this->call(CurrencySeeder::class);
        // $this->call(AssetCategorySeeder::class);
        // $this->call(TaxTypeSeeder::class);
        $this->call(PayrollPermissionsSeeder::class);

        // GENERAL
        \App\Models\Status::create(['id' => 10, 'name' => 'Approved']);
        \App\Models\Status::create(['id' => 11, 'name' => 'Pending']);
        \App\Models\Status::create(['id' => 12, 'name' => 'Rejected']);
        \App\Models\Status::create(['id' => 13, 'name' => 'Canceled']);
        \App\Models\Status::create(['id' => 14, 'name' => 'Adjusted']);

        // DEFAULTS
        \App\Models\Customer::create(['id' => 1, 'name' => 'Walk in Customer']);
        \App\Models\BusinessType::create(['id' => 1, 'name' => 'Default']);
        \App\Models\Supplier::create(['id' => 1, 'name' => 'Default Supplier']);
        \App\Models\Branch::create(['id' => 1, 'name' => 'Default Branch']);
        \App\Models\Location::create(['id' => 1, 'name' => 'Default Location']);


        // Seed sample data if needed
        $this->call(WarehouseSeeder::class);
        $this->call(LocationPermissionsSeeder::class);
        $this->call(LocationSeeder::class);
        $this->call(CurrencySeeder::class);

    }

    public function runLandlordSpecificSeeders()
    {
        // run landlord specific seeders
        // \App\Models\Tenant::updateOrcreate(['domain' => "one", 'name' => 'One Account', "database" => "accounting_one"]);
        // \App\Models\Tenant::updateOrcreate(['domain' => "two", 'name' => 'Two Account', "database" => "accounting_two"]);
        // \App\Models\Tenant::updateOrcreate(['domain' => "three", 'name' => 'Three Account', "database" => "accounting_three"]);
    }


}
