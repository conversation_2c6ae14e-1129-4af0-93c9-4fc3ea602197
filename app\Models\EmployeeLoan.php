<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use Carbon\Carbon;

class EmployeeLoan extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'employee_id',
        'loan_reference',
        'loan_amount',
        'installment_amount',
        'remaining_balance',
        'total_paid',
        'total_interest_paid',
        'total_interest_due',
        'total_installments',
        'installments_paid',
        'loan_date',
        'first_deduction_date',
        'expected_completion_date',
        'actual_completion_date',
        'status',
        'purpose',
        'terms_conditions',
        'interest_rate',
        'interest_type',
        'approval_notes',
        'approved_by',
        'approved_at',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'loan_date' => 'date',
        'first_deduction_date' => 'date',
        'expected_completion_date' => 'date',
        'actual_completion_date' => 'date',
        'approved_at' => 'datetime',
        'loan_amount' => 'decimal:2',
        'installment_amount' => 'decimal:2',
        'remaining_balance' => 'decimal:2',
        'total_paid' => 'decimal:2',
        'total_interest_paid' => 'decimal:2',
        'total_interest_due' => 'decimal:2',
        'interest_rate' => 'decimal:2',
    ];

    protected $searchableFields = ['*'];

    protected $appends = ['progress_percentage', 'is_active', 'next_payment_date', 'total_interest'];

    /**
     * Relationship with Employee
     */
    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Relationship with User who approved the loan
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Relationship with User who created the loan
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship with User who last updated the loan
     */
    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the progress percentage of the loan
     */
    public function getProgressPercentageAttribute()
    {
        if ($this->loan_amount <= 0) {
            return 0;
        }
        return round(($this->total_paid / $this->loan_amount) * 100, 2);
    }

    /**
     * Check if the loan is currently active
     */
    public function getIsActiveAttribute()
    {
        return $this->status === 'active';
    }

    /**
     * Get the next payment date
     */
    public function getNextPaymentDateAttribute()
    {
        if ($this->status !== 'active') {
            return null;
        }

        $nextMonth = Carbon::parse($this->first_deduction_date)
            ->addMonths($this->installments_paid);

        return $nextMonth;
    }

    /**
     * Generate a unique loan reference
     */
    public static function generateLoanReference()
    {
        $year = date('Y');
        $month = date('m');
        $lastLoan = self::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastLoan ? (int)substr($lastLoan->loan_reference, -4) + 1 : 1;

        return 'LOAN-' . $year . $month . '-' . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate expected completion date
     */
    public function calculateExpectedCompletionDate()
    {
        if ($this->first_deduction_date && $this->total_installments) {
            return Carbon::parse($this->first_deduction_date)
                ->addMonths($this->total_installments - 1);
        }
        return null;
    }

    /**
     * Process a loan payment (principal only - interest is tracked separately)
     */
    public function processPayment($principalAmount)
    {
        $this->total_paid += $principalAmount;
        $this->remaining_balance -= $principalAmount;
        $this->installments_paid += 1;

        // Ensure remaining balance doesn't go negative
        if ($this->remaining_balance < 0) {
            $this->remaining_balance = 0;
        }

        // Check if loan is fully paid
        $wasCompleted = false;
        if ($this->remaining_balance <= 0 || $this->installments_paid >= $this->total_installments) {
            $this->status = 'completed';
            $this->actual_completion_date = now();
            $wasCompleted = true;
        }

        $this->save();

        // Send completion notification if loan was just completed
        if ($wasCompleted) {
            try {
                $notificationService = app(\App\Services\NotificationService::class);
                $notificationService->sendLoanCompletionNotification($this);
            } catch (\Exception $e) {
                \Log::error("Failed to send loan completion notification for loan {$this->loan_reference}: " . $e->getMessage());
            }
        }
    }

    /**
     * Approve the loan
     */
    public function approve($approvedBy, $notes = null)
    {
        $this->status = 'approved';
        $this->approved_by = $approvedBy;
        $this->approved_at = now();
        $this->approval_notes = $notes;
        $this->expected_completion_date = $this->calculateExpectedCompletionDate();
        $this->save();
    }

    /**
     * Activate the loan (start deductions)
     */
    public function activate()
    {
        if ($this->status === 'approved') {
            $this->status = 'active';
            $this->save();
        }
    }

    /**
     * Cancel the loan
     */
    public function cancel($reason = null)
    {
        $this->status = 'cancelled';
        $this->approval_notes = $reason;
        $this->save();
    }

    /**
     * Scope for active loans
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for pending loans
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for completed loans
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Calculate monthly interest amount for this loan
     */
    public function calculateMonthlyInterest()
    {
        // If no interest rate is set, return 0
        if (!$this->interest_rate || $this->interest_rate <= 0) {
            return 0;
        }

        $monthlyInterestRate = $this->interest_rate / 100 / 12; // Convert annual percentage to monthly decimal

        if ($this->interest_type === 'simple') {
            // Simple interest: calculated on original loan amount
            return round($this->loan_amount * $monthlyInterestRate, 2);
        } else {
            // Compound interest: calculated on remaining balance
            return round($this->remaining_balance * $monthlyInterestRate, 2);
        }
    }

    /**
     * Calculate payment breakdown for current month
     *
     * UPDATED: installment_amount now represents principal payment only.
     * Interest is calculated separately and not included in installment.
     */
    public function getPaymentBreakdown()
    {
        $interestAmount = $this->calculateMonthlyInterest();

        // installment_amount now represents the principal payment only
        $principalAmount = $this->installment_amount;

        // Ensure principal doesn't exceed remaining balance
        if ($principalAmount > $this->remaining_balance) {
            $principalAmount = $this->remaining_balance;
        }

        // Ensure principal is not negative
        $principalAmount = max(0, $principalAmount);

        return [
            'principal' => round($principalAmount, 2),
            'interest' => round($interestAmount, 2),
            'total' => round($principalAmount + $interestAmount, 2)
        ];
    }

    /**
     * Get total interest that will be paid over the life of the loan
     */
    public function getTotalInterestAttribute()
    {
        if (!$this->interest_rate || $this->interest_rate <= 0) {
            return 0;
        }

        if ($this->interest_type === 'simple') {
            // Simple interest: Interest = Principal × Rate × Time
            $monthlyInterest = $this->calculateMonthlyInterest();
            return round($monthlyInterest * $this->total_installments, 2);
        } else {
            // For compound interest, we need to calculate month by month
            // UPDATED: installment_amount is now principal only, interest calculated separately
            $totalInterest = 0;
            $remainingBalance = $this->loan_amount;
            $monthlyRate = $this->interest_rate / 100 / 12;

            for ($i = 0; $i < $this->total_installments && $remainingBalance > 0; $i++) {
                $monthlyInterest = $remainingBalance * $monthlyRate;
                $principalPayment = $this->installment_amount; // Principal payment only

                if ($principalPayment > $remainingBalance) {
                    $principalPayment = $remainingBalance;
                }

                $totalInterest += $monthlyInterest;
                $remainingBalance -= $principalPayment;
            }

            return round($totalInterest, 2);
        }
    }

    /**
     * Scope for loans by employee
     */
    public function scopeForEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }
}
