<?php

namespace App\Http\Controllers;

use App\Models\FiscalYear;
use App\Models\FiscalPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FiscalYearController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', FiscalYear::class);

        $search = $request->get('search', '');

        $fiscalYears = FiscalYear::search($search)
            ->latest()
            ->paginate()
            ->withQueryString();

        return view('app.fiscal_years.index', compact('fiscalYears', 'search'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', FiscalYear::class);

        return view('app.fiscal_years.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', FiscalYear::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'status' => 'nullable|in:open,closed,locked',
        ]);

        DB::beginTransaction();

        try {
            // Check if setting as active and deactivate others
            if ($validated['is_active'] ?? false) {
                FiscalYear::where('is_active', true)->update(['is_active' => false]);
            }

            // Create fiscal year
            $fiscalYear = FiscalYear::create([
                'name' => $validated['name'],
                'start_date' => $validated['start_date'],
                'end_date' => $validated['end_date'],
                'description' => $validated['description'],
                'is_active' => $validated['is_active'] ?? false,
                'status' => $validated['status'] ?? 'open',
                'is_closed' => false,
            ]);

            DB::commit();

            return redirect()
                ->route('fiscal-years.show', $fiscalYear)
                ->withSuccess('Fiscal year created successfully!');
        } catch (\Exception $e) {
            DB::rollback();

            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\FiscalYear  $fiscalYear
     * @return \Illuminate\Http\Response
     */
    public function show(FiscalYear $fiscalYear)
    {
        $this->authorize('view', $fiscalYear);

        $fiscalPeriods = $fiscalYear->fiscalPeriods()
            ->orderBy('start_date')
            ->get();

        return view('app.fiscal_years.show', compact('fiscalYear', 'fiscalPeriods'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\FiscalYear  $fiscalYear
     * @return \Illuminate\Http\Response
     */
    public function edit(FiscalYear $fiscalYear)
    {
        $this->authorize('update', $fiscalYear);

        if ($fiscalYear->is_closed) {
            return redirect()
                ->route('fiscal-years.show', $fiscalYear)
                ->withError('Closed fiscal years cannot be edited.');
        }

        return view('app.fiscal_years.edit', compact('fiscalYear'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\FiscalYear  $fiscalYear
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, FiscalYear $fiscalYear)
    {
        $this->authorize('update', $fiscalYear);

        if ($fiscalYear->is_closed) {
            return redirect()
                ->route('fiscal-years.show', $fiscalYear)
                ->withError('Closed fiscal years cannot be edited.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'status' => 'nullable|in:open,closed,locked',
        ]);

        // Check if setting as active and deactivate others
        if ($validated['is_active'] ?? false) {
            FiscalYear::where('is_active', true)
                ->where('id', '!=', $fiscalYear->id)
                ->update(['is_active' => false]);
        }

        $fiscalYear->update($validated);

        return redirect()
            ->route('fiscal-years.show', $fiscalYear)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\FiscalYear  $fiscalYear
     * @return \Illuminate\Http\Response
     */
    public function destroy(FiscalYear $fiscalYear)
    {
        $this->authorize('delete', $fiscalYear);

        if ($fiscalYear->is_closed) {
            return redirect()
                ->route('fiscal-years.index')
                ->withError('Closed fiscal years cannot be deleted.');
        }

        if ($fiscalYear->journalEntries()->count() > 0) {
            return redirect()
                ->route('fiscal-years.index')
                ->withError('Fiscal year has journal entries and cannot be deleted.');
        }

        DB::beginTransaction();

        try {
            // Delete fiscal periods
            $fiscalYear->fiscalPeriods()->delete();

            // Delete fiscal year
            $fiscalYear->delete();

            DB::commit();

            return redirect()
                ->route('fiscal-years.index')
                ->withSuccess(__('crud.common.removed'));
        } catch (\Exception $e) {
            DB::rollback();

            return redirect()
                ->back()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Close the fiscal year.
     *
     * @param  \App\Models\FiscalYear  $fiscalYear
     * @return \Illuminate\Http\Response
     */
    public function close(FiscalYear $fiscalYear)
    {
        $this->authorize('update', $fiscalYear);

        if ($fiscalYear->is_closed) {
            return redirect()
                ->route('fiscal-years.show', $fiscalYear)
                ->withError('Fiscal year is already closed.');
        }

        // Check if all periods are closed
        $openPeriods = $fiscalYear->fiscalPeriods()
            ->where('is_closed', false)
            ->count();

        if ($openPeriods > 0) {
            return redirect()
                ->route('fiscal-years.show', $fiscalYear)
                ->withError('All fiscal periods must be closed before closing the fiscal year.');
        }

        $fiscalYear->update([
            'is_closed' => true,
            'closed_date' => now(),
            'closed_by' => auth()->id(),
        ]);

        return redirect()
            ->route('fiscal-years.show', $fiscalYear)
            ->withSuccess('Fiscal year has been closed successfully.');
    }

    /**
     * Create monthly periods for a fiscal year.
     *
     * @param  \App\Models\FiscalYear  $fiscalYear
     * @return void
     */
    private function createMonthlyPeriods(FiscalYear $fiscalYear)
    {
        $startDate = new \DateTime($fiscalYear->start_date);
        $endDate = new \DateTime($fiscalYear->end_date);

        $currentDate = clone $startDate;
        $periodNumber = 1;

        while ($currentDate <= $endDate) {
            $periodStartDate = clone $currentDate;
            $periodEndDate = clone $currentDate;
            $periodEndDate->modify('last day of this month');

            // Ensure the period end date doesn't exceed the fiscal year end date
            if ($periodEndDate > $endDate) {
                $periodEndDate = clone $endDate;
            }

            $periodName = 'Period ' . $periodNumber . ' - ' . $periodStartDate->format('M Y');

            FiscalPeriod::create([
                'name' => $periodName,
                'start_date' => $periodStartDate->format('Y-m-d'),
                'end_date' => $periodEndDate->format('Y-m-d'),
                'fiscal_year_id' => $fiscalYear->id,
                'is_closed' => false,
                'is_active' => true,
                'period_number' => $periodNumber,
            ]);

            // Move to the first day of the next month
            $currentDate->modify('first day of next month');
            $periodNumber++;
        }
    }

    /**
     * Create quarterly periods for a fiscal year.
     *
     * @param  \App\Models\FiscalYear  $fiscalYear
     * @return void
     */
    private function createQuarterlyPeriods(FiscalYear $fiscalYear)
    {
        $startDate = new \DateTime($fiscalYear->start_date);
        $endDate = new \DateTime($fiscalYear->end_date);

        $currentDate = clone $startDate;
        $periodNumber = 1;

        while ($currentDate <= $endDate) {
            $periodStartDate = clone $currentDate;
            $periodEndDate = clone $currentDate;

            // Add 3 months and subtract 1 day to get the quarter end
            $periodEndDate->modify('+3 months -1 day');

            // Ensure the period end date doesn't exceed the fiscal year end date
            if ($periodEndDate > $endDate) {
                $periodEndDate = clone $endDate;
            }

            $periodName = 'Quarter ' . $periodNumber;

            FiscalPeriod::create([
                'name' => $periodName,
                'start_date' => $periodStartDate->format('Y-m-d'),
                'end_date' => $periodEndDate->format('Y-m-d'),
                'fiscal_year_id' => $fiscalYear->id,
                'is_closed' => false,
                'is_active' => true,
                'period_number' => $periodNumber,
            ]);

            // Move to the next quarter
            $currentDate = clone $periodEndDate;
            $currentDate->modify('+1 day');
            $periodNumber++;

            // Break if we've reached the end date
            if ($currentDate > $endDate) {
                break;
            }
        }
    }
}
