<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('production_order_items', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('production_order_id');
            $table->unsignedBigInteger('product_id');
            $table->unsignedBigInteger('bom_item_id')->nullable();
            $table->string('item_type')->default('material'); // material, labor, overhead
            $table->decimal('planned_quantity', 12, 2);
            $table->decimal('actual_quantity', 12, 2)->default(0);
            $table->decimal('quantity_variance', 12, 2)->default(0);
            $table->string('unit_of_measure')->default('unit');
            $table->decimal('planned_cost', 20, 2)->default(0);
            $table->decimal('actual_cost', 20, 2)->default(0);
            $table->decimal('cost_variance', 20, 2)->default(0);
            $table->string('status')->default('pending'); // pending, issued, completed
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unsignedBigInteger('business_type_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->foreign('production_order_id')
                ->references('id')
                ->on('production_orders')
                ->onDelete('cascade');
                
            $table->foreign('product_id')
                ->references('id')
                ->on('products')
                ->onDelete('cascade');
                
            $table->foreign('bom_item_id')
                ->references('id')
                ->on('bom_items')
                ->onDelete('set null');
                
            $table->foreign('created_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('updated_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('business_type_id')
                ->references('id')
                ->on('business_types')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('production_order_items');
    }
};
