<?php

namespace App\TenantFinder;

use Illuminate\Http\Request;
use App\Models\Tenant;
use Spatie\Multitenancy\TenantFinder\TenantFinder;

class NameTenantFinder extends TenantFinder
{
    public function findForRequest(Request $request): ?Tenant
    {
        // Get tenant name from form input or session
        $tenantName = cache(_authId());
        if ($tenantName) {
            return Tenant::where('domain', $tenantName)->first();
        }

        if(!request()->is("*login") && !$tenantName){
            Tenant::forgetCurrent();
            \Session::invalidate();
        }
        return null;
    }
}