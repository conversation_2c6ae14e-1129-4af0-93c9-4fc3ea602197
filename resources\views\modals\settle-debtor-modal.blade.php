<form action="/settle-debtor" method="POST">
	@csrf
	<x-lg-modal>
		<x-slot name="class">settle-debtor-modal</x-slot>
		<x-slot name="title">SETTLE DEBTOR</x-slot>


	    <div class="" id="settle-debtor-el">

	      <div class="justify-content-md-between mb-4">				

					<div class="mb-3">
						<label>Amount Paid:</label>
				    <input type="number" step="0.01" value="" class="form-control" :max="debtor ? debtor.balance : 0" name="amount_paid" required>	
				    <input type="hidden" :value="debtor?.id" name="id" required>	
					</div>

					<div class="mb-3">
						<label>Note:</label>
						<textarea name="comment" class="form-control" placeholder="Add comment." required></textarea>
					</div>

          <!-- List Group -->
          <ul class="list-group mt-4" v-if="debtor">
            <li class="list-group-item d-flex align-items-center bg-light">
              <!-- <i class="bi-house list-group-icon"></i>  -->
              <span v-text="debtor.debtable.name"></span>
              <h2 class="badge bg-primary rounded-pill ms-auto" style="font-size:20px" >
              	Balance: 
              	<span v-text="debtor.balance"></span>
              </h2>
            </li>
          </ul>
          <!-- End List Group -->

	      </div>
	      <!-- End Row -->
	     </div>

		<x-slot name="footer">
			<button type="submit" class="btn btn-primary"> Save </button>
		</x-slot>
	</x-lg-modal>


</form>




@push('scripts')

  <script type="text/javascript">
    
    new Vue({
      el: "#settle-debtor-el",
      data(){
        return{
          debtor: null,
        }
      },

      methods: {

        getDebtor(id) {
        	axios.get('/ajax-debtor/' + id ).then( res => {
        		this.debtor = res.data;
        	});
        }


      },


      created(){

      	$(".settle-debtor-btn").click( (e) => {
      		var id = $(e.target).attr("data-id");
      		this.getDebtor(id);
      	})
      }

    })

  </script>

@endpush
