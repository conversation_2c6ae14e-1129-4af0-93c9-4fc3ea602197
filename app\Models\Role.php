<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Role as BaseRole;

class Role extends BaseRole
{
    use HasFactory;

    protected $connection = "tenant";

    // public function getNameAttribute($value){
    //     return ucfirst($value);
    // }


    // public function setNameAttribute($value){
    //     return strtolower($value);
    // }


}
