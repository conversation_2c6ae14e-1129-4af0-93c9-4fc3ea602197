@php $editing = isset($product) @endphp

<div class="row">
    <!-- Basic Information Card -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-header-title">
                    <i class="bi bi-info-circle me-2"></i>Basic Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-12 col-lg-6 mb-4">
                        <x-inputs.text
                            name="name"
                            label="Product Name"
                            :value="old('name', ($editing ? $product->name : ''))"
                            maxlength="255"
                            placeholder="Enter product name"
                            required
                        ></x-inputs.text>
                    </div>
                    
                    <div class="col-sm-12 col-lg-6 mb-4">
                        <x-inputs.text
                            name="barcode"
                            label="Barcode / SKU"
                            :value="old('barcode', ($editing ? $product->barcode : ''))"
                            maxlength="255"
                            placeholder="Enter barcode or SKU"
                            required
                        ></x-inputs.text>
                    </div>
                    
                    <div class="col-sm-12 mb-4">
                        <label for="description" class="form-label">Description</label>
                        <textarea 
                            name="description" 
                            id="description" 
                            class="form-control" 
                            rows="3" 
                            placeholder="Enter product description"
                            required
                        >{{ old('description', ($editing ? $product->description : '')) }}</textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Classification Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-header-title">
                    <i class="bi bi-tag me-2"></i>Classification
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <label for="status_id" class="form-label">Status</label>
                    <div class="tom-select-custom">
                        <select name="status_id" id="status_id" required class="js-select form-select" data-hs-tom-select-options='{
                                    "placeholder": "Select a status..."
                                  }'>
                            @php $selected = old('status_id', ($editing ? $product->status_id : '')) @endphp
                            <option disabled {{ empty($selected) ? 'selected' : '' }}>Please select the Status</option>
                            @foreach($statuses as $value => $label)
                            <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }} >{{ $label }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="category_id" class="form-label">Category</label>
                    <div class="tom-select-custom">
                        <select name="category_id" id="category_id" class="js-select form-select" data-hs-tom-select-options='{
                                    "placeholder": "Select a category..."
                                  }'>
                            @php $selected = old('category_id', ($editing ? $product->category_id : '')) @endphp
                            <option disabled {{ empty($selected) ? 'selected' : '' }}>Please select the Category</option>
                            @foreach($categories as $value => $label)
                            <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }} >{{ $label }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                
                <div>
                    <label for="vat_applied" class="form-label">VAT Applied</label>
                    <div class="tom-select-custom">
                        <select name="vat_applied" id="vat_applied" class="js-select form-select">
                            @php $selected = old('vat_applied', ($editing ? $product->vat_applied : '')) @endphp
                            @foreach(['No','Yes'] as $value => $label)
                            <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }} >{{ $label }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Product Image Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-header-title">
                    <i class="bi bi-image me-2"></i>Product Image
                </h5>
            </div>
            <div class="card-body d-flex flex-column justify-content-center align-items-center">
                <div
                    x-data="imageViewer('{{ $editing ? $product->image : '' }}')"
                    class="text-center"
                >
                    <!-- Show the image -->
                    <template x-if="imageUrl">
                        <img
                            :src="imageUrl"
                            class="img-fluid rounded border border-gray-200 mb-3"
                            style="max-width: 200px; max-height: 200px; object-fit: contain;"
                        />
                    </template>

                    <!-- Show the gray box when image is not available -->
                    <template x-if="!imageUrl">
                        <div class="d-flex align-items-center justify-content-center border rounded border-gray-200 bg-light mb-3"
                            style="width: 200px; height: 200px;">
                            <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                        </div>
                    </template>

                    <div class="mt-2">
                        <label for="image" class="form-label">Upload Product Image</label>
                        <input type="file" name="image" id="image" class="form-control" @change="fileChosen" />
                        <small class="form-text text-muted">Recommended size: 800x800px, max 2MB</small>
                    </div>

                    @error('image') @include('components.inputs.partials.error') @enderror
                </div>
            </div>
        </div>
    </div>

    @if($editing)
    <!-- Units & Pricing Card -->
    <div class="col-12 mb-4">
        <div class="card" id="product-el">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-header-title">
                        <i class="bi bi-tags me-2"></i>Units & Pricing
                    </h5>
                    <button type="button" @click="addUnit" class="btn btn-primary btn-sm">
                        <i class="bi bi-plus-circle me-1"></i>Add Unit
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th class="text-center" style="width: 20%">Unit Name</th>
                                <th class="text-center" style="width: 15%">Unit Quantity</th>
                                <th class="text-center" style="width: 15%">Buying Price</th>
                                <th class="text-center" style="width: 15%">Selling Price</th>
                                <th class="text-center" style="width: 25%">Description</th>
                                <th class="text-center" style="width: 10%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-if="units.length === 0">
                                <td colspan="6" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="bi bi-tag text-muted" style="font-size: 2rem;"></i>
                                        <p class="mt-2 mb-0">No units defined yet</p>
                                        <button type="button" @click="addUnit" class="btn btn-sm btn-primary mt-3">
                                            <i class="bi bi-plus-circle me-1"></i>Add First Unit
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr v-for="(unit, index) in units" :key="index">    
                                <td> 
                                    <input type="hidden" name="units[]" v-model="unit.id">
                                    <input type="text" class="form-control" name="unit_names[]" v-model="unit.name" placeholder="e.g. Box, Piece, Kg" required>
                                </td>
                                <td> 
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="unit_quantities[]" v-model="unit.quantity" step="0.01" placeholder="Qty" required>
                                    </div>
                                </td>
                                <td> 
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" name="unit_buying_prices[]" v-model="unit.buying_price" step="0.01" placeholder="0.00" required>
                                    </div>
                                </td>                    
                                <td> 
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" name="unit_selling_prices[]" v-model="unit.selling_price" step="0.01" placeholder="0.00" required>
                                    </div>
                                </td>
                                <td>
                                    <input type="text" class="form-control" name="unit_descriptions[]" v-model="unit.description" placeholder="Optional description">
                                </td>
                                <td class="text-center">
                                    <button type="button" @click="removeUnit(index)" class="btn btn-outline-danger btn-sm" title="Remove Unit">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer bg-light d-flex justify-content-between align-items-center">
                <span class="text-muted small">Define all units and pricing for this product</span>
                <span class="badge bg-info" v-if="units.length > 0">@{{ units.length }} unit(s) defined</span>
            </div>
        </div>
    </div>
    @endif

</div>

@if($editing)
@push('scripts')
<script type="text/javascript">
    new Vue({
        el: "#product-el",

        data() {
            return {
                units: @json($product->units ?? []),
            }
        },

        methods: {
            removeUnit(index) {
                if (confirm('Are you sure you want to remove this unit?')) {
                    this.units.splice(index, 1);
                }
            },

            addUnit() {
                this.units.push({
                    id: '',
                    name: '',
                    quantity: '1',
                    description: '',
                    selling_price: 0,
                    buying_price: 0
                });
                
                // Focus on the new unit's name field after rendering
                this.$nextTick(() => {
                    const inputs = document.querySelectorAll('input[name="unit_names[]"]');
                    if (inputs.length > 0) {
                        inputs[inputs.length - 1].focus();
                    }
                });
            },
            
            calculateProfit(buyingPrice, sellingPrice) {
                if (!buyingPrice || !sellingPrice) return 0;
                const profit = sellingPrice - buyingPrice;
                const profitPercentage = (profit / buyingPrice) * 100;
                return profitPercentage.toFixed(2);
            }
        }
    });
</script>
@endpush
@endif