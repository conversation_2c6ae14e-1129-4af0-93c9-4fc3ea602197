@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">journal-text</x-slot>
        <x-slot name="title">Account Types</x-slot>
        <x-slot name="subtitle">Manage chart of accounts classification</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Accounting</a></li>
            <li class="breadcrumb-item active" aria-current="page">Account Types</li>
        </x-slot>
        <x-slot name="controls">
            @can('create', App\Models\AccountType::class)
            <a href="{{ route('account-types.create') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i> Add Account Type
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Search & Filters -->
    <div class="card mb-3">
        <div class="card-body">
            <form action="{{ route('account-types.index') }}" method="GET" class="row g-3">
                <div class="col-md-6">
                    <label for="search" class="form-label">Search Account Types</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="search" name="search" placeholder="Name, code, classification..." value="{{ $search ?? '' }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="classification" class="form-label">Classification</label>
                    <select class="form-select js-select" id="classification" name="classification">
                        <option value="">All Classifications</option>
                        <option value="asset" {{ request('classification') == 'asset' ? 'selected' : '' }}>Asset</option>
                        <option value="liability" {{ request('classification') == 'liability' ? 'selected' : '' }}>Liability</option>
                        <option value="equity" {{ request('classification') == 'equity' ? 'selected' : '' }}>Equity</option>
                        <option value="revenue" {{ request('classification') == 'revenue' ? 'selected' : '' }}>Revenue</option>
                        <option value="expense" {{ request('classification') == 'expense' ? 'selected' : '' }}>Expense</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-filter me-1"></i> Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Account Types Table -->
    <div class="card">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">Account Types</h4>
                </div>
                <div class="col-auto">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-download me-1"></i> Export
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="exportDropdown">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-excel me-1"></i> Excel</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-pdf me-1"></i> PDF</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-text me-1"></i> CSV</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>@lang('crud.account_types.inputs.name')</th>
                        <th>@lang('crud.account_types.inputs.code')</th>
                        <th>@lang('crud.account_types.inputs.classification')</th>
                        <th class="text-center">@lang('crud.account_types.inputs.is_active')</th>
                        <th class="text-center">@lang('crud.common.actions')</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($accountTypes as $accountType)
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar avatar-xs avatar-circle">
                                        <span class="avatar-initials">{{ substr($accountType->name, 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5 class="mb-0">{{ $accountType->name ?? '-' }}</h5>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-soft-primary">{{ $accountType->code ?? '-' }}</span>
                        </td>
                        <td>
                            @php
                                $classColors = [
                                    'asset' => 'success',
                                    'liability' => 'danger',
                                    'equity' => 'info',
                                    'revenue' => 'primary',
                                    'expense' => 'warning'
                                ];
                                $color = $classColors[$accountType->classification] ?? 'secondary';
                            @endphp
                            <span class="badge bg-soft-{{ $color }} text-{{ $color }}">
                                {{ ucfirst($accountType->classification) ?? '-' }}
                            </span>
                        </td>
                        <td class="text-center">
                            @if($accountType->is_active)
                                <span class="badge bg-soft-success text-success">
                                    <i class="bi bi-check-circle me-1"></i> Active
                                </span>
                            @else
                                <span class="badge bg-soft-danger text-danger">
                                    <i class="bi bi-x-circle me-1"></i> Inactive
                                </span>
                            @endif
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group" aria-label="Row Actions">
                                @can('update', $accountType)
                                <a href="{{ route('account-types.edit', $accountType) }}" class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit">
                                    <i class="bi bi-pencil-square"></i>
                                </a>
                                @endcan 
                                
                                @can('view', $accountType)
                                <a href="{{ route('account-types.show', $accountType) }}" class="btn btn-outline-info btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="View">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @endcan 
                                
                                @can('delete', $accountType)
                                <form action="{{ route('account-types.destroy', $accountType) }}" method="POST" onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')">
                                    @csrf @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="Delete" {{ $accountType->is_system ? 'disabled' : '' }}>
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="5" class="text-center p-4">
                            <div class="d-flex flex-column align-items-center justify-content-center py-5">
                                <i class="bi bi-journal-text text-muted" style="font-size: 3rem;"></i>
                                <h5 class="mt-4">No account types found</h5>
                                <p class="text-muted">Try adjusting your search or filter to find what you're looking for.</p>
                                @can('create', App\Models\AccountType::class)
                                <a href="{{ route('account-types.create') }}" class="btn btn-primary mt-2">
                                    <i class="bi bi-plus-circle me-1"></i> Add New Account Type
                                </a>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        @if(isset($accountTypes) && $accountTypes->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-center">
                {{ $accountTypes->links() }}
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
@endpush
