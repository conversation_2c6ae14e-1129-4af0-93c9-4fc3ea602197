<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('asset_categories', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->unsignedBigInteger('asset_account_id')->nullable();
            $table->unsignedBigInteger('accumulated_depreciation_account_id')->nullable();
            $table->unsignedBigInteger('depreciation_expense_account_id')->nullable();
            $table->unsignedBigInteger('gain_loss_account_id')->nullable();
            $table->integer('useful_life_years')->default(5);
            $table->decimal('depreciation_rate', 8, 4)->default(0.2); // 20% for 5 years
            $table->string('depreciation_method')->default('straight_line'); // straight_line, declining_balance, etc.
            $table->boolean('is_active')->default(true);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unsignedBigInteger('business_type_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->foreign('asset_account_id')
                ->references('id')
                ->on('accounts')
                ->onDelete('set null');
                
            $table->foreign('accumulated_depreciation_account_id')
                ->references('id')
                ->on('accounts')
                ->onDelete('set null');
                
            $table->foreign('depreciation_expense_account_id')
                ->references('id')
                ->on('accounts')
                ->onDelete('set null');
                
            $table->foreign('gain_loss_account_id')
                ->references('id')
                ->on('accounts')
                ->onDelete('set null');
                
            $table->foreign('created_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('updated_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('business_type_id')
                ->references('id')
                ->on('business_types')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('asset_categories');
    }
};
