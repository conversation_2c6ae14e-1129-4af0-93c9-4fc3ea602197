<div>
    <div class="mb-4">
        @can('create', App\Models\AccountCategory::class)
        <button class="btn btn-primary" wire:click="newAccountCategory">
            <i class="icon ion-md-add"></i>
            @lang('crud.common.new')
        </button>
        @endcan
    </div>

    <x-modal wire:model="showingModal">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ $modalTitle }}</h5>
                <button
                    type="button"
                    class="close"
                    data-dismiss="modal"
                    aria-label="Close"
                >
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="modal-body">
                <div>
                    <x-inputs.group class="col-sm-12">
                        <x-inputs.text
                            name="accountCategoryName"
                            label="Name"
                            wire:model="accountCategoryName"
                            maxlength="255"
                            placeholder="Name"
                        ></x-inputs.text>
                    </x-inputs.group>

                    <x-inputs.group class="col-sm-12">
                        <x-inputs.text
                            name="accountCategoryCode"
                            label="Code"
                            wire:model="accountCategoryCode"
                            maxlength="255"
                            placeholder="Code"
                        ></x-inputs.text>
                    </x-inputs.group>

                    <x-inputs.group class="col-sm-12">
                        <x-inputs.textarea
                            name="accountCategoryDescription"
                            label="Description"
                            wire:model="accountCategoryDescription"
                            maxlength="255"
                        ></x-inputs.textarea>
                    </x-inputs.group>

                    <x-inputs.group class="col-sm-12">
                        <x-inputs.checkbox
                            id="accountCategoryIsActive"
                            name="accountCategoryIsActive"
                            label="Active"
                            wire:model="accountCategoryIsActive"
                        ></x-inputs.checkbox>
                    </x-inputs.group>
                </div>
            </div>

            <div class="modal-footer">
                <button
                    type="button"
                    class="btn btn-light float-left"
                    wire:click="$toggle('showingModal')"
                >
                    <i class="icon ion-md-close"></i>
                    @lang('crud.common.cancel')
                </button>

                <button type="button" class="btn btn-primary" wire:click="createAccountCategory">
                    <i class="icon ion-md-save"></i>
                    @lang('crud.common.create')
                </button>
            </div>
        </div>
    </x-modal>

    <div class="table-responsive">
        <table class="table table-borderless table-hover">
            <thead>
                <tr>
                    <th class="text-left">
                        @lang('crud.account_categories.inputs.name')
                    </th>
                    <th class="text-left">
                        @lang('crud.account_categories.inputs.code')
                    </th>
                    <th class="text-center">
                        @lang('crud.account_categories.inputs.is_active')
                    </th>
                    <th></th>
                </tr>
            </thead>
            <tbody class="text-gray-600">
                @forelse($accountCategories as $accountCategory)
                <tr class="hover:bg-gray-100">
                    <td class="text-left">
                        {{ $accountCategory->name ?? '-' }}
                    </td>
                    <td class="text-left">
                        {{ $accountCategory->code ?? '-' }}
                    </td>
                    <td class="text-center">
                        <span class="badge {{ $accountCategory->is_active ? 'badge-success' : 'badge-danger' }}">
                            {{ $accountCategory->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </td>
                    <td class="text-right" style="width: 134px;">
                        <div
                            role="group"
                            aria-label="Row Actions"
                            class="relative inline-flex align-middle"
                        >
                            @can('update', $accountCategory)
                            <a
                                href="{{ route('account-categories.edit', $accountCategory) }}"
                                class="mr-1"
                            >
                                <button
                                    type="button"
                                    class="btn btn-light"
                                >
                                    <i class="icon ion-md-create"></i>
                                </button>
                            </a>
                            @endcan @can('view', $accountCategory)
                            <a
                                href="{{ route('account-categories.show', $accountCategory) }}"
                                class="mr-1"
                            >
                                <button
                                    type="button"
                                    class="btn btn-light"
                                >
                                    <i class="icon ion-md-eye"></i>
                                </button>
                            </a>
                            @endcan
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="4">
                        @lang('crud.common.no_items_found')
                    </td>
                </tr>
                @endforelse
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="4">
                        <div class="mt-10 px-4">
                            {{ $accountCategories->render() }}
                        </div>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>
