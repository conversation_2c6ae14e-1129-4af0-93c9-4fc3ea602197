<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\DeductionContribution;
use App\Models\EmployeeDeductionContribution;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class EmployeeDeductionContributionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', EmployeeDeductionContribution::class);

        $search = $request->get('search', '');
        $employeeId = $request->get('employee_id', null);

        $query = EmployeeDeductionContribution::search($search)
            ->with(['employee', 'deductionContribution']);
            
        if ($employeeId) {
            $query->where('employee_id', $employeeId);
        }

        $employeeDeductionContributions = $query->latest()
            ->paginate()
            ->withQueryString();

        $employees = Employee::orderBy('name')->pluck('name', 'id');

        return view('app.employee_deduction_contributions.index', compact(
            'employeeDeductionContributions', 
            'search', 
            'employees', 
            'employeeId'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', EmployeeDeductionContribution::class);

        $employees = Employee::orderBy('name')->pluck('name', 'id');
        $deductionContributions = DeductionContribution::where('status_id', 1)
            ->orderBy('name')
            ->get()
            ->pluck('name', 'id');

        return view('app.employee_deduction_contributions.create', compact('employees', 'deductionContributions'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', EmployeeDeductionContribution::class);

        $validated = $request->validate([
            'employee_id' => 'required|tenant_exists:employees,id',
            'deduction_contribution_id' => 'required|tenant_exists:deduction_contributions,id',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $validated['created_by'] = auth()->id();

        $employeeDeductionContribution = EmployeeDeductionContribution::create($validated);

        return redirect()
            ->route('employee-deduction-contributions.index', ['employee_id' => $validated['employee_id']])
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Models\EmployeeDeductionContribution $employeeDeductionContribution
     * @return \Illuminate\Http\Response
     */
    public function show(EmployeeDeductionContribution $employeeDeductionContribution)
    {
        $this->authorize('view', $employeeDeductionContribution);

        return view('app.employee_deduction_contributions.show', compact('employeeDeductionContribution'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Models\EmployeeDeductionContribution $employeeDeductionContribution
     * @return \Illuminate\Http\Response
     */
    public function edit(EmployeeDeductionContribution $employeeDeductionContribution)
    {
        $this->authorize('update', $employeeDeductionContribution);

        $employees = Employee::orderBy('name')->pluck('name', 'id');
        $deductionContributions = DeductionContribution::where('status_id', 1)
            ->orderBy('name')
            ->get()
            ->pluck('name', 'id');

        return view('app.employee_deduction_contributions.edit', compact(
            'employeeDeductionContribution',
            'employees',
            'deductionContributions'
        ));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\EmployeeDeductionContribution $employeeDeductionContribution
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, EmployeeDeductionContribution $employeeDeductionContribution)
    {
        $this->authorize('update', $employeeDeductionContribution);

        $validated = $request->validate([
            'employee_id' => 'required|tenant_exists:employees,id',
            'deduction_contribution_id' => 'required|tenant_exists:deduction_contributions,id',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $validated['updated_by'] = auth()->id();

        $employeeDeductionContribution->update($validated);

        return redirect()
            ->route('employee-deduction-contributions.index', ['employee_id' => $validated['employee_id']])
            ->withSuccess(__('crud.common.updated'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Models\EmployeeDeductionContribution $employeeDeductionContribution
     * @return \Illuminate\Http\Response
     */
    public function destroy(EmployeeDeductionContribution $employeeDeductionContribution)
    {
        $this->authorize('delete', $employeeDeductionContribution);

        $employeeId = $employeeDeductionContribution->employee_id;
        
        $employeeDeductionContribution->delete();

        return redirect()
            ->route('employee-deduction-contributions.index', ['employee_id' => $employeeId])
            ->withSuccess(__('crud.common.deleted'));
    }

    /**
     * Bulk assign deductions/contributions to an employee.
     *
     * @param \App\Models\Employee $employee
     * @return \Illuminate\Http\Response
     */
    public function bulkAssignForm(Employee $employee)
    {
        $this->authorize('create', EmployeeDeductionContribution::class);

        $deductions = DeductionContribution::where('apply_mode', 'DEDUCTION')
            ->where('status_id', 1)
            ->orderBy('name')
            ->get();
            
        $contributions = DeductionContribution::where('apply_mode', 'CONTRIBUTION')
            ->where('status_id', 1)
            ->orderBy('name')
            ->get();
            
        $assignedIds = EmployeeDeductionContribution::where('employee_id', $employee->id)
            ->pluck('deduction_contribution_id')
            ->toArray();

        return view('app.employee_deduction_contributions.bulk_assign', compact(
            'employee',
            'deductions',
            'contributions',
            'assignedIds'
        ));
    }

    /**
     * Process bulk assignment of deductions/contributions to an employee.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Employee $employee
     * @return \Illuminate\Http\Response
     */
    public function bulkAssign(Request $request, Employee $employee)
    {
        $this->authorize('create', EmployeeDeductionContribution::class);

        $validated = $request->validate([
            'deduction_contribution_ids' => 'required|array',
            'deduction_contribution_ids.*' => 'tenant_exists:deduction_contributions,id',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        DB::beginTransaction();

        try {
            // Remove existing assignments
            EmployeeDeductionContribution::where('employee_id', $employee->id)->delete();
            
            // Create new assignments
            foreach ($validated['deduction_contribution_ids'] as $deductionContributionId) {
                EmployeeDeductionContribution::create([
                    'employee_id' => $employee->id,
                    'deduction_contribution_id' => $deductionContributionId,
                    'start_date' => $validated['start_date'],
                    'end_date' => $validated['end_date'],
                    'created_by' => auth()->id(),
                ]);
            }
            
            DB::commit();
            
            return redirect()
                ->route('employee-deduction-contributions.index', ['employee_id' => $employee->id])
                ->withSuccess('Deductions and contributions assigned successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
}
