<?php
namespace App\Http\Controllers\Api;

use App\Models\User;
use Illuminate\Http\Request;
use App\Models\BusinessType;
use App\Http\Controllers\Controller;
use App\Http\Resources\BusinessTypeCollection;

class UserBusinessTypesController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, User $user)
    {
        $this->authorize('view', $user);

        $search = $request->get('search', '');

        $businessTypes = $user
            ->businessTypes()
            ->search($search)
            ->latest()
            ->paginate();

        return new BusinessTypeCollection($businessTypes);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @param \App\Models\BusinessType $businessType
     * @return \Illuminate\Http\Response
     */
    public function store(
        Request $request,
        User $user,
        BusinessType $businessType
    ) {
        $this->authorize('update', $user);

        $user->businessTypes()->syncWithoutDetaching([$businessType->id]);

        return response()->noContent();
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @param \App\Models\BusinessType $businessType
     * @return \Illuminate\Http\Response
     */
    public function destroy(
        Request $request,
        User $user,
        BusinessType $businessType
    ) {
        $this->authorize('update', $user);

        $user->businessTypes()->detach($businessType);

        return response()->noContent();
    }
}
