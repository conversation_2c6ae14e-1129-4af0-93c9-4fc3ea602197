@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">Currencies</x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Currency::class)
            <a href="{{ route('currencies.create') }}" class="btn btn-info btn-sm">
                @lang('crud.common.create') Currency
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="searchbar mt-4 mb-5">
        <form class="d-flex" id="filter">
            <div class="me-3">
                <input
                    type="text"
                    name="search"
                    value="{{ $search ?? '' }}"
                    class="form-control"
                    placeholder="{{ __('crud.common.search') }}"
                    autocomplete="off"
                />
            </div>
            <div class="me-3">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-search"></i>
                </button>
            </div>
        </form>
    </div>

    <div class="card card-table">
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            @lang('crud.currencies.inputs.name')
                        </th>
                        <th class="text-left">
                            @lang('crud.currencies.inputs.code')
                        </th>
                        <th class="text-left">
                            @lang('crud.currencies.inputs.symbol')
                        </th>
                        <th class="text-left">
                            @lang('crud.currencies.inputs.decimal_places')
                        </th>
                        <th class="text-left">
                            @lang('crud.currencies.inputs.exchange_rate')
                        </th>
                        <th class="text-center">
                            @lang('crud.currencies.inputs.is_base_currency')
                        </th>
                        <th class="text-center">
                            @lang('crud.currencies.inputs.is_default')
                        </th>
                        <th class="text-center">
                            @lang('crud.currencies.inputs.is_active')
                        </th>
                        <th class="text-center">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($currencies as $currency)
                    <tr>
                        <td>{{ $currency->name ?? '-' }}</td>
                        <td>{{ $currency->code ?? '-' }}</td>
                        <td>{{ $currency->symbol ?? '-' }}</td>
                        <td>{{ $currency->decimal_places ?? '-' }}</td>
                        <td>{{ $currency->exchange_rate ?? '-' }}</td>
                        <td class="text-center">
                            <span class="badge {{ $currency->is_base_currency ? 'bg-success' : 'bg-secondary' }}">
                                {{ $currency->is_base_currency ? 'Yes' : 'No' }}
                            </span>
                        </td>
                        <td class="text-center">
                            <span class="badge {{ $currency->is_default ? 'bg-success' : 'bg-secondary' }}">
                                {{ $currency->is_default ? 'Yes' : 'No' }}
                            </span>
                        </td>
                        <td class="text-center">
                            <span class="badge {{ $currency->is_active ? 'bg-success' : 'bg-secondary' }}">
                                {{ $currency->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </td>
                        <td class="text-center" style="width: 134px;">
                            <div
                                role="group"
                                aria-label="Row Actions"
                                class="btn-group"
                            >
                                @can('update', $currency)
                                <a
                                    href="{{ route('currencies.edit', $currency) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        edit
                                    </button>
                                </a>
                                @endcan @can('view', $currency)
                                <a
                                    href="{{ route('currencies.show', $currency) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        view
                                    </button>
                                </a>
                                @endcan @can('delete', $currency)
                                <form
                                    action="{{ route('currencies.destroy', $currency) }}"
                                    method="POST"
                                    onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                >
                                    @csrf @method('DELETE')
                                    <button
                                        type="submit"
                                        class="btn btn-light text-danger m-1"
                                    >
                                        del
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="9" class="text-center">
                            @lang('crud.common.no_items_found')
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="card-footer d-flex justify-content-center">
            {!! $currencies->render() !!}
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
  $("document").ready(function () {
    dataTableBtn()
  });
</script>
@endpush
