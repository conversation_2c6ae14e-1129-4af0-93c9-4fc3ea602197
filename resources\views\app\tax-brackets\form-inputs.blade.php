@php $editing = isset($taxBracket) @endphp

<div class="row">
    <div class="col-md-6">
        <div class="mb-3">
            <label for="name" class="form-label">Name</label>
            <input type="text" name="name" id="name"
                   class="form-control @error('name') is-invalid @enderror"
                   value="{{ old('name', $editing ? $taxBracket->name : '') }}"
                   placeholder="e.g., First Bracket, Tax Free Threshold">
            @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            <div class="form-text">Optional descriptive name for this tax bracket</div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="mb-3">
            <label for="order" class="form-label">Order <span class="text-danger">*</span></label>
            <input type="number" name="order" id="order"
                   class="form-control @error('order') is-invalid @enderror"
                   value="{{ old('order', $editing ? $taxBracket->order : '') }}"
                   min="0" step="1" required>
            @error('order')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            <div class="form-text">Order of application (lower numbers applied first)</div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="mb-3">
            <label for="min_amount" class="form-label">Minimum Amount (K) <span class="text-danger">*</span></label>
            <input type="number" name="min_amount" id="min_amount"
                   class="form-control @error('min_amount') is-invalid @enderror"
                   value="{{ old('min_amount', $editing ? $taxBracket->min_amount : '') }}"
                   min="0" step="0.01" required>
            @error('min_amount')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            <div class="form-text">Minimum income amount for this bracket</div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="mb-3">
            <label for="max_amount" class="form-label">Maximum Amount (K)</label>
            <input type="number" name="max_amount" id="max_amount"
                   class="form-control @error('max_amount') is-invalid @enderror"
                   value="{{ old('max_amount', $editing ? $taxBracket->max_amount : '') }}"
                   min="0" step="0.01">
            @error('max_amount')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            <div class="form-text">Maximum income amount (leave empty for unlimited)</div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="mb-3">
            <label for="rate" class="form-label">Tax Rate (%) <span class="text-danger">*</span></label>
            <div class="input-group">
                <input type="number" name="rate" id="rate"
                       class="form-control @error('rate') is-invalid @enderror"
                       value="{{ old('rate', $editing ? $taxBracket->rate_percentage : '') }}"
                       min="0" max="100" step="0.01" required>
                <span class="input-group-text">%</span>
            </div>
            @error('rate')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            <div class="form-text">Tax rate as percentage (e.g., 25 for 25%)</div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="mb-3">
            <label for="is_active" class="form-label">Status</label>
            <div class="form-check form-switch">
                <input type="hidden" name="is_active" value="0">
                <input type="checkbox" name="is_active" id="is_active"
                       class="form-check-input @error('is_active') is-invalid @enderror"
                       value="1" {{ old('is_active', $editing ? $taxBracket->is_active : true) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_active">
                    Active
                </label>
            </div>
            @error('is_active')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            <div class="form-text">Only active brackets are used in tax calculations</div>
        </div>
    </div>
</div>

<div class="mb-3">
    <label for="description" class="form-label">Description</label>
    <textarea name="description" id="description" rows="3"
              class="form-control @error('description') is-invalid @enderror"
              placeholder="Optional description of this tax bracket">{{ old('description', $editing ? $taxBracket->description : '') }}</textarea>
    @error('description')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <div class="form-text">Optional description for documentation purposes</div>
</div>

<!-- Tax Calculation Preview -->
<div class="card bg-light">
    <div class="card-header">
        <h6 class="mb-0">Tax Calculation Preview</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <label for="preview_amount" class="form-label">Test Amount (K)</label>
                <input type="number" id="preview_amount" class="form-control"
                       placeholder="Enter amount to preview tax calculation"
                       min="0" step="0.01">
            </div>
            <div class="col-md-6">
                <label class="form-label">Tax Amount</label>
                <div id="preview_result" class="form-control-plaintext">
                    <span class="text-muted">Enter amount to see tax calculation</span>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const previewAmount = document.getElementById('preview_amount');
    const previewResult = document.getElementById('preview_result');

    function updatePreview() {
        const amount = parseFloat(previewAmount.value);
        if (!amount || amount <= 0) {
            previewResult.innerHTML = '<span class="text-muted">Enter amount to see tax calculation</span>';
            return;
        }

        // Simple preview calculation based on current form values
        const minAmount = parseFloat(document.getElementById('min_amount').value) || 0;
        const maxAmount = parseFloat(document.getElementById('max_amount').value) || null;
        const rate = parseFloat(document.getElementById('rate').value) || 0;

        let taxableAmount = 0;
        if (amount > minAmount) {
            if (maxAmount === null || amount <= maxAmount) {
                taxableAmount = amount - minAmount;
            } else {
                taxableAmount = maxAmount - minAmount;
            }
        }

        const taxAmount = taxableAmount * (rate / 100);

        previewResult.innerHTML = `
            <div class="d-flex justify-content-between">
                <span>Taxable in this bracket:</span>
                <strong>K${taxableAmount.toLocaleString('en-US', {minimumFractionDigits: 2})}</strong>
            </div>
            <div class="d-flex justify-content-between">
                <span>Tax amount:</span>
                <strong class="text-primary">K${taxAmount.toLocaleString('en-US', {minimumFractionDigits: 2})}</strong>
            </div>
        `;
    }

    if (previewAmount) {
        previewAmount.addEventListener('input', updatePreview);
        document.getElementById('min_amount').addEventListener('input', updatePreview);
        document.getElementById('max_amount').addEventListener('input', updatePreview);
        document.getElementById('rate').addEventListener('input', updatePreview);
    }
});
</script>
@endpush
