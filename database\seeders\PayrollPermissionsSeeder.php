<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\PermissionRegistrar;

class PayrollPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Reset cached roles and permissions
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        // Create payroll permissions
        $this->createPayrollPermissions();

        // Assign permissions to roles
        $this->assignPermissionsToRoles();
    }

    /**
     * Create payroll permissions.
     *
     * @return void
     */
    private function createPayrollPermissions()
    {
        // Employee permissions
        $this->createResourcePermissions('employees');

        // Monthly Payroll permissions
        $this->createResourcePermissions('monthly-payrolls');
        $this->createPermission('generate monthly-payrolls');
        $this->createPermission('approve monthly-payrolls');
        $this->createPermission('create-journal-entries monthly-payrolls');

        // Deduction/Contribution permissions
        $this->createResourcePermissions('deduction-contributions');
        $this->createPermission('assign deduction-contributions');

        // Employee Deduction/Contribution permissions
        $this->createResourcePermissions('employee-deduction-contributions');
        $this->createPermission('bulk-assign employee-deduction-contributions');

        // Payroll Report permissions
        $this->createPermission('view payroll-reports');
        $this->createPermission('export payroll-reports');

        // Employee Loan permissions
        $this->createResourcePermissions('employee-loans');
        $this->createPermission('approve employee-loans');
        $this->createPermission('cancel employee-loans');
        $this->createPermission('view employee-loan-statistics');
    }

    /**
     * Create standard CRUD permissions for a resource.
     *
     * @param string $resource
     * @return void
     */
    private function createResourcePermissions($resource)
    {
        $this->createPermission("list {$resource}");
        $this->createPermission("view {$resource}");
        $this->createPermission("create {$resource}");
        $this->createPermission("update {$resource}");
        $this->createPermission("delete {$resource}");
    }

    /**
     * Create a single permission if it doesn't exist.
     *
     * @param string $name
     * @return void
     */
    private function createPermission($name)
    {
        Permission::firstOrCreate(['name' => $name]);
    }

    /**
     * Assign permissions to roles.
     *
     * @return void
     */
    private function assignPermissionsToRoles()
    {
        // Get or create roles
        $superAdmin = Role::firstOrCreate(['name' => 'super-admin']);
        $accountant = Role::firstOrCreate(['name' => 'accountant']);
        $financeManager = Role::firstOrCreate(['name' => 'finance-manager']);
        $hrManager = Role::firstOrCreate(['name' => 'hr-manager']);
        $payrollManager = Role::firstOrCreate(['name' => 'payroll-manager']);

        // Super Admin gets all permissions
        $superAdmin->givePermissionTo(Permission::all());

        // Payroll Manager permissions
        $payrollManagerPermissions = [
            'list employees', 'view employees', 'create employees', 'update employees', 'delete employees',
            'list monthly-payrolls', 'view monthly-payrolls', 'create monthly-payrolls', 'update monthly-payrolls', 'delete monthly-payrolls',
            'generate monthly-payrolls', 'approve monthly-payrolls', 'create-journal-entries monthly-payrolls',
            'list deduction-contributions', 'view deduction-contributions', 'create deduction-contributions', 'update deduction-contributions', 'delete deduction-contributions',
            'assign deduction-contributions',
            'list employee-deduction-contributions', 'view employee-deduction-contributions', 'create employee-deduction-contributions', 'update employee-deduction-contributions', 'delete employee-deduction-contributions',
            'bulk-assign employee-deduction-contributions',
            'view payroll-reports', 'export payroll-reports',
            'list employee-loans', 'view employee-loans', 'create employee-loans', 'update employee-loans', 'delete employee-loans',
            'approve employee-loans', 'cancel employee-loans', 'view employee-loan-statistics',
        ];
        $payrollManager->givePermissionTo($payrollManagerPermissions);

        // HR Manager permissions
        $hrManagerPermissions = [
            'list employees', 'view employees', 'create employees', 'update employees',
            'list monthly-payrolls', 'view monthly-payrolls',
            'list deduction-contributions', 'view deduction-contributions',
            'list employee-deduction-contributions', 'view employee-deduction-contributions',
            'view payroll-reports',
            'list employee-loans', 'view employee-loans', 'create employee-loans', 'update employee-loans',
            'view employee-loan-statistics',
        ];
        $hrManager->givePermissionTo($hrManagerPermissions);

        // Finance Manager permissions
        $financeManagerPermissions = [
            'list employees', 'view employees',
            'list monthly-payrolls', 'view monthly-payrolls', 'approve monthly-payrolls', 'create-journal-entries monthly-payrolls',
            'view payroll-reports', 'export payroll-reports',
            'list employee-loans', 'view employee-loans', 'approve employee-loans', 'cancel employee-loans',
            'view employee-loan-statistics',
        ];
        $financeManager->givePermissionTo($financeManagerPermissions);

        // Accountant permissions
        $accountantPermissions = [
            'list employees', 'view employees',
            'list monthly-payrolls', 'view monthly-payrolls',
            'view payroll-reports',
            'list employee-loans', 'view employee-loans', 'view employee-loan-statistics',
        ];
        $accountant->givePermissionTo($accountantPermissions);
    }
}
