<?php

namespace App\Http\Controllers;

use App\Models\WorkHoursConfiguration;
use Illuminate\Http\Request;

class WorkHoursConfigurationController extends Controller
{
    /**
     * Display a listing of work hours configurations.
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', WorkHoursConfiguration::class);

        $search = $request->get('search', '');

        $configurations = WorkHoursConfiguration::when($search, function ($query, $search) {
                return $query->where('name', 'like', "%{$search}%")
                           ->orWhere('description', 'like', "%{$search}%");
            })
            ->orderBy('is_active', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(15)
            ->withQueryString();

        return view('app.work-hours-configurations.index', compact('configurations', 'search'));
    }

    /**
     * Show the form for creating a new work hours configuration.
     */
    public function create()
    {
        $this->authorize('create', WorkHoursConfiguration::class);

        return view('app.work-hours-configurations.create');
    }

    /**
     * Store a newly created work hours configuration.
     */
    public function store(Request $request)
    {
        $this->authorize('create', WorkHoursConfiguration::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'standard_hours_per_day' => 'required|numeric|min:1|max:24',
            'overtime_rate' => 'required|numeric|min:1|max:5',
            'is_active' => 'boolean',
            'description' => 'nullable|string|max:1000',
        ]);

        // If this configuration is set as active, deactivate others
        if ($validated['is_active'] ?? false) {
            WorkHoursConfiguration::where('is_active', true)->update(['is_active' => false]);
        }

        $validated['created_by'] = auth()->id();

        $configuration = WorkHoursConfiguration::create($validated);

        return redirect()
            ->route('work-hours-configurations.show', $configuration)
            ->with('success', 'Work hours configuration created successfully.');
    }

    /**
     * Display the specified work hours configuration.
     */
    public function show(WorkHoursConfiguration $workHoursConfiguration)
    {
        $this->authorize('view', $workHoursConfiguration);

        return view('app.work-hours-configurations.show', compact('workHoursConfiguration'));
    }

    /**
     * Show the form for editing the specified work hours configuration.
     */
    public function edit(WorkHoursConfiguration $workHoursConfiguration)
    {
        $this->authorize('update', $workHoursConfiguration);

        return view('app.work-hours-configurations.edit', compact('workHoursConfiguration'));
    }

    /**
     * Update the specified work hours configuration.
     */
    public function update(Request $request, WorkHoursConfiguration $workHoursConfiguration)
    {
        $this->authorize('update', $workHoursConfiguration);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'standard_hours_per_day' => 'required|numeric|min:1|max:24',
            'overtime_rate' => 'required|numeric|min:1|max:5',
            'is_active' => 'boolean',
            'description' => 'nullable|string|max:1000',
        ]);

        // If this configuration is set as active, deactivate others
        if ($validated['is_active'] ?? false) {
            WorkHoursConfiguration::where('id', '!=', $workHoursConfiguration->id)
                ->where('is_active', true)
                ->update(['is_active' => false]);
        }

        $validated['updated_by'] = auth()->id();

        $workHoursConfiguration->update($validated);

        return redirect()
            ->route('work-hours-configurations.show', $workHoursConfiguration)
            ->with('success', 'Work hours configuration updated successfully.');
    }

    /**
     * Remove the specified work hours configuration.
     */
    public function destroy(WorkHoursConfiguration $workHoursConfiguration)
    {
        $this->authorize('delete', $workHoursConfiguration);

        if ($workHoursConfiguration->is_active) {
            return redirect()
                ->back()
                ->with('error', 'Cannot delete the active work hours configuration.');
        }

        $workHoursConfiguration->delete();

        return redirect()
            ->route('work-hours-configurations.index')
            ->with('success', 'Work hours configuration deleted successfully.');
    }

    /**
     * Activate a work hours configuration.
     */
    public function activate(WorkHoursConfiguration $workHoursConfiguration)
    {
        $this->authorize('update', $workHoursConfiguration);

        // Deactivate all other configurations
        WorkHoursConfiguration::where('is_active', true)->update(['is_active' => false]);

        // Activate this configuration
        $workHoursConfiguration->update(['is_active' => true]);

        return redirect()
            ->back()
            ->with('success', 'Work hours configuration activated successfully.');
    }
}
