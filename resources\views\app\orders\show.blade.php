@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">file-earmark-text</x-slot>
        <x-slot name="title">Purchase Order #{{ $order->order_id }}</x-slot>
        <x-slot name="subtitle">View complete details of this purchase order</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('orders.index') }}">Purchase Orders</a></li>
            <li class="breadcrumb-item active" aria-current="page">View Order</li>
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group">
                <a href="{{ route('orders.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i> Back to Orders
                </a>
                <a href="{{ route('orders.create') }}" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i> New Order
                </a>
                <button type="button" class="btn btn-primary btn-sm" onclick="printOrder()">
                    <i class="bi bi-printer me-1"></i> Print
                </button>
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <div class="col-lg-8 mb-4 mb-lg-0">
            <!-- Purchase Order Card -->
            <div class="card card-lg mb-4" id="order">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-header-title">
                            <i class="bi bi-file-earmark-text me-2"></i>Purchase Order Details
                        </h5>
                        <span class="badge {{ $order->status_id == 13 ? 'bg-danger' : ($order->approved_by ? 'bg-success' : 'bg-warning') }}">
                            {{ $order->status_id == 13 ? 'Canceled' : ($order->approved_by ? 'Approved' : 'Pending') }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Company and Order Info -->
                    <div class="row mb-5">
                        <div class="col-sm-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <img class="avatar avatar-xl" src="{{ auth()->user()->getGlobalInstance('logo')->path ?? asset('assets/svg/logos/logo-short.svg') }}" alt="Logo">
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h3 class="mb-0 text-primary">{{ auth()->user()->getGlobal("business") ?? 'Your Business'}}</h3>
                                </div>
                            </div>
                            <address class="mb-0">
                                {{ auth()->user()->address ?? '' }}<br>
                                {{ auth()->user()->phone ?? '' }}<br>
                                {{ auth()->user()->email ?? '' }}
                            </address>
                        </div>

                        <div class="col-sm-6 text-sm-end">
                            <h4 class="mb-1">Purchase Order #{{ $order->order_id }}</h4>
                            <div class="mb-3">
                                <span class="d-block">
                                    <span class="fw-semibold">Date Issued:</span>
                                    {{ optional($order->created_at)->format('M d, Y') ?? ''}}
                                </span>
                                <span class="d-block">
                                    <span class="fw-semibold">Due Date:</span>
                                    {{ optional($order->date_to)->format('M d, Y') ?? 'N/A'}}
                                </span>
                                @if($order->approved_by)
                                <span class="d-block">
                                    <span class="fw-semibold">Approved By:</span>
                                    {{ optional($order->approvedBy)->name ?? '' }}
                                </span>
                                <span class="d-block">
                                    <span class="fw-semibold">Approval Date:</span>
                                    {{ optional($order->approved_at)->format('M d, Y') ?? ''}}
                                </span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Supplier Info -->
                    <div class="row mb-5">
                        <div class="col-sm-6">
                            <h5 class="mb-3">Supplier Information:</h5>
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <div class="avatar avatar-soft-primary avatar-circle">
                                        <span class="avatar-initials">{{ substr($order->supplier->name ?? 'S', 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5 class="mb-0">{{ $order->supplier->name ?? 'N/A' }}</h5>
                                </div>
                            </div>
                            <address>
                                {{ $order->supplier->address ?? 'No address provided' }}<br>
                                {{ $order->supplier->phone ?? 'No phone provided' }}<br>
                                {{ $order->supplier->email ?? 'No email provided' }}
                            </address>
                        </div>

                        <div class="col-sm-6">
                            <h5 class="mb-3">Order Information:</h5>
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <div class="avatar avatar-soft-info avatar-circle">
                                        <span class="avatar-initials">{{ substr($order->createdBy->name ?? 'U', 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5 class="mb-0">Created By: {{ $order->createdBy->name ?? 'N/A' }}</h5>
                                    <span class="d-block text-muted">{{ $order->created_at->diffForHumans() }}</span>
                                </div>
                            </div>
                            @if($order->description)
                            <div class="alert alert-soft-info mb-0">
                                <h6 class="mb-1">Order Notes:</h6>
                                <p class="mb-0">{{ $order->description }}</p>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Order Items Table -->
                    <div class="mb-5">
                        <h5 class="mb-3">Order Items:</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Item</th>
                                        <th>Unit</th>
                                        <th class="text-center">Quantity</th>
                                        <th class="text-end">Unit Price</th>
                                        <th class="text-end">Location</th>
                                        <th class="text-center">Expiration Date</th>
                                        <th class="text-end">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($order->stocks as $stock)
                                    <tr>
                                        <td>
                                            <h6 class="mb-1">{{ $stock->product->name ?? '-' }}</h6>
                                            <small class="text-muted">{{ $stock->product->description ?? '' }}</small>
                                        </td>
                                        <td>{{ $stock->unit->name ?? '-' }}</td>
                                        <td class="text-center">
                                            <span class="badge bg-soft-info">{{ $stock->quantity ?? '0' }}</span>
                                        </td>
                                        <td class="text-end">{{ _money($stock->buying_price) }}</td>
                                        <td class="text-end">{{ $stock->location }}</td>
                                        <td class="text-center">
                                            @if($stock->expires_at)
                                                <span class="badge {{ $stock->expires_at->isPast() ? 'bg-danger' : ($stock->expires_at->isToday() ? 'bg-warning' : 'bg-success') }}">
                                                    {{ $stock->expires_at->format('M d, Y') }}
                                                </span>
                                                @if($stock->expires_at->isPast())
                                                    <br><small class="text-danger">This item has expired</small>
                                                @elseif($stock->expires_at->isToday())
                                                    <br><small class="text-warning">This item expires today</small>
                                                @elseif($stock->expires_at->diffInDays() <= 7)
                                                    <br><small class="text-warning">This item expires soon</small>
                                                @endif
                                            @else
                                                <span class="text-muted">No expiration</span>
                                            @endif
                                        </td>
                                        <td class="text-end fw-semibold">{{ _money($stock->quantity * $stock->buying_price) }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="row justify-content-end mb-4">
                        <div class="col-sm-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="mb-3">Order Summary</h5>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Subtotal:</span>
                                        <span>{{ _money($order->sub_total) }}</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>VAT ({{ auth()->user()->getGlobal('vat') ?? '0' }}%):</span>
                                        <span>{{ _money($order->vat) }}</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Discount:</span>
                                        <span class="text-danger">-{{ _money($order->discount) }}</span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="fw-bold">Total:</span>
                                        <span class="fw-bold">{{ _money($order->amount_total) }}</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Amount Paid:</span>
                                        <span class="text-success">{{ _money($order->amount_paid) }}</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span class="fw-bold">Balance Due:</span>
                                        <span class="fw-bold {{ ($order->amount_total - $order->amount_paid) > 0 ? 'text-danger' : 'text-success' }}">
                                            {{ _money($order->amount_total - $order->amount_paid) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Thank You Note -->
                    <div class="text-center border-top pt-4">
                        <h5>Thank you for your business!</h5>
                        <p class="mb-0">If you have any questions concerning this purchase order, please contact us.</p>
                        <small class="text-muted">&copy; {{ date('Y') }} {{ auth()->user()->getGlobal("business") ?? 'Your Business' }}</small>
                    </div>
                </div>
            </div>
            <!-- End Purchase Order Card -->

            <!-- Action Buttons -->
            <div class="d-flex justify-content-between d-print-none mb-4">
                <div>
                    <a href="{{ route('orders.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-arrow-left me-1"></i> Back to Orders
                    </a>

                    @if(!$order->approved_by && $order->status_id != 13)
                        @can('update', $order)
                        <a href="{{ route('orders.edit', $order) }}" class="btn btn-outline-primary">
                            <i class="bi bi-pencil-square me-1"></i> Edit Order
                        </a>
                        @endcan
                    @endif
                </div>

                <div>
                    <a href="{{ route('orders.pdf', $order) }}" class="btn btn-outline-info me-2" target="_blank">
                        <i class="bi bi-file-earmark-pdf me-1"></i> Download PDF
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Order Status Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-info-circle me-2"></i>Order Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-4">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-lg avatar-circle {{ $order->status_id == 13 ? 'avatar-soft-danger' : ($order->approved_by ? 'avatar-soft-success' : 'avatar-soft-warning') }}">
                                <span class="avatar-initials">
                                    <i class="bi {{ $order->status_id == 13 ? 'bi-x-circle' : ($order->approved_by ? 'bi-check-circle' : 'bi-hourglass-split') }}"></i>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1">
                                {{ $order->status_id == 13 ? 'Canceled' : ($order->approved_by ? 'Approved' : 'Pending Approval') }}
                            </h4>
                            <p class="mb-0">
                                {{ $order->status_id == 13 ? 'This order has been canceled' :
                                   ($order->approved_by ? 'This order has been approved and is being processed' :
                                   'This order is waiting for approval') }}
                            </p>
                        </div>
                    </div>

                    @if($order->status_id != 13 && !$order->approved_by && auth()->user()->can('approve order'))
                    <div class="d-grid mb-3">
                        <button type="button" class="btn btn-success pending-order-btn" data-bs-toggle="modal" data-id="{{ $order->id }}" data-bs-target=".pending-order-modal">
                            <i class="bi bi-check-circle me-1"></i> Approve This Order
                        </button>
                    </div>
                    @elseif($order->status_id != 13 && $order->approved_by && auth()->user()->can('cancel orders'))
                    <div class="d-grid mb-3">
                        <button type="button" class="btn btn-danger pending-order-btn" data-bs-toggle="modal" data-id="{{ $order->id }}" data-bs-target=".pending-order-modal">
                            <i class="bi bi-x-circle me-1"></i> Cancel This Order
                        </button>
                    </div>
                    @endif

                    <div class="timeline-step">
                        <div class="timeline-content">
                            <div class="border-start border-3 ps-3 {{ $order->created_at ? 'border-success' : 'border-secondary' }}">
                                <span class="d-block text-muted small">{{ $order->created_at ? $order->created_at->format('M d, Y h:i A') : '' }}</span>
                                <h6 class="mb-1">Order Created</h6>
                                <p class="text-muted mb-0">Created by {{ $order->createdBy->name ?? 'System' }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step mt-3">
                        <div class="timeline-content">
                            <div class="border-start border-3 ps-3 {{ $order->approved_by ? 'border-success' : 'border-secondary' }}">
                                <span class="d-block text-muted small">{{ $order->approved_at ? $order->approved_at->format('M d, Y h:i A') : 'Pending' }}</span>
                                <h6 class="mb-1">Order Approval</h6>
                                <p class="text-muted mb-0">
                                    {{ $order->approved_by ? 'Approved by ' . optional($order->approvedBy)->name : 'Waiting for approval' }}
                                </p>
                            </div>
                        </div>
                    </div>

                    @if($order->status_id == 13)
                    <div class="timeline-step mt-3">
                        <div class="timeline-content">
                            <div class="border-start border-3 ps-3 border-danger">
                                <span class="d-block text-muted small">{{ $order->canceled_at ? $order->canceled_at->format('M d, Y h:i A') : '' }}</span>
                                <h6 class="mb-1">Order Canceled</h6>
                                <p class="text-muted mb-0">
                                    {{ $order->canceled_by ? 'Canceled by ' . optional($order->canceledBy)->name : 'Order was canceled' }}
                                </p>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Payment History Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-credit-card me-2"></i>Payment History
                    </h5>
                </div>
                <div class="card-body p-0">
                    @if(count($order->payments) > 0)
                    <div class="table-responsive">
                        <table class="table table-bordered mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th>#</th>
                                    <th>Amount</th>
                                    <th>Balance</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($order->payments as $key => $payment)
                                <tr>
                                    <td>{{ $key + 1 }}</td>
                                    <td class="text-success">{{ _money($payment->amount) }}</td>
                                    <td>{{ _money($payment->balance) }}</td>
                                    <td>{{ $payment->created_at->format('M d, Y') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="text-center py-4">
                        <div class="avatar avatar-lg avatar-soft-secondary avatar-circle mx-auto mb-3">
                            <span class="avatar-initials"><i class="bi bi-cash"></i></span>
                        </div>
                        <h6>No Payment Records</h6>
                        <p class="text-muted mb-0">No payment transactions have been recorded for this order yet.</p>
                    </div>
                    @endif
                </div>

                @if(($order->amount_total - $order->amount_paid) > 0 && $order->status_id != 13)
                <!-- <div class="card-footer">
                    <button type="button" class="btn btn-primary w-100" data-bs-toggle="modal" data-bs-target="#recordPaymentModal">
                        <i class="bi bi-plus-circle me-1"></i> Record Payment
                    </button>
                </div> -->
                @endif
            </div>
        </div>
    </div>
</div>
<!-- End Content -->

<!-- Record Payment Modal -->
<div class="modal fade" id="recordPaymentModal" tabindex="-1" aria-labelledby="recordPaymentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="recordPaymentModalLabel">Record Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="paymentForm">
                    <div class="mb-3">
                        <label for="paymentAmount" class="form-label">Payment Amount</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" class="form-control" id="paymentAmount" name="amount" step="0.01" min="0.01"
                                   max="{{ $order->amount_total - $order->amount_paid }}"
                                   value="{{ $order->amount_total - $order->amount_paid }}">
                        </div>
                        <div class="form-text">Balance due: {{ _money($order->amount_total - $order->amount_paid) }}</div>
                    </div>

                    <div class="mb-3">
                        <label for="paymentMethod" class="form-label">Payment Method</label>
                        <select class="form-select" id="paymentMethod" name="payment_method">
                            <option value="cash">Cash</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="check">Check</option>
                            <option value="credit_card">Credit Card</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="paymentReference" class="form-label">Reference Number</label>
                        <input type="text" class="form-control" id="paymentReference" name="reference" placeholder="Optional: Enter reference number">
                    </div>

                    <div class="mb-3">
                        <label for="paymentNotes" class="form-label">Notes</label>
                        <textarea class="form-control" id="paymentNotes" name="notes" rows="3" placeholder="Optional: Add payment notes"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="savePaymentBtn">Record Payment</button>
            </div>
        </div>
    </div>
</div>
<!-- End Record Payment Modal -->

@push('styles')
<style>
@media print {
    .d-print-none {
        display: none !important;
    }

    body {
        background: white !important;
        color: #333 !important;
    }

    .card {
        border: 1px solid #333 !important;
        box-shadow: none !important;
        background: white !important;
        page-break-inside: avoid;
    }

    .card-header {
        background: #f8f9fa !important;
        border-bottom: 2px solid #007bff !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .table {
        border-collapse: collapse !important;
    }

    .table th,
    .table td {
        border: 1px solid #333 !important;
        background-color: white !important;
        color: #333 !important;
    }

    .table th {
        background-color: #f8f9fa !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .badge {
        border: 1px solid #333 !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .bg-success {
        background-color: #d4edda !important;
        color: #155724 !important;
    }

    .bg-warning {
        background-color: #fff3cd !important;
        color: #856404 !important;
    }

    .bg-danger {
        background-color: #f8d7da !important;
        color: #721c24 !important;
    }

    .bg-soft-info {
        background-color: #e3f2fd !important;
        color: #1976d2 !important;
    }

    .text-primary {
        color: #007bff !important;
    }

    @page {
        margin: 1in;
        size: A4;
    }
}
</style>
@endpush

@push('scripts')
<script>
    // Print order function
    function printOrder() {
        // Create a new window with only the order content
        var printWindow = window.open('', '_blank', 'width=800,height=600');
        var orderContent = document.getElementById('order').innerHTML;

        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Purchase Order #{{ $order->order_id }}</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        color: #333;
                        line-height: 1.4;
                    }
                    .card {
                        border: none;
                        box-shadow: none;
                    }
                    .card-header {
                        background: #f8f9fa !important;
                        border-bottom: 2px solid #007bff;
                        padding: 15px;
                        margin-bottom: 20px;
                    }
                    .card-body {
                        padding: 0;
                    }
                    .table {
                        border-collapse: collapse;
                        width: 100%;
                        margin-bottom: 20px;
                    }
                    .table th, .table td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: left;
                    }
                    .table th {
                        background-color: #f8f9fa;
                        font-weight: bold;
                    }
                    .badge {
                        display: inline-block;
                        padding: 4px 8px;
                        border-radius: 4px;
                        font-size: 11px;
                        font-weight: bold;
                    }
                    .bg-success { background-color: #d4edda; color: #155724; }
                    .bg-warning { background-color: #fff3cd; color: #856404; }
                    .bg-danger { background-color: #f8d7da; color: #721c24; }
                    .bg-soft-info { background-color: #e3f2fd; color: #1976d2; }
                    .text-primary { color: #007bff; }
                    .text-muted { color: #6c757d; }
                    .text-end { text-align: right; }
                    .text-center { text-align: center; }
                    .fw-semibold { font-weight: 600; }
                    .mb-0 { margin-bottom: 0; }
                    .mb-1 { margin-bottom: 0.25rem; }
                    .mb-3 { margin-bottom: 1rem; }
                    .mb-5 { margin-bottom: 3rem; }
                    .d-flex { display: flex; }
                    .justify-content-between { justify-content: space-between; }
                    .align-items-center { align-items: center; }
                    .border-top { border-top: 1px solid #dee2e6; }
                    .pt-4 { padding-top: 1.5rem; }
                    @media print {
                        body { margin: 0; }
                        .d-print-none { display: none !important; }
                    }
                </style>
            </head>
            <body>
                ${orderContent}
                <script>
                    window.onload = function() {
                        window.print();
                        window.onafterprint = function() {
                            window.close();
                        }
                    }
                </script>
            </body>
            </html>
        `);

        printWindow.document.close();
    }


</script>
@endpush

@endsection
