@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">speedometer2</x-slot>
        <x-slot name="title">Dashboard</x-slot>
        <x-slot name="subtitle">
            Welcome back, {{ head(explode(' ', trim(auth()->user()->name))) ?? '' }}
            @if(auth()->user()->branch_id)
                from {{ auth()->user()->branch->name ?? '' }}
            @endif
        </x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item active" aria-current="page">Dashboard</li>
        </x-slot>
        <x-slot name="controls">
            <a href="/close-business-hour" class="btn btn-primary">
                <i class="bi bi-clock-history me-1"></i> Close Business Day
            </a>
        </x-slot>
    </x-page-header>
    <!-- End <PERSON> Header -->

    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-header-title">Filter Dashboard Data</h5>
        </div>
        <div class="card-body">
            <form action="" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">Date Range</label>
                    <input type="hidden" name="from" value="{{ request()->from }}">
                    <input type="hidden" name="to" value="{{ request()->to }}">
                    <button type="button" id="js-daterangepicker-predefined" class="btn btn-white w-100">
                        <i class="bi-calendar-week me-1"></i>
                        <span class="js-daterangepicker-predefined-preview">Select date range</span>
                    </button>
                </div>

                <div class="col-md-4">
                    <label class="form-label">Product</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select product-filter" name="product_id" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a product..."
                        }'>
                            <option value="0">All Products</option>
                            @foreach(App\Models\Product::get() as $product)
                                <option value="{{ $product->id }}" @if($product->id == request()->product_id) selected @endif>
                                    {{ $product->name }} ~ {{ $product->description }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="col-md-4">
                    <label class="form-label">Closing Day</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select day-filter" name="day" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a closing day..."
                        }'>
                            <option value="0">All Closing Days</option>
                            @foreach(Facades\App\Cache\Repo::getClosingDays() as $day)
                                <option value="{{ $day }}" @if($day == request()->day) selected @endif>
                                    {{ $day }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="col-12 d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-filter me-1"></i> Apply Filters
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- End Filters Card -->

    <!-- Stats Overview -->
    <div class="row mb-4">
        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-subtitle mb-2">Total Sales</h6>
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="card-title text-inherit">{{ _money(Facades\App\Cache\Repo::getSaleMoney()) }}</h2>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-soft-primary text-primary p-2">
                                <i class="bi bi-cart-check fs-3"></i>
                            </span>
                        </div>
                    </div>
                    <span class="badge bg-soft-primary text-primary">
                        Revenue from sales
                    </span>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-subtitle mb-2">Inventory Value</h6>
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="card-title text-inherit">{{ _money(Facades\App\Cache\Repo::getClosingStockQtyValue()) }}</h2>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-soft-info text-info p-2">
                                <i class="bi bi-box-seam fs-3"></i>
                            </span>
                        </div>
                    </div>
                    <span class="badge bg-soft-info text-info">
                        Current stock value
                    </span>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-subtitle mb-2">Total Discounts</h6>
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="card-title text-inherit">{{ _money(Facades\App\Cache\Repo::getDiscountMoney()) }}</h2>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-soft-warning text-warning p-2">
                                <i class="bi bi-percent fs-3"></i>
                            </span>
                        </div>
                    </div>
                    <span class="badge bg-soft-warning text-warning">
                        Discounts applied
                    </span>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-subtitle mb-2">Receivables</h6>
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="card-title text-inherit">{{ _money(Facades\App\Cache\Repo::getReceivableMoney()) }}</h2>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-soft-danger text-danger p-2">
                                <i class="bi bi-cash-stack fs-3"></i>
                            </span>
                        </div>
                    </div>
                    <span class="badge bg-soft-danger text-danger">
                        Outstanding payments
                    </span>
                </div>
            </div>
        </div>
    </div>
    <!-- End Stats Overview -->

    <!-- Sales Chart -->
    @if(auth()->user()->can('view sales revenue'))
    <div class="card mb-4">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">
                        Sales Revenue
                        <i class="bi-question-circle text-body ms-1" data-bs-toggle="tooltip" data-bs-placement="top" 
                           title="Net sales (gross sales minus discounts and returns) plus taxes and shipping. Includes orders from all sales channels."></i>
                    </h4>
                </div>
                <div class="col-auto">
                    <div class="tom-select-custom">
                        <select class="js-select form-select form-select-sm year" autocomplete="off" data-hs-tom-select-options='{
                            "searchInDropdown": false,
                            "hideSearch": true,
                            "dropdownWidth": "10rem"
                        }'>
                            @for($i = 0; $i < 5; $i++)
                                @php $yr = date('Y') - $i @endphp
                                <option value="{{ $yr }}">{{ $yr }}</option>
                            @endfor
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-lg-9 mb-4 mb-lg-0">
                    <div class="chartjs-custom" style="height: 20rem;">
                        <canvas id="ecommerce-sales" class="js-chart" data-hs-chartjs-options='{
                            "type": "bar",
                            "data": {
                                "labels": ["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],
                                "datasets": [{
                                    "data": @json(Facades\App\Cache\Repo::getAnnualSales()),
                                    "backgroundColor": "#377dff",
                                    "hoverBackgroundColor": "#377dff",
                                    "borderColor": "#377dff",
                                    "maxBarThickness": "10"
                                }]
                            },
                            "options": {
                                "scales": {
                                    "y": {
                                        "grid": {
                                            "color": "#e7eaf3",
                                            "drawBorder": false,
                                            "zeroLineColor": "#e7eaf3"
                                        },
                                        "ticks": {
                                            "beginAtZero": true,
                                            "stepSize": 100,
                                            "color": "#97a4af",
                                            "font": {
                                                "size": 12,
                                                "family": "Open Sans, sans-serif"
                                            },
                                            "padding": 10
                                        }
                                    },
                                    "x": {
                                        "grid": {
                                            "display": false,
                                            "drawBorder": false
                                        },
                                        "ticks": {
                                            "color": "#97a4af",
                                            "font": {
                                                "size": 12,
                                                "family": "Open Sans, sans-serif"
                                            },
                                            "padding": 5
                                        },
                                        "categoryPercentage": 0.5,
                                        "maxBarThickness": "10"
                                    }
                                },
                                "cornerRadius": 2,
                                "plugins": {
                                    "tooltip": {
                                        "hasIndicator": true,
                                        "mode": "index",
                                        "intersect": false
                                    }
                                },
                                "hover": {
                                    "mode": "nearest",
                                    "intersect": true
                                }
                            }
                        }'></canvas>
                    </div>
                </div>

                <div class="col-lg-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <h5 class="card-title">Annual Revenue</h5>
                            <div class="d-flex justify-content-center align-items-center my-4">
                                <span class="display-4 text-dark">
                                    {{ _money(array_sum(Facades\App\Cache\Repo::getAnnualSales())) }}
                                </span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="text-muted mb-1">Monthly Avg</h6>
                                    <span class="h5">
                                        {{ _money(array_sum(Facades\App\Cache\Repo::getAnnualSales()) / 12) }}
                                    </span>
                                </div>
                                <div>
                                    <h6 class="text-muted mb-1">Growth</h6>
                                    <span class="h5 text-success">
                                        <i class="bi-graph-up me-1"></i> 12.5%
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
    <!-- End Sales Chart -->

    <div class="row">
        <!-- Quick Access -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h4 class="card-header-title">Quick Access</h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        @can('view-any', App\Models\Product::class)
                        <a class="card card-hover-shadow h-100" href="/products">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="avatar avatar-sm avatar-soft-primary avatar-circle">
                                            <i class="bi bi-box"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5 class="text-inherit mb-0">Products</h5>
                                        <span class="text-body small">Manage inventory</span>
                                    </div>
                                    <div class="ms-2">
                                        <i class="bi-chevron-right text-body"></i>
                                    </div>
                                </div>
                            </div>
                        </a>
                        @endcan

                        @can('view-any', App\Models\Stock::class)
                        <a class="card card-hover-shadow h-100" href="{{ route('stocks.index') }}">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="avatar avatar-sm avatar-soft-info avatar-circle">
                                            <i class="bi bi-boxes"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5 class="text-inherit mb-0">Stock Inventory</h5>
                                        <span class="text-body small">Manage stock levels</span>
                                    </div>
                                    <div class="ms-2">
                                        <i class="bi-chevron-right text-body"></i>
                                    </div>
                                </div>
                            </div>
                        </a>
                        @endcan

                        @can('view-any', App\Models\Stock::class)
                        <a class="card card-hover-shadow h-100" href="{{ route('stocks.transfers') }}">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="avatar avatar-sm avatar-soft-warning avatar-circle">
                                            <i class="bi bi-arrow-left-right"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5 class="text-inherit mb-0">Stock Transfers</h5>
                                        <span class="text-body small">Transfer between warehouses</span>
                                    </div>
                                    <div class="ms-2">
                                        <i class="bi-chevron-right text-body"></i>
                                    </div>
                                </div>
                            </div>
                        </a>
                        @endcan

                        @can('view-any', App\Models\Invoice::class)
                        <a class="card card-hover-shadow h-100" href="/invoices">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="avatar avatar-sm avatar-soft-success avatar-circle">
                                            <i class="bi bi-receipt"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5 class="text-inherit mb-0">Sales</h5>
                                        <span class="text-body small">Manage invoices</span>
                                    </div>
                                    <div class="ms-2">
                                        <i class="bi-chevron-right text-body"></i>
                                    </div>
                                </div>
                            </div>
                        </a>
                        @endcan

                        @can('view-any', App\Models\Branch::class)
                        <a class="card card-hover-shadow h-100" href="/branches">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="avatar avatar-sm avatar-soft-info avatar-circle">
                                            <i class="bi bi-building"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5 class="text-inherit mb-0">Branches</h5>
                                        <span class="text-body small">Manage locations</span>
                                    </div>
                                    <div class="ms-2">
                                        <i class="bi-chevron-right text-body"></i>
                                    </div>
                                </div>
                            </div>
                        </a>
                        @endcan

                        <a class="card card-hover-shadow h-100" href="/reports/stock">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="avatar avatar-sm avatar-soft-warning avatar-circle">
                                            <i class="bi bi-graph-up"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5 class="text-inherit mb-0">Reports</h5>
                                        <span class="text-body small">View analytics</span>
                                    </div>
                                    <div class="ms-2">
                                        <i class="bi-chevron-right text-body"></i>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Quick Access -->

        <!-- Top Products -->
        <div class="col-lg-8 mb-4">
                <!-- Recent Activity -->
    <div class="card mb-4">
        <div class="card-header">
            <h4 class="card-header-title">Recent Activity</h4>
        </div>
        <div class="card-body">
            <ul class="step step-icon-xs">
                @php
                    $recentInvoices = App\Models\Invoice::orderBy('created_at', 'desc')->take(5)->get();
                @endphp
                
                @forelse($recentInvoices as $invoice)
                <li class="step-item">
                    <div class="step-content-wrapper">
                        <span class="step-icon step-icon-soft-primary">
                            <i class="bi bi-receipt"></i>
                        </span>
                        <div class="step-content">
                            <h5 class="mb-1">
                                <a href="{{ route('invoices.show', $invoice) }}">Invoice #{{ $invoice->invoice_id }}</a>
                                <span class="badge {{ $invoice->approved_by ? 'bg-soft-success text-success' : 'bg-soft-warning text-warning' }}">
                                    {{ $invoice->approved_by ? 'Approved' : 'Pending' }}
                                </span>
                            </h5>
                            <p class="mb-0">
                                {{ $invoice->customer_name ?? 'Customer' }} - 
                                {{ _money($invoice->amount_total) }} - 
                                {{ $invoice->created_at->diffForHumans() }}
                            </p>
                            <p class="text-muted small">
                                Created by {{ $invoice->createdBy->name ?? 'User' }}
                            </p>
                        </div>
                    </div>
                </li>
                @empty
                <li class="step-item">
                    <div class="step-content-wrapper">
                        <div class="step-content text-center py-4">
                            <i class="bi bi-activity text-muted" style="font-size: 2rem;"></i>
                            <p class="mt-2">No recent activity found</p>
                        </div>
                    </div>
                </li>
                @endforelse
            </ul>
        </div>
        <div class="card-footer text-center">
            <a href="{{ route('invoices.index') }}" class="btn btn-outline-primary btn-sm">
                <i class="bi bi-eye me-1"></i> View All Activity
            </a>
        </div>
    </div>
    <!-- End Recent Activity -->
        </div>
        <!-- End Top Products -->
    </div>
    <!-- End Row -->


</div>
<!-- End Content -->
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialize any additional dashboard-specific scripts here
    });
</script>
@endpush