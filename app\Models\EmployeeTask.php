<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\CreatedUpdatedTrait;

class EmployeeTask extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'employee_time_record_id',
        'task_description',
        'time_spent',
        'start_time',
        'end_time',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'time_spent' => 'decimal:2',
        'start_time' => 'datetime:H:i:s',
        'end_time' => 'datetime:H:i:s',
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];

    protected $appends = ['formatted_start_time', 'formatted_end_time'];

    public function employeeTimeRecord()
    {
        return $this->belongsTo(EmployeeTimeRecord::class);
    }

    public function getFormattedStartTimeAttribute()
    {
        return $this->start_time ? date('H:i', strtotime($this->start_time)) : null;
    }

    public function getFormattedEndTimeAttribute()
    {
        return $this->end_time ? date('H:i', strtotime($this->end_time)) : null;
    }

    /**
     * Calculate time spent based on start and end times.
     */
    public function calculateTimeSpent()
    {
        if ($this->start_time && $this->end_time) {
            $start = strtotime($this->start_time);
            $end = strtotime($this->end_time);
            $this->time_spent = ($end - $start) / 3600; // Convert to hours
        }
    }

    /**
     * Boot method to automatically calculate time spent.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($task) {
            if ($task->start_time && $task->end_time && !$task->time_spent) {
                $task->calculateTimeSpent();
            }
        });
    }
}
