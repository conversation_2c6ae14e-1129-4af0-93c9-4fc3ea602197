<?php
require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Tenant;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

// Create landlord database tables if they don't exist
try {
    if (!Schema::hasTable('tenants')) {
        Schema::create('tenants', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->string('password')->nullable();
            $table->string('current_tenant')->nullable();
            $table->string('domain')->unique();
            $table->string('database')->unique();
            $table->timestamps();
        });
        echo "Created tenants table\n";
    }
} catch (\Exception $e) {
    echo "Error creating tenants table: " . $e->getMessage() . "\n";
}

// Create a tenant
try {
    $tenant = Tenant::create([
        'name' => 'Test Company',
        'email' => '<EMAIL>',
        'phone' => '**********',
        'password' => Hash::make('password'),
        'domain' => 'test',
        'database' => 'accounting_test',
    ]);

    echo "Created tenant: " . $tenant->name . "\n";
    echo "Database: " . $tenant->database . "\n";

    // Create the tenant database
    DB::statement("CREATE DATABASE IF NOT EXISTS `{$tenant->database}`");
    echo "Created tenant database\n";

    // Configure tenant connection
    config([
        'database.connections.tenant' => [
            'driver' => env('DB_CONNECTION', 'mysql'),
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '3306'),
            'database' => $tenant->database,
            'username' => env('DB_USERNAME', 'root'),
            'password' => env('DB_PASSWORD', ''),
        ],
    ]);

    // Purge the connection to ensure the new config is used
    DB::purge('tenant');

    // Make the tenant current
    $tenant->makeCurrent();
    echo "Made tenant current\n";

    // Run migrations for the tenant
    echo "Running migrations...\n";
    Artisan::call('migrate', [
        '--database' => 'tenant',
        '--force' => true,
    ]);
    echo Artisan::output();

    // Run seeders for the tenant
    echo "Running seeders...\n";
    Artisan::call('db:seed', [
        '--database' => 'tenant',
        '--force' => true,
    ]);
    echo Artisan::output();

    // Run specific accounting seeders
    echo "Running accounting seeders...\n";
    Artisan::call('db:seed', [
        '--class' => 'PermissionsSeeder',
        '--database' => 'tenant',
        '--force' => true,
    ]);
    echo Artisan::output();

    Artisan::call('db:seed', [
        '--class' => 'ChartOfAccountsSeeder',
        '--database' => 'tenant',
        '--force' => true,
    ]);
    echo Artisan::output();

    Artisan::call('db:seed', [
        '--class' => 'FiscalYearSeeder',
        '--database' => 'tenant',
        '--force' => true,
    ]);
    echo Artisan::output();

    Artisan::call('db:seed', [
        '--class' => 'CurrencySeeder',
        '--database' => 'tenant',
        '--force' => true,
    ]);
    echo Artisan::output();

    Artisan::call('db:seed', [
        '--class' => 'AssetCategorySeeder',
        '--database' => 'tenant',
        '--force' => true,
    ]);
    echo Artisan::output();

    Artisan::call('db:seed', [
        '--class' => 'TaxTypeSeeder',
        '--database' => 'tenant',
        '--force' => true,
    ]);
    echo Artisan::output();

    echo "Tenant setup complete!\n";

} catch (\Exception $e) {
    echo "Error creating tenant: " . $e->getMessage() . "\n";
}
