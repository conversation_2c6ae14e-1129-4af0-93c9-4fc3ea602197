@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">bag-check</x-slot>
        <x-slot name="title">Purchase Orders</x-slot>
        <x-slot name="subtitle">Manage and track all purchase orders from suppliers</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item active" aria-current="page">Purchase Orders</li>
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group">
                @can('create', App\Models\Order::class)
                <a href="{{ route('orders.adjustments') }}" class="btn btn-outline-danger btn-sm">
                    <i class="bi bi-arrow-return-left me-1"></i>Order Adjustments
                </a>
                @endcan    
                @can('create', App\Models\Order::class)
                <a href="{{ route('orders.create') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>New Purchase Order
                </a>
                @endcan

            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Purchase Order Overview Cards -->
    <div class="row mb-4">
        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-soft-primary avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-currency-dollar"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1">{{ _money($orders->sum('amount_total')) }}</h4>
                            <span class="d-block">Total Purchase Value</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-soft-success avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-check-circle"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1">{{ $orders->whereNotNull('approved_by')->count() }}</h4>
                            <span class="d-block">Approved Orders</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-sm-6 col-lg-3 mb-3 mb-sm-0">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-soft-warning avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-hourglass-split"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1">{{ $orders->whereNull('approved_by')->where('status_id', '!=', 13)->count() }}</h4>
                            <span class="d-block">Pending Approval</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-sm-6 col-lg-3">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-soft-danger avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-x-circle"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1">{{ $orders->where('status_id', 13)->count() }}</h4>
                            <span class="d-block">Canceled Orders</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Purchase Order Overview Cards -->

    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ route('orders.index') }}" method="GET" class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" name="search" 
                               placeholder="Search by order ID or supplier..." value="{{ request('search') ?? '' }}">
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="input-group">
                        <button id="js-daterangepicker-predefined" type="button" class="btn btn-outline-secondary w-100">
                            <i class="bi-calendar-week me-1"></i>
                            <span class="js-daterangepicker-predefined-preview">Select date range</span>
                        </button>
                        <input type="hidden" name="from" value="{{ request()->from }}">
                        <input type="hidden" name="to" value="{{ request()->to }}">
                    </div>
                </div>
                
                <div class="col-md-2">
                    <select class="form-select" name="status">
                        <option value="">All Statuses</option>
                        <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="canceled" {{ request('status') == 'canceled' ? 'selected' : '' }}>Canceled</option>
                    </select>
                </div>
                
                <div class="col-md-2 d-flex">
                    <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                </div>
            </form>
        </div>
    </div>
    <!-- End Filters and Search -->

    <!-- Purchase Orders Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-header-title">
                    <i class="bi bi-list-ul me-2"></i>Purchase Orders
                </h5>
            </div>
        </div>
        
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th style="width: 10%">Order ID</th>
                        <th style="width: 25%">Supplier</th>
                        <th style="width: 12%">Date</th>
                        <th style="width: 12%">Total Amount</th>
                        <th style="width: 10%">Paid</th>
                        <th style="width: 10%">Balance</th>
                        <th style="width: 10%">Status</th>
                        <th style="width: 11%" class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($orders as $order)
                    <tr class="@if(in_array($order->status_id, [13,14])) bg-soft-danger @endif">
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0 me-2">
                                    @if($order->status_id == 13)
                                        <div class="avatar avatar-xs avatar-soft-danger avatar-circle">
                                            <span class="avatar-initials"><i class="bi bi-x"></i></span>
                                        </div>
                                    @elseif($order->approved_by)
                                        <div class="avatar avatar-xs avatar-soft-success avatar-circle">
                                            <span class="avatar-initials"><i class="bi bi-check"></i></span>
                                        </div>
                                    @else
                                        <div class="avatar avatar-xs avatar-soft-warning avatar-circle">
                                            <span class="avatar-initials"><i class="bi bi-clock"></i></span>
                                        </div>
                                    @endif
                                </div>
                                <div class="flex-grow-1">
                                    <span class="fw-semibold">#{{ $order->order_id ?? $order->id }}</span>
                                    <br>
                                    <small class="text-muted">ID: {{ $order->id }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar avatar-soft-primary avatar-circle">
                                        <span class="avatar-initials">{{ substr($order->supplier->name ?? 'S', 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-0">{{ $order->supplier->name ?? '-' }}</h6>
                                    <small class="text-muted">Created by: {{ $order->createdBy->name ?? '-' }}</small>
                                </div>
                            </div>
                        </td>
                        <td>{{ $order->created_at->format('M d, Y') ?? '-' }}</td>
                        <td class="fw-semibold">{{ _money($order->amount_total) ?? '-' }}</td>
                        <td>{{ _money($order->amount_paid) ?? '-' }}</td>
                        <td>
                            @php $balance = $order->amount_total - $order->amount_paid; @endphp
                            <span class="badge {{ $balance > 0 ? 'bg-warning' : 'bg-success' }}">
                                {{ _money($balance) }}
                            </span>
                        </td>
                        <td>
                            @if($order->status_id == 13)
                                <span class="badge bg-danger">Canceled</span>
                            @elseif($order->approved_by)
                                <span class="badge bg-success">Approved</span>
                            @else
                                <span class="badge bg-warning">Pending</span>
                            @endif
                        </td>
                        <td class="text-center">
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button"
                                        id="orderActions{{ $order->id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-three-dots"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="orderActions{{ $order->id }}">
                                    @can('view', $order)
                                    <li>
                                        <button type="button" class="dropdown-item preview-order-btn"
                                                data-bs-toggle="modal" data-id="{{ $order->id }}" data-bs-target=".preview-order-modal">
                                            <i class="bi bi-eye me-2"></i>View Details
                                        </button>
                                    </li>
                                    @endcan

                                    @can('view', $order)
                                    <li>
                                        <a href="{{ route('orders.show', $order) }}" class="dropdown-item">
                                            <i class="bi bi-file-text me-2"></i>Open Order
                                        </a>
                                    </li>
                                    @endcan

                                    @if(!$order->approved_by && $order->status_id != 13)
                                        @can('update', $order)
                                        <li>
                                            <a href="{{ route('orders.edit', $order) }}" class="dropdown-item">
                                                <i class="bi bi-pencil me-2"></i>Edit Order
                                            </a>
                                        </li>
                                        @endcan

                                        <li><hr class="dropdown-divider"></li>

                                        @if(auth()->user()->can('approve order'))
                                        <li>
                                            <button type="button" class="dropdown-item text-success pending-order-btn"
                                                    data-bs-toggle="modal" data-id="{{ $order->id }}" data-bs-target=".pending-order-modal">
                                                <i class="bi bi-check-circle me-2"></i>Approve Order
                                            </button>
                                        </li>
                                        @endif

                                        <li><hr class="dropdown-divider"></li>

                                        @can('delete', $order)
                                        <li>
                                            <form action="{{ route('orders.destroy', $order) }}" method="POST" class="d-inline w-100"
                                                  onsubmit="return confirm('Are you sure you want to delete this order? This action cannot be undone.')">
                                                @csrf @method('DELETE')
                                                <button type="submit" class="dropdown-item text-danger">
                                                    <i class="bi bi-trash me-2"></i>Delete Order
                                                </button>
                                            </form>
                                        </li>
                                        @endcan
                                    @else
                                        @if($order->approved_by && $order->status_id != 13)
                                            @if(auth()->user()->can('cancel orders'))
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <button type="button" class="dropdown-item text-danger pending-order-btn"
                                                        data-bs-toggle="modal" data-id="{{ $order->id }}" data-bs-target=".pending-order-modal">
                                                    <i class="bi bi-x-circle me-2"></i>Cancel Order
                                                </button>
                                            </li>
                                            @endif
                                        @endif
                                    @endif

                                    <!-- Additional Actions -->
                                    <li><hr class="dropdown-divider"></li>

                                    @if($order->approved_by && $order->status_id != 13)
                                    @can('create', App\Models\Order::class)
                                    <li>
                                        <a href="{{ route('orders.create-adjustment', $order) }}" class="dropdown-item text-warning">
                                            <i class="bi bi-arrow-return-left me-2"></i>Create Adjustment
                                        </a>
                                    </li>
                                    @endcan
                                    @endif

                                    <li>
                                        <a href="{{ route('orders.pdf', $order) }}" class="dropdown-item">
                                            <i class="bi bi-download me-2"></i>Export PDF
                                        </a>
                                    </li>
                                    @if($order->supplier)
                                    <li>
                                        <a href="#" class="dropdown-item">
                                            <i class="bi bi-envelope me-2"></i>Email to Supplier
                                        </a>
                                    </li>
                                    @endif
                                </ul>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="text-center p-5">
                            <x-empty-state 
                                icon="bag" 
                                title="No purchase orders found" 
                                message="No purchase orders match your search criteria or no orders have been created yet."
                            />
                        </td>
                    </tr>
                    @endforelse
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3" class="fw-bold">Totals:</td>
                        <td class="fw-bold">{{ _money($orders->sum('amount_total')) }}</td>
                        <td class="fw-bold">{{ _money($orders->sum('amount_paid')) }}</td>
                        <td class="fw-bold">{{ _money($orders->sum('amount_total') - $orders->sum('amount_paid')) }}</td>
                        <td colspan="2"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <!-- End Purchase Orders Table -->
</div>
<!-- End Content -->

@push('styles')
<style>
    /* Custom dropdown styling */
    .dropdown-menu {
        border: 1px solid #e7eaf3;
        box-shadow: 0 0.375rem 1.5rem rgba(140, 152, 164, 0.175);
        border-radius: 0.5rem;
        min-width: 180px;
    }

    .dropdown-item {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        transition: all 0.15s ease-in-out;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
        transform: translateX(2px);
    }

    .dropdown-item.text-success:hover {
        background-color: #d1e7dd;
        color: #0a3622 !important;
    }

    .dropdown-item.text-danger:hover {
        background-color: #f8d7da;
        color: #58151c !important;
    }

    .dropdown-divider {
        margin: 0.25rem 0;
    }

    /* Action button styling */
    .dropdown-toggle::after {
        display: none;
    }

    .btn-outline-secondary.dropdown-toggle {
        border: 1px solid #e7eaf3;
        color: #677788;
    }

    .btn-outline-secondary.dropdown-toggle:hover {
        background-color: #f8f9fa;
        border-color: #d1d5db;
    }

    /* Table row hover effect */
    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    /* Status badge improvements */
    .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }
</style>
@endpush

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialize datatable
        dataTableBtn();

        // Prevent dropdown from closing when clicking on form elements
        $('.dropdown-menu').on('click', 'form', function(e) {
            e.stopPropagation();
        });

        // Add confirmation for delete actions
        $('.dropdown-menu form[method="POST"]').on('submit', function(e) {
            const orderText = $(this).closest('tr').find('td:first').text().trim();
            if (!confirm(`Are you sure you want to delete order ${orderText}? This action cannot be undone.`)) {
                e.preventDefault();
                return false;
            }
        });

        // Add smooth animation for dropdown items
        $('.dropdown').on('show.bs.dropdown', function() {
            $(this).find('.dropdown-menu').addClass('animate__animated animate__fadeIn animate__faster');
        });

        // Handle action button clicks
        $('.dropdown-item').on('click', function(e) {
            const $this = $(this);

            // Add loading state for actions that navigate away
            if ($this.attr('href') && !$this.attr('href').startsWith('#')) {
                $this.html('<i class="bi bi-arrow-clockwise spin me-2"></i>' + $this.text());
            }
        });
    });
</script>
@endpush

@endsection