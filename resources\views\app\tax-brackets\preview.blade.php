@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="bi bi-calculator me-2"></i>Tax Calculator Preview
                        </h4>
                        <a href="{{ route('tax-brackets.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i>Back to Tax Brackets
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Tax Calculator Form -->
                    <form method="GET" action="{{ route('tax-brackets.preview') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="amount" class="form-label">Gross Salary (K)</label>
                                <input type="number" name="amount" id="amount" class="form-control" 
                                       value="{{ $amount }}" placeholder="Enter gross salary amount" 
                                       min="0" step="0.01">
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="bi bi-calculator me-1"></i>Calculate Tax
                                </button>
                                <button type="button" id="clear-btn" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-lg me-1"></i>Clear
                                </button>
                            </div>
                        </div>
                    </form>

                    @if($breakdown)
                    <!-- Tax Calculation Results -->
                    <div class="row">
                        <!-- Tax Breakdown -->
                        <div class="col-md-8">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">Tax Breakdown for K{{ number_format($amount, 2) }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Tax Bracket</th>
                                                    <th>Range</th>
                                                    <th>Rate</th>
                                                    <th class="text-end">Taxable Amount</th>
                                                    <th class="text-end">Tax Amount</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($breakdown['breakdown'] as $item)
                                                <tr class="{{ $item['tax_amount'] > 0 ? '' : 'text-muted' }}">
                                                    <td>
                                                        <strong>{{ $item['bracket']->name ?: 'Bracket ' . $item['bracket']->order }}</strong>
                                                    </td>
                                                    <td>
                                                        K{{ number_format($item['bracket']->min_amount, 0) }} - 
                                                        @if($item['bracket']->max_amount)
                                                            K{{ number_format($item['bracket']->max_amount, 0) }}
                                                        @else
                                                            Unlimited
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">{{ number_format($item['rate_percentage'], 2) }}%</span>
                                                    </td>
                                                    <td class="text-end">
                                                        K{{ number_format($item['taxable_amount'], 2) }}
                                                    </td>
                                                    <td class="text-end">
                                                        <strong class="{{ $item['tax_amount'] > 0 ? 'text-primary' : 'text-muted' }}">
                                                            K{{ number_format($item['tax_amount'], 2) }}
                                                        </strong>
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                            <tfoot class="table-dark">
                                                <tr>
                                                    <th colspan="4">Total Tax</th>
                                                    <th class="text-end">K{{ number_format($breakdown['total_tax'], 2) }}</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Summary -->
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-header">
                                    <h6 class="mb-0 text-white">Summary</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Gross Salary:</span>
                                        <strong>K{{ number_format($amount, 2) }}</strong>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Total Tax:</span>
                                        <strong>K{{ number_format($breakdown['total_tax'], 2) }}</strong>
                                    </div>
                                    <hr class="border-light">
                                    <div class="d-flex justify-content-between">
                                        <span>Net Salary:</span>
                                        <strong class="fs-5">K{{ number_format($breakdown['net_amount'], 2) }}</strong>
                                    </div>
                                    <div class="mt-3">
                                        <small>
                                            Effective Tax Rate: 
                                            <strong>{{ number_format(($breakdown['total_tax'] / $amount) * 100, 2) }}%</strong>
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Examples -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6 class="mb-0">Quick Examples</h6>
                                </div>
                                <div class="card-body">
                                    @php
                                        $examples = [50000, 150000, 500000, 1000000, 2000000];
                                    @endphp
                                    @foreach($examples as $example)
                                    <div class="d-flex justify-content-between mb-1">
                                        <a href="{{ route('tax-brackets.preview', ['amount' => $example]) }}" 
                                           class="text-decoration-none small">
                                            K{{ number_format($example, 0) }}
                                        </a>
                                        <small class="text-muted">
                                            K{{ number_format(\App\Models\TaxBracket::calculateTax($example), 0) }}
                                        </small>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Current Tax Brackets -->
                    <div class="mt-4">
                        <h6 class="text-muted">Current Active Tax Brackets</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Order</th>
                                        <th>Name</th>
                                        <th>Range</th>
                                        <th>Rate</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($taxBrackets as $bracket)
                                    <tr>
                                        <td><span class="badge bg-primary">{{ $bracket->order }}</span></td>
                                        <td>{{ $bracket->name ?: 'Bracket ' . $bracket->order }}</td>
                                        <td>
                                            K{{ number_format($bracket->min_amount, 0) }} - 
                                            @if($bracket->max_amount)
                                                K{{ number_format($bracket->max_amount, 0) }}
                                            @else
                                                Unlimited
                                            @endif
                                        </td>
                                        <td><span class="badge bg-info">{{ $bracket->formatted_rate }}</span></td>
                                        <td>
                                            @if($bracket->is_active)
                                                <span class="badge bg-success">Active</span>
                                            @else
                                                <span class="badge bg-secondary">Inactive</span>
                                            @endif
                                        </td>
                                    </tr>
                                    @empty
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">
                                            No tax brackets configured. 
                                            <a href="{{ route('tax-brackets.create') }}">Create one now</a>.
                                        </td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const clearBtn = document.getElementById('clear-btn');
    const amountInput = document.getElementById('amount');
    
    clearBtn.addEventListener('click', function() {
        amountInput.value = '';
        window.location.href = '{{ route("tax-brackets.preview") }}';
    });
    
    // Auto-submit on Enter
    amountInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.target.closest('form').submit();
        }
    });
});
</script>
@endpush
@endsection
