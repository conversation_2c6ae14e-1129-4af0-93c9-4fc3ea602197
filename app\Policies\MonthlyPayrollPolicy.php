<?php

namespace App\Policies;

use App\Models\User;
use App\Models\MonthlyPayroll;
use Illuminate\Auth\Access\HandlesAuthorization;

class MonthlyPayrollPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo('list monthly-payrolls');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\MonthlyPayroll  $monthlyPayroll
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, MonthlyPayroll $monthlyPayroll)
    {
        return $user->hasPermissionTo('view monthly-payrolls');
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo('create monthly-payrolls');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\MonthlyPayroll  $monthlyPayroll
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, MonthlyPayroll $monthlyPayroll)
    {
        return $user->hasPermissionTo('update monthly-payrolls');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\MonthlyPayroll  $monthlyPayroll
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, MonthlyPayroll $monthlyPayroll)
    {
        return $user->hasPermissionTo('delete monthly-payrolls');
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\MonthlyPayroll  $monthlyPayroll
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, MonthlyPayroll $monthlyPayroll)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\MonthlyPayroll  $monthlyPayroll
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, MonthlyPayroll $monthlyPayroll)
    {
        return false;
    }
}
