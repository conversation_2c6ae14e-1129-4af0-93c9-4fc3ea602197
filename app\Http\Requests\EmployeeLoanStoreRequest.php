<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EmployeeLoanStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return $this->user()->can('create', \App\Models\EmployeeLoan::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'employee_id' => 'required|tenant_exists:employees,id',
            'loan_amount' => 'required|numeric|min:1|max:10000000',
            'installment_amount' => 'required|numeric|min:1|max:' . $this->loan_amount,
            'loan_date' => 'required|date|before_or_equal:today',
            'first_deduction_date' => 'required|date|after:today',
            'purpose' => 'required|string|max:1000',
            'terms_conditions' => 'nullable|string|max:2000',
            'interest_rate' => 'nullable|numeric|min:0|max:100',
            'interest_type' => 'nullable|in:simple,compound',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'employee_id.required' => 'Please select an employee.',
            'employee_id.tenant_exists' => 'The selected employee does not exist.',
            'loan_amount.required' => 'Loan amount is required.',
            'loan_amount.numeric' => 'Loan amount must be a valid number.',
            'loan_amount.min' => 'Loan amount must be at least 1.',
            'loan_amount.max' => 'Loan amount cannot exceed 10,000,000.',
            'installment_amount.required' => 'Monthly installment amount is required.',
            'installment_amount.numeric' => 'Installment amount must be a valid number.',
            'installment_amount.min' => 'Installment amount must be at least 1.',
            'installment_amount.max' => 'Installment amount cannot exceed the loan amount.',
            'loan_date.required' => 'Loan date is required.',
            'loan_date.date' => 'Loan date must be a valid date.',
            'loan_date.before_or_equal' => 'Loan date cannot be in the future.',
            'first_deduction_date.required' => 'First deduction date is required.',
            'first_deduction_date.date' => 'First deduction date must be a valid date.',
            'first_deduction_date.after' => 'First deduction date must be after today.',
            'purpose.required' => 'Loan purpose is required.',
            'purpose.max' => 'Loan purpose cannot exceed 1000 characters.',
            'terms_conditions.max' => 'Terms and conditions cannot exceed 2000 characters.',
            'interest_rate.numeric' => 'Interest rate must be a valid number.',
            'interest_rate.min' => 'Interest rate cannot be negative.',
            'interest_rate.max' => 'Interest rate cannot exceed 100%.',
            'interest_type.in' => 'Interest type must be either simple or compound.',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Check if employee has any active loans
            if ($this->employee_id) {
                $activeLoans = \App\Models\EmployeeLoan::where('employee_id', $this->employee_id)
                    ->where('status', 'active')
                    ->count();
                
                if ($activeLoans > 0) {
                    $validator->errors()->add('employee_id', 'This employee already has an active loan.');
                }
            }

            // Validate installment amount doesn't exceed loan amount
            if ($this->loan_amount && $this->installment_amount) {
                if ($this->installment_amount > $this->loan_amount) {
                    $validator->errors()->add('installment_amount', 'Installment amount cannot exceed the loan amount.');
                }
            }

            // Validate first deduction date is at least next month
            if ($this->first_deduction_date) {
                $nextMonth = now()->addMonth()->startOfMonth();
                if (strtotime($this->first_deduction_date) < $nextMonth->timestamp) {
                    $validator->errors()->add('first_deduction_date', 'First deduction date should be at least next month.');
                }
            }
        });
    }
}
