<?php

namespace Database\Factories;

use App\Models\Sale;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class SaleFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Sale::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'product_name' => $this->faker->name(),
            'unit_name' => $this->faker->text(255),
            'price' => $this->faker->randomFloat(2, 0, 9999),
            'discount' => $this->faker->randomFloat(2, 0, 9999),
            'quantity' => $this->faker->randomNumber,
            'product_id' => \App\Models\Product::factory(),
            'unit_id' => \App\Models\Unit::factory(),
            'saleable_type' => $this->faker->randomElement([
                \App\Models\Invoice::class,
                \App\Models\Order::class,
            ]),
            'saleable_id' => function (array $item) {
                return app($item['saleable_type'])->factory();
            },
        ];
    }
}
