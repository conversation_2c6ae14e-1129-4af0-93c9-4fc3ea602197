@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">Purchase Order Report</x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Invoice::class)
            <a href="{{ route('invoices.create') }}" class="btn btn-info btn-sm">
                <!-- <i class="icon ion-md-add"></i> -->
                @lang('crud.common.create') Purchase Order
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="searchbar mt-4 mb-5">
        <form class="d-flex" id="filter">
            <div class="me-3 _100px">
                <input type="hidden" name="from" value="{{ request()->from }}">
                <input type="hidden" name="to" value="{{ request()->to }}">
                <label class="form-label">Dates</label>
                <button id="js-daterangepicker-predefined" type="button" style="width:100%" class="btn btn-white">
                  <i class="bi-calendar-week me-1"></i>
                  <span class="js-daterangepicker-predefined-preview"></span>
                </button>
            </div>


            <div class="me-3 _100px">
                <label class="form-label">Branch</label>
                <div class="tom-select-custom _200px">
                  <select class="js-select form-select branch-filter" name="branch_id" autocomplete="off" data-hs-tom-select-options='{
                    "placeholder": "Select a branch..."
                  }'>
                  <option></option>
                  @foreach( App\Models\Branch::get() as $branch)
                    <option value="{{ $branch->id }}" @if($branch->id == request()->branch_id ) selected @endif>
                      {{ $branch->name }}
                    </option>
                  @endforeach
                  </select>
                </div>
            </div>

            <div class="me-3 _100px">
                <label class="form-label">Category</label>
                <div class="tom-select-custom _200px">
                  <select class="js-select form-select category-filter" name="category_id" autocomplete="off" data-hs-tom-select-options='{
                    "placeholder": "Select a category..."
                  }'>
                  <option value="0"> All Categories</option>
                  @foreach( App\Models\Category::where("applied_to", "products")->get() as $category)
                    <option value="{{ $category->id }}" @if($category->id == request()->category_id ) selected @endif>
                      {{ $category->name }}
                    </option>
                  @endforeach
                  </select>
                </div>
            </div>

            <div class="me-3">
                <label class="form-label transparent">_</label>
                <div class="input-group-append">
                    <button type="submit" class="btn btn-primary">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Table -->
    <div class="card card-table">
        <div class="table-responsive mt-3">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            Order ID
                        </th>                           
                        <th class="text-left">
                            Supplier
                        </th>                 
                        <th class="text-left">
                            @lang('crud.orders.inputs.amount_total')
                        </th>
                        <th class="text-left">
                            @lang('crud.orders.inputs.amount_paid')
                        </th>
                        <th class="text-left">
                            Balance
                        </th>
                        <th class="text-left">
                            @lang('crud.orders.inputs.discount')
                        </th>                            

                        <th class="text-left">
                            @lang('crud.orders.inputs.approved_by')
                        </th>
                        <th class="text-center">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($orders as $order)
                    <tr>
                        <td>{{ $order->order_id ?? '-' }}</td>
                        <td>{{ $order->supplier->name ?? '-' }}</td>
                        <td>{{ _money(  $order->amount_total ) ?? '-' }}</td>
                        <td>{{ _money( $order->amount_paid ) ?? '-' }}</td>
                        <td>{{ _money( $order->amount_total - $order->amount_paid ) ?? '-' }}</td>
                        <td>{{ $order->discount ?? '-' }}</td>
                        <td>
                            @if($order->approved_by)
                            {{ optional($order->approvedBy)->name ?? '' }}
                            @else
                                @if( auth()->user()->can('approve order') )
                                <a href="/approve-order/{{ $order->id }}"> Approve </a>
                                @else
                                <a href="#"> Waiting Approval </a>
                                @endif
                            @endif
                        </td>
                        <td class="text-center" style="width: 134px;">
                            <div role="group" aria-label="Row Actions" class="btn-group">

                                @can('view', $order)
                                <button type="button" class="btn btn-soft-primary m-1 preview-order-btn" data-bs-toggle="modal" class="preview-order-btn" data-id="{{ $order->id }}" data-bs-target=".preview-order-modal">
                                    View
                                </button>
                                @endcan 

                                @if( ! $order->approved_by )
                                @can('update', $order)
                                <a
                                    href="{{ route('orders.edit', $order) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        <!-- <i class="icon ion-md-create"></i> --> edit
                                    </button>
                                </a>
                                @endcan 


                                @can('delete', $order)
                                <form
                                    action="{{ route('orders.destroy', $order) }}"
                                    method="POST"
                                    onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                >
                                    @csrf @method('DELETE')
                                    <button
                                        type="submit"
                                        class="btn btn-light text-danger m-1"
                                    >
                                        <!-- <i class="icon ion-md-trash"></i> --> del
                                    </button>
                                </form>
                                @endcan
                                @endif

                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="9">
                            @lang('crud.common.no_items_found')
                        </td>
                    </tr>
                    @endforelse
                </tbody>

            </table>

        </div>
    </div>



</div>
@endsection





@push('scripts')
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
    dataTableBtn()
    });
</script>

@endpush