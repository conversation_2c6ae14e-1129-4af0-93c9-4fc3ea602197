<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

use App\Traits\ActiveConfigTrait;
use App\Traits\CreatedUpdatedTrait;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Models\Activity;
use App\Traits\BusinessTypeTrait;
use App\Traits\BranchTrait;

class StockItem extends Model implements HasMedia
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use InteractsWithMedia;
    use BusinessTypeTrait;
    use BranchTrait;

    protected $connection = "tenant";


    protected $fillable = [
        'code',
        'order_id',
        'invoice_id',
        'branch_id',
        'buying_price',
        'discount',
        'selling_price',
        'closed_by',
        'description',
        'product_id',
        'warehouse_id',
        'stock_id',
        'created_by',
        'updated_by',
    ];

    protected $searchableFields = ['*'];

    protected $appends = ['name', 'image'];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    public function getNameAttribute(){
        return $this->product->name ?? '';
    }    

    public function getImageAttribute(){
        $file = $this->file();
        return $file ? $file->getUrl() : '/assets/images/profile/cover.jpg';
    }

    public function file($collection = "image"){
        return $this->getMedia($collection)->first();
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function posts()
    {
        return $this->morphMany(Post::class, 'postable');
    }

    public function stock()
    {
        return $this->belongsTo(Stock::class);
    }

    public function location()
    {
        return $this->belongsTo(Location::class);
    }

    public function item()
    {
        return $this->belongsTo(Item::class);
    }


}
