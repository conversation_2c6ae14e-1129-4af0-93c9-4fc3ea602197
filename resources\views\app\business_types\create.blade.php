@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">building-add</x-slot>
        <x-slot name="title">@lang('crud.business_types.create_new_business_type')</x-slot>
        <x-slot name="subtitle">Add a new business type to categorize your business operations</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('business-types.index') }}">@lang('crud.business_types.name')</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('business-types.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> @lang('crud.business_types.back_to_business_types')
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <div class="col-lg-9 mb-3 mb-lg-0">
            <!-- Card -->
            <div class="card mb-3 mb-lg-5">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">@lang('crud.business_types.business_type_details')</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <x-form
                        method="POST"
                        action="{{ route('business-types.store') }}"
                        id="businessTypeForm"
                    >
                        @include('app.business_types.form-inputs')
                    </x-form>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>

        <div class="col-lg-3">
            <!-- Card -->
            <div class="card">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Actions</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <div class="d-flex justify-content-end">
                            <a href="{{ route('business-types.index') }}" class="btn btn-white me-2">Cancel</a>
                            <button type="submit" form="businessTypeForm" class="btn btn-primary">
                                <i class="bi-check-circle me-1"></i> @lang('crud.common.create')
                            </button>
                        </div>
                    </div>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->

            <!-- Card -->
            <div class="card mt-3">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">@lang('crud.business_types.tips.title')</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <ul class="list-unstyled list-py-2 mb-0">
                        <li>
                            <i class="bi-check-circle text-success me-2"></i>
                            @lang('crud.business_types.tips.descriptive_names')
                        </li>
                        <li>
                            <i class="bi-check-circle text-success me-2"></i>
                            @lang('crud.business_types.tips.appropriate_status')
                        </li>
                        <li>
                            <i class="bi-check-circle text-success me-2"></i>
                            @lang('crud.business_types.tips.detailed_descriptions')
                        </li>
                        <li>
                            <i class="bi-check-circle text-success me-2"></i>
                            @lang('crud.business_types.tips.categorize_operations')
                        </li>
                    </ul>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>
    </div>
</div>
@endsection
