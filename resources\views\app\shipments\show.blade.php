@extends('layouts.app')

@section('content')
    <!-- Content -->
    <div class="content container-fluid">
      <!-- Page Header -->
      <div class="page-header d-print-none">
        <div class="row align-items-center">
          <div class="col-sm mb-2 mb-sm-0">
            <nav aria-label="breadcrumb">
              <ol class="breadcrumb breadcrumb-no-gutter">
                <li class="breadcrumb-item">
                  <a class="breadcrumb-link" href="{{ route('shipments.index') }}">Shipments</a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">Shipment #{{ $shipment->shipment_id }}</li>
              </ol>
            </nav>
            <h1 class="page-header-title">
              <i class="bi-truck me-2"></i>
              Shipment #{{ $shipment->shipment_id }}
              <span class="badge {{ $shipment->status_badge_class }} ms-2">{{ $shipment->status_name }}</span>
            </h1>
          </div>
          <!-- End Col -->

          <div class="col-sm-auto">
            <div class="d-flex gap-2">
              <!-- Print Button -->
              <button type="button" class="btn btn-outline-primary preview-shipment-btn"
                      data-bs-toggle="modal"
                      data-id="{{ $shipment->id }}"
                      data-auto="true"
                      data-bs-target=".preview-shipment-modal">
                <i class="bi-printer me-1"></i> Print
              </button>

              <!-- PDF Download Button -->
              <button type="button" class="btn btn-primary" onclick="createPDFfromHTML('shipment')">
                <i class="bi-file-earmark-pdf me-1"></i> Download PDF
              </button>

              <!-- Convert to Invoice or View Invoice -->
              @if($shipment->invoice_id)
                <a href="{{ route('invoices.show', $shipment->invoice_id) }}" class="btn btn-info">
                  <i class="bi-receipt me-1"></i> View Invoice #{{ str_pad($shipment->invoice_id, 5, '0', STR_PAD_LEFT) }}
                </a>
              @else
                <!-- Always show convert button for testing -->
                @can('update', $shipment)
                <form action="{{ route('shipments.convert-to-invoice', $shipment) }}" method="POST" class="d-inline">
                  @csrf
                  <button type="submit" class="btn btn-success" onclick="return confirm('Convert this shipment to invoice? This will create a new invoice with all shipment items.')">
                    <i class="bi-receipt me-1"></i> Convert to Invoice
                  </button>
                </form>
                @else
                <span class="btn btn-outline-secondary disabled">
                  <i class="bi-receipt me-1"></i> No Permission to Convert
                </span>
                @endcan

                <!-- Debug info -->
                <div class="mt-2">
                  <small class="text-muted">
                    Status: {{ $shipment->status_id }} ({{ $shipment->status_name ?? 'Unknown' }}),
                    Sales: {{ $shipment->sales->count() }},
                    Invoice ID: {{ $shipment->invoice_id ?? 'None' }}
                  </small>
                </div>
              @endif

              <!-- Back Button -->
              <a class="btn btn-outline-secondary" href="{{ route('shipments.index') }}">
                <i class="bi-arrow-left me-1"></i> Back
              </a>

              <!-- Edit Button -->
              @if($shipment->status_id == '10' && !$shipment->approved_by)
                @can('update', $shipment)
                <a href="{{ route('shipments.edit', $shipment) }}" class="btn btn-primary">
                  <i class="bi-pencil me-1"></i> Edit
                </a>
                @endcan
              @endif

              <!-- New Shipment Button -->
              @can('create', App\Models\Shipment::class)
              <a class="btn btn-primary" href="{{ route('shipments.create') }}">
                <i class="bi-plus-lg me-1"></i> New Shipment
              </a>
              @endcan
            </div>
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </div>
      <!-- End Page Header -->

      <!-- Invoice-style Layout -->
      <div class="row justify-content-lg-center">
        <div class="col-lg-9">
          <!-- Card -->
          <div class="card card-lg mb-3 mb-lg-5 shipment">
            <div class="card-body">
              <!-- Header -->
              <div class="row justify-content-sm-between">
                <div class="col-sm-6 mb-4">
                  <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                      <i class="bi-truck display-4 text-primary"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h1 class="h2 text-primary mb-0">SHIPMENT</h1>
                      <span class="d-block">#{{ $shipment->shipment_id }}</span>
                    </div>
                  </div>
                </div>
                <!-- End Col -->

                <div class="col-sm-6 mb-4">
                  <div class="text-sm-end">
                    <h2 class="h4 text-uppercase mb-1">{{ config('app.name', 'Company Name') }}</h2>
                    <address class="text-muted">
                      {{ auth()->user()->branch->address ?? 'Company Address' }}<br>
                      Phone: {{ auth()->user()->branch->phone ?? '+****************' }}<br>
                      Email: {{ auth()->user()->branch->email ?? '<EMAIL>' }}
                    </address>
                  </div>
                </div>
                <!-- End Col -->
              </div>
              <!-- End Header -->

              <!-- Shipment Info -->
              <div class="row justify-content-md-between mb-3">
                <div class="col-md-6 mb-3">
                  <h5>Ship To:</h5>
                  <span class="d-block h5 mb-0">{{ $shipment->customer_name ?? 'N/A' }}</span>
                  @if($shipment->customer?->email)
                    <span class="d-block text-muted">{{ $shipment->customer->email }}</span>
                  @endif
                  @if($shipment->customer?->phone)
                    <span class="d-block text-muted">{{ $shipment->customer->phone }}</span>
                  @endif
                  @if($shipment->shipping_address)
                    <address class="text-muted mt-2">
                      {{ $shipment->shipping_address }}
                    </address>
                  @endif
                </div>
                <!-- End Col -->

                <div class="col-md-6 mb-3">
                  <dl class="row">
                    <dt class="col-sm-6">Shipment Date:</dt>
                    <dd class="col-sm-6">{{ $shipment->created_at->format('M d, Y') }}</dd>

                    @if($shipment->tracking_number)
                    <dt class="col-sm-6">Tracking Number:</dt>
                    <dd class="col-sm-6">
                      <span class="badge bg-soft-primary text-primary">{{ $shipment->tracking_number }}</span>
                    </dd>
                    @endif

                    @if($shipment->carrier)
                    <dt class="col-sm-6">Carrier:</dt>
                    <dd class="col-sm-6">{{ $shipment->carrier }}</dd>
                    @endif

                    @if($shipment->shipping_method)
                    <dt class="col-sm-6">Shipping Method:</dt>
                    <dd class="col-sm-6">{{ ucfirst($shipment->shipping_method) }}</dd>
                    @endif

                    @if($shipment->estimated_delivery)
                    <dt class="col-sm-6">Est. Delivery:</dt>
                    <dd class="col-sm-6">{{ $shipment->estimated_delivery->format('M d, Y') }}</dd>
                    @endif

                    @if($shipment->reference_no)
                    <dt class="col-sm-6">Reference:</dt>
                    <dd class="col-sm-6">{{ $shipment->reference_no }}</dd>
                    @endif
                  </dl>
                </div>
                <!-- End Col -->
              </div>
              <!-- End Shipment Info -->

              <!-- Package Details -->
              @if($shipment->weight || $shipment->dimensions || $shipment->insurance_value)
              <div class="row mb-4">
                <div class="col-12">
                  <div class="bg-soft-light p-3 rounded">
                    <h6 class="mb-2">Package Details</h6>
                    <div class="row">
                      @if($shipment->weight)
                      <div class="col-md-4">
                        <small class="text-muted d-block">Weight</small>
                        <span>{{ $shipment->weight }} kg</span>
                      </div>
                      @endif
                      @if($shipment->dimensions)
                      <div class="col-md-4">
                        <small class="text-muted d-block">Dimensions</small>
                        <span>{{ $shipment->dimensions }}</span>
                      </div>
                      @endif
                      @if($shipment->insurance_value)
                      <div class="col-md-4">
                        <small class="text-muted d-block">Insurance Value</small>
                        <span>{{ _money($shipment->insurance_value) }}</span>
                      </div>
                      @endif
                    </div>
                  </div>
                </div>
              </div>
              @endif

              <!-- Special Instructions -->
              @if($shipment->special_instructions)
              <div class="row mb-4">
                <div class="col-12">
                  <div class="alert alert-warning">
                    <h6 class="alert-heading">Special Instructions</h6>
                    <p class="mb-0">{{ $shipment->special_instructions }}</p>
                  </div>
                </div>
              </div>
              @endif

              <!-- Items Table -->
              <div class="table-responsive">
                <table class="table table-borderless table-nowrap table-align-middle">
                  <thead class="thead-light">
                    <tr>
                      <th scope="col" style="width: 1%;">#</th>
                      <th scope="col">Item</th>
                      <th scope="col">Unit</th>
                      <th scope="col" class="text-end">Qty</th>
                      <th scope="col" class="text-end">Rate</th>
                      <th scope="col" class="text-end">Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    @forelse($shipment->sales as $index => $sale)
                    <tr>
                      <td>{{ $index + 1 }}</td>
                      <td>
                        <div>
                          <h5 class="mb-0">{{ $sale->product_name }}</h5>
                          @if($sale->product?->description)
                            <small class="text-muted">{{ $sale->product->description }}</small>
                          @endif
                        </div>
                      </td>
                      <td>{{ $sale->unit_name ?? '-' }}</td>
                      <td class="text-end">{{ number_format($sale->quantity, 2) }}</td>
                      <td class="text-end">{{ _money($sale->selling_price) }}</td>
                      <td class="text-end">{{ _money($sale->selling_price * $sale->quantity) }}</td>
                    </tr>
                    @empty
                    <tr>
                      <td colspan="6" class="text-center py-4">
                        <div class="text-center">
                          <i class="bi-box display-4 text-muted"></i>
                          <p class="mt-2 text-muted">No items in this shipment</p>
                        </div>
                      </td>
                    </tr>
                    @endforelse
                  </tbody>
                </table>
              </div>
              <!-- End Items Table -->

              <hr class="my-5">

              <!-- Totals -->
              <div class="row justify-content-md-end mb-3">
                <div class="col-md-8 col-lg-7">
                  <dl class="row text-sm-end">
                    <dt class="col-sm-6">Subtotal:</dt>
                    <dd class="col-sm-6">{{ _money($shipment->sub_total) }}</dd>

                    @if($shipment->discount > 0)
                    <dt class="col-sm-6">Discount:</dt>
                    <dd class="col-sm-6 text-danger">-{{ _money($shipment->discount) }}</dd>
                    @endif

                    @if($shipment->vat > 0)
                    <dt class="col-sm-6">VAT:</dt>
                    <dd class="col-sm-6">{{ _money($shipment->vat) }}</dd>
                    @endif

                    <dt class="col-sm-6 border-top pt-2">Total:</dt>
                    <dd class="col-sm-6 border-top pt-2">
                      <span class="h4">{{ _money($shipment->amount_total) }}</span>
                    </dd>

                    @if($shipment->amount_paid > 0)
                    <dt class="col-sm-6">Amount Paid:</dt>
                    <dd class="col-sm-6 text-success">{{ _money($shipment->amount_paid) }}</dd>

                    <dt class="col-sm-6">Due Balance:</dt>
                    <dd class="col-sm-6 {{ ($shipment->amount_total - $shipment->amount_paid) > 0 ? 'text-danger' : 'text-success' }}">
                      {{ _money($shipment->amount_total - $shipment->amount_paid) }}
                    </dd>
                    @endif
                  </dl>
                  <!-- End Row -->
                </div>
              </div>
              <!-- End Totals -->

              <!-- Footer -->
              <div class="row">
                <div class="col-sm-7 mb-3">
                  @if($shipment->description)
                  <div>
                    <h6>Notes:</h6>
                    <p>{{ $shipment->description }}</p>
                  </div>
                  @endif
                </div>

                <div class="col-sm-5 mb-3">
                  <div class="text-sm-end">
                    <h6>Prepared by:</h6>
                    <p>
                      {{ $shipment->createdBy->name ?? 'System' }}<br>
                      <span class="text-muted">{{ $shipment->created_at->format('M d, Y h:i A') }}</span>
                    </p>
                  </div>
                </div>
              </div>
              <!-- End Footer -->

            </div>
          </div>
          <!-- End Card -->

          <!-- Timeline Card -->
          <div class="card mt-4">
            <div class="card-header">
              <h4 class="card-header-title">
                <i class="bi-clock-history me-2"></i>
                Shipment Timeline
              </h4>
            </div>
            <div class="card-body">
              <ul class="step step-icon-sm step-inline step-item-between-checkmark">
                <li class="step-item">
                  <div class="step-content-wrapper">
                    <span class="step-icon step-icon-soft-primary">1</span>
                    <div class="step-content">
                      <h6 class="mb-1">Created</h6>
                      <p class="fs-5 mb-1">{{ $shipment->created_at->format('M d, Y h:i A') }}</p>
                      <span class="text-muted fs-5">by {{ $shipment->createdBy->name ?? 'System' }}</span>
                    </div>
                  </div>
                </li>

                @if($shipment->estimated_delivery)
                <li class="step-item">
                  <div class="step-content-wrapper">
                    <span class="step-icon step-icon-soft-info">2</span>
                    <div class="step-content">
                      <h6 class="mb-1">Estimated Delivery</h6>
                      <p class="fs-5 mb-0">{{ $shipment->estimated_delivery->format('M d, Y') }}</p>
                    </div>
                  </div>
                </li>
                @endif

                @if($shipment->actual_delivery)
                <li class="step-item">
                  <div class="step-content-wrapper">
                    <span class="step-icon step-icon-soft-success">3</span>
                    <div class="step-content">
                      <h6 class="mb-1">Delivered</h6>
                      <p class="fs-5 mb-0">{{ $shipment->actual_delivery->format('M d, Y') }}</p>
                    </div>
                  </div>
                </li>
                @endif
              </ul>
            </div>
          </div>
          <!-- End Timeline Card -->
        </div>
      </div>

    </div>
@endsection
