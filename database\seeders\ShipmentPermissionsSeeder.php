<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class ShipmentPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create shipment permissions
        $permissions = [
            'list shipments',
            'view shipments',
            'create shipments',
            'update shipments',
            'delete shipments',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permissions to roles
        $roles = [
            'super-admin' => $permissions,
            'admin' => $permissions,
            'manager' => $permissions,
            'sales-manager' => $permissions,
            'warehouse-manager' => $permissions,
            'shipping-clerk' => [
                'list shipments',
                'view shipments',
                'create shipments',
                'update shipments',
            ],
            'sales-rep' => [
                'list shipments',
                'view shipments',
                'create shipments',
            ],
        ];

        foreach ($roles as $roleName => $rolePermissions) {
            $role = Role::where('name', $roleName)->first();
            if ($role) {
                $role->givePermissionTo($rolePermissions);
            }
        }

        $this->command->info('Shipment permissions created and assigned successfully!');
    }
}
