@extends('layouts.app')

@push('styles')
<style>
    .approval-card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.75rem;
        transition: all 0.2s ease;
        border-left: 4px solid #ffc107;
    }
    
    .approval-card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }
    
    .approval-card.transfer {
        border-left-color: #0d6efd;
    }
    
    .approval-card.stock {
        border-left-color: #198754;
    }
    
    .approval-card.order {
        border-left-color: #dc3545;
    }
    
    .approval-type-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
        border-radius: 0.25rem;
        text-transform: uppercase;
        font-weight: 600;
    }
    
    .pending-indicator {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
    
    .filter-tabs {
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 1.5rem;
    }
    
    .filter-tab {
        padding: 0.75rem 1rem;
        border: none;
        background: none;
        color: #6c757d;
        text-decoration: none;
        border-bottom: 2px solid transparent;
        transition: all 0.2s ease;
    }
    
    .filter-tab:hover {
        color: #495057;
        background-color: #f8f9fa;
    }
    
    .filter-tab.active {
        color: #0d6efd;
        border-bottom-color: #0d6efd;
        background-color: #f8f9fa;
    }
</style>
@endpush

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">clock-history</x-slot>
        <x-slot name="title">Pending Approvals</x-slot>
        <x-slot name="subtitle">Review and approve pending items across the system</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Pending Approvals</li>
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group">
                <button type="button" class="btn btn-outline-info btn-sm" onclick="refreshCounts()">
                    <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                </button>
            </div>
        </x-slot>
    </x-page-header>

    <!-- Filter Tabs -->
    <div class="filter-tabs d-flex">
        <a href="{{ route('approvals.index', ['filter' => 'all'] + request()->only(['search'])) }}" 
           class="filter-tab {{ $filter === 'all' ? 'active' : '' }}">
            <i class="bi bi-list-ul me-1"></i>All Approvals
            <span class="badge bg-secondary ms-1" id="total-count">0</span>
        </a>
        <a href="{{ route('approvals.index', ['filter' => 'transfers'] + request()->only(['search'])) }}" 
           class="filter-tab {{ $filter === 'transfers' ? 'active' : '' }}">
            <i class="bi bi-arrow-left-right me-1"></i>Transfers
            <span class="badge bg-primary ms-1" id="transfers-count">0</span>
        </a>
        <a href="{{ route('approvals.index', ['filter' => 'stock'] + request()->only(['search'])) }}" 
           class="filter-tab {{ $filter === 'stock' ? 'active' : '' }}">
            <i class="bi bi-box me-1"></i>Stock
            <span class="badge bg-success ms-1" id="stock-count">0</span>
        </a>
        <a href="{{ route('approvals.index', ['filter' => 'orders'] + request()->only(['search'])) }}" 
           class="filter-tab {{ $filter === 'orders' ? 'active' : '' }}">
            <i class="bi bi-receipt me-1"></i>Orders
            <span class="badge bg-danger ms-1" id="orders-count">0</span>
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('approvals.index') }}">
                <input type="hidden" name="filter" value="{{ $filter }}">
                <div class="row g-3">
                    <div class="col-md-8">
                        <label for="search" class="form-label">Search Approvals</label>
                        <input type="text" name="search" id="search" class="form-control" 
                               value="{{ request('search') }}" placeholder="Search by title, description, or details...">
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>Search
                        </button>
                        <a href="{{ route('approvals.index', ['filter' => $filter]) }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    @if($approvals->count() > 0)
        <!-- Approvals Grid -->
        <div class="row">
            @foreach($approvals as $approval)
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card approval-card {{ $approval['type'] }}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <span class="approval-type-badge 
                                    @if($approval['type'] === 'transfer') bg-primary text-white
                                    @elseif($approval['type'] === 'stock') bg-success text-white
                                    @elseif($approval['type'] === 'order') bg-danger text-white
                                    @else bg-secondary text-white @endif">
                                    {{ ucfirst($approval['type']) }}
                                </span>
                                <i class="bi bi-clock-history pending-indicator ms-2 text-warning"></i>
                            </div>
                            <small class="text-muted">{{ $approval['created_at']->format('M d, Y') }}</small>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title">{{ $approval['title'] }}</h6>
                            <p class="card-text text-muted small">{{ $approval['description'] }}</p>
                            
                            <div class="mb-3">
                                @if($approval['type'] === 'transfer')
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="border-end">
                                                <h6 class="text-primary mb-1">{{ number_format($approval['amount']) }}</h6>
                                                <small class="text-muted">Quantity</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h6 class="text-success mb-1">{{ number_format($approval['total_value'], 2) }}</h6>
                                            <small class="text-muted">Value</small>
                                        </div>
                                    </div>
                                @elseif($approval['type'] === 'order')
                                    <div class="text-center">
                                        <h5 class="text-success mb-1">{{ $approval['currency'] }}{{ number_format($approval['amount'], 2) }}</h5>
                                        <small class="text-muted">Order Total</small>
                                    </div>
                                @endif
                            </div>

                            <div class="mb-3">
                                @if($approval['from_location'])
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span class="text-muted">From:</span>
                                        <span class="badge bg-secondary">{{ $approval['from_location'] }}</span>
                                    </div>
                                @endif
                                @if($approval['to_location'])
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span class="text-muted">To:</span>
                                        <span class="badge bg-primary">{{ $approval['to_location'] }}</span>
                                    </div>
                                @endif
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">By:</span>
                                    <span class="badge bg-info">{{ $approval['created_by'] }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex gap-2">
                                <button type="button" 
                                        class="btn btn-success btn-sm flex-fill"
                                        onclick="showApprovalModal({{ $approval['id'] }}, '{{ $approval['title'] }}', '{{ $approval['type'] }}', 'approve')">
                                    <i class="bi bi-check-circle me-1"></i>Approve
                                </button>
                                <button type="button" 
                                        class="btn btn-danger btn-sm flex-fill"
                                        onclick="showApprovalModal({{ $approval['id'] }}, '{{ $approval['title'] }}', '{{ $approval['type'] }}', 'reject')">
                                    <i class="bi bi-x-circle me-1"></i>Reject
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($pagination['last_page'] > 1)
            <div class="d-flex justify-content-center">
                <nav aria-label="Approvals pagination">
                    <ul class="pagination">
                        @if($pagination['current_page'] > 1)
                            <li class="page-item">
                                <a class="page-link" href="{{ request()->fullUrlWithQuery(['page' => $pagination['current_page'] - 1]) }}">Previous</a>
                            </li>
                        @endif
                        
                        @for($i = 1; $i <= $pagination['last_page']; $i++)
                            <li class="page-item {{ $i == $pagination['current_page'] ? 'active' : '' }}">
                                <a class="page-link" href="{{ request()->fullUrlWithQuery(['page' => $i]) }}">{{ $i }}</a>
                            </li>
                        @endfor
                        
                        @if($pagination['current_page'] < $pagination['last_page'])
                            <li class="page-item">
                                <a class="page-link" href="{{ request()->fullUrlWithQuery(['page' => $pagination['current_page'] + 1]) }}">Next</a>
                            </li>
                        @endif
                    </ul>
                </nav>
            </div>
        @endif
    @else
        <div class="text-center py-5">
            <i class="bi bi-check-circle text-success" style="font-size: 4rem;"></i>
            <h4 class="mt-3">No Pending Approvals</h4>
            <p class="text-muted">
                @if($filter === 'all')
                    All items have been processed.
                @else
                    No pending {{ $filter }} approvals found.
                @endif
            </p>
            <a href="{{ route('dashboard') }}" class="btn btn-primary">
                <i class="bi bi-house me-1"></i>Back to Dashboard
            </a>
        </div>
    @endif
</div>

<!-- Generic Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1" aria-labelledby="approvalModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approvalModalLabel">
                    <span id="modalAction">Approve</span> <span id="modalType">Item</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    You are about to <span id="modalActionText">approve</span> the <span id="modalTypeText">item</span>: 
                    <strong id="modalItemTitle"></strong>
                </div>
                
                <form id="approvalForm">
                    <input type="hidden" id="itemId" name="item_id">
                    <input type="hidden" id="itemType" name="item_type">
                    <input type="hidden" id="actionType" name="action_type">
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label" id="notesLabel">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="Optional notes about this decision..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn" id="confirmButton" onclick="submitApproval()">
                    <span id="confirmButtonText">Approve Item</span>
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let currentItemId = null;
let currentItemType = null;
let currentAction = null;

// Load approval counts on page load
document.addEventListener('DOMContentLoaded', function() {
    refreshCounts();
});

function refreshCounts() {
    fetch('/approvals/counts')
        .then(response => response.json())
        .then(data => {
            document.getElementById('total-count').textContent = data.total;
            document.getElementById('transfers-count').textContent = data.transfers;
            document.getElementById('stock-count').textContent = data.stock;
            document.getElementById('orders-count').textContent = data.orders;
        })
        .catch(error => console.error('Error fetching counts:', error));
}

function showApprovalModal(itemId, itemTitle, itemType, action) {
    currentItemId = itemId;
    currentItemType = itemType;
    currentAction = action;

    document.getElementById('itemId').value = itemId;
    document.getElementById('itemType').value = itemType;
    document.getElementById('actionType').value = action;
    document.getElementById('modalItemTitle').textContent = itemTitle;

    // Update modal content based on action and type
    if (action === 'approve') {
        document.getElementById('modalAction').textContent = 'Approve';
        document.getElementById('modalActionText').textContent = 'approve';
        document.getElementById('notesLabel').textContent = 'Approval Notes';
        document.getElementById('notes').placeholder = 'Optional notes about this approval...';
        document.getElementById('confirmButton').className = 'btn btn-success';
        document.getElementById('confirmButtonText').textContent = 'Approve ' + ucfirst(itemType);
        document.getElementById('notes').name = 'approval_notes';
        document.getElementById('notes').required = false;
    } else {
        document.getElementById('modalAction').textContent = 'Reject';
        document.getElementById('modalActionText').textContent = 'reject';
        document.getElementById('notesLabel').textContent = 'Rejection Reason *';
        document.getElementById('notes').placeholder = 'Please provide a reason for rejection...';
        document.getElementById('confirmButton').className = 'btn btn-danger';
        document.getElementById('confirmButtonText').textContent = 'Reject ' + ucfirst(itemType);
        document.getElementById('notes').name = 'rejection_reason';
        document.getElementById('notes').required = true;
    }

    // Update type-specific text
    document.getElementById('modalType').textContent = ucfirst(itemType);
    document.getElementById('modalTypeText').textContent = itemType;

    // Clear previous notes
    document.getElementById('notes').value = '';

    // Show modal
    new bootstrap.Modal(document.getElementById('approvalModal')).show();
}

async function submitApproval() {
    const form = document.getElementById('approvalForm');
    const notes = document.getElementById('notes').value;

    // Validate rejection reason
    if (currentAction === 'reject' && !notes.trim()) {
        alert('Please provide a reason for rejection.');
        return;
    }

    const confirmButton = document.getElementById('confirmButton');
    const originalText = confirmButton.innerHTML;
    confirmButton.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Processing...';
    confirmButton.disabled = true;

    try {
        let url = '';
        let data = {
            _token: '{{ csrf_token() }}'
        };

        // Determine the correct endpoint based on item type
        if (currentItemType === 'transfer') {
            url = currentAction === 'approve' ?
                '{{ route("stocks.approve-transfer") }}' :
                '{{ route("stocks.reject-transfer") }}';
            data.transfer_id = currentItemId;
        } else if (currentItemType === 'order') {
            url = currentAction === 'approve' ?
                `/approve-order/${currentItemId}` :
                `/reject-order/${currentItemId}`;
            // No need to add order_id as it's in the URL
        } else if (currentItemType === 'stock') {
            url = currentAction === 'approve' ?
                '/stock/approve' :
                '/stock/reject';
            data.stock_id = currentItemId;
        }

        // Add notes/reason
        if (currentAction === 'approve') {
            data.approval_notes = notes;
        } else {
            data.rejection_reason = notes;
        }

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            // Hide modal
            bootstrap.Modal.getInstance(document.getElementById('approvalModal')).hide();

            // Show success message
            alert(result.message);

            // Reload page to update the list
            window.location.reload();
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('An error occurred while processing the request.');
    } finally {
        confirmButton.innerHTML = originalText;
        confirmButton.disabled = false;
    }
}

function ucfirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}
</script>
@endpush
