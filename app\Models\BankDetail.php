<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

use App\Traits\CreatedUpdatedTrait;

class BankDetail extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;

    protected $connection = "tenant";


    protected $fillable = [
        'bank_name',
        'account_number',
        'account_type',
        'account_name',
        'payroll_id',
        'active',
        'branch',
        'created_by',
        'updated_by',
        'employee_id',
    ];

    protected $searchableFields = ['*'];

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }






}
