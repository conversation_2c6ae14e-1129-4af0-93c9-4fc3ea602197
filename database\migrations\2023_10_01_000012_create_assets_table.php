<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('assets', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name');
            $table->string('asset_number')->unique();
            $table->text('description')->nullable();
            $table->unsignedBigInteger('asset_category_id');
            $table->date('acquisition_date');
            $table->decimal('acquisition_cost', 20, 2);
            $table->decimal('salvage_value', 20, 2)->default(0);
            $table->decimal('depreciable_cost', 20, 2)->storedAs('acquisition_cost - salvage_value');
            $table->decimal('accumulated_depreciation', 20, 2)->default(0);
            $table->decimal('current_value', 20, 2)->storedAs('acquisition_cost - accumulated_depreciation');
            $table->integer('useful_life_years');
            $table->decimal('depreciation_rate', 8, 4);
            $table->string('depreciation_method');
            $table->date('last_depreciation_date')->nullable();
            $table->date('next_depreciation_date')->nullable();
            $table->string('status')->default('active'); // active, disposed, etc.
            $table->date('disposal_date')->nullable();
            $table->decimal('disposal_amount', 20, 2)->nullable();
            $table->text('disposal_notes')->nullable();
            $table->unsignedBigInteger('location_id')->nullable(); // branch or department
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unsignedBigInteger('business_type_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->foreign('asset_category_id')
                ->references('id')
                ->on('asset_categories')
                ->onDelete('cascade');
                
            $table->foreign('location_id')
                ->references('id')
                ->on('branches')
                ->onDelete('set null');
                
            $table->foreign('created_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('updated_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('business_type_id')
                ->references('id')
                ->on('business_types')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('assets');
    }
};
