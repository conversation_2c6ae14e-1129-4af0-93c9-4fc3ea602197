<?php

namespace App\Http\Controllers;

use App\Models\Status;
use App\Models\Leave;
use App\Models\LeaveRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

use Facades\App\Libraries\InvoiceHandler;

class LeaveRequestController extends Controller
{


    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', LeaveRequest::class);

        $search = $request->get('search', '');

        if(!auth()->user()->employee) {
            auth()->user()->employee()->create( auth()->user()->toArray());
        }

        $employee = auth()->user()->employee;
        $leaveRequests = [];
        if($employee) {
            $leaveRequests = $employee->leaveRequests()->search($search)
            ->latest()
            ->paginate()
            ->withQueryString();
        }

        return view('app.leave-requests.index', compact('leaveRequests', 'search'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', LeaveRequest::class);

        $leaves = Leave::pluck('name', 'id');

        return view('app.leave-requests.create', compact('leaves'));
    }

    /**
     * @param \App\Http\Requests\leavestoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // $this->authorize('create', LeaveRequest::class);

        $request->validate([
            "leave_id" => ['required'], 
        ]);   

        $validated = $request->all();

        if(!auth()->user()->employee) {
            auth()->user()->employee()->create( auth()->user()->toArray());
        }

        $validated['phone'] = auth()->user()->phone;
        $validated['employee_id'] = auth()->user()->employee->id?? null;

        $leaveRequest = LeaveRequest::create($validated);

        if(request()->attachment) {
            $leaveRequest->clearMediaCollection('attachment');
            // foreach (request()->attachment as $file) {
            if(request()->attachment) $leaveRequest->addMedia(request()->attachment)->toMediaCollection("attachment");
            // }
        }

        return redirect()
            ->route('leave-requests.edit', $leaveRequest)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\LeaveRequest $leaveRequest
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, LeaveRequest $leaveRequest)
    {
        $this->authorize('view', $leaveRequest);

        return view('app.leave-requests.show', compact('leaveRequest'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\LeaveRequest $leaveRequest
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, LeaveRequest $leaveRequest)
    {
        $this->authorize('update', $leaveRequest);

        $leaves = Leave::pluck('name', 'id');

        return view('app.leave-requests.edit', compact('leaveRequest', 'leaves'));
    }

    /**
     * @param \App\Http\Requests\CustomerUpdateRequest $request
     * @param \App\Models\LeaveRequest $leaveRequest
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, LeaveRequest $leaveRequest)
    {
        $this->authorize('update', $leaveRequest);

        $validated = $request->all();

        $leaveRequest->update($validated);
        if(request()->image) {
            $leaveRequest->clearMediaCollection('attachment');
            // foreach (request()->attachment as $file) {
            if(request()->attachment) $leaveRequest->addMedia(request()->attachment)->toMediaCollection("attachment");
            // }
        }

        return redirect()
            ->route('leave-requests.edit', $leaveRequest)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\LeaveRequest $leaveRequest
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, LeaveRequest $leaveRequest)
    {
        $this->authorize('delete', $leaveRequest);

        if ($leaveRequest->name) {
            Storage::delete($leaveRequest->name);
        }

        $leaveRequest->delete();

        return redirect()
            ->route('leave-requests.index')
            ->withSuccess(__('crud.common.removed'));
    }




}
