<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Employee;
use App\Models\WorkHoursConfiguration;
use App\Models\EmployeeTimeRecord;
use App\Models\EmployeeTask;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class TimeTrackingTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $employee;
    protected $workConfig;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a work hours configuration
        $this->workConfig = WorkHoursConfiguration::create([
            'name' => 'Test Work Hours',
            'start_time' => '09:00:00',
            'end_time' => '17:00:00',
            'standard_hours_per_day' => 8.00,
            'overtime_rate' => 1.50,
            'is_active' => true,
            'description' => 'Test configuration',
            'created_by' => 1,
        ]);

        // Create a user and employee
        $this->user = User::factory()->create();
        $this->employee = Employee::create([
            'name' => 'Test Employee',
            'email' => '<EMAIL>',
            'basic_pay' => 5000.00,
            'user_id' => $this->user->id,
            'created_by' => 1,
        ]);
    }

    public function test_work_hours_configuration_creation()
    {
        $this->assertDatabaseHas('work_hours_configurations', [
            'name' => 'Test Work Hours',
            'is_active' => true,
        ]);

        $config = WorkHoursConfiguration::getActive();
        $this->assertNotNull($config);
        $this->assertEquals('Test Work Hours', $config->name);
    }

    public function test_employee_time_record_creation()
    {
        $record = EmployeeTimeRecord::create([
            'employee_id' => $this->employee->id,
            'work_date' => today(),
            'check_in_time' => now()->setTime(9, 0),
            'check_out_time' => now()->setTime(18, 0), // 9 hours total
            'created_by' => $this->user->id,
        ]);

        $record->calculateHours();

        $this->assertEquals(9.0, $record->total_hours);
        $this->assertEquals(8.0, $record->regular_hours);
        $this->assertEquals(1.0, $record->overtime_hours);
    }

    public function test_overtime_calculation()
    {
        // Test standard hours (no overtime)
        $this->assertEquals(0, $this->workConfig->calculateOvertimeHours(8.0));
        $this->assertEquals(8.0, $this->workConfig->calculateRegularHours(8.0));

        // Test overtime
        $this->assertEquals(2.0, $this->workConfig->calculateOvertimeHours(10.0));
        $this->assertEquals(8.0, $this->workConfig->calculateRegularHours(10.0));

        // Test under hours
        $this->assertEquals(0, $this->workConfig->calculateOvertimeHours(6.0));
        $this->assertEquals(6.0, $this->workConfig->calculateRegularHours(6.0));
    }

    public function test_employee_task_creation()
    {
        $timeRecord = EmployeeTimeRecord::create([
            'employee_id' => $this->employee->id,
            'work_date' => today(),
            'created_by' => $this->user->id,
        ]);

        $task = EmployeeTask::create([
            'employee_time_record_id' => $timeRecord->id,
            'task_description' => 'Test Task',
            'time_spent' => 2.5,
            'start_time' => '09:00:00',
            'end_time' => '11:30:00',
            'notes' => 'Test notes',
            'created_by' => $this->user->id,
        ]);

        $this->assertDatabaseHas('employee_tasks', [
            'task_description' => 'Test Task',
            'time_spent' => 2.5,
        ]);

        $this->assertEquals(2.5, $task->time_spent);
    }

    public function test_employee_monthly_overtime_calculation()
    {
        $year = now()->year;
        $month = now()->month;

        // Create multiple time records for the month
        for ($day = 1; $day <= 5; $day++) {
            $date = Carbon::create($year, $month, $day);
            $record = EmployeeTimeRecord::create([
                'employee_id' => $this->employee->id,
                'work_date' => $date,
                'check_in_time' => $date->copy()->setTime(9, 0),
                'check_out_time' => $date->copy()->setTime(19, 0), // 10 hours = 2 hours overtime each day
                'created_by' => $this->user->id,
            ]);
            $record->calculateHours();
        }

        $totalOvertime = $this->employee->getOvertimeHoursForMonth($year, $month);
        $totalRegular = $this->employee->getRegularHoursForMonth($year, $month);

        $this->assertEquals(10.0, $totalOvertime); // 5 days × 2 hours overtime
        $this->assertEquals(40.0, $totalRegular);  // 5 days × 8 hours regular
    }

    public function test_check_in_check_out_workflow()
    {
        $record = EmployeeTimeRecord::getOrCreateTodayRecord($this->employee->id);
        
        // Test check-in
        $checkInTime = now()->setTime(9, 15);
        $record->checkIn($checkInTime);
        
        $this->assertEquals('checked_in', $record->status);
        $this->assertEquals($checkInTime->format('Y-m-d H:i:s'), $record->check_in_time->format('Y-m-d H:i:s'));

        // Test check-out
        $checkOutTime = now()->setTime(17, 30);
        $record->checkOut($checkOutTime);
        
        $this->assertEquals('checked_out', $record->status);
        $this->assertEquals($checkOutTime->format('Y-m-d H:i:s'), $record->check_out_time->format('Y-m-d H:i:s'));
        
        // Verify hours calculation
        $expectedHours = $checkOutTime->diffInMinutes($checkInTime) / 60;
        $this->assertEquals($expectedHours, $record->total_hours);
    }

    public function test_work_hours_validation()
    {
        $morningTime = '08:30:00'; // Before work hours
        $workTime = '10:00:00';    // During work hours
        $eveningTime = '18:30:00'; // After work hours

        $this->assertFalse($this->workConfig->isWithinWorkHours($morningTime));
        $this->assertTrue($this->workConfig->isWithinWorkHours($workTime));
        $this->assertFalse($this->workConfig->isWithinWorkHours($eveningTime));
    }
}
