<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\UnitController;
use App\Http\Controllers\SaleController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\StockController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\StatusController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\SupplierController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\WarehouseController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ReceivableController;
use App\Http\Controllers\PayableController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\BusinessTypeController;

use App\Http\Controllers\QuotationController;
use App\Http\Controllers\ShipmentController;
use App\Http\Controllers\BackupController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\AccountController;
use App\Http\Controllers\LeaveController;
use App\Http\Controllers\LeaveRequestController;

// Accounting Module Controllers
use App\Http\Controllers\AccountTypeController;
use App\Http\Controllers\AccountCategoryController;
use App\Http\Controllers\FiscalYearController;
use App\Http\Controllers\FiscalPeriodController;
use App\Http\Controllers\JournalEntryController;
use App\Http\Controllers\CurrencyController;
use App\Http\Controllers\ExchangeRateController;
use App\Http\Controllers\FinancialReportController;
use App\Http\Controllers\AssetCategoryController;
use App\Http\Controllers\AssetController;
use App\Http\Controllers\AssetDepreciationController;
use App\Http\Controllers\BankAccountController;
use App\Http\Controllers\BankReconciliationController;
use App\Http\Controllers\TaxTypeController;
use App\Http\Controllers\TaxTransactionController;
use App\Http\Controllers\BudgetController;
use App\Http\Controllers\BomController;
use App\Http\Controllers\ProductionOrderController;
use App\Http\Controllers\PayrollController;
use App\Http\Controllers\DeductionContributionController;
use App\Http\Controllers\EmployeeDeductionContributionController;
use App\Http\Controllers\PayrollReportController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\EmployeeLoanController;
use App\Http\Controllers\TaxBracketController;

use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\DebtController;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Auth::routes();

// Invitation routes (outside tenant middleware)
Route::get('/invitation/accept/{token}', [App\Http\Controllers\InvitationController::class, 'show'])->name('invitation.show');
Route::post('/invitation/accept/{token}', [App\Http\Controllers\InvitationController::class, 'accept'])->name('invitation.accept');

Route::get('/fix', function(){

    // // Create admin role and assign all permissions
    // $allPermissions = Permission::all();
    // $adminRole = Role::where('name', 'super-admin')->first();
    // $adminRole->givePermissionTo($allPermissions);

        Permission::create(['name' => 'list quotations']);
        Permission::create(['name' => 'view quotations']);
        Permission::create(['name' => 'create quotations']);
        Permission::create(['name' => 'update quotations']);
        Permission::create(['name' => 'delete quotations']);

        // Permission::create(['name' => 'list leave']);
        // Permission::create(['name' => 'view leave']);
        // Permission::create(['name' => 'create leave']);
        // Permission::create(['name' => 'update leave']);
        // Permission::create(['name' => 'delete leave']);
});

Route::get('/welcome', function(){
    return view('welcome');
});


Route::post("/reset-database", [BackupController::class, 'resetDatabase'])->name("reset-database");
Route::post("/restore-database", [BackupController::class, 'restoreDatabase'])->name("restore-database");

Route::post("/login", [LoginController::class, 'login'])->name("login");

Route::prefix('/')->middleware(['auth'])->group(function () {

    Route::resource('leaves', LeaveController::class);
    Route::get('/my-leaves', [LeaveController::class, 'myLeaves']);
    Route::resource('leave-requests', LeaveRequestController::class);

    Route::resource('settings', SettingController::class);

    Route::resource('business-types', BusinessTypeController::class);
    Route::get('/set-business-type/{businessType}', [BusinessTypeController::class, 'setBusinessType']);

    Route::prefix('/')->middleware('businessType')->group(function () {

        Route::get('/', [HomeController::class, 'index'])->name('home');
        Route::get('/home', [HomeController::class, 'index'])->name('dashboard');
        Route::get('/close-business-hour', [HomeController::class, 'closeBusinessHour']);

        Route::get('/sale-report',      [HomeController::class, 'saleReport']);
        Route::get('/stock-report',     [HomeController::class, 'stockReport']);
        Route::get('/order-report',     [HomeController::class, 'OrderReport']);
        Route::get('/expense-report',   [HomeController::class, 'expenseReport']);
        Route::get('/credit-report',    [HomeController::class, 'creditReport']);

        Route::resource('debts',    DebtController::class);
        Route::get('/debtors',    [DebtController::class, 'debtor']);
        Route::get('/expenses',   [DebtController::class, 'expense']);
        Route::get('/ajax-debtor/{debt}',   [DebtController::class, 'ajaxDebtor']);
        Route::get('/edit-expense/{debt}',   [DebtController::class, 'editExpense']);
        Route::post('/save-expense',   [DebtController::class, 'saveExpense']);
        Route::get('/customer-debtors',   [DebtController::class, 'customerDebtors']);
        Route::get('/supplier-debtors',   [DebtController::class, 'supplierDebtors']);
        Route::post('/settle-debtor',   [DebtController::class, 'settleDebtor']);
        Route::post('/save-customer-debtor',   [DebtController::class, 'saveCustomerDebtors']);
        Route::post('/save-supplier-debtor',   [DebtController::class, 'saveSupplierDebtors']);
        Route::get('/accounts',   [DebtController::class, 'accounts']);
        Route::get('/account-details/{debt}',   [DebtController::class, 'accountDetails']);

        Route::resource('roles', RoleController::class);
        Route::resource('permissions', PermissionController::class);

        Route::resource('branches', BranchController::class);
        Route::get('/set-branch/{branch}', [BranchController::class, 'setBranch' ]);

        Route::resource('customers', CustomerController::class);
        Route::post('/add-credit', [CustomerController::class, 'addCredit']);
        Route::get('/ajax-customer-balance/{customer}', [CustomerController::class, 'ajaxCustomerBalance']);

        Route::resource('invoices', InvoiceController::class);
        Route::get('/ajax-invoice/{invoice}', [InvoiceController::class, 'ajaxInvoice']);
        Route::post('/approve-invoice/{invoice}', [InvoiceController::class, 'approve']);
        Route::post('/cancel-invoice/{invoice}', [InvoiceController::class, 'cancel']);
        Route::get('/have-paid-invoice-infull/{invoice}', [InvoiceController::class, 'havePaidInvoiceInFull']);

        Route::resource('users', UserController::class);
        Route::resource('units', UnitController::class);

        Route::resource('orders', OrderController::class);
        Route::post('/approve-order/{order}', [OrderController::class, 'approve'])->name('orders.approve');
        Route::post('/reject-order/{order}', [OrderController::class, 'reject'])->name('orders.reject');
        Route::get('/ajax-order/{order}', [OrderController::class, 'ajaxOrder']);
        Route::get('/ajax-quotation/{quotation}', [QuotationController::class, 'ajaxQuotation']);
        Route::post('/import-order', [OrderController::class, 'importOrder']);
        Route::get('/orders/{order}/pdf', [OrderController::class, 'downloadPdf'])->name('orders.pdf');

        // Order Adjustments
        Route::get('/adjustments', [OrderController::class, 'adjustments'])->name('orders.adjustments');
        Route::get('/adjustments/create/{originalOrder?}', [OrderController::class, 'createAdjustment'])->name('orders.create-adjustment');
        Route::post('/adjustments/store', [OrderController::class, 'storeAdjustment'])->name('orders.store-adjustment');

        Route::resource('sales', SaleController::class);
        Route::resource('statuses', StatusController::class);
        Route::resource('suppliers', SupplierController::class);

        // Stock Transfer Routes (must be before resource routes)
        Route::get('/stocks/transfers', [StockController::class, 'transfers'])->name('stocks.transfers');
        Route::get('/stocks/transfers/export', [StockController::class, 'exportTransfers'])->name('stocks.transfers.export');
        Route::get('/stocks/transfers/create', [StockController::class, 'createTransfer'])->name('stocks.create-transfer');
        Route::post('/stocks/transfers/store', [StockController::class, 'storeTransfer'])->name('stocks.store-transfer');
        Route::get('/stocks/transfers/pending-approvals', [StockController::class, 'pendingApprovals'])->name('stocks.pending-approvals');
        Route::post('/stocks/transfers/approve', [StockController::class, 'approveTransfer'])->name('stocks.approve-transfer');
        Route::post('/stocks/transfers/reject', [StockController::class, 'rejectTransfer'])->name('stocks.reject-transfer');
        Route::get('/stocks/product-stock', [StockController::class, 'getProductStock'])->name('stocks.product-stock');
        Route::get('/stocks/product-available', [StockController::class, 'getProductAvailable'])->name('stocks.product-available');
        Route::get('/stocks/warehouse-stock', [StockController::class, 'getWarehouseStock'])->name('stocks.warehouse-stock');
        Route::get('/stocks/search-products', [StockController::class, 'searchProducts'])->name('stocks.search-products');
        Route::get('/stocks/taking-sheet', [StockController::class, 'downloadTakingSheet'])->name('stocks.taking-sheet');
        Route::get('/stocks/create-test-transfer', [StockController::class, 'createTestTransfer'])->name('stocks.create-test-transfer');

        // Stock Adjustments
        Route::get('/stocks/adjustments', [StockController::class, 'adjustments'])->name('stocks.adjustments');
        Route::get('/stocks/create-adjustment', [StockController::class, 'createAdjustment'])->name('stocks.create-adjustment');
        Route::post('/stocks/store-adjustment', [StockController::class, 'storeAdjustment'])->name('stocks.store-adjustment');
        Route::post('/stocks/adjustments/approve', [StockController::class, 'approveAdjustment'])->name('stocks.approve-adjustment');

        // Invitation management routes (inside tenant)
        Route::post('/invitations/resend', [App\Http\Controllers\InvitationController::class, 'resend'])->name('invitations.resend');
        Route::post('/invitations/cancel', [App\Http\Controllers\InvitationController::class, 'cancel'])->name('invitations.cancel');

        // Tenant switching routes
        Route::post('/tenant/switch/{tenantId}', [App\Http\Controllers\TenantController::class, 'switch'])->name('tenant.switch');
        Route::post('/tenant/primary/{tenantId}', [App\Http\Controllers\TenantController::class, 'setPrimary'])->name('tenant.set-primary');
        Route::get('/tenant/setup', [App\Http\Controllers\TenantController::class, 'setup'])->name('tenant.setup.show');
        Route::post('/tenant/setup', [App\Http\Controllers\TenantController::class, 'addUserToTenant'])->name('tenant.setup');

        // Profile routes
        Route::get('/profile', [App\Http\Controllers\ProfileController::class, 'show'])->name('profile.show');
        Route::put('/profile', [App\Http\Controllers\ProfileController::class, 'updateProfile'])->name('profile.update');
        Route::put('/profile/password', [App\Http\Controllers\ProfileController::class, 'updatePassword'])->name('profile.password.update');
        Route::post('/profile/avatar', [App\Http\Controllers\ProfileController::class, 'uploadAvatar'])->name('profile.avatar.upload');
        Route::delete('/profile/avatar', [App\Http\Controllers\ProfileController::class, 'removeAvatar'])->name('profile.avatar.remove');

        // Test email route (remove after testing)
        Route::get('/test-email', function() {
            try {
                \Mail::raw('This is a test email from the accounting system.', function($message) {
                    $message->to('<EMAIL>')
                           ->subject('Test Email - Accounting System');
                });
                return 'Test email sent successfully!';
            } catch (\Exception $e) {
                return 'Failed to send email: ' . $e->getMessage();
            }
        });

        // Test invitation email
        Route::get('/test-invitation', function() {
            try {
                $invitation = new \App\Models\UserInvitation([
                    'email' => '<EMAIL>',
                    'token' => 'test-token-123',
                    'tenant_id' => 'test_tenant',
                    'role' => 'user',
                    'message' => 'This is a test invitation',
                    'expires_at' => now()->addDays(7)
                ]);

                \Mail::to('<EMAIL>')
                     ->send(new \App\Mail\UserInvitationMail($invitation, 'Test User', 'Test Tenant'));

                return 'Test invitation email sent successfully!';
            } catch (\Exception $e) {
                return 'Failed to send invitation email: ' . $e->getMessage();
            }
        });

        // Fix business type permissions
        Route::get('/fix-permissions', function() {
            try {
                // Check if permissions table exists
                if (!\Schema::hasTable('permissions')) {
                    return 'Permissions table does not exist. Please run migrations first.';
                }

                $permissions = [
                    'list businesstypes',
                    'view businesstypes',
                    'create businesstypes',
                    'update businesstypes',
                    'delete businesstypes'
                ];

                $results = [];
                foreach ($permissions as $permission) {
                    $perm = \Spatie\Permission\Models\Permission::firstOrCreate(['name' => $permission]);
                    $results[] = "Permission '{$permission}' " . ($perm->wasRecentlyCreated ? 'created' : 'already exists');
                }

                // Assign permissions to super-admin role if it exists
                $superAdminRole = \Spatie\Permission\Models\Role::where('name', 'super-admin')->first();
                if ($superAdminRole) {
                    $superAdminRole->givePermissionTo($permissions);
                    $results[] = 'Assigned permissions to super-admin role';
                }

                // Assign permissions to admin role if it exists
                $adminRole = \Spatie\Permission\Models\Role::where('name', 'admin')->first();
                if ($adminRole) {
                    $adminRole->givePermissionTo($permissions);
                    $results[] = 'Assigned permissions to admin role';
                }

                // Clear permission cache
                app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
                $results[] = 'Cleared permission cache';

                return '<h3>Business Type Permissions Fixed!</h3><ul><li>' . implode('</li><li>', $results) . '</li></ul>';

            } catch (\Exception $e) {
                return 'Error fixing permissions: ' . $e->getMessage();
            }
        });

        // Generic Approval Routes
        Route::get('/pending-approvals', [App\Http\Controllers\ApprovalController::class, 'index'])->name('approvals.index');
        Route::get('/approvals/counts', [App\Http\Controllers\ApprovalController::class, 'getCounts'])->name('approvals.counts');

        // Order Approval Routes (if not already defined elsewhere)
        Route::post('/orders/{order}/approve-via-approvals', [App\Http\Controllers\ApprovalController::class, 'approveOrder'])->name('orders.approve-via-approvals');
        Route::post('/orders/{order}/reject-via-approvals', [App\Http\Controllers\ApprovalController::class, 'rejectOrder'])->name('orders.reject-via-approvals');

        // Stock Utility Routes
        Route::get('/stocks/low-stock-alerts', [StockController::class, 'lowStockAlerts'])->name('stocks.low-stock-alerts');
        Route::get('/stocks/export', [StockController::class, 'export'])->name('stocks.export');

        // Stock Resource Routes (must be after specific routes)
        Route::resource('stocks', StockController::class);

        Route::resource('warehouses', WarehouseController::class);
        Route::resource('locations', LocationController::class);

        // Receivables Routes (Customer Debtors)
        Route::get('/receivables', [ReceivableController::class, 'index'])->name('receivables.index');
        Route::get('/receivables/{customer}', [ReceivableController::class, 'show'])->name('receivables.show');
        Route::get('/receivables/{customer}/payment', [ReceivableController::class, 'createPayment'])->name('receivables.create-payment');
        Route::post('/receivables/{customer}/payment', [ReceivableController::class, 'storePayment'])->name('receivables.store-payment');

        // Payables Routes (Supplier Creditors)
        Route::get('/payables', [PayableController::class, 'index'])->name('payables.index');
        Route::get('/payables/{supplier}', [PayableController::class, 'show'])->name('payables.show');
        Route::get('/payables/{supplier}/payment', [PayableController::class, 'createPayment'])->name('payables.create-payment');
        Route::post('/payables/{supplier}/payment', [PayableController::class, 'storePayment'])->name('payables.store-payment');

        // API Routes for AJAX requests
        Route::get('/customers/api', [CustomerController::class, 'api'])->name('customers.api');
        Route::get('/suppliers/api', [SupplierController::class, 'api'])->name('suppliers.api');

        // Test route for debugging
        Route::get('/test/customers', function() {
            try {
                $customers = \App\Models\Customer::select('id', 'name', 'email')->limit(10)->get();
                return response()->json([
                    'success' => true,
                    'count' => $customers->count(),
                    'customers' => $customers
                ]);
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'error' => $e->getMessage()
                ]);
            }
        });

        // Payment Routes
        Route::post('/payments/ajax', [PaymentController::class, 'storeAjax'])->name('payments.store-ajax');
        Route::get('/payments/customer-invoices', [PaymentController::class, 'getCustomerInvoices'])->name('payments.customer-invoices');
        Route::get('/payments/supplier-bills', [PaymentController::class, 'getSupplierBills'])->name('payments.supplier-bills');
        Route::get('/payments/invoice-details', [PaymentController::class, 'getInvoiceDetails'])->name('payments.invoice-details');
        Route::resource('payments', PaymentController::class);

        Route::resource('quotations', QuotationController::class);
        Route::post('/quotations/{quotation}/approve', [QuotationController::class, 'approve'])->name('quotations.approve');
        Route::post('/quotations/{quotation}/convert-to-invoice', [QuotationController::class, 'convertToInvoice'])->name('quotations.convert-to-invoice');
        Route::post('/quotations/{quotation}/duplicate', [QuotationController::class, 'duplicate'])->name('quotations.duplicate');

        Route::resource('shipments', ShipmentController::class);
        Route::post('/shipments/{shipment}/approve', [ShipmentController::class, 'approve'])->name('shipments.approve');
        Route::post('/shipments/{shipment}/convert-to-invoice', [ShipmentController::class, 'convertToInvoice'])->name('shipments.convert-to-invoice');
        Route::post('/shipments/{shipment}/duplicate', [ShipmentController::class, 'duplicate'])->name('shipments.duplicate');
        Route::get('/ajax-shipment/{shipment}', [ShipmentController::class, 'ajaxShipment']);
        Route::resource('categories', CategoryController::class);
        Route::resource('accounts', AccountController::class);
        Route::get('/get-account-categories', [AccountController::class, 'getAccountCategories']);
        Route::resource('transactions', TransactionController::class);

        // Accounting Module Routes
        Route::resource('account-types', AccountTypeController::class);
        Route::resource('account-categories', AccountCategoryController::class);
        Route::resource('fiscal-years', FiscalYearController::class);
        Route::post('/fiscal-years/{fiscalYear}/close', [FiscalYearController::class, 'close'])->name('fiscal-years.close');
        Route::resource('fiscal-periods', FiscalPeriodController::class);
        Route::post('/fiscal-periods/{fiscalPeriod}/close', [FiscalPeriodController::class, 'close'])->name('fiscal-periods.close');
        Route::post('/fiscal-periods/{fiscalPeriod}/reopen', [FiscalPeriodController::class, 'reopen'])->name('fiscal-periods.reopen');
        Route::get('/get-fiscal-periods', [JournalEntryController::class, 'getFiscalPeriods']);

        Route::resource('currencies', CurrencyController::class);
        Route::resource('exchange-rates', ExchangeRateController::class);

        Route::resource('journal-entries', JournalEntryController::class);
        Route::post('/journal-entries/{journalEntry}/post', [JournalEntryController::class, 'post'])->name('journal-entries.post');
        Route::post('/journal-entries/{journalEntry}/unpost', [JournalEntryController::class, 'unpost'])->name('journal-entries.unpost');

        Route::get('/financial-reports', [FinancialReportController::class, 'index'])->name('financial-reports.index');
        Route::get('/financial-reports/trial-balance', [FinancialReportController::class, 'trialBalance'])->name('financial-reports.trial-balance');
        Route::get('/financial-reports/balance-sheet', [FinancialReportController::class, 'balanceSheet'])->name('financial-reports.balance-sheet');
        Route::get('/financial-reports/income-statement', [FinancialReportController::class, 'incomeStatement'])->name('financial-reports.income-statement');

        Route::resource('asset-categories', AssetCategoryController::class);
        Route::resource('assets', AssetController::class);
        Route::get('/assets/{asset}/dispose-form', [AssetController::class, 'disposeForm'])->name('assets.dispose-form');
        Route::post('/assets/{asset}/dispose', [AssetController::class, 'dispose'])->name('assets.dispose');

        Route::resource('asset-depreciations', AssetDepreciationController::class)->except(['edit', 'update', 'destroy']);
        Route::get('/asset-depreciations/batch', [AssetDepreciationController::class, 'batchForm'])->name('asset-depreciations.batch');
        Route::post('/asset-depreciations/run-batch', [AssetDepreciationController::class, 'runBatch'])->name('asset-depreciations.run-batch');

        Route::resource('bank-accounts', BankAccountController::class);
        Route::resource('bank-reconciliations', BankReconciliationController::class);
        Route::post('/bank-reconciliations/{bankReconciliation}/complete', [BankReconciliationController::class, 'complete'])->name('bank-reconciliations.complete');
        Route::post('/bank-reconciliations/{bankReconciliation}/refresh', [BankReconciliationController::class, 'refresh'])->name('bank-reconciliations.refresh');

        Route::resource('tax-types', TaxTypeController::class);
        Route::resource('tax-transactions', TaxTransactionController::class)->only(['index', 'show']);
        Route::get('/tax-transactions/report', [TaxTransactionController::class, 'report'])->name('tax-transactions.report');

        Route::resource('budgets', BudgetController::class);
        Route::get('/budgets/{budget}/edit-items', [BudgetController::class, 'editItems'])->name('budgets.edit-items');
        Route::post('/budgets/{budget}/update-items', [BudgetController::class, 'updateItems'])->name('budgets.update-items');
        Route::get('/budgets/report', [BudgetController::class, 'report'])->name('budgets.report');

        // Payroll Routes
        Route::prefix('payroll')->name('payroll.')->group(function () {
            Route::get('/', [PayrollController::class, 'index'])->name('index');
            Route::get('/generate', [PayrollController::class, 'generate'])->name('generate');
            Route::post('/generate', [PayrollController::class, 'processGenerate'])->name('process-generate');
            Route::get('/{payroll}', [PayrollController::class, 'show'])->name('show');
            Route::get('/{payroll}/edit', [PayrollController::class, 'edit'])->name('edit');
            Route::put('/{payroll}', [PayrollController::class, 'update'])->name('update');
            Route::get('/{payroll}/payslip', [PayrollController::class, 'payslip'])->name('payslip');
            Route::post('/{payroll}/email-payslip', [PayrollController::class, 'emailPayslip'])->name('email-payslip');
            Route::post('/bulk-email-payslips', [PayrollController::class, 'bulkEmailPayslips'])->name('bulk-email-payslips');
            Route::get('/journal-entries/create', [PayrollController::class, 'createJournalEntries'])->name('create-journal-entries');
            Route::post('/journal-entries', [PayrollController::class, 'journalEntries'])->name('journal-entries');
        });

        Route::resource('deduction-contributions', DeductionContributionController::class);
        Route::get('/deduction-contributions/{deductionContribution}/assign', [DeductionContributionController::class, 'assignForm'])->name('deduction-contributions.assign-form');
        Route::post('/deduction-contributions/{deductionContribution}/assign', [DeductionContributionController::class, 'assign'])->name('deduction-contributions.assign');

        // Employee Management
        Route::resource('employees', EmployeeController::class);

        // Employee Loans Management
        Route::resource('employee-loans', EmployeeLoanController::class);
        Route::post('/employee-loans/{employeeLoan}/approve', [EmployeeLoanController::class, 'approve'])->name('employee-loans.approve');
        Route::post('/employee-loans/{employeeLoan}/cancel', [EmployeeLoanController::class, 'cancel'])->name('employee-loans.cancel');
        Route::get('/employees/{employee}/loans', [EmployeeLoanController::class, 'getEmployeeLoans'])->name('employees.loans');
        Route::get('/employee-loans-statistics', [EmployeeLoanController::class, 'statistics'])->name('employee-loans.statistics');

        // Tax Brackets Management
        Route::resource('tax-brackets', TaxBracketController::class);
        Route::get('tax-brackets-preview', [TaxBracketController::class, 'preview'])->name('tax-brackets.preview');
        Route::get('tax-brackets-calculate', [TaxBracketController::class, 'calculateTax'])->name('tax-brackets.calculate-tax');

        Route::resource('employee-deduction-contributions', EmployeeDeductionContributionController::class);
        Route::get('/employees/{employee}/deduction-contributions', [EmployeeDeductionContributionController::class, 'bulkAssignForm'])->name('employees.deduction-contributions');
        Route::post('/employees/{employee}/deduction-contributions', [EmployeeDeductionContributionController::class, 'bulkAssign'])->name('employees.deduction-contributions.assign');

        // Time Tracking Routes
        Route::prefix('time-tracking')->name('time-tracking.')->group(function () {
            Route::get('/', [\App\Http\Controllers\TimeTrackingController::class, 'index'])->name('index');
            Route::post('/check-in', [\App\Http\Controllers\TimeTrackingController::class, 'checkIn'])->name('check-in');
            Route::post('/check-out', [\App\Http\Controllers\TimeTrackingController::class, 'checkOut'])->name('check-out');
            Route::post('/add-comment', [\App\Http\Controllers\TimeTrackingController::class, 'addComment'])->name('add-comment');
            Route::post('/add-task', [\App\Http\Controllers\TimeTrackingController::class, 'addTask'])->name('add-task');
            Route::get('/records', [\App\Http\Controllers\TimeTrackingController::class, 'getRecords'])->name('records');
        });

        // Work Hours Configuration Routes
        Route::resource('work-hours-configurations', \App\Http\Controllers\WorkHoursConfigurationController::class);
        Route::post('/work-hours-configurations/{workHoursConfiguration}/activate', [\App\Http\Controllers\WorkHoursConfigurationController::class, 'activate'])->name('work-hours-configurations.activate');

        // Payroll Reports
        Route::prefix('payroll-reports')->name('payroll-reports.')->group(function () {
            Route::get('/journal', [PayrollReportController::class, 'journalReportForm'])->name('journal-form');
            Route::post('/journal', [PayrollReportController::class, 'journalReport'])->name('journal');
            Route::get('/liabilities', [PayrollReportController::class, 'liabilitiesReportForm'])->name('liabilities-form');
            Route::post('/liabilities', [PayrollReportController::class, 'liabilitiesReport'])->name('liabilities');
            Route::get('/cost-summary', [PayrollReportController::class, 'costSummaryForm'])->name('cost-summary-form');
            Route::post('/cost-summary', [PayrollReportController::class, 'costSummary'])->name('cost-summary');
        });

        Route::resource('boms', BomController::class);
        Route::get('/boms/{bom}/edit-items', [BomController::class, 'editItems'])->name('boms.edit-items');
        Route::post('/boms/{bom}/update-items', [BomController::class, 'updateItems'])->name('boms.update-items');
        Route::post('/boms/{bom}/copy', [BomController::class, 'copy'])->name('boms.copy');
        Route::get('/get-boms', [ProductionOrderController::class, 'getBoms']);

        Route::resource('production-orders', ProductionOrderController::class);
        Route::get('/production-orders/{productionOrder}/edit-items', [ProductionOrderController::class, 'editItems'])->name('production-orders.edit-items');
        Route::post('/production-orders/{productionOrder}/update-items', [ProductionOrderController::class, 'updateItems'])->name('production-orders.update-items');
        Route::post('/production-orders/{productionOrder}/complete', [ProductionOrderController::class, 'complete'])->name('production-orders.complete');

        Route::resource('products', ProductController::class);
        Route::get('/ajax-products', [ProductController::class, 'ajaxProducts']);
        Route::get('/ajax-product-search', [ProductController::class, 'ajaxProductSearch']);
        Route::get('/ajax-product-by-barcode', [ProductController::class, 'ajaxProductByBarcode']);
        Route::get('/ajax-product/{product}', [ProductController::class, 'ajaxProduct']);
        Route::post('/import-products', [ProductController::class, 'importProducts']);
        Route::get('/product-template', [ProductController::class, 'productTemplate']);

        Route::get('/stock-adjustments', [OrderController::class, 'stockAdjustments']);
        Route::get('/canceled-orders', [OrderController::class, 'canceledOrders']);
        Route::get('/stock-adjustment', [OrderController::class, 'stockAdjustment']);
        Route::post('/stock-adjustment', [OrderController::class, 'stockAdjustmentSave']);

    });
});
