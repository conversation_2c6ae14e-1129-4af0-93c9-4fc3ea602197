<div id="preview-order-el">
  @csrf
  <x-lg-modal>
    <x-slot name="class">preview-order-modal</x-slot>
    <x-slot name="title">PURCHASE ORDER</x-slot>

      <div style="text-transform: uppercase!important;">

        <div class="order-print print" id="order-print" v-if="order"  style="max-width: 400px; margin: auto;">

          <div class="mb-4" style="font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;">
            <!-- Avatar -->
            <center>
              <!-- <div class="avatar avatar-xl avatar-circle avatar-centered mb-3"> -->
              <div class="">
                <img width="50" src="{{ auth()->user()->getGlobalInstance('logo')->path ?? asset('assets/svg/logos/logo-short.svg') }}" alt="Logo">
              </div>

              <div class="text-center">
                <span>{{ auth()->user()->getGlobal("business") ?? ''}}</span>
              </div>           
              <div class="text-center">
                <span>{{ auth()->user()->getGlobal("address") ?? ''}}</span>
              </div>                    
              <div class="text-center">
                <span>{{ auth()->user()->getGlobal("email") ?? ''}}</span>
              </div>              
              <div class="text-center">
                <span>{{ auth()->user()->getGlobal("phone") ?? ''}}</span>
              </div>

            </center>
            <!-- End Avatar -->

            <hr style="margin:5px">

            <div>
              <span class="text-uppercase"> DATE: </span>
              <span style="float: right;" v-text="order.created_at"></span>
            </div>     
            <div>
              <span class="text-uppercase"> SUPPLIER:  </span>
              <span style="float: right;" v-text="order.supplier_name"></span>
            </div>      
            <div>
              <span class="text-uppercase"> RECEIPT #: </span>
              <span style="float: right;" v-text="order.order_id"></span>
            </div>
            <hr style="margin:5px">

            <table style=" " cellpadding="0" cellspacing="0" width="100%">
              <tr>
                <th style="text-align: left;">DESC</th>
                <th style="text-align: center;">UNIT</th>
                <th style="text-align: center;">QTY x UN PRICE</th>
                <th style="text-align: right;">AMOUNT</th>
              </tr>
              <tr v-for="(stock, index) in order.stocks">
                <td style="text-align: left; font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;">
                  <span v-text="stock?.product?.description"></span>
                </td>
                <td style="text-align:center;">
                  <span v-text="stock?.unit_name"></span>
                </td>        
                <td style="text-align:center;">
                  <span v-text="stock.quantity"></span> <i>x</i> <span v-text="formatNumber(stock.buying_price)"></span>
                </td>
                <td style="text-align: right; font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;">
                  <span v-text="formatNumber(stock.buying_amount.toFixed(2))"></span>
                </td>
              </tr>
            </table>  



            <hr style="margin:5px">                 

            <table border="0" cellpadding="0" cellspacing="0" width="100%">

              <tr>
                <td align="left" width="75%" style=" font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong>SUB TOTAL</strong></td>
                <td align="right" width="25%" style=" font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong v-text="formatNumber(order.sub_total)"></strong></td>
              </tr>    

              <tr>
                <td align="left" width="75%" style="font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong>TOTAL VAT</strong></td>
                <td align="right" width="25%" style="font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong v-text="formatNumber(order.vat)"></strong></td>
              </tr>                         


              <tr>
                <td align="left" width="75%" style="font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong>TOTAL</strong></td>
                <td align="right" width="25%" style="font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong v-text="formatNumber((Number(order.sub_total) + Number(order.vat)).toFixed(2))"></strong></td>
              </tr>

              <tr>
                <td align="left" width="75%" style="font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong>DISCOUNT</strong></td>
                <td align="right" width="25%" style="font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;">-<strong v-text="formatNumber(order.discount)"></strong></td>
              </tr> 


              <tr>
                <td align="left" width="75%" style="font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong>TOTAL PAID</strong></td>
                <td align="right" width="25%" style="font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong v-text="formatNumber(order.amount_total)"></strong></td>
              </tr>


              <tr>
                <td align="left" width="75%" style=" font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong>CASH PAID</strong></td>
                <td align="right" width="25%" style=" font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong v-text="formatNumber(order.amount_paid)"></strong></td>
              </tr> 

            </table>

            <hr style="margin:5px">
            <div>
              <span class="text-uppercase"> Operator </span>
              <span style="float: right;" v-text="order?.prepared_by"></span>
            </div>            
            <hr style="margin:5px">
            <div style="text-align:center;">
              <span>END OF RECEIPT</span>
            </div>


          </div>

         </div>

         <div v-else>
            <h1 class="center">Loading...</h1>
         </div>
      </div>


    <x-slot name="footer">
      <div v-if="order">
          <button class="btn btn-sm btn-soft-primary"  onclick="extractPrint('order-print')" href="javascript:;">
            <i class="bi-download me-1"></i> PRINT
          </button>                  
          <button class="btn btn-sm btn-soft-primary"  onclick="createPDFfromHTML('order-print')" href="javascript:;">
            <i class="bi-download me-1"></i> PDF
          </button>                
          <a :href="'/orders/' + order.id " class="btn btn-sm btn-soft-primary">
            <i class="bi-view me-1"></i> MORE
          </a>   
      </div>
    </x-slot>
  </x-lg-modal>
</div>



@push('scripts')

  <script type="text/javascript">
    
    new Vue({
      el: "#preview-order-el",
      data(){
        return{
          order: null,
        }
      },

      methods: {

        getOrder(id) {
          axios.get('/ajax-order/' + id).then( res => {
          	this.order = res.data;
          	// setTimeout( ()=> { window.extractPrint('order-print'); }, 2000)
          }) 
        },

        formatNumber (num) {
            return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")
        },

      },


      mounted(){
      	$(".preview-order-btn").on("click", (e) => {
      		var id = $(e.target).attr("data-id");
      		this.getOrder(id);
      	})
      },

      created(){
        $("body").on("click", ".preview-order-btn", (e) => {
          var id = $(e.target).attr("data-id");
          this.getOrder(id);
        })
      }

    });

  </script>

@endpush
