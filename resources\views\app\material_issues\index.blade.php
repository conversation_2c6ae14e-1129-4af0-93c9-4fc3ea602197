@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">Material Issues</x-slot>
        <x-slot name="controls">
            @can('create', App\Models\MaterialIssue::class)
            <a href="{{ route('material-issues.create') }}" class="btn btn-info btn-sm">
                @lang('crud.common.create') Material Issue
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="searchbar mt-4 mb-5">
        <form class="d-flex" id="filter">
            <div class="me-3">
                <input
                    type="text"
                    name="search"
                    value="{{ $search ?? '' }}"
                    class="form-control"
                    placeholder="{{ __('crud.common.search') }}"
                    autocomplete="off"
                />
            </div>
            <div class="me-3">
                <select name="production_order_id" class="form-select">
                    <option value="">All Production Orders</option>
                    @foreach($productionOrders ?? [] as $id => $orderNumber)
                    <option value="{{ $id }}" {{ $id == ($productionOrderId ?? '') ? 'selected' : '' }}>
                        {{ $orderNumber }}
                    </option>
                    @endforeach
                </select>
            </div>
            <div class="me-3">
                <select name="status" class="form-select">
                    <option value="">All Statuses</option>
                    <option value="draft" {{ ($status ?? '') == 'draft' ? 'selected' : '' }}>Draft</option>
                    <option value="issued" {{ ($status ?? '') == 'issued' ? 'selected' : '' }}>Issued</option>
                    <option value="cancelled" {{ ($status ?? '') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                </select>
            </div>
            <div class="me-3">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-search"></i>
                </button>
            </div>
        </form>
    </div>

    <div class="card card-table">
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            Issue Number
                        </th>
                        <th class="text-left">
                            Production Order
                        </th>
                        <th class="text-left">
                            Issue Date
                        </th>
                        <th class="text-left">
                            Warehouse
                        </th>
                        <th class="text-center">
                            Status
                        </th>
                        <th class="text-center">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($materialIssues ?? [] as $materialIssue)
                    <tr>
                        <td>{{ $materialIssue->issue_number ?? '-' }}</td>
                        <td>{{ optional($materialIssue->productionOrder)->order_number ?? '-' }}</td>
                        <td>{{ $materialIssue->issue_date ? $materialIssue->issue_date->format('Y-m-d') : '-' }}</td>
                        <td>{{ optional($materialIssue->warehouse)->name ?? '-' }}</td>
                        <td class="text-center">
                            <span class="badge {{ 
                                $materialIssue->status == 'draft' ? 'bg-secondary' : 
                                ($materialIssue->status == 'issued' ? 'bg-success' : 'bg-danger') 
                            }}">
                                {{ ucfirst($materialIssue->status ?? '-') }}
                            </span>
                        </td>
                        <td class="text-center" style="width: 134px;">
                            <div
                                role="group"
                                aria-label="Row Actions"
                                class="btn-group"
                            >
                                @can('update', $materialIssue)
                                <a
                                    href="{{ route('material-issues.edit', $materialIssue) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        edit
                                    </button>
                                </a>
                                @endcan @can('view', $materialIssue)
                                <a
                                    href="{{ route('material-issues.show', $materialIssue) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        view
                                    </button>
                                </a>
                                @endcan @can('delete', $materialIssue)
                                <form
                                    action="{{ route('material-issues.destroy', $materialIssue) }}"
                                    method="POST"
                                    onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                >
                                    @csrf @method('DELETE')
                                    <button
                                        type="submit"
                                        class="btn btn-light text-danger m-1"
                                    >
                                        del
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="text-center">
                            @lang('crud.common.no_items_found')
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="card-footer d-flex justify-content-center">
            @if(isset($materialIssues))
            {!! $materialIssues->render() !!}
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
  $("document").ready(function () {
    dataTableBtn()
  });
</script>
@endpush
