<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Shipment;
use Illuminate\Auth\Access\HandlesAuthorization;

class ShipmentPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the shipment can be viewed by the user.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Shipment  $shipment
     * @return bool
     */
    public function view(User $user, Shipment $shipment)
    {
        return $user->hasPermissionTo('list shipments');
    }

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo('list shipments');
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo('create shipments');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Shipment  $shipment
     * @return bool
     */
    public function update(User $user, Shipment $shipment)
    {
        return $user->hasPermissionTo('update shipments');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Shipment  $shipment
     * @return bool
     */
    public function delete(User $user, Shipment $shipment)
    {
        return $user->hasPermissionTo('delete shipments');
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Shipment  $shipment
     * @return bool
     */
    public function restore(User $user, Shipment $shipment)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Shipment  $shipment
     * @return bool
     */
    public function forceDelete(User $user, Shipment $shipment)
    {
        return false;
    }
}
