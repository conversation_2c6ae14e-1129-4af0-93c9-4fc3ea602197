<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class BusinessTypeMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {

        if( !auth()->user()->business_type_id ) {
        
            return redirect('/business-types')->with("error", "Please Setup and Select Business Type first .");
        
        }

        if ( auth()->check() && ! auth()->user()->branch ) {
            $branch = \App\Models\Branch::first();
            if ( $branch ) auth()->user()->update(['branch_id' => $branch->id ]);
        }

        return $next($request);
    }
}
