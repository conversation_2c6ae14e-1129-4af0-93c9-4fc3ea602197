@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <x-page-header>
        <x-slot name="title">Units & Pricing</x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Product::class)
            <a href="{{ route('units.create') }}" class="btn btn-info btn-sm">
                <!-- <i class="icon ion-md-add"></i> -->
                @lang('crud.common.create') Unit
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="searchbar mt-4 mb-5">
        <form class="d-flex" id="filter">
                <div class="tom-select-custom me-2 _200px">
                  <select class="js-select form-select branch-filter" name="product_id" autocomplete="off" data-hs-tom-select-options='{
                    "placeholder": "Select a product..."
                  }'>
                  <option></option>
                  @foreach( App\Models\Product::get() as $product)
                    <option value="{{ $product->id }}" @if($product->id == request()->product_id ) selected @endif>
                      {{ $product->name }}
                    </option>
                  @endforeach
                  </select>
                </div>


                <div class="input-group-append">
                    <button type="submit" class="btn btn-white">
                        Search
                    </button>
                </div>
        </form>
    </div>

    <!-- Table -->
    <div class="card card-table">
        <div class="table-responsive mt-3">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">

                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            Product
                        </th>                    
                        <th class="text-left">
                            @lang('crud.units.inputs.name')
                        </th>
                        <th class="text-left">
                            @lang('crud.units.inputs.quantity')
                        </th>                    
                        <th class="text-left">
                            Buying Price
                        </th>                    
                        <th class="text-left">
                            Selling Price
                        </th>
                        <th class="text-left">
                            Description
                        </th>
                        <th class="text-center">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($units as $unit)
                    <tr>
                        <td>{{ $unit->product->name ?? '-' }}</td>
                        <td>{{ $unit->name ?? '-' }}</td>
                        <td>{{ $unit->quantity ?? '-' }}</td>
                        <td>{{ $unit->buying_price ?? '-' }}</td>
                        <td>{{ $unit->selling_price ?? '-' }}</td>
                        <td>{{ $unit->description ?? '-' }}</td>
                        <td class="text-center" style="width: 134px;">
                            <div
                                role="group"
                                aria-label="Row Actions"
                                class="btn-group"
                            >
                                @can('update', $unit)
                                <a href="{{ route('units.edit', $unit) }}">
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        <!-- <i class="icon ion-md-create"></i> --> edit
                                    </button>
                                </a>
                                @endcan @can('view', $unit)
                                <a href="{{ route('units.show', $unit) }}">
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        <!-- <i class="icon ion-md-eye"></i> --> view
                                    </button>
                                </a>
                                @endcan @can('delete', $unit)
                                <form
                                    action="{{ route('units.destroy', $unit) }}"
                                    method="POST"
                                    onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                >
                                    @csrf @method('DELETE')
                                    <button
                                        type="submit"
                                        class="btn btn-light text-danger m-1"
                                    >
                                        <!-- <i class="icon ion-md-trash"></i> --> del
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    
                    @endforelse
                </tbody>
            </table>

        </div>
    </div>
    




</div>
@endsection





@push('scripts')
<script>

    $(document).on('ready', function () {
      // INITIALIZATION OF DATERANGEPICKER

      dataTableBtn()  
    });
</script>

@endpush