<?php

namespace Tests\Feature\Controllers;

use App\Models\User;
use App\Models\Stock;

use App\Models\Status;
use App\Models\Product;
use App\Models\Supplier;

use Tests\TestCase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class StockControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $this->actingAs(
            User::factory()->create(['email' => '<EMAIL>'])
        );

        $this->seed(\Database\Seeders\PermissionsSeeder::class);

        $this->withoutExceptionHandling();
    }

    /**
     * @test
     */
    public function it_displays_index_view_with_stocks()
    {
        $stocks = Stock::factory()
            ->count(5)
            ->create();

        $response = $this->get(route('stocks.index'));

        $response
            ->assertOk()
            ->assertViewIs('app.stocks.index')
            ->assertViewHas('stocks');
    }

    /**
     * @test
     */
    public function it_displays_create_view_for_stock()
    {
        $response = $this->get(route('stocks.create'));

        $response->assertOk()->assertViewIs('app.stocks.create');
    }

    /**
     * @test
     */
    public function it_stores_the_stock()
    {
        $data = Stock::factory()
            ->make()
            ->toArray();

        $response = $this->post(route('stocks.store'), $data);

        unset($data['created_by']);
        unset($data['updated_by']);
        unset($data['status_id']);

        $this->assertDatabaseHas('stocks', $data);

        $stock = Stock::latest('id')->first();

        $response->assertRedirect(route('stocks.edit', $stock));
    }

    /**
     * @test
     */
    public function it_displays_show_view_for_stock()
    {
        $stock = Stock::factory()->create();

        $response = $this->get(route('stocks.show', $stock));

        $response
            ->assertOk()
            ->assertViewIs('app.stocks.show')
            ->assertViewHas('stock');
    }

    /**
     * @test
     */
    public function it_displays_edit_view_for_stock()
    {
        $stock = Stock::factory()->create();

        $response = $this->get(route('stocks.edit', $stock));

        $response
            ->assertOk()
            ->assertViewIs('app.stocks.edit')
            ->assertViewHas('stock');
    }

    /**
     * @test
     */
    public function it_updates_the_stock()
    {
        $stock = Stock::factory()->create();

        $product = Product::factory()->create();
        $user = User::factory()->create();
        $user = User::factory()->create();
        $status = Status::factory()->create();
        $supplier = Supplier::factory()->create();
        $user = User::factory()->create();
        $stock = Stock::factory()->create();

        $data = [
            'quantity' => $this->faker->randomNumber,
            'balance' => $this->faker->randomNumber(0),
            'description' => $this->faker->sentence(15),
            'product_id' => $product->id,
            'created_by' => $user->id,
            'updated_by' => $user->id,
            'status_id' => $status->id,
            'supplier_id' => $supplier->id,
            'approved_by' => $user->id,
            'stock_id' => $stock->id,
        ];

        $response = $this->put(route('stocks.update', $stock), $data);

        unset($data['created_by']);
        unset($data['updated_by']);
        unset($data['status_id']);

        $data['id'] = $stock->id;

        $this->assertDatabaseHas('stocks', $data);

        $response->assertRedirect(route('stocks.edit', $stock));
    }

    /**
     * @test
     */
    public function it_deletes_the_stock()
    {
        $stock = Stock::factory()->create();

        $response = $this->delete(route('stocks.destroy', $stock));

        $response->assertRedirect(route('stocks.index'));

        $this->assertSoftDeleted($stock);
    }
}
