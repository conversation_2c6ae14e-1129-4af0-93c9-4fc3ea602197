<?php

namespace App\Http\Controllers;

use App\Models\Status;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Http\Requests\CustomerStoreRequest;
use App\Http\Requests\CustomerUpdateRequest;

use Facades\App\Libraries\InvoiceHandler;

class CustomerController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Customer::class);

        $search = $request->get('search', '');

        $customers = Customer::search($search)
            ->latest()
            ->paginate()
            ->withQueryString();

        return view('app.customers.index', compact('customers', 'search'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Customer::class);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');

        return view('app.customers.create', compact('statuses'));
    }

    /**
     * @param \App\Http\Requests\CustomerStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(CustomerStoreRequest $request)
    {
        $this->authorize('create', Customer::class);

        $validated = $request->validated();

        $customer = Customer::create($validated);

        if(request()->image) {
            $customer->clearMediaCollection('image');
            // foreach (request()->image as $file) {
            if(request()->image) $customer->addMedia(request()->image)->toMediaCollection("image");
            // }
        }

        return redirect()
            ->route('customers.edit', $customer)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Customer $customer
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Customer $customer)
    {
        $this->authorize('view', $customer);

        return view('app.customers.show', compact('customer'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Customer $customer
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Customer $customer)
    {
        $this->authorize('update', $customer);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');

        return view('app.customers.edit', compact('customer', 'statuses'));
    }

    /**
     * @param \App\Http\Requests\CustomerUpdateRequest $request
     * @param \App\Models\Customer $customer
     * @return \Illuminate\Http\Response
     */
    public function update(CustomerUpdateRequest $request, Customer $customer)
    {
        $this->authorize('update', $customer);

        $validated = $request->validated();

        $customer->update($validated);

        if(request()->image) {
            $customer->clearMediaCollection('image');
            // foreach (request()->image as $file) {
            if(request()->image) $customer->addMedia(request()->image)->toMediaCollection("image");
            // }
        }

        return redirect()
            ->route('customers.edit', $customer)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Customer $customer
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Customer $customer)
    {
        $this->authorize('delete', $customer);

        if ($customer->name) {
            Storage::delete($customer->name);
        }

        $customer->delete();

        return redirect()
            ->route('customers.index')
            ->withSuccess(__('crud.common.removed'));
    }

    public function addCredit(Request $request){

        $customer = Customer::find($request->customer_id);
        if( $customer ) {
            $invoices = $customer->invoices()->whereNotNull('approved_by')->get();
            $amountPaid = $request->amount_paid;

            foreach ($invoices as $invoice) {


                $invoiceBalance = $invoice->balance;
                if($amountPaid && $invoiceBalance) {

                    if( $amountPaid >= $invoiceBalance ) {
                        $amountPaid = $amountPaid - $invoiceBalance;
                        $balance = 0;
                        $amount  = $invoiceBalance;
                        $invoice->increment("amount_paid", $invoiceBalance);
                    } else {
                        $balance = $invoiceBalance - $amountPaid;
                        $amount  = $amountPaid;
                        $amountPaid = 0;
                        $invoice->increment("amount_paid", $amount);
                    }

                    $params = [
                        'amount'  => $amount,
                        'comment' => request()->comment,
                        'description' => request()->comment,
                        'balance' => $balance,
                    ];

                    InvoiceHandler::addPayment($invoice, $params);

                }

            }
        }

        return redirect()->back();
    }

    public function ajaxCustomerBalance(Request $request, Customer $customer){
        $customer->balance = $customer->invoices()->whereNotNull('approved_by')->sum("amount_total") - $customer->invoices()->whereNotNull('approved_by')->sum("amount_paid");
        return response($customer);
    }

    /**
     * Get customers for API/AJAX requests
     */
    public function api(Request $request)
    {
        try {
            $this->authorize('view-any', Customer::class);
        } catch (\Exception $e) {
            \Log::error('Customer API authorization failed: ' . $e->getMessage());
            return response()->json(['error' => 'Authorization failed: ' . $e->getMessage()], 403);
        }

        try {
            $customers = Customer::select('id', 'name', 'email')
                ->when(!auth()->user()->isSuperAdmin(), function($query) {
                    $query->where('branch_id', auth()->user()->branch_id);
                })
                ->orderBy('name')
                ->get();

            \Log::info('Customer API: Returning ' . $customers->count() . ' customers');
            return response()->json($customers);
        } catch (\Exception $e) {
            \Log::error('Customer API error: ' . $e->getMessage());
            return response()->json(['error' => 'Database error: ' . $e->getMessage()], 500);
        }
    }
}
