@php $editing = isset($sale) @endphp

<div id="salesForm">
    <div class="row">
        <!-- Product Selection Card -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-box-seam me-2"></i>Product Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="product_id" class="form-label">Select Product <span class="text-danger">*</span></label>
                            <div class="tom-select-custom">
                                <select 
                                    id="product_id" 
                                    name="product_id" 
                                    class="js-select form-select" 
                                    required
                                    data-hs-tom-select-options='{
                                        "placeholder": "Search for a product...",
                                        "hideSearch": false
                                    }'
                                    @change="updateProductDetails"
                                    v-model="selectedProductId"
                                >
                                    <option value="">Select a product</option>
                                    @foreach($products as $value => $label)
                                    <option 
                                        value="{{ $value }}" 
                                        {{ old('product_id', ($editing ? $sale->product_id : '')) == $value ? 'selected' : '' }}
                                    >
                                        {{ $label }}
                                    </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="form-text">Select the product you want to sell</div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="product_name" class="form-label">Product Name <span class="text-danger">*</span></label>
                            <input
                                type="text"
                                id="product_name"
                                name="product_name"
                                class="form-control"
                                v-model="productName"
                                placeholder="Product name will appear here"
                                required
                                readonly
                            >
                            <div class="form-text">This field will be auto-filled based on your product selection</div>
                        </div>
                        
                        <div class="col-md-12 mt-3" v-if="productDescription">
                            <div class="alert alert-soft-info mb-0">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-info-circle"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <h6 class="mb-1">Product Description</h6>
                                        <p class="mb-0" v-text="productDescription"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Unit and Pricing Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-tag me-2"></i>Unit & Pricing
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="unit_id" class="form-label">Select Unit <span class="text-danger">*</span></label>
                        <div class="tom-select-custom">
                            <select 
                                id="unit_id" 
                                name="unit_id" 
                                class="form-select" 
                                required
                                @change="updateUnitDetails"
                                v-model="selectedUnitId"
                            >
                                <option value="">Select a unit</option>
                                @foreach($units as $value => $label)
                                <option 
                                    value="{{ $value }}" 
                                    {{ old('unit_id', ($editing ? $sale->unit_id : '')) == $value ? 'selected' : '' }}
                                >
                                    {{ $label }}
                                </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-text">Select the unit of measurement for this sale</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="unit_name" class="form-label">Unit Name</label>
                        <input
                            type="text"
                            id="unit_name"
                            name="unit_name"
                            class="form-control"
                            v-model="unitName"
                            placeholder="Unit name will appear here"
                            readonly
                        >
                        <div class="form-text">This field will be auto-filled based on your unit selection</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="price" class="form-label">Unit Price <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input
                                type="number"
                                id="price"
                                name="price"
                                class="form-control"
                                v-model="price"
                                step="0.01"
                                min="0"
                                placeholder="0.00"
                                required
                                @input="calculateTotal"
                            >
                        </div>
                        <div class="form-text">Enter the price per unit</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quantity and Discount Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-calculator me-2"></i>Quantity & Discount
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="quantity" class="form-label">Quantity <span class="text-danger">*</span></label>
                        <input
                            type="number"
                            id="quantity"
                            name="quantity"
                            class="form-control"
                            v-model="quantity"
                            min="1"
                            step="1"
                            placeholder="1"
                            required
                            @input="calculateTotal"
                        >
                        <div class="form-text">Enter the quantity being sold</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="discount" class="form-label">Discount Amount</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input
                                type="number"
                                id="discount"
                                name="discount"
                                class="form-control"
                                v-model="discount"
                                step="0.01"
                                min="0"
                                placeholder="0.00"
                                @input="calculateTotal"
                            >
                        </div>
                        <div class="form-text">Enter any discount amount to be applied</div>
                    </div>
                    
                    <div class="alert alert-soft-success mt-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="fw-semibold">Subtotal:</span>
                            <span class="fw-bold" v-text="formatCurrency(subtotal)"></span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <span class="fw-semibold">Discount:</span>
                            <span class="fw-bold text-danger" v-text="formatCurrency(discount)"></span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="fw-semibold">Total:</span>
                            <span class="fw-bold fs-4" v-text="formatCurrency(total)"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Additional Information Card -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-info-circle me-2"></i>Additional Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea 
                                id="notes" 
                                name="notes" 
                                class="form-control" 
                                rows="3" 
                                placeholder="Enter any additional notes about this sale"
                            >{{ old('notes', ($editing ? $sale->notes : '')) }}</textarea>
                            <div class="form-text">Optional: Add any relevant notes about this sale</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    new Vue({
        el: '#salesForm',
        data: {
            selectedProductId: '{{ old('product_id', ($editing ? $sale->product_id : '')) }}',
            selectedUnitId: '{{ old('unit_id', ($editing ? $sale->unit_id : '')) }}',
            productName: '{{ old('product_name', ($editing ? $sale->product_name : '')) }}',
            productDescription: '',
            unitName: '{{ old('unit_name', ($editing ? $sale->unit_name : '')) }}',
            price: {{ old('price', ($editing ? $sale->price : '0')) }},
            quantity: {{ old('quantity', ($editing ? $sale->quantity : '1')) }},
            discount: {{ old('discount', ($editing ? $sale->discount : '0')) }},
            subtotal: 0,
            total: 0,
            products: @json($products),
            units: @json($units)
        },
        mounted() {
            this.calculateTotal();
            
            // Initialize product details if editing
            if (this.selectedProductId) {
                this.updateProductDetails();
            }
            
            // Initialize unit details if editing
            if (this.selectedUnitId) {
                this.updateUnitDetails();
            }
        },
        methods: {
            updateProductDetails() {
                // This would normally fetch product details from the server
                // For now, we'll just update the product name
                if (this.selectedProductId) {
                    const productLabel = document.querySelector(`#product_id option[value="${this.selectedProductId}"]`).textContent;
                    this.productName = productLabel.trim();
                    this.productDescription = "Product description would be loaded here based on the selected product.";
                } else {
                    this.productName = '';
                    this.productDescription = '';
                }
            },
            updateUnitDetails() {
                // This would normally fetch unit details from the server
                // For now, we'll just update the unit name
                if (this.selectedUnitId) {
                    const unitLabel = document.querySelector(`#unit_id option[value="${this.selectedUnitId}"]`).textContent;
                    this.unitName = unitLabel.trim();
                } else {
                    this.unitName = '';
                }
            },
            calculateTotal() {
                this.subtotal = this.price * this.quantity;
                this.total = Math.max(0, this.subtotal - this.discount);
            },
            formatCurrency(value) {
                return '$' + parseFloat(value).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
            }
        }
    });
</script>
@endpush
