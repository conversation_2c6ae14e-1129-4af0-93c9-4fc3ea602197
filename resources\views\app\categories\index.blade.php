@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">folder</x-slot>
        <x-slot name="title">Categories</x-slot>
        <x-slot name="subtitle">Manage product and service categories</x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Category::class)
            <a href="{{ route('categories.create') }}" class="btn btn-primary btn-sm">
                <i class="bi bi-plus-circle me-1"></i> Add Category
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Search & Filters -->
    <div class="card mb-3">
        <div class="card-body">
            <form action="{{ route('categories.index') }}" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search Categories</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="search" name="search" placeholder="Name, description..." value="{{ request('search') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="group" class="form-label">Group</label>
                    <select class="form-select js-select" id="group" name="group">
                        <option value="">All Groups</option>
                        <option value="product" {{ request('group') == 'product' ? 'selected' : '' }}>Product</option>
                        <option value="service" {{ request('group') == 'service' ? 'selected' : '' }}>Service</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select js-select" id="status" name="status_id">
                        <option value="">All Statuses</option>
                        @foreach(\App\Models\Status::orderBy('name')->get() as $status)
                        <option value="{{ $status->id }}" {{ request('status_id') == $status->id ? 'selected' : '' }}>{{ $status->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-filter me-1"></i> Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Table -->
    <div class="card card-table">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">Category List</h4>
                </div>
                <div class="col-auto">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-download me-1"></i> Export
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="exportDropdown">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-excel me-1"></i> Excel</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-pdf me-1"></i> PDF</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-text me-1"></i> CSV</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            @lang('crud.categories.inputs.name')
                        </th>                    
                        <th class="text-left">
                            Group
                        </th>
                        <th class="text-left">
                            @lang('crud.categories.inputs.status_id')
                        </th>
                        <th class="text-left">
                            @lang('crud.categories.inputs.description')
                        </th>
                        <th class="text-center">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($categories as $category)
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h5 class="text-inherit mb-0">{{ $category->name ?? '-' }}</h5>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-soft-primary">{{ $category->applied_to ?? '-' }}</span>
                        </td>
                        <td>
                            <span class="badge bg-{{ optional($category->status)->name == 'Active' ? 'success' : 'secondary' }}">
                                {{ optional($category->status)->name ?? '-' }}
                            </span>
                        </td>
                        <td>{{ $category->description ?? '-' }}</td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                @can('update', $category)
                                <a href="{{ route('categories.edit', $category) }}" class="btn btn-white btn-sm">
                                    <i class="bi-pencil-fill me-1"></i> Edit
                                </a>
                                @endcan

                                <!-- Button Group -->
                                <div class="btn-group">
                                    <button type="button" class="btn btn-white btn-icon btn-sm dropdown-toggle dropdown-toggle-empty" id="categoryDropdown{{ $category->id }}" data-bs-toggle="dropdown" aria-expanded="false"></button>
                                    <div class="dropdown-menu dropdown-menu-end mt-1" aria-labelledby="categoryDropdown{{ $category->id }}">
                                        @can('view', $category)
                                        <a class="dropdown-item" href="{{ route('categories.show', $category) }}">
                                            <i class="bi-eye dropdown-item-icon"></i> View
                                        </a>
                                        @endcan
                                        @can('delete', $category)
                                        <div class="dropdown-divider"></div>
                                        <form action="{{ route('categories.destroy', $category) }}" method="POST" onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')">
                                            @csrf @method('DELETE')
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="bi-trash dropdown-item-icon"></i> Delete
                                            </button>
                                        </form>
                                        @endcan
                                    </div>
                                </div>
                                <!-- End Button Group -->
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="5" class="text-center p-4">
                            <div class="d-flex flex-column align-items-center justify-content-center py-5">
                                <i class="bi bi-folder text-muted" style="font-size: 3rem;"></i>
                                <h5 class="mt-4">No categories found</h5>
                                <p class="text-muted">Try adjusting your search or filter to find what you're looking for.</p>
                                @can('create', App\Models\Category::class)
                                <a href="{{ route('categories.create') }}" class="btn btn-primary mt-2">
                                    <i class="bi bi-plus-circle me-1"></i> Add New Category
                                </a>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        @if(isset($categories) && method_exists($categories, 'hasPages') && $categories->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-center">
                {{ $categories->links() }}
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
  $("document").ready(function () {
    dataTableBtn();
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl)
    });
  });
</script>
@endpush
