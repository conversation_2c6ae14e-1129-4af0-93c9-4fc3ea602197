@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">key</x-slot>
        <x-slot name="title">System Permissions</x-slot>
        <x-slot name="subtitle">Manage individual permissions that can be assigned to roles</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item active" aria-current="page">Permissions</li>
        </x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Permission::class)
            <a href="{{ route('permissions.create') }}" class="btn btn-primary btn-sm">
                <i class="bi bi-plus-circle me-1"></i> Create Permission
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Search & Info Cards Row -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <!-- Search Card -->
            <div class="card">
                <div class="card-body">
                    <form action="{{ route('permissions.index') }}" method="GET">
                        <div class="input-group">
                            <span class="input-group-text bg-transparent">
                                <i class="bi bi-search"></i>
                            </span>
                            <input
                                type="text"
                                name="search"
                                class="form-control"
                                placeholder="Search permissions..."
                                value="{{ $search ?? '' }}"
                                aria-label="Search permissions"
                            />
                            <button type="submit" class="btn btn-primary">Search</button>
                            @if(isset($search))
                            <a href="{{ route('permissions.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i>
                            </a>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Info Card -->
            <div class="card bg-soft-info h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-info-circle text-info" style="font-size: 2rem;"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="card-title">About Permissions</h5>
                            <p class="card-text mb-0">Permissions define individual actions that can be performed in the system. Assign them to roles to control user access.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Permissions Table Card -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-header-title">
                    <i class="bi bi-list-check me-2"></i>All Permissions
                </h5>
                <span class="badge bg-primary">{{ $permissions->total() }} permissions</span>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered table-hover mb-0">
                    <thead class="thead-light">
                        <tr>
                            <th style="width: 5%">#</th>
                            <th style="width: 40%">Permission Name</th>
                            <th style="width: 25%">Resource</th>
                            <th style="width: 15%">Roles Using</th>
                            <th style="width: 15%" class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($permissions as $index => $permission)
                        @php
                            $parts = explode('-', $permission->name);
                            $action = $parts[0] ?? '';
                            $resource = $parts[1] ?? 'other';
                        @endphp
                        <tr>
                            <td>{{ $permissions->firstItem() + $index }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    @php
                                        $iconClass = 'bi-shield';
                                        
                                        if (strpos($permission->name, 'view') === 0) {
                                            $iconClass = 'bi-eye';
                                        } elseif (strpos($permission->name, 'create') === 0) {
                                            $iconClass = 'bi-plus-circle';
                                        } elseif (strpos($permission->name, 'update') === 0) {
                                            $iconClass = 'bi-pencil-square';
                                        } elseif (strpos($permission->name, 'delete') === 0) {
                                            $iconClass = 'bi-trash';
                                        }
                                    @endphp
                                    <span class="me-2"><i class="bi {{ $iconClass }}"></i></span>
                                    <span class="fw-semibold">{{ ucfirst(str_replace('-', ' ', $permission->name)) }}</span>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-soft-secondary text-capitalize">{{ $resource }}</span>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $permission->roles->count() }} roles</span>
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    @can('view', $permission)
                                    <a href="{{ route('permissions.show', $permission) }}" class="btn btn-outline-info btn-sm" data-bs-toggle="tooltip" title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    @endcan
                                    
                                    @can('update', $permission)
                                    <a href="{{ route('permissions.edit', $permission) }}" class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" title="Edit Permission">
                                        <i class="bi bi-pencil-square"></i>
                                    </a>
                                    @endcan
                                    
                                    @can('delete', $permission)
                                    <form action="{{ route('permissions.destroy', $permission) }}" method="POST" class="d-inline" 
                                          onsubmit="return confirm('Are you sure you want to delete this permission? This may affect existing roles.')">
                                        @csrf @method('DELETE')
                                        <button type="submit" class="btn btn-outline-danger btn-sm" data-bs-toggle="tooltip" title="Delete Permission">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                    @endcan
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="5" class="text-center p-4">
                                <x-empty-state 
                                    icon="key" 
                                    title="No permissions found" 
                                    message="No permissions match your search criteria or no permissions have been created yet."
                                    action="{{ can('create', App\Models\Permission::class) ? 'true' : 'false' }}"
                                    actionUrl="{{ route('permissions.create') }}"
                                    actionText="Create Permission"
                                />
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        
        @if($permissions->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-center">
                {!! $permissions->render() !!}
            </div>
        </div>
        @endif
    </div>

    <!-- Permission Management Info -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-header-title">
                <i class="bi bi-info-circle me-2"></i>Understanding Permissions
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3 mb-md-0">
                    <h6><i class="bi bi-key me-2"></i>What are permissions?</h6>
                    <p class="text-muted small">Permissions are individual actions that can be performed in the system, like "create-users" or "view-reports".</p>
                </div>
                <div class="col-md-4 mb-3 mb-md-0">
                    <h6><i class="bi bi-shield me-2"></i>Permissions vs. Roles</h6>
                    <p class="text-muted small">Permissions are granular actions, while roles are collections of permissions assigned to users.</p>
                </div>
                <div class="col-md-4">
                    <h6><i class="bi bi-gear me-2"></i>Best Practices</h6>
                    <p class="text-muted small">Create specific, well-named permissions and group them logically into roles based on job functions.</p>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Initialize tooltips
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
@endpush

@endsection
