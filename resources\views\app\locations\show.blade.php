@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">geo-alt</x-slot>
        <x-slot name="title">{{ $location->name }}</x-slot>
        <x-slot name="subtitle">Location details and information</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('locations.index') }}">Locations</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ $location->name }}</li>
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group">
                <a href="{{ route('locations.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-1"></i> Back to Locations
                </a>
                @can('update', $location)
                <a href="{{ route('locations.edit', $location) }}" class="btn btn-primary">
                    <i class="bi bi-pencil me-1"></i> Edit Location
                </a>
                @endcan
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <div class="col-lg-8 mb-4 mb-lg-0">
            <!-- Location Details Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-header-title">
                            <i class="bi bi-info-circle me-2"></i>Location Information
                        </h4>
                        @if($location->status)
                            <span class="badge {{ $location->status->id == 1 ? 'bg-success' : 'bg-secondary' }}">
                                {{ $location->status->name }}
                            </span>
                        @endif
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6 mb-4">
                            <h6 class="text-muted mb-2">Location Name</h6>
                            <p class="mb-0">{{ $location->name }}</p>
                        </div>

                        @if($location->code)
                        <div class="col-sm-6 mb-4">
                            <h6 class="text-muted mb-2">Location Code</h6>
                            <span class="badge bg-soft-info">{{ $location->code }}</span>
                        </div>
                        @endif

                        @if($location->description)
                        <div class="col-12 mb-4">
                            <h6 class="text-muted mb-2">Description</h6>
                            <p class="mb-0">{{ $location->description }}</p>
                        </div>
                        @endif

                        <div class="col-sm-6 mb-4">
                            <h6 class="text-muted mb-2">Status</h6>
                            @if($location->status)
                                <span class="badge {{ $location->status->id == 1 ? 'bg-success' : 'bg-secondary' }}">
                                    <i class="bi {{ $location->status->id == 1 ? 'bi-check-circle' : 'bi-x-circle' }} me-1"></i>
                                    {{ $location->status->name }}
                                </span>
                            @else
                                <span class="text-muted">No status assigned</span>
                            @endif
                        </div>

                        <div class="col-sm-6 mb-4">
                            <h6 class="text-muted mb-2">Created</h6>
                            <p class="mb-0">
                                {{ $location->created_at->format('M d, Y \a\t g:i A') }}
                                @if($location->createdBy)
                                    <br><small class="text-muted">by {{ $location->createdBy->name }}</small>
                                @endif
                            </p>
                        </div>

                        @if($location->updated_at != $location->created_at)
                        <div class="col-sm-6 mb-4">
                            <h6 class="text-muted mb-2">Last Updated</h6>
                            <p class="mb-0">
                                {{ $location->updated_at->format('M d, Y \a\t g:i A') }}
                                @if($location->updatedBy)
                                    <br><small class="text-muted">by {{ $location->updatedBy->name }}</small>
                                @endif
                            </p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            <!-- End Location Details Card -->
        </div>

        <div class="col-lg-4">
            <!-- Quick Actions Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-lightning me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @can('update', $location)
                        <a href="{{ route('locations.edit', $location) }}" class="btn btn-outline-primary">
                            <i class="bi bi-pencil me-1"></i> Edit Location
                        </a>
                        @endcan

                        @can('create', App\Models\Location::class)
                        <a href="{{ route('locations.create') }}" class="btn btn-outline-success">
                            <i class="bi bi-plus-circle me-1"></i> Add New Location
                        </a>
                        @endcan

                        <a href="{{ route('locations.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-list me-1"></i> View All Locations
                        </a>

                        @can('delete', $location)
                        <hr class="my-3">
                        <form action="{{ route('locations.destroy', $location) }}" method="POST" 
                              onsubmit="return confirm('Are you sure you want to delete this location? This action cannot be undone.')">
                            @csrf @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="bi bi-trash me-1"></i> Delete Location
                            </button>
                        </form>
                        @endcan
                    </div>
                </div>
            </div>
            <!-- End Quick Actions Card -->

            <!-- Location Stats Card -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-bar-chart me-2"></i>Location Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="mb-1 text-primary">{{ $location->created_at->diffInDays() }}</h4>
                                <small class="text-muted">Days Active</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="mb-1 text-success">{{ $location->isActive() ? 'Active' : 'Inactive' }}</h4>
                            <small class="text-muted">Current Status</small>
                        </div>
                    </div>

                    <hr class="my-3">

                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">Location ID</span>
                        <span class="fw-semibold">#{{ $location->id }}</span>
                    </div>

                    @if($location->code)
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">Location Code</span>
                        <span class="fw-semibold">{{ $location->code }}</span>
                    </div>
                    @endif

                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">Business Type</span>
                        <span class="fw-semibold">{{ $location->businessType->name ?? 'Default' }}</span>
                    </div>
                </div>
            </div>
            <!-- End Location Stats Card -->
        </div>
    </div>
</div>
<!-- End Content -->
@endsection
