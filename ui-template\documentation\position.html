<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from htmlstream.com/front-dashboard/documentation/position.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 30 Aug 2022 07:13:09 GMT -->
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Position - Documentation | Front - Multipurpose Responsive Template</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="../favicon.ico">

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&amp;display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="../assets/css/vendor.min.css">

  <!-- CSS Front Template -->
  <link rel="stylesheet" href="../assets/css/theme.minc619.css?v=1.0">

  <link rel="preload" href="../assets/css/theme.min.css" data-hs-appearance="default" as="style">
  <link rel="preload" href="../assets/css/docs.css" data-hs-appearance="default" as="style">
  <link rel="preload" href="../assets/css/theme-dark.min.css" data-hs-appearance="dark" as="style">

  <style data-hs-appearance-onload-styles>
    *
    {
      transition: unset !important;
    }

    body
    {
      opacity: 0;
    }
  </style>

  <script>
            window.hs_config = {"autopath":"@@autopath","deleteLine":"hs-builder:delete","deleteLine:build":"hs-builder:build-delete","deleteLine:dist":"hs-builder:dist-delete","previewMode":false,"startPath":"/index.html","vars":{"themeFont":"https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap","version":"?v=1.0"},"layoutBuilder":{"extend":{"switcherSupport":true},"header":{"layoutMode":"default","containerMode":"container-fluid"},"sidebarLayout":"default"},"themeAppearance":{"layoutSkin":"default","sidebarSkin":"default","styles":{"colors":{"primary":"#377dff","transparent":"transparent","white":"#fff","dark":"132144","gray":{"100":"#f9fafc","900":"#1e2022"}},"font":"Inter"}},"languageDirection":{"lang":"en"},"skipFilesFromBundle":{"dist":["assets/js/hs.theme-appearance.js","assets/js/hs.theme-appearance-charts.html","assets/js/demo.js"],"build":["assets/css/theme.css","assets/vendor/hs-navbar-vertical-aside/dist/hs-navbar-vertical-aside-mini-cache.html","assets/js/demo.html","assets/css/theme-dark.html","assets/css/docs.html","assets/vendor/icon-set/style.html","assets/js/hs.theme-appearance.html","assets/js/hs.theme-appearance-charts.html","node_modules/chartjs-plugin-datalabels/dist/chartjs-plugin-datalabels.min.html","assets/js/demo.js"]},"minifyCSSFiles":["assets/css/theme.css","assets/css/theme-dark.css"],"copyDependencies":{"dist":{"*assets/js/theme-custom.js":""},"build":{"*assets/js/theme-custom.js":"","node_modules/bootstrap-icons/font/*fonts/**":"assets/css"}},"buildFolder":"","replacePathsToCDN":{},"directoryNames":{"src":"./src","dist":"./dist","build":"./build"},"fileNames":{"dist":{"js":"theme.min.js","css":"theme.min.css"},"build":{"css":"theme.min.css","js":"theme.min.js","vendorCSS":"vendor.min.css","vendorJS":"vendor.min.js"}},"fileTypes":"jpg|png|svg|mp4|webm|ogv|json"}
            window.hs_config.gulpRGBA = (p1) => {
  const options = p1.split(',')
  const hex = options[0].toString()
  const transparent = options[1].toString()

  var c;
  if(/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)){
    c= hex.substring(1).split('');
    if(c.length== 3){
      c= [c[0], c[0], c[1], c[1], c[2], c[2]];
    }
    c= '0x'+c.join('');
    return 'rgba('+[(c>>16)&255, (c>>8)&255, c&255].join(',')+',' + transparent + ')';
  }
  throw new Error('Bad Hex');
}
            window.hs_config.gulpDarken = (p1) => {
  const options = p1.split(',')

  let col = options[0].toString()
  let amt = -parseInt(options[1])
  var usePound = false

  if (col[0] == "#") {
    col = col.slice(1)
    usePound = true
  }
  var num = parseInt(col, 16)
  var r = (num >> 16) + amt
  if (r > 255) {
    r = 255
  } else if (r < 0) {
    r = 0
  }
  var b = ((num >> 8) & 0x00FF) + amt
  if (b > 255) {
    b = 255
  } else if (b < 0) {
    b = 0
  }
  var g = (num & 0x0000FF) + amt
  if (g > 255) {
    g = 255
  } else if (g < 0) {
    g = 0
  }
  return (usePound ? "#" : "") + (g | (b << 8) | (r << 16)).toString(16)
}
            window.hs_config.gulpLighten = (p1) => {
  const options = p1.split(',')

  let col = options[0].toString()
  let amt = parseInt(options[1])
  var usePound = false

  if (col[0] == "#") {
    col = col.slice(1)
    usePound = true
  }
  var num = parseInt(col, 16)
  var r = (num >> 16) + amt
  if (r > 255) {
    r = 255
  } else if (r < 0) {
    r = 0
  }
  var b = ((num >> 8) & 0x00FF) + amt
  if (b > 255) {
    b = 255
  } else if (b < 0) {
    b = 0
  }
  var g = (num & 0x0000FF) + amt
  if (g > 255) {
    g = 255
  } else if (g < 0) {
    g = 0
  }
  return (usePound ? "#" : "") + (g | (b << 8) | (r << 16)).toString(16)
}
            </script>
</head>

<body class="navbar-sidebar-aside-lg">

  <script src="../assets/js/hs.theme-appearance.js"></script>

  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand navbar-end navbar-light navbar-sticky-lg-top bg-white">
    <div class="container-fluid">
      <nav class="navbar-nav-wrap">
        <div class="row flex-grow-1">
          <!-- Default Logo -->
          <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
            <a class="navbar-brand" href="index.html" aria-label="Front">
              <img class="navbar-brand-logo" src="../assets/svg/logos/logo.svg" alt="Logo" data-hs-theme-appearance="default">
              <img class="navbar-brand-logo" src="../assets/svg/logos-light/logo.svg" alt="Logo" data-hs-theme-appearance="dark">
            </a>
            <a href="changelog.html">
              <span class="badge bg-soft-primary text-primary">v2.1</span>
            </a>
          </div>
          <!-- End Default Logo -->

          <div class="col-md px-lg-0">
            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
              <div class="d-none d-md-block">
                <!-- Search Form -->
                <form id="docsSearch" class="position-relative" data-hs-list-options='{
                       "searchMenu": true,
                       "keyboard": true,
                       "item": "searchTemplate",
                       "valueNames": ["component", "category", {"name": "link", "attr": "href"}],
                       "empty": "#searchNoResults"
                     }'>
                  <!-- Input Group -->
                  <div class="input-group input-group-merge navbar-input-group">
                    <div class="input-group-prepend input-group-text">
                      <i class="bi-search"></i>
                    </div>

                    <input type="search" class="search form-control form-control-sm" placeholder="Search in docs" aria-label="Search in docs">

                    <a class="input-group-append input-group-text" href="javascript:;">
                      <i id="clearSearchResultsIcon" class="bi-x" style="display: none;"></i>
                    </a>
                  </div>
                  <!-- End Input Group -->

                  <!-- List -->
                  <div class="list dropdown-menu navbar-dropdown-menu-borderless w-100 overflow-auto" style="max-height: 16rem;"></div>
                  <!-- End List -->

                  <!-- Empty -->
                  <div id="searchNoResults" style="display: none;">
                    <div class="text-center p-4">
                      <img class="mb-3" src="../assets/svg/illustrations/oc-error.svg" alt="Image Description" data-hs-theme-appearance="default" style="width: 7rem;">
                      <img class="mb-3" src="../assets/svg/illustrations-light/oc-error.svg" alt="Image Description" data-hs-theme-appearance="dark" style="width: 7rem;">
                      <p class="mb-0">No Results</p>
                    </div>
                  </div>
                  <!-- End Empty -->
                </form>
                <!-- End Search Form -->

                <!-- List Item Template -->
                <div class="d-none">
                  <div id="searchTemplate" class="dropdown-item">
                    <a class="d-block link" href="#">
                      <span class="category d-block fw-normal text-muted mb-1"></span>
                      <span class="component text-dark"></span>
                    </a>
                  </div>
                </div>
                <!-- End List Item Template -->
              </div>

              <!-- Navbar -->
              <ul class="navbar-nav p-0">
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://htmlstream.com/contact-us" target="_blank">
                    Get Support <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-primary btn-sm" href="../index.html">
                    <i class="bi-eye me-1"></i> Preview Demo
                  </a>
                </li>
              </ul>
              <!-- End Navbar -->
            </div>
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </nav>
    </div>
  </header>

  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <!-- Navbar -->
    <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end" data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
      <!-- Navbar Toggle -->
      <div class="d-grid flex-grow-1 px-2">
        <button type="button" class="navbar-toggler btn btn-white" data-bs-toggle="collapse" data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false" aria-controls="navbarVerticalNavMenu">
          <span class="d-flex justify-content-between align-items-center">
            <span class="h3 mb-0">Nav menu</span>

            <span class="navbar-toggler-default">
              <i class="bi-list"></i>
            </span>

            <span class="navbar-toggler-toggled">
              <i class="bi-x"></i>
            </span>
          </span>
        </button>
      </div>
      <!-- End Navbar Toggle -->

      <!-- Navbar Collapse -->
      <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
        <div class="navbar-brand-wrapper border-end" style="height: auto;">
          <!-- Default Logo -->
          <div class="d-flex align-items-center mb-3">
            <a class="navbar-brand" href="index.html" aria-label="Front">
              <img class="navbar-brand-logo" src="../assets/svg/logos/logo.svg" alt="Logo" data-hs-theme-appearance="default">
              <img class="navbar-brand-logo" src="../assets/svg/logos-light/logo.svg" alt="Logo" data-hs-theme-appearance="dark">
            </a>
            <a class="navbar-brand-badge" href="changelog.html">
              <span class="badge bg-soft-primary text-primary ms-2">v2.1</span>
            </a>
          </div>
          <!-- End Default Logo -->
        </div>

        <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
          <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
            <li class="nav-item">
              <span class="nav-subtitle">Documentation</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="index.html">Introduction</a>
            </li>

            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Getting started</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="getting-started.html">Getting Started</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="gulp.html">Gulp</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="darkmode.html">Dark Mode <span class="badge bg-soft-dark text-dark lh-base ms-1">New</span></a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="customization.html">Customization</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="credits.html">Credits</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="changelog.html">Changelog</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Design &amp; Graphics</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="bs-icons.html">Bootstrap Icons</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="illustrations.html">Illustrations</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Components</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="accordion.html">Accordion</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="alerts.html">Alerts</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="avatars.html">Avatars</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="badge.html">Badge</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="breadcrumb.html">Breadcrumb</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="buttons.html">Buttons</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="button-group.html">Button Group</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="cards.html">Cards</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="collapse.html">Collapse</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="column-divider.html">Column Divider</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="devices.html">Devices</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="divider.html">Divider</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="dropdowns.html">Dropdowns</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="icons.html">Icons</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="list-group.html">List Group</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="lists.html">Lists</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="legend-indicator.html">Legend Indicator</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="modal.html">Modal</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="offcanvas.html">Offcanvas</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="page-header.html">Page Header</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="pagination.html">Pagination</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="popovers.html">Popovers</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="progress.html">Progress</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="profile.html">Profile</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="shapes.html">Shapes</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sliding-img.html">Sliding Image</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="spinners.html">Spinners</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="steps.html">Steps</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="tab.html">Tab</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="toasts.html">Toasts</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="tooltips.html">Tooltips</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="typography.html">Typography</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Navbars</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="navbar.html">Navbar</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="navs.html">Navs</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="mega-menu.html">Mega Menu</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="navbar-vertical-aside.html">Navbar Vertical Aside</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="scrollspy.html">Scrollspy</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Tables</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="tables.html">Tables</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="datatables.html">Datatables</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="table-sticky-header.html">Sticky Header</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Basic forms</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="basic-forms.html">Basic Forms</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="checks-and-switches.html">Checks &amp; Switches</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="input-group.html">Input Group</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle">Advanced Forms</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="select.html">Advanced Select</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="datepicker.html">Datepicker (Flatpickr)</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="daterangepicker.html">Date Range Picker</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="calendar.html">Calendar (Fullcalendar)</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="file-attachments.html">File Attachments</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="dropzone.html">Drag’ n’ Drop File Uploads</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="quill.html">WYSIWYG Editor</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="quantity-counter.html">Quantity Counter</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="copy-to-clipboard.html">Copy to Clipboard</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="input-mask.html">Input Mask</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="step-forms.html">Step Forms (Wizards)</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="add-field.html">Add Field</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="toggle-password.html">Toggle Password</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="count-characters.html">Count Characters</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="form-search.html">Form Search</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="toggle-switch.html">Toggle Switch</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="google-recaptcha.html">Google reCAPTCHA</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle">Charts</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="chartjs.html">Chart.js</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="counter.html">Counter</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="circles.html">Circles.js (Pie Chart)</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle">Others</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="fslightbox.html">Fullscreen Lightbox</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="maps-leaflet.html">Leaflet</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="maps-jsvectormap.html">JSVectorMap</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sortablejs.html">SortableJS</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sticky-block.html">Sticky Block</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="go-to.html">Go To</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Utilities</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="backgrounds.html">Backgrounds</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="borders.html">Borders</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="colors.html">Colors</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="links.html">Links</a>
            </li>

            <li class="nav-item">
              <a class="nav-link active" href="position.html">Position</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="shadows.html">Shadows</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sizing.html">Sizing</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="spacing.html">Spacing</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="z-index.html">Z-index</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="opacity.html">Opacity</a>
            </li>
          </ul>
        </div>
      </div>
      <!-- End Navbar Collapse -->
    </nav>
    <!-- End Navbar -->

    <!-- Content -->
    <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
      <!-- Page Header -->
      <div class="docs-page-header">
        <div class="row align-items-center">
          <div class="col-sm">
            <h1 class="docs-page-header-title">Position</h1>
            <p class="docs-page-header-text">Use these shorthand utilities for quickly configuring the position of an element.</p>
            <a class="link" href="https://getbootstrap.com/docs/5.0/utilities/position/" target="_blank">Bootstrap Position documentation <i class="bi-box-arrow-up-right"></i></a>
          </div>
        </div>
      </div>
      <!-- End Page Header -->

      <!-- Heading -->
      <h2 id="fixed-top" class="hs-docs-heading">
        Fixed top <a class="anchorjs-link" href="#fixed-top" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Position an element at the top of the viewport, from edge to edge. Be sure you understand the ramifications of fixed position in your project; you may need to add additional CSS.</p>

      <pre class="rounded">
        <code class="language-markup" data-lang="html">
          &lt;div class="fixed-top"&gt;...&lt;/div&gt;
        </code>
      </pre>

      <!-- Heading -->
      <h2 id="fixed-bottom" class="hs-docs-heading">
        Fixed bottom <a class="anchorjs-link" href="#fixed-bottom" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Position an element at the bottom of the viewport, from edge to edge. Be sure you understand the ramifications of fixed position in your project; you may need to add additional CSS.</p>

      <pre class="rounded">
        <code class="language-markup" data-lang="html">
          &lt;div class="fixed-bottom"&gt;...&lt;/div&gt;
        </code>
      </pre>

      <!-- Heading -->
      <h2 id="sticky-top" class="hs-docs-heading">
        Fixed bottom <a class="anchorjs-link" href="#sticky-top" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Position an element at the top of the viewport, from edge to edge, but only after you scroll past it. The .sticky-top utility uses CSS’s position: sticky, which isn’t fully supported in all browsers.</p>

      <pre class="rounded">
        <code class="language-markup" data-lang="html">
          &lt;div class="sticky-top"&gt;...&lt;/div&gt;
        </code>
      </pre>

      <!-- Heading -->
      <h2 id="position-values" class="hs-docs-heading">
        Position values <a class="anchorjs-link" href="#position-values" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Quick positioning classes are available, and they are responsive.</p>

      <pre class="rounded mb-7">
        <code class="language-markup" data-lang="html">
          &lt;div class="position-static"&gt;...&lt;/div&gt;
          &lt;div class="position-relative"&gt;...&lt;/div&gt;
          &lt;div class="position-absolute"&gt;...&lt;/div&gt;
          &lt;div class="position-fixed"&gt;...&lt;/div&gt;
          &lt;div class="position-sticky"&gt;...&lt;/div&gt;

          &lt;div class="position-sm-static"&gt;...&lt;/div&gt;
          &lt;div class="position-sm-relative"&gt;...&lt;/div&gt;
          &lt;div class="position-sm-absolute"&gt;...&lt;/div&gt;
          &lt;div class="position-sm-fixed"&gt;...&lt;/div&gt;
          &lt;div class="position-sm-sticky"&gt;...&lt;/div&gt;

          &lt;div class="position-md-static"&gt;...&lt;/div&gt;
          &lt;div class="position-md-relative"&gt;...&lt;/div&gt;
          &lt;div class="position-md-absolute"&gt;...&lt;/div&gt;
          &lt;div class="position-md-fixed"&gt;...&lt;/div&gt;
          &lt;div class="position-md-sticky"&gt;...&lt;/div&gt;

          &lt;div class="position-lg-static"&gt;...&lt;/div&gt;
          &lt;div class="position-lg-relative"&gt;...&lt;/div&gt;
          &lt;div class="position-lg-absolute"&gt;...&lt;/div&gt;
          &lt;div class="position-lg-fixed"&gt;...&lt;/div&gt;
          &lt;div class="position-lg-sticky"&gt;...&lt;/div&gt;

          &lt;div class="position-xl-static"&gt;...&lt;/div&gt;
          &lt;div class="position-xl-relative"&gt;...&lt;/div&gt;
          &lt;div class="position-xl-absolute"&gt;...&lt;/div&gt;
          &lt;div class="position-xl-fixed"&gt;...&lt;/div&gt;
          &lt;div class="position-xl-sticky"&gt;...&lt;/div&gt;

          &lt;div class="position-xxl-static"&gt;...&lt;/div&gt;
          &lt;div class="position-xxl-relative"&gt;...&lt;/div&gt;
          &lt;div class="position-xxl-absolute"&gt;...&lt;/div&gt;
          &lt;div class="position-xxl-fixed"&gt;...&lt;/div&gt;
          &lt;div class="position-xxl-sticky"&gt;...&lt;/div&gt;
        </code>
      </pre>

      <!-- Heading -->
      <h2 id="arrange-elements" class="hs-docs-heading">
        Arrange elements <a class="anchorjs-link" href="#arrange-elements" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Arrange elements easily with the edge positioning utilities. The format is <code>{property}-{position}</code>.</p>

      <p>Where <em>property</em> is one of:</p>

      <ul>
        <li><code>top</code> - for the vertical <code>top</code> position</li>
        <li><code>start</code> - for the horizontal <code>left</code> position (in LTR)</li>
        <li><code>bottom</code> - for the vertical <code>bottom</code> position</li>
        <li><code>end</code> - for the horizontal <code>right</code> position (in LTR)</li>
      </ul>

      <p>Where <em>position</em> is one of:</p>

      <ul>
        <li><code>0</code> - for <code>0</code> edge position</li>
        <li><code>50</code> - for <code>50%</code> edge position</li>
        <li><code>100</code> - for <code>100%</code> edge position</li>
      </ul>

      <p>(You can add more position values by adding entries to the <code>$position-values</code> Sass map variable.)</p>

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab1" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-htmlTab1" href="#nav-html1" data-bs-toggle="pill" data-bs-target="#nav-html1" role="tab" aria-controls="nav-html1" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-gulpTab1" href="#nav-gulp1" data-bs-toggle="pill" data-bs-target="#nav-gulp1" role="tab" aria-controls="nav-gulp1" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent1">
          <div class="tab-pane fade p-4 show active" id="nav-html1" role="tabpanel" aria-labelledby="nav-htmlTab1">
            <div class="position-relative bg-light" style="height: 200px;">
              <div class="position-absolute top-0 start-0" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute top-0 end-0" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute top-50 start-50" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute bottom-50 end-50" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute bottom-0 start-0" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute bottom-0 end-0" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
            </div>
          </div>

          <div class="tab-pane fade" id="nav-gulp1" role="tabpanel" aria-labelledby="nav-gulpTab1">
            <pre>
              <code class="language-html" data-lang="html">
                &lt;div class="top-0"&gt;...&lt;/div&gt;
                &lt;div class="right-0"&gt;...&lt;/div&gt;
                &lt;div class="bottom-0"&gt;...&lt;/div&gt;
                &lt;div class="left-0"&gt;...&lt;/div&gt;

                &lt;div class="top-sm-0"&gt;...&lt;/div&gt;
                &lt;div class="right-sm-0"&gt;...&lt;/div&gt;
                &lt;div class="bottom-sm-0"&gt;...&lt;/div&gt;
                &lt;div class="left-sm-0"&gt;...&lt;/div&gt;

                &lt;div class="top-md-0"&gt;...&lt;/div&gt;
                &lt;div class="right-md-0"&gt;...&lt;/div&gt;
                &lt;div class="bottom-md-0"&gt;...&lt;/div&gt;
                &lt;div class="left-md-0"&gt;...&lt;/div&gt;

                &lt;div class="top-lg-0"&gt;...&lt;/div&gt;
                &lt;div class="right-lg-0"&gt;...&lt;/div&gt;
                &lt;div class="bottom-lg-0"&gt;...&lt;/div&gt;
                &lt;div class="left-lg-0"&gt;...&lt;/div&gt;

                &lt;div class="top-xl-0"&gt;...&lt;/div&gt;
                &lt;div class="right-xl-0"&gt;...&lt;/div&gt;
                &lt;div class="bottom-xl-0"&gt;...&lt;/div&gt;
                &lt;div class="left-xl-0"&gt;...&lt;/div&gt;

                &lt;div class="top-xxl-0"&gt;...&lt;/div&gt;
                &lt;div class="right-xxl-0"&gt;...&lt;/div&gt;
                &lt;div class="bottom-xxl-0"&gt;...&lt;/div&gt;
                &lt;div class="left-xxl-0"&gt;...&lt;/div&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="center-elements" class="hs-docs-heading">
        Center elements <a class="anchorjs-link" href="#center-elements" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>In addition, you can also center the elements with the transform utility class <code>.translate-middle</code>.</p>

      <p>This class applies the transformations <code>translateX(-50%)</code> and <code>translateY(-50%)</code> to the element which, in combination with the edge positioning utilities, allows you to absolute center an element.</p>

      <!-- Card -->
      <div class="card mb-7">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab2" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-htmlTab2" href="#nav-html2" data-bs-toggle="pill" data-bs-target="#nav-html2" role="tab" aria-controls="nav-html2" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-gulpTab2" href="#nav-gulp2" data-bs-toggle="pill" data-bs-target="#nav-gulp2" role="tab" aria-controls="nav-gulp2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent2">
          <div class="tab-pane fade p-4 show active" id="nav-html2" role="tabpanel" aria-labelledby="nav-htmlTab2">
            <div class="position-relative bg-light" style="height: 200px;">
              <div class="position-absolute top-0 start-0 translate-middle" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute top-0 start-50 translate-middle" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute top-0 start-100 translate-middle" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute top-50 start-0 translate-middle" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute top-50 start-50 translate-middle" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute top-50 start-100 translate-middle" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute top-100 start-0 translate-middle" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute top-100 start-50 translate-middle" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute top-100 start-100 translate-middle" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
            </div>
          </div>

          <div class="tab-pane fade" id="nav-gulp2" role="tabpanel" aria-labelledby="nav-gulpTab2">
            <pre>
              <code class="language-html" data-lang="html">
                &lt;div class="position-relative"&gt;
                  &lt;div class="position-absolute top-0 start-0 translate-middle"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-0 start-50 translate-middle"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-0 start-100 translate-middle"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-50 start-0 translate-middle"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-50 start-50 translate-middle"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-50 start-100 translate-middle"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-100 start-0 translate-middle"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-100 start-50 translate-middle"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-100 start-100 translate-middle"&gt;&lt;/div&gt;
                &lt;/div&gt;

                &lt;div class="position-relative"&gt;
                  &lt;div class="position-absolute top-sm-0 start-sm-0 translate-middle-sm"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-sm-0 start-sm-50 translate-middle-sm"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-sm-0 start-sm-100 translate-middle-sm"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-sm-50 start-sm-0 translate-middle-sm"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-sm-50 start-sm-50 translate-middle-sm"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-sm-50 start-sm-100 translate-middle-sm"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-sm-100 start-sm-0 translate-middle-sm"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-sm-100 start-sm-50 translate-middle-sm"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-sm-100 start-sm-100 translate-middle-sm"&gt;&lt;/div&gt;
                &lt;/div&gt;

                &lt;div class="position-relative"&gt;
                  &lt;div class="position-absolute top-lg-0 start-lg-0 translate-middle-lg"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-lg-0 start-lg-50 translate-middle-lg"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-lg-0 start-lg-100 translate-middle-lg"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-lg-50 start-lg-0 translate-middle-lg"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-lg-50 start-lg-50 translate-middle-lg"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-lg-50 start-lg-100 translate-middle-lg"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-lg-100 start-lg-0 translate-middle-lg"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-lg-100 start-lg-50 translate-middle-lg"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-lg-100 start-lg-100 translate-middle-lg"&gt;&lt;/div&gt;
                &lt;/div&gt;

                &lt;div class="position-relative"&gt;
                  &lt;div class="position-absolute top-xl-0 start-xl-0 translate-middle-xl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xl-0 start-xl-50 translate-middle-xl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xl-0 start-xl-100 translate-middle-xl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xl-50 start-xl-0 translate-middle-xl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xl-50 start-xl-50 translate-middle-xl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xl-50 start-xl-100 translate-middle-xl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xl-100 start-xl-0 translate-middle-xl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xl-100 start-xl-50 translate-middle-xl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xl-100 start-xl-100 translate-middle-xl"&gt;&lt;/div&gt;
                &lt;/div&gt;

                &lt;div class="position-relative"&gt;
                  &lt;div class="position-absolute top-xxl-0 start-xxl-0 translate-middle-xxl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xxl-0 start-xxl-50 translate-middle-xxl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xxl-0 start-xxl-100 translate-middle-xxl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xxl-50 start-xxl-0 translate-middle-xxl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xxl-50 start-xxl-50 translate-middle-xxl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xxl-50 start-xxl-100 translate-middle-xxl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xxl-100 start-xxl-0 translate-middle-xxl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xxl-100 start-xxl-50 translate-middle-xxl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xxl-100 start-xxl-100 translate-middle-xxl"&gt;&lt;/div&gt;
                &lt;/div&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <p>By adding <code>.translate-middle-x</code> or <code>.translate-middle-y</code> classes, elements can be positioned only in horizontal or vertical direction.</p>

      <!-- Card -->
      <div class="card mb-7">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab3" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-htmlTab3" href="#nav-html3" data-bs-toggle="pill" data-bs-target="#nav-html3" role="tab" aria-controls="nav-html3" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-gulpTab3" href="#nav-gulp3" data-bs-toggle="pill" data-bs-target="#nav-gulp3" role="tab" aria-controls="nav-gulp3" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent3">
          <div class="tab-pane fade p-4 show active" id="nav-html3" role="tabpanel" aria-labelledby="nav-htmlTab3">
            <div class="position-relative bg-light" style="height: 200px;">
              <div class="position-absolute top-0 start-0" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute top-0 start-50 translate-middle-x" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute top-0 end-0" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute top-50 start-0 translate-middle-y" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute top-50 start-50 translate-middle" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute top-50 end-0 translate-middle-y" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute bottom-0 start-0" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute bottom-0 start-50 translate-middle-x" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
              <div class="position-absolute bottom-0 end-0" style="width: 2em; height: 2em; background-color: #212529; border-radius: .25rem;"></div>
            </div>
          </div>

          <div class="tab-pane fade" id="nav-gulp3" role="tabpanel" aria-labelledby="nav-gulpTab3">
            <pre>
              <code class="language-html" data-lang="html">
                &lt;div class="position-relative"&gt;
                  &lt;div class="position-absolute top-0 start-0"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-0 start-50 translate-middle-x"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-0 end-0"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-50 start-0 translate-middle-y"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-50 start-50 translate-middle"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-50 end-0 translate-middle-y"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute bottom-0 start-0"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute bottom-0 start-50 translate-middle-x"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute bottom-0 end-0"&gt;&lt;/div&gt;
                &lt;/div&gt;

                &lt;div class="position-relative"&gt;
                  &lt;div class="position-absolute top-sm-0 start-sm-0"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-sm-0 start-sm-50 translate-middle-sm-x"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-sm-0 end-sm-0"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-sm-50 start-sm-0 translate-middle-sm-y"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-sm-50 start-sm-50 translate-middle-sm"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-sm-50 end-sm-0 translate-middle-sm-y"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute bottom-sm-0 start-sm-0"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute bottom-sm-0 start-sm-50 translate-middle-sm-x"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute bottom-sm-0 end-sm-0"&gt;&lt;/div&gt;
                &lt;/div&gt;

                &lt;div class="position-relative"&gt;
                  &lt;div class="position-absolute top-lg-0 start-lg-0"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-lg-0 start-lg-50 translate-middle-lg-x"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-lg-0 end-lg-0"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-lg-50 start-lg-0 translate-middle-lg-y"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-lg-50 start-lg-50 translate-middle-lg"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-lg-50 end-lg-0 translate-middle-lg-y"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute bottom-lg-0 start-lg-0"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute bottom-lg-0 start-lg-50 translate-middle-lg-x"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute bottom-lg-0 end-lg-0"&gt;&lt;/div&gt;
                &lt;/div&gt;

                &lt;div class="position-relative"&gt;
                  &lt;div class="position-absolute top-xl-0 start-xl-0"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xl-0 start-xl-50 translate-middle-xl-x"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xl-0 end-xl-0"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xl-50 start-xl-0 translate-middle-xl-y"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xl-50 start-xl-50 translate-middle-xl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xl-50 end-xl-0 translate-middle-xl-y"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute bottom-xl-0 start-xl-0"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute bottom-xl-0 start-xl-50 translate-middle-xl-x"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute bottom-xl-0 end-xl-0"&gt;&lt;/div&gt;
                &lt;/div&gt;

                &lt;div class="position-relative"&gt;
                  &lt;div class="position-absolute top-xxl-0 start-xxl-0"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xxl-0 start-xxl-50 translate-middle-xxl-x"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xxl-0 end-xxl-0"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xxl-50 start-xxl-0 translate-middle-xxl-y"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xxl-50 start-xxl-50 translate-middle-xxl"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute top-xxl-50 end-xxl-0 translate-middle-xxl-y"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute bottom-xxl-0 start-xxl-0"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute bottom-xxl-0 start-xxl-50 translate-middle-xxl-x"&gt;&lt;/div&gt;
                  &lt;div class="position-absolute bottom-xxl-0 end-xxl-0"&gt;&lt;/div&gt;
                &lt;/div&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="examples" class="hs-docs-heading">
        Examples <a class="anchorjs-link" href="#examples" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Here are some real life examples of these classes:</p>

      <!-- Card -->
      <div class="card mb-7">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab4" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-htmlTab4" href="#nav-html4" data-bs-toggle="pill" data-bs-target="#nav-html4" role="tab" aria-controls="nav-html4" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-gulpTab4" href="#nav-gulp4" data-bs-toggle="pill" data-bs-target="#nav-gulp4" role="tab" aria-controls="nav-gulp4" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent4">
          <div class="tab-pane fade p-4 show active" id="nav-html4" role="tabpanel" aria-labelledby="nav-htmlTab4">
            <div class="d-flex justify-content-around">
              <button type="button" class="btn btn-primary position-relative">
                Mails <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-secondary">+99 <span class="visually-hidden">unread messages</span></span>
              </button>

              <button type="button" class="btn btn-dark position-relative">
                Marker <svg width="1em" height="1em" viewBox="0 0 16 16" class="position-absolute top-100 start-50 translate-middle mt-1 bi bi-caret-down-fill" fill="#212529" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7.247 11.14L2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                </svg>
              </button>

              <button type="button" class="btn btn-primary position-relative">
                Alerts <span class="position-absolute top-0 start-100 translate-middle badge border border-light rounded-circle bg-danger p-2"><span class="visually-hidden">unread messages</span></span>
              </button>
            </div>
          </div>

          <div class="tab-pane fade" id="nav-gulp4" role="tabpanel" aria-labelledby="nav-gulpTab4">
            <pre>
              <code class="language-html" data-lang="html">
                &lt;button type="button" class="btn btn-primary position-relative"&gt;
                  Mails &lt;span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-secondary"&gt;+99 &lt;span class="visually-hidden"&gt;unread messages&lt;/span&gt;&lt;/span&gt;
                &lt;/button&gt;

                &lt;button type="button" class="btn btn-dark position-relative"&gt;
                  Marker &lt;svg width="1em" height="1em" viewBox="0 0 16 16" class="position-absolute top-100 start-50 translate-middle mt-1 bi bi-caret-down-fill" fill="#212529" xmlns="http://www.w3.org/2000/svg"&gt;&lt;path d="M7.247 11.14L2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/&gt;&lt;/svg&gt;
                &lt;/button&gt;

                &lt;button type="button" class="btn btn-primary position-relative"&gt;
                  Alerts &lt;span class="position-absolute top-0 start-100 translate-middle badge border border-light rounded-circle bg-danger p-2"&gt;&lt;span class="visually-hidden"&gt;unread messages&lt;/span&gt;&lt;/span&gt;
                &lt;/button&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <p>You can use these classes with existing components to create new ones. Remember that you can extend its functionality by adding entries to the <code>$position-values</code> variable.</p>

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab5" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-htmlTab5" href="#nav-html5" data-bs-toggle="pill" data-bs-target="#nav-html5" role="tab" aria-controls="nav-html5" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-gulpTab5" href="#nav-gulp5" data-bs-toggle="pill" data-bs-target="#nav-gulp5" role="tab" aria-controls="nav-gulp5" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent5">
          <div class="tab-pane fade p-4 show active" id="nav-html5" role="tabpanel" aria-labelledby="nav-htmlTab5">
            <div class="position-relative m-5">
              <div class="progress" style="height: 1px;">
                <div class="progress-bar" role="progressbar" style="width: 50%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
              <button type="button" class="position-absolute top-0 start-0 translate-middle btn btn-sm btn-primary btn-icon rounded-pill" style="width: 2rem; height:2rem;">1</button>
              <button type="button" class="position-absolute top-0 start-50 translate-middle btn btn-sm btn-primary btn-icon rounded-pill" style="width: 2rem; height:2rem;">2</button>
              <button type="button" class="position-absolute top-0 start-100 translate-middle btn btn-sm btn-secondary btn-icon rounded-pill" style="width: 2rem; height:2rem;">3</button>
            </div>
          </div>

          <div class="tab-pane fade" id="nav-gulp5" role="tabpanel" aria-labelledby="nav-gulpTab5">
            <pre>
              <code class="language-html" data-lang="html">
                &lt;div class="position-relative m-4"&gt;
                  &lt;div class="progress" style="height: 1px;"&gt;
                    &lt;div class="progress-bar" role="progressbar" style="width: 50%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"&gt;&lt;/div&gt;
                  &lt;/div&gt;
                  &lt;button type="button" class="position-absolute top-0 start-0 translate-middle btn btn-sm btn-primary btn-icon rounded-pill" style="width: 2rem; height:2rem;"&gt;1&lt;/button&gt;
                  &lt;button type="button" class="position-absolute top-0 start-50 translate-middle btn btn-sm btn-primary btn-icon rounded-pill" style="width: 2rem; height:2rem;"&gt;2&lt;/button&gt;
                  &lt;button type="button" class="position-absolute top-0 start-100 translate-middle btn btn-sm btn-secondary btn-icon rounded-pill" style="width: 2rem; height:2rem;"&gt;3&lt;/button&gt;
                &lt;/div&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->
    </div>
    <!-- End Content -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- ========== SECONDARY CONTENTS ========== -->
  <!-- Go To -->
  <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
    <i class="bi-chevron-up"></i>
  </a>
  <!-- ========== END SECONDARY CONTENTS ========== -->

  <!-- JS Implementing Plugins -->
  <script src="../assets/js/vendor.min.js"></script>

  <!-- JS Front -->
  <script src="../assets/js/theme.min.js"></script>

  <!-- JS Plugins Init. -->
  <script>
    (function() {
      // INITIALIZATION OF HEADER
      // =======================================================
      new HSHeader('#header').init()


      // INITIALIZATION OF NAV SCROLLER
      // =======================================================
      new HsNavScroller('.js-nav-scroller', {
        delay: 400,
        offset: 140
      })


      // INITIALIZATION OF LISTJS COMPONENT
      // =======================================================
      HSCore.components.HSList.init('#docsSearch');
      const docsSearch = HSCore.components.HSList.getItem('docsSearch');


      // GET JSON FILE RESULTS
      // =======================================================
      fetch('../assets/json/docs-search.json')
      .then(response => response.json())
      .then(data => {
        docsSearch.add(data)
      })


      // INITIALIZATION OF GO TO
      // =======================================================
      new HSGoTo('.js-go-to')
    })()
  </script>
</body>

<!-- Mirrored from htmlstream.com/front-dashboard/documentation/position.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 30 Aug 2022 07:13:09 GMT -->
</html>