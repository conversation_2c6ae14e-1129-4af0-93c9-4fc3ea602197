package com.example


/**
* Homeflow Technologies | Invoice.
*
* @property id
* @property date_from
* @property date_to
* @property amount_total
* @property amount_paid
* @property discount
* @property vat
* @property description
* @property created_by
* @property updated_by
* @property status_id
* @property approved_by
*
* @constructor Create Invoice model
*/
data class Invoice( var date_from: String? = null, var date_to: String? = null, var amount_total: Double? = null, var amount_paid: Double? = null, var discount: Double? = null, var vat: Double? = null, var description: String? = null, var created_by: Int? = null, var updated_by: Int? = null, var status_id: Int? = null, var approved_by: Int? = null,
)
