<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\UnitController;
use App\Http\Controllers\Api\SaleController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\RoleController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\StockController;
use App\Http\Controllers\Api\BranchController;
use App\Http\Controllers\Api\StatusController;
use App\Http\Controllers\Api\InvoiceController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\CustomerController;
use App\Http\Controllers\Api\SupplierController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\UserUsersController;
use App\Http\Controllers\Api\UserUnitsController;
use App\Http\Controllers\Api\UnitSalesController;
use App\Http\Controllers\Api\UserStocksController;
use App\Http\Controllers\Api\UserOrdersController;
use App\Http\Controllers\Api\PermissionController;
use App\Http\Controllers\Api\BranchUsersController;
use App\Http\Controllers\Api\StatusUsersController;
use App\Http\Controllers\Api\StatusUnitsController;
use App\Http\Controllers\Api\StockStocksController;
use App\Http\Controllers\Api\BusinessTypeController;
use App\Http\Controllers\Api\UserProductsController;
use App\Http\Controllers\Api\UserStatusesController;
use App\Http\Controllers\Api\UserBranchesController;
use App\Http\Controllers\Api\UserInvoicesController;
use App\Http\Controllers\Api\UnitProductsController;
use App\Http\Controllers\Api\StatusStocksController;
use App\Http\Controllers\Api\StatusOrdersController;
use App\Http\Controllers\Api\ProductSalesController;
use App\Http\Controllers\Api\UserSuppliersController;
use App\Http\Controllers\Api\UserCustomersController;
use App\Http\Controllers\Api\ProductStocksController;
use App\Http\Controllers\Api\UserCategoriesController;
use App\Http\Controllers\Api\StatusProductsController;
use App\Http\Controllers\Api\StatusBranchesController;
use App\Http\Controllers\Api\StatusInvoicesController;
use App\Http\Controllers\Api\SupplierStocksController;
use App\Http\Controllers\Api\StatusSuppliersController;
use App\Http\Controllers\Api\StatusCustomersController;
use App\Http\Controllers\Api\StatusCategoriesController;
use App\Http\Controllers\Api\CategoryProductsController;
use App\Http\Controllers\Api\BusinessTypeUsersController;
use App\Http\Controllers\Api\UserBusinessTypesController;
use App\Http\Controllers\Api\StatusBusinessTypesController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::post('/login', [AuthController::class, 'login'])->name('api.login');

Route::middleware('auth:sanctum')
    ->get('/user', function (Request $request) {
        return $request->user();
    })
    ->name('api.user');

Route::name('api.')
    ->middleware('auth:sanctum')
    ->group(function () {
        Route::apiResource('roles', RoleController::class);
        Route::apiResource('permissions', PermissionController::class);

        Route::apiResource('branches', BranchController::class);

        // Branch Users
        Route::get('/branches/{branch}/users', [
            BranchUsersController::class,
            'index',
        ])->name('branches.users.index');
        Route::post('/branches/{branch}/users', [
            BranchUsersController::class,
            'store',
        ])->name('branches.users.store');

        Route::apiResource('business-types', BusinessTypeController::class);

        // BusinessType Users
        Route::get('/business-types/{businessType}/users', [
            BusinessTypeUsersController::class,
            'index',
        ])->name('business-types.users.index');
        Route::post('/business-types/{businessType}/users/{user}', [
            BusinessTypeUsersController::class,
            'store',
        ])->name('business-types.users.store');
        Route::delete('/business-types/{businessType}/users/{user}', [
            BusinessTypeUsersController::class,
            'destroy',
        ])->name('business-types.users.destroy');

        Route::apiResource('customers', CustomerController::class);

        Route::apiResource('invoices', InvoiceController::class);

        Route::apiResource('users', UserController::class);

        // User Products
        Route::get('/users/{user}/products', [
            UserProductsController::class,
            'index',
        ])->name('users.products.index');
        Route::post('/users/{user}/products', [
            UserProductsController::class,
            'store',
        ])->name('users.products.store');

        // User Products2
        Route::get('/users/{user}/products', [
            UserProductsController::class,
            'index',
        ])->name('users.products.index');
        Route::post('/users/{user}/products', [
            UserProductsController::class,
            'store',
        ])->name('users.products.store');

        // User Statuses
        Route::get('/users/{user}/statuses', [
            UserStatusesController::class,
            'index',
        ])->name('users.statuses.index');
        Route::post('/users/{user}/statuses', [
            UserStatusesController::class,
            'store',
        ])->name('users.statuses.store');

        // User Statuses2
        Route::get('/users/{user}/statuses', [
            UserStatusesController::class,
            'index',
        ])->name('users.statuses.index');
        Route::post('/users/{user}/statuses', [
            UserStatusesController::class,
            'store',
        ])->name('users.statuses.store');

        // User Business Types2
        Route::get('/users/{user}/business-types', [
            UserBusinessTypesController::class,
            'index',
        ])->name('users.business-types.index');
        Route::post('/users/{user}/business-types', [
            UserBusinessTypesController::class,
            'store',
        ])->name('users.business-types.store');

        // User Business Types3
        Route::get('/users/{user}/business-types', [
            UserBusinessTypesController::class,
            'index',
        ])->name('users.business-types.index');

        // User Users
        Route::get('/users/{user}/users', [
            UserUsersController::class,
            'index',
        ])->name('users.users.index');
        Route::post('/users/{user}/users', [
            UserUsersController::class,
            'store',
        ])->name('users.users.store');

        // User Users2
        Route::get('/users/{user}/users', [
            UserUsersController::class,
            'index',
        ])->name('users.users.index');
        Route::post('/users/{user}/users', [
            UserUsersController::class,
            'store',
        ])->name('users.users.store');

        // User Stocks
        Route::get('/users/{user}/stocks', [
            UserStocksController::class,
            'index',
        ])->name('users.stocks.index');
        Route::post('/users/{user}/stocks', [
            UserStocksController::class,
            'store',
        ])->name('users.stocks.store');

        // User Stocks2
        Route::get('/users/{user}/stocks', [
            UserStocksController::class,
            'index',
        ])->name('users.stocks.index');
        Route::post('/users/{user}/stocks', [
            UserStocksController::class,
            'store',
        ])->name('users.stocks.store');

        // User Suppliers
        Route::get('/users/{user}/suppliers', [
            UserSuppliersController::class,
            'index',
        ])->name('users.suppliers.index');
        Route::post('/users/{user}/suppliers', [
            UserSuppliersController::class,
            'store',
        ])->name('users.suppliers.store');

        // User Suppliers2
        Route::get('/users/{user}/suppliers', [
            UserSuppliersController::class,
            'index',
        ])->name('users.suppliers.index');
        Route::post('/users/{user}/suppliers', [
            UserSuppliersController::class,
            'store',
        ])->name('users.suppliers.store');

        // User Branches
        Route::get('/users/{user}/branches', [
            UserBranchesController::class,
            'index',
        ])->name('users.branches.index');
        Route::post('/users/{user}/branches', [
            UserBranchesController::class,
            'store',
        ])->name('users.branches.store');

        // User Branches2
        Route::get('/users/{user}/branches', [
            UserBranchesController::class,
            'index',
        ])->name('users.branches.index');
        Route::post('/users/{user}/branches', [
            UserBranchesController::class,
            'store',
        ])->name('users.branches.store');

        // User Units
        Route::get('/users/{user}/units', [
            UserUnitsController::class,
            'index',
        ])->name('users.units.index');
        Route::post('/users/{user}/units', [
            UserUnitsController::class,
            'store',
        ])->name('users.units.store');

        // User Units2
        Route::get('/users/{user}/units', [
            UserUnitsController::class,
            'index',
        ])->name('users.units.index');
        Route::post('/users/{user}/units', [
            UserUnitsController::class,
            'store',
        ])->name('users.units.store');

        // User Invoices
        Route::get('/users/{user}/invoices', [
            UserInvoicesController::class,
            'index',
        ])->name('users.invoices.index');
        Route::post('/users/{user}/invoices', [
            UserInvoicesController::class,
            'store',
        ])->name('users.invoices.store');

        // User Invoices2
        Route::get('/users/{user}/invoices', [
            UserInvoicesController::class,
            'index',
        ])->name('users.invoices.index');
        Route::post('/users/{user}/invoices', [
            UserInvoicesController::class,
            'store',
        ])->name('users.invoices.store');

        // User Customers
        Route::get('/users/{user}/customers', [
            UserCustomersController::class,
            'index',
        ])->name('users.customers.index');
        Route::post('/users/{user}/customers', [
            UserCustomersController::class,
            'store',
        ])->name('users.customers.store');

        // User Customers2
        Route::get('/users/{user}/customers', [
            UserCustomersController::class,
            'index',
        ])->name('users.customers.index');
        Route::post('/users/{user}/customers', [
            UserCustomersController::class,
            'store',
        ])->name('users.customers.store');

        // User Orders
        Route::get('/users/{user}/orders', [
            UserOrdersController::class,
            'index',
        ])->name('users.orders.index');
        Route::post('/users/{user}/orders', [
            UserOrdersController::class,
            'store',
        ])->name('users.orders.store');

        // User Invoices3
        Route::get('/users/{user}/invoices', [
            UserInvoicesController::class,
            'index',
        ])->name('users.invoices.index');
        Route::post('/users/{user}/invoices', [
            UserInvoicesController::class,
            'store',
        ])->name('users.invoices.store');

        // User Stocks3
        Route::get('/users/{user}/stocks', [
            UserStocksController::class,
            'index',
        ])->name('users.stocks.index');
        Route::post('/users/{user}/stocks', [
            UserStocksController::class,
            'store',
        ])->name('users.stocks.store');

        // User Orders2
        Route::get('/users/{user}/orders', [
            UserOrdersController::class,
            'index',
        ])->name('users.orders.index');
        Route::post('/users/{user}/orders', [
            UserOrdersController::class,
            'store',
        ])->name('users.orders.store');

        // User Orders3
        Route::get('/users/{user}/orders', [
            UserOrdersController::class,
            'index',
        ])->name('users.orders.index');
        Route::post('/users/{user}/orders', [
            UserOrdersController::class,
            'store',
        ])->name('users.orders.store');

        // User Categories
        Route::get('/users/{user}/categories', [
            UserCategoriesController::class,
            'index',
        ])->name('users.categories.index');
        Route::post('/users/{user}/categories', [
            UserCategoriesController::class,
            'store',
        ])->name('users.categories.store');

        // User Categories2
        Route::get('/users/{user}/categories', [
            UserCategoriesController::class,
            'index',
        ])->name('users.categories.index');
        Route::post('/users/{user}/categories', [
            UserCategoriesController::class,
            'store',
        ])->name('users.categories.store');

        // User Business Types
        Route::get('/users/{user}/business-types', [
            UserBusinessTypesController::class,
            'index',
        ])->name('users.business-types.index');
        Route::post('/users/{user}/business-types/{businessType}', [
            UserBusinessTypesController::class,
            'store',
        ])->name('users.business-types.store');
        // Route::delete('/users/{user}/business-types/{businessType}', [
        //     UserBusinessTypesController::class,
        //     'destroy',
        // ])->name('users.business-types.destroy');

        Route::apiResource('units', UnitController::class);

        // Unit Sales
        Route::get('/units/{unit}/sales', [
            UnitSalesController::class,
            'index',
        ])->name('units.sales.index');
        Route::post('/units/{unit}/sales', [
            UnitSalesController::class,
            'store',
        ])->name('units.sales.store');

        // Unit Products
        Route::get('/units/{unit}/products', [
            UnitProductsController::class,
            'index',
        ])->name('units.products.index');
        Route::post('/units/{unit}/products', [
            UnitProductsController::class,
            'store',
        ])->name('units.products.store');

        Route::apiResource('orders', OrderController::class);

        // Order Management API Routes
        Route::prefix('order-management')->group(function () {
            Route::get('/products', [App\Http\Controllers\Api\OrderManagementController::class, 'getProducts']);
            Route::get('/product-by-barcode', [App\Http\Controllers\Api\OrderManagementController::class, 'getProductByBarcode']);
            Route::get('/suppliers', [App\Http\Controllers\Api\OrderManagementController::class, 'getSuppliers']);
            Route::get('/returnable-orders', [App\Http\Controllers\Api\OrderManagementController::class, 'getReturnableOrders']);
            Route::get('/returnable-items/{order}', [App\Http\Controllers\Api\OrderManagementController::class, 'getReturnableItems']);
            Route::post('/save-order', [App\Http\Controllers\Api\OrderManagementController::class, 'saveOrder']);
            Route::post('/calculate-totals', [App\Http\Controllers\Api\OrderManagementController::class, 'calculateTotals']);
        });

        Route::apiResource('sales', SaleController::class);

        Route::apiResource('statuses', StatusController::class);

        // Status Users
        Route::get('/statuses/{status}/users', [
            StatusUsersController::class,
            'index',
        ])->name('statuses.users.index');
        Route::post('/statuses/{status}/users', [
            StatusUsersController::class,
            'store',
        ])->name('statuses.users.store');

        // Status Business Types
        Route::get('/statuses/{status}/business-types', [
            StatusBusinessTypesController::class,
            'index',
        ])->name('statuses.business-types.index');
        Route::post('/statuses/{status}/business-types', [
            StatusBusinessTypesController::class,
            'store',
        ])->name('statuses.business-types.store');

        // Status Stocks
        Route::get('/statuses/{status}/stocks', [
            StatusStocksController::class,
            'index',
        ])->name('statuses.stocks.index');
        Route::post('/statuses/{status}/stocks', [
            StatusStocksController::class,
            'store',
        ])->name('statuses.stocks.store');

        // Status Products
        Route::get('/statuses/{status}/products', [
            StatusProductsController::class,
            'index',
        ])->name('statuses.products.index');
        Route::post('/statuses/{status}/products', [
            StatusProductsController::class,
            'store',
        ])->name('statuses.products.store');

        // Status Suppliers
        Route::get('/statuses/{status}/suppliers', [
            StatusSuppliersController::class,
            'index',
        ])->name('statuses.suppliers.index');
        Route::post('/statuses/{status}/suppliers', [
            StatusSuppliersController::class,
            'store',
        ])->name('statuses.suppliers.store');

        // Status Branches
        Route::get('/statuses/{status}/branches', [
            StatusBranchesController::class,
            'index',
        ])->name('statuses.branches.index');
        Route::post('/statuses/{status}/branches', [
            StatusBranchesController::class,
            'store',
        ])->name('statuses.branches.store');

        // Status Units
        Route::get('/statuses/{status}/units', [
            StatusUnitsController::class,
            'index',
        ])->name('statuses.units.index');
        Route::post('/statuses/{status}/units', [
            StatusUnitsController::class,
            'store',
        ])->name('statuses.units.store');

        // Status Invoices
        Route::get('/statuses/{status}/invoices', [
            StatusInvoicesController::class,
            'index',
        ])->name('statuses.invoices.index');
        Route::post('/statuses/{status}/invoices', [
            StatusInvoicesController::class,
            'store',
        ])->name('statuses.invoices.store');

        // Status Customers
        Route::get('/statuses/{status}/customers', [
            StatusCustomersController::class,
            'index',
        ])->name('statuses.customers.index');
        Route::post('/statuses/{status}/customers', [
            StatusCustomersController::class,
            'store',
        ])->name('statuses.customers.store');

        // Status Orders
        Route::get('/statuses/{status}/orders', [
            StatusOrdersController::class,
            'index',
        ])->name('statuses.orders.index');
        Route::post('/statuses/{status}/orders', [
            StatusOrdersController::class,
            'store',
        ])->name('statuses.orders.store');

        // Status Categories
        Route::get('/statuses/{status}/categories', [
            StatusCategoriesController::class,
            'index',
        ])->name('statuses.categories.index');
        Route::post('/statuses/{status}/categories', [
            StatusCategoriesController::class,
            'store',
        ])->name('statuses.categories.store');

        Route::apiResource('suppliers', SupplierController::class);

        // Supplier Stocks
        Route::get('/suppliers/{supplier}/stocks', [
            SupplierStocksController::class,
            'index',
        ])->name('suppliers.stocks.index');
        Route::post('/suppliers/{supplier}/stocks', [
            SupplierStocksController::class,
            'store',
        ])->name('suppliers.stocks.store');

        Route::apiResource('stocks', StockController::class);

        // Stock Stocks
        Route::get('/stocks/{stock}/stocks', [
            StockStocksController::class,
            'index',
        ])->name('stocks.stocks.index');
        Route::post('/stocks/{stock}/stocks', [
            StockStocksController::class,
            'store',
        ])->name('stocks.stocks.store');

        Route::apiResource('categories', CategoryController::class);

        // Category Products
        Route::get('/categories/{category}/products', [
            CategoryProductsController::class,
            'index',
        ])->name('categories.products.index');
        Route::post('/categories/{category}/products', [
            CategoryProductsController::class,
            'store',
        ])->name('categories.products.store');

        Route::apiResource('products', ProductController::class);

        // Product Stocks
        Route::get('/products/{product}/stocks', [
            ProductStocksController::class,
            'index',
        ])->name('products.stocks.index');
        Route::post('/products/{product}/stocks', [
            ProductStocksController::class,
            'store',
        ])->name('products.stocks.store');

        // Product Sales
        Route::get('/products/{product}/sales', [
            ProductSalesController::class,
            'index',
        ])->name('products.sales.index');
        Route::post('/products/{product}/sales', [
            ProductSalesController::class,
            'store',
        ])->name('products.sales.store');
    });
