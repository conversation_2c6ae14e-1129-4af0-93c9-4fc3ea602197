@php
    $editing = isset($invoice);
@endphp

<div class="container-fluid">
    <div class="card shadow-sm mb-5">
        <div class="card-body" id="invoice">
            <div class="row g-4">
                <!-- Barcode Scanner -->
                <div class="col-12">
                    <input
                        type="text"
                        v-model="barcode"
                        @input="getProduct"
                        placeholder="Scan Barcode..."
                        class="form-control form-control-lg text-center"
                        id="input-scan"
                        autocomplete="off"
                    >
                </div>

                <!-- Product and Customer Selection -->
                <div class="col-md-6">
                    <h3 class="mb-3">Add Product</h3>
                    <div class="position-relative">
                        <input
                            type="text"
                            v-model="search"
                            @input="debouncedGetProducts"
                            @focus="showDropdown = true"
                            @blur="hideDropdownDelayed"
                            @keydown.down="navigateDown"
                            @keydown.up="navigateUp"
                            @keydown.enter="selectHighlighted"
                            @keydown.escape="hideDropdown"
                            placeholder="Search for products..."
                            class="form-control form-control-lg fs-4"
                            autocomplete="off"
                        >
                        <div
                            v-if="showDropdown && (products.length || isSearching)"
                            class="dropdown-menu show position-absolute w-100"
                            style="max-height: 300px; overflow-y: auto; z-index: 1050; top: 100%;"
                        >
                            <div v-if="isSearching" class="dropdown-item-text text-center py-3">
                                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                Searching products...
                            </div>
                            <div v-else-if="!products.length && search.length >= 2" class="dropdown-item-text text-center py-3 text-muted">
                                <i class="bi bi-search me-2"></i>
                                No products found for @{{ search }}
                            </div>
                            <a
                                v-for="(product, index) in products"
                                :key="product.id"
                                @mousedown="addItem(product)"
                                @mouseenter="highlightedIndex = index"
                                :class="['dropdown-item', { 'active': highlightedIndex === index }]"
                                style="cursor: pointer;"
                            >
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <strong v-text="product.name"></strong>
                                        <br>
                                        <small class="text-muted" v-text="product.description"></small>
                                        <br>
                                        <small class="badge bg-info text-white" v-if="product.barcode" v-text="product.barcode"></small>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-success fw-bold" v-text="formatCurrency(product.selling_price)"></small>
                                        <br>
                                        <small class="badge bg-primary text-white" v-text="product.total_stock + ' in stock'"></small>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <h3 class="mb-3">Bill To</h3>
                    <select
                        class="form-select form-select-lg fs-4"
                        v-model="invoice.customer_id"
                        name="customer_id"
                        required
                        data-hs-tom-select-options='{
                            "create": true,
                            "placeholder": "Select or create customer..."
                        }'
                    >
                        @foreach ($customers as $customer)
                            <option
                                value="{{ $customer->id }}"
                                {{ $editing && $invoice->customer_id == $customer->id ? 'selected' : '' }}
                            >
                                {{ $customer->name ?? 'Unnamed Customer' }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>

            <!-- Invoice Table -->
            <div class="table-responsive mt-4">
                <table class="table table-hover table-striped align-middle">
                    <thead class="table-light">
                        <tr>
                            <th>Item</th>
                            <th>Unit</th>
                            <th>Quantity</th>
                            <th>Discount</th>
                            <th>Selling Price</th>
                            <th>Buying Price</th>
                            <th class="text-end">Amount</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(sale, index) in invoice.sales" :key="index">
                            <td>
                                <strong class="d-block fs-5" v-text="sale.product.name"></strong>
                                <small v-text="sale.product.description"></small>
                                <small class="d-block text-info" v-text="'Available Stock: ' + sale.max"></small>
                            </td>
                            <td>
                                <select
                                    v-model="sale.unit_id"
                                    @change="setSaleMax(index)"
                                    name="units[]"
                                    class="form-select"
                                    required
                                >
                                    <option value="" disabled>Select unit</option>
                                    <option
                                        v-for="unit in sale.units"
                                        :key="unit.id"
                                        :value="unit.id"
                                        v-text="unit.name"
                                    ></option>
                                </select>
                            </td>
                            <td>
                                <input
                                    type="number"
                                    v-model.number="sale.quantity"
                                    @change="calculate"
                                    name="quantities[]"
                                    min="0.01"
                                    step="0.01"
                                    :max="sale.max || 0"
                                    class="form-control"
                                    required
                                >
                            </td>
                            <td>
                                <input
                                    type="number"
                                    v-model.number="sale.discount"
                                    @change="calculate"
                                    name="discounts[]"
                                    min="0"
                                    step="0.01"
                                    :max="sale.amount || 0"
                                    class="form-control"
                                    required
                                >
                            </td>
                            <td v-text="formatNumber(sale.selling_price)"></td>
                            <td v-text="formatNumber(sale.buying_price)"></td>
                            <td class="text-end" v-text="formatNumber(sale.amount)"></td>
                            <td>
                                <button
                                    @click="removeItem(index)"
                                    class="btn btn-outline-danger btn-sm"
                                >
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Summary -->
            <hr class="my-4">
            <div class="row justify-content-end">
                <div class="col-md-6 col-lg-5">
                    <dl class="row text-end fs-5">
                        <dt class="col-6">Subtotal:</dt>
                        <dd class="col-6" v-text="formatNumber(invoice.sub_total)"></dd>

                        <dt class="col-6">VAT:</dt>
                        <dd class="col-6" v-text="formatNumber(invoice.vat)"></dd>

                        <dt class="col-6">Discount:</dt>
                        <dd class="col-6" v-text="formatNumber(invoice.discount)"></dd>

                        <dt class="col-6">Total:</dt>
                        <dd class="col-6" v-text="formatNumber(invoice.amount_total)"></dd>

                        <dt class="col-6">Amount Paid:</dt>
                        <dd class="col-6">
                            <input
                                type="number"
                                v-model.number="invoice.amount_paid"
                                @change="calculate"
                                :max="invoice.amount_total"
                                step="0.01"
                                name="amount_paid"
                                class="form-control text-danger fw-bold"
                            >
                        </dd>
                    </dl>
                </div>
            </div>
            <!-- Footer Actions -->
            <div class="d-flex justify-content-end gap-3 mt-4 d-print-none">
                <button type="button"
                    v-if="!isReady"
                    @click="getReady"
                    class="btn btn-primary"
                >
                    <i class="bi bi-calculator me-1"></i> Calculate
                </button>

                <!-- New buttons after calculate -->
                <div v-if="isReady" class="d-flex gap-2 flex-wrap">
                    <button
                        type="submit"
                        class="btn btn-success"
                        name="action"
                        value="save_as_done"
                    >
                        <i class="bi bi-check-circle me-1"></i> Save as Done
                    </button>
                    <button
                        type="submit"
                        class="btn btn-info"
                        name="action"
                        value="save_as_quotation"
                    >
                        <i class="bi bi-file-earmark-text me-1"></i> Save as Quotation
                    </button>
                    <button
                        type="submit"
                        class="btn btn-warning"
                        name="action"
                        value="save_as_shipment"
                    >
                        <i class="bi bi-truck me-1"></i> Save as Shipment
                    </button>
                    <button
                        v-if="invoice.discount <= 0"
                        type="submit"
                        name="direct_printing"
                        value="true"
                        class="btn btn-primary"
                    >
                        <i class="bi bi-printer me-1"></i> Save and Print
                    </button>
                    <!-- <button
                        type="submit"
                        class="btn btn-outline-primary"
                        name="action"
                        value="save_as_draft"
                    >
                        <i class="bi bi-save me-1"></i> Save as Draft
                    </button> -->
                </div>
            </div>

            <!-- Hidden Invoice Data -->
            <textarea
                name="invoice"
                v-model="serializedInvoice"
                style="display: none;"
            ></textarea>

        </div>
    </div>
</div>

@push('styles')
<style>
/* Enhanced product search dropdown */
.dropdown-menu.show {
    border: 1px solid #dee2e6;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.375rem;
    background-color: #ffffff !important;
}

/* Fix dropdown item text visibility */
.dropdown-item {
    color: #212529 !important;
    background-color: transparent;
}

.dropdown-item:hover,
.dropdown-item.active {
    background-color: #0d6efd !important;
    color: white !important;
}

.dropdown-item:hover .text-muted,
.dropdown-item.active .text-muted {
    color: rgba(255, 255, 255, 0.75) !important;
}

.dropdown-item:hover .badge,
.dropdown-item.active .badge {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

/* Ensure text is visible in default state */
.dropdown-item .text-muted {
    color: #6c757d !important;
}

.dropdown-item strong {
    color: #212529 !important;
}

.dropdown-item:hover strong,
.dropdown-item.active strong {
    color: white !important;
}

/* Loading and empty states */
.dropdown-item-text {
    color: #6c757d !important;
}

/* Additional text color fixes for product search */
.dropdown-item .text-success {
    color: #198754 !important;
}

.dropdown-item:hover .text-success,
.dropdown-item.active .text-success {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Ensure all text elements are visible */
.dropdown-item * {
    color: inherit;
}

.dropdown-item small {
    color: #6c757d !important;
}

.dropdown-item:hover small,
.dropdown-item.active small {
    color: rgba(255, 255, 255, 0.75) !important;
}

/* Force text visibility on all child elements */
.dropdown-menu .dropdown-item {
    color: #212529 !important;
}

.dropdown-menu .dropdown-item:hover,
.dropdown-menu .dropdown-item.active {
    color: #ffffff !important;
}

.dropdown-menu .dropdown-item:hover *,
.dropdown-menu .dropdown-item.active * {
    color: inherit !important;
}

/* Spinner styling */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Better badge visibility */
.badge.bg-info {
    background-color: #0dcaf0 !important;
}

.badge.bg-primary {
    background-color: #0d6efd !important;
}

.badge.bg-success {
    background-color: #198754 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}
</style>
@endpush

@push('scripts')
<script>
new Vue({
    el: "#invoice",
    data() {
        return {
            isReady: false,
            barcode: "",
            search: "",
            invoice: {
                sales: [],
                sub_total: 0,
                amount_total: 0,
                amount_paid: 0,
                discount: 0,
                vat: 0,
                customer_id: {{ $editing ? $invoice->customer_id : 1 }}
            },
            products: [],
            vat: @json(auth()->user()->getGlobal('vat') ?? 0),
            showDropdown: false,
            isSearching: false,
            highlightedIndex: -1,
            searchTimeout: null
        };
    },
    computed: {
        serializedInvoice() {
            return JSON.stringify(this.invoice);
        }
    },
    methods: {
        async getProduct() {
            if (!this.barcode) return;
            try {
                const res = await axios.get(`/ajax-product-by-barcode?barcode=${this.barcode}`);
                if (res.data) {
                    this.addItem(res.data);
                    this.barcode = "";
                }
            } catch (error) {
                console.error("Error fetching product by barcode:", error);
            }
        },
        debouncedGetProducts() {
            // Clear existing timeout
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }

            // Set new timeout for debounced search
            this.searchTimeout = setTimeout(() => {
                this.getProducts();
            }, 300); // 300ms delay
        },
        async getProducts() {
            if (!this.search || this.search.length < 2) {
                this.products = [];
                this.showDropdown = false;
                this.isSearching = false;
                return;
            }

            this.isSearching = true;
            this.showDropdown = true;
            this.highlightedIndex = -1;

            try {
                const res = await axios.get(`/ajax-product-search?search=${encodeURIComponent(this.search)}`);
                this.products = res.data.data || [];
                this.highlightedIndex = this.products.length > 0 ? 0 : -1;
            } catch (error) {
                console.error("Error searching products:", error);
                this.products = [];
            } finally {
                this.isSearching = false;
            }
        },
        // Keyboard navigation methods
        navigateDown() {
            if (this.products.length === 0) return;
            this.highlightedIndex = Math.min(this.highlightedIndex + 1, this.products.length - 1);
        },
        navigateUp() {
            if (this.products.length === 0) return;
            this.highlightedIndex = Math.max(this.highlightedIndex - 1, 0);
        },
        selectHighlighted() {
            if (this.highlightedIndex >= 0 && this.products[this.highlightedIndex]) {
                this.addItem(this.products[this.highlightedIndex]);
            }
        },
        hideDropdown() {
            this.showDropdown = false;
            this.highlightedIndex = -1;
        },
        hideDropdownDelayed() {
            // Delay hiding to allow click events to fire
            setTimeout(() => {
                this.hideDropdown();
            }, 150);
        },
        formatCurrency(amount) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(amount || 0);
        },
        async addItem(product) {
            if (this.invoice.sales.some(item => item.product_id === product.id)) {
                this.hideDropdown();
                this.search = "";
                this.products = [];
                alert("Product already added!");
                return;
            }
            const sale = {
                product,
                product_id: product.id,
                quantity: 1,
                selling_price: product.selling_price || 0,
                buying_price: product.buying_price || 0,
                discount: product.discount || 0,
                unit_id: product.unit_id || null,
                units: product.units || [],
                max: product.total_stock || 0,
                vat: product.vat_applied ? this.vat : 0,
                amount: 0
            };
            this.invoice.sales.push(sale);
            this.search = "";
            this.products = [];
            this.hideDropdown();
            if (sale.unit_id) {
                const index = this.invoice.sales.length - 1;
                await this.setSaleMax(index);
            }
            this.calculate();
            this.$nextTick(() => document.getElementById('input-scan').focus());
        },
        async setSaleMax(index) {
            const sale = this.invoice.sales[index];
            if (!sale.unit_id) return;
            try {
                const res = await axios.get(`/ajax-product/${sale.product_id}`);
                const product = res.data;
                const unit = product.units.find(u => u.id === sale.unit_id);
                if (unit) {
                    sale.max = product.total_stock / unit.quantity;
                    sale.buying_price = unit.buying_price || 0;
                    sale.selling_price = unit.selling_price || 0;
                }
                this.calculate();
            } catch (error) {
                console.error("Error fetching product units:", error);
            }
        },

        removeItem(index) {
            this.invoice.sales.splice(index, 1);
            this.calculate();
        },

        calculate() {
            this.isReady = false;
        },

        getReady() {
            this.invoice.sub_total = 0;
            this.invoice.vat = 0;
            this.invoice.discount = 0;

            for (const sale of this.invoice.sales) {
                sale.amount = Number((sale.quantity * sale.selling_price).toFixed(2));
                this.invoice.sub_total += sale.amount;
                if (sale.vat > 0) {
                    this.invoice.vat += Number((sale.amount * sale.vat / 100).toFixed(2));
                }
                this.invoice.discount += Number(sale.discount || 0);
            }

            this.invoice.amount_total = Number((this.invoice.sub_total + this.invoice.vat - this.invoice.discount).toFixed(2));
            this.invoice.sub_total = Number(this.invoice.sub_total.toFixed(2));
            this.invoice.vat = Number(this.invoice.vat.toFixed(2));
            this.invoice.discount = Number(this.invoice.discount.toFixed(2));
            this.invoice.amount_paid = this.invoice.amount_paid;
            this.isReady = true;
        },
        
        formatNumber(num) {
            if (!num || isNaN(num)) return "0.00";
            return Number(num).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,");
        }
    },
    created() {
        const invoice = @json($invoice ?? null);
        if (invoice) {
            this.invoice = {
                ...invoice,
                sales: invoice.sales.map(sale => ({
                    ...sale,
                    max: sale.max || 0,
                    amount: sale.amount || 0
                }))
            };
            this.invoice.sales.forEach((_, index) => this.setSaleMax(index));
            this.getReady();
        }
    }
});

// Prevent form submission on Enter key
document.querySelector('form').addEventListener('keydown', (event) => {
    if (event.key === 'Enter') event.preventDefault();
});
</script>
@endpush