@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <x-page-header>
        <x-slot name="title">Canceled Orders</x-slot>
        <x-slot name="controls">

        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="searchbar mt-4 mb-5">
        <form class="d-flex">
            <input type="hidden" name="from" value="{{ request()->from }}">
            <input type="hidden" name="to" value="{{ request()->to }}">
            
            <button id="js-daterangepicker-predefined" type="button" class="btn btn-white me-2">
              <i class="bi-calendar-week me-1"></i>
              <span class="js-daterangepicker-predefined-preview"></span>
            </button>

            <div class="input-group-append">
                <button type="submit" class="btn btn-white">
                    Search
                </button>
            </div>

        </form>
    </div>



    <!-- Table -->
    <div class="card card-table">
        <div class="table-responsive mt-3 mb-3">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">

                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            Order ID
                        </th>                                                  
                        <th class="text-left">
                            Operator
                        </th>                 
                        <th class="text-left">
                            Total Amount
                        </th>
                        <th class="text-left">
                            Paid
                        </th>
                        <th class="text-left">
                            Balance
                        </th>
                        <th class="text-left">
                            Discount
                        </th>                            
                        <th class="text-left">
                            Description
                        </th>                            

                        <th class="text-left">
                            Cancelled By
                        </th>
                        <th class="text-left">
                            Date
                        </th>
                        <th class="text-center">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($orders as $order)
                    <tr class=" @if( in_array($order->status_id, [13,14]) ) bg-soft-danger @endif ">
                        <td>{{ $order->order_id ?? '-' }}</td>
                        <td>{{ $order->createdBy->name ?? '-' }}</td>
                        <td>{{ _money(  $order->amount_total ) ?? '-' }}</td>
                        <td>{{ _money( $order->amount_paid ) ?? '-' }}</td>
                        <td>{{ _money( $order->amount_total - $order->amount_paid ) ?? '-' }}</td>
                        <td>{{ $order->discount ?? '-' }}</td>
                        <td>{{ $order->description ?? '-' }}</td>
                        <td>
                            {{ optional($order->approvedBy)->name ?? '' }}
                        </td>
                        <td>{{ $order->created_at ?? '-' }}</td>

                        <td class="text-center" style="width: 134px;">
                            <div role="group" aria-label="Row Actions" class="btn-group">

                                @can('view', $order)
                                <button type="button" class="btn btn-soft-primary m-1 preview-order-btn" data-bs-toggle="modal" class="preview-order-btn" data-id="{{ $order->id }}" data-bs-target=".preview-order-modal">
                                    View
                                </button>
                                @endcan 

                                @if( ! $order->approved_by )
                                @can('update', $order)
                                <a
                                    href="{{ route('orders.edit', $order) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        <!-- <i class="icon ion-md-create"></i> --> edit
                                    </button>
                                </a>
                                @endcan 


                                @can('delete', $order)
                                <form
                                    action="{{ route('orders.destroy', $order) }}"
                                    method="POST"
                                    onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                >
                                    @csrf @method('DELETE')
                                    <button
                                        type="submit"
                                        class="btn btn-light text-danger m-1"
                                    >
                                        <!-- <i class="icon ion-md-trash"></i> --> del
                                    </button>
                                </form>
                                @endcan
                                @endif

                            </div>
                        </td>
                    </tr>
                    @empty
                    @endforelse
                </tbody>

                <thead>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                </thead>

            </table>

        </div>
    </div>



</div>
@endsection





@push('scripts')
<script>
  $("document").ready(function () {
        dataTableBtn()
        // dataTableBtn(null, {order: [[0, 'desc']]})
  });
</script>

@endpush