<?php

namespace App\Http\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Account;
use App\Models\AccountCategory;

class AccountCategoryAccountsDetail extends Component
{
    use WithPagination;

    public AccountCategory $accountCategory;
    public $accountName;
    public $accountCode;
    public $accountDescription;
    public $accountIsActive = true;
    public $accountAllowsManualEntries = true;
    public $accountNormalBalance = 'debit';

    public $showingModal = false;
    public $modalTitle = 'New Account';

    protected $rules = [
        'accountName' => ['required', 'max:255', 'string'],
        'accountCode' => ['required', 'max:255', 'string'],
        'accountDescription' => ['nullable', 'max:255', 'string'],
        'accountIsActive' => ['boolean'],
        'accountAllowsManualEntries' => ['boolean'],
        'accountNormalBalance' => ['required', 'in:debit,credit'],
    ];

    public function mount(AccountCategory $accountCategory)
    {
        $this->accountCategory = $accountCategory;
    }

    public function render()
    {
        $accounts = Account::where('account_category_id', $this->accountCategory->id)
            ->paginate(20);

        return view('livewire.account-category-accounts-detail', [
            'accounts' => $accounts,
        ]);
    }

    public function newAccount()
    {
        $this->modalTitle = trans('crud.accounts.create_title');
        $this->resetAccountData();

        $this->showingModal = true;
    }

    public function resetAccountData()
    {
        $this->accountName = '';
        $this->accountCode = '';
        $this->accountDescription = '';
        $this->accountIsActive = true;
        $this->accountAllowsManualEntries = true;
        $this->accountNormalBalance = 'debit';
    }

    public function createAccount()
    {
        $this->validate();

        $account = Account::create([
            'account_type_id' => $this->accountCategory->account_type_id,
            'account_category_id' => $this->accountCategory->id,
            'name' => $this->accountName,
            'code' => $this->accountCode,
            'description' => $this->accountDescription,
            'is_active' => $this->accountIsActive,
            'allows_manual_entries' => $this->accountAllowsManualEntries,
            'normal_balance' => $this->accountNormalBalance,
        ]);

        $this->showingModal = false;

        $this->emit('refreshParent');
        $this->resetAccountData();
    }
}
