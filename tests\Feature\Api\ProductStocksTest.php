<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Stock;
use App\Models\Product;

use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductStocksTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create(['email' => '<EMAIL>']);

        Sanctum::actingAs($user, [], 'web');

        $this->seed(\Database\Seeders\PermissionsSeeder::class);

        $this->withoutExceptionHandling();
    }

    /**
     * @test
     */
    public function it_gets_product_stocks()
    {
        $product = Product::factory()->create();
        $stocks = Stock::factory()
            ->count(2)
            ->create([
                'product_id' => $product->id,
            ]);

        $response = $this->getJson(
            route('api.products.stocks.index', $product)
        );

        $response->assertOk()->assertSee($stocks[0]->description);
    }

    /**
     * @test
     */
    public function it_stores_the_product_stocks()
    {
        $product = Product::factory()->create();
        $data = Stock::factory()
            ->make([
                'product_id' => $product->id,
            ])
            ->toArray();

        $response = $this->postJson(
            route('api.products.stocks.store', $product),
            $data
        );

        unset($data['created_by']);
        unset($data['updated_by']);
        unset($data['status_id']);

        $this->assertDatabaseHas('stocks', $data);

        $response->assertStatus(201)->assertJsonFragment($data);

        $stock = Stock::latest('id')->first();

        $this->assertEquals($product->id, $stock->product_id);
    }
}
