@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">building-gear</x-slot>
        <x-slot name="title">@lang('crud.business_types.edit_title')</x-slot>
        <x-slot name="subtitle">Update business type information and settings</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('business-types.index') }}">@lang('crud.business_types.name')</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('business-types.show', $businessType) }}" class="btn btn-outline-info me-2">
                <i class="bi bi-eye me-1"></i> @lang('crud.business_types.view_business_type')
            </a>
            <a href="{{ route('business-types.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> @lang('crud.business_types.back_to_business_types')
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <div class="col-lg-9 mb-3 mb-lg-0">
            <!-- Card -->
            <div class="card mb-3 mb-lg-5">
                <!-- Header -->
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-header-title">@lang('crud.business_types.business_type_details')</h4>
                        <span class="badge bg-primary">ID: {{ $businessType->id }}</span>
                    </div>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <x-form
                        method="PUT"
                        action="{{ route('business-types.update', $businessType) }}"
                        id="businessTypeForm"
                    >
                        @include('app.business_types.form-inputs')
                    </x-form>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>

        <div class="col-lg-3">
            <!-- Card -->
            <div class="card">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Actions</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <div class="d-flex justify-content-end">
                            <a href="{{ route('business-types.index') }}" class="btn btn-white me-2">Cancel</a>
                            <button type="submit" form="businessTypeForm" class="btn btn-primary">
                                <i class="bi-check-circle me-1"></i> @lang('crud.common.update')
                            </button>
                        </div>
                        <hr>
                        <div class="d-grid">
                            <a href="{{ route('business-types.create') }}" class="btn btn-outline-primary">
                                <i class="bi-plus-circle me-1"></i> @lang('crud.common.create') New
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->

            <!-- Card -->
            <div class="card mt-3">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Information</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <ul class="list-unstyled list-py-2 mb-0">
                        <li class="d-flex justify-content-between">
                            <span>Created:</span>
                            <span class="text-muted">{{ $businessType->created_at->format('M d, Y') }}</span>
                        </li>
                        <li class="d-flex justify-content-between">
                            <span>Updated:</span>
                            <span class="text-muted">{{ $businessType->updated_at->format('M d, Y') }}</span>
                        </li>
                        @if($businessType->createdBy)
                        <li class="d-flex justify-content-between">
                            <span>Created by:</span>
                            <span class="text-muted">{{ $businessType->createdBy->name }}</span>
                        </li>
                        @endif
                        @if($businessType->updatedBy)
                        <li class="d-flex justify-content-between">
                            <span>Updated by:</span>
                            <span class="text-muted">{{ $businessType->updatedBy->name }}</span>
                        </li>
                        @endif
                    </ul>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>
    </div>
</div>
@endsection
