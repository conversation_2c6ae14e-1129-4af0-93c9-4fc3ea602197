<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Stock;
use App\Models\StockItem;
use App\Models\Invoice;
use App\Models\Customer;
use Illuminate\Http\Request;
use App\Http\Requests\InvoiceStoreRequest;
use App\Http\Requests\InvoiceUpdateRequest;

use Facades\App\Libraries\InvoiceHandler;
use Facades\App\Cache\Repo;

class InvoiceController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Invoice::class);
        $paidInvoices = Repo::getInvoiceCursor(10)->latest()->limit(2000)->get();
        $pendingInvoices = Repo::getInvoiceCursor(11)->latest()->limit(2000)->get();
        $actionInvoices = Repo::getInvoiceCursor(12)->latest()->limit(2000)->get();
        $canceledInvoices = Repo::getInvoiceCursor(13)->latest()->limit(2000)->get();
        return view('app.invoices.index', compact('paidInvoices', 'actionInvoices', 'pendingInvoices', 'canceledInvoices'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Invoice::class);
        $products = Product::with("unit", "units")->get()->filter(fn($item) => $item->total_stock > 0 )->values();
        $customers = Customer::get();
        return view('app.invoices.create', compact('products', 'customers'));
    }

    /**
     * @param \App\Http\Requests\InvoiceStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(InvoiceStoreRequest $request)
    {
        $this->authorize('create', Invoice::class);

        if( ! auth()->user()->branch_id ) return redirect()->back()->with("error", "Please Select Branch first");

        $validated = $request->validated();
        $action = $request->input('action', 'save_as_draft');

        // Handle different action types
        switch ($action) {
            case 'save_as_quotation':
                return $this->saveAsQuotation($request);
            case 'save_as_shipment':
                return $this->saveAsShipment($request);
            case 'save_as_done':
                $invoice = InvoiceHandler::saveInvoice();
                if( _from($invoice, "error") ){
                    return redirect()->back()->with("error", _from($invoice, "error"));
                }
                // Mark as approved/done
                $invoice->update(['approved_by' => auth()->id(), 'status_id' => 12]);
                break;
            default: // save_as_draft
                $invoice = InvoiceHandler::saveInvoice();
                if( _from($invoice, "error") ){
                    return redirect()->back()->with("error", _from($invoice, "error"));
                }
                break;
        }

        if( request()->direct_printing || ! auth()->user()->can('view invoices') ) {
            return redirect()->back()->with("invoicable", $invoice)->withSuccess(__('crud.common.created'));
        }

        return redirect()
            ->route('invoices.show', $invoice)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Invoice $invoice
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Invoice $invoice)
    {

        $this->authorize('view', $invoice);
        $invoices = Invoice::latest()->paginate();
        return view('app.invoices.show', compact('invoice', 'invoices'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Invoice $invoice
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Invoice $invoice)
    {
        $this->authorize('update', $invoice);
        $products = Product::with("unit", "units")->get()->filter(fn($item) => $item->total_stock > 0 )->values();
        $customers = Customer::get();
        $invoice->sales;
        $invoice->payments;
        return view('app.invoices.edit', compact('invoice', 'products', 'customers'));
    }

    /**
     * @param \App\Http\Requests\InvoiceUpdateRequest $request
     * @param \App\Models\Invoice $invoice
     * @return \Illuminate\Http\Response
     */
    public function update(InvoiceUpdateRequest $request, Invoice $invoice)
    {
        $this->authorize('update', $invoice);

        $validated = $request->validated();

        if( ! auth()->user()->branch_id ) return redirect()->back()->with("error", "Please Select Branch first");

        $invoice = InvoiceHandler::updateInvoice($invoice);

        if( _from($invoice, "error") ){
            return redirect()->back()->with("error", _from($invoice, "error"));
        }

        return redirect()
            ->route('invoices.show', $invoice)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Invoice $invoice
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Invoice $invoice)
    {
        $this->authorize('delete', $invoice);

        $invoice->sales()->delete();
        $invoice->delete();

        return redirect()
            ->route('invoices.index')
            ->withSuccess(__('crud.common.removed'));
    }

    public function approve( Request $request, Invoice $invoice){
        InvoiceHandler::approveInvoice($invoice);
        return redirect()->back()->withSuccess("Approved successfully ");
    }

    public function havePaidInvoiceInFull( Request $request, Invoice $invoice){
        InvoiceHandler::havePaidInvoiceInFull($invoice);
        return redirect()->back()->withSuccess("Approved successfully.");
    }

    public function ajaxInvoice(Request $request, Invoice $invoice){
        $invoice->sales;
        $invoice->stockItems;
        $invoice->customer;
        return response($invoice);
    }

    /**
     * Save invoice data as quotation
     */
    private function saveAsQuotation(Request $request)
    {
        try {
            $invoiceData = json_decode($request->invoice, true);

            // Create quotation from invoice data
            $quotationData = [
                'customer_id' => $invoiceData['customer_id'],
                'customer_name' => $invoiceData['customer_name'] ?? '',
                'sub_total' => $invoiceData['sub_total'],
                'amount_total' => $invoiceData['amount_total'],
                'amount_paid' => 0, // Reset payment for quotation
                'vat' => $invoiceData['vat'],
                'discount' => $invoiceData['discount'],
                'description' => $invoiceData['description'] ?? '',
                'status_id' => 10, // Draft status for quotation
                'created_by' => auth()->id(),
                'branch_id' => auth()->user()->branch_id,
                'business_type_id' => auth()->user()->business_type_id,
            ];

            $quotation = \App\Models\Quotation::create($quotationData);

            // Copy sales items
            foreach ($invoiceData['sales'] as $saleData) {
                $quotation->sales()->create([
                    'product_id' => $saleData['product_id'],
                    'product_name' => $saleData['product']['name'] ?? '',
                    'unit_id' => $saleData['unit_id'],
                    'unit_name' => $saleData['unit_name'] ?? '',
                    'unit_quantity' => $saleData['unit_quantity'] ?? 1,
                    'quantity' => $saleData['quantity'],
                    'selling_price' => $saleData['selling_price'],
                    'buying_price' => $saleData['buying_price'],
                    'discount' => $saleData['discount'] ?? 0,
                    'vat' => $saleData['vat'] ?? 0,
                    'created_by' => auth()->id(),
                    'branch_id' => auth()->user()->branch_id,
                ]);
            }

            return redirect()
                ->route('quotations.show', $quotation)
                ->withSuccess('Invoice saved as quotation successfully.');

        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withError('Error saving as quotation: ' . $e->getMessage());
        }
    }

    /**
     * Save invoice data as shipment
     */
    private function saveAsShipment(Request $request)
    {
        try {
            $invoiceData = json_decode($request->invoice, true);

            // Create shipment from invoice data
            $shipmentData = [
                'customer_id' => $invoiceData['customer_id'],
                'customer_name' => $invoiceData['customer_name'] ?? '',
                'sub_total' => $invoiceData['sub_total'],
                'amount_total' => $invoiceData['amount_total'],
                'amount_paid' => 0, // Reset payment for shipment
                'vat' => $invoiceData['vat'],
                'discount' => $invoiceData['discount'],
                'description' => $invoiceData['description'] ?? '',
                'status_id' => 10, // Draft status for shipment
                'created_by' => auth()->id(),
                'branch_id' => auth()->user()->branch_id,
                'business_type_id' => auth()->user()->business_type_id,
            ];

            $shipment = \App\Models\Shipment::create($shipmentData);

            // Copy sales items
            foreach ($invoiceData['sales'] as $saleData) {
                $shipment->sales()->create([
                    'product_id' => $saleData['product_id'],
                    'product_name' => $saleData['product']['name'] ?? '',
                    'unit_id' => $saleData['unit_id'],
                    'unit_name' => $saleData['unit_name'] ?? '',
                    'unit_quantity' => $saleData['unit_quantity'] ?? 1,
                    'quantity' => $saleData['quantity'],
                    'selling_price' => $saleData['selling_price'],
                    'buying_price' => $saleData['buying_price'],
                    'discount' => $saleData['discount'] ?? 0,
                    'vat' => $saleData['vat'] ?? 0,
                    'created_by' => auth()->id(),
                    'branch_id' => auth()->user()->branch_id,
                ]);
            }

            return redirect()
                ->route('shipments.show', $shipment)
                ->withSuccess('Invoice saved as shipment successfully.');

        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withError('Error saving as shipment: ' . $e->getMessage());
        }
    }

}
