@extends('layouts.app')

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">box</x-slot>
        <x-slot name="title">{{ $product->name }}</x-slot>
        <x-slot name="subtitle">{{ $product->barcode }}</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('products.index') }}">Products</a></li>
            <li class="breadcrumb-item active" aria-current="page">View</li>
        </x-slot>
        <x-slot name="controls">
            @can('update', $product)
            <a href="{{ route('products.edit', $product) }}" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> Edit Product
            </a>
            @endcan
            <a href="{{ route('products.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Products
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <!-- Product Overview Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-info-circle me-2"></i>Product Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        @if($product->image)
                            <img src="{{ $product->image }}" alt="{{ $product->name }}" class="img-fluid rounded mb-3" style="max-height: 200px;">
                        @else
                            <div class="d-flex align-items-center justify-content-center border rounded bg-light mb-3 mx-auto" style="width: 200px; height: 200px;">
                                <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                            </div>
                        @endif
                        <h4 class="mb-1">{{ $product->name }}</h4>
                        <p class="text-muted">{{ $product->barcode }}</p>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Status:</span>
                            <span class="badge bg-{{ optional($product->status)->name == 'Active' ? 'success' : 'secondary' }}">
                                {{ optional($product->status)->name ?? 'N/A' }}
                            </span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Category:</span>
                            <span>{{ optional($product->category)->name ?? 'N/A' }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">VAT Applied:</span>
                            <span class="badge bg-{{ $product->vat_applied ? 'info' : 'secondary' }}">
                                {{ $product->vat_applied ? 'Yes' : 'No' }}
                            </span>
                        </div>
                    </div>

                    <hr>

                    <h6 class="mb-3">Description</h6>
                    <p class="mb-0">{{ $product->description ?? 'No description available.' }}</p>
                </div>
            </div>
        </div>

        <!-- Inventory Information Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-box-seam me-2"></i>Inventory Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="display-4 mb-1">{{ _number($product->total_stock) }}</div>
                        <p class="text-muted">Available Stock</p>
                        
                        @if($product->total_stock > 10)
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <span class="badge bg-success">In Stock</span>
                        @elseif($product->total_stock > 0)
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-warning" role="progressbar" style="width: 50%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <span class="badge bg-warning">Low Stock</span>
                        @else
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-danger" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <span class="badge bg-danger">Out of Stock</span>
                        @endif
                    </div>

                    <hr>

                    <h6 class="mb-3">Stock Movement</h6>
                    <div class="d-flex justify-content-between mb-1">
                        <span class="text-muted">Last Restock:</span>
                        <span>{{ now()->subDays(rand(1, 30))->format('M d, Y') }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span class="text-muted">Last Sale:</span>
                        <span>{{ now()->subDays(rand(1, 10))->format('M d, Y') }}</span>
                    </div>
                    
                    <div class="mt-4">
                        <a href="#" class="btn btn-sm btn-outline-primary w-100">
                            <i class="bi bi-graph-up me-1"></i> View Stock History
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pricing Information Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-tags me-2"></i>Units & Pricing
                    </h5>
                </div>
                <div class="card-body">
                    @if(count($product->units) > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Unit</th>
                                        <th>Qty</th>
                                        <th>Buy Price</th>
                                        <th>Sell Price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($product->units as $unit)
                                    <tr>
                                        <td>{{ $unit->name }}</td>
                                        <td>{{ $unit->quantity }}</td>
                                        <td>{{ _money($unit->buying_price) }}</td>
                                        <td class="text-primary fw-bold">{{ _money($unit->selling_price) }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="bi bi-tag text-muted" style="font-size: 2rem;"></i>
                            <p class="mt-2">No units defined for this product</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="d-flex justify-content-between mt-2 mb-4">
        <div>
            <a href="{{ route('products.index') }}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left me-1"></i> Back to Products
            </a>

            @can('create', App\Models\Product::class)
            <a href="{{ route('products.create') }}" class="btn btn-outline-primary">
                <i class="bi bi-plus-circle me-1"></i> Create New Product
            </a>
            @endcan
        </div>

        <div>
            @can('update', $product)
            <a href="{{ route('products.edit', $product) }}" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> Edit
            </a>
            @endcan
            
            @can('delete', $product)
            <form action="{{ route('products.destroy', $product) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this product?')">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-danger">
                    <i class="bi bi-trash me-1"></i> Delete
                </button>
            </form>
            @endcan
        </div>
    </div>
</div>
@endsection
