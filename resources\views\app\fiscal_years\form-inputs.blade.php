@php $editing = isset($fiscalYear) @endphp

<div class="row">
    <x-inputs.group class="col-sm-12">
        <x-inputs.text
            name="name"
            label="Name"
            :value="old('name', ($editing ? $fiscalYear->name : ''))"
            maxlength="255"
            placeholder="Name"
            required
        ></x-inputs.text>
    </x-inputs.group>

    <x-inputs.group class="col-sm-12">
        <x-inputs.date
            name="start_date"
            label="Start Date"
            value="{{ old('start_date', ($editing ? optional($fiscalYear->start_date)->format('Y-m-d') : '')) }}"
            max="255"
            required
        ></x-inputs.date>
    </x-inputs.group>

    <x-inputs.group class="col-sm-12">
        <x-inputs.date
            name="end_date"
            label="End Date"
            value="{{ old('end_date', ($editing ? optional($fiscalYear->end_date)->format('Y-m-d') : '')) }}"
            max="255"
            required
        ></x-inputs.date>
    </x-inputs.group>

    @if($editing)
    <x-inputs.group class="col-sm-12">
        <x-inputs.checkbox
            name="is_closed"
            label="Is Closed"
            :checked="old('is_closed', ($editing ? $fiscalYear->is_closed : false))"
        ></x-inputs.checkbox>
    </x-inputs.group>
    @endif

    <x-inputs.group class="col-sm-12">
        <div
            x-data="{ isActive: {{ $editing && $fiscalYear->is_active ? 'true' : 'false' }} }"
            class="form-check"
        >
            <input
                type="checkbox"
                name="is_active"
                id="is_active"
                class="form-check-input"
                value="1"
                x-model="isActive"
                {{ $editing && $fiscalYear->is_active ? 'checked' : '' }}
            />
            <label class="form-check-label" for="is_active">Active</label>
        </div>
    </x-inputs.group>

    <x-inputs.group class="col-sm-12">
        <x-inputs.textarea
            name="description"
            label="Description"
            maxlength="255"
            >{{ old('description', ($editing ? $fiscalYear->description : ''))
            }}</x-inputs.textarea
        >
    </x-inputs.group>
</div>
