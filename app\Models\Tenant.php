<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Spatie\Multitenancy\Models\Tenant as SpatieTenant;
use Illuminate\Support\Facades\Auth;
use App\Models\User;


class Tenant extends SpatieTenant
{
    protected $fillable = ['name', 'domain', 'database', 'email', 'phone', 'current_tenant'];

    /**
     * Boot the model and add creating event listener.
     */
    protected static function booted()
    {
        static::creating(function (Tenant $tenant) {
            // Generate a unique database name if not provided
            if (empty($tenant->database)) {
                $tenant->database = env("DB_DATABASE") . "_" . strtolower($tenant->name) . '_' . uniqid();
            }
        });

        static::created(function (Tenant $tenant) {
            // Create the tenant's database and run migrations/seeding
            $tenant->createTenantDatabase();
            $tenant->configureTenantConnection();
            $tenant->runTenantMigrationsAndSeed();
        });
    }

    /**
     * Create a new database for the tenant.
     */
    protected function createTenantDatabase()
    {
        // Use the landlord connection to create the database
        DB::connection(config('tenancy.database.central_connection'))
            ->statement("CREATE DATABASE IF NOT EXISTS `{$this->database}`");
    }

    /**
     * Configure the tenant's database connection dynamically.
     */
    protected function configureTenantConnection()
    {
        // Set the tenant database connection configuration
        config([
            'database.connections.tenant' => [
                'driver' => env('DB_CONNECTION', 'mysql'),
                'host' => env('DB_HOST', '127.0.0.1'),
                'port' => env('DB_PORT', '3306'),
                'database' => $this->database,
                'username' => env('DB_USERNAME'),
                'password' => env('DB_PASSWORD')
            ],
        ]);

        // Purge the connection to ensure the new config is used
        DB::purge('tenant');
    }

    /**
     * Run migrations and seed the tenant's database.
     */
    protected function runTenantMigrationsAndSeed()
    {
        // Ensure the tenant connection is active
        $this->makeCurrent();
        // config(['database.connections.tenant.database' => 'dev_accounting_latifah travis_685c6d4363058']);
        // Run migrations for the tenant
        Artisan::call('migrate', [
            '--database' => 'tenant',
            '--force' => true,
        ]);

        // Run seeders for the tenant
        Artisan::call('db:seed', [
            '--database' => 'tenant',
            '--force' => true,
        ]);
        // Optional: Revert to landlord context
        $this->forgetCurrent();
    }


    public function createAdmin($data){
        $this->makeCurrent();
        $user = User::where("email", $data['email'])->first();
        if($user) {
            $user->update($data);
        } else {
            $user = User::create($data);
            $adminRole = Role::where("name",'super-admin')->first();
            $user->assignRole($adminRole);
        }
        // Log in the user instance
        return $user;
    }

    public function switching() {
        cache([_authId() => $this->domain]);
        $tenant = Tenant::find($this->current_tenant?? 0);
        if($tenant){
            $tenant->makeCurrent();
            cache([_authId() => $tenant->domain]);
            $user = User::where("email", $this->email)->first();
            if($user) auth()->login($user);
        }
    }
    
}

//  php artisan tenants:artisan "migrate --database=tenant"