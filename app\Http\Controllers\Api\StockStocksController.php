<?php

namespace App\Http\Controllers\Api;

use App\Models\Stock;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\StockResource;
use App\Http\Resources\StockCollection;

class StockStocksController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Stock $stock
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, Stock $stock)
    {
        $this->authorize('view', $stock);

        $search = $request->get('search', '');

        $stocks = $stock
            ->stocks()
            ->search($search)
            ->latest()
            ->paginate();

        return new StockCollection($stocks);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Stock $stock
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, Stock $stock)
    {
        $this->authorize('create', Stock::class);

        $validated = $request->validate([
            'product_id' => ['nullable', 'exists:products,id'],
            'quantity' => ['nullable', 'numeric'],
            'balance' => ['nullable', 'numeric'],
            'supplier_id' => ['nullable', 'exists:suppliers,id'],
            'approved_by' => ['nullable', 'exists:users,id'],
            'description' => ['nullable', 'max:255', 'string'],
        ]);

        $stock = $stock->stocks()->create($validated);

        return new StockResource($stock);
    }
}
