<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;
use App\Traits\BranchTrait;

class JournalEntry extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;
    use BranchTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'entry_number',
        'entry_date',
        'reference_number',
        'description',
        'entry_type',
        'status',
        'fiscal_year_id',
        'fiscal_period_id',
        'currency_id',
        'exchange_rate',
        'is_recurring',
        'recurring_entry_id',
        'created_by',
        'updated_by',
        'approved_by',
        'approved_at',
        'posted_by',
        'posted_at',
        'business_type_id',
        'branch_id',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'entry_date' => 'date',
        'approved_at' => 'datetime',
        'posted_at' => 'datetime',
        'exchange_rate' => 'decimal:6',
        'is_recurring' => 'boolean',
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];

    protected $appends = ['total_debit', 'total_credit', 'is_balanced'];

    public function getTotalDebitAttribute()
    {
        return $this->journalEntryLines()->sum('debit');
    }

    public function getTotalCreditAttribute()
    {
        return $this->journalEntryLines()->sum('credit');
    }

    public function getIsBalancedAttribute()
    {
        return abs($this->total_debit - $this->total_credit) < 0.01;
    }

    public function journalEntryLines()
    {
        return $this->hasMany(JournalEntryLine::class);
    }

    public function fiscalYear()
    {
        return $this->belongsTo(FiscalYear::class);
    }

    public function fiscalPeriod()
    {
        return $this->belongsTo(FiscalPeriod::class);
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function recurringEntry()
    {
        return $this->belongsTo(RecurringEntry::class);
    }

    public function generatedEntries()
    {
        return $this->hasMany(JournalEntry::class, 'recurring_entry_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function postedBy()
    {
        return $this->belongsTo(User::class, 'posted_by');
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function taxTransactions()
    {
        return $this->hasMany(TaxTransaction::class);
    }

    public function assetDepreciations()
    {
        return $this->hasMany(AssetDepreciation::class);
    }

    public function bankReconciliationItems()
    {
        return $this->hasMany(BankReconciliationItem::class);
    }
}
