@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">receipt</x-slot>
        <x-slot name="title">Invoice #{{ $invoice->invoice_id }}</x-slot>
        <x-slot name="subtitle">View invoice details</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('invoices.index') }}">Invoices</a></li>
            <li class="breadcrumb-item active" aria-current="page">View</li>
        </x-slot>
        <x-slot name="controls">
            <div class="d-flex gap-2">
                <a class="btn btn-outline-secondary" href="{{ route('invoices.index') }}">
                    <i class="bi-arrow-left me-1"></i> Back
                </a>
                <a class="btn btn-primary" href="{{ route('invoices.create') }}">
                    <i class="bi-plus-lg me-1"></i> New Invoice
                </a>
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <div class="col-lg-8 mb-5 mb-lg-0">
            <!-- Invoice Card -->
            <div class="card card-lg mb-5">
                <div class="card-header">
                    <div class="row justify-content-between align-items-center">
                        <div class="col">
                            <h4 class="card-header-title">Invoice Details</h4>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-{{ $invoice->approved_by ? 'success' : 'warning' }}">
                                {{ $invoice->approved_by ? 'Approved' : 'Pending Approval' }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body invoice" id="invoice">
                    <!-- Invoice Header -->
                    <div class="row justify-content-lg-between mb-5">
                        <div class="col-sm order-2 order-sm-1">
                            <div class="mb-3">
                                <img class="img-fluid" style="max-height: 100px; max-width: 200px" 
                                    src="{{ auth()->user()->getGlobalInstance('logo')->path ?? asset('assets/svg/logos/logo-short.svg') }}" 
                                    alt="Logo">
                            </div>
                            <h3 class="text-primary mb-1">{{ auth()->user()->getGlobal("business") ?? ''}}</h3>
                            <p class="mb-0">{{ auth()->user()->getGlobal("address") ?? ''}}</p>
                            <p class="mb-0">{{ auth()->user()->getGlobal("phone") ?? ''}}</p>
                            <p>{{ auth()->user()->getGlobal("email") ?? ''}}</p>
                        </div>
                        <!-- End Col -->

                        <div class="col-sm-auto order-1 order-sm-2 text-sm-end">
                            <div class="mb-3">
                                <h4 class="mb-1">Invoice #{{ $invoice->invoice_id }}</h4>
                                <span class="d-block text-muted">
                                    Reference: {{ $invoice->reference_no ?? 'N/A' }}
                                </span>
                            </div>

                            <div class="mb-3">
                                <h5 class="mb-1">Issued By:</h5>
                                <p class="mb-0">{{ $invoice->createdBy->name ?? ''}}</p>
                                <p class="mb-0">{{ $invoice->createdBy->email ?? ''}}</p>
                                <p class="mb-0">{{ $invoice->createdBy->address ?? ''}}</p>
                            </div>
                        </div>
                        <!-- End Col -->
                    </div>
                    <!-- End Row -->

                    <!-- Customer Info -->
                    <div class="row justify-content-md-between mb-4">
                        <div class="col-md-6">
                            <div class="card bg-soft-secondary">
                                <div class="card-body">
                                    <h5 class="card-title">Bill to:</h5>
                                    <h5 class="mb-1">{{ $invoice->customer_name ?? ''}}</h5>
                                    <p class="mb-0">{{ $invoice->customer->phone ?? ''}}</p>
                                    <p class="mb-0">{{ $invoice->customer->email ?? ''}}</p>
                                    <p class="mb-0">{{ $invoice->customer->address ?? ''}}</p>
                                </div>
                            </div>
                        </div>
                        <!-- End Col -->

                        <div class="col-md-6 mt-3 mt-md-0">
                            <div class="card bg-soft-primary">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <h5 class="card-title">Invoice Date:</h5>
                                            <p class="mb-0">{{ optional($invoice->created_at)->format('M d, Y') ?? ''}}</p>
                                        </div>
                                        <div class="col-6">
                                            <h5 class="card-title">Due Date:</h5>
                                            <p class="mb-0">{{ optional($invoice->date_to)->format('M d, Y') ?? 'N/A'}}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- End Col -->
                    </div>
                    <!-- End Row -->

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-borderless table-nowrap table-align-middle">
                            <thead class="thead-light">
                                <tr>
                                    <th>Item</th>
                                    <th>Quantity</th>
                                    <th>Unit</th>
                                    <th>Unit Price</th>
                                    <th class="text-end">Amount</th>
                                </tr>
                            </thead>

                            <tbody>
                                @foreach($invoice->sales as $sale)
                                <tr>
                                    <td>
                                        <h6 class="mb-0">{{ $sale->product->name ?? '-' }}</h6>
                                        <small class="text-muted">{{ $sale->product->description ?? ''}}</small>
                                    </td>
                                    <td>{{ $sale->quantity ?? ''}}</td>
                                    <td>{{ $sale->unit->name ?? ''}}</td>
                                    <td>{{ _money($sale->selling_price) ?? ''}}</td>
                                    <td class="text-end fw-semibold">
                                        {{ _money($sale->quantity * $sale->selling_price)}}
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <!-- End Table -->

                    <hr class="my-4">

                    <!-- Invoice Summary -->
                    <div class="row justify-content-md-end mb-4">
                        <div class="col-md-6 col-lg-5">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <dl class="row text-sm-end mb-0">
                                        <dt class="col-sm-6">Sub-Total:</dt>
                                        <dd class="col-sm-6 mb-1">{{ _money($invoice->sub_total) ?? '' }}</dd>

                                        <dt class="col-sm-6">VAT ({{ auth()->user()->getGlobal('vat') ?? 0 }}%):</dt>
                                        <dd class="col-sm-6 mb-1">{{ _money($invoice->vat) ?? '' }}</dd>

                                        <dt class="col-sm-6">Total + VAT:</dt>
                                        <dd class="col-sm-6 mb-1">{{ _money($invoice->sub_total + $invoice->vat) ?? '' }}</dd>

                                        <dt class="col-sm-6">Discount:</dt>
                                        <dd class="col-sm-6 mb-1">{{ _money($invoice->discount) ?? '' }}</dd>

                                        <dt class="col-sm-6 fw-bold">Total Amount:</dt>
                                        <dd class="col-sm-6 fw-bold mb-1 text-primary">{{ _money($invoice->amount_total) ?? '' }}</dd>

                                        <dt class="col-sm-6">Amount Paid:</dt>
                                        <dd class="col-sm-6 mb-1 text-success">{{ _money($invoice->amount_paid) ?? '' }}</dd>

                                        @if($invoice->amount_total > $invoice->amount_paid)
                                        <dt class="col-sm-6 text-danger">Balance Due:</dt>
                                        <dd class="col-sm-6 mb-0 text-danger">{{ _money($invoice->amount_total - $invoice->amount_paid) ?? '' }}</dd>
                                        @endif
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- End Row -->

                    <!-- Footer -->
                    <div class="mt-5">
                        <h5>Thank you for your business!</h5>
                        <p class="mb-0">If you have any questions concerning this invoice, please contact:</p>
                        <p class="mb-0">{{ auth()->user()->getGlobal("phone") ?? ''}}</p>
                        <p>{{ auth()->user()->getGlobal("email") ?? ''}}</p>
                        <div class="border-top pt-3 mt-5">
                            <p class="small text-muted mb-0">&copy; {{ date('Y')}} {{ auth()->user()->getGlobal("business") ?? ''}}. All rights reserved.</p>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Card -->

            <!-- Footer Actions -->
            <div class="d-flex justify-content-end d-print-none gap-3 mb-5">
                @if($invoice->approved_by)
                <button class="btn btn-outline-primary preview-invoice-btn" data-bs-toggle="modal" data-id="{{ $invoice->id }}" data-auto="true" data-bs-target=".preview-invoice-modal">
                    <i class="bi-printer me-1"></i> Print
                </button>
                <a class="btn btn-primary" onclick="createPDFfromHTML('invoice')" href="javascript:;">
                    <i class="bi-file-earmark-pdf me-1"></i> Download PDF
                </a>
                @endif
            </div>
            <!-- End Footer -->
        </div>

        <div class="col-lg-4">
            <!-- Status Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="card-header-title">Invoice Status</h4>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-2">
                            <span class="me-2">Status:</span>
                            <span class="badge bg-{{ $invoice->approved_by ? 'success' : 'warning' }}">
                                {{ $invoice->approved_by ? 'Approved' : 'Pending Approval' }}
                            </span>
                        </div>
                        
                        @if($invoice->approved_by)
                        <div class="d-flex align-items-center mb-2">
                            <span class="me-2">Approved By:</span>
                            <span class="fw-semibold">{{ optional($invoice->approvedBy)->name ?? '' }}</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="me-2">Approval Date:</span>
                            <span>{{ optional($invoice->approved_at)->format('M d, Y H:i') ?? '' }}</span>
                        </div>
                        @else
                            @if(auth()->user()->can('approve invoice'))
                            <div class="mt-3">
                                <a class="btn btn-primary w-100 pending-invoice-btn" href="#" data-bs-toggle="modal" data-id="{{ $invoice->id }}" data-bs-target=".pending-invoice-modal">
                                    <i class="bi-check-circle me-1"></i> Approve/Reject/Cancel
                                </a>
                            </div>
                            @else
                            <div class="alert alert-warning mb-0">
                                <i class="bi-exclamation-triangle me-1"></i> This invoice is awaiting approval
                            </div>
                            @endif
                        @endif
                    </div>
                </div>
            </div>
            <!-- End Status Card -->

            <!-- Payment History Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="card-header-title">Payment History</h4>
                </div>
                <div class="card-body p-0">
                    @if(count($invoice->payments) > 0)
                    <div class="table-responsive">
                        <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th>#</th>
                                    <th>Amount</th>
                                    <th>Balance</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($invoice->payments as $key => $payment)
                                <tr>
                                    <td>{{ $key + 1 }}</td>
                                    <td class="text-success fw-semibold">{{ _money($payment->amount) }}</td>
                                    <td>{{ _money($payment->balance) }}</td>
                                    <td>{{ $payment->created_at->format('M d, Y') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="text-center py-4">
                        <i class="bi bi-credit-card text-muted" style="font-size: 2rem;"></i>
                        <p class="mt-2">No payment records found</p>
                    </div>
                    @endif
                </div>
            </div>
            <!-- End Payment History Card -->

            <!-- Actions Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="card-header-title">Actions</h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @can('update', $invoice)
                        <!-- <a href="{{ route('invoices.edit', $invoice) }}" class="btn btn-outline-primary">
                            <i class="bi-pencil me-1"></i> Edit Invoice
                        </a> -->
                        @endcan
                        
                        @can('create', App\Models\Invoice::class)
                        <a href="{{ route('invoices.create') }}" class="btn btn-outline-success">
                            <i class="bi-plus-circle me-1"></i> Create New Invoice
                        </a>
                        @endcan
                        
                        @can('delete', $invoice)
                        <!-- <form action="{{ route('invoices.destroy', $invoice) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this invoice?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="bi-trash me-1"></i> Delete Invoice
                            </button>
                        </form> -->
                        @endcan
                    </div>
                </div>
            </div>
            <!-- End Actions Card -->
        </div>
    </div>
</div>
<!-- End Content -->
@endsection
