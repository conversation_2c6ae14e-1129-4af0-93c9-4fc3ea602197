<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Employee Cost Summary Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
        }
        .container {
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .header p {
            margin: 5px 0;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table th, table td {
            border: 1px solid #ddd;
            padding: 6px;
            font-size: 10px;
        }
        table th {
            background-color: #f2f2f2;
            text-align: left;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .summary {
            margin-top: 20px;
        }
        .summary h2 {
            font-size: 16px;
            margin-bottom: 10px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        .row {
            display: flex;
            margin: 0 -15px;
        }
        .col {
            flex: 1;
            padding: 0 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Employee Cost Summary Report</h1>
            <p>Period: {{ $monthName }} {{ $year }}</p>
            <p>Generated on: {{ date('d M Y H:i:s') }}</p>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Employee</th>
                    <th>Basic Pay</th>
                    <th>Employer Contributions</th>
                    <th>Total Cost</th>
                </tr>
            </thead>
            <tbody>
                @forelse($employeeCosts as $cost)
                <tr>
                    <td>{{ $cost['employee']->name }}</td>
                    <td class="text-right">{{ number_format($cost['basic_pay'], 2) }}</td>
                    <td class="text-right">{{ number_format($cost['contributions'], 2) }}</td>
                    <td class="text-right">{{ number_format($cost['total_cost'], 2) }}</td>
                </tr>
                @empty
                <tr>
                    <td colspan="4" class="text-center">No employee cost data found for this period.</td>
                </tr>
                @endforelse
            </tbody>
            <tfoot>
                <tr>
                    <th>Total</th>
                    <th class="text-right">{{ number_format(array_sum(array_column($employeeCosts, 'basic_pay')), 2) }}</th>
                    <th class="text-right">{{ number_format(array_sum(array_column($employeeCosts, 'contributions')), 2) }}</th>
                    <th class="text-right">{{ number_format($totalCost, 2) }}</th>
                </tr>
            </tfoot>
        </table>

        <div class="summary">
            <h2>Cost Analysis</h2>
            <div class="row">
                <div class="col">
                    <h3>Cost Breakdown</h3>
                    <table>
                        <tr>
                            <th>Basic Pay</th>
                            <td class="text-right">{{ number_format(array_sum(array_column($employeeCosts, 'basic_pay')), 2) }}</td>
                            <td class="text-right">{{ number_format(array_sum(array_column($employeeCosts, 'basic_pay')) / $totalCost * 100, 2) }}%</td>
                        </tr>
                        <tr>
                            <th>Employer Contributions</th>
                            <td class="text-right">{{ number_format(array_sum(array_column($employeeCosts, 'contributions')), 2) }}</td>
                            <td class="text-right">{{ number_format(array_sum(array_column($employeeCosts, 'contributions')) / $totalCost * 100, 2) }}%</td>
                        </tr>
                        <tr>
                            <th>Total</th>
                            <td class="text-right">{{ number_format($totalCost, 2) }}</td>
                            <td class="text-right">100.00%</td>
                        </tr>
                    </table>
                </div>
                <div class="col">
                    <h3>Average Cost per Employee</h3>
                    <table>
                        <tr>
                            <th>Average Basic Pay</th>
                            <td class="text-right">{{ number_format(count($employeeCosts) > 0 ? array_sum(array_column($employeeCosts, 'basic_pay')) / count($employeeCosts) : 0, 2) }}</td>
                        </tr>
                        <tr>
                            <th>Average Contributions</th>
                            <td class="text-right">{{ number_format(count($employeeCosts) > 0 ? array_sum(array_column($employeeCosts, 'contributions')) / count($employeeCosts) : 0, 2) }}</td>
                        </tr>
                        <tr>
                            <th>Average Total Cost</th>
                            <td class="text-right">{{ number_format(count($employeeCosts) > 0 ? $totalCost / count($employeeCosts) : 0, 2) }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>This is a computer-generated document. No signature is required.</p>
        </div>
    </div>
</body>
</html>
