<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SupplierUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', 'max:255', 'string'],
            'email' => ['nullable', 'email'],
            'phone' => ['nullable', 'max:255', 'string'],
            'status_id' => ['nullable', 'tenant_exists:statuses,id'],
            'address' => ['nullable', 'max:255', 'string'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => __('crud.suppliers.validation.name_required'),
            'name.max' => __('crud.suppliers.validation.name_max'),
            'name.string' => __('crud.suppliers.validation.name_string'),
            'email.email' => __('crud.suppliers.validation.email_invalid'),
            'phone.max' => __('crud.suppliers.validation.phone_max'),
            'phone.string' => __('crud.suppliers.validation.phone_string'),
            'status_id.tenant_exists' => __('crud.suppliers.validation.status_invalid'),
            'address.max' => __('crud.suppliers.validation.address_max'),
            'address.string' => __('crud.suppliers.validation.address_string'),
        ];
    }
}
