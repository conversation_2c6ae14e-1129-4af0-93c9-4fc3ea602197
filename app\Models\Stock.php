<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;
use App\Traits\WarehouseTrait;


// Types 'type' => 
// "STOCK_PURCHASE", 'SALE_RETURN', 'PURCHASE_RETURN','STOCK_TRANSFER_IN', 'STOCK_TRANSFER_OUT', 'STOCK_ADJUSTMENT', 'STOCK_COUNT', 'STOCK_EXPIRY', 'STOCK_SCRAP', 'STOCK_LOSS', 'STOCK_REPAIR', 'STOCK_RECYCLE', 'STOCK_REORDER', 'STOCK_RETURN', 'STOCK_WRITE_OFF', 'STOCK_WRITE_DOWN', 'STOCK_WRITE_UP', 'STOCK_WRITE_OFF_RETURN', 'STOCK_WRITE_DOWN_RETURN', 'STOCK_WRITE_UP_RETURN',

class Stock extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;
    use WarehouseTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'product_name',
        'product_id',
        'unit_name',
        'unit_id',
        'unit_quantity',
        'selling_price',
        'supplier_id',
        'buying_price',
        'created_by',
        'updated_by',
        'branch_id',
        'discount',
        'vat',
        'quantity',
        'balance',
        'type',
        'returned_quantity',
        'available_for_return',
        'saleable_id',
        'saleable_type',
        'business_type_id',
        'expires_at',
        'description',
        'currency_id',
        'warehouse_id',
        'location',
        'stock_id',
        'approved_by',
        'status_id',
    ];

    protected $searchableFields = ['*'];


    protected $casts = [
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
        'expires_at' => 'datetime',
        'quantity' => 'decimal:2',
        'returned_quantity' => 'decimal:2',
        'available_for_return' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'buying_price' => 'decimal:2',
        'discount' => 'decimal:2',
        'vat' => 'decimal:2',
    ];


    protected $appends = ['total_stock', 'selling_amount', 'buying_amount', 'units', 'selling_amount_total', 'buying_amount_total'];

    public function getSellingAmountAttribute(){
        return $this->selling_price * $this->quantity;
    }

    public function getUnitNameAttribute($value){
        return $this->value ?? $this->unit->name;
    }

    public function getBuyingAmountAttribute(){
        return $this->buying_price * $this->quantity;
    }

    public function getSellingAmountTotalAttribute(){
        $selling_amount = $this->selling_amount - $this->discount;
        if( $this->vat ) {
            $selling_amount = $selling_amount + ( $this->selling_amount * $this->vat / 100 );
        }
        return $selling_amount;
    }

    public function getBuyingAmountTotalAttribute(){
        $buying_amount = $this->buying_amount;
        // - $this->discount;
        if( $this->vat ) {
            $buying_amount = $buying_amount + ( $this->buying_amount * $this->vat / 100 );
        }
        return $buying_amount;
    }

    public function getProductNameAttribute(){
        return $this->product->name ?? '';
    }

    public function getUnitsAttribute(){
        return $this->product->units ?? [];
    }
    // public function getQuantityAttribute($value){
    //     return $this->stockItems()->whereNull('invoice_id')->count();
    // }

    public function getTotalStockAttribute($value){
        return $this->quantity;
    }
    
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function stock()
    {
        return $this->belongsTo(Stock::class);
    }

    public function stocks()
    {
        return $this->hasMany(Stock::class);
    }

    public function stockItems()
    {
        return $this->hasMany(StockItem::class);
    }

    
    public function payments()
    {
        return $this->morphMany(Payment::class, 'paymentable');
    }
    
    public function stockable()
    {
        return $this->morphTo();
    }

}
