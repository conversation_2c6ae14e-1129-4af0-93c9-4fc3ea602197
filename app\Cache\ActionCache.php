<?php

namespace App\Cache;

use App\Models\Form;
use App\Models\Approval;
use App\Models\FormResponse;

use Filament\{Tables, Forms};
use Illuminate\Database\Eloquent\Model;
use Livewire\Component;

/**
 * 
 */
class ActionCache
{
	
    public function processBasicFields()
    {
        return \Filament\Forms\Components\Section::make('Basic Fields')
            ->schema([
                \Filament\Forms\Components\Grid::make(['default' => 12])->schema([
                    \Filament\Forms\Components\Select::make('formCategory')
                    ->label('Categories')
                    ->relationship('formCategory', 'name')
                    // ->helperText('Your full name here, including any middle names.')
                    // ->hint('[Forgotten your password?](forgotten-password)')
                    ->columnSpan([
                        'default' => 6,
                        'md' => 6,
                        'lg' => 6,
                    ]),
                    \Filament\Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(65535)
                    ->columnSpan([
                        'default' => 6,
                        'md' => 6,
                        'lg' => 6,
                    ]),
                    \Filament\Forms\Components\Textarea::make('description')
                    ->maxLength(65535)
                    ->columnSpan([
                        'default' => 6,
                        'md' => 6,
                        'lg' => 6,
                    ]),
                ]),
               
                
            ])
            ->collapsed()
            ->columnSpan([
                'default' => 12,
                'md' => 12,
                'lg' => 12,
            ]);
    }




}