@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                Create New {{ $type == 'deduction' ? 'Deduction' : 'Contribution' }}
            </h4>
        </div>

        <div class="card-body">
            <form action="{{ route('deduction-contributions.store') }}" method="POST">
                @csrf

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="name">Name</label>
                            <input type="text" name="name" id="name" class="form-control" value="{{ old('name') }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="code">Code</label>
                            <input type="text" name="code" id="code" class="form-control" value="{{ old('code') }}">
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="formula">Formula/Amount</label>
                            <input type="text" name="formula" id="formula" class="form-control" value="{{ old('formula', 'BASIC_PAY * 0.05') }}" required>
                            <small class="form-text text-muted">
                                Use BASIC_PAY as a variable. Example: BASIC_PAY * 0.05 for 5% of basic pay.
                            </small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="status_id">Status</label>
                            <select name="status_id" id="status_id" class="form-control" required>
                                @foreach($statuses as $id => $name)
                                    <option value="{{ $id }}" {{ old('status_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="apply_mode">Type</label>
                            <select name="apply_mode" id="apply_mode" class="form-control" required>
                                <option value="DEDUCTION" {{ $type == 'deduction' || old('apply_mode') == 'DEDUCTION' ? 'selected' : '' }}>Deduction</option>
                                <option value="CONTRIBUTION" {{ $type == 'contribution' || old('apply_mode') == 'CONTRIBUTION' ? 'selected' : '' }}>Contribution</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="apply_at">Apply At</label>
                            <select name="apply_at" id="apply_at" class="form-control" required>
                                <option value="BEFORE" {{ old('apply_at') == 'BEFORE' ? 'selected' : '' }}>Before Tax</option>
                                <option value="AFTER" {{ old('apply_at') == 'AFTER' ? 'selected' : '' }}>After Tax</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="is_calculatable"> Affects Net Pay?</label>
                            <select name="is_calculatable" id="is_calculatable" class="form-control" required>
                                <option value="1" {{ old('is_calculatable') == 1 ? 'selected' : 'selected' }}>Yes</option>
                                <option value="0" {{ old('is_calculatable') === 0 ? 'selected' : '' }}>No</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-4">
                    <label for="description">Description</label>
                    <textarea name="description" id="description" class="form-control" rows="3">{{ old('description') }}</textarea>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ route('deduction-contributions.index', ['type' => $type]) }}" class="btn btn-light">
                        <i class="bi bi-arrow-left"></i> Back
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Create
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
