<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Stock;

use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UserStocksTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create(['email' => '<EMAIL>']);

        Sanctum::actingAs($user, [], 'web');

        $this->seed(\Database\Seeders\PermissionsSeeder::class);

        $this->withoutExceptionHandling();
    }

    /**
     * @test
     */
    public function it_gets_user_stocks()
    {
        $user = User::factory()->create();
        $stocks = Stock::factory()
            ->count(2)
            ->create([
                'approved_by' => $user->id,
            ]);

        $response = $this->getJson(route('api.users.stocks.index', $user));

        $response->assertOk()->assertSee($stocks[0]->description);
    }

    /**
     * @test
     */
    public function it_stores_the_user_stocks()
    {
        $user = User::factory()->create();
        $data = Stock::factory()
            ->make([
                'approved_by' => $user->id,
            ])
            ->toArray();

        $response = $this->postJson(
            route('api.users.stocks.store', $user),
            $data
        );

        unset($data['created_by']);
        unset($data['updated_by']);
        unset($data['status_id']);

        $this->assertDatabaseHas('stocks', $data);

        $response->assertStatus(201)->assertJsonFragment($data);

        $stock = Stock::latest('id')->first();

        $this->assertEquals($user->id, $stock->approved_by);
    }
}
