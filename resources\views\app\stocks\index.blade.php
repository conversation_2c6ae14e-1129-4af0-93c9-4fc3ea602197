@extends('layouts.app')

@push('styles')
<style>
    /* Prevent page break/flash before CSS loads */
    .content {
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
    }

    .content.loaded {
        opacity: 1;
    }

    /* Stock level indicators */
    .bg-soft-danger {
        background-color: rgba(220, 53, 69, 0.1) !important;
        border-left: 3px solid #dc3545;
    }
    .bg-soft-warning {
        background-color: rgba(255, 193, 7, 0.1) !important;
        border-left: 3px solid #ffc107;
    }
    .bg-soft-success {
        background-color: rgba(25, 135, 84, 0.1) !important;
        border-left: 3px solid #198754;
    }

    /* Avatar improvements */
    .avatar-xs {
        width: 2rem;
        height: 2rem;
        font-size: 0.75rem;
    }

    .avatar-soft-danger {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .avatar-soft-warning {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .avatar-soft-success {
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
    }

    .avatar-circle {
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Table improvements */
    .table tbody tr:hover {
        background-color: #f8f9fa !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.2s ease;
    }

    /* Card improvements */
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        border-radius: 0.5rem 0.5rem 0 0 !important;
    }

    /* Dropdown styling */
    .dropdown-menu {
        border: 1px solid #e7eaf3;
        box-shadow: 0 0.375rem 1.5rem rgba(140, 152, 164, 0.175);
        border-radius: 0.5rem;
        min-width: 180px;
    }

    .dropdown-item {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        transition: all 0.15s ease-in-out;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
        transform: translateX(2px);
    }

    .dropdown-item.text-danger:hover {
        background-color: #f8d7da;
        color: #58151c !important;
    }

    /* Badge improvements */
    .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        font-weight: 500;
    }

    /* Loading state */
    .table-loading {
        position: relative;
    }

    .table-loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }
</style>
@endpush

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">boxes</x-slot>
        <x-slot name="title">Stock Inventory</x-slot>
        <x-slot name="subtitle">Monitor inventory levels and manage stock</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item active" aria-current="page">Stock Inventory</li>
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group">
                @can('view-any', App\Models\Stock::class)
                <a href="{{ route('stocks.transfers') }}" class="btn btn-outline-info btn-sm">
                    <i class="bi bi-arrow-left-right me-1"></i>Stock Transfers
                </a>
                @endcan
                @can('create', App\Models\Stock::class)
                <a href="{{ route('stocks.taking-sheet') }}" class="btn btn-outline-success btn-sm" title="Download CSV for physical stock verification">
                    <i class="bi bi-download me-1"></i>Stock Taking Sheet
                </a>
                @endcan
                @can('create', App\Models\Order::class)
                <a href="{{ route('orders.create') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-cart-plus me-1"></i>New Purchase Order
                </a>
                @endcan
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->



    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="card-header-title">
                <i class="bi bi-funnel me-2"></i>Filters & Search
            </h6>
        </div>
        <div class="card-body">
            <form action="{{ route('stocks.index') }}" method="GET" class="row g-3">
                <!-- Search -->
                <div class="col-md-4">
                    <label class="form-label">Search</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" name="search"
                               placeholder="Search products, suppliers, barcodes..."
                               value="{{ request('search') }}">
                    </div>
                </div>

                <!-- Product Filter -->
                <div class="col-md-4">
                    <label class="form-label">Product</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select" name="product_id" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a product..."
                        }'>
                            <option value="">All Products</option>
                            @if(isset($filterOptions['products']))
                                @foreach($filterOptions['products'] as $product)
                                    <option value="{{ $product->id }}" {{ request('product_id') == $product->id ? 'selected' : '' }}>
                                        {{ $product->name }} @if($product->category) - {{ $product->category->name }} @endif
                                    </option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>

                <!-- Stock Type Filter -->
                <div class="col-md-4">
                    <label class="form-label">Stock Type</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select" name="type" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select stock type..."
                        }'>
                            <option value="">All Types</option>
                            @if(isset($filterOptions['stock_types']))
                                @foreach($filterOptions['stock_types'] as $key => $label)
                                    <option value="{{ $key }}" {{ request('type') == $key ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>

                <!-- Date Range -->
                <div class="col-md-4">
                    <label class="form-label">Date Range</label>
                    <input type="hidden" name="date_from" value="{{ request('date_from') }}">
                    <input type="hidden" name="date_to" value="{{ request('date_to') }}">
                    <button id="js-daterangepicker-predefined" type="button" class="btn btn-white w-100">
                        <i class="bi-calendar-week me-1"></i>
                        <span class="js-daterangepicker-predefined-preview">
                            @if(request('date_from') && request('date_to'))
                                {{ request('date_from') }} - {{ request('date_to') }}
                            @else
                                Select date range
                            @endif
                        </span>
                    </button>
                </div>

                <!-- Quick Date Filters -->
                <div class="col-md-2">
                    <label class="form-label">Quick Filter</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select" name="date_filter" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select period..."
                        }'>
                            <option value="">All Time</option>
                            <option value="today" {{ request('date_filter') == 'today' ? 'selected' : '' }}>Today</option>
                            <option value="yesterday" {{ request('date_filter') == 'yesterday' ? 'selected' : '' }}>Yesterday</option>
                            <option value="this_week" {{ request('date_filter') == 'this_week' ? 'selected' : '' }}>This Week</option>
                            <option value="last_week" {{ request('date_filter') == 'last_week' ? 'selected' : '' }}>Last Week</option>
                            <option value="this_month" {{ request('date_filter') == 'this_month' ? 'selected' : '' }}>This Month</option>
                            <option value="last_month" {{ request('date_filter') == 'last_month' ? 'selected' : '' }}>Last Month</option>
                        </select>
                    </div>
                </div>

                <!-- Warehouse Filter -->
                <div class="col-md-3">
                    <label class="form-label">Warehouse</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select" name="warehouse_id" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select warehouse..."
                        }'>
                            <option value="">All Warehouses</option>
                            @if(isset($filterOptions['warehouses']))
                                @foreach($filterOptions['warehouses'] as $warehouse)
                                    <option value="{{ $warehouse->id }}" {{ request('warehouse_id') == $warehouse->id ? 'selected' : '' }}>
                                        {{ $warehouse->name }} @if($warehouse->branch) - {{ $warehouse->branch->name }} @endif
                                    </option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>

                <!-- Stock Level Filter -->
                <div class="col-md-3">
                    <label class="form-label">Stock Level</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select" name="stock_level" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select stock level..."
                        }'>
                            <option value="">All Stock Levels</option>
                            <option value="out_of_stock" {{ request('stock_level') == 'out_of_stock' ? 'selected' : '' }}>Out of Stock</option>
                            <option value="low" {{ request('stock_level') == 'low' ? 'selected' : '' }}>Low Stock (≤5)</option>
                            <option value="medium" {{ request('stock_level') == 'medium' ? 'selected' : '' }}>Medium Stock (6-10)</option>
                            <option value="good" {{ request('stock_level') == 'good' ? 'selected' : '' }}>Good Stock (>10)</option>
                        </select>
                    </div>
                </div>

                <!-- Category Filter -->
                <div class="col-md-3">
                    <label class="form-label">Category</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select" name="category_id" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select category..."
                        }'>
                            <option value="">All Categories</option>
                            @if(isset($filterOptions['categories']))
                                @foreach($filterOptions['categories'] as $category)
                                    <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>

                <!-- Supplier Filter -->
                <div class="col-md-3">
                    <label class="form-label">Supplier</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select" name="supplier_id" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select supplier..."
                        }'>
                            <option value="">All Suppliers</option>
                            @if(isset($filterOptions['suppliers']))
                                @foreach($filterOptions['suppliers'] as $supplier)
                                    <option value="{{ $supplier->id }}" {{ request('supplier_id') == $supplier->id ? 'selected' : '' }}>
                                        {{ $supplier->name }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>

                <!-- Expiry Status Filter -->
                <div class="col-md-3">
                    <label class="form-label">Expiry Status</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select" name="expiry_status" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select expiry status..."
                        }'>
                            <option value="">All Items</option>
                            <option value="expired" {{ request('expiry_status') == 'expired' ? 'selected' : '' }}>Expired</option>
                            <option value="expiring_soon" {{ request('expiry_status') == 'expiring_soon' ? 'selected' : '' }}>Expiring Soon (30 days)</option>
                            <option value="fresh" {{ request('expiry_status') == 'fresh' ? 'selected' : '' }}>Fresh (>30 days)</option>
                            <option value="no_expiry" {{ request('expiry_status') == 'no_expiry' ? 'selected' : '' }}>No Expiry Date</option>
                        </select>
                    </div>
                </div>

                <!-- Status Filter -->
                <div class="col-md-3">
                    <label class="form-label">Status</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select" name="status" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select status..."
                        }'>
                            <option value="">All Status</option>
                            <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending Approval</option>
                        </select>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="col-12 d-flex gap-2 justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-funnel me-1"></i>Apply Filters
                    </button>
                    <a href="{{ route('stocks.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-x-circle me-1"></i>Clear All
                    </a>
                    <button type="button" class="btn btn-outline-info" onclick="exportStocks()">
                        <i class="bi bi-download me-1"></i>Export
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- End Filters -->


    <!-- Stock Inventory Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-header-title">
                    <i class="bi bi-list-ul me-2"></i>Stock Inventory
                </h5>
            </div>
        </div>

        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th style="width: 8%">Date</th>
                        <th style="width: 12%">Product Code</th>
                        <th style="width: 20%">Product</th>
                        <th style="width: 12%">Type</th>
                        <th style="width: 10%">Warehouse</th>
                        <th style="width: 8%">Stock Level</th>
                        <th style="width: 8%">Unit Cost</th>
                        <th style="width: 8%">Quantity</th>
                        <th style="width: 8%">Total Value</th>
                        <th style="width: 10%">Supplier</th>
                        <th style="width: 8%">Status</th>
                        <th style="width: 6%" class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($stocks as $stock)
                    <tr class="@if($stock->quantity <= 0) bg-soft-danger @elseif($stock->quantity <= 5) bg-soft-warning @endif">
                        <!-- Date -->
                        <td>
                            <small class="text-muted">{{ $stock->created_at->format('M d, Y') ?? '-' }}</small>
                        </td>

                        <!-- Product Code -->
                        <td>
                            <span class="fw-semibold">{{ $stock->product->barcode ?? '-' }}</span>
                        </td>

                        <!-- Product -->
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0 me-3">
                                    @if($stock->quantity <= 0)
                                        <div class="avatar avatar-xs avatar-soft-danger avatar-circle">
                                            <span class="avatar-initials"><i class="bi bi-x"></i></span>
                                        </div>
                                    @elseif($stock->quantity <= 5)
                                        <div class="avatar avatar-xs avatar-soft-warning avatar-circle">
                                            <span class="avatar-initials"><i class="bi bi-exclamation"></i></span>
                                        </div>
                                    @else
                                        <div class="avatar avatar-xs avatar-soft-success avatar-circle">
                                            <span class="avatar-initials"><i class="bi bi-check"></i></span>
                                        </div>
                                    @endif
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">{{ $stock->product->name ?? 'Unknown Product' }}</h6>
                                    @if($stock->product && $stock->product->category)
                                        <small class="text-muted">{{ $stock->product->category->name }}</small>
                                    @endif
                                </div>
                            </div>
                        </td>

                        <!-- Type -->
                        <td>
                            @if($stock->type)
                                @php
                                    $typeColors = [
                                        'STOCK_PURCHASE' => 'bg-primary',
                                        'SALE_RETURN' => 'bg-info',
                                        'PURCHASE_RETURN' => 'bg-warning',
                                        'STOCK_TRANSFER_IN' => 'bg-success',
                                        'STOCK_TRANSFER_OUT' => 'bg-danger',
                                        'STOCK_ADJUSTMENT' => 'bg-secondary',
                                        'STOCK_COUNT' => 'bg-dark',
                                    ];
                                    $color = $typeColors[$stock->type] ?? 'bg-light text-dark';
                                @endphp
                                <span class="badge {{ $color }}">{{ str_replace('_', ' ', $stock->type) }}</span>
                            @else
                                <span class="text-muted">-</span>
                            @endif
                        </td>

                        <!-- Warehouse -->
                        <td>
                            <span class="fw-semibold">{{ $stock->warehouse->name ?? '-' }}</span>
                            @if($stock->warehouse && $stock->warehouse->branch)
                                <small class="text-muted d-block">{{ $stock->warehouse->branch->name }}</small>
                            @endif
                        </td>

                        <!-- Stock Level -->
                        <td>
                            @if($stock->quantity <= 0)
                                <span class="badge bg-danger">Out of Stock</span>
                            @elseif($stock->quantity <= 5)
                                <span class="badge bg-warning">Low Stock</span>
                            @elseif($stock->quantity <= 10)
                                <span class="badge bg-info">Medium</span>
                            @else
                                <span class="badge bg-success">Good</span>
                            @endif
                        </td>

                        <!-- Unit Cost -->
                        <td>
                            <span class="fw-semibold">{{ _money($stock->buying_price) }}</span>
                        </td>

                        <!-- Quantity -->
                        <td>
                            <span class="fw-semibold">{{ number_format($stock->quantity, 2) }}</span>
                            @if($stock->unit_name)
                                <small class="text-muted d-block">{{ $stock->unit_name }}</small>
                            @endif
                        </td>

                        <!-- Total Value -->
                        <td>
                            <span class="fw-semibold">{{ _money($stock->buying_price * $stock->quantity) }}</span>
                        </td>

                        <!-- Supplier -->
                        <td>
                            <span class="fw-semibold">{{ $stock->supplier->name ?? '-' }}</span>
                        </td>

                        <!-- Status -->
                        <td>
                            @if($stock->approved_by)
                                <span class="badge bg-success">Approved</span>
                            @else
                                <span class="badge bg-warning">Pending</span>
                            @endif
                            @if($stock->expires_at)
                                @if($stock->expires_at < now())
                                    <span class="badge bg-danger mt-1">Expired</span>
                                @elseif($stock->expires_at < now()->addDays(30))
                                    <span class="badge bg-warning mt-1">Expiring Soon</span>
                                @endif
                            @endif
                        </td>
                        <td class="text-center">
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button"
                                        id="stockActions{{ $stock->id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-three-dots"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="stockActions{{ $stock->id }}">
                                    @if($stock->quantity >= 0)
                                    @can('view', $stock)
                                    <li>
                                        <a href="{{ route('stocks.show', $stock) }}" class="dropdown-item">
                                            <i class="bi bi-eye me-2"></i>View Details
                                        </a>
                                    </li>
                                    @endcan
                                    @can('update', $stock)
                                    <!-- <li>
                                        <a href="{{ route('stocks.edit', $stock) }}" class="dropdown-item">
                                            <i class="bi bi-pencil me-2"></i>Edit Stock
                                        </a>
                                    </li> -->
                                    @endcan                        
                                    @endif

                                    @can('delete', $stock)
                                    <!-- <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form action="{{ route('stocks.destroy', $stock) }}" method="POST" class="d-inline w-100"
                                              onsubmit="return confirm('Are you sure you want to delete this stock entry?')">
                                            @csrf @method('DELETE')
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="bi bi-trash me-2"></i>Delete Stock
                                            </button>
                                        </form>
                                    </li> -->
                                    @endcan
                                </ul>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="12" class="text-center p-5">
                            <x-empty-state
                                icon="boxes"
                                title="No stock items found"
                                message="No stock items match your search criteria or no stock has been added yet."
                            />
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="card-footer">
            {{ $stocks->appends(request()->query())->links() }}
        </div>
    </div>
    <!-- End Stock Inventory Table -->




</div>
@endsection





@push('scripts')
<script>
$(document).ready(function() {
    // Show content after page loads to prevent flash
    $('.content').addClass('loaded');

    // Initialize DataTable
    if (typeof dataTableBtn === 'function') {
        dataTableBtn();
    }

    // Initialize Tom Select dropdowns (handled by theme)
    if (typeof HSCore !== 'undefined' && HSCore.components && HSCore.components.HSTomSelect) {
        HSCore.components.HSTomSelect.init('.js-select');
    }

    // Initialize date range picker
    if (typeof HSDaterangepicker !== 'undefined') {
        HSDaterangepicker.init('#js-daterangepicker-predefined', {
            startDate: moment().subtract(29, 'days'),
            endDate: moment(),
            ranges: {
                'Today': [moment(), moment()],
                'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                'This Month': [moment().startOf('month'), moment().endOf('month')],
                'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            }
        }, function(start, end, label) {
            $('input[name="date_from"]').val(start.format('YYYY-MM-DD'));
            $('input[name="date_to"]').val(end.format('YYYY-MM-DD'));
            $('.js-daterangepicker-predefined-preview').text(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        });
    }

    // Prevent dropdown from closing when clicking on form elements
    $('.dropdown-menu').on('click', 'form', function(e) {
        e.stopPropagation();
    });

    // Add confirmation for delete actions
    $('.dropdown-menu form[method="POST"]').on('submit', function(e) {
        const stockName = $(this).closest('tr').find('h6').text().trim();
        if (!confirm(`Are you sure you want to delete "${stockName}"? This action cannot be undone.`)) {
            e.preventDefault();
            return false;
        }
    });

    // Smooth hover effects for table rows
    $('.table tbody tr').hover(
        function() {
            $(this).addClass('table-hover-effect');
        },
        function() {
            $(this).removeClass('table-hover-effect');
        }
    );

    // Date range validation
    $('input[name="date_from"]').on('change', function() {
        const dateTo = $('input[name="date_to"]').val();
        if (dateTo && $(this).val() > dateTo) {
            alert('Start date cannot be later than end date');
            $(this).val('');
        }
    });

    $('input[name="date_to"]').on('change', function() {
        const dateFrom = $('input[name="date_from"]').val();
        if (dateFrom && $(this).val() < dateFrom) {
            alert('End date cannot be earlier than start date');
            $(this).val('');
        }
    });
});

// Export functionality
function exportStocks() {
    const form = $('form')[0];
    const formData = new FormData(form);

    // Add export parameter
    formData.append('export', 'true');

    // Create URL with parameters
    const params = new URLSearchParams(formData);
    const exportUrl = `{{ route('stocks.index') }}?${params.toString()}`;

    // Open in new window for download
    window.open(exportUrl, '_blank');
}
</script>
@endpush

