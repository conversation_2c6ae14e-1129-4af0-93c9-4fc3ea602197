/* Enhanced UI Styles */

/* Card enhancements */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.01);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Button enhancements */
.btn {
    font-weight: 500;
    letter-spacing: 0.3px;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-success {
    background-color: #2ecc71;
    border-color: #2ecc71;
}

.btn-success:hover {
    background-color: #27ae60;
    border-color: #27ae60;
}

.btn-outline-primary {
    border-color: #3498db;
    color: #3498db;
}

.btn-outline-primary:hover {
    background-color: #3498db;
    color: #fff;
}

/* Badge enhancements */
.badge {
    font-weight: 500;
    letter-spacing: 0.3px;
    padding: 0.35em 0.65em;
}

.bg-soft-primary {
    background-color: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.bg-soft-success {
    background-color: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
}

.bg-soft-warning {
    background-color: rgba(241, 196, 15, 0.1);
    color: #f39c12;
}

.bg-soft-danger {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.bg-soft-info {
    background-color: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.bg-soft-secondary {
    background-color: rgba(149, 165, 166, 0.1);
    color: #7f8c8d;
}

/* Table enhancements */
.table {
    font-size: 0.9rem;
}

.table thead th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.table-borderless tbody tr {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table-borderless tbody tr:last-child {
    border-bottom: none;
}

/* Form enhancements */
.form-control, .form-select {
    border-radius: 0.375rem;
    border-color: rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
}

.input-group-text {
    background-color: rgba(0, 0, 0, 0.03);
    border-color: rgba(0, 0, 0, 0.1);
}

/* Page header enhancements */
.page-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.page-header-title {
    font-weight: 600;
    color: #2c3e50;
}

.page-header-icon {
    margin-right: 0.5rem;
    color: #3498db;
}

.page-header-text {
    color: #7f8c8d;
    margin-top: 0.25rem;
    margin-bottom: 0;
}

/* Breadcrumb enhancements */
.breadcrumb {
    font-size: 0.85rem;
    padding: 0.5rem 0;
}

.breadcrumb-item a {
    color: #3498db;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #7f8c8d;
}

/* Empty state enhancements */
.empty-state {
    padding: 3rem 1rem;
    text-align: center;
}

.empty-state-icon {
    font-size: 3rem;
    color: #bdc3c7;
    margin-bottom: 1rem;
}

.empty-state-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.empty-state-text {
    color: #7f8c8d;
    margin-bottom: 1.5rem;
}

/* Tooltip enhancements */
.tooltip {
    font-size: 0.75rem;
}

.tooltip-inner {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

/* Dropdown enhancements */
.dropdown-menu {
    border-radius: 0.375rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.dropdown-item:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

.dropdown-item i {
    margin-right: 0.5rem;
    color: #7f8c8d;
}

/* Pagination enhancements */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: #3498db;
    border-color: rgba(0, 0, 0, 0.05);
    padding: 0.375rem 0.75rem;
}

.page-link:hover {
    background-color: rgba(52, 152, 219, 0.05);
    border-color: rgba(0, 0, 0, 0.05);
    color: #2980b9;
}

.page-item.active .page-link {
    background-color: #3498db;
    border-color: #3498db;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .card-header-title {
        font-size: 1.25rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .table {
        font-size: 0.8rem;
    }
}