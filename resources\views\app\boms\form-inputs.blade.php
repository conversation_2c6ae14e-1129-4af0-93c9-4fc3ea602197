@php $editing = isset($bom) @endphp

<div class="row">
    <x-inputs.group class="col-sm-12">
        <x-inputs.text
            name="name"
            label="Name"
            :value="old('name', ($editing ? $bom->name : ''))"
            maxlength="255"
            placeholder="Name"
            required
        ></x-inputs.text>
    </x-inputs.group>

    <x-inputs.group class="col-sm-12">
        <x-inputs.select name="product_id" label="Product" required>
            @php $selected = old('product_id', ($editing ? $bom->product_id : '')) @endphp
            <option disabled {{ empty($selected) ? 'selected' : '' }}>Please select the Product</option>
            @foreach($products as $value => $label)
            <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }} >{{ $label }}</option>
            @endforeach
        </x-inputs.select>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6">
        <x-inputs.number
            name="quantity"
            label="Quantity"
            :value="old('quantity', ($editing ? $bom->quantity : '1'))"
            min="0.01"
            step="0.01"
            placeholder="Quantity"
            required
        ></x-inputs.number>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6">
        <x-inputs.text
            name="unit_of_measure"
            label="Unit of Measure"
            :value="old('unit_of_measure', ($editing ? $bom->unit_of_measure : 'unit'))"
            maxlength="255"
            placeholder="Unit of Measure"
            required
        ></x-inputs.text>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6">
        <x-inputs.select name="status" label="Status" required>
            @php $selected = old('status', ($editing ? $bom->status : 'draft')) @endphp
            <option value="draft" {{ $selected == 'draft' ? 'selected' : '' }} >Draft</option>
            <option value="active" {{ $selected == 'active' ? 'selected' : '' }} >Active</option>
            <option value="inactive" {{ $selected == 'inactive' ? 'selected' : '' }} >Inactive</option>
        </x-inputs.select>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6">
        <x-inputs.text
            name="version"
            label="Version"
            :value="old('version', ($editing ? $bom->version : '1.0'))"
            maxlength="255"
            placeholder="Version"
        ></x-inputs.text>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6">
        <x-inputs.date
            name="effective_date"
            label="Effective Date"
            value="{{ old('effective_date', ($editing ? optional($bom->effective_date)->format('Y-m-d') : '')) }}"
        ></x-inputs.date>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6">
        <x-inputs.date
            name="expiry_date"
            label="Expiry Date"
            value="{{ old('expiry_date', ($editing ? optional($bom->expiry_date)->format('Y-m-d') : '')) }}"
        ></x-inputs.date>
    </x-inputs.group>

    <x-inputs.group class="col-sm-12">
        <x-inputs.checkbox
            name="is_default"
            label="Default BOM for this product"
            :checked="old('is_default', ($editing ? $bom->is_default : false))"
        ></x-inputs.checkbox>
    </x-inputs.group>

    <x-inputs.group class="col-sm-12">
        <x-inputs.textarea
            name="description"
            label="Description"
            maxlength="255"
            >{{ old('description', ($editing ? $bom->description : ''))
            }}</x-inputs.textarea
        >
    </x-inputs.group>
</div>
