<?php

namespace App\Http\Controllers;

use App\Models\TaxType;
use App\Models\TaxTransaction;
use Illuminate\Http\Request;

class TaxTransactionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', TaxTransaction::class);

        $search = $request->get('search', '');
        $taxTypeId = $request->get('tax_type_id', '');
        $transactionType = $request->get('transaction_type', '');
        $fromDate = $request->get('from_date', '');
        $toDate = $request->get('to_date', '');

        $taxTransactions = TaxTransaction::search($search);
        
        if ($taxTypeId) {
            $taxTransactions->where('tax_type_id', $taxTypeId);
        }
        
        if ($transactionType) {
            $taxTransactions->where('transaction_type', $transactionType);
        }
        
        if ($fromDate) {
            $taxTransactions->where('transaction_date', '>=', $fromDate);
        }
        
        if ($toDate) {
            $taxTransactions->where('transaction_date', '<=', $toDate);
        }
        
        $taxTransactions = $taxTransactions->latest('transaction_date')
            ->paginate()
            ->withQueryString();

        $taxTypes = TaxType::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.tax_transactions.index', compact(
            'taxTransactions', 
            'search', 
            'taxTypes',
            'taxTypeId',
            'transactionType',
            'fromDate',
            'toDate'
        ));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\TaxTransaction  $taxTransaction
     * @return \Illuminate\Http\Response
     */
    public function show(TaxTransaction $taxTransaction)
    {
        $this->authorize('view', $taxTransaction);

        return view('app.tax_transactions.show', compact('taxTransaction'));
    }

    /**
     * Generate tax report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function report(Request $request)
    {
        $this->authorize('view-any', TaxTransaction::class);

        $validated = $request->validate([
            'tax_type_id' => 'nullable|tenant_exists:tax_types,id',
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
            'transaction_type' => 'nullable|string',
            'format' => 'required|in:html,pdf,csv',
        ]);

        $taxTypeId = $validated['tax_type_id'] ?? null;
        $fromDate = $validated['from_date'];
        $toDate = $validated['to_date'];
        $transactionType = $validated['transaction_type'] ?? null;
        
        $query = TaxTransaction::whereBetween('transaction_date', [$fromDate, $toDate]);
        
        if ($taxTypeId) {
            $query->where('tax_type_id', $taxTypeId);
        }
        
        if ($transactionType) {
            $query->where('transaction_type', $transactionType);
        }
        
        $taxTransactions = $query->with('taxType')
            ->orderBy('transaction_date')
            ->get();
            
        $totalTaxableAmount = $taxTransactions->sum('taxable_amount');
        $totalTaxAmount = $taxTransactions->sum('tax_amount');
        
        $taxTypes = TaxType::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');
            
        $data = [
            'taxTransactions' => $taxTransactions,
            'totalTaxableAmount' => $totalTaxableAmount,
            'totalTaxAmount' => $totalTaxAmount,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'taxTypeId' => $taxTypeId,
            'taxTypes' => $taxTypes,
            'transactionType' => $transactionType,
        ];
        
        if ($validated['format'] === 'html') {
            return view('app.tax_transactions.report', $data);
        } elseif ($validated['format'] === 'pdf') {
            $pdf = PDF::loadView('app.tax_transactions.report_pdf', $data);
            return $pdf->download('tax_report_' . date('Y-m-d') . '.pdf');
        } else {
            // CSV format
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="tax_report_' . date('Y-m-d') . '.csv"',
            ];
            
            $callback = function() use ($taxTransactions, $totalTaxableAmount, $totalTaxAmount) {
                $file = fopen('php://output', 'w');
                fputcsv($file, ['Date', 'Reference', 'Tax Type', 'Transaction Type', 'Taxable Amount', 'Tax Amount']);
                
                foreach ($taxTransactions as $transaction) {
                    fputcsv($file, [
                        $transaction->transaction_date,
                        $transaction->reference_number,
                        $transaction->taxType->name,
                        $transaction->transaction_type,
                        $transaction->taxable_amount,
                        $transaction->tax_amount,
                    ]);
                }
                
                fputcsv($file, ['', '', '', 'Total', $totalTaxableAmount, $totalTaxAmount]);
                fclose($file);
            };
            
            return response()->stream($callback, 200, $headers);
        }
    }
}
