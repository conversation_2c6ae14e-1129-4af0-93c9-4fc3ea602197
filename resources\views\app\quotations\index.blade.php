@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-sm mb-2 mb-sm-0">
                <h1 class="page-header-title">
                    <i class="bi-file-earmark-text me-2"></i>
                    Quotations
                    <span class="badge bg-soft-dark text-dark ms-2">{{ $quotations->total() }}</span>
                </h1>
            </div>
            <div class="col-sm-auto">
                @can('create', App\Models\Quotation::class)
                <a href="{{ route('quotations.create') }}" class="btn btn-primary">
                    <i class="bi-plus-lg me-1"></i>
                    New Quotation
                </a>
                @endcan
            </div>
        </div>
    </div>
    <!-- End Page Header -->

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h4 class="card-title">
                <i class="bi-funnel me-2"></i>
                Filters
            </h4>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('quotations.index') }}">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ $search }}" placeholder="Customer, description, reference...">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Statuses</option>
                            @foreach($statuses as $statusId => $statusName)
                                <option value="{{ $statusId }}" {{ $status == $statusId ? 'selected' : '' }}>
                                    {{ $statusName }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="customer" class="form-label">Customer</label>
                        <select class="form-select" id="customer" name="customer">
                            <option value="">All Customers</option>
                            @foreach($customers as $customerId => $customerName)
                                <option value="{{ $customerId }}" {{ $customer == $customerId ? 'selected' : '' }}>
                                    {{ $customerName }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="{{ $dateFrom }}">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="{{ $dateTo }}">
                    </div>
                    <div class="col-md-1 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi-search"></i>
                        </button>
                        <a href="{{ route('quotations.index') }}" class="btn btn-outline-secondary">
                            <i class="bi-arrow-clockwise"></i>
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- End Filters -->



    <!-- Quotations Table -->
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                <i class="bi-table me-2"></i>
                Quotations List
            </h4>
        </div>
        <div class="table-responsive table-dropdown-space">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                <thead class="thead-light">
                    <tr>
                        <th>Quotation #</th>
                        <th>@lang('crud.customers.singular')</th>
                        <th>Status</th>
                        <th>Amount</th>
                        <th>Created By</th>
                        <th>Date</th>
                        <th class="text-end">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($quotations as $quotation)
                    <tr>
                        <td>
                            <a href="{{ route('quotations.show', $quotation) }}" class="text-decoration-none">
                                <span class="fw-semibold text-primary">#{{ $quotation->quotation_id }}</span>
                            </a>
                            @if($quotation->reference_no)
                                <br><small class="text-muted">Ref: {{ $quotation->reference_no }}</small>
                            @endif
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-sm avatar-circle me-2">
                                    <span class="avatar-initials avatar-initials-sm bg-soft-primary text-primary">
                                        {{ substr($quotation->customer_name ?? 'N/A', 0, 1) }}
                                    </span>
                                </div>
                                <div>
                                    <span class="fw-semibold">{{ $quotation->customer_name ?? 'N/A' }}</span>
                                    @if($quotation->customer?->email)
                                        <br><small class="text-muted">{{ $quotation->customer->email }}</small>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td>
                            @php
                                $statusClasses = [
                                    '10' => 'bg-soft-secondary text-secondary',
                                    '11' => 'bg-soft-info text-info',
                                    '12' => 'bg-soft-success text-success',
                                    '13' => 'bg-soft-danger text-danger',
                                    '14' => 'bg-soft-warning text-warning'
                                ];
                                $statusClass = $statusClasses[$quotation->status_id] ?? 'bg-soft-secondary text-secondary';
                            @endphp
                            <span class="badge {{ $statusClass }}">
                                {{ $statuses[$quotation->status_id] ?? 'Unknown' }}
                            </span>
                        </td>
                        <td>
                            <span class="fw-semibold">{{ _money($quotation->amount_total) }}</span>
                            @if($quotation->discount > 0)
                                <br><small class="text-muted">Discount: {{ _money($quotation->discount) }}</small>
                            @endif
                        </td>
                        <td>
                            <span>{{ $quotation->createdBy->name ?? 'N/A' }}</span>
                            <br><small class="text-muted">{{ $quotation->created_at->format('M d, Y') }}</small>
                        </td>
                        <td>
                            <span>{{ $quotation->created_at->format('M d, Y') }}</span>
                            <br><small class="text-muted">{{ $quotation->created_at->format('h:i A') }}</small>
                        </td>
                        <td class="text-end">
                            <div class="btn-group" role="group">
                                @can('view', $quotation)
                                <a href="{{ route('quotations.show', $quotation) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="bi-eye"></i>
                                </a>
                                @endcan

                                @if(!$quotation->approved_by)
                                    @can('update', $quotation)
                                    <a href="{{ route('quotations.edit', $quotation) }}" class="btn btn-sm btn-outline-secondary">
                                        <i class="bi-pencil"></i>
                                    </a>
                                    @endcan
                                @endif

                                <!-- Dropdown for more actions -->
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        @if($quotation->status_id == '10' && !$quotation->approved_by)
                                            @can('update', $quotation)
                                            <li>
                                                <form action="{{ route('quotations.convert-to-invoice', $quotation) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="dropdown-item" onclick="return confirm('Convert this quotation to invoice?')">
                                                        <i class="bi-receipt me-2"></i>Convert to Invoice
                                                    </button>
                                                </form>
                                            </li>
                                            @endcan
                                        @endif

                                        <li>
                                            <button type="button" class="dropdown-item preview-quotation-btn"
                                                    data-bs-toggle="modal"
                                                    data-bs-target=".preview-quotation-modal"
                                                    data-id="{{ $quotation->id }}">
                                                <i class="bi-printer me-2"></i>Print
                                            </button>
                                        </li>
                                        <li>
                                            <a href="{{ route('quotations.show', $quotation) }}" class="dropdown-item" onclick="setTimeout(() => createPDFfromHTML('quotation'), 1000)">
                                                <i class="bi-file-earmark-pdf me-2"></i>Download PDF
                                            </a>
                                        </li>

                                        @can('create', App\Models\Quotation::class)
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <form action="{{ route('quotations.duplicate', $quotation) }}" method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="dropdown-item">
                                                    <i class="bi-files me-2"></i>Duplicate
                                                </button>
                                            </form>
                                        </li>
                                        @endcan

                                        @if(!$quotation->approved_by)
                                            @can('delete', $quotation)
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <form action="{{ route('quotations.destroy', $quotation) }}" method="POST" class="d-inline">
                                                    @csrf @method('DELETE')
                                                    <button type="submit" class="dropdown-item text-danger" onclick="return confirm('Are you sure you want to delete this quotation?')">
                                                        <i class="bi-trash me-2"></i>Delete
                                                    </button>
                                                </form>
                                            </li>
                                            @endcan
                                        @endif
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-center">
                                <i class="bi-file-earmark-text display-4 text-muted"></i>
                                <h5 class="mt-2">No quotations found</h5>
                                <p class="text-muted">Try adjusting your search criteria or create a new quotation.</p>
                                @can('create', App\Models\Quotation::class)
                                <a href="{{ route('quotations.create') }}" class="btn btn-primary">
                                    <i class="bi-plus-lg me-1"></i>Create Quotation
                                </a>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($quotations->hasPages())
        <div class="card-footer">
            {{ $quotations->links() }}
        </div>
        @endif
    </div>



</div>
@endsection





@push('scripts')
<script>
  $("document").ready(function () {
        dataTableBtn()
        // dataTableBtn(null, {Quotation: [[0, 'desc']]})
  });
</script>

@endpush