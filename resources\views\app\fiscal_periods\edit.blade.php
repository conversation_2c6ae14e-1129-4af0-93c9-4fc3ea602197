@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-pencil me-2"></i>Edit Fiscal Period
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group" role="group">
                <a href="{{ route('fiscal-periods.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Periods
                </a>
                <a href="{{ route('fiscal-periods.show', $fiscalPeriod) }}" class="btn btn-outline-info btn-sm">
                    <i class="bi bi-eye me-1"></i>View Details
                </a>
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar3 me-2"></i>Edit: {{ $fiscalPeriod->name }}
                    </h5>
                </div>

                <form method="POST" action="{{ route('fiscal-periods.update', $fiscalPeriod) }}">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        
                        <!-- Error Display -->
                        @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <h6 class="alert-heading">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Please fix the following errors:
                            </h6>
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        @endif

                        <!-- Current Status Display -->
                        <div class="alert alert-info">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Current Status:</strong> 
                                    @if($fiscalPeriod->is_closed)
                                        <span class="badge bg-danger">Closed</span>
                                    @else
                                        <span class="badge bg-success">Open</span>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    <strong>Fiscal Year:</strong> {{ $fiscalPeriod->fiscalYear->name }}
                                </div>
                            </div>
                        </div>

                        <!-- Basic Information -->
                        <h6 class="text-primary mb-3">
                            <i class="bi bi-info-circle me-2"></i>Basic Information
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Fiscal Year <span class="text-danger">*</span></label>
                                <select name="fiscal_year_id" class="form-select @error('fiscal_year_id') is-invalid @enderror" required>
                                    <option value="">Select Fiscal Year</option>
                                    @foreach($fiscalYears as $id => $name)
                                        <option value="{{ $id }}" {{ old('fiscal_year_id', $fiscalPeriod->fiscal_year_id) == $id ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('fiscal_year_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Select the fiscal year this period belongs to</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Period Name <span class="text-danger">*</span></label>
                                <input type="text" name="name" class="form-control @error('name') is-invalid @enderror" 
                                       placeholder="e.g., January 2024, Q1 2024" value="{{ old('name', $fiscalPeriod->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Enter a descriptive name for the period</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Start Date <span class="text-danger">*</span></label>
                                <input type="date" name="start_date" class="form-control @error('start_date') is-invalid @enderror" 
                                       value="{{ old('start_date', $fiscalPeriod->start_date->format('Y-m-d')) }}" required>
                                @error('start_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">End Date <span class="text-danger">*</span></label>
                                <input type="date" name="end_date" class="form-control @error('end_date') is-invalid @enderror" 
                                       value="{{ old('end_date', $fiscalPeriod->end_date->format('Y-m-d')) }}" required>
                                @error('end_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Status Settings -->
                        <h6 class="text-primary mb-3 mt-4">
                            <i class="bi bi-gear me-2"></i>Status Settings
                        </h6>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input type="checkbox" name="is_active" class="form-check-input" id="is_active" 
                                           value="1" {{ old('is_active', $fiscalPeriod->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        <strong>Active Period</strong>
                                    </label>
                                </div>
                                <div class="form-text">Set as an active fiscal period</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input type="checkbox" name="is_closed" class="form-check-input" id="is_closed" 
                                           value="1" {{ old('is_closed', $fiscalPeriod->is_closed) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_closed">
                                        <strong>Closed Period</strong>
                                    </label>
                                </div>
                                <div class="form-text">Close this period to prevent new transactions</div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea name="description" class="form-control @error('description') is-invalid @enderror" 
                                      rows="3" placeholder="Optional description for this fiscal period">{{ old('description', $fiscalPeriod->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Warning for Closed Periods -->
                        @if($fiscalPeriod->is_closed)
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> This fiscal period is closed. Changing the status to "Open" will allow new transactions to be posted to this period.
                        </div>
                        @endif

                        <!-- Audit Information -->
                        <div class="alert alert-light">
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>Created:</strong> {{ $fiscalPeriod->created_at->format('M d, Y H:i') }}
                                        @if($fiscalPeriod->createdBy)
                                            by {{ $fiscalPeriod->createdBy->name }}
                                        @endif
                                    </small>
                                </div>
                                @if($fiscalPeriod->updated_at != $fiscalPeriod->created_at)
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>Last Updated:</strong> {{ $fiscalPeriod->updated_at->format('M d, Y H:i') }}
                                        @if($fiscalPeriod->updatedBy)
                                            by {{ $fiscalPeriod->updatedBy->name }}
                                        @endif
                                    </small>
                                </div>
                                @endif
                            </div>
                            @if($fiscalPeriod->closed_date)
                            <div class="row mt-2">
                                <div class="col-md-12">
                                    <small class="text-muted">
                                        <strong>Closed:</strong> {{ $fiscalPeriod->closed_date->format('M d, Y H:i') }}
                                        @if($fiscalPeriod->closedBy)
                                            by {{ $fiscalPeriod->closedBy->name }}
                                        @endif
                                    </small>
                                </div>
                            </div>
                            @endif
                        </div>

                    </div>

                    <div class="card-footer d-flex justify-content-between">
                        <a href="{{ route('fiscal-periods.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>Update Fiscal Period
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
