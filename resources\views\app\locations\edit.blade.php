@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">geo-alt</x-slot>
        <x-slot name="title">Edit Location</x-slot>
        <x-slot name="subtitle">Update location information</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('locations.index') }}">Locations</a></li>
            <li class="breadcrumb-item"><a href="{{ route('locations.show', $location) }}">{{ $location->name }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group">
                <a href="{{ route('locations.show', $location) }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-1"></i> Back to Location
                </a>
                <a href="{{ route('locations.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-list me-1"></i> All Locations
                </a>
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row ">
        <div class="col-lg-10">
            <!-- Edit Location Card -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-header-title">
                            <i class="bi bi-pencil me-2"></i>Edit Location Information
                        </h4>
                        <div class="d-flex align-items-center">
                            <small class="text-muted me-2">Last updated:</small>
                            <span class="badge bg-soft-info">{{ $location->updated_at->format('M d, Y') }}</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Display Validation Errors -->
                    @if ($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <h6 class="alert-heading">
                                <i class="bi bi-exclamation-triangle me-1"></i>
                                Please fix the following errors:
                            </h6>
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <x-form
                        method="PUT"
                        action="{{ route('locations.update', $location) }}"
                        id="locationForm"
                    >
                        @include('app.locations.form-inputs')

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">Ready to save changes?</h6>
                                                <small class="text-muted">Make sure all information is correct before updating.</small>
                                            </div>
                                            <div class="btn-group">
                                                <a href="{{ route('locations.show', $location) }}" class="btn btn-outline-secondary">
                                                    <i class="bi bi-x-circle me-1"></i> Cancel
                                                </a>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="bi bi-check-circle me-1"></i> Update Location
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- End Form Actions -->
                    </x-form>
                </div>
            </div>
            <!-- End Edit Location Card -->

            <!-- Location History Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-clock-history me-2"></i>Location History
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Location Created</h6>
                                <p class="text-muted mb-1">{{ $location->created_at->format('M d, Y \a\t g:i A') }}</p>
                                <small class="text-muted">Created by {{ $location->createdBy->name ?? 'System' }}</small>
                            </div>
                        </div>

                        @if($location->updated_at != $location->created_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Last Updated</h6>
                                <p class="text-muted mb-1">{{ $location->updated_at->format('M d, Y \a\t g:i A') }}</p>
                                <small class="text-muted">Updated by {{ $location->updatedBy->name ?? 'System' }}</small>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            <!-- End Location History Card -->
        </div>
    </div>
</div>
<!-- End Content -->

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Simple form validation and submission
    $('#locationForm').on('submit', function(e) {
        var nameField = $('#name');
        var statusField = $('#status_id');
        var hasErrors = false;

        // Check name field
        if (!nameField.val() || !nameField.val().trim()) {
            nameField.addClass('is-invalid');
            hasErrors = true;
        } else {
            nameField.removeClass('is-invalid');
        }

        // Check status field
        if (!statusField.val()) {
            statusField.addClass('is-invalid');
            hasErrors = true;
        } else {
            statusField.removeClass('is-invalid');
        }

        if (hasErrors) {
            e.preventDefault();
            // Show error message
            if (typeof toastr !== 'undefined') {
                toastr.error('Please fill in all required fields.');
            } else {
                alert('Please fill in all required fields.');
            }
            // Focus on first error
            $('.is-invalid').first().focus();
            return false;
        }

        // Remove beforeunload warning when submitting
        $(window).off('beforeunload');

        // Show loading state
        var submitBtn = $(this).find('button[type="submit"]');
        submitBtn.html('<i class="bi bi-hourglass-split me-1"></i> Updating...').prop('disabled', true);
    });

    // Real-time validation feedback
    $('#name, #status_id').on('blur change', function() {
        if ($(this).val()) {
            $(this).removeClass('is-invalid');
        }
    });

    // Form submission feedback
    $('#locationForm').on('submit', function() {
        var submitBtn = $(this).find('button[type="submit"]');
        if (!submitBtn.hasClass('disabled')) {
            submitBtn.html('<i class="bi bi-hourglass-split me-1"></i> Updating...').addClass('disabled');
        }
    });

    // Track changes for visual feedback
    var originalData = $('#locationForm').serialize();
    var hasChanges = false;

    $('#locationForm input, #locationForm select, #locationForm textarea').on('change input', function() {
        hasChanges = $('#locationForm').serialize() !== originalData;

        var submitBtn = $('#locationForm button[type="submit"]');
        if (hasChanges) {
            submitBtn.removeClass('btn-primary').addClass('btn-warning').html('<i class="bi bi-exclamation-triangle me-1"></i> Save Changes');
        } else {
            submitBtn.removeClass('btn-warning').addClass('btn-primary').html('<i class="bi bi-check-circle me-1"></i> Update Location');
        }
    });

    // Warn before leaving if there are unsaved changes
    $(window).on('beforeunload', function() {
        if (hasChanges) {
            return 'You have unsaved changes. Are you sure you want to leave?';
        }
    });
});
</script>
@endpush
@endsection
