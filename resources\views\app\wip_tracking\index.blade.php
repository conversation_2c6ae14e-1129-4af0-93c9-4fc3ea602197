@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">WIP Tracking</x-slot>
        <x-slot name="controls">
            @can('create', App\Models\WIPTracking::class)
            <a href="{{ route('wip-tracking.create') }}" class="btn btn-info btn-sm">
                @lang('crud.common.create') WIP Tracking
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="searchbar mt-4 mb-5">
        <form class="d-flex" id="filter">
            <div class="me-3">
                <input
                    type="text"
                    name="search"
                    value="{{ $search ?? '' }}"
                    class="form-control"
                    placeholder="{{ __('crud.common.search') }}"
                    autocomplete="off"
                />
            </div>
            <div class="me-3">
                <select name="production_order_id" class="form-select">
                    <option value="">All Production Orders</option>
                    @foreach($productionOrders ?? [] as $id => $orderNumber)
                    <option value="{{ $id }}" {{ $id == ($productionOrderId ?? '') ? 'selected' : '' }}>
                        {{ $orderNumber }}
                    </option>
                    @endforeach
                </select>
            </div>
            <div class="me-3">
                <select name="status" class="form-select">
                    <option value="">All Statuses</option>
                    <option value="in_progress" {{ ($status ?? '') == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                    <option value="completed" {{ ($status ?? '') == 'completed' ? 'selected' : '' }}>Completed</option>
                </select>
            </div>
            <div class="me-3">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-search"></i>
                </button>
            </div>
        </form>
    </div>

    <div class="card card-table">
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            Tracking Number
                        </th>
                        <th class="text-left">
                            Production Order
                        </th>
                        <th class="text-left">
                            Product
                        </th>
                        <th class="text-left">
                            Work Center
                        </th>
                        <th class="text-left">
                            Start Date
                        </th>
                        <th class="text-left">
                            End Date
                        </th>
                        <th class="text-center">
                            Status
                        </th>
                        <th class="text-center">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($wipTrackings ?? [] as $wipTracking)
                    <tr>
                        <td>{{ $wipTracking->tracking_number ?? '-' }}</td>
                        <td>{{ optional($wipTracking->productionOrder)->order_number ?? '-' }}</td>
                        <td>{{ optional($wipTracking->product)->name ?? '-' }}</td>
                        <td>{{ $wipTracking->work_center ?? '-' }}</td>
                        <td>{{ $wipTracking->start_date ? $wipTracking->start_date->format('Y-m-d') : '-' }}</td>
                        <td>{{ $wipTracking->end_date ? $wipTracking->end_date->format('Y-m-d') : '-' }}</td>
                        <td class="text-center">
                            <span class="badge {{ 
                                $wipTracking->status == 'in_progress' ? 'bg-primary' : 'bg-success'
                            }}">
                                {{ ucfirst(str_replace('_', ' ', $wipTracking->status ?? '-')) }}
                            </span>
                        </td>
                        <td class="text-center" style="width: 134px;">
                            <div
                                role="group"
                                aria-label="Row Actions"
                                class="btn-group"
                            >
                                @can('update', $wipTracking)
                                <a
                                    href="{{ route('wip-tracking.edit', $wipTracking) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        edit
                                    </button>
                                </a>
                                @endcan @can('view', $wipTracking)
                                <a
                                    href="{{ route('wip-tracking.show', $wipTracking) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        view
                                    </button>
                                </a>
                                @endcan @can('delete', $wipTracking)
                                <form
                                    action="{{ route('wip-tracking.destroy', $wipTracking) }}"
                                    method="POST"
                                    onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                >
                                    @csrf @method('DELETE')
                                    <button
                                        type="submit"
                                        class="btn btn-light text-danger m-1"
                                    >
                                        del
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="text-center">
                            @lang('crud.common.no_items_found')
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="card-footer d-flex justify-content-center">
            @if(isset($wipTrackings))
            {!! $wipTrackings->render() !!}
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
  $("document").ready(function () {
    dataTableBtn()
  });
</script>
@endpush
