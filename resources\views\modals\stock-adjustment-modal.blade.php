<form action="/stock-adjustment" method="POST">
	@csrf
	<x-lg-modal>
		<x-slot name="class">stock-adjustment-modal</x-slot>
		<x-slot name="title">Stock Adjustment </x-slot>


	    <div class="" id="stock-adjust-el">

	      <!-- Table -->
	      <div class="table-responsive">
	        <table class="table table-borderless table-nowrap table-align-middle">
	          <thead class="thead-light">
	            <tr>
	              <th>Item</th>
	              <th>Available Stock</th>
	              <th>Quantity</th>
	              <th></th>
	            </tr>
	          </thead>
	          <tbody>
	            <tr v-for="(item, index) in items">
	              <th> 
	                <select name="products[]" v-model="item.product_id" class="form-control" @change="updateSale($event, index)">
	                  <option v-for="(product, i) in products" :value="product.id" v-text="product.name + ' ~ ' + product.description"></option>
	                </select>
	              </th>
	              <th> <span v-text="item.total_stock"></span> </th>
	              <th> <input type="number" :name="item.product_id" :max="item.total_stock" class="form-control" v-model="item.quantity" required> </th>
	              <td> <span @click="removeItem(index)" class="btn btn-outline-danger btn-sm"> del</span></td>
	            </tr>
	          </tbody>
	        </table>
	        <button type="button" @click="addItem" class="btn btn-primary btn-sm m-2"> Add</button>
	      </div>


	     </div>

		<x-slot name="footer">
			<button type="submit" class="btn btn-primary"> Adjust Stock </button>
		</x-slot>
	</x-lg-modal>


</form>




@push('scripts')

  <script type="text/javascript">
    
    new Vue({
      el: "#stock-adjust-el",
      data(){
        return{
          products: [],
          branch_id: @json( auth()->user()->branch_id ?? null ),
          items: [],
        }
      },

      methods: {

      	getFilters(){
      		var filters = "&branch_id=" + this.branch_id;
      		filters += ""
      	},

        removeItem(index){
          this.items.splice(index, 1);
          this.calculate();
        },


        updateSale(e, index) {
          var products = this.products.filter( item => item.id == e.target.value);
          if( products.length > 0) {
            var product = products[0];
            var item   = this.items[index];
            item.total_stock = product.total_stock;
            item.product_id = product.id;
          }
        },

        addItem(){
          var item = {
          	product_id: null,
          	stock: 0,
            quantity: 0,
          }
          this.items.push(item);
        },

        calculate(){

        },

        getProducts() {
        	axios.get('/ajax-products?' + this.getFilters() ).then( res => {
        		this.products = res.data;
        	});
        }


      },


      created(){

      	this.getProducts();
      }

    })

  </script>

@endpush
