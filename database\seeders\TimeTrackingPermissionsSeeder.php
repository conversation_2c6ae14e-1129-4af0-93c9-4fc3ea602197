<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON><PERSON>\Permission\Models\Permission;
use <PERSON><PERSON>\Permission\Models\Role;

class TimeTrackingPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Work Hours Configuration Permissions
        $workHoursPermissions = [
            'view_any_work_hours_configuration',
            'view_work_hours_configuration',
            'create_work_hours_configuration',
            'update_work_hours_configuration',
            'delete_work_hours_configuration',
            'restore_work_hours_configuration',
            'force_delete_work_hours_configuration',
        ];

        // Employee Time Record Permissions
        $timeRecordPermissions = [
            'view_any_employee_time_record',
            'view_employee_time_record',
            'create_employee_time_record',
            'update_employee_time_record',
            'delete_employee_time_record',
            'restore_employee_time_record',
            'force_delete_employee_time_record',
        ];

        // Employee Task Permissions
        $taskPermissions = [
            'view_any_employee_task',
            'view_employee_task',
            'create_employee_task',
            'update_employee_task',
            'delete_employee_task',
            'restore_employee_task',
            'force_delete_employee_task',
        ];

        // Create all permissions
        $allPermissions = array_merge($workHoursPermissions, $timeRecordPermissions, $taskPermissions);
        
        foreach ($allPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permissions to roles
        $superAdminRole = Role::where('name', 'super-admin')->first();
        if ($superAdminRole) {
            $superAdminRole->givePermissionTo($allPermissions);
        }

        $payrollRole = Role::where('name', 'payroll')->first();
        if ($payrollRole) {
            // Payroll role gets work hours configuration permissions
            $payrollRole->givePermissionTo($workHoursPermissions);
            
            // And view permissions for time records
            $payrollRole->givePermissionTo([
                'view_any_employee_time_record',
                'view_employee_time_record',
                'view_any_employee_task',
                'view_employee_task',
            ]);
        }

        // Create employee role if it doesn't exist
        $employeeRole = Role::firstOrCreate(['name' => 'employee']);
        
        // Employees can manage their own time records and tasks
        $employeeRole->givePermissionTo([
            'create_employee_time_record',
            'update_employee_time_record',
            'view_employee_time_record',
            'create_employee_task',
            'update_employee_task',
            'view_employee_task',
        ]);
    }
}
