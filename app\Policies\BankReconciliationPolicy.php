<?php

namespace App\Policies;

use App\Models\User;
use App\Models\BankReconciliation;
use Illuminate\Auth\Access\HandlesAuthorization;

class BankReconciliationPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo('list bank-reconciliations');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\BankReconciliation  $bankReconciliation
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, BankReconciliation $bankReconciliation)
    {
        return $user->hasPermissionTo('view bank-reconciliations');
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo('create bank-reconciliations');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\BankReconciliation  $bankReconciliation
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, BankReconciliation $bankReconciliation)
    {
        return $user->hasPermissionTo('update bank-reconciliations') && 
            $bankReconciliation->status !== 'completed';
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\BankReconciliation  $bankReconciliation
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, BankReconciliation $bankReconciliation)
    {
        return $user->hasPermissionTo('delete bank-reconciliations') && 
            $bankReconciliation->status !== 'completed';
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\BankReconciliation  $bankReconciliation
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, BankReconciliation $bankReconciliation)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\BankReconciliation  $bankReconciliation
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, BankReconciliation $bankReconciliation)
    {
        return false;
    }

    /**
     * Determine whether the user can complete the bank reconciliation.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\BankReconciliation  $bankReconciliation
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function complete(User $user, BankReconciliation $bankReconciliation)
    {
        return $user->hasPermissionTo('complete bank-reconciliations') && 
            $bankReconciliation->status !== 'completed';
    }
}
