<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Branch;

use App\Models\Status;

use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BranchTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create(['email' => '<EMAIL>']);

        Sanctum::actingAs($user, [], 'web');

        $this->seed(\Database\Seeders\PermissionsSeeder::class);

        $this->withoutExceptionHandling();
    }

    /**
     * @test
     */
    public function it_gets_branches_list()
    {
        $branches = Branch::factory()
            ->count(5)
            ->create();

        $response = $this->getJson(route('api.branches.index'));

        $response->assertOk()->assertSee($branches[0]->name);
    }

    /**
     * @test
     */
    public function it_stores_the_branch()
    {
        $data = Branch::factory()
            ->make()
            ->toArray();

        $response = $this->postJson(route('api.branches.store'), $data);

        unset($data['created_by']);
        unset($data['updated_by']);

        $this->assertDatabaseHas('branches', $data);

        $response->assertStatus(201)->assertJsonFragment($data);
    }

    /**
     * @test
     */
    public function it_updates_the_branch()
    {
        $branch = Branch::factory()->create();

        $user = User::factory()->create();
        $user = User::factory()->create();
        $status = Status::factory()->create();

        $data = [
            'name' => $this->faker->name(),
            'address' => $this->faker->text,
            'phone' => $this->faker->phoneNumber,
            'email' => $this->faker->email,
            'created_by' => $user->id,
            'updated_by' => $user->id,
            'status_id' => $status->id,
        ];

        $response = $this->putJson(
            route('api.branches.update', $branch),
            $data
        );

        unset($data['created_by']);
        unset($data['updated_by']);

        $data['id'] = $branch->id;

        $this->assertDatabaseHas('branches', $data);

        $response->assertOk()->assertJsonFragment($data);
    }

    /**
     * @test
     */
    public function it_deletes_the_branch()
    {
        $branch = Branch::factory()->create();

        $response = $this->deleteJson(route('api.branches.destroy', $branch));

        $this->assertSoftDeleted($branch);

        $response->assertNoContent();
    }
}
