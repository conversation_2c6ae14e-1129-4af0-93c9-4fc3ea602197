<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Resources\UnitResource;
use App\Http\Controllers\Controller;
use App\Http\Resources\UnitCollection;

class UserUnitsController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, User $user)
    {
        $this->authorize('view', $user);

        $search = $request->get('search', '');

        $units = $user
            ->units2()
            ->search($search)
            ->latest()
            ->paginate();

        return new UnitCollection($units);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, User $user)
    {
        $this->authorize('create', Unit::class);

        $validated = $request->validate([
            'name' => ['required', 'max:255', 'string'],
            'quantity' => ['nullable', 'numeric'],
            'status_id' => ['nullable', 'exists:statuses,id'],
        ]);

        $unit = $user->units2()->create($validated);

        return new UnitResource($unit);
    }
}
