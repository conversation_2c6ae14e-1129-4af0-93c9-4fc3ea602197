@extends('layouts.app')

@push('styles')
    <style type="text/css">
@media print {
    @page {
/*        size: A1|A4|A5 landscape; /* auto is default portrait; */*/
        background: red;
        height: 20% !important;
        size: portrait ;
    }
}
    </style>
@endpush

@section('content')
    <!-- Content -->
    <div class="content container-fluid">

        <table>
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Age</th>
                </tr>
            </thead>

            <tbody>
                <tr>
                    <td>Prince</td>
                    <td><PERSON></td>
                </tr>                
                <tr>
                    <td><PERSON>man</td>
                    <td><PERSON></td>
                </tr>
            </tbody>
        </table>
    </div>



@endsection



<script>

    // $(document).on('ready', function () {
    //   // INITIALIZATION OF DATERANGEPICKER

    //   $(".year").change( function(){
    //     var params  = @json( request()->all() );
    //     var search  = "?";
    //     params.year = this.value;
    //     for( para in params){
    //       search += "&" + para + "=" + params[para];
    //     }

    //     window.location.href = search;
    //   })

    //   // =======================================================
    //   $('.js-daterangepicker').daterangepicker();

    //   $('.js-daterangepicker-times').daterangepicker({
    //     timePicker: true,
    //     startDate: moment().startOf('hour'),
    //     endDate: moment().startOf('hour').add(32, 'hour'),
    //     locale: {
    //       format: 'M/DD hh:mm A'
    //     }
    //   });

    //   var start = @json( request()->from );
    //   var end = @json( request()->to );
    //   start = (start) ? moment(start, "YYYY-MM-DD HH:mm:ss") : moment();
    //   end = (end) ? moment(end, "YYYY-MM-DD HH:mm:ss") : moment();
    //   $('#js-daterangepicker-predefined .js-daterangepicker-predefined-preview').html(start.format('MMM D') + ' - ' + end.format('MMM D, YYYY'));

    //   function cb(start, end) {

    //     var params  = @json( request()->all() );
    //     var search  = "?";
    //     params.from = start.format("YYYY-MM-DD HH:mm:ss");
    //     params.to   = end.format("YYYY-MM-DD HH:mm:ss");
    //     $('input[name="from"]').val(params.from);
    //     $('input[name="to"]').val(params.to);
    //     for( para in params){
    //       search += "&" + para + "=" + params[para];
    //     }

    //     // window.location.href = search;
    //     // $('#js-daterangepicker-predefined .js-daterangepicker-predefined-preview').html(start.format('MMM D') + ' - ' + end.format('MMM D, YYYY'));
    //   }

    //   $('#js-daterangepicker-predefined').daterangepicker({
    //     startDate: start,
    //     endDate: end,
    //     ranges: {
    //       'Today': [moment(), moment()],
    //       'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
    //       'Last 7 Days': [moment().subtract(6, 'days'), moment()],
    //       'Last 30 Days': [moment().subtract(29, 'days'), moment()],
    //       'This Month': [moment().startOf('month'), moment().endOf('month')],
    //       'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    //     }
    //   }, cb);

    //   // cb(start, end);
    // });
</script>

