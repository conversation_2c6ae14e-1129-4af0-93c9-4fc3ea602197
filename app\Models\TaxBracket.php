<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;

class TaxBracket extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'min_amount',
        'max_amount',
        'rate',
        'order',
        'is_active',
        'description',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'min_amount' => 'decimal:2',
        'max_amount' => 'decimal:2',
        'rate' => 'decimal:4',
        'order' => 'integer',
        'is_active' => 'boolean',
    ];

    protected $searchableFields = ['name', 'description'];

    /**
     * Get the rate as percentage for display
     */
    public function getRatePercentageAttribute()
    {
        return $this->rate * 100;
    }

    /**
     * Get formatted min amount
     */
    public function getFormattedMinAmountAttribute()
    {
        return number_format($this->min_amount, 2);
    }

    /**
     * Get formatted max amount
     */
    public function getFormattedMaxAmountAttribute()
    {
        if ($this->max_amount === null) {
            return 'Unlimited';
        }
        return number_format($this->max_amount, 2);
    }

    /**
     * Get formatted rate
     */
    public function getFormattedRateAttribute()
    {
        return number_format($this->rate_percentage, 2) . '%';
    }

    /**
     * Scope for active tax brackets
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered tax brackets
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('order')->orderBy('min_amount');
    }

    /**
     * Get all active tax brackets in order
     */
    public static function getActiveBrackets()
    {
        return static::active()->ordered()->get();
    }

    /**
     * Calculate tax for a given amount using all active brackets
     */
    public static function calculateTax($grossSalary)
    {
        $brackets = static::getActiveBrackets();

        if ($brackets->isEmpty()) {
            return 0;
        }

        $totalTax = 0;
        $remainingSalary = $grossSalary;

        foreach ($brackets as $bracket) {
            if ($remainingSalary <= 0) {
                break;
            }

            // Determine the taxable amount for this bracket
            $bracketMin = $bracket->min_amount;
            $bracketMax = $bracket->max_amount;

            // Skip if salary is below this bracket
            if ($grossSalary <= $bracketMin) {
                continue;
            }

            // Calculate the amount subject to this bracket's rate
            $taxableInBracket = 0;

            if ($bracketMax === null) {
                // Unlimited bracket - tax all remaining salary
                $taxableInBracket = max(0, $grossSalary - $bracketMin);
            } else {
                // Limited bracket
                $taxableInBracket = min(
                    $bracketMax - $bracketMin,
                    max(0, $grossSalary - $bracketMin)
                );
            }

            $taxForBracket = $taxableInBracket * $bracket->rate;
            $totalTax += $taxForBracket;
        }

        return round($totalTax, 2);
    }

    /**
     * Get tax breakdown for a given amount
     */
    public static function getTaxBreakdown($grossSalary)
    {
        $brackets = static::getActiveBrackets();
        $breakdown = [];
        $totalTax = 0;

        foreach ($brackets as $bracket) {
            $bracketMin = $bracket->min_amount;
            $bracketMax = $bracket->max_amount;

            // Skip if salary is below this bracket
            if ($grossSalary <= $bracketMin) {
                continue;
            }

            // Calculate the amount subject to this bracket's rate
            $taxableInBracket = 0;

            if ($bracketMax === null) {
                $taxableInBracket = max(0, $grossSalary - $bracketMin);
            } else {
                $taxableInBracket = min(
                    $bracketMax - $bracketMin,
                    max(0, $grossSalary - $bracketMin)
                );
            }

            $taxForBracket = $taxableInBracket * $bracket->rate;
            $totalTax += $taxForBracket;

            $breakdown[] = [
                'bracket' => $bracket,
                'taxable_amount' => $taxableInBracket,
                'tax_amount' => $taxForBracket,
                'rate' => $bracket->rate,
                'rate_percentage' => $bracket->rate_percentage,
            ];
        }

        return [
            'breakdown' => $breakdown,
            'total_tax' => round($totalTax, 2),
            'net_amount' => $grossSalary - round($totalTax, 2),
        ];
    }
}
