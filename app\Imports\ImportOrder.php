<?php

namespace App\Imports;

use App\Models\Product;
use App\Models\Category;
use App\Models\Stock;
use App\Models\Unit;
use App\Models\Order;
use Maatwebsite\Excel\Concerns\ToModel;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;

class ImportOrder implements ToModel, WithHeadingRow, WithCalculatedFormulas
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public $product = null;
    public $order = null;

    public function model(array $row)
    {
        $params = ["name" => _from($row, "item")];
        $this->product = Product::where($params)->first();
        $this->product = ( $this->product ) ? $this->product : Product::find( _from($row, "product_id") );
        if( $this->product ) {
            $this->insertRows($row);
        }
        return $this->order;
    }

    private function getUnit($row){
        $units = ['unit1', 'unit2', 'unit3', 'unit4', 'unit5', 'unit6', 'unit6', 'unit7', 'unit8', 'unit9', 'unit10'];
        foreach ($units as $unitKey) {

            $name     = _from($row, $unitKey);
            $quantity = _from($row, $unitKey . "quantity");
            if( $name && $quantity ) {
                $unit = Unit::where("name", $name)->where("product_id", $this->product->id ?? '')->first();
                if($unit) $unit->newQty = $quantity;
                return $unit;
            }

        }
        return null;
    }

    public function insertRows($row){

        $data = [];
        $data["supplier_name"] = 'Imported Data';    
        $data["sub_total"] = 0;    
        $data["amount_total"] = 0;    
        $data["amount_paid"] = 0;    
        $data["discount"] = 0;    
        $data["branch_id"] = auth()->user()->branch_id;    
        $unit = $this->getUnit($row);

        if( ! $unit ) return;


        if( ! $this->order ) {
            $this->order = Order::create($data); 
        }

        if( $unit && $this->order && $this->product ) {
            $params['product_name'] = $this->product->name;
            $params['product_id']   = $this->product->id;
            $params['unit_name']    = $unit->name; 
            $params['unit_id']      = $unit->id; 
            $params['quantity']     = $unit->newQty;
            $params['unit_quantity'] = $unit->quantity; 
            $params['selling_price'] = $unit->selling_price; 
            $params['buying_price']  = $unit->buying_price; 
            $params['discount'] = _from($row, "discount") ?? 0 ; 
            $this->order->stocks()->create($params);
        }


    }



}
