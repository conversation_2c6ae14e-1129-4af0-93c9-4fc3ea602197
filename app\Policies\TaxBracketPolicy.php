<?php

namespace App\Policies;

use App\Models\User;
use App\Models\TaxBracket;
use Illuminate\Auth\Access\HandlesAuthorization;

class TaxBracketPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view tax brackets');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, TaxBracket $taxBracket): bool
    {
        return $user->can('view tax brackets');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create tax brackets');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, TaxBracket $taxBracket): bool
    {
        return $user->can('update tax brackets');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, TaxBracket $taxBracket): bool
    {
        return $user->can('delete tax brackets');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, TaxBracket $taxBracket): bool
    {
        return $user->can('delete tax brackets');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, TaxBracket $taxBracket): bool
    {
        return $user->can('delete tax brackets');
    }
}
