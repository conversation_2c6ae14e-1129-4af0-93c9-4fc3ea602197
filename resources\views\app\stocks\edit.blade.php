@extends('layouts.app')

@push('styles')
<style>
    .content {
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
    }

    .content.loaded {
        opacity: 1;
    }

    .form-card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.75rem;
    }

    .form-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 0.75rem 0.75rem 0 0 !important;
        border: none;
    }

    .form-section {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .form-section-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .form-section-title i {
        margin-right: 0.5rem;
        color: #6c757d;
    }

    .form-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border-radius: 0.5rem;
        border: 1px solid #e9ecef;
        transition: all 0.15s ease-in-out;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn {
        border-radius: 0.5rem;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
    }

    .stock-info-alert {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border: none;
        border-radius: 0.75rem;
        color: #1565c0;
    }
</style>
@endpush

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">pencil</x-slot>
        <x-slot name="title">Edit Stock</x-slot>
        <x-slot name="subtitle">Update stock information and quantities</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('stocks.index') }}">Stock Inventory</a></li>
            <li class="breadcrumb-item"><a href="{{ route('stocks.show', $stock) }}">Stock Details</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit Stock</li>
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group">
                <a href="{{ route('stocks.show', $stock) }}" class="btn btn-outline-info btn-sm">
                    <i class="bi bi-eye me-1"></i>View Details
                </a>
                <a href="{{ route('stocks.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Stock
                </a>
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Current Stock Info Alert -->
    <div class="alert stock-info-alert mb-4">
        <div class="d-flex align-items-center">
            <i class="bi bi-info-circle me-2"></i>
            <div>
                <strong>Editing: {{ $stock->product->name ?? 'Unknown Product' }}</strong>
                <br>
                <small>Current Quantity: {{ number_format($stock->quantity) }} |
                       Current Value: {{ _money(($stock->quantity ?? 0) * ($stock->buying_price ?? 0)) }} |
                       Last Updated: {{ $stock->updated_at->format('M d, Y') }}</small>
            </div>
        </div>
    </div>

    <!-- Stock Form -->
    <div class=" justify-content-center">
        <div >
            <div class="card form-card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-pencil me-2"></i>Update Stock Information
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('stocks.update', $stock) }}">
                        @csrf
                        @method('PUT')

                        @include('app.stocks.form-inputs')

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                            <div class="btn-group">
                                <a href="{{ route('stocks.show', $stock) }}" class="btn btn-outline-info">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                <a href="{{ route('stocks.index') }}" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-circle me-1"></i>Cancel
                                </a>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-1"></i>Update Stock
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- End Stock Form -->
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Show content after page loads to prevent flash
    $('.content').addClass('loaded');

    // Form validation feedback
    $('form').on('submit', function() {
        $(this).find('button[type="submit"]').prop('disabled', true).html(
            '<i class="bi bi-arrow-clockwise spin me-1"></i>Updating...'
        );
    });

    // Highlight changed fields
    $('.form-control, .form-select').on('change', function() {
        $(this).addClass('border-warning');
    });
});
</script>
@endpush
