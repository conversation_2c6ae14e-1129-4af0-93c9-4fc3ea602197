<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Notification')</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }

        .email-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .email-header .company-info {
            font-size: 14px;
            opacity: 0.9;
        }

        .email-body {
            padding: 40px 30px;
        }

        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #2c3e50;
        }

        .content {
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .highlight-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 25px 0;
            text-align: center;
        }

        .info-box {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin: 25px 0;
            border-radius: 4px;
        }

        .success-box {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 20px;
            margin: 25px 0;
            border-radius: 4px;
        }

        .warning-box {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 20px;
            margin: 25px 0;
            border-radius: 4px;
        }

        .error-box {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 20px;
            margin: 25px 0;
            border-radius: 4px;
        }

        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .details-table th,
        .details-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .details-table th {
            background-color: #f5f5f5;
            font-weight: 600;
            color: #555;
        }

        .details-table tr:last-child td {
            border-bottom: none;
        }

        .amount {
            font-weight: 600;
            color: #2e7d32;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 10px 5px;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-success {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
        }

        .btn-primary {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
        }

        .email-footer {
            background-color: #f8f9fa;
            padding: 30px 20px;
            text-align: center;
            border-top: 1px solid #e0e0e0;
        }

        .email-footer p {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }

        .social-links {
            margin: 20px 0;
        }

        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #667eea;
            text-decoration: none;
        }

        .divider {
            height: 1px;
            background: linear-gradient(to right, transparent, #e0e0e0, transparent);
            margin: 30px 0;
        }

        @media only screen and (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .email-body {
                padding: 20px 15px;
            }
            
            .email-header {
                padding: 20px 15px;
            }
            
            .details-table {
                font-size: 14px;
            }
            
            .details-table th,
            .details-table td {
                padding: 8px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <h1>{{ config('app.name', 'Company Name') }}</h1>
            <div class="company-info">
                @if(auth()->check() && auth()->user()->branch)
                    {{ auth()->user()->branch->address }}<br>
                    Phone: {{ auth()->user()->branch->phone }}
                @else
                    Professional Payroll & HR Management System
                @endif
            </div>
        </div>

        <div class="email-body">
            @yield('content')
        </div>

        <div class="email-footer">
            <div class="divider"></div>
            <p><strong>{{ config('app.name', 'Company Name') }}</strong></p>
            <p>This is an automated message. Please do not reply to this email.</p>
            <p>If you have any questions, please contact our HR department.</p>
            
            <div class="social-links">
                <a href="#">Privacy Policy</a> |
                <a href="#">Terms of Service</a> |
                <a href="#">Contact Support</a>
            </div>
            
            <p style="font-size: 12px; color: #999;">
                © {{ date('Y') }} {{ config('app.name', 'Company Name') }}. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
