<?php

namespace App\Http\Controllers;

use App\Models\BankAccount;
use App\Models\BankReconciliation;
use App\Models\BankReconciliationItem;
use App\Models\JournalEntryLine;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BankReconciliationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', BankReconciliation::class);

        $search = $request->get('search', '');
        $bankAccountId = $request->get('bank_account_id', '');
        $status = $request->get('status', '');

        $bankReconciliations = BankReconciliation::search($search);
        
        if ($bankAccountId) {
            $bankReconciliations->where('bank_account_id', $bankAccountId);
        }
        
        if ($status) {
            $bankReconciliations->where('status', $status);
        }
        
        $bankReconciliations = $bankReconciliations->latest('statement_date')
            ->paginate()
            ->withQueryString();

        $bankAccounts = BankAccount::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.bank_reconciliations.index', compact(
            'bankReconciliations', 
            'search', 
            'bankAccounts',
            'bankAccountId',
            'status'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', BankReconciliation::class);

        $bankAccounts = BankAccount::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.bank_reconciliations.create', compact('bankAccounts'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', BankReconciliation::class);

        $validated = $request->validate([
            'bank_account_id' => 'required|exists:bank_accounts,id',
            'statement_date' => 'required|date',
            'statement_balance' => 'required|numeric',
            'notes' => 'nullable|string',
        ]);

        // Get bank account
        $bankAccount = BankAccount::findOrFail($validated['bank_account_id']);
        
        // Check if there's already a reconciliation in progress
        $inProgressReconciliation = BankReconciliation::where('bank_account_id', $bankAccount->id)
            ->where('status', '!=', 'completed')
            ->first();
            
        if ($inProgressReconciliation) {
            return redirect()
                ->back()
                ->withInput()
                ->withError('There is already a reconciliation in progress for this bank account.');
        }
        
        // Get book balance (current balance from GL account)
        $bookBalance = $bankAccount->glAccount->balance;
        
        // Create bank reconciliation
        $bankReconciliation = BankReconciliation::create([
            'bank_account_id' => $validated['bank_account_id'],
            'statement_date' => $validated['statement_date'],
            'statement_balance' => $validated['statement_balance'],
            'book_balance' => $bookBalance,
            'reconciled_balance' => 0,
            'difference' => $validated['statement_balance'] - $bookBalance,
            'status' => 'draft',
            'notes' => $validated['notes'],
        ]);
        
        // Get unreconciled transactions
        $this->loadUnreconciledTransactions($bankReconciliation);

        return redirect()
            ->route('bank-reconciliations.edit', $bankReconciliation)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\BankReconciliation  $bankReconciliation
     * @return \Illuminate\Http\Response
     */
    public function show(BankReconciliation $bankReconciliation)
    {
        $this->authorize('view', $bankReconciliation);

        $bankReconciliationItems = $bankReconciliation->bankReconciliationItems()
            ->orderBy('transaction_date')
            ->get();

        return view('app.bank_reconciliations.show', compact(
            'bankReconciliation', 
            'bankReconciliationItems'
        ));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\BankReconciliation  $bankReconciliation
     * @return \Illuminate\Http\Response
     */
    public function edit(BankReconciliation $bankReconciliation)
    {
        $this->authorize('update', $bankReconciliation);
        
        if ($bankReconciliation->status === 'completed') {
            return redirect()
                ->route('bank-reconciliations.show', $bankReconciliation)
                ->withError('Completed reconciliations cannot be edited.');
        }

        $bankReconciliationItems = $bankReconciliation->bankReconciliationItems()
            ->orderBy('transaction_date')
            ->get();

        return view('app.bank_reconciliations.edit', compact(
            'bankReconciliation', 
            'bankReconciliationItems'
        ));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\BankReconciliation  $bankReconciliation
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, BankReconciliation $bankReconciliation)
    {
        $this->authorize('update', $bankReconciliation);
        
        if ($bankReconciliation->status === 'completed') {
            return redirect()
                ->route('bank-reconciliations.show', $bankReconciliation)
                ->withError('Completed reconciliations cannot be edited.');
        }

        $validated = $request->validate([
            'statement_date' => 'required|date',
            'statement_balance' => 'required|numeric',
            'notes' => 'nullable|string',
            'items' => 'array',
            'items.*.id' => 'required|exists:bank_reconciliation_items,id',
            'items.*.is_reconciled' => 'boolean',
        ]);

        DB::beginTransaction();
        
        try {
            // Update reconciliation
            $bankReconciliation->update([
                'statement_date' => $validated['statement_date'],
                'statement_balance' => $validated['statement_balance'],
                'notes' => $validated['notes'],
            ]);
            
            // Update reconciliation items
            if (isset($validated['items'])) {
                foreach ($validated['items'] as $item) {
                    $reconciliationItem = BankReconciliationItem::find($item['id']);
                    $reconciliationItem->update([
                        'is_reconciled' => $item['is_reconciled'] ?? false,
                    ]);
                }
            }
            
            // Recalculate reconciled balance and difference
            $this->recalculateReconciliation($bankReconciliation);
            
            DB::commit();
            
            return redirect()
                ->route('bank-reconciliations.edit', $bankReconciliation)
                ->withSuccess(__('crud.common.saved'));
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\BankReconciliation  $bankReconciliation
     * @return \Illuminate\Http\Response
     */
    public function destroy(BankReconciliation $bankReconciliation)
    {
        $this->authorize('delete', $bankReconciliation);
        
        if ($bankReconciliation->status === 'completed') {
            return redirect()
                ->route('bank-reconciliations.index')
                ->withError('Completed reconciliations cannot be deleted.');
        }

        DB::beginTransaction();
        
        try {
            // Delete reconciliation items
            $bankReconciliation->bankReconciliationItems()->delete();
            
            // Delete reconciliation
            $bankReconciliation->delete();
            
            DB::commit();
            
            return redirect()
                ->route('bank-reconciliations.index')
                ->withSuccess(__('crud.common.removed'));
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
    
    /**
     * Complete the bank reconciliation.
     *
     * @param  \App\Models\BankReconciliation  $bankReconciliation
     * @return \Illuminate\Http\Response
     */
    public function complete(BankReconciliation $bankReconciliation)
    {
        $this->authorize('update', $bankReconciliation);
        
        if ($bankReconciliation->status === 'completed') {
            return redirect()
                ->route('bank-reconciliations.show', $bankReconciliation)
                ->withError('Reconciliation is already completed.');
        }
        
        // Check if reconciliation is balanced
        if (abs($bankReconciliation->difference) > 0.01) {
            return redirect()
                ->route('bank-reconciliations.edit', $bankReconciliation)
                ->withError('Reconciliation is not balanced. The difference must be zero.');
        }

        DB::beginTransaction();
        
        try {
            // Update reconciliation status
            $bankReconciliation->update([
                'status' => 'completed',
                'completed_by' => auth()->id(),
                'completed_at' => now(),
            ]);
            
            // Update bank account
            $bankAccount = $bankReconciliation->bankAccount;
            $bankAccount->update([
                'last_reconciliation_date' => $bankReconciliation->statement_date,
                'current_balance' => $bankReconciliation->reconciled_balance,
            ]);
            
            DB::commit();
            
            return redirect()
                ->route('bank-reconciliations.show', $bankReconciliation)
                ->withSuccess('Reconciliation has been completed successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
    
    /**
     * Refresh unreconciled transactions.
     *
     * @param  \App\Models\BankReconciliation  $bankReconciliation
     * @return \Illuminate\Http\Response
     */
    public function refresh(BankReconciliation $bankReconciliation)
    {
        $this->authorize('update', $bankReconciliation);
        
        if ($bankReconciliation->status === 'completed') {
            return redirect()
                ->route('bank-reconciliations.show', $bankReconciliation)
                ->withError('Completed reconciliations cannot be refreshed.');
        }

        DB::beginTransaction();
        
        try {
            // Delete existing unreconciled items
            $bankReconciliation->bankReconciliationItems()
                ->where('is_reconciled', false)
                ->delete();
            
            // Load unreconciled transactions
            $this->loadUnreconciledTransactions($bankReconciliation);
            
            // Recalculate reconciled balance and difference
            $this->recalculateReconciliation($bankReconciliation);
            
            DB::commit();
            
            return redirect()
                ->route('bank-reconciliations.edit', $bankReconciliation)
                ->withSuccess('Unreconciled transactions have been refreshed.');
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
    
    /**
     * Load unreconciled transactions for a bank reconciliation.
     *
     * @param  \App\Models\BankReconciliation  $bankReconciliation
     * @return void
     */
    private function loadUnreconciledTransactions(BankReconciliation $bankReconciliation)
    {
        $bankAccount = $bankReconciliation->bankAccount;
        $glAccountId = $bankAccount->gl_account_id;
        
        // Get last reconciliation date
        $lastReconciliationDate = $bankAccount->last_reconciliation_date ?? '1900-01-01';
        
        // Get journal entry lines for this account
        $journalEntryLines = JournalEntryLine::where('account_id', $glAccountId)
            ->whereHas('journalEntry', function ($query) use ($lastReconciliationDate, $bankReconciliation) {
                $query->where('status', 'posted')
                    ->where('entry_date', '>', $lastReconciliationDate)
                    ->where('entry_date', '<=', $bankReconciliation->statement_date);
            })
            ->with('journalEntry')
            ->get();
            
        foreach ($journalEntryLines as $line) {
            // Check if this line is already in a reconciliation item
            $existingItem = BankReconciliationItem::where('journal_entry_line_id', $line->id)
                ->exists();
                
            if (!$existingItem) {
                // Create reconciliation item
                BankReconciliationItem::create([
                    'bank_reconciliation_id' => $bankReconciliation->id,
                    'journal_entry_line_id' => $line->id,
                    'transaction_date' => $line->journalEntry->entry_date,
                    'reference' => $line->journalEntry->reference_number,
                    'description' => $line->description ?? $line->journalEntry->description,
                    'debit' => $line->debit,
                    'credit' => $line->credit,
                    'is_reconciled' => false,
                    'transaction_type' => $line->debit > 0 ? 'deposit' : 'withdrawal',
                ]);
            }
        }
    }
    
    /**
     * Recalculate reconciled balance and difference.
     *
     * @param  \App\Models\BankReconciliation  $bankReconciliation
     * @return void
     */
    private function recalculateReconciliation(BankReconciliation $bankReconciliation)
    {
        // Get reconciled items
        $reconciledItems = $bankReconciliation->bankReconciliationItems()
            ->where('is_reconciled', true)
            ->get();
            
        // Calculate reconciled balance
        $reconciledBalance = $bankReconciliation->bankAccount->glAccount->balance;
        
        // Calculate difference
        $difference = $bankReconciliation->statement_balance - $reconciledBalance;
        
        // Update reconciliation
        $bankReconciliation->update([
            'reconciled_balance' => $reconciledBalance,
            'difference' => $difference,
        ]);
    }
}
