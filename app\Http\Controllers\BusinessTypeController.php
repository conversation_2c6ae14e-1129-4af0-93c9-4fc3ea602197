<?php

namespace App\Http\Controllers;

use App\Models\Status;
use App\Models\BusinessType;
use Illuminate\Http\Request;
use App\Http\Requests\BusinessTypeStoreRequest;
use App\Http\Requests\BusinessTypeUpdateRequest;

class BusinessTypeController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // $this->authorize('view-any', BusinessType::class);

        $search = $request->get('search', '');

        $businessTypes = BusinessType::search($search)
            ->latest()
            ->paginate()
            ->withQueryString();

        return view(
            'app.business_types.index',
            compact('businessTypes', 'search')
        );
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', BusinessType::class);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');

        return view('app.business_types.create', compact('statuses'));
    }

    /**
     * @param \App\Http\Requests\BusinessTypeStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(BusinessTypeStoreRequest $request)
    {
        $this->authorize('create', BusinessType::class);

        $validated = $request->validated();

        $businessType = BusinessType::create($validated);

        return redirect()
            ->route('business-types.edit', $businessType)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\BusinessType $businessType
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, BusinessType $businessType)
    {
        $this->authorize('view', $businessType);

        return view('app.business_types.show', compact('businessType'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\BusinessType $businessType
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, BusinessType $businessType)
    {
        $this->authorize('update', $businessType);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');

        return view(
            'app.business_types.edit',
            compact('businessType', 'statuses')
        );
    }

    /**
     * @param \App\Http\Requests\BusinessTypeUpdateRequest $request
     * @param \App\Models\BusinessType $businessType
     * @return \Illuminate\Http\Response
     */
    public function update(
        BusinessTypeUpdateRequest $request,
        BusinessType $businessType
    ) {
        $this->authorize('update', $businessType);

        $validated = $request->validated();

        $businessType->update($validated);

        return redirect()
            ->route('business-types.edit', $businessType)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\BusinessType $businessType
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, BusinessType $businessType)
    {
        $this->authorize('delete', $businessType);

        $businessType->delete();

        return redirect()
            ->route('business-types.index')
            ->withSuccess(__('crud.common.removed'));
    }

    public function setBusinessType(Request $request, BusinessType $businessType){
        auth()->user()->update([ "business_type_id" => $businessType->id ]);
        if ( ! auth()->user()->branch ) {
            $branch = \App\Models\Branch::first();
            if ( $branch ) auth()->user()->update(['branch_id' => $branch->id ]);
        }
        return redirect()->back();
    }

}
