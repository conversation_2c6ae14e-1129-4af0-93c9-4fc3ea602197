<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;
use Facades\App\Cache\Repo;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Branch extends Model implements HasMedia
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;
    use InteractsWithMedia;
    protected $connection = "tenant";


    protected $fillable = [
        'name',
        'address',
        'phone',
        'email',
        'status_id',
        'created_by',
        'updated_by',
        'business_type_id',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];

    protected $appends = ['image'];

    public function getImageAttribute(){
        $file = $this->file();
        return $file ? $file->getUrl() : '';
    }

    public function file($collection = "image"){
        return $this->getMedia($collection)->first();
    }

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function getStock() {
        request()->merge(["branch_id" => $this->id]);
        return Repo::getStock();
    }

    public function getSale() {
        request()->merge(["branch_id" => $this->id]);
        return Repo::getSale();
    }

    public function getOrder() {
        request()->merge(["branch_id" => $this->id]);
        return Repo::getOrder();
    }



}
