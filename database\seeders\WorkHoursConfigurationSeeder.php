<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\WorkHoursConfiguration;

class WorkHoursConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Check if a default configuration already exists
        if (WorkHoursConfiguration::count() == 0) {
            WorkHoursConfiguration::create([
                'name' => 'Standard Work Hours',
                'start_time' => '09:00:00',
                'end_time' => '17:00:00',
                'standard_hours_per_day' => 8.00,
                'overtime_rate' => 1.50,
                'is_active' => true,
                'description' => 'Default work hours configuration - 9 AM to 5 PM with 8 hours standard and 1.5x overtime rate',
                'created_by' => 1,
            ]);
        }
    }
}
