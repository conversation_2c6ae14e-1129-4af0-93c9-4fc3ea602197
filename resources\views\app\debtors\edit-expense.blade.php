
@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">pencil-square</x-slot>
        <x-slot name="title">Edit Expense</x-slot>
        <x-slot name="subtitle">Update expense information</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ url()->previous() }}">Expenses</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ url()->previous() }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Expenses
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <div class="col-lg-8 mb-3 mb-lg-0">
            <!-- Card -->
            <div class="card mb-3 mb-lg-5">
                <!-- Header -->
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-header-title">Expense Details</h4>
                        <span class="badge bg-primary">ID: {{ $debt->id }}</span>
                    </div>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <x-form
                        method="PUT"
                        action="{{ route('debts.update', $debt) }}"
                        has-files
                        id="expenseForm"
                    >
                        <div id="supplier-debtor-modal-el">
                            <input type="hidden" value="{{ $debt->type }}" name="type" required>
                            
                            <div class="row mb-4">
                                <div class="col-sm-6 mb-4">
                                    <label for="category" class="form-label">Category <i class="bi-asterisk text-danger small"></i></label>
                                    <div class="tom-select-custom">
                                        <select class="js-select form-select @error('category') is-invalid @enderror" 
                                            id="category" name="category" required
                                            data-hs-tom-select-options='{
                                                "placeholder": "Select a category..."
                                            }'>
                                            @foreach(App\Models\Category::where("applied_to", 'debtors')->get() as $category)
                                                <option value="{{ $category->name }}" @if($category->name == $debt->category) selected @endif>
                                                    {{ $category->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @error('category')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                @if(auth()->user()->isSuperAdmin())
                                <div class="col-sm-6 mb-4">
                                    <label for="branch_id" class="form-label">Branch <i class="bi-asterisk text-danger small"></i></label>
                                    <div class="tom-select-custom">
                                        <select class="js-select form-select @error('branch_id') is-invalid @enderror" 
                                            id="branch_id" name="branch_id" required
                                            data-hs-tom-select-options='{
                                                "placeholder": "Select a branch..."
                                            }'>
                                            @foreach(App\Models\Branch::get() as $branch)
                                                <option value="{{ $branch->id }}" @if($branch->id == $debt->branch_id) selected @endif>
                                                    {{ $branch->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @error('branch_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                @endif

                                <div class="col-sm-12 mb-4">
                                    <label for="amount_total" class="form-label">Amount <i class="bi-asterisk text-danger small"></i></label>
                                    <div class="input-group">
                                        <span class="input-group-text">{{ config('app.currency_symbol') }}</span>
                                        <input type="number" class="form-control @error('amount_total') is-invalid @enderror" 
                                            id="amount_total" name="amount_total" 
                                            value="{{ $debt->amount_total }}" required step="0.01" min="0">
                                    </div>
                                    @error('amount_total')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-sm-12 mb-4">
                                    <label for="description" class="form-label">Note <i class="bi-asterisk text-danger small"></i></label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                        id="description" name="description" rows="4" 
                                        placeholder="Add details about this expense" required>{{ $debt->description }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </x-form>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>

        <div class="col-lg-4">
            <!-- Card -->
            <div class="card">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Expense Summary</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5>Reference ID</h5>
                            <span class="badge bg-soft-primary text-primary">{{ $debt->debt_id }}</span>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5>Created Date</h5>
                            <span>{{ $debt->created_at->format('M d, Y') }}</span>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5>Created By</h5>
                            <span>{{ $debt->createdBy->name ?? 'N/A' }}</span>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5>Status</h5>
                            @if($debt->amount_total <= $debt->amount_paid)
                                <span class="badge bg-success">Fully Paid</span>
                            @else
                                <span class="badge bg-warning">Partially Paid</span>
                            @endif
                        </div>
                        
                        @if($debt->amount_paid > 0)
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5>Amount Paid</h5>
                            <span class="text-success fw-semibold">{{ _money($debt->amount_paid) }}</span>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5>Balance</h5>
                            <span class="text-danger fw-semibold">{{ _money($debt->amount_total - $debt->amount_paid) }}</span>
                        </div>
                        @endif
                    </div>
                    
                    <hr>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" form="expenseForm" class="btn btn-primary">
                            <i class="bi-check-circle me-1"></i> @lang('crud.common.update')
                        </button>
                        
                        <a href="{{ url()->previous() }}" class="btn btn-white">
                            <i class="bi-x-circle me-1"></i> Cancel
                        </a>
                    </div>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
            
            <!-- Card -->
            <div class="card mt-3">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Payment History</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    @if($debt->payments && count($debt->payments) > 0)
                        <ul class="list-group mb-3">
                            @foreach($debt->payments as $payment)
                            <li class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">{{ $payment->created_at->format('M d, Y') }}</h6>
                                        <small class="text-muted">{{ $payment->payment_method }}</small>
                                    </div>
                                    <span class="fw-semibold">{{ _money($payment->amount) }}</span>
                                </div>
                            </li>
                            @endforeach
                        </ul>
                    @else
                        <div class="text-center py-4">
                            <i class="bi bi-cash text-muted" style="font-size: 2rem;"></i>
                            <p class="mt-2">No payment records found</p>
                        </div>
                    @endif
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script type="text/javascript">
    new Vue({
        el: "#supplier-debtor-modal-el",
        data() {
            return {
                customer_id: null,
                customer: null,
            }
        },
        methods: {
            getCustomerBalance() {
                axios.get('/ajax-customer-balance/' + this.customer_id).then(res => {
                    this.customer = res.data;
                    console.log(this.customer)
                });
            }
        },
        created() {
            // this.getProducts();
        }
    })
</script>
@endpush
