@extends('layouts.app')

@section('content')
<div class="container">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-header-title">@lang('crud.warehouses.show_title')</h1>
                <p class="page-header-text">Warehouse details and information</p>
            </div>
            <div class="col-auto">
                <div class="btn-group" role="group">
                    <a href="{{ route('warehouses.index') }}" class="btn btn-white">
                        <i class="bi-arrow-left me-1"></i> @lang('crud.common.back')
                    </a>
                    
                    @can('update', $warehouse)
                    <a href="{{ route('warehouses.edit', $warehouse) }}" class="btn btn-primary">
                        <i class="bi-pencil-fill me-1"></i> @lang('crud.common.edit')
                    </a>
                    @endcan
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Card -->
            <div class="card mb-3 mb-lg-5">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Warehouse Information</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6 mb-4">
                            <label class="form-label">@lang('crud.warehouses.inputs.name')</label>
                            <div class="mb-2">
                                <h5>{{ $warehouse->name ?? '-' }}</h5>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-4">
                            <label class="form-label">@lang('crud.warehouses.inputs.code')</label>
                            <div class="mb-2">
                                <h5>{{ $warehouse->code ?? '-' }}</h5>
                            </div>
                        </div>
                    </div>
                    <!-- End Row -->

                    <div class="row">
                        <div class="col-sm-6 mb-4">
                            <label class="form-label">@lang('crud.warehouses.inputs.address')</label>
                            <div class="mb-2">
                                <span>{{ $warehouse->address ?? '-' }}</span>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-4">
                            <label class="form-label">@lang('crud.warehouses.inputs.phone')</label>
                            <div class="mb-2">
                                <span>{{ $warehouse->phone ?? '-' }}</span>
                            </div>
                        </div>
                    </div>
                    <!-- End Row -->

                    <div class="row">
                        <div class="col-sm-6 mb-4">
                            <label class="form-label">@lang('crud.warehouses.inputs.email')</label>
                            <div class="mb-2">
                                <span>{{ $warehouse->email ?? '-' }}</span>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-4">
                            <label class="form-label">@lang('crud.warehouses.inputs.manager_name')</label>
                            <div class="mb-2">
                                <span>{{ $warehouse->manager_name ?? '-' }}</span>
                            </div>
                        </div>
                    </div>
                    <!-- End Row -->

                    <div class="row">
                        <div class="col-sm-6 mb-4">
                            <label class="form-label">@lang('crud.warehouses.inputs.branch_id')</label>
                            <div class="mb-2">
                                <span>{{ optional($warehouse->branch)->name ?? '-' }}</span>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-4">
                            <label class="form-label">@lang('crud.warehouses.inputs.is_active')</label>
                            <div class="mb-2">
                                <span class="badge bg-{{ $warehouse->is_active ? 'success' : 'danger' }}">
                                    {{ $warehouse->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <!-- End Row -->

                    <div class="mb-4">
                        <label class="form-label">@lang('crud.warehouses.inputs.description')</label>
                        <div class="mb-2">
                            <p>{{ $warehouse->description ?? '-' }}</p>
                        </div>
                    </div>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>

        <div class="col-lg-4">
            <!-- Card -->
            <div class="card mb-3 mb-lg-5">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Warehouse Image</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="text-center">
                        @if($warehouse->image)
                            <img class="img-fluid rounded" src="{{ \Storage::url($warehouse->image) }}" alt="Warehouse Image">
                        @else
                            <div class="text-center p-4">
                                <img class="mb-3" src="{{ asset('assets/svg/illustrations/oc-image.svg') }}" alt="Image Description" style="width: 10rem;">
                                <p class="mb-0">No image available</p>
                            </div>
                        @endif
                    </div>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->

            <!-- Card -->
            <div class="card">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Actions</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @can('create', App\Models\Warehouse::class)
                        <a href="{{ route('warehouses.create') }}" class="btn btn-white">
                            <i class="bi-plus-circle me-1"></i> @lang('crud.common.create')
                        </a>
                        @endcan

                        @can('delete', $warehouse)
                        <form action="{{ route('warehouses.destroy', $warehouse) }}" method="POST">
                            @csrf @method('DELETE')
                            <button type="submit" class="btn btn-danger w-100" onclick="return confirm('{{ __('crud.common.are_you_sure') }}')">
                                <i class="bi-trash me-1"></i> @lang('crud.common.delete')
                            </button>
                        </form>
                        @endcan
                    </div>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>
    </div>
</div>
@endsection
