<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;

class Location extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'code',
        'description',
        'status_id',
        'created_by',
        'updated_by',
        'business_type_id',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];

    /**
     * Get the status that owns the location.
     */
    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    /**
     * Get the business type that owns the location.
     */
    public function businessType()
    {
        return $this->belongsTo(BusinessType::class);
    }

    /**
     * Get the user who created the location.
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the location.
     */
    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope a query to only include active locations.
     */
    public function scopeActive($query)
    {
        return $query->where('status_id', 1);
    }

    /**
     * Scope a query to only include inactive locations.
     */
    public function scopeInactive($query)
    {
        return $query->where('status_id', 2);
    }

    /**
     * Get the location's display name with code.
     */
    public function getDisplayNameAttribute()
    {
        return $this->code ? "{$this->name} ({$this->code})" : $this->name;
    }

    /**
     * Check if the location is active.
     */
    public function isActive()
    {
        return $this->status_id == 1;
    }

    /**
     * Check if the location is inactive.
     */
    public function isInactive()
    {
        return $this->status_id == 2;
    }
}
