<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\StockResource;
use App\Http\Resources\StockCollection;

class UserStocksController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, User $user)
    {
        $this->authorize('view', $user);

        $search = $request->get('search', '');

        $stocks = $user
            ->stocks3()
            ->search($search)
            ->latest()
            ->paginate();

        return new StockCollection($stocks);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, User $user)
    {
        $this->authorize('create', Stock::class);

        $validated = $request->validate([
            'product_id' => ['nullable', 'exists:products,id'],
            'quantity' => ['nullable', 'numeric'],
            'balance' => ['nullable', 'numeric'],
            'supplier_id' => ['nullable', 'exists:suppliers,id'],
            'stock_id' => ['nullable', 'exists:stocks,id'],
            'description' => ['nullable', 'max:255', 'string'],
        ]);

        $stock = $user->stocks3()->create($validated);

        return new StockResource($stock);
    }
}
