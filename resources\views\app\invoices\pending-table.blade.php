<!-- Pending Invoices Table -->
<div class="card-body p-0">
    <div class="table-responsive">
        <table id="pendingTable" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
            <thead class="thead-light">
                <tr>
                    <th class="text-left">Invoice #</th>
                    <th class="text-left">@lang('crud.customers.singular')</th>
                    <th class="text-left">Date</th>
                    <th class="text-left">Amount</th>
                    <th class="text-left">Discount</th>
                    <th class="text-left">Status</th>
                    <th class="text-left">Created By</th>
                    <th class="text-center">Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse($pendingInvoices as $invoice)
                <tr>
                    <td>
                        <a href="{{ route('invoices.show', $invoice) }}" class="text-primary fw-semibold">
                            #{{ $invoice->invoice_id ?? '-' }}
                        </a>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar avatar-xs avatar-circle">
                                    <span class="avatar-initials">{{ substr($invoice->customer->name ?? 'C', 0, 1) }}</span>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <h6 class="mb-0">{{ $invoice->customer->name ?? '-' }}</h6>
                                <small class="text-muted">{{ $invoice->customer->phone ?? '' }}</small>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex flex-column">
                            <span>{{ optional($invoice->created_at)->format('M d, Y') }}</span>
                            <small class="text-muted">{{ optional($invoice->created_at)->format('h:i A') }}</small>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex flex-column">
                            <span class="fw-semibold">{{ _money($invoice->amount_total) }}</span>
                            <small class="text-muted">Paid: {{ _money($invoice->amount_paid) }}</small>
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-warning">{{ _money($invoice->discount) }}</span>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-warning me-2">Pending Approval</span>
                            <i class="bi bi-hourglass-split text-warning"></i>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar avatar-xs avatar-soft-info avatar-circle">
                                    <span class="avatar-initials">{{ substr($invoice->createdBy->name ?? 'U', 0, 1) }}</span>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <span class="d-block">{{ $invoice->createdBy->name ?? '-' }}</span>
                            </div>
                        </div>
                    </td>
                    <td class="text-center" style="width: 150px;">
                        <div class="btn-group" role="group" aria-label="Row Actions">
                            @can('update', $invoice)
                            <a href="{{ route('invoices.edit', $invoice) }}" class="btn btn-soft-primary btn-sm" title="Edit Invoice">
                                <i class="bi bi-pencil-square"></i>
                            </a>
                            @endcan

                            @if(auth()->user()->can('approve discount'))
                            <button type="button" class="btn btn-soft-success btn-sm pending-invoice-btn"
                                    data-bs-toggle="modal" data-id="{{ $invoice->id }}" data-bs-target=".pending-invoice-modal"
                                    title="Approve/Reject Invoice">
                                <i class="bi bi-check-circle"></i>
                            </button>
                            @endif

                            @can('view', $invoice)
                            <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-soft-info btn-sm" title="View Invoice">
                                <i class="bi bi-eye"></i>
                            </a>
                            @endcan

                            @can('delete', $invoice)
                            <button type="button" class="btn btn-soft-danger btn-sm"
                                    data-bs-toggle="modal" data-bs-target="#deleteInvoice{{ $invoice->id }}"
                                    title="Delete Invoice">
                                <i class="bi bi-trash"></i>
                            </button>

                            <!-- Delete Modal -->
                            <div class="modal fade" id="deleteInvoice{{ $invoice->id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Confirm Delete</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Are you sure you want to delete Invoice #{{ $invoice->invoice_id }}?</p>
                                            <p class="text-danger mb-0">This action cannot be undone.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                                            <form action="{{ route('invoices.destroy', $invoice) }}" method="POST">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger">Delete Invoice</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endcan
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="8" class="text-center p-5">
                        <div class="avatar avatar-lg avatar-soft-warning avatar-circle mb-3 mx-auto">
                            <span class="avatar-initials"><i class="bi bi-hourglass-split"></i></span>
                        </div>
                        <h5>No Pending Invoices</h5>
                        <p class="mb-0 text-muted">There are no invoices pending approval at this time.</p>
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination and Summary Footer -->
@if(count($pendingInvoices) > 0)
<div class="card-footer bg-light">
    <div class="row align-items-center">
        <div class="col-md-6 mb-2 mb-md-0">
            <span>Showing {{ count($pendingInvoices) }} pending invoices</span>
        </div>
        <div class="col-md-6">
            <div class="d-flex justify-content-md-end">
                <!-- Pagination would go here if needed -->
            </div>
        </div>
    </div>
</div>
@endif