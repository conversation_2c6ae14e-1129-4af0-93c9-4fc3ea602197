@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                Assign Deductions & Contributions to {{ $employee->name }}
            </h4>
        </div>

        <div class="card-body">
            <form action="{{ route('employees.deduction-contributions.assign', $employee) }}" method="POST">
                @csrf

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="start_date">Start Date</label>
                            <input type="date" name="start_date" id="start_date" class="form-control" value="{{ old('start_date', date('Y-m-d')) }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="end_date">End Date (Optional)</label>
                            <input type="date" name="end_date" id="end_date" class="form-control" value="{{ old('end_date') }}">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h5>Deductions</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="select-all-deductions">
                                            </div>
                                        </th>
                                        <th>Name</th>
                                        <th>Formula</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($deductions as $deduction)
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input deduction-checkbox" type="checkbox" name="deduction_contribution_ids[]" value="{{ $deduction->id }}" {{ in_array($deduction->id, $assignedIds) ? 'checked' : '' }}>
                                            </div>
                                        </td>
                                        <td>{{ $deduction->name }}</td>
                                        <td>{{ $deduction->formula }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5>Contributions</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="select-all-contributions">
                                            </div>
                                        </th>
                                        <th>Name</th>
                                        <th>Formula</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($contributions as $contribution)
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input contribution-checkbox" type="checkbox" name="deduction_contribution_ids[]" value="{{ $contribution->id }}" {{ in_array($contribution->id, $assignedIds) ? 'checked' : '' }}>
                                            </div>
                                        </td>
                                        <td>{{ $contribution->name }}</td>
                                        <td>{{ $contribution->formula }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('employee-deduction-contributions.index', ['employee_id' => $employee->id]) }}" class="btn btn-light">
                            <i class="bi bi-arrow-left"></i> Back
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Save Assignments
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    $(document).ready(function() {
        // Select/deselect all deductions
        $('#select-all-deductions').click(function() {
            $('.deduction-checkbox').prop('checked', this.checked);
        });

        // If all deduction checkboxes are selected, check the select-all checkbox
        $('.deduction-checkbox').click(function() {
            if ($('.deduction-checkbox').length == $('.deduction-checkbox:checked').length) {
                $('#select-all-deductions').prop('checked', true);
            } else {
                $('#select-all-deductions').prop('checked', false);
            }
        });

        // Select/deselect all contributions
        $('#select-all-contributions').click(function() {
            $('.contribution-checkbox').prop('checked', this.checked);
        });

        // If all contribution checkboxes are selected, check the select-all checkbox
        $('.contribution-checkbox').click(function() {
            if ($('.contribution-checkbox').length == $('.contribution-checkbox:checked').length) {
                $('#select-all-contributions').prop('checked', true);
            } else {
                $('#select-all-contributions').prop('checked', false);
            }
        });
    });
</script>
@endpush
@endsection
