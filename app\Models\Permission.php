<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Role as BaseRole;
use Spatie\Permission\Models\Permission as BasePermission;

class Permission extends BasePermission
{
    use HasFactory;

    protected $connection = "tenant";

    // public function getNameAttribute($value){
    //     return ucfirst($value);
    // }

    // public function setNameAttribute($value){
    //     return strtolower($value);
    // }


}