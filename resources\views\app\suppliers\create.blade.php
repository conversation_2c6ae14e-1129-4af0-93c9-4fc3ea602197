@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">building-add</x-slot>
        <x-slot name="title">Create Supplier</x-slot>
        <x-slot name="subtitle">Add a new supplier to your system</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('suppliers.index') }}">Suppliers</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('suppliers.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Suppliers
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <div class="col-lg-8 mb-3 mb-lg-0">
            <!-- Card -->
            <div class="card mb-3 mb-lg-5">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Supplier Information</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <x-form
                        method="POST"
                        action="{{ route('suppliers.store') }}"
                        id="supplierForm"
                    >
                        @include('app.suppliers.form-inputs')
                    </x-form>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>

        <div class="col-lg-4">
            <!-- Card -->
            <div class="card">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Actions</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <!-- Form Group -->
                        <div class="mb-4">
                            <label for="supplierStatus" class="form-label">Status</label>

                            <div class="form-check form-switch">
                                <input type="checkbox" class="form-check-input" id="supplierStatus" checked form="supplierForm">
                                <label class="form-check-label" for="supplierStatus">Active</label>
                            </div>
                        </div>
                        <!-- End Form Group -->

                        <div class="d-flex justify-content-end">
                            <a href="{{ route('suppliers.index') }}" class="btn btn-white me-2">Cancel</a>
                            <button type="submit" form="supplierForm" class="btn btn-primary">
                                <i class="bi-check-circle me-1"></i> Create
                            </button>
                        </div>
                    </div>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->

            <!-- Card -->
            <div class="card mt-3">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Tips</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <ul class="list-unstyled list-py-2 mb-0">
                        <li>
                            <i class="bi-check-circle text-success me-2"></i>
                            Use the supplier's legal business name
                        </li>
                        <li>
                            <i class="bi-check-circle text-success me-2"></i>
                            Provide accurate contact information
                        </li>
                        <li>
                            <i class="bi-check-circle text-success me-2"></i>
                            Include complete business address
                        </li>
                    </ul>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>
    </div>
</div>
@endsection
