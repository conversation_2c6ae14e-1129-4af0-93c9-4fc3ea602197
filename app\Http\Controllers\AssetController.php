<?php

namespace App\Http\Controllers;

use App\Models\Asset;
use App\Models\AssetCategory;
use App\Models\Branch;
use App\Models\JournalEntry;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AssetController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Asset::class);

        $search = $request->get('search', '');
        $categoryId = $request->get('category_id', '');
        $status = $request->get('status', '');
        $locationId = $request->get('location_id', '');

        $assets = Asset::search($search);
        
        if ($categoryId) {
            $assets->where('asset_category_id', $categoryId);
        }
        
        if ($status) {
            $assets->where('status', $status);
        }
        
        if ($locationId) {
            $assets->where('location_id', $locationId);
        }
        
        $assets = $assets->latest()
            ->paginate()
            ->withQueryString();

        $assetCategories = AssetCategory::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');
            
        $locations = Branch::orderBy('name')
            ->pluck('name', 'id');

        return view('app.assets.index', compact(
            'assets', 
            'search', 
            'assetCategories',
            'locations',
            'categoryId',
            'status',
            'locationId'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', Asset::class);

        $assetCategories = AssetCategory::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');
            
        $locations = Branch::orderBy('name')
            ->pluck('name', 'id');

        return view('app.assets.create', compact('assetCategories', 'locations'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', Asset::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'asset_number' => 'required|string|max:255|unique:assets,asset_number',
            'description' => 'nullable|string',
            'asset_category_id' => 'required|exists:asset_categories,id',
            'acquisition_date' => 'required|date',
            'acquisition_cost' => 'required|numeric|min:0',
            'salvage_value' => 'required|numeric|min:0',
            'location_id' => 'nullable|exists:branches,id',
            'image' => 'nullable|image|max:2048',
        ]);

        // Get asset category details
        $assetCategory = AssetCategory::findOrFail($validated['asset_category_id']);
        
        // Calculate depreciable cost
        $depreciableValue = $validated['acquisition_cost'] - $validated['salvage_value'];
        
        // Set depreciation details from category
        $validated['useful_life_years'] = $assetCategory->useful_life_years;
        $validated['depreciation_rate'] = $assetCategory->depreciation_rate;
        $validated['depreciation_method'] = $assetCategory->depreciation_method;
        $validated['status'] = 'active';
        
        // Set next depreciation date (one month after acquisition)
        $acquisitionDate = new \DateTime($validated['acquisition_date']);
        $nextDepreciationDate = clone $acquisitionDate;
        $nextDepreciationDate->modify('+1 month');
        $validated['next_depreciation_date'] = $nextDepreciationDate->format('Y-m-d');

        DB::beginTransaction();
        
        try {
            // Create asset
            $asset = Asset::create($validated);
            
            // Handle image upload
            if ($request->hasFile('image')) {
                $asset->addMediaFromRequest('image')
                    ->toMediaCollection('image');
            }
            
            DB::commit();
            
            return redirect()
                ->route('assets.show', $asset)
                ->withSuccess(__('crud.common.created'));
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Asset  $asset
     * @return \Illuminate\Http\Response
     */
    public function show(Asset $asset)
    {
        $this->authorize('view', $asset);

        $assetDepreciations = $asset->assetDepreciations()
            ->orderBy('depreciation_date')
            ->get();

        return view('app.assets.show', compact('asset', 'assetDepreciations'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Asset  $asset
     * @return \Illuminate\Http\Response
     */
    public function edit(Asset $asset)
    {
        $this->authorize('update', $asset);
        
        if ($asset->status !== 'active') {
            return redirect()
                ->route('assets.show', $asset)
                ->withError('Only active assets can be edited.');
        }

        $assetCategories = AssetCategory::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');
            
        $locations = Branch::orderBy('name')
            ->pluck('name', 'id');

        return view('app.assets.edit', compact('asset', 'assetCategories', 'locations'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Asset  $asset
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Asset $asset)
    {
        $this->authorize('update', $asset);
        
        if ($asset->status !== 'active') {
            return redirect()
                ->route('assets.show', $asset)
                ->withError('Only active assets can be edited.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'asset_number' => 'required|string|max:255|unique:assets,asset_number,' . $asset->id,
            'description' => 'nullable|string',
            'location_id' => 'nullable|exists:branches,id',
            'image' => 'nullable|image|max:2048',
        ]);

        DB::beginTransaction();
        
        try {
            // Update asset
            $asset->update($validated);
            
            // Handle image upload
            if ($request->hasFile('image')) {
                // Remove old image
                $asset->clearMediaCollection('image');
                
                // Add new image
                $asset->addMediaFromRequest('image')
                    ->toMediaCollection('image');
            }
            
            DB::commit();
            
            return redirect()
                ->route('assets.show', $asset)
                ->withSuccess(__('crud.common.saved'));
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Asset  $asset
     * @return \Illuminate\Http\Response
     */
    public function destroy(Asset $asset)
    {
        $this->authorize('delete', $asset);
        
        if ($asset->status !== 'active') {
            return redirect()
                ->route('assets.index')
                ->withError('Only active assets can be deleted.');
        }
        
        if ($asset->assetDepreciations()->count() > 0) {
            return redirect()
                ->route('assets.index')
                ->withError('Asset has depreciation records and cannot be deleted.');
        }

        DB::beginTransaction();
        
        try {
            // Remove image
            $asset->clearMediaCollection('image');
            
            // Delete asset
            $asset->delete();
            
            DB::commit();
            
            return redirect()
                ->route('assets.index')
                ->withSuccess(__('crud.common.removed'));
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
    
    /**
     * Show the form for disposing an asset.
     *
     * @param  \App\Models\Asset  $asset
     * @return \Illuminate\Http\Response
     */
    public function disposeForm(Asset $asset)
    {
        $this->authorize('update', $asset);
        
        if ($asset->status !== 'active') {
            return redirect()
                ->route('assets.show', $asset)
                ->withError('Only active assets can be disposed.');
        }

        return view('app.assets.dispose', compact('asset'));
    }
    
    /**
     * Dispose the asset.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Asset  $asset
     * @return \Illuminate\Http\Response
     */
    public function dispose(Request $request, Asset $asset)
    {
        $this->authorize('update', $asset);
        
        if ($asset->status !== 'active') {
            return redirect()
                ->route('assets.show', $asset)
                ->withError('Only active assets can be disposed.');
        }

        $validated = $request->validate([
            'disposal_date' => 'required|date',
            'disposal_amount' => 'required|numeric|min:0',
            'disposal_notes' => 'nullable|string',
        ]);

        DB::beginTransaction();
        
        try {
            // Update asset status
            $asset->update([
                'status' => 'disposed',
                'disposal_date' => $validated['disposal_date'],
                'disposal_amount' => $validated['disposal_amount'],
                'disposal_notes' => $validated['disposal_notes'],
            ]);
            
            // Create journal entry for disposal
            $this->createDisposalJournalEntry($asset);
            
            DB::commit();
            
            return redirect()
                ->route('assets.show', $asset)
                ->withSuccess('Asset has been disposed successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
    
    /**
     * Create a journal entry for asset disposal.
     *
     * @param  \App\Models\Asset  $asset
     * @return void
     */
    private function createDisposalJournalEntry(Asset $asset)
    {
        $assetCategory = $asset->assetCategory;
        
        // Calculate gain or loss
        $bookValue = $asset->current_value;
        $disposalAmount = $asset->disposal_amount;
        $gainLoss = $disposalAmount - $bookValue;
        
        // Generate entry number
        $latestEntry = JournalEntry::latest()->first();
        $entryNumber = 'JE-' . date('Y') . '-' . sprintf('%06d', $latestEntry ? ($latestEntry->id + 1) : 1);
        
        // Create journal entry
        $journalEntry = JournalEntry::create([
            'entry_number' => $entryNumber,
            'entry_date' => $asset->disposal_date,
            'reference_number' => 'ASSET-DISPOSAL-' . $asset->asset_number,
            'description' => 'Disposal of asset: ' . $asset->name,
            'entry_type' => 'system',
            'status' => 'posted',
            'posted_by' => auth()->id(),
            'posted_at' => now(),
        ]);
        
        // Create journal entry lines
        
        // Debit accumulated depreciation
        $journalEntry->journalEntryLines()->create([
            'account_id' => $assetCategory->accumulated_depreciation_account_id,
            'description' => 'Accumulated depreciation for ' . $asset->name,
            'debit' => $asset->accumulated_depreciation,
            'credit' => 0,
        ]);
        
        // Credit asset account
        $journalEntry->journalEntryLines()->create([
            'account_id' => $assetCategory->asset_account_id,
            'description' => 'Asset disposal: ' . $asset->name,
            'debit' => 0,
            'credit' => $asset->acquisition_cost,
        ]);
        
        // Debit cash/bank for disposal amount
        if ($disposalAmount > 0) {
            $journalEntry->journalEntryLines()->create([
                'account_id' => 1, // Assuming 1 is the cash/bank account ID
                'description' => 'Proceeds from asset disposal: ' . $asset->name,
                'debit' => $disposalAmount,
                'credit' => 0,
            ]);
        }
        
        // Record gain or loss
        if ($gainLoss != 0) {
            if ($gainLoss > 0) {
                // Gain on disposal
                $journalEntry->journalEntryLines()->create([
                    'account_id' => $assetCategory->gain_loss_account_id,
                    'description' => 'Gain on asset disposal: ' . $asset->name,
                    'debit' => 0,
                    'credit' => abs($gainLoss),
                ]);
            } else {
                // Loss on disposal
                $journalEntry->journalEntryLines()->create([
                    'account_id' => $assetCategory->gain_loss_account_id,
                    'description' => 'Loss on asset disposal: ' . $asset->name,
                    'debit' => abs($gainLoss),
                    'credit' => 0,
                ]);
            }
        }
    }
}
