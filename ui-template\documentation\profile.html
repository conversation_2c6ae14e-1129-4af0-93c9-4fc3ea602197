<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from htmlstream.com/front-dashboard/documentation/profile.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 30 Aug 2022 07:13:03 GMT -->
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Profile - Documentation | Front - Multipurpose Responsive Template</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="../favicon.ico">

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&amp;display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="../assets/css/vendor.min.css">

  <!-- CSS Front Template -->
  <link rel="stylesheet" href="../assets/css/theme.minc619.css?v=1.0">

  <link rel="preload" href="../assets/css/theme.min.css" data-hs-appearance="default" as="style">
  <link rel="preload" href="../assets/css/docs.css" data-hs-appearance="default" as="style">
  <link rel="preload" href="../assets/css/theme-dark.min.css" data-hs-appearance="dark" as="style">

  <style data-hs-appearance-onload-styles>
    *
    {
      transition: unset !important;
    }

    body
    {
      opacity: 0;
    }
  </style>

  <script>
            window.hs_config = {"autopath":"@@autopath","deleteLine":"hs-builder:delete","deleteLine:build":"hs-builder:build-delete","deleteLine:dist":"hs-builder:dist-delete","previewMode":false,"startPath":"/index.html","vars":{"themeFont":"https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap","version":"?v=1.0"},"layoutBuilder":{"extend":{"switcherSupport":true},"header":{"layoutMode":"default","containerMode":"container-fluid"},"sidebarLayout":"default"},"themeAppearance":{"layoutSkin":"default","sidebarSkin":"default","styles":{"colors":{"primary":"#377dff","transparent":"transparent","white":"#fff","dark":"132144","gray":{"100":"#f9fafc","900":"#1e2022"}},"font":"Inter"}},"languageDirection":{"lang":"en"},"skipFilesFromBundle":{"dist":["assets/js/hs.theme-appearance.js","assets/js/hs.theme-appearance-charts.html","assets/js/demo.js"],"build":["assets/css/theme.css","assets/vendor/hs-navbar-vertical-aside/dist/hs-navbar-vertical-aside-mini-cache.html","assets/js/demo.html","assets/css/theme-dark.html","assets/css/docs.html","assets/vendor/icon-set/style.html","assets/js/hs.theme-appearance.html","assets/js/hs.theme-appearance-charts.html","node_modules/chartjs-plugin-datalabels/dist/chartjs-plugin-datalabels.min.html","assets/js/demo.js"]},"minifyCSSFiles":["assets/css/theme.css","assets/css/theme-dark.css"],"copyDependencies":{"dist":{"*assets/js/theme-custom.js":""},"build":{"*assets/js/theme-custom.js":"","node_modules/bootstrap-icons/font/*fonts/**":"assets/css"}},"buildFolder":"","replacePathsToCDN":{},"directoryNames":{"src":"./src","dist":"./dist","build":"./build"},"fileNames":{"dist":{"js":"theme.min.js","css":"theme.min.css"},"build":{"css":"theme.min.css","js":"theme.min.js","vendorCSS":"vendor.min.css","vendorJS":"vendor.min.js"}},"fileTypes":"jpg|png|svg|mp4|webm|ogv|json"}
            window.hs_config.gulpRGBA = (p1) => {
  const options = p1.split(',')
  const hex = options[0].toString()
  const transparent = options[1].toString()

  var c;
  if(/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)){
    c= hex.substring(1).split('');
    if(c.length== 3){
      c= [c[0], c[0], c[1], c[1], c[2], c[2]];
    }
    c= '0x'+c.join('');
    return 'rgba('+[(c>>16)&255, (c>>8)&255, c&255].join(',')+',' + transparent + ')';
  }
  throw new Error('Bad Hex');
}
            window.hs_config.gulpDarken = (p1) => {
  const options = p1.split(',')

  let col = options[0].toString()
  let amt = -parseInt(options[1])
  var usePound = false

  if (col[0] == "#") {
    col = col.slice(1)
    usePound = true
  }
  var num = parseInt(col, 16)
  var r = (num >> 16) + amt
  if (r > 255) {
    r = 255
  } else if (r < 0) {
    r = 0
  }
  var b = ((num >> 8) & 0x00FF) + amt
  if (b > 255) {
    b = 255
  } else if (b < 0) {
    b = 0
  }
  var g = (num & 0x0000FF) + amt
  if (g > 255) {
    g = 255
  } else if (g < 0) {
    g = 0
  }
  return (usePound ? "#" : "") + (g | (b << 8) | (r << 16)).toString(16)
}
            window.hs_config.gulpLighten = (p1) => {
  const options = p1.split(',')

  let col = options[0].toString()
  let amt = parseInt(options[1])
  var usePound = false

  if (col[0] == "#") {
    col = col.slice(1)
    usePound = true
  }
  var num = parseInt(col, 16)
  var r = (num >> 16) + amt
  if (r > 255) {
    r = 255
  } else if (r < 0) {
    r = 0
  }
  var b = ((num >> 8) & 0x00FF) + amt
  if (b > 255) {
    b = 255
  } else if (b < 0) {
    b = 0
  }
  var g = (num & 0x0000FF) + amt
  if (g > 255) {
    g = 255
  } else if (g < 0) {
    g = 0
  }
  return (usePound ? "#" : "") + (g | (b << 8) | (r << 16)).toString(16)
}
            </script>
</head>

<body class="navbar-sidebar-aside-lg">

  <script src="../assets/js/hs.theme-appearance.js"></script>

  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand navbar-end navbar-light navbar-sticky-lg-top bg-white">
    <div class="container-fluid">
      <nav class="navbar-nav-wrap">
        <div class="row flex-grow-1">
          <!-- Default Logo -->
          <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
            <a class="navbar-brand" href="index.html" aria-label="Front">
              <img class="navbar-brand-logo" src="../assets/svg/logos/logo.svg" alt="Logo" data-hs-theme-appearance="default">
              <img class="navbar-brand-logo" src="../assets/svg/logos-light/logo.svg" alt="Logo" data-hs-theme-appearance="dark">
            </a>
            <a href="changelog.html">
              <span class="badge bg-soft-primary text-primary">v2.1</span>
            </a>
          </div>
          <!-- End Default Logo -->

          <div class="col-md px-lg-0">
            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
              <div class="d-none d-md-block">
                <!-- Search Form -->
                <form id="docsSearch" class="position-relative" data-hs-list-options='{
                       "searchMenu": true,
                       "keyboard": true,
                       "item": "searchTemplate",
                       "valueNames": ["component", "category", {"name": "link", "attr": "href"}],
                       "empty": "#searchNoResults"
                     }'>
                  <!-- Input Group -->
                  <div class="input-group input-group-merge navbar-input-group">
                    <div class="input-group-prepend input-group-text">
                      <i class="bi-search"></i>
                    </div>

                    <input type="search" class="search form-control form-control-sm" placeholder="Search in docs" aria-label="Search in docs">

                    <a class="input-group-append input-group-text" href="javascript:;">
                      <i id="clearSearchResultsIcon" class="bi-x" style="display: none;"></i>
                    </a>
                  </div>
                  <!-- End Input Group -->

                  <!-- List -->
                  <div class="list dropdown-menu navbar-dropdown-menu-borderless w-100 overflow-auto" style="max-height: 16rem;"></div>
                  <!-- End List -->

                  <!-- Empty -->
                  <div id="searchNoResults" style="display: none;">
                    <div class="text-center p-4">
                      <img class="mb-3" src="../assets/svg/illustrations/oc-error.svg" alt="Image Description" data-hs-theme-appearance="default" style="width: 7rem;">
                      <img class="mb-3" src="../assets/svg/illustrations-light/oc-error.svg" alt="Image Description" data-hs-theme-appearance="dark" style="width: 7rem;">
                      <p class="mb-0">No Results</p>
                    </div>
                  </div>
                  <!-- End Empty -->
                </form>
                <!-- End Search Form -->

                <!-- List Item Template -->
                <div class="d-none">
                  <div id="searchTemplate" class="dropdown-item">
                    <a class="d-block link" href="#">
                      <span class="category d-block fw-normal text-muted mb-1"></span>
                      <span class="component text-dark"></span>
                    </a>
                  </div>
                </div>
                <!-- End List Item Template -->
              </div>

              <!-- Navbar -->
              <ul class="navbar-nav p-0">
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://htmlstream.com/contact-us" target="_blank">
                    Get Support <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-primary btn-sm" href="../index.html">
                    <i class="bi-eye me-1"></i> Preview Demo
                  </a>
                </li>
              </ul>
              <!-- End Navbar -->
            </div>
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </nav>
    </div>
  </header>

  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <!-- Navbar -->
    <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end" data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
      <!-- Navbar Toggle -->
      <div class="d-grid flex-grow-1 px-2">
        <button type="button" class="navbar-toggler btn btn-white" data-bs-toggle="collapse" data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false" aria-controls="navbarVerticalNavMenu">
          <span class="d-flex justify-content-between align-items-center">
            <span class="h3 mb-0">Nav menu</span>

            <span class="navbar-toggler-default">
              <i class="bi-list"></i>
            </span>

            <span class="navbar-toggler-toggled">
              <i class="bi-x"></i>
            </span>
          </span>
        </button>
      </div>
      <!-- End Navbar Toggle -->

      <!-- Navbar Collapse -->
      <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
        <div class="navbar-brand-wrapper border-end" style="height: auto;">
          <!-- Default Logo -->
          <div class="d-flex align-items-center mb-3">
            <a class="navbar-brand" href="index.html" aria-label="Front">
              <img class="navbar-brand-logo" src="../assets/svg/logos/logo.svg" alt="Logo" data-hs-theme-appearance="default">
              <img class="navbar-brand-logo" src="../assets/svg/logos-light/logo.svg" alt="Logo" data-hs-theme-appearance="dark">
            </a>
            <a class="navbar-brand-badge" href="changelog.html">
              <span class="badge bg-soft-primary text-primary ms-2">v2.1</span>
            </a>
          </div>
          <!-- End Default Logo -->
        </div>

        <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
          <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
            <li class="nav-item">
              <span class="nav-subtitle">Documentation</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="index.html">Introduction</a>
            </li>

            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Getting started</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="getting-started.html">Getting Started</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="gulp.html">Gulp</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="darkmode.html">Dark Mode <span class="badge bg-soft-dark text-dark lh-base ms-1">New</span></a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="customization.html">Customization</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="credits.html">Credits</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="changelog.html">Changelog</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Design &amp; Graphics</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="bs-icons.html">Bootstrap Icons</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="illustrations.html">Illustrations</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Components</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="accordion.html">Accordion</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="alerts.html">Alerts</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="avatars.html">Avatars</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="badge.html">Badge</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="breadcrumb.html">Breadcrumb</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="buttons.html">Buttons</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="button-group.html">Button Group</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="cards.html">Cards</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="collapse.html">Collapse</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="column-divider.html">Column Divider</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="devices.html">Devices</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="divider.html">Divider</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="dropdowns.html">Dropdowns</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="icons.html">Icons</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="list-group.html">List Group</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="lists.html">Lists</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="legend-indicator.html">Legend Indicator</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="modal.html">Modal</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="offcanvas.html">Offcanvas</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="page-header.html">Page Header</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="pagination.html">Pagination</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="popovers.html">Popovers</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="progress.html">Progress</a>
            </li>

            <li class="nav-item">
              <a class="nav-link active" href="profile.html">Profile</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="shapes.html">Shapes</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sliding-img.html">Sliding Image</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="spinners.html">Spinners</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="steps.html">Steps</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="tab.html">Tab</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="toasts.html">Toasts</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="tooltips.html">Tooltips</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="typography.html">Typography</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Navbars</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="navbar.html">Navbar</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="navs.html">Navs</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="mega-menu.html">Mega Menu</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="navbar-vertical-aside.html">Navbar Vertical Aside</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="scrollspy.html">Scrollspy</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Tables</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="tables.html">Tables</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="datatables.html">Datatables</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="table-sticky-header.html">Sticky Header</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Basic forms</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="basic-forms.html">Basic Forms</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="checks-and-switches.html">Checks &amp; Switches</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="input-group.html">Input Group</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle">Advanced Forms</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="select.html">Advanced Select</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="datepicker.html">Datepicker (Flatpickr)</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="daterangepicker.html">Date Range Picker</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="calendar.html">Calendar (Fullcalendar)</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="file-attachments.html">File Attachments</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="dropzone.html">Drag’ n’ Drop File Uploads</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="quill.html">WYSIWYG Editor</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="quantity-counter.html">Quantity Counter</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="copy-to-clipboard.html">Copy to Clipboard</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="input-mask.html">Input Mask</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="step-forms.html">Step Forms (Wizards)</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="add-field.html">Add Field</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="toggle-password.html">Toggle Password</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="count-characters.html">Count Characters</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="form-search.html">Form Search</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="toggle-switch.html">Toggle Switch</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="google-recaptcha.html">Google reCAPTCHA</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle">Charts</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="chartjs.html">Chart.js</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="counter.html">Counter</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="circles.html">Circles.js (Pie Chart)</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle">Others</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="fslightbox.html">Fullscreen Lightbox</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="maps-leaflet.html">Leaflet</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="maps-jsvectormap.html">JSVectorMap</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sortablejs.html">SortableJS</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sticky-block.html">Sticky Block</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="go-to.html">Go To</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Utilities</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="backgrounds.html">Backgrounds</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="borders.html">Borders</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="colors.html">Colors</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="links.html">Links</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="position.html">Position</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="shadows.html">Shadows</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sizing.html">Sizing</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="spacing.html">Spacing</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="z-index.html">Z-index</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="opacity.html">Opacity</a>
            </li>
          </ul>
        </div>
      </div>
      <!-- End Navbar Collapse -->
    </nav>
    <!-- End Navbar -->

    <!-- Content -->
    <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
      <!-- Page Header -->
      <div class="docs-page-header">
        <div class="row align-items-center">
          <div class="col-sm">
            <h1 class="docs-page-header-title">Profile</h1>
            <p class="docs-page-header-text">Beautiful profile cover panels.</p>
          </div>
        </div>
      </div>
      <!-- End Page Header -->

      <!-- Heading -->
      <h2 id="basic-example" class="hs-docs-heading">
        Basic example <a class="anchorjs-link" href="#basic-example" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab1" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab1" href="#nav-result1" data-bs-toggle="pill" data-bs-target="#nav-result1" role="tab" aria-controls="nav-result1" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab1" href="#nav-html1" data-bs-toggle="pill" data-bs-target="#nav-html1" role="tab" aria-controls="nav-html1" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent1">
          <div class="tab-pane fade p-4 show active" id="nav-result1" role="tabpanel" aria-labelledby="nav-resultTab1">
            <!-- Profile Cover -->
            <div class="profile-cover">
              <div class="profile-cover-img-wrapper">
                <img class="profile-cover-img" src="../assets/img/1920x400/img1.jpg" alt="Image Description">
              </div>
            </div>
            <!-- End Profile Cover -->

            <!-- Profile Header -->
            <div class="text-center mb-5">
              <!-- Avatar -->
              <div class="avatar avatar-xxl avatar-circle profile-cover-avatar">
                <img class="avatar-img" src="../assets/img/160x160/img9.jpg" alt="Image Description">
                <span class="avatar-status avatar-status-success"></span>
              </div>
              <!-- End Avatar -->

              <h1 class="page-header-title">Ella Lauda <i class="bi-patch-check-fill fs-2 text-primary" data-bs-toggle="tooltip" data-bs-placement="top" title="Top endorsed"></i></h1>

              <!-- List -->
              <ul class="list-inline list-px-2">
                <li class="list-inline-item">
                  <i class="bi-building me-1"></i>
                  <span>Htmlstream</span>
                </li>

                <li class="list-inline-item">
                  <i class="bi-geo-alt me-1"></i>
                  <a href="#">San Francisco,</a>
                  <a href="#">US</a>
                </li>

                <li class="list-inline-item">
                  <i class="bi-calendar-week me-1"></i>
                  <span>Joined March 2017</span>
                </li>
              </ul>
              <!-- End List -->
            </div>
            <!-- End Profile Header -->
          </div>

          <div class="tab-pane fade" id="nav-html1" role="tabpanel" aria-labelledby="nav-htmlTab1">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Profile Cover --&gt;
                &lt;div class="profile-cover"&gt;
                  &lt;div class="profile-cover-img-wrapper"&gt;
                    &lt;img class="profile-cover-img" src="../assets/img/1920x400/img1.jpg" alt="Image Description"&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
                &lt;!-- End Profile Cover --&gt;

                &lt;!-- Profile Header --&gt;
                &lt;div class="text-center mb-5"&gt;
                  &lt;!-- Avatar --&gt;
                  &lt;div class="avatar avatar-xxl avatar-circle profile-cover-avatar"&gt;
                    &lt;img class="avatar-img" src="../assets/img/160x160/img9.jpg" alt="Image Description"&gt;
                    &lt;span class="avatar-status avatar-status-success"&gt;&lt;/span&gt;
                  &lt;/div&gt;
                  &lt;!-- End Avatar --&gt;

                  &lt;h1 class="page-header-title"&gt;Ella Lauda &lt;i class="bi-patch-check-fill fs-2 text-primary" data-bs-toggle="tooltip" data-bs-placement="top" title="Top endorsed"&gt;&lt;/i&gt;&lt;/h1&gt;

                  &lt;!-- List --&gt;
                  &lt;ul class="list-inline list-px-2"&gt;
                    &lt;li class="list-inline-item"&gt;
                      &lt;i class="bi-building me-1"&gt;&lt;/i&gt;
                      &lt;span&gt;Htmlstream&lt;/span&gt;
                    &lt;/li&gt;

                    &lt;li class="list-inline-item"&gt;
                      &lt;i class="bi-geo-alt me-1"&gt;&lt;/i&gt;
                      &lt;a href="#"&gt;San Francisco,&lt;/a&gt;
                      &lt;a href="#"&gt;US&lt;/a&gt;
                    &lt;/li&gt;

                    &lt;li class="list-inline-item"&gt;
                      &lt;i class="bi-calendar-week me-1"&gt;&lt;/i&gt;
                      &lt;span&gt;Joined March 2017&lt;/span&gt;
                    &lt;/li&gt;
                  &lt;/ul&gt;
                  &lt;!-- End List --&gt;
                &lt;/div&gt;
                &lt;!-- End Profile Header --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="with-card" class="hs-docs-heading">
        With card <a class="anchorjs-link" href="#with-card" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab2" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab2" href="#nav-result2" data-bs-toggle="pill" data-bs-target="#nav-result2" role="tab" aria-controls="nav-result2" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab2" href="#nav-html2" data-bs-toggle="pill" data-bs-target="#nav-html2" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-cssTab2" href="#nav-css2" data-bs-toggle="pill" data-bs-target="#nav-css2" role="tab" aria-controls="nav-css2" aria-selected="false">CSS</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-jsTab2" href="#nav-js2" data-bs-toggle="pill" data-bs-target="#nav-js2" role="tab" aria-controls="nav-js2" aria-selected="false">JS</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Alert -->
        <div class="alert alert-soft-dark card-alert" role="alert">
          This example uses <a class="alert-link" href="file-attachments.html">HS File Attach <i class="bi-link-45deg"></i></a> and <a class="alert-link" href="select.html">Advanced Select <i class="bi-link-45deg"></i></a> libraries.
        </div>
        <!-- End Alert -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent2">
          <div class="tab-pane fade p-4 show active" id="nav-result2" role="tabpanel" aria-labelledby="nav-resultTab2">
            <!-- Card -->
            <div class="card">
              <!-- Profile Cover -->
              <div class="profile-cover">
                <div class="profile-cover-img-wrapper">
                  <img id="profileCoverImg" class="profile-cover-img" src="../assets/img/1920x400/img2.jpg" alt="Image Description">

                  <!-- Custom File Cover -->
                  <div class="profile-cover-content profile-cover-uploader p-3">
                    <input type="file" class="js-file-attach profile-cover-uploader-input" id="profileCoverUplaoder" data-hs-file-attach-options='{
                                "textTarget": "#profileCoverImg",
                                "mode": "image",
                                "targetAttr": "src",
                                "allowTypes": [".png", ".jpeg", ".jpg"]
                             }'>
                    <label class="profile-cover-uploader-label btn btn-sm btn-white" for="profileCoverUplaoder">
                      <i class="bi-camera-fill"></i>
                      <span class="d-none d-sm-inline-block ms-1">Upload header</span>
                    </label>
                  </div>
                  <!-- End Custom File Cover -->
                </div>
              </div>
              <!-- End Profile Cover -->

              <!-- Avatar -->
              <label class="avatar avatar-xxl avatar-circle avatar-uploader profile-cover-avatar" for="editAvatarUploaderModal">
                <img id="editAvatarImgModal" class="avatar-img" src="../assets/img/160x160/img6.jpg" alt="Image Description">

                <input type="file" class="js-file-attach avatar-uploader-input" id="editAvatarUploaderModal" data-hs-file-attach-options='{
                            "textTarget": "#editAvatarImgModal",
                            "mode": "image",
                            "targetAttr": "src",
                            "allowTypes": [".png", ".jpeg", ".jpg"]
                         }'>

                <span class="avatar-uploader-trigger">
                  <i class="bi-pencil-fill avatar-uploader-icon shadow-sm"></i>
                </span>
              </label>
              <!-- End Avatar -->

              <!-- Body -->
              <div class="card-body">
                <div class="row">
                  <div class="col-sm-5">
                    <span class="d-block fs-5 mb-2">Who can see your profile photo? <i class="bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top" title="Your visibility setting only applies to your profile photo. Your header image is always visible to anyone."></i></span>

                    <!-- Select -->
                    <div class="tom-select-custom">
                      <select class="js-select form-select" data-hs-tom-select-options='{
                                "searchInDropdown": false,
                                "dropdownWidth": "auto"
                              }'>
                        <option value="privacy1" data-option-template='<div class="d-flex align-items-start"><div class="flex-shrink-0"><i class="bi-globe"></i></div><div class="flex-grow-1 ms-2"><span class="d-block fw-semibold">Anyone</span><span class="tom-select-custom-hide small">Visible to anyone who can view your content. Accessible by installed apps.</span></div></div>'>Anyone</option>
                        <option value="privacy2" data-option-template='<div class="d-flex align-items-start"><div class="flex-shrink-0"><i class="bi-lock"></i></div><div class="flex-grow-1 ms-2"><span class="d-block fw-semibold">Only you</span><span class="tom-select-custom-hide small">Only visible to you.</span></div></div>'>Only you</option>
                      </select>
                    </div>
                    <!-- End Select -->
                  </div>
                  <!-- End Col -->
                </div>
                <!-- End Row -->
              </div>
              <!-- End Body -->
            </div>
            <!-- End Card -->
          </div>

          <div class="tab-pane fade" id="nav-html2" role="tabpanel" aria-labelledby="nav-htmlTab2">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Card --&gt;
                &lt;div class="card"&gt;
                  &lt;!-- Profile Cover --&gt;
                  &lt;div class="profile-cover"&gt;
                    &lt;div class="profile-cover-img-wrapper"&gt;
                      &lt;img id="profileCoverImg" class="profile-cover-img" src="../assets/img/1920x400/img2.jpg" alt="Image Description"&gt;

                      &lt;!-- Custom File Cover --&gt;
                      &lt;div class="profile-cover-content profile-cover-uploader p-3"&gt;
                        &lt;input type="file" class="js-file-attach profile-cover-uploader-input" id="profileCoverUplaoder"
                               data-hs-file-attach-options='{
                                    "textTarget": "#profileCoverImg",
                                    "mode": "image",
                                    "targetAttr": "src",
                                    "allowTypes": [".png", ".jpeg", ".jpg"]
                                 }'&gt;
                        &lt;label class="profile-cover-uploader-label btn btn-sm btn-white" for="profileCoverUplaoder"&gt;
                          &lt;i class="bi-camera-fill"&gt;&lt;/i&gt;
                          &lt;span class="d-none d-sm-inline-block ms-1"&gt;Upload header&lt;/span&gt;
                        &lt;/label&gt;
                      &lt;/div&gt;
                      &lt;!-- End Custom File Cover --&gt;
                    &lt;/div&gt;
                  &lt;/div&gt;
                  &lt;!-- End Profile Cover --&gt;

                  &lt;!-- Avatar --&gt;
                  &lt;label class="avatar avatar-xxl avatar-circle avatar-uploader profile-cover-avatar" for="editAvatarUploaderModal"&gt;
                    &lt;img id="editAvatarImgModal" class="avatar-img" src="../assets/img/160x160/img6.jpg" alt="Image Description"&gt;

                    &lt;input type="file" class="js-file-attach avatar-uploader-input" id="editAvatarUploaderModal"
                           data-hs-file-attach-options='{
                                "textTarget": "#editAvatarImgModal",
                                "mode": "image",
                                "targetAttr": "src",
                                "allowTypes": [".png", ".jpeg", ".jpg"]
                             }'&gt;

                    &lt;span class="avatar-uploader-trigger"&gt;
                      &lt;i class="bi-pencil-fill avatar-uploader-icon shadow-sm"&gt;&lt;/i&gt;
                    &lt;/span&gt;
                  &lt;/label&gt;
                  &lt;!-- End Avatar --&gt;

                  &lt;!-- Body --&gt;
                  &lt;div class="card-body"&gt;
                    &lt;div class="row"&gt;
                      &lt;div class="col-sm-5"&gt;
                        &lt;span class="d-block fs-5 mb-2"&gt;Who can see your profile photo? &lt;i class="bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top" title="Your visibility setting only applies to your profile photo. Your header image is always visible to anyone."&gt;&lt;/i&gt;&lt;/span&gt;

                        &lt;!-- Select --&gt;
                        &lt;div class="tom-select-custom"&gt;
                          &lt;select class="js-select form-select"
                                  data-hs-tom-select-options='{
                                    "searchInDropdown": false,
                                    "dropdownWidth": "auto"
                                  }'&gt;
                            &lt;option value="privacy1" data-option-template='&lt;div class="d-flex align-items-start"&gt;&lt;div class="flex-shrink-0"&gt;&lt;i class="bi-globe"&gt;&lt;/i&gt;&lt;/div&gt;&lt;div class="flex-grow-1 ms-2"&gt;&lt;span class="d-block fw-semibold"&gt;Anyone&lt;/span&gt;&lt;span class="tom-select-custom-hide small"&gt;Visible to anyone who can view your content. Accessible by installed apps.&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;'&gt;Anyone&lt;/option&gt;
                            &lt;option value="privacy2" data-option-template='&lt;div class="d-flex align-items-start"&gt;&lt;div class="flex-shrink-0"&gt;&lt;i class="bi-lock"&gt;&lt;/i&gt;&lt;/div&gt;&lt;div class="flex-grow-1 ms-2"&gt;&lt;span class="d-block fw-semibold"&gt;Only you&lt;/span&gt;&lt;span class="tom-select-custom-hide small"&gt;Only visible to you.&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;'&gt;Only you&lt;/option&gt;
                          &lt;/select&gt;
                        &lt;/div&gt;
                        &lt;!-- End Select --&gt;
                       &lt;/div&gt;
                       &lt;!-- End Col --&gt;
                    &lt;/div&gt;
                    &lt;!-- End Row --&gt;
                  &lt;/div&gt;
                  &lt;!-- End Body --&gt;
                &lt;/div&gt;
                &lt;!-- End Card --&gt;
              </code>
            </pre>
          </div>

          <div class="tab-pane fade" id="nav-css2" role="tabpanel" aria-labelledby="nav-cssTab2">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;link rel="stylesheet" href="../node_modules/tom-select/dist/css/tom-select.bootstrap5.css"&gt;
              </code>
            </pre>
          </div>

          <div class="tab-pane fade" id="nav-js2" role="tabpanel" aria-labelledby="nav-jsTab2">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- JS Implementing Plugins --&gt;
                &lt;!-- bundlejs:vendor [..] --&gt;
                &lt;script src="../assets/vendor/hs-file-attach/dist/hs-file-attach.min.js"&gt;&lt;/script&gt;
                &lt;script src="../node_modules/tom-select/dist/js/tom-select.complete.min.js"&gt;&lt;/script&gt;

                &lt;!-- JS Front --&gt;
                &lt;!-- bundlejs:theme [..] --&gt;
                &lt;script src="../assets/js/hs.tom-select.js"&gt;&lt;/script&gt;

                &lt;!-- JS Plugins Init. --&gt;
                &lt;script&gt;
                  (function() {
                    // INITIALIZATION OF SELECT
                    // =======================================================
                    HSCore.components.HSTomSelect.init('.js-select')


                    // INITIALIZATION OF FILE ATTACHMENT
                    // =======================================================
                    new HSFileAttach('.js-file-attach')
                  })()
                &lt;/script&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->
    </div>
    <!-- End Content -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- ========== SECONDARY CONTENTS ========== -->
  <!-- Go To -->
  <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
    <i class="bi-chevron-up"></i>
  </a>
  <!-- ========== END SECONDARY CONTENTS ========== -->

  <!-- JS Implementing Plugins -->
  <script src="../assets/js/vendor.min.js"></script>

  <!-- JS Front -->
  <script src="../assets/js/theme.min.js"></script>

  <!-- JS Plugins Init. -->
  <script>
    (function() {
      // INITIALIZATION OF HEADER
      // =======================================================
      new HSHeader('#header').init()


      // INITIALIZATION OF NAV SCROLLER
      // =======================================================
      new HsNavScroller('.js-nav-scroller', {
        delay: 400,
        offset: 140
      })


      // INITIALIZATION OF LISTJS COMPONENT
      // =======================================================
      HSCore.components.HSList.init('#docsSearch');
      const docsSearch = HSCore.components.HSList.getItem('docsSearch');


      // GET JSON FILE RESULTS
      // =======================================================
      fetch('../assets/json/docs-search.json')
      .then(response => response.json())
      .then(data => {
        docsSearch.add(data)
      })


      // INITIALIZATION OF GO TO
      // =======================================================
      new HSGoTo('.js-go-to')


      // INITIALIZATION OF SELECT
      // =======================================================
      HSCore.components.HSTomSelect.init('.js-select')


      // INITIALIZATION OF FILE ATTACHMENT
      // =======================================================
      new HSFileAttach('.js-file-attach')
    })()
  </script>
</body>

<!-- Mirrored from htmlstream.com/front-dashboard/documentation/profile.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 30 Aug 2022 07:13:03 GMT -->
</html>