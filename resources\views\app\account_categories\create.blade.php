@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">folder-plus</x-slot>
        <x-slot name="title">Create New Account Category</x-slot>
        <x-slot name="subtitle">Add a new category to the chart of accounts</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Accounting</a></li>
            <li class="breadcrumb-item"><a href="{{ route('account-categories.index') }}">Account Categories</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('account-categories.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Categories
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Form Card -->
    <form method="POST" action="{{ route('account-categories.store') }}">
        @csrf
        
        @include('app.account_categories.form-inputs')

        <div class="d-flex justify-content-end mt-4 border-top pt-4">
            <a href="{{ route('account-categories.index') }}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-x-circle me-1"></i> Cancel
            </a>

            <button type="submit" class="btn btn-primary">
                <i class="bi bi-check-circle me-1"></i> Create Category
            </button>
        </div>
    </form>
</div>
@endsection
