@extends('layouts.app')

@push('styles')
<style>
    .content {
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
    }

    .content.loaded {
        opacity: 1;
    }

    .form-card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.75rem;
    }

    .form-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 0.75rem 0.75rem 0 0 !important;
        border: none;
    }

    .form-section {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .form-section-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .form-section-title i {
        margin-right: 0.5rem;
        color: #6c757d;
    }

    .form-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border-radius: 0.5rem;
        border: 1px solid #e9ecef;
        transition: all 0.15s ease-in-out;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn {
        border-radius: 0.5rem;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
    }
</style>
@endpush

@section('content')
<!-- Content -->
<div class="content">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">plus-circle</x-slot>
        <x-slot name="title">Add New Stock</x-slot>
        <x-slot name="subtitle">Add new inventory items to your stock</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('stocks.index') }}">Stock Inventory</a></li>
            <li class="breadcrumb-item active" aria-current="page">Add New Stock</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('stocks.index') }}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-left me-1"></i>Back to Stock
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Stock Form -->
    <div class=" justify-content-center">
        <div>
            <div class="card form-card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-box me-2"></i>Stock Information
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('stocks.store') }}">
                        @csrf

                        @include('app.stocks.form-inputs')

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                            <a href="{{ route('stocks.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-1"></i>Create Stock Entry
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- End Stock Form -->
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Show content after page loads to prevent flash
    $('.content').addClass('loaded');

    // Auto-focus first input
    $('.form-control:first').focus();

    // Form validation feedback
    $('form').on('submit', function() {
        $(this).find('button[type="submit"]').prop('disabled', true).html(
            '<i class="bi bi-arrow-clockwise spin me-1"></i>Creating...'
        );
    });
});
</script>
@endpush
