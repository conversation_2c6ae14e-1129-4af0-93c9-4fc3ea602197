<?php

namespace App\Services;

use App\Models\Employee;
use App\Models\EmployeeLoan;
use App\Models\MonthlyPayroll;
use App\Models\User;
use App\Mail\PayslipGenerated;
use App\Mail\LoanStatusNotification;
use App\Mail\PayrollCompletedNotification;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;

class NotificationService
{
    /**
     * Send payslip to employee
     *
     * @param \App\Models\MonthlyPayroll $payroll
     * @return bool
     */
    public function sendPayslip(MonthlyPayroll $payroll)
    {
        try {
            if (!$payroll->employee || !$payroll->employee->email) {
                Log::warning("Cannot send payslip: Employee or email not found for payroll ID {$payroll->id}");
                return false;
            }

            // Validate email address
            if (!filter_var($payroll->employee->email, FILTER_VALIDATE_EMAIL)) {
                Log::warning("Invalid email address for employee {$payroll->employee->name}: {$payroll->employee->email}");
                return false;
            }

            // Load required relationships
            $payroll->load('employee', 'payrollItems', 'contributions', 'deductions');

            Mail::to($payroll->employee->email)
                ->send(new PayslipGenerated($payroll));

            Log::info("Payslip sent successfully to {$payroll->employee->email} for payroll ID {$payroll->id}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send payslip for payroll ID {$payroll->id}: " . $e->getMessage());
            Log::error("Stack trace: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Send payslips to multiple employees
     *
     * @param \Illuminate\Support\Collection $payrolls
     * @return array
     */
    public function sendBulkPayslips(Collection $payrolls)
    {
        $results = [
            'sent' => 0,
            'failed' => 0,
            'errors' => []
        ];

        foreach ($payrolls as $payroll) {
            if ($this->sendPayslip($payroll)) {
                $results['sent']++;
            } else {
                $results['failed']++;
                $employeeName = $payroll->employee ? $payroll->employee->name : 'Unknown';
                $results['errors'][] = "Failed to send payslip to {$employeeName} (ID: {$payroll->id})";
            }
        }

        return $results;
    }

    /**
     * Send loan status notification
     *
     * @param \App\Models\EmployeeLoan $loan
     * @param string $status
     * @return bool
     */
    public function sendLoanNotification(EmployeeLoan $loan, $status)
    {
        try {
            if (!$loan->employee || !$loan->employee->email) {
                Log::warning("Cannot send loan notification: Employee or email not found for loan ID {$loan->id}");
                return false;
            }

            Mail::to($loan->employee->email)
                ->send(new LoanStatusNotification($loan, $status));

            Log::info("Loan notification sent successfully to {$loan->employee->email} for loan {$loan->loan_reference}, status: {$status}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send loan notification for loan ID {$loan->id}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send payroll completion notification to managers
     *
     * @param \Illuminate\Support\Collection $payrolls
     * @param string $period
     * @return bool
     */
    public function sendPayrollCompletionNotification(Collection $payrolls, $period)
    {
        try {
            $managers = $this->getManagerEmails();

            if ($managers->isEmpty()) {
                Log::warning("No manager emails found for payroll completion notification");
                return false;
            }

            foreach ($managers as $email) {
                Mail::to($email)
                    ->send(new PayrollCompletedNotification($payrolls, $period));
            }

            Log::info("Payroll completion notification sent to " . $managers->count() . " managers for period: {$period}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send payroll completion notification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send loan approval notification
     *
     * @param \App\Models\EmployeeLoan $loan
     * @return bool
     */
    public function sendLoanApprovalNotification(EmployeeLoan $loan)
    {
        return $this->sendLoanNotification($loan, 'approved');
    }

    /**
     * Send loan rejection notification
     *
     * @param \App\Models\EmployeeLoan $loan
     * @return bool
     */
    public function sendLoanRejectionNotification(EmployeeLoan $loan)
    {
        return $this->sendLoanNotification($loan, 'rejected');
    }

    /**
     * Send loan completion notification
     *
     * @param \App\Models\EmployeeLoan $loan
     * @return bool
     */
    public function sendLoanCompletionNotification(EmployeeLoan $loan)
    {
        return $this->sendLoanNotification($loan, 'completed');
    }

    /**
     * Send loan cancellation notification
     *
     * @param \App\Models\EmployeeLoan $loan
     * @return bool
     */
    public function sendLoanCancellationNotification(EmployeeLoan $loan)
    {
        return $this->sendLoanNotification($loan, 'cancelled');
    }

    /**
     * Get manager email addresses
     *
     * @return \Illuminate\Support\Collection
     */
    private function getManagerEmails()
    {
        // Get users with manager-related roles
        $managerRoles = ['super-admin', 'hr-manager', 'payroll-manager', 'finance-manager'];

        return User::whereHas('roles', function ($query) use ($managerRoles) {
            $query->whereIn('name', $managerRoles);
        })
        ->whereNotNull('email')
        ->pluck('email')
        ->unique();
    }

    /**
     * Send notification to specific email addresses
     *
     * @param array $emails
     * @param \Illuminate\Mail\Mailable $mailable
     * @return bool
     */
    public function sendToEmails(array $emails, $mailable)
    {
        try {
            foreach ($emails as $email) {
                if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    Mail::to($email)->send($mailable);
                }
            }
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to send email notification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send test email to verify email configuration
     *
     * @param string $email
     * @return bool
     */
    public function sendTestEmail($email)
    {
        try {
            Mail::raw('This is a test email from the payroll system. Email configuration is working correctly.', function ($message) use ($email) {
                $message->to($email)
                        ->subject('Test Email - Payroll System');
            });

            Log::info("Test email sent successfully to {$email}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send test email to {$email}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get notification statistics
     *
     * @return array
     */
    public function getNotificationStats()
    {
        // This could be expanded to track notification history in the database
        return [
            'total_sent' => 0, // Would come from database
            'failed_count' => 0, // Would come from database
            'last_sent' => null, // Would come from database
        ];
    }
}
