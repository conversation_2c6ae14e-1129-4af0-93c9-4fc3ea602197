<?php

namespace App\Http\Controllers\Api;

use App\Models\Status;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\StockResource;
use App\Http\Resources\StockCollection;

class StatusStocksController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Status $status
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, Status $status)
    {
        $this->authorize('view', $status);

        $search = $request->get('search', '');

        $stocks = $status
            ->stocks()
            ->search($search)
            ->latest()
            ->paginate();

        return new StockCollection($stocks);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Status $status
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, Status $status)
    {
        $this->authorize('create', Stock::class);

        $validated = $request->validate([
            'product_id' => ['nullable', 'exists:products,id'],
            'quantity' => ['nullable', 'numeric'],
            'balance' => ['nullable', 'numeric'],
            'supplier_id' => ['nullable', 'exists:suppliers,id'],
            'stock_id' => ['nullable', 'exists:stocks,id'],
            'approved_by' => ['nullable', 'exists:users,id'],
            'description' => ['nullable', 'max:255', 'string'],
        ]);

        $stock = $status->stocks()->create($validated);

        return new StockResource($stock);
    }
}
