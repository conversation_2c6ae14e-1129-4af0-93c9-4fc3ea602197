@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
          <div class="col-sm mb-2 mb-sm-0">
            <h1 class="page-header-title">
                Edit Customer
                <a href="{{ route('accounts.index') }}" class="mr-4 float-right"
                    ><i class="icon ion-md-arrow-back"></i> Back
                </a>
            </h1>
          </div>
          <!-- End Col -->
        </div>
    </div>
    <!-- End Page Header -->
    <x-form
        method="PUT"
        action="{{ route('accounts.update', $account) }}"
        has-files
        class="mt-4"
    >
        @include('app.accounts.form-inputs')

        <div class="mt-4">
            <a
                href="{{ route('accounts.index') }}"
                class="btn btn-light"
            >
                <!-- <i class="icon ion-md-return-left text-primary"></i> -->
                @lang('crud.common.back')
            </a>

            <a
                href="{{ route('accounts.create') }}"
                class="btn btn-light"
            >
                <!-- <i class="icon ion-md-add text-primary"></i> -->
                @lang('crud.common.create') account 
            </a>

            <button type="submit" class="btn btn-primary float-right">
                <!-- <i class="icon ion-md-save"></i> -->
                @lang('crud.common.update')
            </button>
        </div>
    </x-form>

</div>
@endsection
