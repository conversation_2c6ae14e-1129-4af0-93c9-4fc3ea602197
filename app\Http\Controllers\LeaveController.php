<?php

namespace App\Http\Controllers;

use App\Models\Status;
use App\Models\Leave;
use App\Models\LeaveRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

use Facades\App\Libraries\InvoiceHandler;

class LeaveController extends Controller
{


    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Leave::class);

        $search = $request->get('search', '');

        $leaves = Leave::search($search)
            ->latest()
            ->paginate()
            ->withQueryString();

        return view('app.leaves.index', compact('leaves', 'search'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Leave::class);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');

        return view('app.leaves.create', compact('statuses'));
    }

    /**
     * @param \App\Http\Requests\leavestoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', Leave::class);

        $validated = $request->validated();

        $leave = Leave::create($validated);

        if(request()->attachment) {
            $leave->clearMediaCollection('attachment');
            // foreach (request()->attachment as $file) {
            if(request()->attachment) $leave->addMedia(request()->attachment)->toMediaCollection("attachment");
            // }
        }

        return redirect()
            ->route('leaves.edit', $leave)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Leave $leave
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Leave $leave)
    {
        $this->authorize('view', $leave);

        return view('app.leaves.show', compact('leave'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Leave $leave
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Leave $leave)
    {
        $this->authorize('update', $leave);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');

        return view('app.leaves.edit', compact('leave', 'statuses'));
    }

    /**
     * @param \App\Http\Requests\CustomerUpdateRequest $request
     * @param \App\Models\Leave $leave
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Leave $leave)
    {
        $this->authorize('update', $leave);

        $validated = $request->all();

        $leave->update($validated);
        if(request()->image) {
            $leave->clearMediaCollection('attachment');
            // foreach (request()->attachment as $file) {
            if(request()->attachment) $leave->addMedia(request()->attachment)->toMediaCollection("attachment");
            // }
        }

        return redirect()
            ->route('leaves.edit', $leave)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Leave $leave
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Leave $leave)
    {
        $this->authorize('delete', $leave);

        if ($leave->name) {
            Storage::delete($leave->name);
        }

        $leave->delete();

        return redirect()
            ->route('leaves.index')
            ->withSuccess(__('crud.common.removed'));
    }




}
