<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AccountType;
use App\Models\AccountCategory;
use App\Models\Account;

class ChartOfAccountsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create Account Types
        $this->createAccountTypes();

        // Create Account Categories
        $this->createAccountCategories();

        // Create Accounts
        $this->createAccounts();
    }

    /**
     * Create default account types.
     *
     * @return void
     */
    private function createAccountTypes()
    {
        $accountTypes = [
            [
                'name' => 'Assets',
                'code' => '1',
                'classification' => 'asset',
                'description' => 'Resources owned by the business that have economic value',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Liabilities',
                'code' => '2',
                'classification' => 'liability',
                'description' => 'Obligations or debts owed by the business to external parties',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Equity',
                'code' => '3',
                'classification' => 'equity',
                'description' => 'Ownership interest in the business',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Revenue',
                'code' => '4',
                'classification' => 'revenue',
                'description' => 'Income earned from business operations',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Expenses',
                'code' => '5',
                'classification' => 'expense',
                'description' => 'Costs incurred in business operations',
                'is_active' => true,
                'is_system' => true,
            ],
        ];

        foreach ($accountTypes as $accountType) {
            AccountType::updateOrCreate(
                ['code' => $accountType['code']],
                $accountType
            );
        }
    }

    /**
     * Create default account categories.
     *
     * @return void
     */
    private function createAccountCategories()
    {
        $accountCategories = [
            // Asset Categories
            [
                'name' => 'Current Assets',
                'code' => '11',
                'account_type_id' => AccountType::where('code', '1')->first()->id,
                'description' => 'Assets expected to be converted to cash within one year',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Fixed Assets',
                'code' => '12',
                'account_type_id' => AccountType::where('code', '1')->first()->id,
                'description' => 'Long-term tangible assets used in business operations',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Other Assets',
                'code' => '13',
                'account_type_id' => AccountType::where('code', '1')->first()->id,
                'description' => 'Assets that do not fit into current or fixed asset categories',
                'is_active' => true,
                'is_system' => true,
            ],
            
            // Liability Categories
            [
                'name' => 'Current Liabilities',
                'code' => '21',
                'account_type_id' => AccountType::where('code', '2')->first()->id,
                'description' => 'Obligations due within one year',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Long-term Liabilities',
                'code' => '22',
                'account_type_id' => AccountType::where('code', '2')->first()->id,
                'description' => 'Obligations due beyond one year',
                'is_active' => true,
                'is_system' => true,
            ],
            
            // Equity Categories
            [
                'name' => 'Capital',
                'code' => '31',
                'account_type_id' => AccountType::where('code', '3')->first()->id,
                'description' => 'Owner\'s investment in the business',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Retained Earnings',
                'code' => '32',
                'account_type_id' => AccountType::where('code', '3')->first()->id,
                'description' => 'Accumulated profits reinvested in the business',
                'is_active' => true,
                'is_system' => true,
            ],
            
            // Revenue Categories
            [
                'name' => 'Operating Revenue',
                'code' => '41',
                'account_type_id' => AccountType::where('code', '4')->first()->id,
                'description' => 'Income from primary business activities',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Other Revenue',
                'code' => '42',
                'account_type_id' => AccountType::where('code', '4')->first()->id,
                'description' => 'Income from secondary business activities',
                'is_active' => true,
                'is_system' => true,
            ],
            
            // Expense Categories
            [
                'name' => 'Cost of Goods Sold',
                'code' => '51',
                'account_type_id' => AccountType::where('code', '5')->first()->id,
                'description' => 'Direct costs attributable to the production of goods sold',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Operating Expenses',
                'code' => '52',
                'account_type_id' => AccountType::where('code', '5')->first()->id,
                'description' => 'Expenses incurred in day-to-day business operations',
                'is_active' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Other Expenses',
                'code' => '53',
                'account_type_id' => AccountType::where('code', '5')->first()->id,
                'description' => 'Expenses not directly related to primary business operations',
                'is_active' => true,
                'is_system' => true,
            ],
        ];

        foreach ($accountCategories as $accountCategory) {
            AccountCategory::updateOrCreate(
                ['code' => $accountCategory['code']],
                $accountCategory
            );
        }
    }

    /**
     * Create default accounts.
     *
     * @return void
     */
    private function createAccounts()
    {
        $accounts = [
            // Current Assets
            [
                'name' => 'Cash',
                'code' => '1101',
                'account_type_id' => AccountType::where('code', '1')->first()->id,
                'account_category_id' => AccountCategory::where('code', '11')->first()->id,
                'description' => 'Cash on hand',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Bank',
                'code' => '1102',
                'account_type_id' => AccountType::where('code', '1')->first()->id,
                'account_category_id' => AccountCategory::where('code', '11')->first()->id,
                'description' => 'Cash in bank accounts',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Accounts Receivable',
                'code' => '1103',
                'account_type_id' => AccountType::where('code', '1')->first()->id,
                'account_category_id' => AccountCategory::where('code', '11')->first()->id,
                'description' => 'Amounts owed by customers',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Inventory',
                'code' => '1104',
                'account_type_id' => AccountType::where('code', '1')->first()->id,
                'account_category_id' => AccountCategory::where('code', '11')->first()->id,
                'description' => 'Goods held for sale',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Raw Materials',
                'code' => '1105',
                'account_type_id' => AccountType::where('code', '1')->first()->id,
                'account_category_id' => AccountCategory::where('code', '11')->first()->id,
                'description' => 'Materials used in production',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Work in Progress',
                'code' => '1106',
                'account_type_id' => AccountType::where('code', '1')->first()->id,
                'account_category_id' => AccountCategory::where('code', '11')->first()->id,
                'description' => 'Partially completed goods',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Finished Goods',
                'code' => '1107',
                'account_type_id' => AccountType::where('code', '1')->first()->id,
                'account_category_id' => AccountCategory::where('code', '11')->first()->id,
                'description' => 'Completed goods ready for sale',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            
            // Fixed Assets
            [
                'name' => 'Land',
                'code' => '1201',
                'account_type_id' => AccountType::where('code', '1')->first()->id,
                'account_category_id' => AccountCategory::where('code', '12')->first()->id,
                'description' => 'Land owned by the business',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Buildings',
                'code' => '1202',
                'account_type_id' => AccountType::where('code', '1')->first()->id,
                'account_category_id' => AccountCategory::where('code', '12')->first()->id,
                'description' => 'Buildings owned by the business',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Equipment',
                'code' => '1203',
                'account_type_id' => AccountType::where('code', '1')->first()->id,
                'account_category_id' => AccountCategory::where('code', '12')->first()->id,
                'description' => 'Equipment used in business operations',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Vehicles',
                'code' => '1204',
                'account_type_id' => AccountType::where('code', '1')->first()->id,
                'account_category_id' => AccountCategory::where('code', '12')->first()->id,
                'description' => 'Vehicles owned by the business',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Accumulated Depreciation',
                'code' => '1205',
                'account_type_id' => AccountType::where('code', '1')->first()->id,
                'account_category_id' => AccountCategory::where('code', '12')->first()->id,
                'description' => 'Accumulated depreciation of fixed assets',
                'normal_balance' => 'credit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            
            // Current Liabilities
            [
                'name' => 'Accounts Payable',
                'code' => '2101',
                'account_type_id' => AccountType::where('code', '2')->first()->id,
                'account_category_id' => AccountCategory::where('code', '21')->first()->id,
                'description' => 'Amounts owed to suppliers',
                'normal_balance' => 'credit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Accrued Expenses',
                'code' => '2102',
                'account_type_id' => AccountType::where('code', '2')->first()->id,
                'account_category_id' => AccountCategory::where('code', '21')->first()->id,
                'description' => 'Expenses incurred but not yet paid',
                'normal_balance' => 'credit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Sales Tax Payable',
                'code' => '2103',
                'account_type_id' => AccountType::where('code', '2')->first()->id,
                'account_category_id' => AccountCategory::where('code', '21')->first()->id,
                'description' => 'Sales tax collected but not yet remitted',
                'normal_balance' => 'credit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            
            // Long-term Liabilities
            [
                'name' => 'Long-term Loans',
                'code' => '2201',
                'account_type_id' => AccountType::where('code', '2')->first()->id,
                'account_category_id' => AccountCategory::where('code', '22')->first()->id,
                'description' => 'Loans due beyond one year',
                'normal_balance' => 'credit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            
            // Equity
            [
                'name' => 'Owner\'s Capital',
                'code' => '3101',
                'account_type_id' => AccountType::where('code', '3')->first()->id,
                'account_category_id' => AccountCategory::where('code', '31')->first()->id,
                'description' => 'Owner\'s investment in the business',
                'normal_balance' => 'credit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Retained Earnings',
                'code' => '3201',
                'account_type_id' => AccountType::where('code', '3')->first()->id,
                'account_category_id' => AccountCategory::where('code', '32')->first()->id,
                'description' => 'Accumulated profits reinvested in the business',
                'normal_balance' => 'credit',
                'is_active' => true,
                'allows_manual_entries' => false,
                'is_system' => true,
            ],
            
            // Revenue
            [
                'name' => 'Sales Revenue',
                'code' => '4101',
                'account_type_id' => AccountType::where('code', '4')->first()->id,
                'account_category_id' => AccountCategory::where('code', '41')->first()->id,
                'description' => 'Revenue from sales of goods or services',
                'normal_balance' => 'credit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Interest Income',
                'code' => '4201',
                'account_type_id' => AccountType::where('code', '4')->first()->id,
                'account_category_id' => AccountCategory::where('code', '42')->first()->id,
                'description' => 'Income from interest on investments',
                'normal_balance' => 'credit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            
            // Expenses
            [
                'name' => 'Cost of Goods Sold',
                'code' => '5101',
                'account_type_id' => AccountType::where('code', '5')->first()->id,
                'account_category_id' => AccountCategory::where('code', '51')->first()->id,
                'description' => 'Cost of inventory sold',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Salaries and Wages',
                'code' => '5201',
                'account_type_id' => AccountType::where('code', '5')->first()->id,
                'account_category_id' => AccountCategory::where('code', '52')->first()->id,
                'description' => 'Employee compensation',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Rent Expense',
                'code' => '5202',
                'account_type_id' => AccountType::where('code', '5')->first()->id,
                'account_category_id' => AccountCategory::where('code', '52')->first()->id,
                'description' => 'Rent for business premises',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Utilities Expense',
                'code' => '5203',
                'account_type_id' => AccountType::where('code', '5')->first()->id,
                'account_category_id' => AccountCategory::where('code', '52')->first()->id,
                'description' => 'Electricity, water, gas, etc.',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Depreciation Expense',
                'code' => '5204',
                'account_type_id' => AccountType::where('code', '5')->first()->id,
                'account_category_id' => AccountCategory::where('code', '52')->first()->id,
                'description' => 'Depreciation of fixed assets',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
            [
                'name' => 'Interest Expense',
                'code' => '5301',
                'account_type_id' => AccountType::where('code', '5')->first()->id,
                'account_category_id' => AccountCategory::where('code', '53')->first()->id,
                'description' => 'Interest on loans and other debt',
                'normal_balance' => 'debit',
                'is_active' => true,
                'allows_manual_entries' => true,
                'is_system' => true,
            ],
        ];

        foreach ($accounts as $account) {
            Account::updateOrCreate(
                ['code' => $account['code']],
                $account
            );
        }
    }
}
