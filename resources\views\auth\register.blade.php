@extends('layouts.guest')

@section('content')
<style>
    .register-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .register-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
        background-size: cover;
        opacity: 0.3;
    }

    .register-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        position: relative;
        z-index: 2;
    }

    .register-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 35px 70px rgba(0, 0, 0, 0.2);
    }

    .logo-container {
        position: relative;
        z-index: 3;
        margin-bottom: 2rem;
    }

    .logo-img {
        transition: transform 0.3s ease;
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    }

    .logo-img:hover {
        transform: scale(1.05);
    }

    .form-control-modern {
        border: 2px solid #e1e5e9;
        border-radius: 12px;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control-modern:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
        background: rgba(255, 255, 255, 1);
        transform: translateY(-1px);
    }

    .form-control-modern:hover {
        border-color: #667eea;
        background: rgba(255, 255, 255, 1);
    }

    .btn-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 0.875rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    .form-label-modern {
        font-weight: 600;
        color: #4a5568;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    @media (max-width: 768px) {
        .register-card {
            margin: 1rem;
            border-radius: 16px;
        }

        .logo-img {
            width: 5rem !important;
        }
    }

    /* Dark theme compatibility */
    [data-hs-appearance="dark"] .register-card {
        background: rgba(30, 41, 59, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    [data-hs-appearance="dark"] .form-control-modern {
        background: rgba(30, 41, 59, 0.9);
        border-color: #4a5568;
        color: #e2e8f0;
    }

    [data-hs-appearance="dark"] .form-control-modern:focus {
        background: rgba(30, 41, 59, 1);
        border-color: #667eea;
    }

    [data-hs-appearance="dark"] .form-label-modern {
        color: #e2e8f0;
    }
</style>

<div class="register-container">
    <div class="container py-5">


        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="register-card p-4 p-md-5">

                                <div class="pb-2 text-center">
                    <a href="/" class="d-inline-block">
                        <img src="{{ asset('img/logo.webp') }}" alt="Sales Pro Logo" style="width: 8rem;">
                    </a>
                </div>

                    <div class="text-center mb-4">
                        <h1 class="h2 mb-3" style="color: #2d3748; font-weight: 700;">Create Your Account</h1>
                        <p class="text-muted mb-0">Join us and start managing your business</p>
                    </div>

                    <form method="POST" action="{{ route('register') }}">
                        @csrf

                        <div class="row">
                            <!-- Name -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label-modern">{{ __('Full Name') }}</label>
                                <input id="name"
                                       type="text"
                                       class="form-control form-control-modern @error('name') is-invalid @enderror"
                                       name="name"
                                       value="{{ old('name') }}"
                                       placeholder="Enter your full name"
                                       required
                                       autocomplete="name"
                                       autofocus>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Phone -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label-modern">{{ __('Phone Number') }}</label>
                                <input id="phone"
                                       type="text"
                                       class="form-control form-control-modern @error('phone') is-invalid @enderror"
                                       name="phone"
                                       value="{{ old('phone') }}"
                                       placeholder="Enter your phone number"
                                       required
                                       autocomplete="phone">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <!-- Domain -->
                            <div class="col-md-6 mb-3">
                                <label for="domain" class="form-label-modern">{{ __('Company Shortname ') }}</label>
                                <input id="domain"
                                       type="text"
                                       class="form-control form-control-modern @error('domain') is-invalid @enderror"
                                       name="domain"
                                       value="{{ old('domain') }}"
                                       placeholder="yourcompany"
                                       required
                                       autocomplete="domain">
                                @error('domain')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">This will be your unique company identifier</small>
                            </div>

                            <!-- Email -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label-modern">{{ __('Email Address') }}</label>
                                <input id="email"
                                       type="email"
                                       class="form-control form-control-modern @error('email') is-invalid @enderror"
                                       name="email"
                                       value="{{ old('email') }}"
                                       placeholder="Enter your email address"
                                       required
                                       autocomplete="email">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <!-- Password -->
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label-modern">{{ __('Password') }}</label>
                                <input id="password"
                                       type="password"
                                       class="form-control form-control-modern @error('password') is-invalid @enderror"
                                       name="password"
                                       placeholder="Create a strong password"
                                       required
                                       autocomplete="new-password">
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Confirm Password -->
                            <div class="col-md-6 mb-3">
                                <label for="password-confirm" class="form-label-modern">{{ __('Confirm Password') }}</label>
                                <input id="password-confirm"
                                       type="password"
                                       class="form-control form-control-modern"
                                       name="password_confirmation"
                                       placeholder="Confirm your password"
                                       required
                                       autocomplete="new-password">
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-modern text-white">
                                <i class="bi-person-plus me-2"></i>
                                Create Account
                            </button>
                        </div>

                        <!-- Login Link -->
                        <div class="text-center">
                            <p class="mb-0" style="color: #6b7280;">
                                Already have an account?
                                <a href="/login"
                                   class="text-decoration-none fw-semibold"
                                   style="color: #667eea;">
                                    Sign in here
                                </a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection