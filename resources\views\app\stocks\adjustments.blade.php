@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">
                            <i class="bi bi-clipboard-minus me-2"></i>Stock Adjustments
                        </h4>
                        <div class="d-flex gap-2">
                            <a href="{{ route('stocks.create-adjustment') }}" class="btn btn-warning btn-sm">
                                <i class="bi bi-plus-circle me-1"></i>New Adjustment
                            </a>
                            <a href="{{ route('stocks.index') }}" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-box me-1"></i>Stock Inventory
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mt-4">
        <div class="card-body">
            <form action="{{ route('stocks.adjustments') }}" method="GET">
                <div class="row g-3">
                    <!-- Search -->
                    <div class="col-md-4">
                        <label class="form-label small text-muted mb-1">
                            <i class="bi bi-search me-1"></i>Search Products
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control" name="search"
                                   placeholder="Product name, description, barcode..."
                                   value="{{ request('search') }}">
                        </div>
                    </div>

                    <!-- Warehouse Filter -->
                    <div class="col-md-3">
                        <label class="form-label small text-muted mb-1">
                            <i class="bi bi-building me-1"></i>Warehouse
                        </label>
                        <select class="form-select" name="warehouse">
                            <option value="">All Warehouses</option>
                            @foreach($filterOptions['warehouses'] as $warehouse)
                                <option value="{{ $warehouse->id }}" {{ request('warehouse') == $warehouse->id ? 'selected' : '' }}>
                                    {{ $warehouse->name }}
                                    @if($warehouse->branch)
                                        - {{ $warehouse->branch->name }}
                                    @endif
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div class="col-md-2">
                        <label class="form-label small text-muted mb-1">
                            <i class="bi bi-check-circle me-1"></i>Status
                        </label>
                        <select class="form-select" name="status">
                            <option value="">All Status</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>
                                🕐 Pending
                            </option>
                            <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>
                                ✅ Approved
                            </option>
                        </select>
                    </div>

                    <!-- Date From -->
                    <div class="col-md-1">
                        <label class="form-label small text-muted mb-1">
                            <i class="bi bi-calendar-event me-1"></i>From
                        </label>
                        <input type="date" class="form-control" name="date_from" value="{{ request('date_from') }}">
                    </div>

                    <!-- Date To -->
                    <div class="col-md-1">
                        <label class="form-label small text-muted mb-1">
                            <i class="bi bi-calendar-check me-1"></i>To
                        </label>
                        <input type="date" class="form-control" name="date_to" value="{{ request('date_to') }}">
                    </div>

                    <!-- Action Buttons -->
                    <div class="col-md-1 d-flex gap-2 align-items-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-funnel me-1"></i>Apply
                        </button>
                        <a href="{{ route('stocks.adjustments') }}" class="btn btn-outline-secondary" title="Clear all filters">
                            <i class="bi bi-arrow-clockwise"></i>
                        </a>
                        @if(request()->hasAny(['search', 'warehouse', 'status', 'date_from', 'date_to']))
                            <span class="badge bg-info align-self-center">
                                <i class="bi bi-funnel-fill me-1"></i>Active
                            </span>
                        @endif
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Adjustments Table -->
    <div class="card mt-4">
        <div class="card-body">
            @if($adjustments->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Date</th>
                                <th>Product</th>
                                <th>Quantity</th>
                                <th>Unit</th>
                                <th>Warehouse</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Created By</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($adjustments as $adjustment)
                                <tr>
                                    <td>
                                        <small class="text-muted">{{ $adjustment->created_at->format('M d, Y') }}</small>
                                        <br>
                                        <small class="text-muted">{{ $adjustment->created_at->format('H:i') }}</small>
                                    </td>
                                    <td>
                                        <strong>{{ $adjustment->product->name ?? 'Unknown Product' }}</strong>
                                        @if($adjustment->product && $adjustment->product->description)
                                            <br>
                                            <small class="text-muted">{{ Str::limit($adjustment->product->description, 50) }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">{{ number_format(abs($adjustment->quantity), 2) }}</span>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ $adjustment->unit_name ?? 'Unknown' }}</span>
                                    </td>
                                    <td>
                                        <strong>{{ $adjustment->warehouse->name ?? 'Unknown' }}</strong>
                                        @if($adjustment->warehouse && $adjustment->warehouse->branch)
                                            <br>
                                            <small class="text-muted">{{ $adjustment->warehouse->branch->name }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        @php
                                            $type = explode(':', $adjustment->description)[0] ?? 'ADJUSTMENT';
                                            $typeColors = [
                                                'STOCK_ADJUSTMENT' => 'warning',
                                                'STOCK_COUNT' => 'info',
                                                'STOCK_EXPIRY' => 'danger',
                                                'STOCK_SCRAP' => 'secondary',
                                                'STOCK_LOSS' => 'danger',
                                                'STOCK_REPAIR' => 'primary',
                                                'STOCK_RECYCLE' => 'success',
                                                'STOCK_WRITE_OFF' => 'dark',
                                                'STOCK_WRITE_DOWN' => 'warning'
                                            ];
                                            $color = $typeColors[$type] ?? 'secondary';
                                        @endphp
                                        <span class="badge bg-{{ $color }}">{{ str_replace('STOCK_', '', $type) }}</span>
                                        @if(strpos($adjustment->description, ':') !== false)
                                            <br>
                                            <small class="text-muted">{{ Str::limit(explode(':', $adjustment->description, 2)[1] ?? '', 30) }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($adjustment->approved_by)
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>Approved
                                            </span>
                                            <br>
                                            <small class="text-muted">{{ $adjustment->approvedBy->name ?? 'System' }}</small>
                                        @else
                                            <span class="badge bg-warning">
                                                <i class="bi bi-clock me-1"></i>Pending
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ $adjustment->createdBy->name ?? 'System' }}</small>
                                    </td>
                                    <td>
                                        @if(!$adjustment->approved_by && auth()->user()->can('approve stock'))
                                            <button type="button" 
                                                    class="btn btn-success btn-sm approve-adjustment-btn"
                                                    data-adjustment-id="{{ $adjustment->id }}"
                                                    title="Approve Adjustment">
                                                <i class="bi bi-check-circle"></i>
                                            </button>
                                        @endif
                                        
                                        <button type="button" 
                                                class="btn btn-outline-info btn-sm view-details-btn"
                                                data-adjustment="{{ json_encode($adjustment) }}"
                                                title="View Details">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="text-muted">
                            Showing {{ $adjustments->firstItem() }} to {{ $adjustments->lastItem() }} 
                            of {{ $adjustments->total() }} adjustments
                        </div>
                    </div>
                    <div class="col-md-6">
                        @if($adjustments->hasPages())
                            <div class="d-flex justify-content-end">
                                {{ $adjustments->onEachSide(10)->links() }}
                            </div>
                        @endif
                    </div>
                </div>
            @else
                <div class="text-center py-5">
                    <div class="text-muted">
                        <i class="bi bi-clipboard-minus fs-1 mb-3 d-block"></i>
                        <h5>No Stock Adjustments Found</h5>
                        <p class="mb-3">No adjustments match your current filters.</p>
                        <a href="{{ route('stocks.create-adjustment') }}" class="btn btn-warning">
                            <i class="bi bi-plus-circle me-1"></i>Create First Adjustment
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Approve adjustment
    document.querySelectorAll('.approve-adjustment-btn').forEach(button => {
        button.addEventListener('click', function() {
            const adjustmentId = this.dataset.adjustmentId;
            
            if (confirm('Are you sure you want to approve this stock adjustment?')) {
                fetch('/stocks/adjustments/approve', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        adjustment_id: adjustmentId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while approving the adjustment.');
                });
            }
        });
    });
});
</script>
@endpush
@endsection
