<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\CreatedUpdatedTrait;
use Carbon\Carbon;

class EmployeeTimeRecord extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'employee_id',
        'work_date',
        'check_in_time',
        'check_out_time',
        'total_hours',
        'regular_hours',
        'overtime_hours',
        'daily_comment',
        'status',
        'created_by',
        'updated_by',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'work_date' => 'date',
        'check_in_time' => 'datetime',
        'check_out_time' => 'datetime',
        'total_hours' => 'decimal:2',
        'regular_hours' => 'decimal:2',
        'overtime_hours' => 'decimal:2',
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];

    protected $appends = ['formatted_work_date', 'formatted_check_in', 'formatted_check_out'];

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function tasks()
    {
        return $this->hasMany(EmployeeTask::class);
    }

    public function getFormattedWorkDateAttribute()
    {
        return $this->work_date ? $this->work_date->format('Y-m-d') : null;
    }

    public function getFormattedCheckInAttribute()
    {
        return $this->check_in_time ? $this->check_in_time->format('H:i') : null;
    }

    public function getFormattedCheckOutAttribute()
    {
        return $this->check_out_time ? $this->check_out_time->format('H:i') : null;
    }

    /**
     * Check in the employee.
     */
    public function checkIn($time = null)
    {
        $this->check_in_time = $time ?? now();
        $this->status = 'checked_in';
        $this->save();
        
        return $this;
    }

    /**
     * Check out the employee and calculate hours.
     */
    public function checkOut($time = null)
    {
        if (!$this->check_in_time) {
            throw new \Exception('Cannot check out without checking in first.');
        }

        $this->check_out_time = $time ?? now();
        $this->status = 'checked_out';
        $this->calculateHours();
        $this->save();
        
        return $this;
    }

    /**
     * Calculate total, regular, and overtime hours.
     */
    public function calculateHours()
    {
        if (!$this->check_in_time || !$this->check_out_time) {
            return;
        }

        $checkIn = Carbon::parse($this->check_in_time);
        $checkOut = Carbon::parse($this->check_out_time);
        
        // Calculate total hours
        $this->total_hours = $checkOut->diffInMinutes($checkIn) / 60;
        
        // Get work hours configuration
        $workConfig = WorkHoursConfiguration::getActive();
        
        if ($workConfig) {
            $this->regular_hours = $workConfig->calculateRegularHours($this->total_hours);
            $this->overtime_hours = $workConfig->calculateOvertimeHours($this->total_hours);
        } else {
            // Default: 8 hours standard
            $this->regular_hours = min($this->total_hours, 8);
            $this->overtime_hours = max(0, $this->total_hours - 8);
        }
    }

    /**
     * Get today's record for an employee.
     */
    public static function getTodayRecord($employeeId)
    {
        return static::where('employee_id', $employeeId)
            ->where('work_date', today())
            ->first();
    }

    /**
     * Create or get today's record for an employee.
     */
    public static function getOrCreateTodayRecord($employeeId)
    {
        return static::firstOrCreate(
            [
                'employee_id' => $employeeId,
                'work_date' => today(),
            ],
            [
                'status' => 'incomplete',
                'created_by' => auth()->id(),
            ]
        );
    }
}
