<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

use App\Traits\CreatedUpdatedTrait;

class PayrollItem extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'amount',
        'formula',
        'type',
        'description',
        'approved_by',
        'created_by',
        'updated_by',
        'is_calculatable',
        'deduction_contribution_id',
        'monthly_payroll_id',
        'loan_id',
    ];

    protected $searchableFields = ['*'];

    public function monthlyPayroll()
    {
        return $this->belongsTo(MonthlyPayroll::class);
    }

    public function deductionContribution() {
        return $this->belongsTo(DeductionContribution::class);
    }

    public function loan() {
        return $this->belongsTo(EmployeeLoan::class, 'loan_id');
    }

}
