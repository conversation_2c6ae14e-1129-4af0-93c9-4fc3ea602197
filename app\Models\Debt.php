<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;
use App\Traits\BranchTrait;
use DB;

class Debt extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;
    use BranchTrait;
    protected $connection = "tenant";

    protected $fillable = [
        'dept_id',
        'date_from',
        'date_to',
        'closed_at',
        'reference_name',
        'amount_total',
        'amount_paid',
        'description',
        'category',
        'type',
        'branch_id',
        'created_by',
        'updated_by',
        'debtable_id',
        'debtable_type',
        'status_id',
        'approved_by',
        'business_type_id',

    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
        'closed_at' => 'datetime:Y-m-d',
        'date_from' => 'datetime:Y-m-d',
        'date_to' => 'datetime:Y-m-d',
    ];
    

    protected $appends = ['debt_id', 'balance'];


    public function getDebtIdAttribute(){
        return _pad( $this->id, 5, '0');
    }

    public function getBalanceAttribute(){
        return $this->amount_total - $this->amount_paid;
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function stockItems()
    {
        return $this->hasMany(StockItem::class);
    }

    
    public function debtable()
    {
        return $this->morphTo();
    }

    public function payments()
    {
        return $this->morphMany(Payment::class, 'paymentable');
    }

  
}
