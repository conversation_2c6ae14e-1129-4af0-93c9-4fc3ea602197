@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">Users</x-slot>
        <x-slot name="controls">
            @can('create', App\Models\User::class)
            <div class="btn-group" role="group">
                <!-- <a href="users/create" class="btn btn-primary btn-sm">
                    <i class="bi bi-person-plus me-1"></i>@lang('crud.common.create') New User
                </a> -->
                @if(auth()->user()->canInviteUsers())
                <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#inviteUserModal">
                    <i class="bi bi-envelope me-1"></i>Invite User
                </button>
                @endif
            </div>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Navigation Tabs -->
    <div class="js-nav-scroller hs-nav-scroller-horizontal mb-4">
        <span class="hs-nav-scroller-arrow-prev" style="display: none;">
            <a class="hs-nav-scroller-arrow-link" href="javascript:;">
                <i class="bi-chevron-left"></i>
            </a>
        </span>
        <span class="hs-nav-scroller-arrow-next" style="display: none;">
            <a class="hs-nav-scroller-arrow-link" href="javascript:;">
                <i class="bi-chevron-right"></i>
            </a>
        </span>
        <ul class="nav nav-tabs" id="userTabs" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="users-tab" data-bs-toggle="tab" href="#users-content" role="tab">
                    <i class="bi-people me-1"></i>Users ({{ $users->count() }})
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="invitations-tab" data-bs-toggle="tab" href="#invitations-content" role="tab">
                    <i class="bi-envelope me-1"></i>Pending Invitations
                    @if(isset($pendingInvitations) && $pendingInvitations->count() > 0)
                        <span class="badge bg-warning text-dark ms-1">{{ $pendingInvitations->count() }}</span>
                    @endif
                </a>
            </li>
        </ul>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="userTabsContent">
        <!-- Users Tab -->
        <div class="tab-pane fade show active" id="users-content" role="tabpanel">
            <div class="card card-table">
        <div class="table-responsive mt-3">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            Name
                        </th>
                        <th class="text-left">
                            Phone
                        </th>
                        <th class="text-left">
                            @lang('crud.users.inputs.email')
                        </th>
                        <th class="text-left">
                            Status
                        </th>
                        <th class="text-left">
                            Branch/Warehouse
                        </th>                    
                        <th class="text-left">
                            Roles
                        </th>
                        <th class="text-center">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($users as $user)
                    <tr>
                        <td>
                            <x-partials.thumbnail src="{{ $user->image ?? '' }}" />
                            {{ $user->name ?? '-' }}
                        </td>
                        <td>{{ $user->phone ?? '-' }}</td>
                        <td>{{ $user->email ?? '-' }}</td>
                        <td>
                            <span class="badge bg-info text-white">{{ optional($user->status)->name ?? '-' }} </span>
                        </td>
                        <td>{{ optional($user->branch)->name ?? '-' }}/{{ optional($user->warehouse)->name ?? '-' }}</td>
                        <td>
                            @foreach( $user->roles as $role)
                            <span class="badge bg-primary text-white"> {{ $role->name ?? '-' }} </span>
                            @endforeach
                        </td>
                        <td class="text-center" style="width: 134px;">
                            <div
                                role="group"
                                aria-label="Row Actions"
                                class="btn-group"
                            >
                                @can('update', $user)
                                <a href="{{ route('users.edit', $user) }}">
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        <!-- <i class="icon ion-md-create"></i> --> edit
                                    </button>
                                </a>
                                @endcan @can('view', $user)
                                <a href="{{ route('users.show', $user) }}">
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        <!-- <i class="icon ion-md-eye"></i> -->view
                                    </button>
                                </a>
                                @endcan @can('delete', $user)
                                <form
                                    action="{{ route('users.destroy', $user) }}"
                                    method="POST"
                                    onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                >
                                    @csrf @method('DELETE')
                                    <button
                                        type="submit"
                                        class="btn btn-light text-danger m-1"
                                    >
                                        <!-- <i class="icon ion-md-trash"></i> --> del
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
        </div>
        <!-- End Users Tab -->

        <!-- Invitations Tab -->
        <div class="tab-pane fade" id="invitations-content" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="bi bi-envelope me-2"></i>Pending Invitations
                    </h4>
                </div>
                <div class="card-body">
                    @if(isset($pendingInvitations) && $pendingInvitations->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Invited By</th>
                                        <th>Sent</th>
                                        <th>Expires</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($pendingInvitations as $invitation)
                                    <tr>
                                        <td>
                                            <strong>{{ $invitation->user_email }}</strong>
                                            @if($invitation->invitation_message)
                                                <br>
                                                <small class="text-muted">{{ Str::limit($invitation->invitation_message, 50) }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-info text-white">{{ ucfirst($invitation->role) }}</span>
                                        </td>
                                        <td>{{ $invitation->inviter->name ?? 'System' }}</td>
                                        <td>
                                            <small class="text-muted">{{ $invitation->invited_at->format('M d, Y') }}</small>
                                            <br>
                                            <small class="text-muted">{{ $invitation->invited_at->format('g:i A') }}</small>
                                        </td>
                                        <td>
                                            @if($invitation->expires_at->isPast())
                                                <span class="badge bg-danger text-white">Expired</span>
                                            @else
                                                <small class="text-muted">{{ $invitation->expires_at->format('M d, Y') }}</small>
                                                <br>
                                                <small class="text-success">{{ $invitation->expires_at->diffForHumans() }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            @if($invitation->status === 'accepted')
                                                <span class="badge bg-success text-white">
                                                    <i class="bi bi-check-circle me-1"></i>Accepted
                                                </span>
                                            @elseif($invitation->status === 'cancelled')
                                                <span class="badge bg-secondary text-white">
                                                    <i class="bi bi-x-circle me-1"></i>Cancelled
                                                </span>
                                            @elseif($invitation->expires_at->isPast())
                                                <span class="badge bg-danger text-white">
                                                    <i class="bi bi-x-circle me-1"></i>Expired
                                                </span>
                                            @else
                                                <span class="badge bg-warning text-dark">
                                                    <i class="bi bi-clock me-1"></i>Pending
                                                </span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($invitation->status === 'pending' && !$invitation->expires_at->isPast())
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary resend-invitation-btn"
                                                            data-invitation-id="{{ $invitation->id }}"
                                                            title="Resend Invitation">
                                                        <i class="bi bi-arrow-repeat"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger cancel-invitation-btn"
                                                            data-invitation-id="{{ $invitation->id }}"
                                                            title="Cancel Invitation">
                                                        <i class="bi bi-x"></i>
                                                    </button>
                                                </div>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="text-muted">
                                <i class="bi bi-envelope fs-1 mb-3 d-block"></i>
                                <h5>No Pending Invitations</h5>
                                <p class="mb-3">No invitations have been sent yet.</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#inviteUserModal">
                                    <i class="bi bi-envelope me-1"></i>Send First Invitation
                                </button>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        <!-- End Invitations Tab -->
    </div>

</div>

<!-- Invite User Modal -->
@if(auth()->user()->canInviteUsers())
<div class="modal fade" id="inviteUserModal" tabindex="-1" aria-labelledby="inviteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="inviteUserModalLabel">
                    <i class="bi bi-envelope me-2"></i>Invite User to Join
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="inviteUserForm" method="POST" action="{{ route('users.store') }}">
                @csrf
                <input type="hidden" name="creation_method" value="invitation">

                <div class="modal-body">
                    <div class="row">
                        <!-- Email -->
                        <div class="col-md-6 mb-3">
                            <label for="invite_email" class="form-label">Email Address <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="invite_email" name="email" required
                                   placeholder="<EMAIL>">
                            <div class="form-text">The invitation will be sent to this email address.</div>
                        </div>

                        <!-- Name -->
                        <div class="col-md-6 mb-3">
                            <label for="invite_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="invite_name" name="name" required
                                   placeholder="John Doe">
                        </div>

                        <!-- Role -->
                        <div class="col-md-6 mb-3">
                            <label for="invite_role" class="form-label">Role <span class="text-danger">*</span></label>
                            <select class="form-select" id="invite_role" name="invitation_role" required>
                                <option value="">Select Role</option>
                                @foreach($roles as $role)
                                <option value="{{ $role->name }}">{{ $role->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="invite_role" class="form-label">Warehouse <span class="text-danger">*</span></label>
                            <select class="form-select" id="warehouse_id" name="warehouse_id" required>
                                <option value="">Select Warehouse</option>
                                @foreach($warehouses as $id => $name)
                                <option value="{{ $id }}">{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>


                        <!-- Personal Message -->
                        <div class="col-12 mb-3">
                            <label for="invite_message" class="form-label">Personal Message (Optional)</label>
                            <textarea class="form-control" id="invite_message" name="invitation_message" rows="3"
                                      placeholder="Add a personal message to include with the invitation..."></textarea>
                            <div class="form-text">This message will be included in the invitation email.</div>
                        </div>
                    </div>

                    <!-- Preview -->
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle me-1"></i>What happens next?</h6>
                        <ul class="mb-0">
                            <li>An invitation email will be sent to the provided email address</li>
                            <li>The user will receive a link to create their account</li>
                            <li>Once they accept, they'll have access to this tenant with the selected role</li>
                            <li>The invitation expires in 7 days</li>
                        </ul>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-send me-1"></i>Send Invitation
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif

<!-- Cancel Invitation Modal -->
<div class="modal fade" id="cancelInvitationModal" tabindex="-1" aria-labelledby="cancelInvitationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cancelInvitationModalLabel">
                    <i class="bi bi-exclamation-triangle text-warning me-2"></i>Cancel Invitation
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-start">
                    <div class="flex-shrink-0">
                        <div class="avatar avatar-sm avatar-circle bg-soft-warning">
                            <i class="bi bi-exclamation-triangle text-warning"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-2">Are you sure you want to cancel this invitation?</h6>
                        <p class="text-muted mb-3">
                            This will permanently cancel the invitation for <strong id="invitationEmail">-</strong>.
                            The invited user will no longer be able to accept this invitation.
                        </p>
                        <div class="alert alert-warning d-flex align-items-center">
                            <i class="bi bi-info-circle me-2"></i>
                            <small>This action cannot be undone. You can send a new invitation if needed.</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>Keep Invitation
                </button>
                <button type="button" class="btn btn-danger" id="confirmCancelInvitation">
                    <i class="bi bi-trash me-1"></i>Cancel Invitation
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
  $("document").ready(function () {
    dataTableBtn()
  });

  // Invitation management
  document.addEventListener('DOMContentLoaded', function() {
    // Resend invitation
    document.querySelectorAll('.resend-invitation-btn').forEach(button => {
        button.addEventListener('click', function() {
            const invitationId = this.dataset.invitationId;

            if (confirm('Are you sure you want to resend this invitation?')) {
                fetch('/invitations/resend', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        invitation_id: invitationId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification.success(data.success || 'Invitation resent successfully!');
                    } else {
                        showNotification.error(data.error || 'Failed to resend invitation');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification.error('Failed to resend invitation. Please try again.');
                });
            }
        });
    });

    // Cancel invitation - show modal
    let currentInvitationId = null;
    document.querySelectorAll('.cancel-invitation-btn').forEach(button => {
        button.addEventListener('click', function() {
            currentInvitationId = this.dataset.invitationId;

            // Find the invitation email from the table row
            const row = this.closest('tr');
            const emailCell = row.querySelector('td:nth-child(2)'); // Assuming email is in 2nd column
            const email = emailCell ? emailCell.textContent.trim() : 'this user';

            // Update modal content
            document.getElementById('invitationEmail').textContent = email;

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('cancelInvitationModal'));
            modal.show();
        });
    });

    // Handle modal confirmation
    document.getElementById('confirmCancelInvitation').addEventListener('click', function() {
        if (!currentInvitationId) return;

        // Disable button and show loading
        const button = this;
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status"></span>Cancelling...';

        fetch('/invitations/cancel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                invitation_id: currentInvitationId
            })
        })
        .then(response => response.json())
        .then(data => {
            // Hide modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('cancelInvitationModal'));
            modal.hide();

            if (data.success) {
                showNotification.success(data.success || 'Invitation cancelled successfully!');
                // Reload page to update the list
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showNotification.error(data.error || 'Failed to cancel invitation');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification.error('Failed to cancel invitation. Please try again.');

            // Hide modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('cancelInvitationModal'));
            modal.hide();
        })
        .finally(() => {
            // Reset button
            button.disabled = false;
            button.innerHTML = originalText;
            currentInvitationId = null;
        });
    });
  });
</script>

@endpush