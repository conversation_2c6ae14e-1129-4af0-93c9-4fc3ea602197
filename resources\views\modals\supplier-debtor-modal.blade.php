

<form action="/save-supplier-debtor" method="POST">
	@csrf
	<x-lg-modal>
		<x-slot name="class">supplier-debtor-modal</x-slot>
		<x-slot name="title">ADD SUPPLIER DEBTOR</x-slot>


	    <div class="" id="supplier-debtor-modal-el">
	    	<input type="hidden" value="supplier-debtors" name="type" required>
	      <div class="row mb-4">

					<div class=" mb-4 col-sm-6">
						 <div class="tom-select-custom">
	          	<label>Supplier</label>
	            <select class="js-select form-select branch-filter" required name="supplier_id" autocomplete="off" data-hs-tom-select-options='{
	              "placeholder": "Select a supplier..."
	            }'>
	            @foreach( App\Models\Supplier::get() as $supplier)
	              <option value="{{ $supplier->id }}">
	                {{ $supplier->name }}
	              </option>
	            @endforeach
	            </select>
	          </div>
					</div>


					<div class=" mb-4 col-sm-6">
						<div class="tom-select-custom">
		          	<label>Category</label>
		            <select class="js-select form-select branch-filter" required name="category" autocomplete="off" data-hs-tom-select-options='{
		              "placeholder": "Select a category..."
		            }'>
		            @foreach( App\Models\Category::where("applied_to", 'debtors')->get() as $category)
		              <option value="{{ $category->name }}">
		                {{ $category->name }}
		              </option>
		            @endforeach
		            </select>
	          	</div>
					</div>

					@if( auth()->user()->isSuperAdmin())
					<div class=" mb-4 col-sm-6">
						<div class="tom-select-custom">
	          	<label>Branch</label>
	            <select class="js-select form-select branch-filter" required name="branch_id" required autocomplete="off" data-hs-tom-select-options='{
	              "placeholder": "Select a branch..."
	            }'>
	            @foreach( App\Models\Branch::get() as $branch)
	              <option value="{{ $branch->id }}" @if($branch->id == request()->branch_id ) selected @endif>
	                {{ $branch->name }}
	              </option>
	            @endforeach
	            </select>
          	</div>
					</div>
		      @endif

					<div class="col-sm-6 mb-4">
						<label>Amount:</label>
				    <input type="number" step="0.01" value="" class="form-control" name="amount_total" required>	
					</div>

					<div class="col-sm-12 mb-4">
						<label>Note:</label>
						<textarea name="description" class="form-control" placeholder="Add comment." required></textarea>
					</div>

	      </div>
	    </div>

		<x-slot name="footer">
			<button type="submit" class="btn btn-primary"> Save </button>
		</x-slot>
	</x-lg-modal>


</form>




@push('scripts')

  <script type="text/javascript">
    
    new Vue({
      el: "#supplier-debtor-modal-el",
      data(){
        return{
          customer_id: null,
          customer: null,
        }
      },

      methods: {

        getCustomerBalance() {
        	axios.get('/ajax-customer-balance/' + this.customer_id ).then( res => {
        		this.customer = res.data;
            console.log(this.customer)
        	});
        }


      },


      created(){

      	// this.getProducts();
      }

    })

  </script>

@endpush
