@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <!-- Header -->
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">@lang('crud.warehouses.index_title')</h4>
                </div>

                <div class="col-auto">
                    <!-- Filter -->
                    <div class="row align-items-sm-center">
                        <div class="col-sm">
                            <form>
                                <!-- Search -->
                                <div class="input-group input-group-merge input-group-flush">
                                    <div class="input-group-prepend input-group-text">
                                        <i class="bi-search"></i>
                                    </div>
                                    <input
                                        id="indexSearch"
                                        type="text"
                                        name="search"
                                        placeholder="{{ __('crud.common.search') }}"
                                        value="{{ $search ?? '' }}"
                                        class="form-control"
                                        autocomplete="off"
                                    />
                                    <div class="input-group-append input-group-text">
                                        <button type="submit" class="btn btn-link p-0">
                                            <i class="bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>
                                <!-- End Search -->
                            </form>
                        </div>

                        <div class="col-sm-auto">
                            @can('create', App\Models\Warehouse::class)
                            <a href="{{ route('warehouses.create') }}" class="btn btn-primary">
                                <i class="bi-plus-circle me-1"></i> @lang('crud.common.create')
                            </a>
                            @endcan
                        </div>
                    </div>
                    <!-- End Filter -->
                </div>
            </div>
        </div>
        <!-- End Header -->

        <!-- Table -->
        <div class="table-responsive datatable-custom">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            @lang('crud.warehouses.inputs.name')
                        </th>
                        <th class="text-left">
                            @lang('crud.warehouses.inputs.code')
                        </th>
                        <th class="text-left">
                            @lang('crud.warehouses.inputs.address')
                        </th>
                        <th class="text-left">
                            @lang('crud.warehouses.inputs.phone')
                        </th>
                        <th class="text-left">
                            @lang('crud.warehouses.inputs.manager_name')
                        </th>
                        <th class="text-left">
                            @lang('crud.warehouses.inputs.branch_id')
                        </th>
                        <th class="text-center">
                            @lang('crud.warehouses.inputs.is_active')
                        </th>
                        <th class="text-center">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($warehouses as $warehouse)
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h5 class="text-inherit mb-0">{{ $warehouse->name ?? '-' }}</h5>
                                </div>
                            </div>
                        </td>
                        <td>{{ $warehouse->code ?? '-' }}</td>
                        <td>{{ $warehouse->address ?? '-' }}</td>
                        <td>{{ $warehouse->phone ?? '-' }}</td>
                        <td>{{ $warehouse->manager->name ?? '-' }}</td>
                        <td>{{ optional($warehouse->branch)->name ?? '-' }}</td>
                        <td class="text-center">
                            <div class="form-check form-switch d-flex justify-content-center">
                                <span class="badge bg-{{ $warehouse->is_active ? 'success' : 'danger' }}">
                                    {{ $warehouse->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                @can('update', $warehouse)
                                <a href="{{ route('warehouses.edit', $warehouse) }}" class="btn btn-white btn-sm">
                                    <i class="bi-pencil-fill me-1"></i> Edit
                                </a>
                                @endcan

                                <!-- Button Group -->
                                <div class="btn-group">
                                    <button type="button" class="btn btn-white btn-icon btn-sm dropdown-toggle dropdown-toggle-empty" id="warehouseDropdown{{ $warehouse->id }}" data-bs-toggle="dropdown" aria-expanded="false"></button>
                                    <div class="dropdown-menu dropdown-menu-end mt-1" aria-labelledby="warehouseDropdown{{ $warehouse->id }}">
                                        @can('view', $warehouse)
                                        <a class="dropdown-item" href="{{ route('warehouses.show', $warehouse) }}">
                                            <i class="bi-eye dropdown-item-icon"></i> View
                                        </a>
                                        @endcan
                                        @can('delete', $warehouse)
                                        <div class="dropdown-divider"></div>
                                        <form action="{{ route('warehouses.destroy', $warehouse) }}" method="POST" onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')">
                                            @csrf @method('DELETE')
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="bi-trash dropdown-item-icon"></i> Delete
                                            </button>
                                        </form>
                                        @endcan
                                    </div>
                                </div>
                                <!-- End Button Group -->
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8">
                            <div class="text-center p-4">
                                <img class="mb-3" src="{{ asset('assets/svg/illustrations/oc-error.svg') }}" alt="Image Description" style="width: 10rem;">
                                <p class="mb-0">@lang('crud.common.no_items_found')</p>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <!-- End Table -->

        <!-- Footer -->
        <div class="card-footer">
            <div class="row justify-content-center justify-content-sm-between align-items-sm-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <div class="d-flex justify-content-center justify-content-sm-start align-items-center">
                        {!! $warehouses->render() !!}
                    </div>
                </div>
            </div>
        </div>
        <!-- End Footer -->
    </div>
</div>
@endsection
