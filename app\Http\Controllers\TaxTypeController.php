<?php

namespace App\Http\Controllers;

use App\Models\Account;
use App\Models\TaxType;
use Illuminate\Http\Request;

class TaxTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', TaxType::class);

        $search = $request->get('search', '');
        $isActive = $request->get('is_active', '');

        $taxTypes = TaxType::search($search);
        
        if ($isActive !== '') {
            $taxTypes->where('is_active', $isActive);
        }
        
        $taxTypes = $taxTypes->latest()
            ->paginate()
            ->withQueryString();

        return view('app.tax_types.index', compact(
            'taxTypes', 
            'search',
            'isActive'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', TaxType::class);

        $taxAccounts = Account::where('is_active', true)
            ->whereHas('accountType', function ($query) {
                $query->where('classification', 'liability')
                    ->orWhere('classification', 'asset');
            })
            ->orderBy('code')
            ->get()
            ->pluck('full_name', 'id');

        return view('app.tax_types.create', compact('taxAccounts'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', TaxType::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:255|unique:tax_types,code',
            'description' => 'nullable|string',
            'tax_type' => 'required|string|in:vat,sales_tax,withholding,income_tax,other',
            'rate' => 'required|numeric|min:0|max:1',
            'is_active' => 'boolean',
            'tax_account_id' => 'required|tenant_exists:accounts,id',
        ]);

        $taxType = TaxType::create($validated);

        return redirect()
            ->route('tax-types.edit', $taxType)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\TaxType  $taxType
     * @return \Illuminate\Http\Response
     */
    public function show(TaxType $taxType)
    {
        $this->authorize('view', $taxType);

        $taxTransactions = $taxType->taxTransactions()
            ->latest('transaction_date')
            ->paginate(10);

        return view('app.tax_types.show', compact('taxType', 'taxTransactions'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\TaxType  $taxType
     * @return \Illuminate\Http\Response
     */
    public function edit(TaxType $taxType)
    {
        $this->authorize('update', $taxType);

        $taxAccounts = Account::where('is_active', true)
            ->whereHas('accountType', function ($query) {
                $query->where('classification', 'liability')
                    ->orWhere('classification', 'asset');
            })
            ->orderBy('code')
            ->get()
            ->pluck('full_name', 'id');

        return view('app.tax_types.edit', compact('taxType', 'taxAccounts'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\TaxType  $taxType
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, TaxType $taxType)
    {
        $this->authorize('update', $taxType);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:255|unique:tax_types,code,' . $taxType->id,
            'description' => 'nullable|string',
            'tax_type' => 'required|string|in:vat,sales_tax,withholding,income_tax,other',
            'rate' => 'required|numeric|min:0|max:1',
            'is_active' => 'boolean',
            'tax_account_id' => 'required|tenant_exists:accounts,id',
        ]);

        $taxType->update($validated);

        return redirect()
            ->route('tax-types.edit', $taxType)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\TaxType  $taxType
     * @return \Illuminate\Http\Response
     */
    public function destroy(TaxType $taxType)
    {
        $this->authorize('delete', $taxType);

        if ($taxType->taxTransactions()->count() > 0) {
            return redirect()
                ->route('tax-types.index')
                ->withError('Tax type has transactions and cannot be deleted.');
        }

        $taxType->delete();

        return redirect()
            ->route('tax-types.index')
            ->withSuccess(__('crud.common.removed'));
    }
}
