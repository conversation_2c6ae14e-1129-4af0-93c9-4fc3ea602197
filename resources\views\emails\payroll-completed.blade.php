@extends('emails.layout')

@section('title', 'Payroll Processing Completed - ' . $period)

@section('content')
<div class="greeting">
    Hello Team,
</div>

<div class="content">
    <p>The payroll processing for <strong>{{ $period }}</strong> has been successfully completed.</p>
    
    <p>All employee payslips have been generated and are ready for distribution.</p>
</div>

<div class="success-box">
    <h3 style="margin-bottom: 20px; color: #2e7d32;">📊 Payroll Summary</h3>
    
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
        <div style="text-align: center; padding: 15px; background-color: rgba(255,255,255,0.3); border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #2e7d32;">{{ $summary['total_employees'] }}</div>
            <div style="font-size: 14px; opacity: 0.9;">Total Employees</div>
        </div>
        <div style="text-align: center; padding: 15px; background-color: rgba(255,255,255,0.3); border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #2e7d32;">K{{ number_format($summary['total_net_pay'], 0) }}</div>
            <div style="font-size: 14px; opacity: 0.9;">Total Net Pay</div>
        </div>
    </div>
</div>

<div class="info-box">
    <h4 style="margin-bottom: 15px;">💰 Financial Breakdown</h4>
    <table class="details-table">
        <tr>
            <td><strong>Total Gross Pay:</strong></td>
            <td class="amount">K{{ number_format($summary['total_gross_pay'], 2) }}</td>
        </tr>
        <tr>
            <td><strong>Total Deductions:</strong></td>
            <td>K{{ number_format($summary['total_deductions'], 2) }}</td>
        </tr>
        <tr>
            <td><strong>Total Tax (PAYE):</strong></td>
            <td>K{{ number_format($summary['total_tax'], 2) }}</td>
        </tr>
        <tr style="background-color: #e8f5e8;">
            <td><strong>Total Net Pay:</strong></td>
            <td class="amount" style="font-size: 16px;">K{{ number_format($summary['total_net_pay'], 2) }}</td>
        </tr>
    </table>
</div>

@if($summary['total_overtime_hours'] > 0)
<div class="info-box">
    <h4 style="margin-bottom: 15px;">⏰ Overtime Summary</h4>
    <table class="details-table">
        <tr>
            <td><strong>Total Overtime Hours:</strong></td>
            <td>{{ number_format($summary['total_overtime_hours'], 2) }} hours</td>
        </tr>
        <tr>
            <td><strong>Total Overtime Pay:</strong></td>
            <td class="amount">K{{ number_format($summary['total_overtime_pay'], 2) }}</td>
        </tr>
    </table>
</div>
@endif

@php
    $loanDeductions = 0;
    $loanCount = 0;
    foreach($payrolls as $payroll) {
        $loans = $payroll->payrollItems()->where('type', 'DEDUCTION')->whereNotNull('loan_id')->get();
        $loanDeductions += $loans->sum('amount');
        $loanCount += $loans->count();
    }
@endphp

@if($loanCount > 0)
<div class="info-box">
    <h4 style="margin-bottom: 15px;">🏦 Loan Deductions Summary</h4>
    <table class="details-table">
        <tr>
            <td><strong>Total Loan Deductions:</strong></td>
            <td>{{ $loanCount }} deductions</td>
        </tr>
        <tr>
            <td><strong>Total Loan Amount Deducted:</strong></td>
            <td class="amount">K{{ number_format($loanDeductions, 2) }}</td>
        </tr>
    </table>
</div>
@endif

<div class="highlight-box">
    <h3 style="margin-bottom: 15px;">📋 Next Steps</h3>
    <ul style="text-align: left; margin: 0; padding-left: 20px;">
        <li>All employees will receive their payslips via email</li>
        <li>Payroll journal entries are ready for accounting review</li>
        <li>Bank transfer files can be generated for salary payments</li>
        <li>Tax and statutory deduction reports are available</li>
    </ul>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="{{ route('payroll.index') }}" class="btn btn-primary">
        View Payroll Dashboard
    </a>
    <a href="{{ route('payroll-reports.journal') }}" class="btn btn-success">
        Generate Reports
    </a>
</div>

<div class="content">
    <h4>Employee Distribution Summary:</h4>
    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0;">
        @php
            $departments = $payrolls->groupBy('employee.department');
        @endphp
        
        @if($departments->count() > 0)
            <table class="details-table">
                <thead>
                    <tr>
                        <th>Department</th>
                        <th>Employees</th>
                        <th>Total Net Pay</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($departments as $department => $deptPayrolls)
                    <tr>
                        <td>{{ $department ?: 'Unassigned' }}</td>
                        <td>{{ $deptPayrolls->count() }}</td>
                        <td class="amount">K{{ number_format($deptPayrolls->sum('net_pay'), 2) }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <p>Department breakdown not available.</p>
        @endif
    </div>
</div>

<div class="warning-box">
    <h4 style="margin-bottom: 10px;">⚠️ Important Reminders</h4>
    <ul style="margin: 10px 0; padding-left: 20px;">
        <li>Verify all bank transfer details before processing payments</li>
        <li>Ensure all statutory deductions are submitted to relevant authorities</li>
        <li>Archive payroll documents as per company policy</li>
        <li>Review any payroll exceptions or adjustments</li>
    </ul>
</div>

<div class="content">
    <p><strong>Payroll processed by:</strong> {{ auth()->user()->name ?? 'System' }}</p>
    <p><strong>Processing completed at:</strong> {{ now()->format('F d, Y \a\t H:i:s') }}</p>
</div>
@endsection
