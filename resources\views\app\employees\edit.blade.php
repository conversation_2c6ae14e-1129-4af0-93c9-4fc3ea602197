@extends('layouts.app')

@push('styles')
<link href="{{ asset('css/employee-forms.css') }}" rel="stylesheet">
@endpush

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-user-edit text-primary"></i> Edit Employee
            </h1>
            <p class="text-muted mb-0">Update {{ $employee->name }}'s information</p>
        </div>
        <div>
            <a href="{{ route('employees.show', $employee) }}" class="btn btn-outline-info me-2">
                <i class="fas fa-eye"></i> View Profile
            </a>
            <a href="{{ route('employees.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Employees
            </a>
        </div>
    </div>

    <form action="{{ route('employees.update', $employee) }}" method="POST" id="employeeEditForm" novalidate>
        @csrf
        @method('PUT')

        <div class="row">
            <!-- Left Column -->
            <div class="col-lg-8">
                <!-- Personal Information Card -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user"></i> Personal Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror"
                                           value="{{ old('name', $employee->name) }}" required placeholder="Enter full name">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" name="email" id="email" class="form-control @error('email') is-invalid @enderror"
                                           value="{{ old('email', $employee->email) }}" required placeholder="<EMAIL>">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="text" name="phone" id="phone" class="form-control @error('phone') is-invalid @enderror"
                                           value="{{ old('phone', $employee->phone) }}" placeholder="+265 xxx xxx xxx">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_of_birth" class="form-label">Date of Birth <span class="text-danger">*</span></label>
                                    <input type="date" name="date_of_birth" id="date_of_birth" class="form-control @error('date_of_birth') is-invalid @enderror"
                                           value="{{ old('date_of_birth', $employee->getFormattedDateOfBirth()) }}" required>
                                    @error('date_of_birth')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="gender" class="form-label">Gender <span class="text-danger">*</span></label>
                                    <select name="gender" id="gender" class="form-select @error('gender') is-invalid @enderror" required>
                                        <option value="">Select Gender</option>
                                        <option value="Male" {{ old('gender', $employee->gender) == "Male" ? 'selected' : '' }}>Male</option>
                                        <option value="Female" {{ old('gender', $employee->gender) == "Female" ? 'selected' : '' }}>Female</option>
                                    </select>
                                    @error('gender')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="marital_status" class="form-label">Marital Status</label>
                                    <select name="marital_status" id="marital_status" class="form-select @error('marital_status') is-invalid @enderror">
                                        <option value="">Select Marital Status</option>
                                        <option value="Single" {{ old('marital_status', $employee->marital_status) == "Single" ? 'selected' : '' }}>Single</option>
                                        <option value="Married" {{ old('marital_status', $employee->marital_status) == "Married" ? 'selected' : '' }}>Married</option>
                                        <option value="Divorced" {{ old('marital_status', $employee->marital_status) == "Divorced" ? 'selected' : '' }}>Divorced</option>
                                        <option value="Widowed" {{ old('marital_status', $employee->marital_status) == "Widowed" ? 'selected' : '' }}>Widowed</option>
                                    </select>
                                    @error('marital_status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea name="address" id="address" class="form-control @error('address') is-invalid @enderror"
                                      rows="3" placeholder="Enter complete address">{{ old('address', $employee->address) }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Employment Information Card -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-briefcase"></i> Employment Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="basic_pay" class="form-label">Basic Pay (Monthly) <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">K</span>
                                        <input type="number" name="basic_pay" id="basic_pay" class="form-control @error('basic_pay') is-invalid @enderror"
                                               value="{{ old('basic_pay', $employee->basic_pay) }}" step="0.01" min="0" required placeholder="0.00">
                                    </div>
                                    @error('basic_pay')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="grade" class="form-label">Grade/Position</label>
                                    <input type="text" name="grade" id="grade" class="form-control @error('grade') is-invalid @enderror"
                                           value="{{ old('grade', $employee->grade) }}" placeholder="e.g., Manager, Assistant, etc.">
                                    @error('grade')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Job Description</label>
                            <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror"
                                      rows="3" placeholder="Describe the employee's role and responsibilities">{{ old('description', $employee->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="user_id" class="form-label">Link to User Account (Optional)</label>
                            <select name="user_id" id="user_id" class="form-select @error('user_id') is-invalid @enderror">
                                <option value="">Select User Account</option>
                                @foreach($users as $id => $name)
                                    <option value="{{ $id }}" {{ old('user_id', $employee->user_id) == $id ? 'selected' : '' }}>{{ $name }}</option>
                                @endforeach
                            </select>
                            @error('user_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Link this employee to an existing user account for system access.</div>
                        </div>
                    </div>
                </div>

                <!-- Emergency Contact Card -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-phone-alt"></i> Emergency Contact & Medical Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="emergency_phone" class="form-label">Emergency Phone</label>
                                    <input type="text" name="emergency_phone" id="emergency_phone" class="form-control @error('emergency_phone') is-invalid @enderror"
                                           value="{{ old('emergency_phone', $employee->emergency_phone) }}" placeholder="+265 xxx xxx xxx">
                                    @error('emergency_phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="emergency_address" class="form-label">Emergency Address</label>
                                    <textarea name="emergency_address" id="emergency_address" class="form-control @error('emergency_address') is-invalid @enderror"
                                              rows="2" placeholder="Emergency contact address">{{ old('emergency_address', $employee->emergency_address) }}</textarea>
                                    @error('emergency_address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="medical_description" class="form-label">Medical Information</label>
                            <textarea name="medical_description" id="medical_description" class="form-control @error('medical_description') is-invalid @enderror"
                                      rows="3" placeholder="Any medical conditions, allergies, or important health information">{{ old('medical_description', $employee->medical_description) }}</textarea>
                            @error('medical_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Any medical conditions or allergies to note for emergency situations.</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Employee Summary and Actions -->
            <div class="col-lg-4">
                <!-- Employee Summary Card -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-user-circle"></i> Employee Summary
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <div class="avatar avatar-xl mx-auto mb-3">
                                <span class="avatar-initial bg-primary rounded-circle fs-1">
                                    {{ substr($employee->name, 0, 1) }}
                                </span>
                            </div>
                            <h6 class="mb-1">{{ $employee->name }}</h6>
                            <p class="text-muted small">{{ $employee->grade ?? 'Employee' }}</p>
                        </div>

                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h6 class="text-primary mb-0">K{{ number_format($employee->basic_pay, 2) }}</h6>
                                    <small class="text-muted">Basic Pay</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6 class="text-success mb-0">{{ $employee->created_at->diffForHumans() }}</h6>
                                <small class="text-muted">Joined</small>
                            </div>
                        </div>

                        <hr>

                        <div class="small">
                            <div class="d-flex justify-content-between mb-1">
                                <span>Email:</span>
                                <span class="text-muted">{{ $employee->email }}</span>
                            </div>
                            @if($employee->phone)
                            <div class="d-flex justify-content-between mb-1">
                                <span>Phone:</span>
                                <span class="text-muted">{{ $employee->phone }}</span>
                            </div>
                            @endif
                            @if($employee->user_id)
                            <div class="d-flex justify-content-between">
                                <span>System Access:</span>
                                <span class="badge bg-success">Linked</span>
                            </div>
                            @else
                            <div class="d-flex justify-content-between">
                                <span>System Access:</span>
                                <span class="badge bg-secondary">Not Linked</span>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Card -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-cogs"></i> Quick Actions
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Employee
                            </button>
                            <a href="{{ route('employees.show', $employee) }}" class="btn btn-outline-info">
                                <i class="fas fa-eye"></i> View Profile
                            </a>
                            <a href="{{ route('employees.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-list"></i> All Employees
                            </a>
                        </div>
                        <hr>
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Changes will be saved immediately after updating.
                        </small>
                    </div>
                </div>

                <!-- Update History Card -->
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-history"></i> Update History
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="small">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Created:</span>
                                <span class="text-muted">{{ $employee->created_at->format('M d, Y') }}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Last Updated:</span>
                                <span class="text-muted">{{ $employee->updated_at->format('M d, Y') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation feedback
    const form = document.getElementById('employeeEditForm');

    form.addEventListener('submit', function(e) {
        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Auto-format phone numbers
    const phoneInputs = document.querySelectorAll('input[type="text"][name*="phone"]');
    phoneInputs.forEach(input => {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 0) {
                if (value.startsWith('265')) {
                    value = '+' + value;
                } else if (!value.startsWith('+')) {
                    value = '+265' + value;
                }
            }
            e.target.value = value;
        });
    });

    // Highlight changes
    const originalValues = {};
    const inputs = form.querySelectorAll('input, select, textarea');

    inputs.forEach(input => {
        originalValues[input.name] = input.value;

        input.addEventListener('change', function() {
            if (this.value !== originalValues[this.name]) {
                this.classList.add('border-warning');
            } else {
                this.classList.remove('border-warning');
            }
        });
    });
});
</script>
@endpush
@endsection
