@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">receipt</x-slot>
        <x-slot name="title">Invoices & Sales</x-slot>
        <x-slot name="subtitle">Manage all your sales transactions</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Invoices</li>
        </x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Invoice::class)
            <a href="{{ route('invoices.create') }}" class="btn btn-primary btn-sm">
                <i class="bi bi-plus-circle me-1"></i> @lang('crud.common.create') Sale
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-header-title">Filter Invoices</h5>
        </div>
        <div class="card-body">
            <form class="row g-3">
                <input type="hidden" name="from" value="{{ request()->from }}">
                <input type="hidden" name="to" value="{{ request()->to }}">
                
                <div class="col-md-4">
                    <label class="form-label">Date Range</label>
                    <button id="js-daterangepicker-predefined" type="button" class="btn btn-white w-100">
                        <i class="bi-calendar-week me-1"></i>
                        <span class="js-daterangepicker-predefined-preview">Select date range</span>
                    </button>
                </div>
                
                <div class="col-md-4">
                    <label class="form-label">Closing Date</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select day-filter" name="day" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a date..."
                        }'>
                            <option value="0">All Closing Days</option>
                            @foreach(Facades\App\Cache\Repo::getClosingDays() as $day)
                                <option value="{{ $day }}" @if($day == request()->day) selected @endif>
                                    {{ $day }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="col-md-4">
                    <label class="form-label">Product</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select product-filter" name="product_id" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a product..."
                        }'>
                            <option value="">All Products</option> 
                            @foreach(App\Models\Product::get() as $product)
                                <option value="{{ $product->id }}" @if($product->id == request()->product_id) selected @endif>
                                    {{ $product->name }} ~ {{ $product->description }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="col-12 d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-filter me-1"></i> Apply Filters
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- End Filters -->

    <!-- Card -->
    <div class="card d-print-none">
        <!-- Header -->
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">Invoice Management</h4>
                </div>
                <div class="col-auto">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-download me-1"></i> Export
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="exportDropdown">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-excel me-1"></i> Excel</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-pdf me-1"></i> PDF</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-text me-1"></i> CSV</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Header -->

        <!-- Body -->
        <div class="card-body">
            <!-- Nav Scroller -->
            <div class="js-nav-scroller hs-nav-scroller-horizontal">
                <span class="hs-nav-scroller-arrow-prev" style="display: none;">
                    <a class="hs-nav-scroller-arrow-link" href="javascript:;">
                        <i class="bi-chevron-left"></i>
                    </a>
                </span>

                <span class="hs-nav-scroller-arrow-next" style="display: none;">
                    <a class="hs-nav-scroller-arrow-link" href="javascript:;">
                        <i class="bi-chevron-right"></i>
                    </a>
                </span>

                <!-- Nav -->
                <ul class="nav nav-tabs nav-tabs-light nav-justified" id="invoicesStatusTab" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="paid-tab" data-bs-toggle="tab" href="#paid" role="tab" aria-controls="paid" aria-selected="true">
                            <div class="d-flex align-items-center">
                                <i class="bi-check-circle me-1 text-success"></i>
                                <span>Paid</span>
                                <span class="badge bg-soft-success text-success ms-2">{{ count($paidInvoices) }}</span>
                            </div>
                        </a>
                    </li>        
                    <li class="nav-item">
                        <a class="nav-link" id="pending-tab" data-bs-toggle="tab" href="#pending" role="tab" aria-controls="pending" aria-selected="false">
                            <div class="d-flex align-items-center">
                                <i class="bi-hourglass-split me-1 text-warning"></i>
                                <span>Discount Pending</span>
                                <span class="badge bg-soft-warning text-warning ms-2">{{ count($pendingInvoices) }}</span>
                            </div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="declined-tab" data-bs-toggle="tab" href="#declined" role="tab" aria-controls="declined" aria-selected="false">
                            <div class="d-flex align-items-center">
                                <i class="bi-exclamation-circle me-1 text-danger"></i>
                                <span>Action Required</span>
                                <span class="badge bg-soft-danger text-danger ms-2">{{ count($actionInvoices) }}</span>
                            </div>
                        </a>
                    </li>
                    @if(auth()->user()->can('view canceled invoices'))
                    <li class="nav-item">
                        <a class="nav-link" id="canceled-tab" data-bs-toggle="tab" href="#canceled" role="tab" aria-controls="canceled" aria-selected="false">
                            <div class="d-flex align-items-center">
                                <i class="bi-x-circle me-1 text-secondary"></i>
                                <span>Canceled</span>
                                <span class="badge bg-soft-secondary text-secondary ms-2">{{ count($canceledInvoices) }}</span>
                            </div>
                        </a>
                    </li>   
                    @endif
                </ul>
                <!-- End Nav -->
            </div>
            <!-- End Nav Scroller -->
        </div>
        <!-- End Body -->

        <!-- Body -->
        <div class="tab-content" id="invoicesStatusTabContent">
            <div class="tab-pane fade show active" id="paid" role="tabpanel" aria-labelledby="paid-tab">
                @include("app.invoices.paid-table")
            </div>

            @if(auth()->user()->can('view canceled invoices'))
            <div class="tab-pane fade" id="canceled" role="tabpanel" aria-labelledby="canceled-tab">
                @include("app.invoices.canceled-table")
            </div>
            @endif

            <div class="tab-pane fade" id="pending" role="tabpanel" aria-labelledby="pending-tab">
                @include("app.invoices.pending-table")
            </div>

            <div class="tab-pane fade" id="declined" role="tabpanel" aria-labelledby="declined-tab">
                @include("app.invoices.action-table")
            </div>
        </div>
        <!-- End Body -->
    </div>
    <!-- End Card -->
</div>
@endsection

@push('scripts')
<script>
    (function () {
        // Initialize DataTables
        dataTableBtn();
        
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    })();
</script>
@endpush