<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

use App\Traits\CreatedUpdatedTrait;

class DocumentType extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'description',
        'created_by',
        'updated_by',
        'year',
    ];

    protected $searchableFields = ['*'];

    public function leave()
    {
        return $this->belongsTo(Leave::class);
    }


    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }



}
