@extends('layouts.app')

@section('content')
<div class="container">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-header-title">@lang('crud.warehouses.create_title')</h1>
                <p class="page-header-text">Create a new warehouse in the system</p>
            </div>
            <div class="col-auto">
                <a href="{{ route('warehouses.index') }}" class="btn btn-ghost-secondary">
                    <i class="bi-arrow-left me-1"></i> @lang('crud.common.back')
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mb-3 mb-lg-0">
            <!-- Card -->
            <div class="card mb-3 mb-lg-5">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Warehouse Information</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <x-form
                        method="POST"
                        action="{{ route('warehouses.store') }}"
                        has-files
                        id="warehouseForm"
                    >
                        @include('app.warehouses.form-inputs')
                    </x-form>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>

        <div class="col-lg-4">
            <!-- Card -->
            <div class="card">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Actions</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <!-- Form Group -->
                        <div class="mb-4">
                            <label for="warehouseStatus" class="form-label">Status</label>
                            
                            <div class="form-check form-switch">
                                <input type="checkbox" class="form-check-input" id="warehouseStatus" checked form="warehouseForm">
                                <label class="form-check-label" for="warehouseStatus">Active</label>
                            </div>
                        </div>
                        <!-- End Form Group -->
                        
                        <div class="d-flex justify-content-end">
                            <a href="{{ route('warehouses.index') }}" class="btn btn-white me-2">Cancel</a>
                            <button type="submit" form="warehouseForm" class="btn btn-primary">
                                <i class="bi-check-circle me-1"></i> @lang('crud.common.create')
                            </button>
                        </div>
                    </div>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>
    </div>
</div>
@endsection
