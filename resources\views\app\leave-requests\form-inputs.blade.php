@php $editing = isset($leaveRequest) @endphp

<div class="row">


    <x-inputs.group class="col-sm-6 mb-4">
        <x-inputs.select name="leave_id" label="Leave" required>
            @php $selected = old('leave_id', ($editing ? $leaveRequest->leave_id : '')) @endphp
            <option disabled {{ empty($selected) ? 'selected' : '' }}>Please select the Leave</option>
            @foreach($leaves as $value => $label)
            <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }} >{{ $label }}</option>
            @endforeach
        </x-inputs.select>
    </x-inputs.group>

    <x-inputs.group class="col-sm-12 col-lg-6 mb-4">
        <x-inputs.text
            name="days"
            label="days"
            min="0.5"
            class="days"
            :value="old('days', ($editing ? $leaveRequest->days : 1))"
            maxlength="255"
            required
            placeholder="days"
        ></x-inputs.text>
    </x-inputs.group>



    <x-inputs.group class="col-sm-12 col-lg-6 mb-4">
        <x-inputs.date
            name="date_from"
            label="date_from"
            class="date-change"
            :value="old('date_from', ($editing ? $leaveRequest->date_from : now()->format('Y-m-d')))"
            maxlength="255"
            min="{{ now()}}"
            placeholder="date_from"
            required
        ></x-inputs.date>
    </x-inputs.group>

    <x-inputs.group class="col-sm-12 col-lg-6 mb-4">
        <x-inputs.date
            name="date_to"
            label="date_to"
            class="date-change"
            :value="old('date_to', ($editing ? $leaveRequest->date_to :  now()->format('Y-m-d')))"
            maxlength="255"
            min="{{ now()}}"
            placeholder="date_to"
        ></x-inputs.date>
    </x-inputs.group>


    <x-inputs.group class="col-sm-12 col-lg-12">
        <x-inputs.textarea
            name="description"
            label="Comment"
            maxlength="255"
            >{{ old('description', ($editing ? $leaveRequest->description : ''))
            }}</x-inputs.textarea
        >
    </x-inputs.group>

</div>
