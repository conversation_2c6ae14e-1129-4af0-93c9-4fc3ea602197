<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tax_brackets', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable(); // e.g., "First Bracket", "Second Bracket"
            $table->decimal('min_amount', 20, 2)->default(0); // Minimum amount for this bracket
            $table->decimal('max_amount', 20, 2)->nullable(); // Maximum amount (null for unlimited)
            $table->decimal('rate', 5, 4); // Tax rate as decimal (e.g., 0.25 for 25%)
            $table->integer('order')->default(0); // Order of application
            $table->boolean('is_active')->default(true);
            $table->text('description')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['is_active', 'order']);
            $table->index('min_amount');
            $table->index('max_amount');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tax_brackets');
    }
};
