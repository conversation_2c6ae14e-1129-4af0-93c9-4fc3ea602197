@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">geo-alt</x-slot>
        <x-slot name="title">Create Location</x-slot>
        <x-slot name="subtitle">Add a new location to your system</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('locations.index') }}">Locations</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('locations.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Locations
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row ">
        <!-- Create Location Card -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-header-title">
                    <i class="bi bi-plus-circle me-2"></i>New Location Information
                </h4>
            </div>
            <div class="card-body">
                <!-- Display Validation Errors -->
                @if ($errors->any())
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <h6 class="alert-heading">
                            <i class="bi bi-exclamation-triangle me-1"></i>
                            Please fix the following errors:
                        </h6>
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                <x-form
                    method="POST"
                    action="{{ route('locations.store') }}"
                    id="locationForm"
                >
                    @include('app.locations.form-inputs')

                    <!-- Form Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">Ready to create this location?</h6>
                                            <small class="text-muted">Make sure all information is correct before saving.</small>
                                        </div>
                                        <div class="btn-group">
                                            <a href="{{ route('locations.index') }}" class="btn btn-outline-secondary">
                                                <i class="bi bi-x-circle me-1"></i> Cancel
                                            </a>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="bi bi-check-circle me-1"></i> Create Location
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- End Form Actions -->
                </x-form>
            </div>
        </div>
        <!-- End Create Location Card -->
    </div>
</div>
<!-- End Content -->

@push('scripts')
<script>
$(document).ready(function() {
    // Simple form validation - let Laravel handle the heavy lifting
    $('#locationForm').on('submit', function(e) {
        var nameField = $('#name');
        var statusField = $('#status_id');
        var hasErrors = false;

        // Check name field
        if (!nameField.val().trim()) {
            nameField.addClass('is-invalid');
            hasErrors = true;
        } else {
            nameField.removeClass('is-invalid');
        }

        // Check status field
        if (!statusField.val()) {
            statusField.addClass('is-invalid');
            hasErrors = true;
        } else {
            statusField.removeClass('is-invalid');
        }

        if (hasErrors) {
            e.preventDefault();
            // Show error message if toastr is available
            if (typeof toastr !== 'undefined') {
                toastr.error('Please fill in all required fields.');
            } else {
                alert('Please fill in all required fields.');
            }
            // Focus on first error
            $('.is-invalid').first().focus();
        }
    });

    // Real-time validation feedback
    $('#name, #status_id').on('blur change', function() {
        if ($(this).val()) {
            $(this).removeClass('is-invalid');
        }
    });

    // Form submission feedback
    $('#locationForm').on('submit', function() {
        var submitBtn = $(this).find('button[type="submit"]');
        if (!submitBtn.hasClass('disabled')) {
            submitBtn.html('<i class="bi bi-hourglass-split me-1"></i> Creating...').addClass('disabled');
        }
    });
});
</script>
@endpush
@endsection
