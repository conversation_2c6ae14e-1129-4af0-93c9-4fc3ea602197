<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

use App\Traits\CreatedUpdatedTrait;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Models\Activity;

use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use App\Traits\BusinessTypeTrait;

class ActiveConfig extends Model implements HasMedia
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use InteractsWithMedia;
    use BusinessTypeTrait;

    use CreatedUpdatedTrait;
    // use LogsActivity;
    protected $connection = "tenant";

    
    // protected static $submitEmptyLogs = false;
    // protected static $logOnlyDirty = true;
    // protected static $logFillable = true;
    // protected static $logName = 'active_config';

    // //only the `deleted` event will get logged automatically
    // // protected static $recordEvents = ['deleted'];
    // public function getActivitylogOptions(): LogOptions
    // {
    //     return LogOptions::defaults()
    //         ->setIpAddressForEvent(fn() => request()->ip() );
    // }

    // public function tapActivity(Activity $activity, string $eventName)
    // {
    //     $activity->ip_address = str_replace('::1', '127.0.0.1', request()->ip() );
    // }

    protected $fillable = [
        'key',
        'value',
        'created_by',
        'updated_by',
        'user_id',
        'school_id',
    ];

    protected $appends = ['path'];

    public function getPathAttribute(){
        $file = $this->file();
        return $file ? $file->getUrl() : null;
    }

    public function file($collection = "media"){
        return $this->getMedia($collection)->first();
    }

    public function getValueAttribute($value) {
        return $this->path ?? $value;
    }


    protected $searchableFields = ['*'];

    protected $table = 'active_configs';

    public function createdBy()
    {
        return $this->belongsTo(User::class, "created_by");
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
