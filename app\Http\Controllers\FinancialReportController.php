<?php

namespace App\Http\Controllers;

use App\Models\Account;
use App\Models\AccountType;
use App\Models\FiscalYear;
use App\Models\FiscalPeriod;
use App\Models\JournalEntry;
use App\Models\JournalEntryLine;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use PDF;

class FinancialReportController extends Controller
{
    /**
     * Display the report selection page.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $fiscalYears = FiscalYear::orderBy('name')
            ->pluck('name', 'id');
            
        $fiscalPeriods = FiscalPeriod::orderBy('name')
            ->pluck('name', 'id');

        return view('app.financial_reports.index', compact('fiscalYears', 'fiscalPeriods'));
    }
    
    /**
     * Generate the trial balance report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function trialBalance(Request $request)
    {
        $validated = $request->validate([
            'fiscal_year_id' => 'required|exists:fiscal_years,id',
            'fiscal_period_id' => 'nullable|exists:fiscal_periods,id',
            'as_of_date' => 'nullable|date',
            'include_zero_balances' => 'boolean',
            'format' => 'required|in:html,pdf,csv',
        ]);
        
        $fiscalYear = FiscalYear::findOrFail($validated['fiscal_year_id']);
        $fiscalPeriod = null;
        $asOfDate = null;
        
        if (!empty($validated['fiscal_period_id'])) {
            $fiscalPeriod = FiscalPeriod::findOrFail($validated['fiscal_period_id']);
            $asOfDate = $fiscalPeriod->end_date;
        } elseif (!empty($validated['as_of_date'])) {
            $asOfDate = $validated['as_of_date'];
        } else {
            $asOfDate = date('Y-m-d');
        }
        
        $includeZeroBalances = $validated['include_zero_balances'] ?? false;
        
        // Get all accounts with their balances
        $accounts = Account::with('accountType')
            ->where('is_active', true)
            ->orderBy('code')
            ->get();
            
        $trialBalance = [];
        $totalDebit = 0;
        $totalCredit = 0;
        
        foreach ($accounts as $account) {
            // Get journal entry lines for this account up to the as_of_date
            $debitSum = JournalEntryLine::join('journal_entries', 'journal_entry_lines.journal_entry_id', '=', 'journal_entries.id')
                ->where('journal_entry_lines.account_id', $account->id)
                ->where('journal_entries.status', 'posted')
                ->where('journal_entries.entry_date', '<=', $asOfDate)
                ->sum('journal_entry_lines.debit');
                
            $creditSum = JournalEntryLine::join('journal_entries', 'journal_entry_lines.journal_entry_id', '=', 'journal_entries.id')
                ->where('journal_entry_lines.account_id', $account->id)
                ->where('journal_entries.status', 'posted')
                ->where('journal_entries.entry_date', '<=', $asOfDate)
                ->sum('journal_entry_lines.credit');
                
            // Add transactions from the old system
            $debitSum += $account->transactions()->where('date', '<=', $asOfDate)->sum('debit');
            $creditSum += $account->transactions()->where('date', '<=', $asOfDate)->sum('credit');
            
            // Calculate balance based on normal balance
            $balance = 0;
            $debitBalance = 0;
            $creditBalance = 0;
            
            if ($account->normal_balance === 'debit') {
                $balance = $debitSum - $creditSum;
                if ($balance > 0) {
                    $debitBalance = $balance;
                } else {
                    $creditBalance = abs($balance);
                }
            } else {
                $balance = $creditSum - $debitSum;
                if ($balance > 0) {
                    $creditBalance = $balance;
                } else {
                    $debitBalance = abs($balance);
                }
            }
            
            // Skip accounts with zero balances if not including them
            if (!$includeZeroBalances && $debitBalance == 0 && $creditBalance == 0) {
                continue;
            }
            
            $trialBalance[] = [
                'account_code' => $account->code,
                'account_name' => $account->name,
                'account_type' => $account->accountType ? $account->accountType->name : '',
                'debit' => $debitBalance,
                'credit' => $creditBalance,
            ];
            
            $totalDebit += $debitBalance;
            $totalCredit += $creditBalance;
        }
        
        $data = [
            'trial_balance' => $trialBalance,
            'total_debit' => $totalDebit,
            'total_credit' => $totalCredit,
            'fiscal_year' => $fiscalYear,
            'fiscal_period' => $fiscalPeriod,
            'as_of_date' => $asOfDate,
        ];
        
        if ($validated['format'] === 'html') {
            return view('app.financial_reports.trial_balance', $data);
        } elseif ($validated['format'] === 'pdf') {
            $pdf = PDF::loadView('app.financial_reports.trial_balance_pdf', $data);
            return $pdf->download('trial_balance_' . date('Y-m-d') . '.pdf');
        } else {
            // CSV format
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="trial_balance_' . date('Y-m-d') . '.csv"',
            ];
            
            $callback = function() use ($trialBalance, $totalDebit, $totalCredit) {
                $file = fopen('php://output', 'w');
                fputcsv($file, ['Account Code', 'Account Name', 'Account Type', 'Debit', 'Credit']);
                
                foreach ($trialBalance as $row) {
                    fputcsv($file, [
                        $row['account_code'],
                        $row['account_name'],
                        $row['account_type'],
                        $row['debit'],
                        $row['credit'],
                    ]);
                }
                
                fputcsv($file, ['', '', 'Total', $totalDebit, $totalCredit]);
                fclose($file);
            };
            
            return response()->stream($callback, 200, $headers);
        }
    }
    
    /**
     * Generate the balance sheet report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function balanceSheet(Request $request)
    {
        $validated = $request->validate([
            'fiscal_year_id' => 'required|exists:fiscal_years,id',
            'fiscal_period_id' => 'nullable|exists:fiscal_periods,id',
            'as_of_date' => 'nullable|date',
            'format' => 'required|in:html,pdf,csv',
        ]);
        
        $fiscalYear = FiscalYear::findOrFail($validated['fiscal_year_id']);
        $fiscalPeriod = null;
        $asOfDate = null;
        
        if (!empty($validated['fiscal_period_id'])) {
            $fiscalPeriod = FiscalPeriod::findOrFail($validated['fiscal_period_id']);
            $asOfDate = $fiscalPeriod->end_date;
        } elseif (!empty($validated['as_of_date'])) {
            $asOfDate = $validated['as_of_date'];
        } else {
            $asOfDate = date('Y-m-d');
        }
        
        // Get asset accounts
        $assetAccounts = $this->getAccountsByClassification('asset', $asOfDate);
        $totalAssets = $this->sumAccountBalances($assetAccounts);
        
        // Get liability accounts
        $liabilityAccounts = $this->getAccountsByClassification('liability', $asOfDate);
        $totalLiabilities = $this->sumAccountBalances($liabilityAccounts);
        
        // Get equity accounts
        $equityAccounts = $this->getAccountsByClassification('equity', $asOfDate);
        $totalEquity = $this->sumAccountBalances($equityAccounts);
        
        $data = [
            'asset_accounts' => $assetAccounts,
            'liability_accounts' => $liabilityAccounts,
            'equity_accounts' => $equityAccounts,
            'total_assets' => $totalAssets,
            'total_liabilities' => $totalLiabilities,
            'total_equity' => $totalEquity,
            'fiscal_year' => $fiscalYear,
            'fiscal_period' => $fiscalPeriod,
            'as_of_date' => $asOfDate,
        ];
        
        if ($validated['format'] === 'html') {
            return view('app.financial_reports.balance_sheet', $data);
        } elseif ($validated['format'] === 'pdf') {
            $pdf = PDF::loadView('app.financial_reports.balance_sheet_pdf', $data);
            return $pdf->download('balance_sheet_' . date('Y-m-d') . '.pdf');
        } else {
            // CSV format
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="balance_sheet_' . date('Y-m-d') . '.csv"',
            ];
            
            $callback = function() use ($assetAccounts, $liabilityAccounts, $equityAccounts, $totalAssets, $totalLiabilities, $totalEquity) {
                $file = fopen('php://output', 'w');
                
                // Assets
                fputcsv($file, ['ASSETS']);
                fputcsv($file, ['Account Code', 'Account Name', 'Balance']);
                
                foreach ($assetAccounts as $account) {
                    fputcsv($file, [
                        $account['account_code'],
                        $account['account_name'],
                        $account['balance'],
                    ]);
                }
                
                fputcsv($file, ['', 'Total Assets', $totalAssets]);
                fputcsv($file, []);
                
                // Liabilities
                fputcsv($file, ['LIABILITIES']);
                fputcsv($file, ['Account Code', 'Account Name', 'Balance']);
                
                foreach ($liabilityAccounts as $account) {
                    fputcsv($file, [
                        $account['account_code'],
                        $account['account_name'],
                        $account['balance'],
                    ]);
                }
                
                fputcsv($file, ['', 'Total Liabilities', $totalLiabilities]);
                fputcsv($file, []);
                
                // Equity
                fputcsv($file, ['EQUITY']);
                fputcsv($file, ['Account Code', 'Account Name', 'Balance']);
                
                foreach ($equityAccounts as $account) {
                    fputcsv($file, [
                        $account['account_code'],
                        $account['account_name'],
                        $account['balance'],
                    ]);
                }
                
                fputcsv($file, ['', 'Total Equity', $totalEquity]);
                fputcsv($file, []);
                fputcsv($file, ['', 'Total Liabilities and Equity', $totalLiabilities + $totalEquity]);
                
                fclose($file);
            };
            
            return response()->stream($callback, 200, $headers);
        }
    }
    
    /**
     * Generate the income statement report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function incomeStatement(Request $request)
    {
        $validated = $request->validate([
            'fiscal_year_id' => 'required|exists:fiscal_years,id',
            'fiscal_period_id' => 'nullable|exists:fiscal_periods,id',
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date|after_or_equal:from_date',
            'format' => 'required|in:html,pdf,csv',
        ]);
        
        $fiscalYear = FiscalYear::findOrFail($validated['fiscal_year_id']);
        $fiscalPeriod = null;
        $fromDate = null;
        $toDate = null;
        
        if (!empty($validated['fiscal_period_id'])) {
            $fiscalPeriod = FiscalPeriod::findOrFail($validated['fiscal_period_id']);
            $fromDate = $fiscalPeriod->start_date;
            $toDate = $fiscalPeriod->end_date;
        } else {
            $fromDate = $validated['from_date'] ?? $fiscalYear->start_date;
            $toDate = $validated['to_date'] ?? $fiscalYear->end_date;
        }
        
        // Get revenue accounts
        $revenueAccounts = $this->getAccountsByClassification('revenue', $toDate, $fromDate);
        $totalRevenue = $this->sumAccountBalances($revenueAccounts);
        
        // Get expense accounts
        $expenseAccounts = $this->getAccountsByClassification('expense', $toDate, $fromDate);
        $totalExpenses = $this->sumAccountBalances($expenseAccounts);
        
        // Calculate net income
        $netIncome = $totalRevenue - $totalExpenses;
        
        $data = [
            'revenue_accounts' => $revenueAccounts,
            'expense_accounts' => $expenseAccounts,
            'total_revenue' => $totalRevenue,
            'total_expenses' => $totalExpenses,
            'net_income' => $netIncome,
            'fiscal_year' => $fiscalYear,
            'fiscal_period' => $fiscalPeriod,
            'from_date' => $fromDate,
            'to_date' => $toDate,
        ];
        
        if ($validated['format'] === 'html') {
            return view('app.financial_reports.income_statement', $data);
        } elseif ($validated['format'] === 'pdf') {
            $pdf = PDF::loadView('app.financial_reports.income_statement_pdf', $data);
            return $pdf->download('income_statement_' . date('Y-m-d') . '.pdf');
        } else {
            // CSV format
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="income_statement_' . date('Y-m-d') . '.csv"',
            ];
            
            $callback = function() use ($revenueAccounts, $expenseAccounts, $totalRevenue, $totalExpenses, $netIncome) {
                $file = fopen('php://output', 'w');
                
                // Revenue
                fputcsv($file, ['REVENUE']);
                fputcsv($file, ['Account Code', 'Account Name', 'Balance']);
                
                foreach ($revenueAccounts as $account) {
                    fputcsv($file, [
                        $account['account_code'],
                        $account['account_name'],
                        $account['balance'],
                    ]);
                }
                
                fputcsv($file, ['', 'Total Revenue', $totalRevenue]);
                fputcsv($file, []);
                
                // Expenses
                fputcsv($file, ['EXPENSES']);
                fputcsv($file, ['Account Code', 'Account Name', 'Balance']);
                
                foreach ($expenseAccounts as $account) {
                    fputcsv($file, [
                        $account['account_code'],
                        $account['account_name'],
                        $account['balance'],
                    ]);
                }
                
                fputcsv($file, ['', 'Total Expenses', $totalExpenses]);
                fputcsv($file, []);
                fputcsv($file, ['', 'Net Income', $netIncome]);
                
                fclose($file);
            };
            
            return response()->stream($callback, 200, $headers);
        }
    }
    
    /**
     * Get accounts by classification with their balances.
     *
     * @param  string  $classification
     * @param  string  $asOfDate
     * @param  string  $fromDate
     * @return array
     */
    private function getAccountsByClassification($classification, $asOfDate, $fromDate = null)
    {
        $accountTypes = AccountType::where('classification', $classification)
            ->pluck('id');
            
        $accounts = Account::with('accountType')
            ->whereIn('account_type_id', $accountTypes)
            ->where('is_active', true)
            ->orderBy('code')
            ->get();
            
        $result = [];
        
        foreach ($accounts as $account) {
            // Get journal entry lines for this account
            $query = JournalEntryLine::join('journal_entries', 'journal_entry_lines.journal_entry_id', '=', 'journal_entries.id')
                ->where('journal_entry_lines.account_id', $account->id)
                ->where('journal_entries.status', 'posted')
                ->where('journal_entries.entry_date', '<=', $asOfDate);
                
            if ($fromDate) {
                $query->where('journal_entries.entry_date', '>=', $fromDate);
            }
            
            $debitSum = $query->sum('journal_entry_lines.debit');
            $creditSum = $query->sum('journal_entry_lines.credit');
            
            // Add transactions from the old system
            $transactionQuery = $account->transactions()->where('date', '<=', $asOfDate);
            if ($fromDate) {
                $transactionQuery->where('date', '>=', $fromDate);
            }
            
            $debitSum += $transactionQuery->sum('debit');
            $creditSum += $transactionQuery->sum('credit');
            
            // Calculate balance based on normal balance
            $balance = 0;
            
            if ($account->normal_balance === 'debit') {
                $balance = $debitSum - $creditSum;
            } else {
                $balance = $creditSum - $debitSum;
            }
            
            // Skip accounts with zero balances if requested
            // Note: For balance sheet and income statement, we might want to show zero balances
            // depending on the report configuration, so this should be a parameter
            if ($balance == 0 && !($classification == 'asset' || $classification == 'liability' || $classification == 'equity')) {
                continue;
            }
            
            $result[] = [
                'account_id' => $account->id,
                'account_code' => $account->code,
                'account_name' => $account->name,
                'account_type' => $account->accountType ? $account->accountType->name : '',
                'balance' => $balance,
            ];
        }
        
        return $result;
    }
    
    /**
     * Sum the balances of accounts.
     *
     * @param  array  $accounts
     * @return float
     */
    private function sumAccountBalances($accounts)
    {
        $total = 0;
        
        foreach ($accounts as $account) {
            $total += $account['balance'];
        }
        
        return $total;
    }
}
