<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Builder;

use App\Traits\CreatedUpdatedTrait;

class Deduction extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'code',
        'formula',
        'apply_mode',
        'apply_at',
        'description',
        'created_by',
        'updated_by',
        'status_id',
    ];

    protected $searchableFields = ['*'];

    protected $table = 'deduction_contributions';

    public function staffCategory()
    {
        return $this->hasMany(StaffCategory::class, 'category_id');
    }

    public function staffs()
    {
        return $this->hasMany(Staff::class);
    }

    public function staff()
    {
        return $this->hasMany(GadgetOwner::class);
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function employeeDeductionContributions()
    {
        return $this->hasMany(EmployeeDeductionContribution::class, 'deduction_contribution_id');
    }

    public static function boot()
    {
        parent::boot();

        static::addGlobalScope(function (Builder $builder) {
            $builder->where('apply_mode', "DEDUCTION"); //->orWhereNull($field);
        });            

        self::creating(function($model){
            $model->apply_mode = "DEDUCTION"; //->orWhereNull($field);
        });

        self::created(function($model){

        });

        self::updating(function($model){
            $model->apply_mode = "DEDUCTION"; //->orWhereNull($field);
        });

        self::updated(function($model){
        });

        self::deleting(function($model){
            // ... code here
        });

        self::deleted(function($model){
            // ... code here
        });
    }




    
}
