@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
     <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">Status</x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Status::class)
            <a href="statuses/create" class="btn btn-info btn-sm">
                @lang('crud.common.create') Status
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->
    <div class="card card-table">
        <div class="table-responsive">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            @lang('crud.statuses.inputs.name')
                        </th>
                        <th class="text-left">
                            @lang('crud.statuses.inputs.class')
                        </th>
                        <th class="text-left">
                            @lang('crud.statuses.inputs.color')
                        </th>
                        <th class="text-center">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($statuses as $status)
                    <tr>
                        <td>{{ $status->name ?? '-' }}</td>
                        <td>{{ $status->class ?? '-' }}</td>
                        <td>{{ $status->color ?? '-' }}</td>
                        <td class="text-center" style="width: 134px;">
                            <div
                                role="group"
                                aria-label="Row Actions"
                                class="btn-group"
                            >
                                @can('update', $status)
                                <a
                                    href="{{ route('statuses.edit', $status) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        <!-- <i class="icon ion-md-create"></i> --> edit
                                    </button>
                                </a>
                                @endcan @can('view', $status)
                                <a
                                    href="{{ route('statuses.show', $status) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        <!-- <i class="icon ion-md-eye"></i> --> view
                                    </button>
                                </a>
                                @endcan @can('delete', $status)
                                <form
                                    action="{{ route('statuses.destroy', $status) }}"
                                    method="POST"
                                    onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                >
                                    @csrf @method('DELETE')
                                    <button
                                        type="submit"
                                        class="btn btn-light text-danger m-1"
                                    >
                                        <!-- <i class="icon ion-md-trash"></i> --> del
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="4">
                            @lang('crud.common.no_items_found')
                        </td>
                    </tr>
                    @endforelse
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="4">{!! $statuses->render() !!}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    
</div>
@endsection
