<?php

namespace App\TenantFinders;

use Illuminate\Http\Request;
use Spatie\Multitenancy\Models\Tenant;
use Spatie\Multitenancy\TenantFinder\TenantFinder;

class PathTenantFinder implements TenantFinder
{
    public function findForRequest(Request $request): ?Tenant
    {
        $segments = $request->segments();
        $tenantSlug = $segments[0] ?? null;

        return Tenant::where('slug', $tenantSlug)->first();
    }
}
