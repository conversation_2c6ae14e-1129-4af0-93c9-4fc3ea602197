<?php

namespace Database\Factories;

use App\Models\Stock;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class StockFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Stock::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'quantity' => $this->faker->randomNumber,
            'balance' => $this->faker->randomNumber(0),
            'description' => $this->faker->sentence(15),
            'product_id' => \App\Models\Product::factory(),
            'created_by' => \App\Models\User::factory(),
            'updated_by' => \App\Models\User::factory(),
            'status_id' => \App\Models\Status::factory(),
            'supplier_id' => \App\Models\Supplier::factory(),
            'approved_by' => \App\Models\User::factory(),
            'stock_id' => function () {
                return \App\Models\Stock::factory()->create([
                    'stock_id' => null,
                ])->id;
            },
        ];
    }
}
