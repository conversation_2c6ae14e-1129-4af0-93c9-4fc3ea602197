<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Payroll Liabilities Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
        }
        .container {
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .header p {
            margin: 5px 0;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table th, table td {
            border: 1px solid #ddd;
            padding: 6px;
            font-size: 10px;
        }
        table th {
            background-color: #f2f2f2;
            text-align: left;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .summary {
            margin-top: 20px;
        }
        .summary h2 {
            font-size: 16px;
            margin-bottom: 10px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Payroll Liabilities Report</h1>
            <p>Period: {{ $monthName }} {{ $year }}</p>
            <p>Generated on: {{ date('d M Y H:i:s') }}</p>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Liability Type</th>
                    <th>Amount</th>
                    <th>Due Date</th>
                    <th>Payee</th>
                </tr>
            </thead>
            <tbody>
                @forelse($liabilities as $name => $amount)
                <tr>
                    <td>{{ $name }}</td>
                    <td class="text-right">{{ number_format($amount, 2) }}</td>
                    <td>{{ date('t/m/Y', strtotime($year . '-' . $month . '-01')) }}</td>
                    <td>
                        @if($name == 'PAYE Tax')
                            Tax Authority
                        @else
                            Various
                        @endif
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="4" class="text-center">No liabilities found for this period.</td>
                </tr>
                @endforelse
            </tbody>
            <tfoot>
                <tr>
                    <th>Total</th>
                    <th class="text-right">{{ number_format($totalLiabilities, 2) }}</th>
                    <th colspan="2"></th>
                </tr>
            </tfoot>
        </table>

        <div class="summary">
            <h2>Payment Instructions</h2>
            <p>The liabilities listed above should be paid by the end of the month to avoid penalties. PAYE tax should be remitted to the tax authority, while other deductions should be paid to the respective benefit providers or institutions.</p>
        </div>

        <div class="summary">
            <h2>Journal Entry</h2>
            <table>
                <thead>
                    <tr>
                        <th>Account</th>
                        <th>Debit</th>
                        <th>Credit</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Payroll Liabilities</td>
                        <td class="text-right">{{ number_format($totalLiabilities, 2) }}</td>
                        <td class="text-right">-</td>
                    </tr>
                    <tr>
                        <td>Cash/Bank</td>
                        <td class="text-right">-</td>
                        <td class="text-right">{{ number_format($totalLiabilities, 2) }}</td>
                    </tr>
                    <tr>
                        <th>Total</th>
                        <th class="text-right">{{ number_format($totalLiabilities, 2) }}</th>
                        <th class="text-right">{{ number_format($totalLiabilities, 2) }}</th>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="footer">
            <p>This is a computer-generated document. No signature is required.</p>
        </div>
    </div>
</body>
</html>
