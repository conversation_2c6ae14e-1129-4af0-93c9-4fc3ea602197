<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\User;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class EmployeeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Employee::class);

        $search = $request->get('search', '');
        $grade = $request->get('grade', '');

        $employees = Employee::search($search);
        
        if ($grade) {
            $employees->where('grade', $grade);
        }
        
        $employees = $employees->latest()
            ->paginate()
            ->withQueryString();

        $grades = Employee::distinct()->pluck('grade')->filter()->sort();

        return view('app.employees.index', compact(
            'employees', 
            'search',
            'grades',
            'grade'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', Employee::class);

        $users = User::whereDoesntHave('employee')
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.employees.create', compact('users'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', Employee::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|tenant_unique:employees,email',
            'phone' => 'nullable|string|max:255',
            'address' => 'nullable|string',
             'gender' => 'nullable|string',
              'date_of_birth' => 'nullable|date',
               'marital_status' => 'nullable|string',
            'emergency_phone' => 'nullable|string|max:255',
            'emergency_address' => 'nullable|string',
            'basic_pay' => 'required|numeric|min:0',
            'grade' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'medical_description' => 'nullable|string',
            'user_id' => 'nullable|tenant_exists:users,id|tenant_unique:employees,user_id',
        ]);

        $validated['created_by'] = auth()->id();

        $employee = Employee::create($validated);

        return redirect()
            ->route('employees.show', $employee)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Employee  $employee
     * @return \Illuminate\Http\Response
     */
    public function show(Employee $employee)
    {
        $this->authorize('view', $employee);

        // Get recent payroll records
        $recentPayrolls = $employee->monthlyPayrolls()
            ->latest('date_from')
            ->take(5)
            ->get();

        // Get active deductions and contributions
        $activeDeductions = $employee->deductions()
            ->whereNull('end_date')
            ->orWhere('end_date', '>=', now())
            ->with('deductionContribution')
            ->get();

        $activeContributions = $employee->contributions()
            ->whereNull('end_date')
            ->orWhere('end_date', '>=', now())
            ->with('deductionContribution')
            ->get();

        return view('app.employees.show', compact(
            'employee', 
            'recentPayrolls', 
            'activeDeductions', 
            'activeContributions'
        ));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Employee  $employee
     * @return \Illuminate\Http\Response
     */
    public function edit(Employee $employee)
    {
        $this->authorize('update', $employee);

        $users = User::whereDoesntHave('employee')
            ->orWhere('id', $employee->user_id)
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.employees.edit', compact('employee', 'users'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Employee  $employee
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Employee $employee)
    {
        $this->authorize('update', $employee);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|tenant_unique:employees,email,' . $employee->id,
            'phone' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'emergency_phone' => 'nullable|string|max:255',
            'gender' => 'nullable|string',
              'date_of_birth' => 'nullable|date',
               'marital_status' => 'nullable|string',
            'emergency_address' => 'nullable|string',
            'basic_pay' => 'required|numeric|min:0',
            'grade' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'medical_description' => 'nullable|string',
            'user_id' => 'nullable|tenant_exists:users,id|tenant_unique:employees,user_id,' . $employee->id,
        ]);

        $validated['updated_by'] = auth()->id();

        $employee->update($validated);

        return redirect()
            ->route('employees.show', $employee)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Employee  $employee
     * @return \Illuminate\Http\Response
     */
    public function destroy(Employee $employee)
    {
        $this->authorize('delete', $employee);

        // Check if employee has payroll records
        if ($employee->monthlyPayrolls()->count() > 0) {
            return redirect()
                ->back()
                ->withError('Cannot delete employee with existing payroll records.');
        }

        $employee->delete();

        return redirect()
            ->route('employees.index')
            ->withSuccess(__('crud.common.removed'));
    }
}
