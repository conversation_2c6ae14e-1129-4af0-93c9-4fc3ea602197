<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Loan Document - {{ $loan->loan_reference }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            margin: 0;
            padding: 20px;
            color: #333;
            background: white;
        }
        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            background: white;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #333;
            padding-bottom: 20px;
        }
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 32px;
            color: #333;
            font-weight: bold;
        }
        .header .company-info {
            margin: 10px 0;
            font-size: 14px;
            color: #666;
        }
        .document-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin: 20px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
        }

        .loan-info {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 25px;
            margin: 30px 0;
        }
        .loan-info h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 18px;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }

        .info-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .info-row {
            display: table-row;
        }
        .info-label, .info-value {
            display: table-cell;
            padding: 8px 15px 8px 0;
            vertical-align: top;
            border-bottom: 1px solid #f0f0f0;
        }
        .info-label {
            font-weight: bold;
            width: 200px;
            color: #555;
        }
        .info-value {
            color: #333;
        }

        .status-badge {
            display: inline-block;
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-approved {
            background-color: #e8f5e8;
            color: #2e7d32;
        }
        .status-completed {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        .status-active {
            background-color: #fff3e0;
            color: #f57c00;
        }

        .terms-section {
            margin: 30px 0;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }
        .terms-header {
            background: #333;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            font-size: 16px;
        }
        .terms-content {
            padding: 20px;
            background: #fafafa;
        }

        .payment-schedule {
            margin: 30px 0;
        }
        .payment-schedule table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .payment-schedule th,
        .payment-schedule td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        .payment-schedule th {
            background-color: #f5f5f5;
            font-weight: bold;
            color: #555;
        }
        .payment-schedule tr:last-child td {
            border-bottom: none;
        }
        .amount {
            text-align: right;
            font-weight: 600;
            color: #2e7d32;
        }

        .signatures {
            margin: 50px 0 30px 0;
            display: table;
            width: 100%;
        }
        .signature-block {
            display: table-cell;
            width: 45%;
            text-align: center;
            vertical-align: bottom;
        }
        .signature-line {
            border-top: 2px solid #333;
            margin: 40px 0 10px 0;
            padding-top: 10px;
        }

        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }

        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 80px;
            color: rgba(0,0,0,0.05);
            z-index: -1;
            font-weight: bold;
        }

        @media print {
            body { margin: 0; padding: 15px; }
            .container { max-width: none; }
        }
    </style>
</head>
<body>
    <div class="watermark">{{ strtoupper($loan->status) }}</div>
    
    <div class="container">
        <div class="header">
            <h1>{{ config('app.name', 'Company Name') }}</h1>
            <div class="company-info">
                {{ auth()->user()->branch->address ?? 'Company Address' }}<br>
                Phone: {{ auth()->user()->branch->phone ?? '+****************' }}<br>
                Email: {{ auth()->user()->branch->email ?? '<EMAIL>' }}
            </div>
        </div>

        <div class="document-title">
            EMPLOYEE LOAN AGREEMENT
        </div>

        <div class="loan-info">
            <h3>📋 Loan Information</h3>
            <div class="info-grid">
                <div class="info-row">
                    <div class="info-label">Loan Reference:</div>
                    <div class="info-value"><strong>{{ $loan->loan_reference }}</strong></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Employee Name:</div>
                    <div class="info-value">{{ $loan->employee->name }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Employee ID:</div>
                    <div class="info-value">{{ $loan->employee->id }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Position:</div>
                    <div class="info-value">{{ $loan->employee->position ?? 'N/A' }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Department:</div>
                    <div class="info-value">{{ $loan->employee->department ?? 'N/A' }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Loan Amount:</div>
                    <div class="info-value amount">K{{ number_format($loan->loan_amount, 2) }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Monthly Installment:</div>
                    <div class="info-value amount">K{{ number_format($loan->installment_amount, 2) }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Total Installments:</div>
                    <div class="info-value">{{ $loan->total_installments }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Loan Date:</div>
                    <div class="info-value">{{ $loan->loan_date->format('F d, Y') }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">First Deduction Date:</div>
                    <div class="info-value">{{ $loan->first_deduction_date->format('F d, Y') }}</div>
                </div>
                @if($loan->expected_completion_date)
                <div class="info-row">
                    <div class="info-label">Expected Completion:</div>
                    <div class="info-value">{{ $loan->expected_completion_date->format('F d, Y') }}</div>
                </div>
                @endif
                <div class="info-row">
                    <div class="info-label">Current Status:</div>
                    <div class="info-value">
                        <span class="status-badge status-{{ $loan->status }}">
                            {{ strtoupper($loan->status) }}
                        </span>
                    </div>
                </div>
                @if($loan->interest_rate > 0)
                <div class="info-row">
                    <div class="info-label">Interest Rate:</div>
                    <div class="info-value">{{ $loan->interest_rate }}% ({{ ucfirst($loan->interest_type) }})</div>
                </div>
                @endif
            </div>
        </div>

        @if($loan->purpose)
        <div class="terms-section">
            <div class="terms-header">💼 Loan Purpose</div>
            <div class="terms-content">
                <p>{{ $loan->purpose }}</p>
            </div>
        </div>
        @endif

        <div class="payment-schedule">
            <h3>💰 Payment Summary</h3>
            <table>
                <tr>
                    <td><strong>Loan Amount:</strong></td>
                    <td class="amount">K{{ number_format($loan->loan_amount, 2) }}</td>
                </tr>
                <tr>
                    <td><strong>Monthly Installment:</strong></td>
                    <td class="amount">K{{ number_format($loan->installment_amount, 2) }}</td>
                </tr>
                <tr>
                    <td><strong>Total Paid to Date:</strong></td>
                    <td class="amount">K{{ number_format($loan->total_paid, 2) }}</td>
                </tr>
                <tr>
                    <td><strong>Remaining Balance:</strong></td>
                    <td class="amount">K{{ number_format($loan->remaining_balance, 2) }}</td>
                </tr>
                <tr>
                    <td><strong>Installments Paid:</strong></td>
                    <td class="amount">{{ $loan->installments_paid }} of {{ $loan->total_installments }}</td>
                </tr>
                <tr>
                    <td><strong>Progress:</strong></td>
                    <td class="amount">{{ $loan->progress_percentage }}%</td>
                </tr>
            </table>
        </div>

        @if($loan->terms_conditions)
        <div class="terms-section">
            <div class="terms-header">📜 Terms and Conditions</div>
            <div class="terms-content">
                <p>{{ $loan->terms_conditions }}</p>
            </div>
        </div>
        @endif

        <div class="terms-section">
            <div class="terms-header">⚖️ Standard Loan Terms</div>
            <div class="terms-content">
                <ul style="margin: 0; padding-left: 20px;">
                    <li>Monthly deductions will be automatically processed from your salary</li>
                    <li>Deductions will continue until the loan is fully repaid</li>
                    <li>Early repayment is allowed without penalty</li>
                    <li>In case of employment termination, the remaining balance becomes immediately due</li>
                    <li>This loan agreement is subject to company policies and local employment laws</li>
                    <li>Any changes to the loan terms must be agreed upon in writing</li>
                </ul>
            </div>
        </div>

        @if($loan->approved_at && $loan->approvedBy)
        <div class="loan-info">
            <h3>✅ Approval Information</h3>
            <div class="info-grid">
                <div class="info-row">
                    <div class="info-label">Approved By:</div>
                    <div class="info-value">{{ $loan->approvedBy->name }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Approval Date:</div>
                    <div class="info-value">{{ $loan->approved_at->format('F d, Y \a\t H:i:s') }}</div>
                </div>
                @if($loan->approval_notes)
                <div class="info-row">
                    <div class="info-label">Approval Notes:</div>
                    <div class="info-value">{{ $loan->approval_notes }}</div>
                </div>
                @endif
            </div>
        </div>
        @endif

        <div class="signatures">
            <div class="signature-block">
                <div class="signature-line">
                    <strong>Employee Signature</strong><br>
                    {{ $loan->employee->name }}<br>
                    Date: ________________
                </div>
            </div>
            <div style="display: table-cell; width: 10%;"></div>
            <div class="signature-block">
                <div class="signature-line">
                    <strong>HR Representative</strong><br>
                    {{ $loan->approvedBy->name ?? 'HR Department' }}<br>
                    Date: {{ $loan->approved_at ? $loan->approved_at->format('M d, Y') : '________________' }}
                </div>
            </div>
        </div>

        <div class="footer">
            <p><strong>This is a computer-generated document.</strong></p>
            <p>Generated on: {{ now()->format('F d, Y \a\t H:i:s') }}</p>
            <p>Document ID: {{ $loan->loan_reference }}-{{ now()->format('Ymd-His') }}</p>
            <p>© {{ date('Y') }} {{ config('app.name', 'Company Name') }}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
