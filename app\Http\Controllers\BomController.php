<?php

namespace App\Http\Controllers;

use App\Models\Bom;
use App\Models\BomItem;
use App\Models\Product;
use App\Models\Unit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BomController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Bom::class);

        $search = $request->get('search', '');
        $productId = $request->get('product_id', '');
        $status = $request->get('status', '');

        $boms = Bom::search($search);
        
        if ($productId) {
            $boms->where('product_id', $productId);
        }
        
        if ($status) {
            $boms->where('status', $status);
        }
        
        $boms = $boms->latest()
            ->paginate()
            ->withQueryString();

        $products = Product::orderBy('name')
            ->pluck('name', 'id');

        return view('app.boms.index', compact(
            'boms', 
            'search', 
            'products',
            'productId',
            'status'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', Bom::class);

        $products = Product::orderBy('name')
            ->pluck('name', 'id');

        return view('app.boms.create', compact('products'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', Bom::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|numeric|min:0.01',
            'unit_of_measure' => 'required|string|max:255',
            'status' => 'required|in:draft,active,inactive',
            'is_default' => 'boolean',
            'version' => 'nullable|string|max:255',
            'effective_date' => 'nullable|date',
            'expiry_date' => 'nullable|date|after_or_equal:effective_date',
        ]);

        // Generate BOM number
        $latestBom = Bom::latest()->first();
        $bomNumber = 'BOM-' . date('Y') . '-' . sprintf('%06d', $latestBom ? ($latestBom->id + 1) : 1);
        $validated['bom_number'] = $bomNumber;
        
        // Set total cost to 0 initially
        $validated['total_cost'] = 0;
        
        // If this is set as default, update all other BOMs for this product
        if ($validated['is_default'] ?? false) {
            Bom::where('product_id', $validated['product_id'])
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }

        $bom = Bom::create($validated);

        return redirect()
            ->route('boms.edit', $bom)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Bom  $bom
     * @return \Illuminate\Http\Response
     */
    public function show(Bom $bom)
    {
        $this->authorize('view', $bom);

        $bomItems = $bom->bomItems()
            ->with('product')
            ->get();

        return view('app.boms.show', compact('bom', 'bomItems'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Bom  $bom
     * @return \Illuminate\Http\Response
     */
    public function edit(Bom $bom)
    {
        $this->authorize('update', $bom);

        $products = Product::orderBy('name')
            ->pluck('name', 'id');

        return view('app.boms.edit', compact('bom', 'products'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Bom  $bom
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Bom $bom)
    {
        $this->authorize('update', $bom);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'quantity' => 'required|numeric|min:0.01',
            'unit_of_measure' => 'required|string|max:255',
            'status' => 'required|in:draft,active,inactive',
            'is_default' => 'boolean',
            'version' => 'nullable|string|max:255',
            'effective_date' => 'nullable|date',
            'expiry_date' => 'nullable|date|after_or_equal:effective_date',
        ]);
        
        // If this is set as default, update all other BOMs for this product
        if (($validated['is_default'] ?? false) && !$bom->is_default) {
            Bom::where('product_id', $bom->product_id)
                ->where('is_default', true)
                ->where('id', '!=', $bom->id)
                ->update(['is_default' => false]);
        }
        
        // If status is changing to active, set approved_by and approved_at
        if ($validated['status'] === 'active' && $bom->status !== 'active') {
            $validated['approved_by'] = auth()->id();
            $validated['approved_at'] = now();
        }

        $bom->update($validated);

        return redirect()
            ->route('boms.edit', $bom)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Bom  $bom
     * @return \Illuminate\Http\Response
     */
    public function destroy(Bom $bom)
    {
        $this->authorize('delete', $bom);
        
        if ($bom->productionOrders()->count() > 0) {
            return redirect()
                ->route('boms.index')
                ->withError('BOM is used in production orders and cannot be deleted.');
        }

        DB::beginTransaction();
        
        try {
            // Delete BOM items
            $bom->bomItems()->delete();
            
            // Delete BOM
            $bom->delete();
            
            DB::commit();
            
            return redirect()
                ->route('boms.index')
                ->withSuccess(__('crud.common.removed'));
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
    
    /**
     * Show the form for editing BOM items.
     *
     * @param  \App\Models\Bom  $bom
     * @return \Illuminate\Http\Response
     */
    public function editItems(Bom $bom)
    {
        $this->authorize('update', $bom);

        $products = Product::orderBy('name')
            ->pluck('name', 'id');
            
        $units = Unit::orderBy('name')
            ->pluck('name', 'id');
            
        $bomItems = $bom->bomItems()
            ->with('product')
            ->get();

        return view('app.boms.edit_items', compact(
            'bom', 
            'products',
            'units',
            'bomItems'
        ));
    }
    
    /**
     * Update BOM items.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Bom  $bom
     * @return \Illuminate\Http\Response
     */
    public function updateItems(Request $request, Bom $bom)
    {
        $this->authorize('update', $bom);

        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'nullable|exists:bom_items,id',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.unit_id' => 'nullable|exists:units,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_of_measure' => 'required|string|max:255',
            'items.*.cost_per_unit' => 'required|numeric|min:0',
            'items.*.description' => 'nullable|string',
            'items.*.item_type' => 'required|in:material,labor,overhead',
            'items.*.is_scrap' => 'boolean',
            'items.*.scrap_percentage' => 'nullable|numeric|min:0|max:100',
        ]);

        DB::beginTransaction();
        
        try {
            $totalCost = 0;
            $existingItemIds = $bom->bomItems()->pluck('id')->toArray();
            $updatedItemIds = [];
            
            foreach ($validated['items'] as $item) {
                $itemId = $item['id'] ?? null;
                
                // Calculate total cost
                $totalItemCost = $item['quantity'] * $item['cost_per_unit'];
                $totalCost += $totalItemCost;
                
                if ($itemId) {
                    // Update existing item
                    $bomItem = BomItem::find($itemId);
                    $bomItem->update([
                        'product_id' => $item['product_id'],
                        'unit_id' => $item['unit_id'],
                        'quantity' => $item['quantity'],
                        'unit_of_measure' => $item['unit_of_measure'],
                        'cost_per_unit' => $item['cost_per_unit'],
                        'total_cost' => $totalItemCost,
                        'description' => $item['description'],
                        'item_type' => $item['item_type'],
                        'is_scrap' => $item['is_scrap'] ?? false,
                        'scrap_percentage' => $item['scrap_percentage'] ?? 0,
                    ]);
                    
                    $updatedItemIds[] = $itemId;
                } else {
                    // Create new item
                    $bomItem = BomItem::create([
                        'bom_id' => $bom->id,
                        'product_id' => $item['product_id'],
                        'unit_id' => $item['unit_id'],
                        'quantity' => $item['quantity'],
                        'unit_of_measure' => $item['unit_of_measure'],
                        'cost_per_unit' => $item['cost_per_unit'],
                        'total_cost' => $totalItemCost,
                        'description' => $item['description'],
                        'item_type' => $item['item_type'],
                        'is_scrap' => $item['is_scrap'] ?? false,
                        'scrap_percentage' => $item['scrap_percentage'] ?? 0,
                    ]);
                    
                    $updatedItemIds[] = $bomItem->id;
                }
            }
            
            // Delete removed items
            $itemsToDelete = array_diff($existingItemIds, $updatedItemIds);
            BomItem::whereIn('id', $itemsToDelete)->delete();
            
            // Update BOM total cost
            $bom->update(['total_cost' => $totalCost]);
            
            DB::commit();
            
            return redirect()
                ->route('boms.show', $bom)
                ->withSuccess('BOM items have been updated successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
    
    /**
     * Copy BOM to create a new version.
     *
     * @param  \App\Models\Bom  $bom
     * @return \Illuminate\Http\Response
     */
    public function copy(Bom $bom)
    {
        $this->authorize('create', Bom::class);

        DB::beginTransaction();
        
        try {
            // Generate BOM number
            $latestBom = Bom::latest()->first();
            $bomNumber = 'BOM-' . date('Y') . '-' . sprintf('%06d', $latestBom ? ($latestBom->id + 1) : 1);
            
            // Create new BOM
            $newVersion = $bom->version ? $this->incrementVersion($bom->version) : '1.1';
            $newBom = Bom::create([
                'name' => $bom->name . ' (Copy)',
                'bom_number' => $bomNumber,
                'description' => $bom->description,
                'product_id' => $bom->product_id,
                'quantity' => $bom->quantity,
                'unit_of_measure' => $bom->unit_of_measure,
                'total_cost' => $bom->total_cost,
                'status' => 'draft',
                'is_default' => false,
                'version' => $newVersion,
                'effective_date' => null,
                'expiry_date' => null,
            ]);
            
            // Copy BOM items
            foreach ($bom->bomItems as $item) {
                BomItem::create([
                    'bom_id' => $newBom->id,
                    'product_id' => $item->product_id,
                    'unit_id' => $item->unit_id,
                    'quantity' => $item->quantity,
                    'unit_of_measure' => $item->unit_of_measure,
                    'cost_per_unit' => $item->cost_per_unit,
                    'total_cost' => $item->total_cost,
                    'description' => $item->description,
                    'item_type' => $item->item_type,
                    'is_scrap' => $item->is_scrap,
                    'scrap_percentage' => $item->scrap_percentage,
                ]);
            }
            
            DB::commit();
            
            return redirect()
                ->route('boms.edit', $newBom)
                ->withSuccess('BOM has been copied successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
    
    /**
     * Increment version number.
     *
     * @param  string  $version
     * @return string
     */
    private function incrementVersion($version)
    {
        $parts = explode('.', $version);
        
        if (count($parts) === 1) {
            return $version . '.1';
        }
        
        $parts[count($parts) - 1]++;
        
        return implode('.', $parts);
    }
}
