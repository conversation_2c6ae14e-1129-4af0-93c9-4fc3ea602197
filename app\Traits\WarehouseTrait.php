<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Facades\App\Cache\Users;

trait WarehouseTrait
{


    public static function bootWarehouseTrait()
    {
        

        if (auth()->check() ) {

            static::addGlobalScope(function (Builder $builder) {
            	if( ! request()->is('api/stock*', 'stock*') && ! auth()->user()->isSuperAdmin() ){
                    $warehouse_id = request()->warehouse_id ??  auth()->user()->warehouse_id;
                    $builder->where("warehouse_id", $warehouse_id );
                }
            });            

            static::creating(function ($model) {
                if( ! request()->is('api/stocks*', 'stocks*') ){
                    $warehouse_id = request()->new_warehouse_id ?? request()->warehouse_id ??  auth()->user()->warehouse_id;
                    $model->warehouse_id = $warehouse_id;
                }
		    });

      //       static::updating(function ($model) {
		    //     $model->updated_by = auth()->id();
		    // });

      //       static::deleting(function ($model) {
      //           $model->updated_by = auth()->id();
      //       });
        }
    }

}
