<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF <PERSON>ken -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ env("APP_NAME")}}</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/notyf@3/notyf.min.css">

    <!-- Icons -->
    <link href="https://unpkg.com/ionicons@4.5.10-0/dist/css/ionicons.min.css" rel="stylesheet">

    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <!-- Scripts -->
    <script src="{{ asset('js/app.js') }}"></script>
    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ asset('img/logo.webp') }}">

    <link href="{{ asset('vue-img/print.min.css') }}" rel="stylesheet">
    <script src="{{ asset('vue-img/print.min.js') }}"></script>
    <link href="{{ asset('vue-img/vue-2-img.css') }}" rel="stylesheet">
    <script src="{{ asset('vue-img/vue-2-img.js') }}"></script>
    <script src="{{ asset('vue-img/html2canvas.min.js') }}"></script>
    <script src="https://superal.github.io/canvas2image/canvas2image.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.min.js"></script>

    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&amp;display=swap" rel="stylesheet">

    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="{{ asset('assets/css/vendor.min.css') }}">
    <link rel="stylesheet" href="{{ asset('css/custom.css') }}">
    <link rel="stylesheet" href="{{ asset('css/custom-ui.css') }}">

    <!-- CSS Front Template -->
    <link rel="stylesheet" href="{{ asset('assets/css/theme.minc619.css?v=1.0') }}">

    <link rel="preload" href="{{ asset('assets/css/theme.min.css') }}" data-hs-appearance="default" as="style">
    <link rel="preload" href="{{ asset('assets/css/theme-dark.min.css') }}" data-hs-appearance="dark" as="style">

    <script src="https://code.jquery.com/jquery-3.6.1.min.js" integrity="sha256-o88AwQnZB+VDvE9tvIXrMQaPlFFSUTR+nldQm1LuPXQ=" crossorigin="anonymous"></script>

    <script type="text/javascript" src="{{ asset('js/vue.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/axios.min.js') }}"></script>

    <link href="https://cdn.datatables.net/1.13.1/css/dataTables.bootstrap4.min.css" rel="stylesheet">

    <style data-hs-appearance-onload-styles>
      * {
        transition: unset !important;
      }

      .form-control {
        height: 40px !important;
      }

      .table-responsive {
        min-height: 200px !important;
      }

      .table-responsive.table-dropdown-space {
        min-height: 300px !important;
      }

      /* Fullscreen specific styles */
      body {
        margin: 0;
        padding: 0;
        background-color: #f8f9fa;
      }

      .fullscreen-container {
        min-height: 100vh;
        padding: 20px;
      }

      .fullscreen-header {
        background: white;
        border-radius: 8px;
        padding: 15px 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        display: flex;
        justify-content: between;
        align-items: center;
      }

      .fullscreen-content {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      @page {
          size: auto;
          margin: 0;
      }

      @media print {
        @page {
          background: red;
          height: 200px !important;
          size: landscape;
        }
      }

      .dt-button-collection {
        display: block;
        height: 450px !important;
        overflow-y: scroll !important;
      }

      .p-select .js-select {
        font-size: 25px;
      }
    </style>

    <script type="module">
        // import hotwiredTurbo from 'https://cdn.skypack.dev/@hotwired/turbo';
    </script>
     @stack('styles')

    <script type="text/javascript" src="{{ asset('assets/js/data.js') }}"></script>
    @livewireStyles

  </head>
  <body>
    <script src="{{ asset('assets/js/hs.theme-appearance.js') }}"></script>

    @include('partial.header')

    <!-- Fullscreen Container -->
    <main id="content" role="main" class="main">
      <div class="container mb-4">
        @include('layouts.error')
        @yield('content')
      </div>
    </main>

    @livewireScripts

    <!-- JS Implementing Plugins -->
    <script src="{{ asset('assets/js/vendor.min.js') }}"></script>

    <!-- JS Front -->
    <script src="{{ asset('assets/js/theme.min.js') }}"></script>
    <script src="{{ asset('assets/js/hs.theme-appearance-charts.js') }}"></script>

    <script src="https://cdn.jsdelivr.net/gh/livewire/turbolinks@v0.1.x/dist/livewire-turbolinks.js" data-turbolinks-eval="false" data-turbo-eval="false"></script>

    <script type="text/javascript" src="{{ asset('js/scripts.js') }}"></script>

    <script src="https://cdn.jsdelivr.net/npm/notyf@3/notyf.min.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    @if (session()->has('success'))
    <script>
        var notyf = new Notyf({dismissible: true})
        notyf.success('{{ session('success') }}')
    </script>
    @endif

    @if (session()->has('error'))
    <script>
        var notyf = new Notyf({dismissible: true})
        notyf.error('{{ session('error') }}')
    </script>
    @endif

    <script>
        /* Simple Alpine Image Viewer */
        document.addEventListener('alpine:init', () => {
            Alpine.data('imageViewer', (src = '') => {
                return {
                    imageUrl: src,

                    refreshUrl() {
                        this.imageUrl = this.$el.getAttribute("image-url")
                    },

                    fileChosen(event) {
                        this.fileToDataUrl(event, src => this.imageUrl = src)
                    },

                    fileToDataUrl(event, callback) {
                        if (! event.target.files.length) return

                        let file = event.target.files[0],
                            reader = new FileReader()

                        reader.readAsDataURL(file)
                        reader.onload = e => callback(e.target.result)
                    },
                }
            })
        });

        function formatNumber (num) {
            return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")
        }

    </script>

  <!-- JS Plugins Init. -->
  <script>
    (function() {
      window.onload = function () {

      // INITIALIZATION OF FORM SEARCH
      // =======================================================
      new HSFormSearch('.js-form-search')

      // INITIALIZATION OF BOOTSTRAP DROPDOWN
      // =======================================================
      HSBsDropdown.init()

      // INITIALIZATION OF SELECT
      // =======================================================
      HSCore.components.HSTomSelect.init('.js-select')

      // INITIALIZATION OF INPUT MASK
      // =======================================================
      HSCore.components.HSMask.init('.js-input-mask')

      // INITIALIZATION OF NAV SCROLLER
      // =======================================================
      new HsNavScroller('.js-nav-scroller')

      // INITIALIZATION OF COUNTER
      // =======================================================
      new HSCounter('.js-counter')

      // INITIALIZATION OF TOGGLE PASSWORD
      // =======================================================
      new HSTogglePassword('.js-toggle-password')

      // INITIALIZATION OF FILE ATTACHMENT
      // =======================================================
      new HSFileAttach('.js-file-attach')

        // INITIALIZATION OF CHARTJS
        // =======================================================
        HSCore.components.HSChartJS.init('.js-chart')

      }
    })()
  </script>

     @stack('scripts')

  <!-- End Style Switcher JS -->
</body>
</html>
