<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

use Filament\Facades\Filament;
use Filament\Navigation\NavigationBuilder;
use Filament\Navigation\NavigationGroup;
use Filament\Navigation\NavigationItem;
use Filament\Navigation\UserMenuItem;

class FilamentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Using Vite
        // Filament::registerViteTheme('resources/css/app.css');


        Filament::registerNavigationGroups([
            'Human Resource',
            'Accounting',
            'Settings',
        ]);


        Filament::serving(function () {
            // Using Vite
            // Filament::registerViteTheme('resources/css/app.css');
            // Using Laravel Mix
            // Filament::registerTheme(
            //     mix('css/app.css'),
            // );

            Filament::registerStyles([
                // "https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css"
                // "https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/css/bootstrap.min.css",
                asset('css/5dist_css_bootstrap.min.css'),
            ]);

            Filament::registerScripts([
                "https://code.jquery.com/jquery-3.7.0.min.js",
                asset("js/print-js.js"),
            ]);


        }); 

    }

    public static function menuHandler() {

        $menu = [];

        if( auth()->user()->hasRole('payroll')) {
            $menu[] = NavigationGroup::make('Payroll')->items([
                    ...\App\Filament\Resources\PayrollResource::getNavigationItems(),
                    ...\App\Filament\Resources\ContributionResource::getNavigationItems(),
                    ...\App\Filament\Resources\DeductionResource::getNavigationItems(),
                    // ...\App\Filament\Resources\DeductionContributionResource::getNavigationItems(),
                    NavigationItem::make()->label('Monthly Payroll')->url("/" . env("FILAMENT_PATH") . '/monthly-payroll')->icon('heroicon-s-document-text'),

                ]);
        }

        $menu[] = NavigationGroup::make('Menu')->items([
            ...\App\Filament\Resources\EmployeeResource::getNavigationItems(),
            ...\App\Filament\Resources\AttendanceResource::getNavigationItems(),                
            ...\App\Filament\Resources\GadgetResource::getNavigationItems(),
            ...\App\Filament\Resources\DepartmentResource::getNavigationItems(),
            ...\App\Filament\Resources\StudentResource::getNavigationItems(),
            // NavigationItem::make()->label('Assistant')->url('/assistances')->icon('heroicon-s-document-text'),
            // NavigationItem::make()->label('Voice To Text')->url('/voice-to-text')->icon('heroicon-s-document-text'),
        ]);

        if( auth()->check() ) {

            if( auth()->user()->isSuperAdmin() ) {

                $menu[] = NavigationGroup::make('User Management')->items([
                        ...\App\Filament\Resources\UserResource::getNavigationItems(),
                        ...\App\Filament\Resources\RoleResource::getNavigationItems(),
                        ...\App\Filament\Resources\PermissionResource::getNavigationItems(),
                        ...\App\Filament\Resources\StatusResource::getNavigationItems(),
                    ]);

            }

        }

        $menu[] =  NavigationGroup::make('Settings')->items([
                ...\App\Filament\Resources\ActiveConfigResource::getNavigationItems(),
                NavigationItem::make()->label('Schedules')->url('/admin/schedules')->icon('heroicon-s-document-text'),
            ]);
        return $menu;
 
    }
}
