<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bank_reconciliation_items', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('bank_reconciliation_id');
            $table->unsignedBigInteger('journal_entry_line_id')->nullable();
            $table->date('transaction_date');
            $table->string('reference')->nullable();
            $table->text('description')->nullable();
            $table->decimal('debit', 20, 2)->default(0);
            $table->decimal('credit', 20, 2)->default(0);
            $table->boolean('is_reconciled')->default(false);
            $table->string('transaction_type')->nullable(); // deposit, withdrawal, check, etc.
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unsignedBigInteger('business_type_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->foreign('bank_reconciliation_id')
                ->references('id')
                ->on('bank_reconciliations')
                ->onDelete('cascade');
                
            $table->foreign('journal_entry_line_id')
                ->references('id')
                ->on('journal_entry_lines')
                ->onDelete('set null');
                
            $table->foreign('created_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('updated_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('business_type_id')
                ->references('id')
                ->on('business_types')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bank_reconciliation_items');
    }
};
