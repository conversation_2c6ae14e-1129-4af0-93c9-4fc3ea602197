@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="bi bi-pencil me-2"></i>Edit Tax Bracket
                        </h4>
                        <div class="d-flex gap-2">
                            <a href="{{ route('tax-brackets.show', $taxBracket) }}" class="btn btn-outline-info">
                                <i class="bi bi-eye me-1"></i>View
                            </a>
                            <a href="{{ route('tax-brackets.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-1"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>

                <form action="{{ route('tax-brackets.update', $taxBracket) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="card-body">
                        @include('app.tax-brackets.form-inputs')
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('tax-brackets.show', $taxBracket) }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-lg me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-1"></i>Update Tax Bracket
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
