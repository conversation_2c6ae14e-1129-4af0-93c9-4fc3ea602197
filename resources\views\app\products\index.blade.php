@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <x-page-header>
        <x-slot name="icon">box</x-slot>
        <x-slot name="title">Inventory</x-slot>
        <x-slot name="subtitle">Manage your product inventory</x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Stock::class)
            <a href="/stocks/transfers" class="btn btn-outline-primary btn-sm me-2">
                <i class="bi bi-arrow-left-right me-1"></i> Transfer Stock             
            </a>
            @endcan      
            @can('create', App\Models\Product::class)
            <a href="{{ route('products.create') }}" class="btn btn-primary btn-sm me-2">
                <i class="bi bi-plus-circle me-1"></i> @lang('crud.common.create') Product
            </a>
            @endcan                
            @can('create', App\Models\Product::class)
            <a href="#" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target=".import-products-modal">
                <i class="bi bi-file-earmark-excel me-1"></i> Import Products
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    

    <!-- Search & Filters -->
    <div class="card mb-3">
        <div class="card-body">
            <form action="{{ route('products.index') }}" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search Products</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="search" name="search" placeholder="Name, barcode, description..." value="{{ request('search') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-select js-select" id="category" name="category_id">
                        <option value="">All Categories</option>
                        @foreach(\App\Models\Category::orderBy('name')->get() as $category)
                        <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>{{ $category->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="stock" class="form-label">Stock Status</label>
                    <select class="form-select js-select" id="stock" name="stock_status">
                        <option value="">All Stock</option>
                        <option value="in_stock" {{ request('stock_status') == 'in_stock' ? 'selected' : '' }}>In Stock</option>
                        <option value="low_stock" {{ request('stock_status') == 'low_stock' ? 'selected' : '' }}>Low Stock</option>
                        <option value="out_of_stock" {{ request('stock_status') == 'out_of_stock' ? 'selected' : '' }}>Out of Stock</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-filter me-1"></i> Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Table -->
    <div class="card card-table">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">Product List</h4>
                </div>
                <div class="col-auto">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-download me-1"></i> Export
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="exportDropdown">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-excel me-1"></i> Excel</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-pdf me-1"></i> PDF</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-text me-1"></i> CSV</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                           Product
                        </th>                            
                        <th class="text-left">
                            Image
                        </th>
                        <th class="text-left">
                            Available Stock
                        </th>
                        <th class="text-left">
                            Units
                        </th>
                        <th class="text-left">
                            Vat Applied
                        </th>
                        <th class="text-center">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($products as $product)
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div>
                                    <span class="d-block h5 mb-0"> {{ $product->name ?? '-' }} </span>
                                    <span class="d-block fs-5 text-body"> {{ $product->description ?? ''}}</span>
                                    @if($product->barcode)
                                        <span class="badge bg-info text-white"> {{ $product->barcode }}</span>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td>
                            <x-partials.thumbnail src="{{ $product->image ?? '' }}"/>
                        </td>
                        <td>
                            @if($product->total_stock > 10)
                                <span class="badge bg-success text-white">{{ _number( $product->total_stock ) ?? '-' }}</span>
                            @elseif($product->total_stock > 0)
                                <span class="badge bg-warning text-dark">{{ _number( $product->total_stock ) ?? '-' }}</span>
                            @else
                                <span class="badge bg-danger text-white">Out of stock</span>
                            @endif
                        </td>
                        <td>
                            @foreach($product->units as $unit)
                                <span class="badge bg-primary text-white mb-1 d-inline-block"> {{ $unit->name ?? '-' }}: {{ _money($unit->selling_price) }} </span>
                            @endforeach
                        </td>
                        <td>
                            @if($product->vat_applied)
                                <span class="badge bg-success text-white"><i class="bi bi-check-circle me-1"></i> Yes</span>
                            @else
                                <span class="badge bg-secondary text-white"><i class="bi bi-x-circle me-1"></i> No</span>
                            @endif
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group" aria-label="Row Actions">
                                @can('update', $product)
                                <a href="{{ route('products.edit', $product) }}" class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit">
                                    <i class="bi bi-pencil-square"></i>
                                </a>
                                @endcan 
                                
                                @can('view', $product)
                                <a href="{{ route('products.show', $product) }}" class="btn btn-outline-info btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="View">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @endcan 
                                
                                @can('delete', $product)
                                <form action="{{ route('products.destroy', $product) }}" method="POST" onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')">
                                    @csrf @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="text-center p-4">
                            <div class="d-flex flex-column align-items-center justify-content-center py-5">
                                <i class="bi bi-box text-muted" style="font-size: 3rem;"></i>
                                <h5 class="mt-4">No products found</h5>
                                <p class="text-muted">Try adjusting your search or filter to find what you're looking for.</p>
                                @can('create', App\Models\Product::class)
                                <a href="{{ route('products.create') }}" class="btn btn-primary mt-2">
                                    <i class="bi bi-plus-circle me-1"></i> Add New Product
                                </a>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        @if(isset($products) && method_exists($products, 'hasPages') && $products->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-center">
                {{ $products->links() }}
            </div>
        </div>
        @endif
    </div>
    




</div>
@endsection





@push('scripts')
<script>
  $("document").ready(function () {
    dataTableBtn();
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl)
    });
  });
</script>
@endpush