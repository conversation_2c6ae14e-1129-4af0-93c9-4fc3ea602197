<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Status;
use Illuminate\Http\Request;
use App\Http\Requests\BranchStoreRequest;
use App\Http\Requests\BranchUpdateRequest;

class BranchController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Branch::class);

        $search = $request->get('search', '');

        $branches = Branch::search($search)
            ->latest()
            ->paginate()
            ->withQueryString();

        return view('app.branches.index', compact('branches', 'search'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Branch::class);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');

        return view('app.branches.create', compact('statuses'));
    }

    /**
     * @param \App\Http\Requests\BranchStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(BranchStoreRequest $request)
    {
        $this->authorize('create', Branch::class);

        $validated = $request->validated();
        $validated['created_by'] = auth()->id();

        $branch = Branch::create($validated);

        return redirect()
            ->route('branches.edit', $branch)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Branch $branch
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Branch $branch)
    {
        $this->authorize('view', $branch);

        return view('app.branches.show', compact('branch'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Branch $branch
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Branch $branch)
    {
        $this->authorize('update', $branch);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');

        return view('app.branches.edit', compact('branch', 'statuses'));
    }

    /**
     * @param \App\Http\Requests\BranchUpdateRequest $request
     * @param \App\Models\Branch $branch
     * @return \Illuminate\Http\Response
     */
    public function update(BranchUpdateRequest $request, Branch $branch)
    {
        $this->authorize('update', $branch);

        $validated = $request->validated();
        $validated['updated_by'] = auth()->id();

        $branch->update($validated);

        return redirect()
            ->route('branches.edit', $branch)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Branch $branch
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Branch $branch)
    {
        $this->authorize('delete', $branch);

        $branch->delete();

        return redirect()
            ->route('branches.index')
            ->withSuccess(__('crud.common.removed'));
    }

    public function setBranch(Request $request, Branch $branch) {
        auth()->user()->update(["branch_id" => $branch->id]);
        return redirect()
            ->back()
            ->withSuccess(__('crud.common.saved'));

    }


}
