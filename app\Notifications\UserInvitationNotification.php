<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\UserInvitation;

class UserInvitationNotification extends Notification
{
    use Queueable;

    protected $invitation;
    protected $inviterName;
    protected $tenantName;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(UserInvitation $invitation, $inviterName, $tenantName)
    {
        $this->invitation = $invitation;
        $this->inviterName = $inviterName;
        $this->tenantName = $tenantName;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $acceptUrl = url('/invitation/accept/' . $this->invitation->token);

        return (new MailMessage)
                    ->subject('You\'re invited to join ' . $this->tenantName)
                    ->greeting('Hello!')
                    ->line($this->inviterName . ' has invited you to join ' . $this->tenantName . ' as a ' . $this->invitation->role . '.')
                    ->when($this->invitation->message, function ($mail) {
                        return $mail->line('Personal message: "' . $this->invitation->message . '"');
                    })
                    ->line('Click the button below to accept the invitation and create your account.')
                    ->action('Accept Invitation', $acceptUrl)
                    ->line('This invitation will expire on ' . $this->invitation->expires_at->format('M d, Y \a\t g:i A') . '.')
                    ->line('If you did not expect to receive this invitation, you can safely ignore this email.')
                    ->salutation('Best regards, ' . config('app.name') . ' Team');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'invitation_id' => $this->invitation->id,
            'inviter_name' => $this->inviterName,
            'tenant_name' => $this->tenantName,
            'role' => $this->invitation->role,
            'expires_at' => $this->invitation->expires_at,
        ];
    }
}
