<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('accounts', function (Blueprint $table) {
            // Make existing columns not nullable
            $table->string('name')->nullable(false)->change();
            $table->string('code')->unique()->nullable(false)->change();
            
            // Add new columns
            $table->unsignedBigInteger('account_type_id')->nullable()->after('code');
            $table->unsignedBigInteger('account_category_id')->nullable()->after('account_type_id');
            $table->unsignedBigInteger('parent_account_id')->nullable()->after('account_category_id');
            $table->boolean('is_active')->default(true)->after('description');
            $table->boolean('is_system')->default(false)->after('is_active');
            $table->boolean('allows_manual_entries')->default(true)->after('is_system');
            $table->string('normal_balance')->default('debit')->after('allows_manual_entries'); // debit or credit
            $table->decimal('opening_balance', 20, 2)->default(0)->after('normal_balance');
            $table->date('opening_balance_date')->nullable()->after('opening_balance');
            $table->unsignedBigInteger('business_type_id')->nullable()->after('updated_by');
            
            // Add foreign keys
            $table->foreign('account_type_id')
                ->references('id')
                ->on('account_types')
                ->onDelete('set null');
                
            $table->foreign('account_category_id')
                ->references('id')
                ->on('account_categories')
                ->onDelete('set null');
                
            $table->foreign('parent_account_id')
                ->references('id')
                ->on('accounts')
                ->onDelete('set null');
                
            $table->foreign('business_type_id')
                ->references('id')
                ->on('business_types')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('accounts', function (Blueprint $table) {
            // Drop foreign keys
            $table->dropForeign(['account_type_id']);
            $table->dropForeign(['account_category_id']);
            $table->dropForeign(['parent_account_id']);
            $table->dropForeign(['business_type_id']);
            
            // Drop columns
            $table->dropColumn([
                'account_type_id',
                'account_category_id',
                'parent_account_id',
                'is_active',
                'is_system',
                'allows_manual_entries',
                'normal_balance',
                'opening_balance',
                'opening_balance_date',
                'business_type_id'
            ]);
            
            // Revert column changes
            $table->string('name')->nullable()->change();
            $table->string('code')->nullable()->change();
        });
    }
};
