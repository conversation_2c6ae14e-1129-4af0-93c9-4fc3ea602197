
<!-- Paid Invoices Table -->
<div class="table-responsive">
    <table id="paidTable" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
        <thead class="thead-light">
            <tr>
                <th class="text-left">Invoice #</th>
                <th class="text-left">@lang('crud.customers.singular')</th>
                <th class="text-left">Date</th>
                <th class="text-left">Amount</th>
                <th class="text-left">Status</th>
                <th class="text-left">Created By</th>
                <th class="text-center">Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($paidInvoices as $invoice)
            <tr>
                <td>
                    <a href="{{ route('invoices.show', $invoice) }}" class="text-primary fw-semibold">
                        #{{ $invoice->invoice_id ?? '-' }}
                    </a>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-xs avatar-circle">
                                <span class="avatar-initials">{{ substr($invoice->customer->name ?? 'C', 0, 1) }}</span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-2">
                            <h6 class="mb-0">{{ $invoice->customer->name ?? '-' }}</h6>
                            <small class="text-muted">{{ $invoice->customer->phone ?? '' }}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="d-flex flex-column">
                        <span>{{ optional($invoice->created_at)->format('M d, Y') }}</span>
                        <small class="text-muted">{{ optional($invoice->created_at)->format('h:i A') }}</small>
                    </div>
                </td>
                <td>
                    <div class="d-flex flex-column">
                        <span class="fw-semibold">{{ $invoice->amount_total ? _money($invoice->amount_total) : '-' }}</span>
                        @if($invoice->discount > 0)
                        <small class="text-muted">Discount: {{ _money($invoice->discount) }}</small>
                        @endif
                    </div>
                </td>
                <td>
                    @if($invoice->balance > 0)
                        <div class="d-flex align-items-center">
                            <span class="badge bg-warning me-2">Partially Paid</span>
                            <span class="text-danger fw-semibold">{{ _money($invoice->balance) }}</span>
                        </div>
                    @else
                        <span class="badge bg-success">Fully Paid</span>
                    @endif
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-xs avatar-soft-info avatar-circle">
                                <span class="avatar-initials">{{ substr($invoice->createdBy->name ?? 'U', 0, 1) }}</span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-2">
                            <span class="d-block">{{ $invoice->createdBy->name ?? '-' }}</span>
                        </div>
                    </div>
                </td>
                <td class="text-center" style="width: 134px;">
                    <div role="group" aria-label="Row Actions" class="btn-group">
                        @can('view', $invoice)
                            <button type="button" class="btn btn-soft-primary btn-sm preview-invoice-btn"
                                    data-bs-toggle="modal" data-id="{{ $invoice->id }}" data-bs-target=".preview-invoice-modal"
                                    title="View Invoice">
                                <i class="bi bi-eye"></i>
                            </button>

                            <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-soft-secondary btn-sm"
                               title="Invoice Details">
                                <i class="bi bi-file-earmark-text"></i>
                            </a>

                            @if($invoice->balance > 0)
                            <button type="button" class="btn btn-soft-success btn-sm"
                                    data-bs-toggle="modal" data-bs-target=".credit-modal"
                                    title="Record Payment">
                                <i class="bi bi-cash"></i>
                            </button>
                            @endif
                        @endcan

                        @if(auth()->user()->can('cancel invoices'))
                            <button type="button" class="btn btn-soft-danger btn-sm pending-invoice-btn"
                                    data-bs-toggle="modal" data-id="{{ $invoice->id }}" data-bs-target=".pending-invoice-modal"
                                    title="Cancel Invoice">
                                <i class="bi bi-x-circle"></i>
                            </button>
                        @endif
                    </div>
                </td>
            </tr>
            @empty
            <tr>
                <td colspan="7" class="text-center p-5">
                    <div class="avatar avatar-lg avatar-soft-secondary avatar-circle mb-3 mx-auto">
                        <span class="avatar-initials"><i class="bi bi-receipt"></i></span>
                    </div>
                    <h5>No Paid Invoices Found</h5>
                    <p class="mb-0 text-muted">No paid invoices match your search criteria or no invoices have been paid yet.</p>
                    @can('create', App\Models\Invoice::class)
                    <div class="mt-3">
                        <a href="{{ route('invoices.create') }}" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-plus-circle me-1"></i> Create New Invoice
                        </a>
                    </div>
                    @endcan
                </td>
            </tr>
            @endforelse
        </tbody>
    </table>
</div>

<!-- Pagination and Summary Footer -->
@if(count($paidInvoices) > 0)
<div class="card-footer bg-light">
    <div class="row align-items-center">
        <div class="col-md-6 mb-2 mb-md-0">
            <span>Showing {{ count($paidInvoices) }} paid invoices</span>
        </div>
        <div class="col-md-6">
            <div class="d-flex justify-content-md-end">
                <!-- Pagination would go here if needed -->
            </div>
        </div>
    </div>
</div>
@endif

