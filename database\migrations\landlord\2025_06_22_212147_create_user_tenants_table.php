<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_tenants', function (Blueprint $table) {
            $table->id();
            $table->string('user_email')->index(); // User email (before account creation)
            $table->unsignedBigInteger('user_id')->nullable()->index(); // User ID (after account creation)
            $table->string('tenant_id')->index(); // Tenant identifier
            $table->string('role')->default('user'); // Role in this tenant (admin, manager, user, etc.)
            $table->json('permissions')->nullable(); // Specific permissions for this tenant
            $table->boolean('is_active')->default(true); // Whether access is active
            $table->boolean('is_primary')->default(false); // Primary tenant for this user

            // Invitation-related columns
            $table->string('invitation_token')->nullable()->unique(); // Unique invitation token
            $table->timestamp('invited_at')->nullable(); // When invitation was sent
            $table->timestamp('expires_at')->nullable(); // When invitation expires
            $table->timestamp('accepted_at')->nullable(); // When invitation was accepted
            $table->timestamp('joined_at')->nullable(); // When user joined (same as accepted_at)
            $table->unsignedBigInteger('invited_by')->nullable(); // User ID who sent the invitation
            $table->text('invitation_message')->nullable(); // Personal message in invitation
            $table->unsignedBigInteger('warehouse_id')->nullable(); // Warehouse assignment
            $table->boolean('is_invitation_used')->default(false); // Whether invitation has been used
            $table->enum('status', ['pending', 'accepted', 'expired', 'cancelled'])->default('pending'); // Invitation status

            $table->timestamps();

            // Indexes for performance
            $table->index(['user_email', 'tenant_id']);
            $table->index(['user_id', 'tenant_id']);
            $table->index(['tenant_id', 'is_active']);
            $table->index(['invitation_token']);
            $table->index(['status', 'expires_at']);
            $table->index(['invited_by']);

            // Unique constraint to prevent duplicate user-tenant relationships
            $table->unique(['user_email', 'tenant_id'], 'unique_user_email_tenant');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_tenants');
    }
};
