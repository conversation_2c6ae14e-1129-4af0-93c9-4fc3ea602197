@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">
                            <i class="bi bi-clipboard-minus me-2"></i>Create Stock Adjustment
                        </h4>
                        <div class="d-flex gap-2">
                            <a href="{{ route('stocks.adjustments') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-list me-1"></i>View Adjustments
                            </a>
                            <a href="{{ route('stocks.index') }}" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-box me-1"></i>Stock Inventory
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Adjustment Form -->
    <div id="adjustmentApp" class="mt-4">
        <form ref="adjustmentForm" action="{{ route('stocks.store-adjustment') }}" method="POST" @submit="submitAdjustment">
            @csrf
            
            <!-- Adjustment Items Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul me-2"></i>Adjustment Items
                        <span class="badge bg-warning ms-2">@{{ adjustment.items.length }} item(s)</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 25%">Product</th>
                                    <th style="width: 12%">Unit</th>
                                    <th style="width: 10%">Quantity</th>
                                    <th style="width: 15%">Warehouse</th>
                                    <th style="width: 12%">Adjustment Type</th>
                                    <th style="width: 18%">Comment</th>
                                    <th style="width: 8%">Available</th>
                                    <th style="width: 5%">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(item, index) in adjustment.items" :key="index">
                                    <!-- Product -->
                                    <td>
                                        <select v-model="item.product_id" 
                                                @change="updateProductDetails(index)"
                                                :name="'items[' + index + '][product_id]'"
                                                class="form-select"
                                                required>
                                            <option value="">Select Product</option>
                                            <option v-for="product in products" 
                                                    :key="product.id" 
                                                    :value="product.id">
                                                @{{ product.name }}
                                            </option>
                                        </select>
                                    </td>

                                    <!-- Unit -->
                                    <td>
                                        <select v-model="item.unit_id"
                                                @change="updateUnitDetails(index)"
                                                :name="'items[' + index + '][unit_id]'"
                                                class="form-select"
                                                required>
                                            <option value="">Select Unit</option>
                                            <option v-for="unit in getProductUnits(item.product_id)" 
                                                    :key="unit.id" 
                                                    :value="unit.id">
                                                @{{ unit.name }}
                                            </option>
                                        </select>
                                    </td>

                                    <!-- Quantity -->
                                    <td>
                                        <input type="number"
                                               v-model="item.quantity"
                                               @input="validateQuantity(index)"
                                               :name="'items[' + index + '][quantity]'"
                                               class="form-control"
                                               step="0.01"
                                               min="0.01"
                                               required>
                                        <small v-if="item.quantityError" class="text-danger">@{{ item.quantityError }}</small>
                                    </td>

                                    <!-- Warehouse -->
                                    <td>
                                        <select v-model="item.warehouse_id"
                                                @change="updateAvailableStock(index)"
                                                :name="'items[' + index + '][warehouse_id]'"
                                                class="form-select"
                                                required>
                                            <option value="">Select Warehouse</option>
                                            <option v-for="warehouse in warehouses" 
                                                    :key="warehouse.id" 
                                                    :value="warehouse.id">
                                                @{{ warehouse.name }} (@{{ warehouse.branch.name }})
                                            </option>
                                        </select>
                                    </td>

                                    <!-- Adjustment Type -->
                                    <td>
                                        <select v-model="item.adjustment_type"
                                                :name="'items[' + index + '][adjustment_type]'"
                                                class="form-select"
                                                required>
                                            <option value="">Select Type</option>
                                            <option value="STOCK_ADJUSTMENT">Stock Adjustment</option>
                                            <option value="STOCK_COUNT">Stock Count</option>
                                            <option value="STOCK_EXPIRY">Stock Expiry</option>
                                            <option value="STOCK_SCRAP">Stock Scrap</option>
                                            <option value="STOCK_LOSS">Stock Loss</option>
                                            <option value="STOCK_REPAIR">Stock Repair</option>
                                            <option value="STOCK_RECYCLE">Stock Recycle</option>
                                            <option value="STOCK_WRITE_OFF">Stock Write Off</option>
                                            <option value="STOCK_WRITE_DOWN">Stock Write Down</option>
                                        </select>
                                    </td>

                                    <!-- Comment -->
                                    <td>
                                        <input type="text"
                                               v-model="item.comment"
                                               :name="'items[' + index + '][comment]'"
                                               class="form-control"
                                               placeholder="Adjustment reason..."
                                               required>
                                    </td>

                                    <!-- Available -->
                                    <td>
                                        <span class="badge bg-info">@{{ formatNumber(item.available_quantity || 0) }}</span>
                                    </td>

                                    <!-- Action -->
                                    <td>
                                        <button type="button" 
                                                @click="removeItem(index)"
                                                class="btn btn-outline-danger btn-sm">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Add Item Button -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <button type="button" @click="addItem" class="btn btn-outline-primary">
                            <i class="bi bi-plus-circle me-1"></i>Add Item
                        </button>
                        
                        <div class="text-muted">
                            Total Items: <strong>@{{ adjustment.items.length }}</strong>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Adjustment Notes -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-file-text me-2"></i>Adjustment Notes
                    </h5>
                </div>
                <div class="card-body">
                    <textarea v-model="adjustment.notes"
                              name="adjustment_notes"
                              class="form-control"
                              rows="3"
                              placeholder="Optional notes about this adjustment..."></textarea>
                    
                    <div class="alert alert-warning mt-3">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Note:</strong> This adjustment will create negative stock entries and requires approval before being processed.
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="card mt-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{{ route('stocks.adjustments') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Cancel
                        </a>
                        <button type="submit" 
                                class="btn btn-warning"
                                :disabled="!isFormValid || isSubmitting">
                            <i class="bi bi-arrow-clockwise spin me-1" v-if="isSubmitting"></i>
                            <i class="bi bi-clipboard-minus me-1" v-else></i>
                            @{{ isSubmitting ? 'Creating Adjustment...' : 'Create Adjustment (Pending Approval)' }}
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    new Vue({
        el: '#adjustmentApp',
        data() {
            return {
                products: @json($products),
                warehouses: @json($warehouses),
                adjustment: {
                    items: [this.createNewItem()],
                    notes: ''
                },
                isSubmitting: false
            }
        },
        computed: {
            isFormValid() {
                if (this.adjustment.items.length === 0) return false;
                
                return this.adjustment.items.every(item => {
                    return item.product_id &&
                           item.unit_id &&
                           item.quantity > 0 &&
                           item.warehouse_id &&
                           item.adjustment_type &&
                           item.comment &&
                           !item.quantityError;
                });
            }
        },
        mounted() {
            // Set up axios defaults
            axios.defaults.headers.common['X-CSRF-TOKEN'] = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        },
        methods: {
            createNewItem() {
                return {
                    product_id: '',
                    unit_id: '',
                    quantity: 1,
                    warehouse_id: '',
                    adjustment_type: '',
                    comment: '',
                    available_quantity: 0,
                    quantityError: ''
                };
            },
            
            addItem() {
                this.adjustment.items.push(this.createNewItem());
            },
            
            removeItem(index) {
                this.adjustment.items.splice(index, 1);
                if (this.adjustment.items.length === 0) {
                    this.addItem();
                }
            },
            
            getProductUnits(productId) {
                const product = this.products.find(p => p.id == productId);
                return product?.units || [];
            },
            
            async updateProductDetails(index) {
                const item = this.adjustment.items[index];
                if (!item.product_id) {
                    item.unit_id = '';
                    item.available_quantity = 0;
                    return;
                }
                
                // Reset unit when product changes
                item.unit_id = '';
                item.available_quantity = 0;
            },
            
            async updateUnitDetails(index) {
                await this.updateAvailableStock(index);
            },
            
            async updateAvailableStock(index) {
                const item = this.adjustment.items[index];
                if (!item.product_id || !item.unit_id || !item.warehouse_id) {
                    item.available_quantity = 0;
                    return;
                }
                
                try {
                    const response = await axios.get('/stocks/warehouse-stock', {
                        params: {
                            product_id: item.product_id,
                            unit_id: item.unit_id,
                            warehouse_id: item.warehouse_id
                        }
                    });
                    
                    item.available_quantity = response.data.available_quantity || 0;
                    this.validateQuantity(index);
                } catch (error) {
                    console.error('Error fetching available stock:', error);
                    item.available_quantity = 0;
                }
            },
            
            validateQuantity(index) {
                const item = this.adjustment.items[index];
                
                if (item.quantity <= 0) {
                    item.quantityError = 'Quantity must be greater than 0';
                } else {
                    item.quantityError = '';
                }
            },
            
            formatNumber(value) {
                return new Intl.NumberFormat().format(value || 0);
            },
            
            submitAdjustment(event) {
                if (!this.isFormValid) {
                    event.preventDefault();
                    alert('Please complete all required fields.');
                    return false;
                }
                
                this.isSubmitting = true;
                return true;
            }
        }
    });
});
</script>
@endpush
@endsection
