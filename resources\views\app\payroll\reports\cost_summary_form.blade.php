@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                Employee Cost Summary Report
            </h4>
        </div>

        <div class="card-body">
            <form action="{{ route('payroll-reports.cost-summary') }}" method="POST">
                @csrf

                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="month">Month</label>
                            <select name="month" id="month" class="form-control" required>
                                @foreach($months as $key => $value)
                                    <option value="{{ $key }}" {{ date('m') == $key ? 'selected' : '' }}>{{ $value }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="year">Year</label>
                            <select name="year" id="year" class="form-control" required>
                                @foreach($years as $key => $value)
                                    <option value="{{ $key }}" {{ date('Y') == $key ? 'selected' : '' }}>{{ $value }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="format">Format</label>
                            <select name="format" id="format" class="form-control" required>
                                <option value="html">HTML</option>
                                <option value="pdf">PDF</option>
                                <option value="csv">CSV</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <p><strong>Note:</strong> This report will show the total cost of employment for each employee, including basic pay and employer contributions. This is useful for budgeting and cost analysis purposes.</p>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ route('payroll.index') }}" class="btn btn-light">
                        <i class="bi bi-arrow-left"></i> Back
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-file-earmark-text"></i> Generate Report
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
