<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Branch;
use App\Models\Customer;
use App\Models\Order;
use App\Models\Invoice;
use App\Models\Debt;
use Carbon\Carbon;

use Facades\App\Cache\Repo;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        return view('home');
    }


    public function closeBusinessHour(Request $request){

        $skipDays = Invoice::whereNotNull("closed_at")
            ->whereYear("closed_at", date("Y"))
            ->whereMonth("closed_at", date("m"))
            ->whereDay("closed_at", date("d"))
            ->get()
            ->groupBy(function($date) {
                return Carbon::parse($date->closed_at)->format('Y-m-d'); // grouping by years
            })->count();

        $date = ( $skipDays ) ? \Carbon\Carbon::now()->addDays( $skipDays ) : \Carbon\Carbon::now();

        Invoice::whereNull("closed_at")
            ->whereYear("created_at", date("Y"))
            ->whereMonth("created_at", date("m"))
            ->whereDay("created_at", date("d"))
            ->update(["closed_at" => $date ]);
            
        Order::whereNull("closed_at")
            ->whereYear("created_at", date("Y"))
            ->whereMonth("created_at", date("m"))
            ->whereDay("created_at", date("d"))
            ->update(["closed_at" => $date ]);

        return redirect()->back()->withSuccess("Business Hour Closed.");
    }


    public function saleReport(Request $request){
        $products = Repo::getProductsWithSales();
        return view('app.reports.sale', compact('products'));
    }

    public function orderReport(Request $request){
        $orders = Repo::getPurchaseOrder();
        return view('app.reports.order', compact('orders'));
    }

    public function stockReport(Request $request){
        $products = Product::get();
        return view('app.reports.stock', compact('products'));
    }

    public function creditReport(Request $request){
        $customers = Customer::get();
        return view('app.reports.credit', compact('customers'));
    }

    public function expenseReport(Request $request){
        $debtors = Repo::getDebts("expenses");
        return view('app.reports.expense', compact('debtors'));
    }




}
