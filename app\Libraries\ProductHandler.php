<?php 

namespace App\Libraries;

use App\Models\Unit;
use App\Models\User;
use App\Models\Product;
use App\Models\Stock;
use App\Models\Sale;
use App\Models\Order;
use App\Models\StockItem;
use Carbon\Carbon;
use DB;

use Facades\App\Libraries\InvoiceHandler;

/**
 * 
 */
class ProductHandler
{



    public function transferStock(){
        foreach (_array( request()->products ) as $id ) {
            $product = Product::where("id", "$id")->first();
            $quantity = _from(request()->all(), $id);

            $params = [];
            $params['product_id'] = $product->id;
            $params['quantity'] = $quantity;
            $params['balance'] = $quantity;
            $params['selling_price'] = $product->selling_price;
            $params['buying_price'] = $product->buying_price;
            $params['branch_id'] = request()->to_branch_id;
            $params['approved_by'] = auth()->id();
            $params['status_id'] = 1;
            $params['stock_id'] = $product->stocks()->latest()->first()->id ?? '';

            // $stock = $product->stocks()->latest()->first();
            // $params = $stock->toArray();
            // $params['quantity'] = $quantity;
            // $params['stock_id'] = $stock->id;
            // $params['branch_id'] = request()->to_branch_id;
            // unset($params['created_at']);
            // unset($params['created_by']);
            // unset($params['updated_at']);
            // unset($params['updated_by']);

            $newStock = Stock::create($params);

            $product->stockItems()->whereNull("invoice_id")->limit($quantity)->update([
                "stock_id" => $newStock->id,
                "branch_id" => request()->to_branch_id,
            ]);
        }
    }


    public function generateItems($stock){
        for( $i = 0; $i < request()->quantity; $i++ ){
            $total = $stock->stockItems()->count();
            if($total < request()->quantity ){
                // $uuid = \Str::uuid()->toString();
                $uuid = time().'-'.mt_rand();
                StockItem::create([
                    "selling_price" => $stock->selling_price,
                    "buying_price" => $stock->buying_price,
                    "product_id" => $stock->product_id,
                    "stock_id" => $stock->id,
                    "branch_id" => auth()->user()->branch_id,
                    "name" => $stock->product_name,
                    "status_id" => 1,
                    "order_id" => request()->order_id,
                    "code" => $uuid,
                ]);
            } else return false;            
        }
    }

    public function approveOrder($order){
        if( $order->approved_by ) return $order;        
        // foreach( $order->stocks as $stock){
        //     // if($stock) $this->generateItems($stock);
        // }
        $order->stocks()->update(["approved_by" => auth()->id() ]);
        $order->update(["approved_by" => auth()->id() ]);
        if($order->amount_paid){
            InvoiceHandler::addPayment($order, [
                "amount" => $order->amount_paid,
                "balance" => $order->amount_total - $order->amount_paid,
            ]);
        }


        return $order;
    }

    public function settingUnits($product){

        $data = request()->all();
        $units = _from($data,'units');
        $unit_names = _from($data,'unit_names');
        $unit_quantities = _from($data,'unit_quantities');
        $unit_selling_prices = _from($data,'unit_selling_prices');
        $unit_buying_prices = _from($data,'unit_buying_prices');
        $unit_descriptions = _from($data,'unit_descriptions');

        foreach(_array($unit_names) as $key => $name){
            $unit = Unit::where("id", _from($units, $key) )->first();
            $params = [
                "product_id" => $product->id,
                "name" => _from($unit_names, $key),
                "quantity" => _from($unit_quantities, $key),
                "buying_price" => _from($unit_buying_prices, $key),
                "selling_price" => _from($unit_selling_prices, $key),
                "description" => _from($unit_descriptions, $key),
            ];
            if( $unit ) {
                $unit->update($params);
            } else {
                Unit::create($params);
            }

        }

    }


    public function adjustProduct($product, $num ) {

        $stocks = Sale::where("saleable_type", "App\Models\Order")->where("product_id", $product->id)->get();

        foreach( $stocks as $sale ) {

            $saleQuantity =  $sale->unit_quantity * $sale->quantity;

            $order = $sale->saleable;

            if( $order ) {

                $sub_total = $order->stocks()->get()->sum("buying_amount");
                $discount = $order->stocks()->get()->sum("discount");
                $amount_total = $sub_total - $discount;
                $data = [];
                $data["sub_total"] = $sub_total;    
                $data["amount_total"] = $amount_total;    
                $data["amount_paid"] = $amount_total;    
                $data["discount"] = $discount;   

                $order->update($data);
                $order->payments()->delete();
                InvoiceHandler::addPayment($order, [
                    "amount" => $order->amount_paid,
                    "balance" => $order->amount_total - $order->amount_paid
                ]); 

            }

        }


    }

    public function stockAdjustment() {

        $validated = request()->all();

        if( ! auth()->user()->branch_id ) return redirect()->back()->with("error", "Please Select Branch first");

        $orderData = request()->input('order');
        $data = json_decode($orderData, true);

        $data["supplier_name"] = 'Stock Adjustment Record';
        $data["description"] = 'Stock Adjustment Record';
        $data["approved_by"] = auth()->id();
        $data["status_id"] = 14;

        $order = Order::create( $data );
        $stocks  = _array( _from($data, "stocks") );

        foreach ($stocks as $stock) {
            if( _from($stock, "product_id") ) {
                $unit = Unit::find(_from($stock, "unit_id") );
                if( $unit ) { 
                    $stock['unit_name'] = $unit->name; 
                    $stock['unit_quantity'] = $unit->quantity; 
                }
                $order->stocks()->create($stock);
            }
        }
    }



}
