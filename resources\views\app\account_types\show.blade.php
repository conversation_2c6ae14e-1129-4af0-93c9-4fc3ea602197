@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">journal-text</x-slot>
        <x-slot name="title">{{ $accountType->name }}</x-slot>
        <x-slot name="subtitle">Account Type Details</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Accounting</a></li>
            <li class="breadcrumb-item"><a href="{{ route('account-types.index') }}">Account Types</a></li>
            <li class="breadcrumb-item active" aria-current="page">View</li>
        </x-slot>
        <x-slot name="controls">
            @can('update', $accountType)
            <a href="{{ route('account-types.edit', $accountType) }}" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> Edit Account Type
            </a>
            @endcan
            <a href="{{ route('account-types.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Account Types
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <!-- Account Type Overview Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-info-circle me-2"></i>Account Type Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        @php
                            $classColors = [
                                'asset' => 'success',
                                'liability' => 'danger',
                                'equity' => 'info',
                                'revenue' => 'primary',
                                'expense' => 'warning'
                            ];
                            $color = $classColors[$accountType->classification] ?? 'secondary';
                        @endphp
                        <div class="avatar avatar-xl avatar-circle mb-3 mx-auto bg-soft-{{ $color }}">
                            <span class="avatar-initials text-{{ $color }}">{{ substr($accountType->name, 0, 1) }}</span>
                        </div>
                        <h4 class="mb-1">{{ $accountType->name }}</h4>
                        <p class="text-muted">
                            <span class="badge bg-soft-{{ $color }} text-{{ $color }}">
                                {{ ucfirst($accountType->classification) }}
                            </span>
                        </p>
                    </div>

                    <hr>

                    <div class="mb-4">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Code:</span>
                            <span class="badge bg-soft-primary">{{ $accountType->code ?? 'N/A' }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Status:</span>
                            <span class="badge bg-{{ $accountType->is_active ? 'success' : 'danger' }}">
                                {{ $accountType->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Type:</span>
                            <span class="badge bg-{{ $accountType->is_system ? 'info' : 'secondary' }}">
                                {{ $accountType->is_system ? 'System' : 'User-defined' }}
                            </span>
                        </div>
                    </div>

                    <hr>

                    <h6 class="mb-3">Description</h6>
                    <p class="mb-0">{{ $accountType->description ?? 'No description available.' }}</p>
                </div>
            </div>
        </div>

        <!-- Account Type Statistics Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-graph-up me-2"></i>Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="card card-dashed h-100">
                                <div class="card-body d-flex justify-content-center flex-column text-center">
                                    <h2 class="mb-1">{{ $accountType->categories()->count() }}</h2>
                                    <span class="text-muted">Categories</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card card-dashed h-100">
                                <div class="card-body d-flex justify-content-center flex-column text-center">
                                    <h2 class="mb-1">{{ $accountType->accounts()->count() }}</h2>
                                    <span class="text-muted">Accounts</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3">System Information</h6>
                    <div class="d-flex justify-content-between mb-1">
                        <span class="text-muted">Created:</span>
                        <span>{{ $accountType->created_at->format('M d, Y h:i A') }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span class="text-muted">Last Updated:</span>
                        <span>{{ $accountType->updated_at->format('M d, Y h:i A') }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Information Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-link-45deg me-2"></i>Related Information
                    </h5>
                </div>
                <div class="card-body">
                    <h6 class="mb-3">Financial Statement</h6>
                    <p class="mb-4">
                        @php
                            $statements = [
                                'asset' => 'Balance Sheet (Assets)',
                                'liability' => 'Balance Sheet (Liabilities)',
                                'equity' => 'Balance Sheet (Equity)',
                                'revenue' => 'Income Statement (Revenue)',
                                'expense' => 'Income Statement (Expenses)'
                            ];
                        @endphp
                        This account type appears on the <strong>{{ $statements[$accountType->classification] ?? 'Unknown' }}</strong>.
                    </p>

                    <h6 class="mb-3">Normal Balance</h6>
                    <p class="mb-4">
                        @php
                            $normalBalances = [
                                'asset' => 'Debit',
                                'liability' => 'Credit',
                                'equity' => 'Credit',
                                'revenue' => 'Credit',
                                'expense' => 'Debit'
                            ];
                        @endphp
                        The normal balance for this account type is <strong>{{ $normalBalances[$accountType->classification] ?? 'Unknown' }}</strong>.
                    </p>

                    <div class="mt-4">
                        <a href="#accountCategories" class="btn btn-soft-primary btn-sm mb-2">
                            <i class="bi bi-folder me-1"></i> View Categories
                        </a>
                        <a href="#accounts" class="btn btn-soft-info btn-sm mb-2">
                            <i class="bi bi-journal-bookmark me-1"></i> View Accounts
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @can('view-any', App\Models\AccountCategory::class)
    <div class="card mb-4" id="accountCategories">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">
                        <i class="bi bi-folder me-2"></i>Account Categories
                    </h4>
                </div>
                <div class="col-auto">
                    @can('create', App\Models\AccountCategory::class)
                    <a href="{{ route('account-categories.create') }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-plus-circle me-1"></i> Add Category
                    </a>
                    @endcan
                </div>
            </div>
        </div>
        <div class="card-body">
            <livewire:account-type-account-categories-detail :accountType="$accountType" />
        </div>
    </div>
    @endcan

    @can('view-any', App\Models\Account::class)
    <div class="card mb-4" id="accounts">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">
                        <i class="bi bi-journal-bookmark me-2"></i>Accounts
                    </h4>
                </div>
                <div class="col-auto">
                    @can('create', App\Models\Account::class)
                    <a href="{{ route('accounts.create') }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-plus-circle me-1"></i> Add Account
                    </a>
                    @endcan
                </div>
            </div>
        </div>
        <div class="card-body">
            <livewire:account-type-accounts-detail :accountType="$accountType" />
        </div>
    </div>
    @endcan

    <!-- Action Buttons -->
    <div class="d-flex justify-content-between mt-2 mb-4">
        <div>
            <a href="{{ route('account-types.index') }}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left me-1"></i> Back to Account Types
            </a>

            @can('create', App\Models\AccountType::class)
            <a href="{{ route('account-types.create') }}" class="btn btn-outline-primary">
                <i class="bi bi-plus-circle me-1"></i> Create New Account Type
            </a>
            @endcan
        </div>

        <div>
            @can('update', $accountType)
            <a href="{{ route('account-types.edit', $accountType) }}" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> Edit
            </a>
            @endcan
            
            @can('delete', $accountType)
            <form action="{{ route('account-types.destroy', $accountType) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this account type?')">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-danger" {{ $accountType->is_system ? 'disabled' : '' }}>
                    <i class="bi bi-trash me-1"></i> Delete
                </button>
            </form>
            @endcan
        </div>
    </div>
</div>
@endsection
