@extends('layouts.app')

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="title">
            {{ $debt->debtable->name ?? ''}}
            (Debtor)
        </x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Invoice::class)
                <a href="{{ url()->previous() }}" class="btn btn-info btn-sm d-inline-flex align-items-center rounded-pill shadow px-3 py-1 gap-2" style="font-weight: 500;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-left" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 1-.5.5H2.707l3.147 3.146a.5.5 0 0 1-.708.708l-4-4a.5.5 0 0 1 0-.708l4-4a.5.5 0 1 1 .708.708L2.707 7.5H14.5A.5.5 0 0 1 15 8z"/>
                    </svg>
                    Back
                </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="mt-4">



      <div class="row">


        <div class="col-lg-8 mb-3 mb-lg-5">
          <!-- Card -->
          <div class="card h-100">
            <!-- Header -->
            <div class="card-header card-header-content-between">
              <h4 class="card-header-title">Amount Summary</h4>

              <!-- Dropdown -->
              <div class="dropdown">
                <button type="button" class="btn btn-ghost-secondary btn-icon btn-sm rounded-circle" id="reportsOverviewDropdown3" data-bs-toggle="dropdown" aria-expanded="false">
                  <i class="bi-three-dots-vertical"></i>
                </button>
              </div>
              <!-- End Dropdown -->
            </div>
            <!-- End Header -->

            <!-- Body -->
            <div class="card-body card-body-height">
              <ul class="list-group list-group-flush list-group-no-gutters">
                <!-- List Item -->
                <li class="list-group-item">
                  <div class="d-flex">

                    <div class="flex-grow-1 ms-3">
                      <div class="row">

                        <div class="col-3 col-md-3 order-md-1">
                          <h5 class="mb-0">Date</h5>
                        </div>                 

                        <div class="col-3 col-md-3 order-md-1">
                          <h5 class="mb-0">Debit</h5>
                        </div>

                        <div class="col-3 col-md-3 order-md-3 text-end mt-2 mt-md-0">
                          <h5 class="mb-0"> Credit </h5>
                        </div>

                        <div class="col-3 col-md-3 order-md-3 text-end mt-2 mt-md-0">
                          <h5 class="mb-0"> Balance </h5>
                        </div>

                      </div>
                      <!-- End Row -->
                    </div>
                  </div>
                </li>

                @php $balance = 0 @endphp
                @foreach( $debtable->debtors()->latest()->get() as $debt)

                  @foreach( $debt->payments()->latest()->get() as $payment)
                    <li class="list-group-item">
                      <div class="d-flex">

                        <div class="flex-grow-1 ms-3">
                          <div class="row">

                            <div class="col-3 col-md-3 order-md-1">
                              <!-- <h5 class="mb-0"></h5> -->
                              <span class="fs-6 text-body"> {{  $payment->created_at }} </span>
                            </div>                

                            <div class="col-3 col-md-3 order-md-1">
                              <!-- <h5 class="mb-0"></h5> -->
                              <span class="fs-6 text-bod text-danger"></span>
                            </div>

                            <div class="col-3 col-md-3 order-md-3 text-end mt-2 mt-md-0">
                              <!-- <h5 class="mb-0">  </h5> -->
                              <span class="fs-6 text-bod text-danger">  {{ _money( $payment->amount ) ?? ''}}  </span>
                            </div>

                            <div class="col-3 col-md-3 order-md-3 text-end mt-2 mt-md-0">
                              <!-- <h5 class="mb-0"> </h5> -->
                              <span class="fs-6 text-bod">  {{ _money( $payment->balance ) ?? ''}}  </span>
                            </div>

                          </div>
                          <!-- End Row -->
                        </div>
                      </div>
                    </li>
                  @endforeach

                    <li class="list-group-item">
                      <div class="d-flex">

                        <div class="flex-grow-1 ms-3">
                          <div class="row">
                            
                            <div class="col-3 col-md-3 order-md-1">
                              <!-- <h5 class="mb-0"></h5> -->
                              <span class="fs-6 text-body"> {{ $debt->created_at }} </span>
                            </div>
                            
                            <div class="col-3 col-md-3 order-md-1">
                              <!-- <h5 class="mb-0"></h5> -->
                              <span class="fs-6 text-bod text-success"> {{ _money( $debt->amount_total ) ?? ''}} </span>
                            </div>

                            <div class="col-3 col-md-3 order-md-3 text-end mt-2 mt-md-0">
                              <!-- <h5 class="mb-0"></h5> -->
                              <span class="fs-6 text-bod text-danger"> </span>
                            </div>

                            <!-- php $balance += $debt->amount_total @endphp -->
                            <div class="col-3 col-md-3 order-md-3 text-end mt-2 mt-md-0">
                              <!-- <h5 class="mb-0"> </h5> -->
                              <span class="fs-6 text-bod"> {{ _money( $debt->amount_total ) ?? ''}} </span>
                            </div>

                          </div>
                          <!-- End Row -->
                        </div>
                      </div>
                    </li>
                @endforeach
                <!-- End List Item -->
              </ul>
            </div>
            <!-- End Body -->

            <!-- Header -->
            <div class="card-footer card-footer-content-between">
              Opening Balance: <span> {{ _money($debtable->debtors->sum('amount_total') ) }}</span> <br>
              Closing Balance: <span> {{ _money(  $debtable->debtors->sum('amount_total')  -  $debtable->debtors->sum('amount_paid')  )}}</span>
            </div>
            <!-- End footer -->

          </div>
          <!-- End Card -->
        </div>
      </div>
      <!-- End Row -->


    </div>


</div>

@endsection

@push("scripts")
    
    <script type="text/javascript">
        
        $("document").ready( function(){

            // INITIALIZATION OF CHARTJS
            // =======================================================
            HSCore.components.HSChartJS.init(document.querySelector('.js-chartjs-doughnut-half'), {
              options: {
                plugins: {
                  tooltip: {
                    postfix: "%"
                  }
                },
                cutout: '85%',
                rotation: '270',
                circumference: '180'
              }
            });
        });

    </script>
    
@endpush