@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">graph-up</x-slot>
        <x-slot name="title">Sales Report</x-slot>
        <x-slot name="subtitle">Track and analyze your product sales performance</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Reports</a></li>
            <li class="breadcrumb-item active" aria-current="page">Sales</li>
        </x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Invoice::class)
            <a href="{{ route('invoices.create') }}" class="btn btn-primary btn-sm">
                <i class="bi bi-plus-circle me-1"></i> @lang('crud.common.create') Sale
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Stats Overview -->
    <div class="row mb-4">
        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-subtitle mb-2">Total Sales</h6>
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="card-title text-inherit">{{ _money($soldValueTotal ?? 0) }}</h2>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-soft-primary text-primary p-2">
                                <i class="bi bi-cart-check fs-3"></i>
                            </span>
                        </div>
                    </div>
                    <span class="badge bg-soft-secondary text-secondary">
                        {{ count($products ?? []) }} products
                    </span>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-subtitle mb-2">Total Profit</h6>
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="card-title text-inherit">{{ _money($soldValueProfitTotal ?? 0) }}</h2>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-soft-success text-success p-2">
                                <i class="bi bi-cash-stack fs-3"></i>
                            </span>
                        </div>
                    </div>
                    <span class="badge bg-soft-success text-success">
                        {{ $soldValueTotal > 0 ? number_format(($soldValueProfitTotal / $soldValueTotal) * 100, 1) : 0 }}% margin
                    </span>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-subtitle mb-2">Average Sale</h6>
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="card-title text-inherit">
                                {{ _money(count($products) > 0 ? $soldValueTotal / count($products) : 0) }}
                            </h2>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-soft-info text-info p-2">
                                <i class="bi bi-calculator fs-3"></i>
                            </span>
                        </div>
                    </div>
                    <span class="badge bg-soft-info text-info">
                        Per product
                    </span>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-subtitle mb-2">Top Selling Product</h6>
                    <div class="row align-items-center">
                        <div class="col">
                            @php
                                $topProduct = null;
                                $topSale = 0;
                                foreach($products ?? [] as $product) {
                                    $sale = $product->getSale();
                                    $saleValue = _from($sale, 'soldValue');
                                    if($saleValue > $topSale) {
                                        $topSale = $saleValue;
                                        $topProduct = $product;
                                    }
                                }
                            @endphp
                            @if($topProduct)
                                <h5 class="card-title text-inherit">{{ $topProduct->name }}</h5>
                                <p class="small text-truncate">{{ _money($topSale) }}</p>
                            @else
                                <h5 class="card-title text-inherit">N/A</h5>
                            @endif
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-soft-warning text-warning p-2">
                                <i class="bi bi-trophy fs-3"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Stats Overview -->

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-header-title">Filter Sales Data</h5>
        </div>
        <div class="card-body">
            <form id="filter" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Date Range</label>
                    <input type="hidden" name="from" value="{{ request()->from }}">
                    <input type="hidden" name="to" value="{{ request()->to }}">
                    <button id="js-daterangepicker-predefined" type="button" class="btn btn-white w-100">
                        <i class="bi-calendar-week me-1"></i>
                        <span class="js-daterangepicker-predefined-preview">Select date range</span>
                    </button>
                </div>

                <div class="col-md-3">
                    <label class="form-label">Business Days</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select day-filter" name="day" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a date..."
                        }'>
                            <option value="0">Select Closing Day</option>
                            @foreach(Facades\App\Cache\Repo::getClosingDays() as $day)
                                <option value="{{ $day }}" @if($day == request()->day) selected @endif>
                                    {{ $day }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <label class="form-label">User</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select user-filter" name="user_id" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a user..."
                        }'>
                            <option value="0">All Users</option>
                            @foreach(App\Models\User::get() as $user)
                                <option value="{{ $user->id }}" @if($user->id == request()->user_id) selected @endif>
                                    {{ $user->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <label class="form-label">Product</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select product-filter" name="product_id" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a product..."
                        }'>
                            <option value="0">All Products</option>
                            @foreach(App\Models\Product::get() as $product)
                                <option value="{{ $product->id }}" @if($product->id == request()->product_id) selected @endif>
                                    {{ $product->name }} ~ {{ $product->description }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="col-12 d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-filter me-1"></i> Apply Filters
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- End Filters -->

    <!-- Table -->
    <div class="card card-table">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">Sales Performance by Product</h4>
                </div>
                <div class="col-auto">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-download me-1"></i> Export
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="exportDropdown">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-excel me-1"></i> Excel</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-pdf me-1"></i> PDF</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-text me-1"></i> CSV</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>#</th>
                        <th>Product Name</th>                       
                        <th>Opening Stock</th>                    
                        <th>Sold Quantity</th>                    
                        <th>Sold Amount</th>
                        <th>Profit</th>
                        <th>Closing Stock</th>
                        <th>Actions</th>                        
                    </tr>
                </thead>
                <tbody>
                    @php 
                    $soldValueTotal = 0;
                    $soldValueProfitTotal = 0;
                    @endphp

                    @forelse($products as $key => $product)
                    @php 
                    $sale = $product->getSale(); 
                    $soldValueTotal += _from($sale, 'soldValue');
                    $soldValueProfitTotal += _from($sale, 'soldProfit');
                    @endphp
                    <tr>
                        <td>{{ $key + 1 }}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar avatar-sm avatar-soft-primary avatar-circle">
                                        <span class="avatar-initials">{{ substr($product->name, 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5 class="mb-0">{{ $product->name ?? '-' }}</h5>
                                    <span class="d-block text-muted small">{{ $product->description ?? '' }}</span>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-soft-secondary">{{ _decimal(_from($sale, 'openingQuantity')) ?? '-' }}</span>
                        </td>
                        <td>
                            <span class="badge bg-soft-primary">{{ _decimal(_from($sale, 'soldQuantity')) ?? '-' }}</span>
                        </td>
                        <td>
                            <span class="fw-semibold">{{ _money(_from($sale, 'soldValue')) ?? '-' }}</span>
                        </td>
                        <td>
                            <span class="fw-semibold text-success">{{ _money(_from($sale, 'soldProfit')) ?? '-' }}</span>
                        </td>
                        <td>
                            <span class="badge bg-soft-info">{{ _decimal(_from($sale, 'closingQuantity')) ?? '-' }}</span>
                        </td>
                        <td>
                            <a href="#" class="btn btn-white btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="View Details">
                                <i class="bi-eye"></i>
                            </a>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="text-center p-4">
                            <div class="d-flex flex-column align-items-center justify-content-center py-5">
                                <i class="bi bi-graph-up text-muted" style="font-size: 3rem;"></i>
                                <h5 class="mt-4">No sales data found</h5>
                                <p class="text-muted">Try adjusting your search or filter to find what you're looking for.</p>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
                <tfoot>
                    @if(count($products))
                    <tr class="bg-light fw-bold">
                        <td colspan="4" class="text-end">Total:</td>
                        <td>
                            <span class="h5 text-primary">{{ _money($soldValueTotal) }}</span>
                        </td>
                        <td>
                            <span class="h5 text-success">{{ _money($soldValueProfitTotal) }}</span>
                        </td>
                        <td colspan="2"></td>
                    </tr>
                    @endif
                </tfoot>
            </table>
        </div>
    </div>
    <!-- End Table -->

    <!-- Sales Chart -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-header-title">Sales Trend</h5>
        </div>
        <div class="card-body">
            <div class="chartjs-custom" style="height: 300px;">
                <canvas id="salesChart" class="js-chart"></canvas>
            </div>
        </div>
    </div>
    <!-- End Sales Chart -->
</div>
@endsection

@push('scripts')
<script>
    $("document").ready(function () {
        // Initialize DataTable
        dataTableBtn();
        
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
        
        // Sample chart data - replace with actual data in production
        var ctx = document.getElementById('salesChart');
        if (ctx) {
            var salesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
                    datasets: [{
                        label: 'Sales',
                        data: [25, 40, 30, 35, 50, 45, 60],
                        borderColor: '#377dff',
                        backgroundColor: 'rgba(55, 125, 255, 0.1)',
                        borderWidth: 2,
                        fill: true
                    }, {
                        label: 'Profit',
                        data: [15, 20, 18, 19, 25, 30, 35],
                        borderColor: '#00c9a7',
                        backgroundColor: 'rgba(0, 201, 167, 0.1)',
                        borderWidth: 2,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(180, 208, 224, 0.3)',
                                drawBorder: false
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(180, 208, 224, 0.3)',
                                drawBorder: false
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        },
                        legend: {
                            position: 'top',
                            align: 'end'
                        }
                    }
                }
            });
        }
    });
</script>
@endpush