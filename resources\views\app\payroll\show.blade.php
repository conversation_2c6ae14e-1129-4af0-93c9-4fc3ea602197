@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header Card -->
            <div class="card mb-4 hide-print">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">
                                <i class="bi bi-receipt me-2"></i>Payslip Details
                            </h4>
                            <small>{{ $payroll->date_from ? $payroll->date_from->format('F Y') : 'N/A' }}</small>
                        </div>
                        <div class="d-flex gap-2">
                    <!--         <button type="button" class="btn btn-light btn-sm" onclick="printPayslip('payslip-content')">
                                <i class="bi bi-printer me-1"></i>Print
                            </button> -->
                            <a href="{{ route('payroll.payslip', $payroll) }}" class="btn btn-success btn-sm" target="_blank">
                                <i class="bi bi-file-earmark-pdf me-1"></i>Download PDF
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Payslip Content -->
            <div class="card payslip-content bg-white text-dark" id="payslip-content">
                <div class="card-body p-4">
                    <!-- Company Header -->
                    <div class="text-center mb-4 border-bottom border-dark pb-3">
                        <h2 class="mb-1 text-dark">{{ config('app.name', 'Company Name') }}</h2>
                        <p class="text-secondary mb-0">{{ auth()->user()->branch->address ?? 'Company Address' }}</p>
                        <p class="text-secondary mb-0">Phone: {{ auth()->user()->branch->phone ?? '+****************' }}</p>
                        <h4 class="mt-3 text-dark fw-bold">PAYSLIP</h4>
                        <p class="mb-0 text-dark">Pay Period: {{ $payroll->date_from ? $payroll->date_from->format('F d, Y') : 'N/A' }} - {{ $payroll->date_to ? $payroll->date_to->format('F d, Y') : 'N/A' }}</p>
                    </div>

                    <!-- Employee Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border border-dark bg-white">
                                <div class="card-header bg-light border-bottom border-dark">
                                    <h6 class="mb-0 text-dark"><i class="bi bi-person me-2"></i>Employee Information</h6>
                                </div>
                                <div class="card-body bg-white">
                                    <table class="table table-borderless mb-0">
                                        <tr>
                                            <td class="fw-bold text-dark">Name:</td>
                                            <td class="text-dark">{{ $payroll->employee->name ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-dark">Employee ID:</td>
                                            <td class="text-dark">{{ $payroll->employee->id ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-dark">Email:</td>
                                            <td class="text-dark">{{ $payroll->employee->email ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-dark">Phone:</td>
                                            <td class="text-dark">{{ $payroll->employee->phone ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-dark">Grade:</td>
                                            <td class="text-dark">{{ $payroll->employee->grade ?? '-' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border border-dark bg-white">
                                <div class="card-header bg-light border-bottom border-dark">
                                    <h6 class="mb-0 text-dark"><i class="bi bi-calculator me-2"></i>Pay Summary</h6>
                                </div>
                                <div class="card-body bg-white">
                                    <table class="table table-borderless mb-0">
                                        <tr>
                                            <td class="fw-bold text-dark">Basic Pay:</td>
                                            <td class="text-end text-dark">K{{ number_format($payroll->basic_pay, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-dark">Gross Pay:</td>
                                            <td class="text-end text-dark">K{{ number_format($payroll->gross_pay ?: $payroll->gross, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-dark">Total Deductions:</td>
                                            <td class="text-end text-dark">K{{ number_format(($payroll->tax_amount ?: $payroll->payee) + $payroll->deductions->sum('amount'), 2) }}</td>
                                        </tr>
                                        <tr class="border-top border-dark">
                                            <td class="fw-bold fs-5 text-dark">Net Pay:</td>
                                            <td class="text-end fw-bold fs-5 text-dark">K{{ number_format($payroll->net_pay, 2) }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Earnings and Deductions -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border border-dark bg-white">
                                <div class="card-header bg-light border-bottom border-dark">
                                    <h6 class="mb-0 text-dark"><i class="bi bi-plus-circle me-2"></i>Earnings</h6>
                                </div>
                                <div class="card-body bg-white">
                                    <table class="table table-bordered border-dark mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th class="text-dark border-dark">Description</th>
                                                <th class="text-end text-dark border-dark">Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="text-dark border-dark">Basic Pay</td>
                                                <td class="text-end text-dark border-dark">K{{ number_format($payroll->basic_pay, 2) }}</td>
                                            </tr>
                                            @forelse($payroll->contributions as $contribution)
                                            <tr>
                                                <td class="text-dark border-dark">{{ $contribution->name }}</td>
                                                <td class="text-end text-dark border-dark">K{{ number_format($contribution->amount, 2) }}</td>
                                            </tr>
                                            @empty
                                            <tr>
                                                <td colspan="2" class="text-center text-secondary border-dark">No additional earnings</td>
                                            </tr>
                                            @endforelse
                                        </tbody>
                                        <tfoot class="bg-dark">
                                            <tr>
                                                <th class="text-white border-dark">Total Earnings</th>
                                                <th class="text-end text-white border-dark">K{{ number_format($payroll->gross_pay ?: $payroll->gross, 2) }}</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border border-dark bg-white">
                                <div class="card-header bg-light border-bottom border-dark">
                                    <h6 class="mb-0 text-dark"><i class="bi bi-dash-circle me-2"></i>Deductions</h6>
                                </div>
                                <div class="card-body bg-white">
                                    <table class="table table-bordered border-dark mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th class="text-dark border-dark">Description</th>
                                                <th class="text-end text-dark border-dark">Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="text-dark border-dark">PAYE Tax</td>
                                                <td class="text-end text-dark border-dark">K{{ number_format($payroll->tax_amount ?: $payroll->payee, 2) }}</td>
                                            </tr>
                                            @forelse($payroll->deductions as $deduction)
                                            <tr>
                                                <td class="text-dark border-dark">{{ $deduction->name }}</td>
                                                <td class="text-end text-dark border-dark">K{{ number_format($deduction->amount, 2) }}</td>
                                            </tr>
                                            @empty
                                            <tr>
                                                <td colspan="2" class="text-center text-secondary border-dark">No additional deductions</td>
                                            </tr>
                                            @endforelse
                                        </tbody>
                                        <tfoot class="bg-dark">
                                            <tr>
                                                <th class="text-white border-dark">Total Deductions</th>
                                                <th class="text-end text-white border-dark">K{{ number_format(($payroll->tax_amount ?: $payroll->payee) + $payroll->deductions->sum('amount'), 2) }}</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

 

                    <!-- Final Summary -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-2 border-dark bg-white">
                                <div class="card-header bg-dark text-white">
                                    <h5 class="mb-0 text-white"><i class="bi bi-calculator me-2"></i>Pay Summary</h5>
                                </div>
                                <div class="card-body bg-light">
                                    <div class="row text-center">
                                        <div class="col-md-4">
                                            <div class="border-end border-dark">
                                                <h3 class="mb-1 text-dark">K{{ number_format($payroll->gross_pay ?: $payroll->gross, 2) }}</h3>
                                                <p class="text-secondary mb-0">Gross Pay</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="border-end border-dark">
                                                <h3 class="mb-1 text-dark">K{{ number_format(($payroll->tax_amount ?: $payroll->payee) + $payroll->deductions->sum('amount'), 2) }}</h3>
                                                <p class="text-secondary mb-0">Total Deductions</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <h3 class="fw-bold mb-1 text-dark">K{{ number_format($payroll->net_pay, 2) }}</h3>
                                            <p class="text-secondary mb-0">Net Pay</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="text-center">
                        <small class="text-secondary">
                            Generated on {{ now()->format('F d, Y \a\t H:i:s') }} |
                            This is a computer-generated document
                        </small>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card mt-4 d-print-none hide-print">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{{ route('payroll.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i>Back to Payroll List
                        </a>
                        <div class="d-flex gap-2">
                            @can('update', $payroll)
                            <a href="{{ route('payroll.edit', $payroll) }}" class="btn btn-primary">
                                <i class="bi bi-pencil me-1"></i>Edit Payroll
                            </a>
                            @endcan
                    <!--         <button type="button" class="btn btn-info" onclick="printPayslip('payslip-content')">
                                <i class="bi bi-printer me-1"></i>Print Payslip
                            </button> -->
                            <a href="{{ route('payroll.payslip', $payroll) }}" class="btn btn-success" target="_blank">
                                <i class="bi bi-file-earmark-pdf me-1"></i>Download PDF
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
/* Refined Payslip Styles */
.payslip-content {
    background: white !important;
    color: #333 !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.payslip-content * {
    color: inherit !important;
}

.payslip-content .text-dark {
    color: #333 !important;
}

.payslip-content .text-white {
    color: white !important;
}

.payslip-content .text-secondary {
    color: #6c757d !important;
}

.payslip-content .text-success {
    color: #198754 !important;
}

.payslip-content .text-danger {
    color: #dc3545 !important;
}

.payslip-content .text-primary {
    color: #0d6efd !important;
}

.payslip-content .text-info {
    color: #0dcaf0 !important;
}

.payslip-content .text-warning {
    color: #ffc107 !important;
}

.payslip-content .bg-white {
    background-color: white !important;
}

.payslip-content .bg-light {
    background-color: #f8f9fa !important;
}

.payslip-content .bg-dark {
    background-color: #333 !important;
}

.payslip-content .border-dark {
    border-color: #333 !important;
}

/* Enhanced Typography */
.payslip-content .font-monospace {
    font-family: 'Courier New', Consolas, monospace !important;
}

/* Table Styling Fixes */
.payslip-content .table {
    border-collapse: separate !important;
    border-spacing: 0 !important;
}

.payslip-content .table-bordered {
    border: 1px solid #dee2e6 !important;
}

.payslip-content .table-bordered th,
.payslip-content .table-bordered td {
    border: 1px solid #dee2e6 !important;
    border-width: 0 0 1px 0 !important;
}

.payslip-content .table-bordered thead th {
    border-bottom: 2px solid #dee2e6 !important;
}

.payslip-content .table-bordered tbody tr:last-child td {
    border-bottom: 0 !important;
}

/* Remove ugly dark borders from tables */
.payslip-content .border-dark.table,
.payslip-content .border-dark.table th,
.payslip-content .border-dark.table td {
    border-color: #dee2e6 !important;
}

/* Card Enhancements */
.payslip-content .card {
    transition: all 0.3s ease;
    border-radius: 0 !important;
}

.payslip-content .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

/* Table Enhancements */
.payslip-content .table-hover tbody tr:hover {
    background-color: rgba(0,0,0,0.02) !important;
}

.payslip-content .table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 12px 16px !important;
}

.payslip-content .table td {
    padding: 12px 16px !important;
    vertical-align: middle !important;
}

/* Clean table styling */
.payslip-content .table thead th {
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6 !important;
    color: #333 !important;
}

.payslip-content .table tbody tr {
    border-bottom: 1px solid #f0f0f0 !important;
}

.payslip-content .table tbody tr:hover {
    background-color: #f8f9fa !important;
}

.payslip-content .table tfoot th {
    background-color: #333 !important;
    color: white !important;
    border-top: 2px solid #333 !important;
    font-weight: bold !important;
}

/* Icon Styling */
.payslip-content .bi {
    vertical-align: middle;
}

/* Badge Styling */
.payslip-content .badge {
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* Border Enhancements */
.border-bottom-2 {
    border-bottom-width: 2px !important;
}

.border-2 {
    border-width: 2px !important;
}

.border-3 {
    border-width: 3px !important;
}

/* Remove all border radius for formal document appearance */
.payslip-content *,
.payslip-content *::before,
.payslip-content *::after {
    border-radius: 0 !important;
}

.payslip-content .badge {
    border-radius: 0 !important;
}

.payslip-content .btn {
    border-radius: 0 !important;
}

.payslip-content .rounded,
.payslip-content .rounded-1,
.payslip-content .rounded-2,
.payslip-content .rounded-3,
.payslip-content .rounded-circle,
.payslip-content .rounded-pill {
    border-radius: 0 !important;
}

@media print {
    .d-print-none, .hide-print {
        display: none !important;
    }

    .payslip-content {
        box-shadow: none !important;
        border: none !important;
        background: white !important;
        color: #333 !important;
    }

    .payslip-content * {
        color: inherit !important;
    }

    .card {
        border: 1px solid #333 !important;
        box-shadow: none !important;
        background: white !important;
    }

    .bg-dark, .bg-primary, .bg-success, .bg-danger, .bg-warning, .bg-info {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    body {
        background: white !important;
    }
}

.card-header {
    font-weight: 600;
}

.table th {
    font-weight: 600;
}

.border-end {
    border-right: 1px solid #333;
}

@media (max-width: 768px) {
    .border-end {
        border-right: none;
        border-bottom: 1px solid #333;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
// Enhanced print function for payslips
function printPayslip(elementId) {
    // Hide elements that shouldn't be printed
    const hideElements = document.querySelectorAll('.hide-print, .d-print-none');
    hideElements.forEach(el => el.style.display = 'none');

    // Get the element to print
    const element = document.getElementById(elementId);
    if (!element) {
        console.error('Element with ID ' + elementId + ' not found');
        return;
    }

    // Create a new window for printing
    const printWindow = window.open('', '_blank');

    // Get all stylesheets
    const stylesheets = Array.from(document.styleSheets);
    let styles = '';

    // Extract CSS rules
    stylesheets.forEach(stylesheet => {
        try {
            if (stylesheet.cssRules) {
                Array.from(stylesheet.cssRules).forEach(rule => {
                    styles += rule.cssText + '\n';
                });
            }
        } catch (e) {
            // Handle cross-origin stylesheets
            if (stylesheet.href) {
                styles += `@import url("${stylesheet.href}");\n`;
            }
        }
    });

    // Write the content to the new window
    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Payslip - {{ $payroll->employee->name ?? 'Employee' }}</title>
            <meta charset="utf-8">
            <style>
                ${styles}
                @media print {
                    body { margin: 0; padding: 15px; }
                    .hide-print, .d-print-none { display: none !important; }
                    .payslip-content { box-shadow: none !important; border: none !important; }
                    .card { border: 1px solid #dee2e6 !important; box-shadow: none !important; }
                    .bg-primary, .bg-success, .bg-danger, .bg-warning, .bg-info {
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                    }
                }
            </style>
        </head>
        <body>
            ${element.outerHTML}
        </body>
        </html>
    `);

    printWindow.document.close();

    // Wait for content to load then print
    printWindow.onload = function() {
        printWindow.focus();
        printWindow.print();
        printWindow.close();
    };

    // Show hidden elements again
    setTimeout(() => {
        hideElements.forEach(el => el.style.display = '');
    }, 1000);
}

// Override canvasPrint for payslip to use the better print function
function canvasPayslipPrint(elementId) {
    printPayslip(elementId);
}
</script>
@endpush
@endsection
