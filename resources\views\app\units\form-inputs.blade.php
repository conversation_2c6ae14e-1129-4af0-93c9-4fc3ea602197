@php $editing = isset($unit) @endphp

<div class="row">
    <x-inputs.group class="col-sm-6 mb-4">
        <x-inputs.text
            name="name"
            label="Name"
            :value="old('name', ($editing ? $unit->name : ''))"
            maxlength="255"
            placeholder="Name"
            required
        ></x-inputs.text>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6 mb-4">
        <x-inputs.number
            name="quantity"
            label="Quantity"
            step="0.01" 
            :value="old('quantity', ($editing ? $unit->quantity : '1'))"
            max="255"
            placeholder="Quantity"
        ></x-inputs.number>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6 mb-4">
        <x-inputs.number
            name="buying_price"
            label="Buying Price"
            step="0.01"
            :value="old('buying_price', ($editing ? $unit->buying_price : 0))"
            placeholder="Buying Price"
        ></x-inputs.number>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6 mb-4">
        <x-inputs.number
            name="selling_price"
            label="Selling Price"
            step="0.01" 
            :value="old('selling_price', ($editing ? $unit->selling_price : 0))"
            placeholder="Selling Price"
        ></x-inputs.number>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6 mb-4">
        <x-inputs.select name="product_id" label="Product">
            @php $selected = old('product_id', ($editing ? $unit->product_id : '')) @endphp
            <option disabled {{ empty($selected) ? 'selected' : '' }}>Please select the product</option>
            @foreach($products as $value => $label)
            <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }} >{{ $label }}</option>
            @endforeach
        </x-inputs.select>
    </x-inputs.group>

    <x-inputs.group class="col-sm-6 mb-4">
        <x-inputs.select name="status_id" label="Status">
            @php $selected = old('status_id', ($editing ? $unit->status_id : '')) @endphp
            <option disabled {{ empty($selected) ? 'selected' : '' }}>Please select the Status</option>
            @foreach($statuses as $value => $label)
            <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }} >{{ $label }}</option>
            @endforeach
        </x-inputs.select>
    </x-inputs.group>


    <x-inputs.group class="col-sm-12">
        <x-inputs.textarea name="description" label="Description" >{{ old('description', ($editing ? $unit->description : ''))
            }}</x-inputs.textarea
        >
    </x-inputs.group>

</div>
