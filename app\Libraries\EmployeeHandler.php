<?php

namespace App\Libraries;

use App\Models\Employee;
use App\Models\MonthlyPayroll;
use App\Models\PayrollItem;
use App\Models\DeductionContribution;
use App\Models\EmployeeDeductionContribution;
use App\Models\EmployeeLoan;
use Carbon\Carbon;

class EmployeeHandler
{
    /**
     * Generate payroll for all employees for a specific month and year.
     *
     * @param array $data
     * @return void
     */
    public function generatePayroll($data)
    {
        $employees = Employee::get();
        foreach ($employees as $employee) {
            $month = $data['month'];
            $year = $data['year'] ?? date('Y');

            $monthlyPayroll = MonthlyPayroll::where("employee_id", $employee->id)
                ->whereYear("date_from", $year)
                ->whereMonth("date_from", $month)
                ->first();

            $dateFrom = new Carbon($year . '-' . $month . '-01');
            $dateTo = (clone $dateFrom)->endOfMonth();

            $params = [
                "name" => $employee->name,
                "date_from" => $dateFrom,
                "date_to" => $dateTo,
                "basic_pay" => $employee->basic_pay,
                "gross_pay" => $employee->basic_pay, // Initialize gross pay
                "total_deductions" => 0,
                "total_allowances" => 0,
                "overtime_pay" => 0,
                "overtime_hours" => 0,
                "tax_amount" => 0,
                "status" => 'draft',
                "employee_id" => $employee->id,
                "created_by" => auth()->id(),
            ];

            if (!$monthlyPayroll) {
                $monthlyPayroll = MonthlyPayroll::create($params);
                $this->calculatePay($monthlyPayroll);
            }
        }
    }

    /**
     * Calculate pay for a monthly payroll record.
     *
     * @param \App\Models\MonthlyPayroll $monthlyPayroll
     * @return void
     */
    public function calculatePay($monthlyPayroll)
    {
        $employee = $monthlyPayroll->employee;

        if (!$employee) {
            return;
        }

        // Get employee deduction contributions
        $employeeDeductionContributions = EmployeeDeductionContribution::where('employee_id', $employee->id)
            ->where(function ($query) use ($monthlyPayroll) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '<=', $monthlyPayroll->date_from);
            })
            ->where(function ($query) use ($monthlyPayroll) {
                $query->where('start_date', '>=', $monthlyPayroll->date_from)
                    ->orWhereNull('start_date');
            })
            ->with('deductionContribution')
            ->get();

        // Delete existing payroll items (both deductions and loan deductions)
        $monthlyPayroll->payrollItems()->delete();

        // STEP 1: Calculate overtime FIRST (before any deductions)
        $year = $monthlyPayroll->date_from->year;
        $month = $monthlyPayroll->date_from->month;

        $overtimeHours = $employee->getOvertimeHoursForMonth($year, $month);
        $regularHours = $employee->getRegularHoursForMonth($year, $month);

        // Calculate overtime pay based on work hours configuration
        $workConfig = \App\Models\WorkHoursConfiguration::getActive();
        $overtimeRate = $workConfig ? $workConfig->overtime_rate : 1.5;

        // Calculate hourly rate from basic pay (assuming monthly pay for standard hours)
        $standardHoursPerMonth = $workConfig ? ($workConfig->standard_hours_per_day * 22) : 176; // 22 working days
        $hourlyRate = $employee->basic_pay / $standardHoursPerMonth;
        $overtimePay = $overtimeHours * $hourlyRate * $overtimeRate;

        // STEP 2: Process allowances/contributions first to calculate gross pay
        $totalDeductions = 0;
        $totalContributions = 0;

        // Create payroll items for allowances/contributions (not deductions yet)
        foreach ($employeeDeductionContributions as $employeeDeductionContribution) {
            $deductionContribution = $employeeDeductionContribution->deductionContribution;

            if (!$deductionContribution) {
                continue;
            }

            $amount = $this->calculateFormula($employee, $deductionContribution->formula);

            // Determine display name based on apply_mode
            $displayName = $deductionContribution->apply_mode === 'DEDUCTION'
                ? $deductionContribution->name
                : $deductionContribution->name . ' (Contribution/Allowance)';

            $params = [
                "name" => $displayName,
                "description" => $deductionContribution->name,
                "amount" => $amount,
                "type" => $deductionContribution->apply_mode,
                "formula" => $deductionContribution->formula,
                "approved_by" => auth()->id(),
                "deduction_contribution_id" => $deductionContribution->id,
                "monthly_payroll_id" => $monthlyPayroll->id,
                "is_calculatable" => $deductionContribution->is_calculatable,
                "created_by" => auth()->id(),
            ];

            $monthlyPayroll->payrollItems()->create($params);

            if($deductionContribution->is_calculatable){
                // Track totals
                if ($deductionContribution->apply_mode === 'DEDUCTION') {
                    $totalDeductions += $amount;
                } else {
                    $totalContributions += $amount;
                }
            }
        }

        // STEP 3: Calculate gross pay (basic pay + allowances + overtime pay)
        $grossPay = $employee->basic_pay + $totalContributions + $overtimePay;

        // STEP 4: Calculate PAYE tax on gross pay (before deductions)
        $taxAmount = $this->payeeTax($grossPay);

        // STEP 5: Process loan deductions AFTER gross pay and tax calculation
        $this->processLoanDeductions($employee, $monthlyPayroll, $totalDeductions, $grossPay);

        // STEP 6: Calculate net pay (gross pay - deductions - tax)
        $netPay = $grossPay - $totalDeductions - $taxAmount;

        // Ensure net pay is not negative
        $netPay = max(0, $netPay);

        // Update payroll with calculated values
        $monthlyPayroll->update([
            'gross_pay' => $grossPay,
            'total_deductions' => $totalDeductions,
            'total_allowances' => $totalContributions,
            'overtime_pay' => $overtimePay,
            'overtime_hours' => $overtimeHours,
            'tax_amount' => $taxAmount,
            'net_pay' => $netPay,
        ]);

        // Log the calculation for debugging with proper order
        \Log::info("Payroll calculated for employee {$employee->name}: Basic: {$employee->basic_pay}, Overtime: {$overtimePay}, Allowances: {$totalContributions}, Gross: {$grossPay}, Tax: {$taxAmount}, Deductions: {$totalDeductions}, Net: {$netPay}");
    }

    /**
     * Calculate formula for deduction/contribution.
     *
     * @param \App\Models\Employee $employee
     * @param string $formula
     * @return float
     */
    public function calculateFormula($employee, $formula)
    {
        if (empty($formula)) {
            return 0;
        }

        $formula = str_replace("BASIC_PAY", $employee->basic_pay, $formula);

        try {
            $amount = eval("return $formula;");
            return $amount ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Calculate PAYE tax based on gross salary using dynamic tax brackets.
     *
     * @param float $grossSalary
     * @return float
     */
    public function payeeTax($grossSalary)
    {
        // Define the employee's gross salary
		// $grossSalary = 3000000; // Adjust this to the actual gross salary
		// // Define the tax brackets and rates
		// $taxBrackets = [
		//     100000 => 0.0,   // 0% tax on the first K100,000
		//     350000 => 0.25,  // 25% tax on the next K350,000
		//     2050000 => 0.30, // 30% tax on the next K2,050,000
		//     2500000 => 0.35, // 30% tax on the next K2,500,000
		// ];

		// // Initialize the PAYE amount
		// $paye = 0;

		// // Calculate PAYE based on the tax brackets and rates
		// foreach ($taxBrackets as $bracket => $rate) {
		//     if ($grossSalary <= 0) {
		//         break; // No more tax to calculate
		//     }

		//     if ($grossSalary > $bracket) {
		//         $taxableAmount = $bracket;
		//     } else {
		//         $taxableAmount = $grossSalary;
		//     }

		//     $taxAmount = $taxableAmount * $rate;
		//     $paye += $taxAmount;
		//     $grossSalary -= $taxableAmount;
		// }

        // Use the TaxBracket model to calculate tax dynamically
        return \App\Models\TaxBracket::calculateTax($grossSalary);
    }

    /**
     * Calculate VAT or other additional calculations.
     *
     * @param \App\Models\Employee $employee
     * @param string $formula
     * @return string
     */
    public function calculateVat($employee, $formula)
    {
        // This method can be extended for additional calculations
        return $formula;
    }

    /**
     * Fix time-related calculations for employees.
     *
     * @param \App\Models\Employee|null $employee
     * @return void
     */
    public function fixTime($employee = null)
    {
        if ($employee) {
            // Fix days for specific employee if the method exists
            if (method_exists($employee, 'fixDays')) {
                $employee->fixDays();
            }
        } else {
            $employees = Employee::get();
            foreach ($employees as $emp) {
                if (method_exists($emp, 'fixDays')) {
                    $emp->fixDays();
                }
            }
        }
    }

    /**
     * Email managers about employee-related notifications.
     *
     * @param array $data
     * @return void
     */
    public function emailManagers($data = [])
    {
        // This method can be implemented for sending notifications to managers
        // For now, it's a placeholder
        session($data);
    }

    /**
     * Process loan deductions for an employee's monthly payroll.
     *
     * @param \App\Models\Employee $employee
     * @param \App\Models\MonthlyPayroll $monthlyPayroll
     * @param float &$totalDeductions
     * @param float $grossPay
     * @return void
     */
    private function processLoanDeductions($employee, $monthlyPayroll, &$totalDeductions, $grossPay = 0)
    {
        // Get active loans for the employee
        $activeLoans = EmployeeLoan::where('employee_id', $employee->id)
            ->where('status', 'active')
            ->where('first_deduction_date', '<=', $monthlyPayroll->date_from)
            ->get();

        foreach ($activeLoans as $loan) {
            // Check if this loan should have a deduction this month
            if ($this->shouldProcessLoanDeduction($loan, $monthlyPayroll)) {
                // Calculate loan payment breakdown (principal + interest)
                $paymentBreakdown = $this->calculateLoanPaymentBreakdown($loan);
                $principalAmount = $paymentBreakdown['principal'];
                $interestAmount = $paymentBreakdown['interest'];
                $totalPayment = $principalAmount + $interestAmount;

                // Validate that deduction doesn't exceed reasonable limits
                $maxDeductionPercentage = 0.5; // Maximum 50% of gross pay
                $maxAllowableDeduction = $grossPay * $maxDeductionPercentage;

                if ($totalPayment > $maxAllowableDeduction && $grossPay > 0) {
                    \Log::warning("Loan payment amount ({$totalPayment}) exceeds 50% of gross pay ({$grossPay}) for employee {$employee->name}. Loan: {$loan->loan_reference}");
                    // You might want to adjust the deduction or skip it based on business rules
                    // For now, we'll proceed but log the warning
                }

                // Create payroll item for loan principal deduction
                if ($principalAmount > 0) {
                    $principalParams = [
                        "name" => "Loan Principal - " . $loan->loan_reference,
                        "description" => "Monthly loan principal payment for loan " . $loan->loan_reference . " (Balance: " . number_format($loan->remaining_balance, 2) . ")",
                        "amount" => $principalAmount,
                        "type" => "DEDUCTION",
                        "formula" => null,
                        "approved_by" => auth()->id(),
                        "deduction_contribution_id" => null,
                        "monthly_payroll_id" => $monthlyPayroll->id,
                        "is_calculatable" => true,
                        "created_by" => auth()->id(),
                        "loan_id" => $loan->id,
                    ];

                    $monthlyPayroll->payrollItems()->create($principalParams);
                    $totalDeductions += $principalAmount;
                }

                // Create separate payroll item for loan interest deduction
                if ($interestAmount > 0) {
                    $interestParams = [
                        "name" => "Loan Interest - " . $loan->loan_reference,
                        "description" => "Monthly interest charge for loan " . $loan->loan_reference . " (" . $loan->interest_rate . "% " . $loan->interest_type . " interest)",
                        "amount" => $interestAmount,
                        "type" => "DEDUCTION",
                        "formula" => null,
                        "approved_by" => auth()->id(),
                        "deduction_contribution_id" => null,
                        "monthly_payroll_id" => $monthlyPayroll->id,
                        "is_calculatable" => true,
                        "created_by" => auth()->id(),
                        "loan_id" => $loan->id,
                    ];

                    $monthlyPayroll->payrollItems()->create($interestParams);
                    $totalDeductions += $interestAmount;
                }

                // Process the loan payment (only principal reduces the balance)
                $loan->processPayment($principalAmount);

                // Track interest payment separately
                if ($interestAmount > 0) {
                    $loan->total_interest_paid += $interestAmount;
                    $loan->save();
                }

                // Enhanced logging
                \Log::info("Loan payment processed for employee {$employee->name}: Loan {$loan->loan_reference}, Principal: {$principalAmount}, Interest: {$interestAmount}, Total: {$totalPayment}, Remaining Balance: {$loan->remaining_balance}, Total Interest Paid: {$loan->total_interest_paid}, Status: {$loan->status}");
            }
        }
    }

    /**
     * Check if a loan should have a deduction processed this month.
     *
     * @param \App\Models\EmployeeLoan $loan
     * @param \App\Models\MonthlyPayroll $monthlyPayroll
     * @return bool
     */
    private function shouldProcessLoanDeduction($loan, $monthlyPayroll)
    {
        // Check if loan is still active and has remaining balance
        if ($loan->status !== 'active' || $loan->remaining_balance <= 0) {
            return false;
        }

        // Check if first deduction date has passed
        if ($loan->first_deduction_date > $monthlyPayroll->date_from) {
            return false;
        }

        // Check if we haven't already processed this month's deduction
        $existingDeduction = PayrollItem::where('monthly_payroll_id', $monthlyPayroll->id)
            ->where('loan_id', $loan->id)
            ->first();

        return !$existingDeduction;
    }

    /**
     * Calculate the loan payment breakdown (principal + interest) for this month.
     *
     * UPDATED: Installment amount is now calculated from loan_amount only (principal).
     * Interest is calculated separately and not included in the installment.
     *
     * @param \App\Models\EmployeeLoan $loan
     * @return array
     */
    private function calculateLoanPaymentBreakdown($loan)
    {
        // Calculate monthly interest as a separate deduction
        $interestAmount = $this->calculateMonthlyInterest($loan);

        // Calculate principal payment from loan_amount only (not including interest)
        // The installment_amount should represent the principal payment per month
        $principalAmount = $loan->installment_amount;

        // Ensure principal doesn't exceed remaining balance
        if ($principalAmount > $loan->remaining_balance) {
            $principalAmount = $loan->remaining_balance;
        }

        // Ensure principal is not negative
        $principalAmount = max(0, $principalAmount);

        return [
            'principal' => round($principalAmount, 2),
            'interest' => round($interestAmount, 2),
            'total' => round($principalAmount + $interestAmount, 2)
        ];
    }

    /**
     * Calculate monthly interest for a loan.
     *
     * @param \App\Models\EmployeeLoan $loan
     * @return float
     */
    private function calculateMonthlyInterest($loan)
    {
        // If no interest rate is set, return 0
        if (!$loan->interest_rate || $loan->interest_rate <= 0) {
            return 0;
        }

        $monthlyInterestRate = $loan->interest_rate / 100 / 12; // Convert annual percentage to monthly decimal

        if ($loan->interest_type === 'simple') {
            // Simple interest: calculated on original loan amount
            return $loan->loan_amount * $monthlyInterestRate;
        } else {
            // Compound interest: calculated on remaining balance
            return $loan->remaining_balance * $monthlyInterestRate;
        }
    }

    /**
     * Calculate the loan deduction amount for this month (legacy method for backward compatibility).
     *
     * @param \App\Models\EmployeeLoan $loan
     * @return float
     */
    private function calculateLoanDeductionAmount($loan)
    {
        $breakdown = $this->calculateLoanPaymentBreakdown($loan);
        return $breakdown['total'];
    }
}
