@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">
                <a href="{{ route('production-orders.index') }}" class="mr-4"
                    ><i class="icon ion-md-arrow-back"></i
                ></a>
                Production Order Details
            </h4>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-4">
                        <h5>Order Number</h5>
                        <span>{{ $productionOrder->order_number ?? '-' }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Product</h5>
                        <span>{{ optional($productionOrder->product)->name ?? '-' }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Quantity</h5>
                        <span>{{ $productionOrder->quantity ?? '-' }} {{ $productionOrder->unit_of_measure ?? '' }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Status</h5>
                        <span class="badge {{ 
                            $productionOrder->status == 'draft' ? 'bg-secondary' : 
                            ($productionOrder->status == 'planned' ? 'bg-info' : 
                            ($productionOrder->status == 'in_progress' ? 'bg-primary' : 
                            ($productionOrder->status == 'completed' ? 'bg-success' : 'bg-danger'))) 
                        }}">
                            {{ ucfirst(str_replace('_', ' ', $productionOrder->status ?? '-')) }}
                        </span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-4">
                        <h5>Planned Start Date</h5>
                        <span>{{ $productionOrder->planned_start_date ? $productionOrder->planned_start_date->format('Y-m-d') : '-' }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Planned End Date</h5>
                        <span>{{ $productionOrder->planned_end_date ? $productionOrder->planned_end_date->format('Y-m-d') : '-' }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Actual Start Date</h5>
                        <span>{{ $productionOrder->actual_start_date ? $productionOrder->actual_start_date->format('Y-m-d') : '-' }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Actual End Date</h5>
                        <span>{{ $productionOrder->actual_end_date ? $productionOrder->actual_end_date->format('Y-m-d') : '-' }}</span>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-4">
                        <h5>Planned Cost</h5>
                        <span>{{ number_format($productionOrder->planned_cost, 2) }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Actual Cost</h5>
                        <span>{{ number_format($productionOrder->actual_cost, 2) }}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-4">
                        <h5>Cost Variance</h5>
                        <span>{{ number_format($productionOrder->cost_variance, 2) }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Branch</h5>
                        <span>{{ optional($productionOrder->branch)->name ?? '-' }}</span>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <h5>Description</h5>
                <span>{{ $productionOrder->description ?? '-' }}</span>
            </div>

            <hr>

            <h5>Production Order Items</h5>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Type</th>
                            <th>Planned Qty</th>
                            <th>Actual Qty</th>
                            <th>Variance</th>
                            <th>Planned Cost</th>
                            <th>Actual Cost</th>
                            <th>Variance</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($productionOrderItems ?? [] as $item)
                        <tr>
                            <td>{{ optional($item->product)->name ?? '-' }}</td>
                            <td>{{ ucfirst($item->item_type) }}</td>
                            <td>{{ $item->planned_quantity }} {{ $item->unit_of_measure }}</td>
                            <td>{{ $item->actual_quantity }} {{ $item->unit_of_measure }}</td>
                            <td>{{ $item->quantity_variance }}</td>
                            <td>{{ number_format($item->planned_cost, 2) }}</td>
                            <td>{{ number_format($item->actual_cost, 2) }}</td>
                            <td>{{ number_format($item->cost_variance, 2) }}</td>
                            <td>
                                <span class="badge {{ 
                                    $item->status == 'pending' ? 'bg-secondary' : 
                                    ($item->status == 'issued' ? 'bg-primary' : 'bg-success') 
                                }}">
                                    {{ ucfirst($item->status) }}
                                </span>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="9" class="text-center">No items found</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                <a href="{{ route('production-orders.index') }}" class="btn btn-light">
                    <i class="icon ion-md-return-left"></i>
                    @lang('crud.common.back')
                </a>

                @if($productionOrder->status == 'in_progress')
                <a href="{{ route('production-orders.edit-items', $productionOrder) }}" class="btn btn-primary">
                    Update Items
                </a>
                
                <a href="{{ route('production-orders.complete', $productionOrder) }}" class="btn btn-success"
                   onclick="return confirm('Are you sure you want to complete this production order?')">
                    Complete Production
                </a>
                @endif

                @can('update', $productionOrder)
                <a href="{{ route('production-orders.edit', $productionOrder) }}" class="btn btn-primary">
                    <i class="icon ion-md-create"></i> @lang('crud.common.edit')
                </a>
                @endcan
            </div>
        </div>
    </div>
</div>
@endsection
