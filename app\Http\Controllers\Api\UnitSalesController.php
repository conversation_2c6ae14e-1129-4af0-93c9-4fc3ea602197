<?php

namespace App\Http\Controllers\Api;

use App\Models\Unit;
use Illuminate\Http\Request;
use App\Http\Resources\SaleResource;
use App\Http\Controllers\Controller;
use App\Http\Resources\SaleCollection;

class UnitSalesController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Unit $unit
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, Unit $unit)
    {
        $this->authorize('view', $unit);

        $search = $request->get('search', '');

        $sales = $unit
            ->sales()
            ->search($search)
            ->latest()
            ->paginate();

        return new SaleCollection($sales);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Unit $unit
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, Unit $unit)
    {
        $this->authorize('create', Sale::class);

        $validated = $request->validate([
            'product_name' => ['required', 'max:255', 'string'],
            'product_id' => ['required', 'exists:products,id'],
            'unit_name' => ['nullable', 'max:255', 'string'],
            'price' => ['nullable', 'numeric'],
            'discount' => ['nullable', 'numeric'],
            'quantity' => ['nullable', 'numeric'],
        ]);

        $sale = $unit->sales()->create($validated);

        return new SaleResource($sale);
    }
}
