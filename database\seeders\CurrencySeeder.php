<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Currency;
use App\Models\ExchangeRate;
use Carbon\Carbon;

class CurrencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create default currencies
        $currencies = [
            [
                'name' => 'US Dollar',
                'code' => 'USD',
                'symbol' => '$',
                'decimal_places' => 2,
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Malawian Kwacha',
                'code' => 'MWK',
                'symbol' => 'K',
                'decimal_places' => 2,
                'is_default' => true,
                'is_active' => true,
            ],
            [
                'name' => 'Euro',
                'code' => 'EUR',
                'symbol' => '€',
                'decimal_places' => 2,
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'British Pound',
                'code' => 'GBP',
                'symbol' => '£',
                'decimal_places' => 2,
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Japanese Yen',
                'code' => 'JPY',
                'symbol' => '¥',
                'decimal_places' => 0,
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Canadian Dollar',
                'code' => 'CAD',
                'symbol' => 'C$',
                'decimal_places' => 2,
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Australian Dollar',
                'code' => 'AUD',
                'symbol' => 'A$',
                'decimal_places' => 2,
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Swiss Franc',
                'code' => 'CHF',
                'symbol' => 'Fr',
                'decimal_places' => 2,
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Chinese Yuan',
                'code' => 'CNY',
                'symbol' => '¥',
                'decimal_places' => 2,
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Indian Rupee',
                'code' => 'INR',
                'symbol' => '₹',
                'decimal_places' => 2,
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Brazilian Real',
                'code' => 'BRL',
                'symbol' => 'R$',
                'decimal_places' => 2,
                'is_default' => false,
                'is_active' => true,
            ],
        ];

        foreach ($currencies as $currency) {
            Currency::updateOrCreate(
                ['code' => $currency['code']],
                $currency
            );
        }

        // Create default exchange rates
        $this->createExchangeRates();
    }

    /**
     * Create default exchange rates.
     *
     * @return void
     */
    private function createExchangeRates()
    {
        $baseCurrency = Currency::where('is_default', true)->first();
        
        if (!$baseCurrency) {
            return;
        }
        
        $today = Carbon::today();
        
        $exchangeRates = [
            'EUR' => 0.85,
            'GBP' => 0.75,
            'JPY' => 110.0,
            'CAD' => 1.25,
            'AUD' => 1.35,
            'CHF' => 0.92,
            'CNY' => 6.45,
            'INR' => 74.5,
            'BRL' => 5.20,
        ];
        
        foreach ($exchangeRates as $code => $rate) {
            $currency = Currency::where('code', $code)->first();
            
            if ($currency) {
                ExchangeRate::updateOrCreate(
                    [
                        'from_currency_id' => $baseCurrency->id,
                        'to_currency_id' => $currency->id,
                        'rate_date' => $today,
                    ],
                    [
                        'exchange_rate' => $rate,
                        'is_active' => true,
                    ]
                );
                
                // Create inverse rate
                ExchangeRate::updateOrCreate(
                    [
                        'from_currency_id' => $currency->id,
                        'to_currency_id' => $baseCurrency->id,
                        'rate_date' => $today,
                    ],
                    [
                        'exchange_rate' => 1 / $rate,
                        'is_active' => true,
                    ]
                );
            }
        }
    }
}
