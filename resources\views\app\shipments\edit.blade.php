@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-sm mb-2 mb-sm-0">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-no-gutter">
                        <li class="breadcrumb-item">
                            <a class="breadcrumb-link" href="{{ route('shipments.index') }}">Shipments</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a class="breadcrumb-link" href="{{ route('shipments.show', $shipment) }}">Shipment #{{ $shipment->shipment_id }}</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Edit</li>
                    </ol>
                </nav>
                <h1 class="page-header-title">
                    <i class="bi-pencil me-2"></i>
                    Edit Shipment #{{ $shipment->shipment_id }}
                </h1>
            </div>
            <div class="col-sm-auto">
                <div class="d-flex gap-2">
                    <a href="{{ route('shipments.show', $shipment) }}" class="btn btn-outline-secondary">
                        <i class="bi-arrow-left me-1"></i>
                        Back to Shipment
                    </a>
                    <a href="{{ route('shipments.index') }}" class="btn btn-outline-secondary">
                        <i class="bi-list me-1"></i>
                        All Shipments
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- End Page Header -->

    <x-form
        method="PUT"
        action="{{ route('shipments.update', $shipment) }}"
        class="mt-4"
    >
        @include('app.shipments.form-inputs')
    </x-form>

</div>
@endsection
