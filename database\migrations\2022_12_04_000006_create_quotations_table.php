<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('quotations', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->date('date_from')->nullable();
            $table->date('date_to')->nullable();
            $table->datetime('closed_at')->nullable();
            $table
                ->decimal('vat', 12, 2)
                ->default(0)
                ->nullable();
            $table->text('description')->nullable();
            $table
                ->decimal('sub_total', 12, 2)
                ->default(0)
                ->nullable();   
            $table
                ->decimal('amount_total', 12, 2)
                ->default(0)
                ->nullable();
            $table
                ->decimal('amount_paid', 12, 2)
                ->default(0)
                ->nullable();
            $table
                ->decimal('discount', 12, 2)
                ->default(0)
                ->nullable();
            $table->text('customer_name')->nullable();
            $table->string('reference_no')->nullable();
            $table->unsignedBigInteger('warehouse_id')->nullable();
            $table->unsignedBigInteger('branch_id')->nullable();
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->unsignedBigInteger('order_id')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->unsignedBigInteger('status_id')->default(10)->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unsignedBigInteger('business_type_id')->nullable();
            $table->unsignedBigInteger('currency_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('quotations');
    }
};
