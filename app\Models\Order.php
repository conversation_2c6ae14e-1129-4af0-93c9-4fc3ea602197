<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;
use App\Traits\BranchTrait;
use App\Traits\WarehouseTrait;

class Order extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;
    use WarehouseTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'date_from',
        'date_to',
        'vat',
        'description',
        'sub_total',
        'amount_total',
        'supplier_id',
        'supplier_name',
        'amount_paid',
        'branch_id',
        'discount',
        'approved_by',
        'status_id',
        'created_by',
        'updated_by',
        'business_type_id',
        'expires_at',
        'reference_no',
        'currency_id',
        'is_adjustment',
        'original_order_id',
        'adjustment_type',
        'adjustment_reason',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
        'date_from' => 'datetime:Y-m-d',
        'date_to' => 'datetime:Y-m-d',
        'is_adjustment' => 'boolean',
    ];

    protected $appends = ['order_id', 'prepared_by'];

    public function getPreparedByAttribute(){
        return $this->createdBy->name ?? '';
    }

    public function getOrderIdAttribute(){
        return _pad( $this->id, 5, '0');
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }


    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }


    public function payments()
    {
        return $this->morphMany(Payment::class, 'paymentable');
    }
    
    public function stocks()
    {
        return $this->morphMany(Stock::class, 'stockable');
    }

    public function originalOrder()
    {
        return $this->belongsTo(Order::class, 'original_order_id');
    }

    public function adjustments()
    {
        return $this->hasMany(Order::class, 'original_order_id');
    }

    public function items()
    {
        return $this->stocks();
    }

    // Helper methods for adjustments
    public function isAdjustment()
    {
        return $this->is_adjustment === true;
    }

    public function isReturn()
    {
        return $this->isAdjustment() && $this->adjustment_type === 'return';
    }

    public function isRefund()
    {
        return $this->isAdjustment() && $this->adjustment_type === 'refund';
    }

    public function isStockAdjustment()
    {
        return $this->isAdjustment() && $this->adjustment_type === 'stock_adjustment';
    }

    public function getAdjustmentTypeDisplayAttribute()
    {
        if (!$this->isAdjustment()) {
            return 'Purchase Order';
        }

        return match($this->adjustment_type) {
            'return' => 'Return Order',
            'refund' => 'Refund Order',
            'stock_adjustment' => 'Stock Adjustment',
            default => 'Adjustment Order'
        };
    }

    public function getOrderTypeAttribute()
    {
        return $this->isAdjustment() ? 'adjustment' : 'order';
    }

    public function getDisplayOrderIdAttribute()
    {
        $prefix = $this->isAdjustment() ? 'ADJ' : 'PO';
        return $prefix . '-' . _pad($this->id, 5, '0');
    }

    public function getBalanceAttribute()
    {
        return $this->amount_total - $this->amount_paid;
    }
}
