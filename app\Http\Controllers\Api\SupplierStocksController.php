<?php

namespace App\Http\Controllers\Api;

use App\Models\Supplier;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\StockResource;
use App\Http\Resources\StockCollection;

class SupplierStocksController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Supplier $supplier
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, Supplier $supplier)
    {
        $this->authorize('view', $supplier);

        $search = $request->get('search', '');

        $stocks = $supplier
            ->stocks()
            ->search($search)
            ->latest()
            ->paginate();

        return new StockCollection($stocks);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Supplier $supplier
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, Supplier $supplier)
    {
        $this->authorize('create', Stock::class);

        $validated = $request->validate([
            'product_id' => ['nullable', 'exists:products,id'],
            'quantity' => ['nullable', 'numeric'],
            'balance' => ['nullable', 'numeric'],
            'stock_id' => ['nullable', 'exists:stocks,id'],
            'approved_by' => ['nullable', 'exists:users,id'],
            'description' => ['nullable', 'max:255', 'string'],
        ]);

        $stock = $supplier->stocks()->create($validated);

        return new StockResource($stock);
    }
}
