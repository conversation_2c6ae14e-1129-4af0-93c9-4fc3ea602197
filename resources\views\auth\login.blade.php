@extends('layouts.guest')

@section('content')
<style>
    .login-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .login-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
        background-size: cover;
        opacity: 0.3;
    }

    .login-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        position: relative;
        z-index: 2;
    }

    .login-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 35px 70px rgba(0, 0, 0, 0.2);
    }

    .logo-container {
        position: relative;
        z-index: 3;
        margin-bottom: 2rem;
    }

    .logo-img {
        transition: transform 0.3s ease;
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    }

    .logo-img:hover {
        transform: scale(1.05);
    }

    .form-control-modern {
        border: 2px solid #e1e5e9;
        border-radius: 12px;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control-modern:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
        background: rgba(255, 255, 255, 1);
        transform: translateY(-1px);
    }

    .form-control-modern:hover {
        border-color: #667eea;
        background: rgba(255, 255, 255, 1);
    }

    .btn-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 0.875rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    .form-label-modern {
        font-weight: 600;
        color: #4a5568;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .alert-modern {
        border: none;
        border-radius: 12px;
        background: rgba(248, 215, 218, 0.9);
        border-left: 4px solid #dc3545;
        backdrop-filter: blur(10px);
    }

    .input-group-modern .input-group-text {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid #e1e5e9;
        border-left: none;
        border-radius: 0 12px 12px 0;
        transition: all 0.3s ease;
        padding: 0.875rem 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: calc(1.5em + 1.75rem + 4px);
    }

    .input-group-modern .form-control-modern {
        border-radius: 12px 0 0 12px;
        border-right: none;
    }

    .input-group-modern .form-control-modern:focus + .input-group-text {
        border-color: #667eea;
        background: rgba(255, 255, 255, 1);
    }

    .input-group-modern .input-group-text i {
        font-size: 1rem;
        color: #6b7280;
        transition: color 0.3s ease;
    }

    .input-group-modern .form-control-modern:focus + .input-group-text i {
        color: #667eea;
    }

    .remember-checkbox {
        transform: scale(1.2);
        accent-color: #667eea;
    }

    .footer-brands {
        opacity: 0.6;
        transition: opacity 0.3s ease;
    }

    .footer-brands:hover {
        opacity: 0.8;
    }

    @media (max-width: 768px) {
        .login-card {
            margin: 1rem;
            border-radius: 16px;
        }

        .logo-img {
            width: 6rem !important;
        }
    }

    /* Dark theme compatibility */
    [data-hs-appearance="dark"] .login-card {
        background: rgba(30, 41, 59, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    [data-hs-appearance="dark"] .form-control-modern {
        background: rgba(30, 41, 59, 0.9);
        border-color: #4a5568;
        color: #e2e8f0;
    }

    [data-hs-appearance="dark"] .form-control-modern:focus {
        background: rgba(30, 41, 59, 1);
        border-color: #667eea;
    }

    [data-hs-appearance="dark"] .form-label-modern {
        color: #e2e8f0;
    }

    [data-hs-appearance="dark"] .input-group-modern .input-group-text {
        background: rgba(30, 41, 59, 0.9);
        border-color: #4a5568;
        color: #e2e8f0;
    }

    [data-hs-appearance="dark"] .input-group-modern .input-group-text i {
        color: #9ca3af;
    }

    [data-hs-appearance="dark"] .input-group-modern .form-control-modern:focus + .input-group-text i {
        color: #667eea;
    }
</style>

<div class="login-container">
    <div class="container py-5">
        <div class="mx-auto" style="max-width: 28rem;">
            <!-- Card -->
            <div class="login-card p-4 p-md-5">
                <div class="pb-2 text-center">
                    <a href="/" class="d-inline-block">
                        <img src="{{ asset('img/logo.webp') }}" alt="Sales Pro Logo" style="width: 8rem;">
                    </a>
                </div>
                <!-- Form -->
                <form class="js-validate needs-validation" novalidate method="POST" action="{{ route('login') }}">
                    @csrf

                    <div class="text-center mb-4">
                        <h1 class="h2 mb-3" style="color: #2d3748; font-weight: 700;">Welcome Back</h1>
                        <p class="text-muted mb-0">Sign in to your account to continue</p>
                    </div>

                    <!-- Alert for errors -->
                    @if(session('error'))
                    <div class="alert alert-modern mb-4">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="bi-exclamation-triangle-fill text-danger"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <strong>Error:</strong> {{ session('error') }}
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Email/Username Field -->
                    <div class="mb-4">
                        <label class="form-label-modern" for="signinEmail">Email Address</label>
                        <input type="text"
                               class="form-control form-control-modern @error('email') is-invalid @enderror"
                               name="email"
                               id="signinEmail"
                               tabindex="1"
                               placeholder="Enter your email address"
                               value="{{ old('email') }}"
                               required>
                        @error('email')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Password Field -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label-modern mb-0" for="signupSrPassword">Password</label>
                            <a class="text-decoration-none"
                               href="{{ route('password.request') }}"
                               style="color: #667eea; font-size: 0.875rem; font-weight: 500;">
                                Forgot Password?
                            </a>
                        </div>

                        <div class="input-group input-group-modern">
                            <input type="password"
                                   class="js-toggle-password form-control form-control-modern @error('password') is-invalid @enderror"
                                   name="password"
                                   id="signupSrPassword"
                                   placeholder="Enter your password"
                                   required
                                   minlength="8"
                                   tabindex="2"
                                   data-hs-toggle-password-options='{
                                       "target": "#changePassTarget",
                                       "defaultClass": "bi-eye-slash",
                                       "showClass": "bi-eye",
                                       "classChangeTarget": "#changePassIcon"
                                   }'>
                            <span class="input-group-text p-1" id="changePassTarget" style="cursor: pointer; min-height: inherit">
                                <i id="changePassIcon" class="bi-eye-slash"></i>
                            </span>
                        </div>

                        @error('password')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Remember Me -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div class="form-check">
                            <input class="form-check-input remember-checkbox"
                                   type="checkbox"
                                   name="remember"
                                   id="remember"
                                   {{ old('remember') ? 'checked' : '' }}>
                            <label class="form-check-label" for="remember" style="color: #4a5568; font-weight: 500;">
                                Remember me
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid mb-4">
                        <button type="submit" class="btn btn-modern text-white">
                            <i class="bi-box-arrow-in-right me-2"></i>
                            Sign In
                        </button>
                    </div>

                    <!-- Register Link -->
                    <div class="text-center">
                        <p class="mb-0" style="color: #6b7280;">
                            Don't have an account?
                            <a href="/register"
                               class="text-decoration-none fw-semibold"
                               style="color: #667eea;">
                                Create one here
                            </a>
                        </p>
                    </div>
                </form>
                <!-- End Form -->
            </div>
        </div>
        <!-- End Card -->

        <!-- Footer -->
        <div class="text-center mt-4">
            <div class="footer-brands">
                <small class="text-white-50 mb-3 d-block" style="font-weight: 500; letter-spacing: 0.5px;">
                    <i class="bi-shield-check me-1"></i>
                    Secure & Trusted Platform
                </small>
                <div class="d-flex justify-content-center align-items-center gap-4 flex-wrap">
                    <div class="d-flex align-items-center text-white-50">
                        <i class="bi-lock-fill me-1"></i>
                        <small>SSL Encrypted</small>
                    </div>
                    <div class="d-flex align-items-center text-white-50">
                        <i class="bi-cloud-check-fill me-1"></i>
                        <small>Cloud Secure</small>
                    </div>
                    <div class="d-flex align-items-center text-white-50">
                        <i class="bi-speedometer2 me-1"></i>
                        <small>Fast & Reliable</small>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Footer -->
    </div>
</div>
<!-- End Content -->

<script>
    // Add some interactive effects
    document.addEventListener('DOMContentLoaded', function() {
        // Add floating animation to the card
        const card = document.querySelector('.login-card');
        if (card) {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        }

        // Add focus effects to form inputs
        const inputs = document.querySelectorAll('.form-control-modern');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });

        // Add ripple effect to button
        const button = document.querySelector('.btn-modern');
        if (button) {
            button.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'rgba(255, 255, 255, 0.3)';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'ripple 0.6s linear';
                ripple.style.pointerEvents = 'none';

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        }
    });
</script>

<style>
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
</style>

@endsection