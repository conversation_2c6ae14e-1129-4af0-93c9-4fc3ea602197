<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

use App\Traits\CreatedUpdatedTrait;

class DeductionContribution extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;

    protected $connection = "tenant";
    
    protected $fillable = [
        'name',
        'code',
        'formula',
        'apply_mode',
        'apply_at',
        'description',
        'is_calculatable',
        'created_by',
        'updated_by',
        'status_id',
    ];

    protected $searchableFields = ['*'];

    protected $table = 'deduction_contributions';

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function employeeDeductionContributions()
    {
        return $this->hasMany(EmployeeDeductionContribution::class);
    }



    
}
