<?php
namespace App\Libraries;

use App\Models\User;
use App\Models\Tenant;
use Carbon\Carbon;
/**
 * 
 */
class UserHandler
{
    public function createUser($data) {
        $current = Tenant::current();
        $data['domain'] = $data['domain'] ?? str()->random();
        $data['current_tenant'] = $current->id ?? null;
        $tenant = $this->newTenant($data);
        cache([_authId() => $tenant->domain]);
        $tenant->makeCurrent();
        return $tenant->createAdmin($data);
    }
    
    public function createTenant($data) {
        $current = Tenant::current();
        $data['domain'] = $data['domain'] ?? str()->random();
        $data['current_tenant'] = $current->id;
        $tenant = $this->newTenant($data);
    }

    public function newTenant($data) {
        $tenant = Tenant::where("email", $data["email"] ?? "")->first();
        if($tenant) {
            $tenant->update($data);
            return $tenant;
        }
        return Tenant::create($data);
    }
}