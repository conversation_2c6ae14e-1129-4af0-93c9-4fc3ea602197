<?php

namespace App\Http\Controllers\Api;

use App\Models\Status;
use Illuminate\Http\Request;
use App\Http\Resources\UnitResource;
use App\Http\Controllers\Controller;
use App\Http\Resources\UnitCollection;

class StatusUnitsController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Status $status
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, Status $status)
    {
        $this->authorize('view', $status);

        $search = $request->get('search', '');

        $units = $status
            ->units()
            ->search($search)
            ->latest()
            ->paginate();

        return new UnitCollection($units);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Status $status
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, Status $status)
    {
        $this->authorize('create', Unit::class);

        $validated = $request->validate([
            'name' => ['required', 'max:255', 'string'],
            'quantity' => ['nullable', 'numeric'],
        ]);

        $unit = $status->units()->create($validated);

        return new UnitResource($unit);
    }
}
