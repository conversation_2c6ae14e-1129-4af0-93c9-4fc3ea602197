<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
        \App\Models\AccountType::class => \App\Policies\AccountTypePolicy::class,
        \App\Models\AccountCategory::class => \App\Policies\AccountCategoryPolicy::class,
        \App\Models\FiscalYear::class => \App\Policies\FiscalYearPolicy::class,
        \App\Models\FiscalPeriod::class => \App\Policies\FiscalPeriodPolicy::class,
        \App\Models\JournalEntry::class => \App\Policies\JournalEntryPolicy::class,
        \App\Models\Asset::class => \App\Policies\AssetPolicy::class,
        \App\Models\BankReconciliation::class => \App\Policies\BankReconciliationPolicy::class,
        \App\Models\Budget::class => \App\Policies\BudgetPolicy::class,
        \App\Models\ProductionOrder::class => \App\Policies\ProductionOrderPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        // Automatically finding the Policies
        Gate::guessPolicyNamesUsing(function ($modelClass) {
            return 'App\\Policies\\' . class_basename($modelClass) . 'Policy';
        });

        $this->registerPolicies();

        // Implicitly grant "Super Admin" role all permission checks using can()
        Gate::before(function ($user, $ability) {
            if ($user->isSuperAdmin()) {
                return true;
            }
        });
    }
}
