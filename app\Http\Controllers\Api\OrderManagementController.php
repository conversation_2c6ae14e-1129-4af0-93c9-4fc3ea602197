<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Supplier;
use App\Models\Order;
use App\Models\Sale;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class OrderManagementController extends Controller
{
    /**
     * Get products for order management
     */
    public function getProducts(Request $request): JsonResponse
    {
        $search = $request->get('search', '');
        $limit = $request->get('limit', 50);

        $query = Product::with(['units', 'category'])
            ->where('is_active', true);

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%");
            });
        }

        $products = $query->limit($limit)->get();

        return response()->json([
            'success' => true,
            'data' => $products
        ]);
    }

    /**
     * Get product by barcode
     */
    public function getProductByBarcode(Request $request): JsonResponse
    {
        $barcode = $request->get('barcode');

        if (!$barcode) {
            return response()->json([
                'success' => false,
                'message' => 'Barcode is required'
            ], 400);
        }

        $product = Product::with(['units', 'category'])
            ->where('barcode', $barcode)
            ->where('is_active', true)
            ->first();

        if (!$product) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $product
        ]);
    }

    /**
     * Get suppliers for order management
     */
    public function getSuppliers(Request $request): JsonResponse
    {
        $search = $request->get('search', '');
        $limit = $request->get('limit', 50);

        $query = Supplier::where('status_id', 1); // Assuming 1 is active status

        if ($search) {
            $query->where('name', 'like', "%{$search}%");
        }

        $suppliers = $query->limit($limit)->get();

        return response()->json([
            'success' => true,
            'data' => $suppliers
        ]);
    }

    /**
     * Get returnable orders
     */
    public function getReturnableOrders(Request $request): JsonResponse
    {
        $search = $request->get('search', '');
        $limit = $request->get('limit', 50);

        $query = Order::with(['supplier', 'sales.product'])
            ->where('status', 'completed')
            ->where('order_type', 'order'); // Only original orders, not returns

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('supplier', function($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $orders = $query->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $orders
        ]);
    }

    /**
     * Get returnable items for a specific order
     */
    public function getReturnableItems(Request $request, Order $order): JsonResponse
    {
        $items = $order->stocks()
            ->with(['product', 'unit'])
            ->get()
            ->map(function($sale) {
                return [
                    'id' => $sale->id,
                    'product_id' => $sale->product_id,
                    'product_name' => $sale->product->name ?? 'Unknown Product',
                    'unit_id' => $sale->unit_id,
                    'unit_name' => $sale->unit->name ?? 'Unknown Unit',
                    'quantity' => $sale->quantity,
                    'buying_price' => $sale->buying_price,
                    'selling_price' => $sale->selling_price,
                    'amount_total' => $sale->amount_total,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $items
        ]);
    }

    /**
     * Save order (create or update)
     */
    public function saveOrder(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'supplier_id' => 'nullable|exists:suppliers,id',
            'order_type' => 'required|in:order,return',
            'original_order_id' => 'nullable|exists:orders,id',
            'notes' => 'nullable|string',
            'sales' => 'required|array',
            'sales.*.product_id' => 'required|exists:products,id',
            'sales.*.unit_id' => 'required|exists:units,id',
            'sales.*.quantity' => 'required|numeric|min:0.01',
            'sales.*.buying_price' => 'required|numeric|min:0',
            'sales.*.selling_price' => 'required|numeric|min:0',
        ]);

        try {
            \DB::beginTransaction();

            // Create order
            $order = Order::create([
                'supplier_id' => $validated['supplier_id'],
                'order_type' => $validated['order_type'],
                'original_order_id' => $validated['original_order_id'] ?? null,
                'notes' => $validated['notes'] ?? '',
                'status' => 'pending',
                'created_by' => auth()->id(),
            ]);

            // Create sales
            foreach ($validated['sales'] as $saleData) {
                $quantity = $validated['order_type'] === 'return' ? -abs($saleData['quantity']) : $saleData['quantity'];
                
                Sale::create([
                    'order_id' => $order->id,
                    'product_id' => $saleData['product_id'],
                    'unit_id' => $saleData['unit_id'],
                    'quantity' => $quantity,
                    'buying_price' => $saleData['buying_price'],
                    'selling_price' => $saleData['selling_price'],
                    'amount_total' => $quantity * $saleData['buying_price'],
                    'created_by' => auth()->id(),
                ]);
            }

            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order saved successfully',
                'data' => $order->load('sales.product', 'supplier')
            ]);

        } catch (\Exception $e) {
            \DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to save order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate order totals
     */
    public function calculateTotals(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'sales' => 'required|array',
            'sales.*.quantity' => 'required|numeric',
            'sales.*.buying_price' => 'required|numeric',
            'order_type' => 'required|in:order,return',
        ]);

        $subtotal = 0;
        $totalItems = 0;

        foreach ($validated['sales'] as $sale) {
            $quantity = $validated['order_type'] === 'return' ? -abs($sale['quantity']) : $sale['quantity'];
            $subtotal += $quantity * $sale['buying_price'];
            $totalItems += abs($sale['quantity']);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'subtotal' => round($subtotal, 2),
                'total_items' => $totalItems,
                'total' => round($subtotal, 2), // Add tax calculation here if needed
            ]
        ]);
    }
}
