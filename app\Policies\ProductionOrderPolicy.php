<?php

namespace App\Policies;

use App\Models\User;
use App\Models\ProductionOrder;
use Illuminate\Auth\Access\HandlesAuthorization;

class ProductionOrderPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo('list production-orders');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\ProductionOrder  $productionOrder
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, ProductionOrder $productionOrder)
    {
        return $user->hasPermissionTo('view production-orders');
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo('create production-orders');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\ProductionOrder  $productionOrder
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, ProductionOrder $productionOrder)
    {
        return $user->hasPermissionTo('update production-orders') && 
            !in_array($productionOrder->status, ['completed', 'cancelled']);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\ProductionOrder  $productionOrder
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, ProductionOrder $productionOrder)
    {
        return $user->hasPermissionTo('delete production-orders') && 
            $productionOrder->status === 'draft';
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\ProductionOrder  $productionOrder
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, ProductionOrder $productionOrder)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\ProductionOrder  $productionOrder
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, ProductionOrder $productionOrder)
    {
        return false;
    }

    /**
     * Determine whether the user can complete the production order.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\ProductionOrder  $productionOrder
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function complete(User $user, ProductionOrder $productionOrder)
    {
        return $user->hasPermissionTo('complete production-orders') && 
            $productionOrder->status === 'in_progress';
    }
}
