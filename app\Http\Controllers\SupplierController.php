<?php

namespace App\Http\Controllers;

use App\Models\Status;
use App\Models\Supplier;
use Illuminate\Http\Request;
use App\Http\Requests\SupplierStoreRequest;
use App\Http\Requests\SupplierUpdateRequest;

class SupplierController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Supplier::class);

        $search = $request->get('search', '');

        $suppliers = Supplier::search($search)
            ->latest()
            ->paginate(5)
            ->withQueryString();

        return view('app.suppliers.index', compact('suppliers', 'search'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Supplier::class);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');

        return view('app.suppliers.create', compact('statuses'));
    }

    /**
     * @param \App\Http\Requests\SupplierStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(SupplierStoreRequest $request)
    {
        $this->authorize('create', Supplier::class);

        $validated = $request->validated();

        $supplier = Supplier::create($validated);

        return redirect()
            ->route('suppliers.edit', $supplier)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Supplier $supplier
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Supplier $supplier)
    {
        $this->authorize('view', $supplier);

        return view('app.suppliers.show', compact('supplier'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Supplier $supplier
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Supplier $supplier)
    {
        $this->authorize('update', $supplier);

        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');

        return view('app.suppliers.edit', compact('supplier', 'statuses'));
    }

    /**
     * @param \App\Http\Requests\SupplierUpdateRequest $request
     * @param \App\Models\Supplier $supplier
     * @return \Illuminate\Http\Response
     */
    public function update(SupplierUpdateRequest $request, Supplier $supplier)
    {
        $this->authorize('update', $supplier);

        $validated = $request->validated();

        $supplier->update($validated);

        return redirect()
            ->route('suppliers.edit', $supplier)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Supplier $supplier
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Supplier $supplier)
    {
        $this->authorize('delete', $supplier);

        $supplier->delete();

        return redirect()
            ->route('suppliers.index')
            ->withSuccess(__('crud.common.removed'));
    }

    /**
     * Get suppliers for API/AJAX requests
     */
    public function api(Request $request)
    {
        try {
            $this->authorize('view-any', Supplier::class);
        } catch (\Exception $e) {
            \Log::error('Supplier API authorization failed: ' . $e->getMessage());
            return response()->json(['error' => 'Authorization failed: ' . $e->getMessage()], 403);
        }

        try {
            $suppliers = Supplier::select('id', 'name', 'email')
                ->when(!auth()->user()->isSuperAdmin(), function($query) {
                    $query->where('branch_id', auth()->user()->branch_id);
                })
                ->orderBy('name')
                ->get();

            \Log::info('Supplier API: Returning ' . $suppliers->count() . ' suppliers');
            return response()->json($suppliers);
        } catch (\Exception $e) {
            \Log::error('Supplier API error: ' . $e->getMessage());
            return response()->json(['error' => 'Database error: ' . $e->getMessage()], 500);
        }
    }
}
