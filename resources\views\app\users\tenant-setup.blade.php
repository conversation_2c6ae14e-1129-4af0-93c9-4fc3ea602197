@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="bi bi-building me-2"></i>Tenant Setup & Testing
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Current User Info -->
                    <div class="alert alert-info">
                        <h6><i class="bi bi-person me-1"></i>Current User</h6>
                        <strong>Email:</strong> {{ auth()->user()->email }}<br>
                        <strong>Current Tenant:</strong> {{ \App\Models\Tenant::current()->name ?? 'None' }}<br>
                        <strong>Available Tenants:</strong> {{ auth()->user()->getAvailableTenants()->count() }}
                    </div>

                    <!-- Add to Multiple Tenants -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6>Add Current User to Multiple Tenants (For Testing)</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('tenant.setup') }}">
                                @csrf
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Tenant ID</label>
                                        <input type="text" class="form-control" name="tenant_id" 
                                               placeholder="e.g., restaurant_branch_1" required>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Role</label>
                                        <select class="form-select" name="role" required>
                                            <option value="admin">Admin</option>
                                            <option value="manager">Manager</option>
                                            <option value="user">User</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2 mb-3 d-flex align-items-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-plus"></i> Add
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Current Tenant Memberships -->
                    <div class="card">
                        <div class="card-header">
                            <h6>Your Tenant Memberships</h6>
                        </div>
                        <div class="card-body">
                            @if(auth()->user()->getAvailableTenants()->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Tenant</th>
                                                <th>Role</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach(auth()->user()->getAvailableTenants() as $tenant)
                                            <tr>
                                                <td>
                                                    <strong>{{ $tenant['name'] }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ $tenant['id'] }}</small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info text-white">{{ ucfirst($tenant['role']) }}</span>
                                                </td>
                                                <td>
                                                    @if($tenant['is_primary'])
                                                        <span class="badge bg-primary text-white">Primary</span>
                                                    @endif
                                                    @if(auth()->user()->getCurrentTenant() === $tenant['id'])
                                                        <span class="badge bg-success text-white">Current</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if(auth()->user()->getCurrentTenant() !== $tenant['id'])
                                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                                onclick="switchTenant('{{ $tenant['id'] }}')">
                                                            Switch
                                                        </button>
                                                    @endif
                                                    @if(!$tenant['is_primary'])
                                                        <button type="button" class="btn btn-sm btn-outline-warning"
                                                                onclick="setPrimary('{{ $tenant['id'] }}')">
                                                            Set Primary
                                                        </button>
                                                    @endif
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="bi bi-building fs-1 text-muted mb-3 d-block"></i>
                                    <h6>No Tenant Memberships</h6>
                                    <p class="text-muted">Add yourself to multiple tenants above to test the tenant switcher.</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Instructions -->
                    <div class="alert alert-warning mt-4">
                        <h6><i class="bi bi-info-circle me-1"></i>How to See Tenant Switcher</h6>
                        <ol class="mb-0">
                            <li>Add yourself to multiple tenants using the form above</li>
                            <li>Look at the <strong>bottom of the sidebar</strong> (navbar-vertical-footer)</li>
                            <li>You'll see a building icon <i class="bi bi-building"></i> next to the theme switcher</li>
                            <li>Click it to see your available tenants and switch between them</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Tenant switching functionality (same as in aside.blade.php)
function switchTenant(tenantId) {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
    button.disabled = true;

    fetch(`/tenant/switch/${tenantId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification.success(data.message || 'Tenant switched successfully!');

            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification.error(data.message || 'Failed to switch tenant');

            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error switching tenant:', error);
        showNotification.error('Failed to switch tenant. Please try again.');

        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function setPrimary(tenantId) {
    if (confirm('Set this as your primary tenant?')) {
        fetch(`/tenant/primary/${tenantId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification.success(data.message || 'Primary tenant set successfully!');

                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showNotification.error(data.message || 'Failed to set primary tenant');
            }
        })
        .catch(error => {
            console.error('Error setting primary tenant:', error);
            showNotification.error('Failed to set primary tenant. Please try again.');
        });
    }
}
</script>
@endpush
@endsection
