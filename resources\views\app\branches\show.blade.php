@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">building</x-slot>
        <x-slot name="title">{{ $branch->name }}</x-slot>
        <x-slot name="subtitle">Branch Details</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('branches.index') }}">Branches</a></li>
            <li class="breadcrumb-item active" aria-current="page">View</li>
        </x-slot>
        <x-slot name="controls">
            @can('update', $branch)
            <a href="{{ route('branches.edit', $branch) }}" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> Edit Branch
            </a>
            @endcan
            <a href="{{ route('branches.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Branches
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <!-- Branch Overview Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-info-circle me-2"></i>Branch Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar avatar-xl avatar-circle mb-3 mx-auto">
                            <span class="avatar-initials">{{ substr($branch->name, 0, 1) }}</span>
                        </div>
                        <h4 class="mb-1">{{ $branch->name }}</h4>
                        <p class="text-muted">
                            <span class="badge bg-{{ optional($branch->status)->name == 'Active' ? 'success' : 'secondary' }}">
                                {{ optional($branch->status)->name ?? 'N/A' }}
                            </span>
                        </p>
                    </div>

                    <hr>

                    <div class="mb-4">
                        <h6 class="mb-3">Contact Information</h6>
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <i class="bi bi-telephone text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">Phone</h6>
                                <a href="tel:{{ $branch->phone }}" class="text-body">{{ $branch->phone ?? 'Not provided' }}</a>
                            </div>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="bi bi-envelope text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">Email</h6>
                                <a href="mailto:{{ $branch->email }}" class="text-body">{{ $branch->email ?? 'Not provided' }}</a>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div>
                        <h6 class="mb-3">Location</h6>
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="bi bi-geo-alt text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">Address</h6>
                                <p class="mb-0">{{ $branch->address ?? 'Not provided' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Branch Statistics Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-graph-up me-2"></i>Branch Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="card card-dashed h-100">
                                <div class="card-body d-flex justify-content-center flex-column text-center">
                                    <h2 class="mb-1">{{ App\Models\User::where('branch_id', $branch->id)->count() }}</h2>
                                    <span class="text-muted">Staff Members</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card card-dashed h-100">
                                <div class="card-body d-flex justify-content-center flex-column text-center">
                                    <h2 class="mb-1">{{ App\Models\Invoice::where('branch_id', $branch->id)->count() }}</h2>
                                    <span class="text-muted">Total Sales</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card card-dashed h-100">
                                <div class="card-body d-flex justify-content-center flex-column text-center">
                                    <h2 class="mb-1">{{ _money(App\Models\Invoice::where('branch_id', $branch->id)->sum('amount_total')) }}</h2>
                                    <span class="text-muted">Revenue</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card card-dashed h-100">
                                <div class="card-body d-flex justify-content-center flex-column text-center">
                                    <h2 class="mb-1">{{ App\Models\Order::where('branch_id', $branch->id)->count() }}</h2>
                                    <span class="text-muted">Orders</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Branch Activity Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-activity me-2"></i>Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    @php
                        $recentInvoices = App\Models\Invoice::where('branch_id', $branch->id)
                            ->orderBy('created_at', 'desc')
                            ->take(5)
                            ->get();
                    @endphp
                    
                    @if($recentInvoices->count() > 0)
                        <ul class="step step-icon-xs">
                            @foreach($recentInvoices as $invoice)
                            <li class="step-item">
                                <div class="step-content-wrapper">
                                    <span class="step-icon step-icon-soft-primary">
                                        <i class="bi bi-receipt"></i>
                                    </span>
                                    <div class="step-content">
                                        <h6 class="mb-0">Invoice #{{ $invoice->invoice_id }}</h6>
                                        <p class="fs-6 text-muted mb-0">
                                            {{ _money($invoice->amount_total) }} - 
                                            {{ $invoice->created_at->diffForHumans() }}
                                        </p>
                                    </div>
                                </div>
                            </li>
                            @endforeach
                        </ul>
                    @else
                        <div class="text-center py-4">
                            <i class="bi bi-activity text-muted" style="font-size: 2rem;"></i>
                            <p class="mt-2">No recent activity found</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="d-flex justify-content-between mt-2 mb-4">
        <div>
            <a href="{{ route('branches.index') }}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left me-1"></i> Back to Branches
            </a>

            @can('create', App\Models\Branch::class)
            <a href="{{ route('branches.create') }}" class="btn btn-outline-primary">
                <i class="bi bi-plus-circle me-1"></i> Create New Branch
            </a>
            @endcan
        </div>

        <div>
            @can('update', $branch)
            <a href="{{ route('branches.edit', $branch) }}" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> Edit
            </a>
            @endcan
            
            @can('delete', $branch)
            <form action="{{ route('branches.destroy', $branch) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this branch?')">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-danger">
                    <i class="bi bi-trash me-1"></i> Delete
                </button>
            </form>
            @endcan
        </div>
    </div>
</div>
@endsection
