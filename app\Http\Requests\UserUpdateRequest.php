<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Models\User;

class UserUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', 'max:255', 'string'],
            'phone' => ['nullable', 'max:255', 'string'],
            'password' => ['nullable'],
            'status_id' => ['nullable', 'tenant_exists:statuses,id'],
            'branch_id' => ['nullable', 'tenant_exists:branches,id'],
            'warehouse_id' => ['nullable', 'tenant_exists:warehouses,id'],
            'roles' => 'array',
        ];
    }
}
