<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;

class Payment extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    // use BusinessTypeTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'amount',
        'comment',
        'description',
        'created_by',
        'updated_by',
        'balance',
        'paymentable_id',
        'paymentable_type',
        'reference_no',
        'currency_id',
    ];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];
    
    protected $searchableFields = ['*'];

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function paymentable()
    {
        return $this->morphTo();
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    /**
     * Get formatted description for transaction display
     */
    public function getTransactionDescriptionAttribute()
    {
        if ($this->description) {
            return $this->description;
        }

        if ($this->comment) {
            return $this->comment;
        }

        if ($this->paymentable) {
            $type = class_basename($this->paymentable_type);
            return "Payment for {$type} #{$this->paymentable_id}";
        }

        return "Payment";
    }

    /**
     * Get the payment type for display
     */
    public function getPaymentTypeAttribute()
    {
        if ($this->paymentable) {
            return class_basename($this->paymentable_type);
        }

        return 'General';
    }

}
