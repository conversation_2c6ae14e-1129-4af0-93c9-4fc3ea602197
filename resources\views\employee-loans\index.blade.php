@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-bank text-primary"></i> Employee Loans
            </h1>
            <p class="text-muted mb-0">Manage employee loan applications and payments</p>
        </div>
        <div>
            @can('create', App\Models\EmployeeLoan::class)
            <a href="{{ route('employee-loans.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> New Loan Application
            </a>
            @endcan
        </div>
    </div>

    <!-- Filters Card -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('employee-loans.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ $search }}" placeholder="Loan reference, purpose, employee...">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="pending" {{ $status === 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="approved" {{ $status === 'approved' ? 'selected' : '' }}>Approved</option>
                        <option value="active" {{ $status === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="completed" {{ $status === 'completed' ? 'selected' : '' }}>Completed</option>
                        <option value="cancelled" {{ $status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="employee_id" class="form-label">Employee</label>
                    <select class="form-select" id="employee_id" name="employee_id">
                        <option value="">All Employees</option>
                        @foreach($employees as $employee)
                            <option value="{{ $employee->id }}" {{ $employee_id == $employee->id ? 'selected' : '' }}>
                                {{ $employee->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i> Filter
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <a href="{{ route('employee-loans.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Loans Table -->
    <div class="card shadow-sm">
        <div class="card-header bg-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-list"></i> Loan Applications
                <span class="badge bg-secondary ms-2">{{ $loans->total() }} total</span>
            </h5>
        </div>
        <div class="card-body p-0">
            @if($loans->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Loan Reference</th>
                            <th>Employee</th>
                            <th>Amount</th>
                            <th>Monthly Payment</th>
                            <th>Remaining Balance</th>
                            <th>Status</th>
                            <th>Progress</th>
                            <th>Date Applied</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($loans as $loan)
                        <tr>
                            <td>
                                <strong>{{ $loan->loan_reference }}</strong>
                                @if($loan->purpose)
                                <br><small class="text-muted">{{ Str::limit($loan->purpose, 30) }}</small>
                                @endif
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2">
                                        <span class="avatar-initial bg-primary rounded-circle">
                                            {{ substr($loan->employee->name, 0, 1) }}
                                        </span>
                                    </div>
                                    <div>
                                        <strong>{{ $loan->employee->name }}</strong>
                                        @if($loan->employee->email)
                                        <br><small class="text-muted">{{ $loan->employee->email }}</small>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>
                                <strong class="text-success">K{{ number_format($loan->loan_amount, 2) }}</strong>
                            </td>
                            <td>
                                K{{ number_format($loan->installment_amount, 2) }}
                                <br><small class="text-muted">{{ $loan->total_installments }} installments</small>
                            </td>
                            <td>
                                <strong class="text-warning">K{{ number_format($loan->remaining_balance, 2) }}</strong>
                                @if($loan->status === 'active')
                                <br><small class="text-muted">{{ $loan->installments_paid }}/{{ $loan->total_installments }} paid</small>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-{{ 
                                    $loan->status === 'pending' ? 'warning' : 
                                    ($loan->status === 'approved' ? 'info' : 
                                    ($loan->status === 'active' ? 'primary' : 
                                    ($loan->status === 'completed' ? 'success' : 'danger'))) 
                                }}">
                                    {{ ucfirst($loan->status) }}
                                </span>
                            </td>
                            <td>
                                @if(in_array($loan->status, ['active', 'completed']))
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-{{ $loan->status === 'completed' ? 'success' : 'primary' }}" 
                                         style="width: {{ $loan->progress_percentage }}%"></div>
                                </div>
                                <small class="text-muted">{{ $loan->progress_percentage }}%</small>
                                @else
                                <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                {{ $loan->loan_date->format('M d, Y') }}
                                <br><small class="text-muted">{{ $loan->loan_date->diffForHumans() }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    @can('view', $loan)
                                    <a href="{{ route('employee-loans.show', $loan) }}" 
                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @endcan
                                    
                                    @can('update', $loan)
                                    @if($loan->status === 'pending')
                                    <a href="{{ route('employee-loans.edit', $loan) }}" 
                                       class="btn btn-sm btn-outline-secondary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @endif
                                    @endcan
                                    
                                    @can('approve', $loan)
                                    @if($loan->status === 'pending')
                                    <button type="button" class="btn btn-sm btn-outline-success" 
                                            onclick="approveLoan({{ $loan->id }})" title="Approve">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    @endif
                                    @endcan
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="card-footer bg-white">
                {{ $loans->appends(request()->query())->links() }}
            </div>
            @else
            <div class="text-center py-5">
                <i class="fas fa-bank fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No loan applications found</h5>
                <p class="text-muted">
                    @if($search || $status || $employee_id)
                        Try adjusting your filters or 
                        <a href="{{ route('employee-loans.index') }}">clear all filters</a>.
                    @else
                        Get started by creating a new loan application.
                    @endif
                </p>
                @can('create', App\Models\EmployeeLoan::class)
                <a href="{{ route('employee-loans.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create First Loan Application
                </a>
                @endcan
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Approve Loan Application</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="approvalForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="approval_notes" class="form-label">Approval Notes (Optional)</label>
                        <textarea class="form-control" id="approval_notes" name="approval_notes" rows="3"
                                  placeholder="Add any notes about this approval..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> Approve Loan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function approveLoan(loanId) {
    const form = document.getElementById('approvalForm');
    form.action = `/employee-loans/${loanId}/approve`;
    
    const modal = new bootstrap.Modal(document.getElementById('approvalModal'));
    modal.show();
}
</script>
@endpush
@endsection
