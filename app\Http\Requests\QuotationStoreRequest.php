<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class QuotationStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date'],
            'sub_paid' => ['nullable', 'numeric'],
            'amount_paid' => ['nullable', 'numeric'],
            'amount_total' => ['nullable', 'numeric'],
            'vat' => ['nullable', 'numeric'],
            'discount' => ['nullable', 'numeric'],
            'customer_id' => ['nullable'],
            'customer_name' => ['nullable'],
            'approved_by' => ['nullable', 'tenant_exists:users,id'],
            'description' => ['nullable', 'max:255', 'string'],
            'customer_id' => ['nullable'],
            'quotation' => ['nullable'],
        ];
    }
}
