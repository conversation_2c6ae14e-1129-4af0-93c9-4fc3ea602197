<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UnitUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', 'max:255', 'string'],
            'quantity' => ['nullable', 'numeric'],
            'status_id' => ['nullable', 'tenant_exists:statuses,id'],
            'product_id' => ['nullable', 'tenant_exists:products,id'],
            'buying_price' => ['nullable'],
            'selling_price' => ['nullable'],
            'position' => ['nullable'],
            'description' => ['nullable'],
        ];
    }
}
