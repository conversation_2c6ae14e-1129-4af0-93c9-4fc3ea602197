@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">folder</x-slot>
        <x-slot name="title">Account Categories</x-slot>
        <x-slot name="subtitle">Manage chart of accounts categories</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Accounting</a></li>
            <li class="breadcrumb-item active" aria-current="page">Account Categories</li>
        </x-slot>
        <x-slot name="controls">
            @can('create', App\Models\AccountCategory::class)
            <a href="{{ route('account-categories.create') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i> Add Category
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Search & Filters -->
    <div class="card mb-3">
        <div class="card-body">
            <form action="{{ route('account-categories.index') }}" method="GET" class="row g-3">
                <div class="col-md-5">
                    <label for="search" class="form-label">Search Categories</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="search" name="search" placeholder="Name, code..." value="{{ $search ?? '' }}">
                    </div>
                </div>
                <div class="col-md-4">
                    <label for="account_type_id" class="form-label">Account Type</label>
                    <select class="form-select js-select" id="account_type_id" name="account_type_id">
                        <option value="">All Account Types</option>
                        @foreach(\App\Models\AccountType::where('is_active', true)->orderBy('name')->get() as $accountType)
                            <option value="{{ $accountType->id }}" {{ request('account_type_id') == $accountType->id ? 'selected' : '' }}>
                                {{ $accountType->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-filter me-1"></i> Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Account Categories Table -->
    <div class="card">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">Account Categories</h4>
                </div>
                <div class="col-auto">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-download me-1"></i> Export
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="exportDropdown">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-excel me-1"></i> Excel</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-pdf me-1"></i> PDF</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-text me-1"></i> CSV</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>@lang('crud.account_categories.inputs.name')</th>
                        <th>@lang('crud.account_categories.inputs.code')</th>
                        <th>@lang('crud.account_categories.inputs.account_type_id')</th>
                        <th class="text-center">@lang('crud.account_categories.inputs.is_active')</th>
                        <th class="text-center">@lang('crud.common.actions')</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($accountCategories as $accountCategory)
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar avatar-xs avatar-soft-primary avatar-circle">
                                        <span class="avatar-initials">{{ substr($accountCategory->name, 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5 class="mb-0">{{ $accountCategory->name ?? '-' }}</h5>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-soft-primary">{{ $accountCategory->code ?? '-' }}</span>
                        </td>
                        <td>
                            @php
                                $accountType = $accountCategory->accountType;
                                if ($accountType) {
                                    $classColors = [
                                        'asset' => 'success',
                                        'liability' => 'danger',
                                        'equity' => 'info',
                                        'revenue' => 'primary',
                                        'expense' => 'warning'
                                    ];
                                    $color = $classColors[$accountType->classification] ?? 'secondary';
                                }
                            @endphp
                            @if($accountType)
                            <span class="badge bg-soft-{{ $color }} text-{{ $color }}">
                                {{ $accountType->name }}
                            </span>
                            @else
                            <span class="text-muted">-</span>
                            @endif
                        </td>
                        <td class="text-center">
                            @if($accountCategory->is_active)
                                <span class="badge bg-soft-success text-success">
                                    <i class="bi bi-check-circle me-1"></i> Active
                                </span>
                            @else
                                <span class="badge bg-soft-danger text-danger">
                                    <i class="bi bi-x-circle me-1"></i> Inactive
                                </span>
                            @endif
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group" aria-label="Row Actions">
                                @can('update', $accountCategory)
                                <a href="{{ route('account-categories.edit', $accountCategory) }}" class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit">
                                    <i class="bi bi-pencil-square"></i>
                                </a>
                                @endcan 
                                
                                @can('view', $accountCategory)
                                <a href="{{ route('account-categories.show', $accountCategory) }}" class="btn btn-outline-info btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="View">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @endcan 
                                
                                @can('delete', $accountCategory)
                                <form action="{{ route('account-categories.destroy', $accountCategory) }}" method="POST" onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')">
                                    @csrf @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="Delete" {{ $accountCategory->is_system ?? false ? 'disabled' : '' }}>
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="5" class="text-center p-4">
                            <div class="d-flex flex-column align-items-center justify-content-center py-5">
                                <i class="bi bi-folder text-muted" style="font-size: 3rem;"></i>
                                <h5 class="mt-4">No account categories found</h5>
                                <p class="text-muted">Try adjusting your search or filter to find what you're looking for.</p>
                                @can('create', App\Models\AccountCategory::class)
                                <a href="{{ route('account-categories.create') }}" class="btn btn-primary mt-2">
                                    <i class="bi bi-plus-circle me-1"></i> Add New Category
                                </a>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        @if(isset($accountCategories) && $accountCategories->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-center">
                {{ $accountCategories->links() }}
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
@endpush
