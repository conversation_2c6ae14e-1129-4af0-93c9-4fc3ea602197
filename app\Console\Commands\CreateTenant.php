<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Tenant;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;

class CreateTenant extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tenant:create {name} {email} {domain} {database?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new tenant';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $name = $this->argument('name');
        $email = $this->argument('email');
        $domain = $this->argument('domain');
        $database = $this->argument('database') ?? 'accounting_' . strtolower($domain);

        // Create a tenant
        try {
            // Check if tenants table exists
            if (!Schema::hasTable('tenants')) {
                $this->info('Creating tenants table...');
                Schema::create('tenants', function ($table) {
                    $table->id();
                    $table->string('name');
                    $table->string('email')->unique();
                    $table->string('phone')->nullable();
                    $table->string('password')->nullable();
                    $table->string('current_tenant')->nullable();
                    $table->string('domain')->unique();
                    $table->string('database')->unique();
                    $table->timestamps();
                });
                $this->info('Created tenants table');
            }

            $tenant = Tenant::create([
                'name' => $name,
                'email' => $email,
                'phone' => '**********',
                'password' => Hash::make('password'),
                'domain' => $domain,
                'database' => $database,
            ]);

            $this->info("Created tenant: " . $tenant->name);
            $this->info("Database: " . $tenant->database);

            // Create the tenant database
            DB::statement("CREATE DATABASE IF NOT EXISTS `{$tenant->database}`");
            $this->info("Created tenant database");

            // Configure tenant connection
            config([
                'database.connections.tenant' => [
                    'driver' => env('DB_CONNECTION', 'mysql'),
                    'host' => env('DB_HOST', '127.0.0.1'),
                    'port' => env('DB_PORT', '3306'),
                    'database' => $tenant->database,
                    'username' => env('DB_USERNAME', 'root'),
                    'password' => env('DB_PASSWORD', ''),
                ],
            ]);

            // Purge the connection to ensure the new config is used
            DB::purge('tenant');

            // Make the tenant current
            $tenant->makeCurrent();
            $this->info("Made tenant current");

            // Run migrations for the tenant
            $this->info("Running migrations...");
            Artisan::call('migrate', [
                '--database' => 'tenant',
                '--force' => true,
            ]);
            $this->info(Artisan::output());

            // Run seeders for the tenant
            $this->info("Running seeders...");
            Artisan::call('db:seed', [
                '--database' => 'tenant',
                '--force' => true,
            ]);
            $this->info(Artisan::output());

            // Run specific accounting seeders
            $this->info("Running accounting seeders...");
            Artisan::call('db:seed', [
                '--class' => 'PermissionsSeeder',
                '--database' => 'tenant',
                '--force' => true,
            ]);
            $this->info(Artisan::output());

            Artisan::call('db:seed', [
                '--class' => 'ChartOfAccountsSeeder',
                '--database' => 'tenant',
                '--force' => true,
            ]);
            $this->info(Artisan::output());

            Artisan::call('db:seed', [
                '--class' => 'FiscalYearSeeder',
                '--database' => 'tenant',
                '--force' => true,
            ]);
            $this->info(Artisan::output());

            Artisan::call('db:seed', [
                '--class' => 'CurrencySeeder',
                '--database' => 'tenant',
                '--force' => true,
            ]);
            $this->info(Artisan::output());

            Artisan::call('db:seed', [
                '--class' => 'AssetCategorySeeder',
                '--database' => 'tenant',
                '--force' => true,
            ]);
            $this->info(Artisan::output());

            Artisan::call('db:seed', [
                '--class' => 'TaxTypeSeeder',
                '--database' => 'tenant',
                '--force' => true,
            ]);
            $this->info(Artisan::output());

            $this->info("Tenant setup complete!");

        } catch (\Exception $e) {
            $this->error("Error creating tenant: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
