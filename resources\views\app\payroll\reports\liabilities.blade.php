@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title mb-0">
                Payroll Liabilities Report - {{ $monthName }} {{ $year }}
            </h4>
            <div>
                <form action="{{ route('payroll-reports.liabilities') }}" method="POST" class="d-inline">
                    @csrf
                    <input type="hidden" name="month" value="{{ $month }}">
                    <input type="hidden" name="year" value="{{ $year }}">
                    <input type="hidden" name="format" value="pdf">
                    <button type="submit" class="btn btn-sm btn-secondary">
                        <i class="bi bi-file-earmark-pdf"></i> PDF
                    </button>
                </form>
                <form action="{{ route('payroll-reports.liabilities') }}" method="POST" class="d-inline">
                    @csrf
                    <input type="hidden" name="month" value="{{ $month }}">
                    <input type="hidden" name="year" value="{{ $year }}">
                    <input type="hidden" name="format" value="csv">
                    <button type="submit" class="btn btn-sm btn-secondary">
                        <i class="bi bi-file-earmark-excel"></i> CSV
                    </button>
                </form>
                <a href="{{ route('payroll-reports.liabilities-form') }}" class="btn btn-sm btn-light">
                    <i class="bi bi-arrow-left"></i> Back
                </a>
            </div>
        </div>

        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Liability Type</th>
                            <th>Amount</th>
                            <th>Due Date</th>
                            <th>Payee</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($liabilities as $name => $amount)
                        <tr>
                            <td>{{ $name }}</td>
                            <td class="text-right">{{ number_format($amount, 2) }}</td>
                            <td>{{ date('t/m/Y', strtotime($year . '-' . $month . '-01')) }}</td>
                            <td>
                                @if($name == 'PAYE Tax')
                                    Tax Authority
                                @else
                                    Various
                                @endif
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="4" class="text-center">No liabilities found for this period.</td>
                        </tr>
                        @endforelse
                    </tbody>
                    <tfoot>
                        <tr>
                            <th>Total</th>
                            <th class="text-right">{{ number_format($totalLiabilities, 2) }}</th>
                            <th colspan="2"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class="mt-4">
                <h5>Payment Instructions</h5>
                <div class="alert alert-info">
                    <p>The liabilities listed above should be paid by the end of the month to avoid penalties. PAYE tax should be remitted to the tax authority, while other deductions should be paid to the respective benefit providers or institutions.</p>
                </div>
            </div>

            <div class="mt-4">
                <h5>Journal Entry</h5>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Account</th>
                                <th>Debit</th>
                                <th>Credit</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Payroll Liabilities</td>
                                <td class="text-right">{{ number_format($totalLiabilities, 2) }}</td>
                                <td class="text-right">-</td>
                            </tr>
                            <tr>
                                <td>Cash/Bank</td>
                                <td class="text-right">-</td>
                                <td class="text-right">{{ number_format($totalLiabilities, 2) }}</td>
                            </tr>
                            <tr>
                                <th>Total</th>
                                <th class="text-right">{{ number_format($totalLiabilities, 2) }}</th>
                                <th class="text-right">{{ number_format($totalLiabilities, 2) }}</th>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
