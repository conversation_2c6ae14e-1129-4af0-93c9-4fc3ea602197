<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from htmlstream.com/front-dashboard/documentation/spacing.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 30 Aug 2022 07:13:10 GMT -->
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Spacing - Documentation | Front - Multipurpose Responsive Template</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="../favicon.ico">

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&amp;display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="../assets/css/vendor.min.css">

  <!-- CSS Front Template -->
  <link rel="stylesheet" href="../assets/css/theme.minc619.css?v=1.0">

  <link rel="preload" href="../assets/css/theme.min.css" data-hs-appearance="default" as="style">
  <link rel="preload" href="../assets/css/docs.css" data-hs-appearance="default" as="style">
  <link rel="preload" href="../assets/css/theme-dark.min.css" data-hs-appearance="dark" as="style">

  <style data-hs-appearance-onload-styles>
    *
    {
      transition: unset !important;
    }

    body
    {
      opacity: 0;
    }
  </style>

  <script>
            window.hs_config = {"autopath":"@@autopath","deleteLine":"hs-builder:delete","deleteLine:build":"hs-builder:build-delete","deleteLine:dist":"hs-builder:dist-delete","previewMode":false,"startPath":"/index.html","vars":{"themeFont":"https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap","version":"?v=1.0"},"layoutBuilder":{"extend":{"switcherSupport":true},"header":{"layoutMode":"default","containerMode":"container-fluid"},"sidebarLayout":"default"},"themeAppearance":{"layoutSkin":"default","sidebarSkin":"default","styles":{"colors":{"primary":"#377dff","transparent":"transparent","white":"#fff","dark":"132144","gray":{"100":"#f9fafc","900":"#1e2022"}},"font":"Inter"}},"languageDirection":{"lang":"en"},"skipFilesFromBundle":{"dist":["assets/js/hs.theme-appearance.js","assets/js/hs.theme-appearance-charts.html","assets/js/demo.js"],"build":["assets/css/theme.css","assets/vendor/hs-navbar-vertical-aside/dist/hs-navbar-vertical-aside-mini-cache.html","assets/js/demo.html","assets/css/theme-dark.html","assets/css/docs.html","assets/vendor/icon-set/style.html","assets/js/hs.theme-appearance.html","assets/js/hs.theme-appearance-charts.html","node_modules/chartjs-plugin-datalabels/dist/chartjs-plugin-datalabels.min.html","assets/js/demo.js"]},"minifyCSSFiles":["assets/css/theme.css","assets/css/theme-dark.css"],"copyDependencies":{"dist":{"*assets/js/theme-custom.js":""},"build":{"*assets/js/theme-custom.js":"","node_modules/bootstrap-icons/font/*fonts/**":"assets/css"}},"buildFolder":"","replacePathsToCDN":{},"directoryNames":{"src":"./src","dist":"./dist","build":"./build"},"fileNames":{"dist":{"js":"theme.min.js","css":"theme.min.css"},"build":{"css":"theme.min.css","js":"theme.min.js","vendorCSS":"vendor.min.css","vendorJS":"vendor.min.js"}},"fileTypes":"jpg|png|svg|mp4|webm|ogv|json"}
            window.hs_config.gulpRGBA = (p1) => {
  const options = p1.split(',')
  const hex = options[0].toString()
  const transparent = options[1].toString()

  var c;
  if(/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)){
    c= hex.substring(1).split('');
    if(c.length== 3){
      c= [c[0], c[0], c[1], c[1], c[2], c[2]];
    }
    c= '0x'+c.join('');
    return 'rgba('+[(c>>16)&255, (c>>8)&255, c&255].join(',')+',' + transparent + ')';
  }
  throw new Error('Bad Hex');
}
            window.hs_config.gulpDarken = (p1) => {
  const options = p1.split(',')

  let col = options[0].toString()
  let amt = -parseInt(options[1])
  var usePound = false

  if (col[0] == "#") {
    col = col.slice(1)
    usePound = true
  }
  var num = parseInt(col, 16)
  var r = (num >> 16) + amt
  if (r > 255) {
    r = 255
  } else if (r < 0) {
    r = 0
  }
  var b = ((num >> 8) & 0x00FF) + amt
  if (b > 255) {
    b = 255
  } else if (b < 0) {
    b = 0
  }
  var g = (num & 0x0000FF) + amt
  if (g > 255) {
    g = 255
  } else if (g < 0) {
    g = 0
  }
  return (usePound ? "#" : "") + (g | (b << 8) | (r << 16)).toString(16)
}
            window.hs_config.gulpLighten = (p1) => {
  const options = p1.split(',')

  let col = options[0].toString()
  let amt = parseInt(options[1])
  var usePound = false

  if (col[0] == "#") {
    col = col.slice(1)
    usePound = true
  }
  var num = parseInt(col, 16)
  var r = (num >> 16) + amt
  if (r > 255) {
    r = 255
  } else if (r < 0) {
    r = 0
  }
  var b = ((num >> 8) & 0x00FF) + amt
  if (b > 255) {
    b = 255
  } else if (b < 0) {
    b = 0
  }
  var g = (num & 0x0000FF) + amt
  if (g > 255) {
    g = 255
  } else if (g < 0) {
    g = 0
  }
  return (usePound ? "#" : "") + (g | (b << 8) | (r << 16)).toString(16)
}
            </script>
</head>

<body class="navbar-sidebar-aside-lg">

  <script src="../assets/js/hs.theme-appearance.js"></script>

  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand navbar-end navbar-light navbar-sticky-lg-top bg-white">
    <div class="container-fluid">
      <nav class="navbar-nav-wrap">
        <div class="row flex-grow-1">
          <!-- Default Logo -->
          <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
            <a class="navbar-brand" href="index.html" aria-label="Front">
              <img class="navbar-brand-logo" src="../assets/svg/logos/logo.svg" alt="Logo" data-hs-theme-appearance="default">
              <img class="navbar-brand-logo" src="../assets/svg/logos-light/logo.svg" alt="Logo" data-hs-theme-appearance="dark">
            </a>
            <a href="changelog.html">
              <span class="badge bg-soft-primary text-primary">v2.1</span>
            </a>
          </div>
          <!-- End Default Logo -->

          <div class="col-md px-lg-0">
            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
              <div class="d-none d-md-block">
                <!-- Search Form -->
                <form id="docsSearch" class="position-relative" data-hs-list-options='{
                       "searchMenu": true,
                       "keyboard": true,
                       "item": "searchTemplate",
                       "valueNames": ["component", "category", {"name": "link", "attr": "href"}],
                       "empty": "#searchNoResults"
                     }'>
                  <!-- Input Group -->
                  <div class="input-group input-group-merge navbar-input-group">
                    <div class="input-group-prepend input-group-text">
                      <i class="bi-search"></i>
                    </div>

                    <input type="search" class="search form-control form-control-sm" placeholder="Search in docs" aria-label="Search in docs">

                    <a class="input-group-append input-group-text" href="javascript:;">
                      <i id="clearSearchResultsIcon" class="bi-x" style="display: none;"></i>
                    </a>
                  </div>
                  <!-- End Input Group -->

                  <!-- List -->
                  <div class="list dropdown-menu navbar-dropdown-menu-borderless w-100 overflow-auto" style="max-height: 16rem;"></div>
                  <!-- End List -->

                  <!-- Empty -->
                  <div id="searchNoResults" style="display: none;">
                    <div class="text-center p-4">
                      <img class="mb-3" src="../assets/svg/illustrations/oc-error.svg" alt="Image Description" data-hs-theme-appearance="default" style="width: 7rem;">
                      <img class="mb-3" src="../assets/svg/illustrations-light/oc-error.svg" alt="Image Description" data-hs-theme-appearance="dark" style="width: 7rem;">
                      <p class="mb-0">No Results</p>
                    </div>
                  </div>
                  <!-- End Empty -->
                </form>
                <!-- End Search Form -->

                <!-- List Item Template -->
                <div class="d-none">
                  <div id="searchTemplate" class="dropdown-item">
                    <a class="d-block link" href="#">
                      <span class="category d-block fw-normal text-muted mb-1"></span>
                      <span class="component text-dark"></span>
                    </a>
                  </div>
                </div>
                <!-- End List Item Template -->
              </div>

              <!-- Navbar -->
              <ul class="navbar-nav p-0">
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://htmlstream.com/contact-us" target="_blank">
                    Get Support <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-primary btn-sm" href="../index.html">
                    <i class="bi-eye me-1"></i> Preview Demo
                  </a>
                </li>
              </ul>
              <!-- End Navbar -->
            </div>
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </nav>
    </div>
  </header>

  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <!-- Navbar -->
    <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end" data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
      <!-- Navbar Toggle -->
      <div class="d-grid flex-grow-1 px-2">
        <button type="button" class="navbar-toggler btn btn-white" data-bs-toggle="collapse" data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false" aria-controls="navbarVerticalNavMenu">
          <span class="d-flex justify-content-between align-items-center">
            <span class="h3 mb-0">Nav menu</span>

            <span class="navbar-toggler-default">
              <i class="bi-list"></i>
            </span>

            <span class="navbar-toggler-toggled">
              <i class="bi-x"></i>
            </span>
          </span>
        </button>
      </div>
      <!-- End Navbar Toggle -->

      <!-- Navbar Collapse -->
      <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
        <div class="navbar-brand-wrapper border-end" style="height: auto;">
          <!-- Default Logo -->
          <div class="d-flex align-items-center mb-3">
            <a class="navbar-brand" href="index.html" aria-label="Front">
              <img class="navbar-brand-logo" src="../assets/svg/logos/logo.svg" alt="Logo" data-hs-theme-appearance="default">
              <img class="navbar-brand-logo" src="../assets/svg/logos-light/logo.svg" alt="Logo" data-hs-theme-appearance="dark">
            </a>
            <a class="navbar-brand-badge" href="changelog.html">
              <span class="badge bg-soft-primary text-primary ms-2">v2.1</span>
            </a>
          </div>
          <!-- End Default Logo -->
        </div>

        <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
          <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
            <li class="nav-item">
              <span class="nav-subtitle">Documentation</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="index.html">Introduction</a>
            </li>

            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Getting started</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="getting-started.html">Getting Started</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="gulp.html">Gulp</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="darkmode.html">Dark Mode <span class="badge bg-soft-dark text-dark lh-base ms-1">New</span></a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="customization.html">Customization</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="credits.html">Credits</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="changelog.html">Changelog</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Design &amp; Graphics</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="bs-icons.html">Bootstrap Icons</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="illustrations.html">Illustrations</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Components</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="accordion.html">Accordion</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="alerts.html">Alerts</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="avatars.html">Avatars</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="badge.html">Badge</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="breadcrumb.html">Breadcrumb</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="buttons.html">Buttons</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="button-group.html">Button Group</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="cards.html">Cards</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="collapse.html">Collapse</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="column-divider.html">Column Divider</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="devices.html">Devices</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="divider.html">Divider</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="dropdowns.html">Dropdowns</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="icons.html">Icons</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="list-group.html">List Group</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="lists.html">Lists</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="legend-indicator.html">Legend Indicator</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="modal.html">Modal</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="offcanvas.html">Offcanvas</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="page-header.html">Page Header</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="pagination.html">Pagination</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="popovers.html">Popovers</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="progress.html">Progress</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="profile.html">Profile</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="shapes.html">Shapes</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sliding-img.html">Sliding Image</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="spinners.html">Spinners</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="steps.html">Steps</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="tab.html">Tab</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="toasts.html">Toasts</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="tooltips.html">Tooltips</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="typography.html">Typography</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Navbars</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="navbar.html">Navbar</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="navs.html">Navs</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="mega-menu.html">Mega Menu</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="navbar-vertical-aside.html">Navbar Vertical Aside</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="scrollspy.html">Scrollspy</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Tables</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="tables.html">Tables</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="datatables.html">Datatables</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="table-sticky-header.html">Sticky Header</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Basic forms</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="basic-forms.html">Basic Forms</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="checks-and-switches.html">Checks &amp; Switches</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="input-group.html">Input Group</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle">Advanced Forms</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="select.html">Advanced Select</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="datepicker.html">Datepicker (Flatpickr)</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="daterangepicker.html">Date Range Picker</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="calendar.html">Calendar (Fullcalendar)</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="file-attachments.html">File Attachments</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="dropzone.html">Drag’ n’ Drop File Uploads</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="quill.html">WYSIWYG Editor</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="quantity-counter.html">Quantity Counter</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="copy-to-clipboard.html">Copy to Clipboard</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="input-mask.html">Input Mask</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="step-forms.html">Step Forms (Wizards)</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="add-field.html">Add Field</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="toggle-password.html">Toggle Password</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="count-characters.html">Count Characters</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="form-search.html">Form Search</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="toggle-switch.html">Toggle Switch</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="google-recaptcha.html">Google reCAPTCHA</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle">Charts</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="chartjs.html">Chart.js</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="counter.html">Counter</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="circles.html">Circles.js (Pie Chart)</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle">Others</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="fslightbox.html">Fullscreen Lightbox</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="maps-leaflet.html">Leaflet</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="maps-jsvectormap.html">JSVectorMap</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sortablejs.html">SortableJS</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sticky-block.html">Sticky Block</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="go-to.html">Go To</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Utilities</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="backgrounds.html">Backgrounds</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="borders.html">Borders</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="colors.html">Colors</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="links.html">Links</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="position.html">Position</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="shadows.html">Shadows</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sizing.html">Sizing</a>
            </li>

            <li class="nav-item">
              <a class="nav-link active" href="spacing.html">Spacing</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="z-index.html">Z-index</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="opacity.html">Opacity</a>
            </li>
          </ul>
        </div>
      </div>
      <!-- End Navbar Collapse -->
    </nav>
    <!-- End Navbar -->

    <!-- Content -->
    <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
      <!-- Page Header -->
      <div class="docs-page-header">
        <div class="row align-items-center">
          <div class="col-sm">
            <h1 class="docs-page-header-title">Spacing</h1>
            <p class="docs-page-header-text">Front includes a wide range of shorthand responsive margin, padding, and gap utility classes to modify an element’s appearance.</p>
          </div>
        </div>
      </div>
      <!-- End Page Header -->

      <!-- Heading -->
      <h2 id="margin-and-padding" class="hs-docs-heading">
        Margin and padding <a class="anchorjs-link" href="#margin-and-padding" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Assign responsive-friendly <code>margin</code> or <code>padding</code> values to an element or a subset of its sides with shorthand classes. Includes support for individual properties, all properties, and vertical and horizontal properties. Classes are built from a default Sass map ranging from <code>.25rem</code> to <code>3rem</code>.</p>

      <p>Using the CSS Grid layout module? Consider using <a class="link" href="#gap">the gap utility</a>.</p>

      <!-- Heading -->
      <h2 id="notation" class="hs-docs-heading">
        Notation <a class="anchorjs-link" href="#notation" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Spacing utilities that apply to all breakpoints, from <code>xs</code> to <code>xxl</code>, have no breakpoint abbreviation in them. This is because those classes are applied from <code>min-width: 0</code> and up, and thus are not bound by a media query. The remaining breakpoints, however, do include a breakpoint abbreviation.</p>

      <p>The classes are named using the format <code>{property}{sides}-{size}</code> for <code>xs</code> and <code>{property}{sides}-{breakpoint}-{size}</code> for <code>sm</code>, <code>md</code>, <code>lg</code>, <code>xl</code>, and <code>xxl</code>.</p>

      <p>Where <em>property</em> is one of:</p>

      <ul>
        <li><code>m</code> - for classes that set <code>margin</code></li>
        <li><code>p</code> - for classes that set <code>padding</code></li>
      </ul>

      <p>Where <em>sides</em> is one of:</p>

      <ul>
        <li><code>t</code> - for classes that set <code>margin-top</code> or <code>padding-top</code></li>
        <li><code>b</code> - for classes that set <code>margin-bottom</code> or <code>padding-bottom</code></li>
        <li><code>s</code> - for classes that set <code>margin-left</code> or <code>padding-left</code> in LTR, <code>margin-right</code> or <code>padding-right</code> in RTL</li>
        <li><code>e</code> - for classes that set <code>margin-right</code> or <code>padding-right</code> in LTR, <code>margin-left</code> or <code>padding-left</code> in RTL</li>
        <li><code>x</code> - for classes that set both <code>*-left</code> and <code>*-right</code></li>
        <li><code>y</code> - for classes that set both <code>*-top</code> and <code>*-bottom</code></li>
        <li>blank - for classes that set a <code>margin</code> or <code>padding</code> on all 4 sides of the element</li>
      </ul>

      <p>Where <em>size</em> is one of:</p>

      <ul>
        <li><code>0</code> - for classes that eliminate the <code>margin</code> or <code>padding</code> by setting it to <code>0</code></li>
        <li><code>1</code> - (by default) for classes that set the <code>margin</code> or <code>padding</code> to <code>$spacer * .25</code></li>
        <li><code>2</code> - (by default) for classes that set the <code>margin</code> or <code>padding</code> to <code>$spacer * .5</code></li>
        <li><code>3</code> - (by default) for classes that set the <code>margin</code> or <code>padding</code> to <code>$spacer</code></li>
        <li><code>4</code> - (by default) for classes that set the <code>margin</code> or <code>padding</code> to <code>$spacer * 1.5</code></li>
        <li><code>5</code> - (by default) for classes that set the <code>margin</code> or <code>padding</code> to <code>$spacer * 3</code></li>
        <li><code>auto</code> - for classes that set the <code>margin</code> to auto</li>
      </ul>

      <p>(You can add more sizes by adding entries to the <code>$spacers</code> Sass map variable.)</p>

      <!-- Heading -->
      <h2 id="content-space-notation" class="hs-docs-heading">
        Content-space notation <a class="anchorjs-link" href="#content-space-notation" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>The classes are named using the format <code>{property}{sides}-{size}</code> for <code>xs</code> and <code>{property}{sides}-{breakpoint}-{size}</code> for <code>sm</code>, <code>md</code>, <code>lg</code>, <code>xl</code>, and <code>xxl</code>.</p>

      <p>Where <em>property</em> is one of:</p>

      <ul>
        <li><code>content-space</code> - for classes that set <code>padding-top</code> and <code>padding-bottom</code></li>
      </ul>

      <p>Where <em>sides</em> is one of:</p>

      <ul>
        <li><code>content-space-t</code> - for classes that set <code>padding-top</code> or <code>padding-top</code></li>
        <li><code>content-space-b</code> - for classes that set <code>padding-bottom</code> or <code>padding-bottom</code></li>
      </ul>

      <p>Where <em>size</em> is one of:</p>

      <ul>
        <li><code>0</code> - for classes that eliminate the <code>padding</code> by setting it to <code>0</code></li>
        <li><code>1</code> - (by default) for classes that set the <code>padding</code> to <code>$content-spacer * 4</code></li>
        <li><code>2</code> - (by default) for classes that set the <code>padding</code> to <code>$content-spacer * 5</code></li>
        <li><code>3</code> - (by default) for classes that set the <code>padding</code> to <code>$content-spacer * 7.5</code></li>
        <li><code>4</code> - (by default) for classes that set the <code>padding</code> to <code>$content-spacer * 10</code></li>
        <li><code>auto</code> - for classes that set the <code>padding</code> to auto</li>
      </ul>

      <p>(You can add more sizes by adding entries to the <code>$content-spacers</code> Sass map variable.)</p>

      <!-- Heading -->
      <h2 id="examples" class="hs-docs-heading">
        Examples <a class="anchorjs-link" href="#examples" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Here are some representative examples of these classes:</p>

      <pre class="rounded mb-3">
        <code class="language-scss" data-lang="html">
          .mt-0 {
            margin-top: 0 !important;
          }

          .ms-1 {
            margin-left: ($spacer * .25) !important;
          }

          .px-2 {
            padding-left: ($spacer * .5) !important;
            padding-right: ($spacer * .5) !important;
          }

          .p-3 {
            padding: $spacer !important;
          }
        </code>
      </pre>

      <!-- Heading -->
      <h2 id="horizontal-centering" class="hs-docs-heading">
        Horizontal centering <a class="anchorjs-link" href="#horizontal-centering" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Additionally, Bootstrap also includes an <code>.mx-auto</code> class for horizontally centering fixed-width block level content—that is, content that has <code>display: block</code> and a <code>width</code> set—by setting the horizontal margins to <code>auto</code>.</p>

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab1" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab1" href="#nav-result1" data-bs-toggle="pill" data-bs-target="#nav-result1" role="tab" aria-controls="nav-result1" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab1" href="#nav-html1" data-bs-toggle="pill" data-bs-target="#nav-html1" role="tab" aria-controls="nav-html1" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent1">
          <div class="tab-pane fade p-4 show active" id="nav-result1" role="tabpanel" aria-labelledby="nav-resultTab1">
            <div class="bg-dark text-white mx-auto" style="width: 200px;">
              Centered element
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html1" role="tabpanel" aria-labelledby="nav-htmlTab1">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;div class="mx-auto" style="width: 200px;"&gt;
                  Centered element
                &lt;/div&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="negative-margin" class="hs-docs-heading">
        Negative margin <a class="anchorjs-link" href="#negative-margin" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>In CSS, <code>margin</code> properties can utilize negative values (<code>padding</code> cannot). These negative margins are <strong>disabled by default</strong>, but can be enabled in Sass by setting <code>$enable-negative-margins: true</code>.</p>

      <p>The syntax is nearly the same as the default, positive margin utilities, but with the addition of <code>n</code> before the requested size. Here&rsquo;s an example class that&rsquo;s the opposite of <code>.mt-1</code>:</p>

      <pre class="rounded mb-3">
        <code class="language-scss" data-lang="html">
          .mt-n1 {
            margin-top: -0.25rem !important;
          }
        </code>
      </pre>

      <!-- Heading -->
      <h2 id="gap" class="hs-docs-heading">
        Gap <a class="anchorjs-link" href="#gap" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>When using <code>display: grid</code>, you can make use of <code>gap</code> utilities on the parent grid container. This can save on having to add margin utilities to individual grid items (children of a <code>display: grid</code> container). Gap utilities are responsive by default, and are generated via our utilities API, based on the <code>$spacers</code> Sass map.</p>

      <!-- Card -->
      <div class="card mb-7">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab2" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab2" href="#nav-result2" data-bs-toggle="pill" data-bs-target="#nav-result2" role="tab" aria-controls="nav-result2" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab2" href="#nav-html2" data-bs-toggle="pill" data-bs-target="#nav-html2" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent2">
          <div class="tab-pane fade p-4 show active" id="nav-result2" role="tabpanel" aria-labelledby="nav-resultTab2">
            <div class="d-grid gap-3">
              <div class="p-2 bg-light border">Grid item 1</div>
              <div class="p-2 bg-light border">Grid item 2</div>
              <div class="p-2 bg-light border">Grid item 3</div>
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html2" role="tabpanel" aria-labelledby="nav-htmlTab2">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;div class="d-grid gap-3"&gt;
                  &lt;div class="p-2 bg-light border"&gt;Grid item 1&lt;/div&gt;
                  &lt;div class="p-2 bg-light border"&gt;Grid item 2&lt;/div&gt;
                  &lt;div class="p-2 bg-light border"&gt;Grid item 3&lt;/div&gt;
                &lt;/div&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <p>Support includes responsive options for all of Bootstrap&rsquo;s grid breakpoints, as well as six sizes from the <code>$spacers</code> map (<code>0</code>–<code>5</code>). There is no <code>.gap-auto</code> utility class as it&rsquo;s effectively the same as <code>.gap-0</code>.</p>

      <!-- Heading -->
      <h2 id="sass" class="hs-docs-heading">
        Sass <a class="anchorjs-link" href="#sass" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Heading -->
      <h2 id="maps" class="hs-docs-heading">
        Maps <a class="anchorjs-link" href="#maps" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Spacing utilities are declared via Sass map and then generated with our utilities API.</p>

      <pre class="rounded mb-3">
        <code class="language-scss" data-lang="html">
          $spacer: 1rem;
          $spacers: (
            0: 0,
            1: $spacer / 4,
            2: $spacer / 2,
            3: $spacer,
            4: $spacer * 1.5,
            5: $spacer * 3,
          );

          $negative-spacers: if($enable-negative-margins, negativify-map($spacers), null);
        </code>
      </pre>

      <!-- Heading -->
      <h2 id="utilities-api" class="hs-docs-heading">
        Utilities API <a class="anchorjs-link" href="#utilities-api" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Spacing utilities are declared in our utilities API in <code>scss/_utilities.scss</code>. <a class="link" href="https://getbootstrap.com/docs/5.0/utilities/api/#using-the-api">Learn how to use the utilities API.</a></p>

      <pre class="rounded mb-3">
        <code class="language-scss" data-lang="html">
          "margin": (
            responsive: true,
            property: margin,
            class: m,
            values: map-merge($spacers, (auto: auto))
          ),
          "margin-x": (
            responsive: true,
            property: margin-right margin-left,
            class: mx,
            values: map-merge($spacers, (auto: auto))
          ),
          "margin-y": (
            responsive: true,
            property: margin-top margin-bottom,
            class: my,
            values: map-merge($spacers, (auto: auto))
          ),
          "margin-top": (
            responsive: true,
            property: margin-top,
            class: mt,
            values: map-merge($spacers, (auto: auto))
          ),
          "margin-end": (
            responsive: true,
            property: margin-right,
            class: me,
            values: map-merge($spacers, (auto: auto))
          ),
          "margin-bottom": (
            responsive: true,
            property: margin-bottom,
            class: mb,
            values: map-merge($spacers, (auto: auto))
          ),
          "margin-start": (
            responsive: true,
            property: margin-left,
            class: ms,
            values: map-merge($spacers, (auto: auto))
          ),
          // Negative margin utilities
          "negative-margin": (
            responsive: true,
            property: margin,
            class: m,
            values: $negative-spacers
          ),
          "negative-margin-x": (
            responsive: true,
            property: margin-right margin-left,
            class: mx,
            values: $negative-spacers
          ),
          "negative-margin-y": (
            responsive: true,
            property: margin-top margin-bottom,
            class: my,
            values: $negative-spacers
          ),
          "negative-margin-top": (
            responsive: true,
            property: margin-top,
            class: mt,
            values: $negative-spacers
          ),
          "negative-margin-end": (
            responsive: true,
            property: margin-right,
            class: me,
            values: $negative-spacers
          ),
          "negative-margin-bottom": (
            responsive: true,
            property: margin-bottom,
            class: mb,
            values: $negative-spacers
          ),
          "negative-margin-start": (
            responsive: true,
            property: margin-left,
            class: ms,
            values: $negative-spacers
          ),
          // Padding utilities
          "padding": (
            responsive: true,
            property: padding,
            class: p,
            values: $spacers
          ),
          "padding-x": (
            responsive: true,
            property: padding-right padding-left,
            class: px,
            values: $spacers
          ),
          "padding-y": (
            responsive: true,
            property: padding-top padding-bottom,
            class: py,
            values: $spacers
          ),
          "padding-top": (
            responsive: true,
            property: padding-top,
            class: pt,
            values: $spacers
          ),
          "padding-end": (
            responsive: true,
            property: padding-right,
            class: pe,
            values: $spacers
          ),
          "padding-bottom": (
            responsive: true,
            property: padding-bottom,
            class: pb,
            values: $spacers
          ),
          "padding-start": (
            responsive: true,
            property: padding-left,
            class: ps,
            values: $spacers
          ),
        </code>
      </pre>

      <!-- Heading -->
      <h2 id="content-space-examples" class="hs-docs-heading">
        Content-space examples <a class="anchorjs-link" href="#content-space-examples" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Here are some representative examples of these classes:</p>

      <pre class="rounded mb-3">
        <code class="language-scss" data-lang="html">
          .content-space-t-0 {
            padding-top: 0 !important;
          }

          .content-space-b-1 {
            padding-bottom: ($content-spacer * 4) !important;
          }

          .content-space-2 {
            padding-top: ($content-spacer * 5) !important;
            padding-bottom: ($content-spacer * 5) !important;
          }
        </code>
      </pre>

      <!-- Heading -->
      <h2 id="content-space-sass" class="hs-docs-heading">
        Content-space sass <a class="anchorjs-link" href="#content-space-sass" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Heading -->
      <h2 id="content-space-maps" class="hs-docs-heading">
        Content-space maps <a class="anchorjs-link" href="#content-space-maps" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Spacing utilities are declared via Sass map and then generated with our utilities API.</p>

      <pre class="rounded mb-3">
        <code class="language-scss" data-lang="html">
          $content-spacer: 1rem !default;
          $content-spacers: (
            0: 0,
            1: ($content-spacer * 4),
            2: ($content-spacer * 5),
            3: ($content-spacer * 7.5),
            4: ($content-spacer * 10)
          ) !default;
        </code>
      </pre>

      <!-- Heading -->
      <h2 id="content-space-utilities-api" class="hs-docs-heading">
        Content-space utilities API <a class="anchorjs-link" href="#content-space-utilities-api" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Spacing utilities are declared in our utilities API in <code>scss/front/_utilities.scss</code>. <a class="link" href="https://getbootstrap.com/docs/5.0/utilities/api/#using-the-api">Learn how to use the utilities API.</a></p>

      <pre class="rounded mb-3">
        <code class="language-scss" data-lang="html">
          "vertical-content-spacer-top": (
            property: padding-top,
            class: content-space-t,
            responsive: true,
            values: map-merge($content-spacers, (auto: auto))
          ),
          "vertical-content-spacer-bottom": (
            property: padding-bottom,
            class: content-space-b,
            responsive: true,
            values: map-merge($content-spacers, (auto: auto))
          ),
          "vertical-content-spacer": (
            property: padding-top padding-bottom,
            class: content-space,
            responsive: true,
            values: map-merge($content-spacers, (auto: auto))
          )
        </code>
      </pre>
    </div>
    <!-- End Content -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- ========== SECONDARY CONTENTS ========== -->
  <!-- Go To -->
  <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
    <i class="bi-chevron-up"></i>
  </a>
  <!-- ========== END SECONDARY CONTENTS ========== -->

  <!-- JS Implementing Plugins -->
  <script src="../assets/js/vendor.min.js"></script>

  <!-- JS Front -->
  <script src="../assets/js/theme.min.js"></script>

  <!-- JS Plugins Init. -->
  <script>
    (function() {
      // INITIALIZATION OF HEADER
      // =======================================================
      new HSHeader('#header').init()


      // INITIALIZATION OF NAV SCROLLER
      // =======================================================
      new HsNavScroller('.js-nav-scroller', {
        delay: 400,
        offset: 140
      })


      // INITIALIZATION OF LISTJS COMPONENT
      // =======================================================
      HSCore.components.HSList.init('#docsSearch');
      const docsSearch = HSCore.components.HSList.getItem('docsSearch');


      // GET JSON FILE RESULTS
      // =======================================================
      fetch('../assets/json/docs-search.json')
      .then(response => response.json())
      .then(data => {
        docsSearch.add(data)
      })


      // INITIALIZATION OF GO TO
      // =======================================================
      new HSGoTo('.js-go-to')
    })()
  </script>
</body>

<!-- Mirrored from htmlstream.com/front-dashboard/documentation/spacing.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 30 Aug 2022 07:13:10 GMT -->
</html>