<?php

namespace Database\Factories;

use App\Models\Order;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Order::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'date_from' => $this->faker->date,
            'date_to' => $this->faker->date,
            'vat' => $this->faker->randomNumber(1),
            'description' => $this->faker->sentence(15),
            'amount_total' => $this->faker->randomNumber(1),
            'amount_paid' => $this->faker->randomNumber(1),
            'discount' => $this->faker->randomFloat(2, 0, 9999),
            'status_id' => \App\Models\Status::factory(),
            'approved_by' => \App\Models\User::factory(),
            'created_by' => \App\Models\User::factory(),
            'updated_by' => \App\Models\User::factory(),
        ];
    }
}
