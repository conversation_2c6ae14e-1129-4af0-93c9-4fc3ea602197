@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">pencil-square</x-slot>
        <x-slot name="title">Edit Product: {{ $product->name }}</x-slot>
        <x-slot name="subtitle">Update product information and pricing</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('products.index') }}">Products</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('products.show', $product) }}" class="btn btn-outline-info me-2">
                <i class="bi bi-eye me-1"></i> View Product
            </a>
            <a href="{{ route('products.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Products
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Form Card -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-header-title">Product Information</h5>
                <span class="badge bg-primary">ID: {{ $product->id }}</span>
            </div>
        </div>
        <div class="card-body">
            <x-form
                method="PUT"
                action="{{ route('products.update', $product) }}"
                has-files
                class="mt-2"
            >
                @include('app.products.form-inputs')

                <div class="d-flex justify-content-between mt-4 border-top pt-4">
                    <div>
                        <a href="{{ route('products.index') }}" class="btn btn-outline-secondary me-2">
                            <i class="bi bi-arrow-left me-1"></i> Back
                        </a>

                        <a href="{{ route('products.create') }}" class="btn btn-outline-primary">
                            <i class="bi bi-plus-circle me-1"></i> New Product
                        </a>
                    </div>

                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-1"></i> Save Changes
                    </button>
                </div>
            </x-form>
        </div>
    </div>
</div>
@endsection
