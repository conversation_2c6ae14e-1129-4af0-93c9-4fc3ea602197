<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AssetCategory;
use App\Models\Account;

class AssetCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get accounts
        $buildingsAccount = Account::where('code', '1202')->first();
        $equipmentAccount = Account::where('code', '1203')->first();
        $vehiclesAccount = Account::where('code', '1204')->first();
        $accumulatedDepreciationAccount = Account::where('code', '1205')->first();
        $depreciationExpenseAccount = Account::where('code', '5204')->first();
        
        // Create default asset categories
        $assetCategories = [
            [
                'name' => 'Buildings',
                'code' => 'BLD',
                'description' => 'Buildings and structures',
                'asset_account_id' => $buildingsAccount->id ?? null,
                'accumulated_depreciation_account_id' => $accumulatedDepreciationAccount->id ?? null,
                'depreciation_expense_account_id' => $depreciationExpenseAccount->id ?? null,
                'depreciation_method' => 'straight_line',
                'useful_life_years' => 30,
                'is_active' => true,
            ],
            [
                'name' => 'Office Equipment',
                'code' => 'OE',
                'description' => 'Office equipment and furniture',
                'asset_account_id' => $equipmentAccount->id ?? null,
                'accumulated_depreciation_account_id' => $accumulatedDepreciationAccount->id ?? null,
                'depreciation_expense_account_id' => $depreciationExpenseAccount->id ?? null,
                'depreciation_method' => 'straight_line',
                'useful_life_years' => 5,
                'is_active' => true,
            ],
            [
                'name' => 'Computer Equipment',
                'code' => 'CE',
                'description' => 'Computers, servers, and IT equipment',
                'asset_account_id' => $equipmentAccount->id ?? null,
                'accumulated_depreciation_account_id' => $accumulatedDepreciationAccount->id ?? null,
                'depreciation_expense_account_id' => $depreciationExpenseAccount->id ?? null,
                'depreciation_method' => 'declining_balance',
                'useful_life_years' => 3,
                'depreciation_rate' => 0.5,
                'is_active' => true,
            ],
            [
                'name' => 'Vehicles',
                'code' => 'VEH',
                'description' => 'Cars, trucks, and other vehicles',
                'asset_account_id' => $vehiclesAccount->id ?? null,
                'accumulated_depreciation_account_id' => $accumulatedDepreciationAccount->id ?? null,
                'depreciation_expense_account_id' => $depreciationExpenseAccount->id ?? null,
                'depreciation_method' => 'straight_line',
                'useful_life_years' => 5,
                'is_active' => true,
            ],
            [
                'name' => 'Machinery',
                'code' => 'MACH',
                'description' => 'Manufacturing machinery and equipment',
                'asset_account_id' => $equipmentAccount->id ?? null,
                'accumulated_depreciation_account_id' => $accumulatedDepreciationAccount->id ?? null,
                'depreciation_expense_account_id' => $depreciationExpenseAccount->id ?? null,
                'depreciation_method' => 'straight_line',
                'useful_life_years' => 10,
                'is_active' => true,
            ],
        ];

        foreach ($assetCategories as $assetCategory) {
            AssetCategory::updateOrCreate(
                ['code' => $assetCategory['code']],
                $assetCategory
            );
        }
    }
}
