<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;
use App\Traits\BranchTrait;

class Shipment extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;
    use BranchTrait;
    protected $connection = "tenant";

    protected $fillable = [
        'date_from',
        'date_to',
        'vat',
        'description',
        'sub_total',
        'amount_total',
        'customer_id',
        'customer_name',
        'amount_paid',
        'branch_id',
        'discount',
        'approved_by',
        'status_id',
        'created_by',
        'updated_by',
        'business_type_id',
        'expires_at',
        'currency_id',
        'reference_no',
        'tracking_number',
        'carrier',
        'shipping_method',
        'shipping_address',
        'estimated_delivery',
        'actual_delivery',
        'weight',
        'dimensions',
        'insurance_value',
        'special_instructions',
        'invoice_id',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
        'date_from' => 'datetime:Y-m-d',
        'date_to' => 'datetime:Y-m-d',
        'estimated_delivery' => 'datetime:Y-m-d',
        'actual_delivery' => 'datetime:Y-m-d',
    ];

    protected $appends = ['shipment_id', 'prepared_by'];

    public function getPreparedByAttribute(){
        return $this->createdBy->name ?? '';
    }

    public function getShipmentIdAttribute(){
        return _pad( $this->id, 5, '0');
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function payments()
    {
        return $this->morphMany(Payment::class, 'paymentable');
    }

    public function sales()
    {
        return $this->morphMany(Sale::class, 'saleable');
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the shipping status badge class
     */
    public function getStatusBadgeClassAttribute()
    {
        $statusClasses = [
            '10' => 'bg-soft-secondary text-secondary', // Draft
            '11' => 'bg-soft-info text-info',           // Pending
            '12' => 'bg-soft-warning text-warning',     // In Transit
            '13' => 'bg-soft-success text-success',     // Delivered
            '14' => 'bg-soft-danger text-danger',       // Cancelled
            '15' => 'bg-soft-primary text-primary',     // Returned
        ];

        return $statusClasses[$this->status_id] ?? 'bg-soft-secondary text-secondary';
    }

    /**
     * Get the shipping status name
     */
    public function getStatusNameAttribute()
    {
        $statusNames = [
            '10' => 'Draft',
            '11' => 'Pending',
            '12' => 'In Transit',
            '13' => 'Delivered',
            '14' => 'Cancelled',
            '15' => 'Returned',
        ];

        return $statusNames[$this->status_id] ?? 'Unknown';
    }
}
