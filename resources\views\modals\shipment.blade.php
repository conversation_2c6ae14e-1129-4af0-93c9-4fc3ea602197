<div id="preview-shipment-el">
  @csrf
  <x-lg-modal>
    <x-slot name="class">preview-shipment-modal</x-slot>
    <x-slot name="title">Shipment</x-slot>

    <div style="text-transform: uppercase!important;">
      <div class="shipment-print print" id="shipment-print" v-if="shipment" style="max-width: 400px; margin: auto;">
        
        <!-- Header -->
        <div class="text-center mb-3">
          <h3 class="mb-1">{{ config('app.name', 'Company Name') }}</h3>
          <p class="mb-0 text-muted">{{ auth()->user()->branch->address ?? 'Company Address' }}</p>
          <p class="mb-0 text-muted">Phone: {{ auth()->user()->branch->phone ?? '+****************' }}</p>
        </div>

        <hr>

        <!-- Shipment Info -->
        <div class="row mb-3">
          <div class="col-6">
            <strong>SHIPMENT #</strong>
            <p v-text="shipment.shipment_id"></p>
          </div>
          <div class="col-6 text-end">
            <strong>DATE</strong>
            <p v-text="formatDate(shipment.created_at)"></p>
          </div>
        </div>

        <!-- Customer Info -->
        <div class="mb-3">
          <strong>SHIP TO:</strong>
          <p class="mb-1" v-text="shipment.customer_name"></p>
          <p class="mb-0 text-muted" v-if="shipment.shipping_address" v-text="shipment.shipping_address"></p>
        </div>

        <!-- Tracking Info -->
        <div class="row mb-3" v-if="shipment.tracking_number || shipment.carrier">
          <div class="col-6" v-if="shipment.tracking_number">
            <strong>TRACKING</strong>
            <p v-text="shipment.tracking_number"></p>
          </div>
          <div class="col-6" v-if="shipment.carrier">
            <strong>CARRIER</strong>
            <p v-text="shipment.carrier"></p>
          </div>
        </div>

        <hr>

        <!-- Items -->
        <div class="mb-3">
          <strong>ITEMS:</strong>
          <table class="table table-sm">
            <thead>
              <tr>
                <th>Item</th>
                <th class="text-end">Qty</th>
                <th class="text-end">Rate</th>
                <th class="text-end">Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="sale in shipment.sales" :key="sale.id">
                <td>
                  <div v-text="sale.product_name"></div>
                  <small class="text-muted" v-text="sale.unit_name"></small>
                </td>
                <td class="text-end" v-text="parseFloat(sale.quantity).toFixed(2)"></td>
                <td class="text-end" v-text="formatMoney(sale.selling_price)"></td>
                <td class="text-end" v-text="formatMoney(sale.selling_price * sale.quantity)"></td>
              </tr>
            </tbody>
          </table>
        </div>

        <hr>

        <!-- Totals -->
        <div class="row">
          <div class="col-6">
            <strong>SUBTOTAL:</strong>
          </div>
          <div class="col-6 text-end" v-text="formatMoney(shipment.sub_total)"></div>
        </div>
        
        <div class="row" v-if="shipment.discount > 0">
          <div class="col-6">
            <strong>DISCOUNT:</strong>
          </div>
          <div class="col-6 text-end text-danger" v-text="'-' + formatMoney(shipment.discount)"></div>
        </div>
        
        <div class="row" v-if="shipment.vat > 0">
          <div class="col-6">
            <strong>VAT:</strong>
          </div>
          <div class="col-6 text-end" v-text="formatMoney(shipment.vat)"></div>
        </div>
        
        <div class="row border-top pt-2">
          <div class="col-6">
            <strong>TOTAL:</strong>
          </div>
          <div class="col-6 text-end">
            <strong v-text="formatMoney(shipment.amount_total)"></strong>
          </div>
        </div>

        <!-- Special Instructions -->
        <div class="mt-3" v-if="shipment.special_instructions">
          <strong>SPECIAL INSTRUCTIONS:</strong>
          <p v-text="shipment.special_instructions"></p>
        </div>

        <!-- Footer -->
        <div class="text-center mt-4">
          <small class="text-muted">
            Prepared by: <span v-text="shipment.prepared_by"></span><br>
            <span v-text="formatDate(shipment.created_at)"></span>
          </small>
        </div>

      </div>
    </div>

    <x-slot name="footer">
      <div v-if="shipment">
        <button class="btn btn-sm btn-soft-primary" onclick="extractPrint('shipment-print')" href="javascript:;">
          <i class="bi-printer me-1"></i> PRINT
        </button>
        <button v-if="auto" class="btn btn-sm btn-soft-primary" onclick="createPDFfromHTML('shipment-print')" href="javascript:;">
          <i class="bi-file-earmark-pdf me-1"></i> PDF
        </button>
        <a :href="'/shipments/' + shipment.id" class="btn btn-sm btn-soft-primary">
          <i class="bi-eye me-1"></i> MORE
        </a>
      </div>
    </x-slot>
  </x-lg-modal>
</div>

@push('scripts')
<script type="text/javascript">
new Vue({
  el: "#preview-shipment-el",
  data(){
    return{
      shipment: null,
      auto: false,
    }
  },

  methods: {
    getShipment(id) {
      axios.get('/ajax-shipment/' + id).then(res => {
        this.shipment = res.data;
        if(this.auto) {
          setTimeout(() => { 
            window.extractPrint('shipment-print');
          }, 2000);
          this.auto = false;
        }
      }) 
    },

    formatMoney(amount) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(amount || 0);
    },

    formatDate(dateString) {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    },

    formatNumber(num) {
      return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,");
    },
  },

  created(){
    var $shipment = @json(session("shipmentable") ?? '');
    if($shipment) {
      this.auto = true;
      this.getShipment($shipment.id);
    }
    
    $("body").on("click", ".preview-shipment-btn", (e) => {
      var id = $(e.target).attr("data-id");
      this.getShipment(id);
      var auto = $(e.target).attr("data-auto");
      if(auto){
        this.auto = true;
      }
    })
  }
});
</script>
@endpush
