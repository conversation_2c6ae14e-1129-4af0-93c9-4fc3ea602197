<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

use App\Traits\CreatedUpdatedTrait;
use Spatie\MediaLibrary\HasMedia;
use <PERSON>tie\MediaLibrary\InteractsWithMedia;

class LeaveRequest extends Model implements HasMedia
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use InteractsWithMedia;

    protected $connection = "tenant";

    protected $fillable = [
        'phone',
        'description',
        'date_from',
        'date_to',
        'days',
        'approve_comment',
        'approved_by',
        'status',
        'leave_id',
        'created_by',
        'updated_by',
        'leave_id',
        'employee_id',
    ];

    protected $searchableFields = ['*'];

    public function leave()
    {
        return $this->belongsTo(Leave::class);
    }


    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }



}
