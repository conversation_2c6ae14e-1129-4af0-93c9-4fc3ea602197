@php
    $editing = isset($stock) && $stock->exists;
    $products = $products ?? collect();
    $suppliers = $suppliers ?? collect();
    $locations = $locations ?? collect();
@endphp


<!-- Quantity & Pricing Section -->
<div class="form-section">
    <div class="form-section-title">
        <i class="bi bi-calculator"></i>Quantity & Pricing
    </div>
    <div class="row g-3">
        <div class="col-md-4">
            <label for="quantity" class="form-label required-field">Quantity</label>
            <input type="number"
                   name="quantity"
                   id="quantity"
                   class="form-control @error('quantity') is-invalid @enderror"
                   value="{{ old('quantity', $editing ? $stock->quantity : '') }}"
                   placeholder="Enter quantity"
                   min="0"
                   step="0.01"
                   required>
            @error('quantity')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="col-md-4">
            <label for="buying_price" class="form-label">Buying Price</label>
            <div class="input-group">
                <span class="input-group-text">$</span>
                <input type="number"
                       name="buying_price"
                       id="buying_price"
                       class="form-control @error('buying_price') is-invalid @enderror"
                       value="{{ old('buying_price', $editing ? $stock->buying_price : '') }}"
                       placeholder="0.00"
                       min="0"
                       step="0.01">
            </div>
            @error('buying_price')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="col-md-4">
            <label for="selling_price" class="form-label">Selling Price</label>
            <div class="input-group">
                <span class="input-group-text">$</span>
                <input type="number"
                       name="selling_price"
                       id="selling_price"
                       class="form-control @error('selling_price') is-invalid @enderror"
                       value="{{ old('selling_price', $editing ? $stock->selling_price : '') }}"
                       placeholder="0.00"
                       min="0"
                       step="0.01">
            </div>
            @error('selling_price')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
    </div>
</div>

<!-- Location & Additional Information Section -->
<div class="form-section">
    <div class="form-section-title">
        <i class="bi bi-geo-alt"></i>Location & Additional Information
    </div>
    <div class="row g-3">
        <div class="col-md-4">
            <label for="location" class="form-label">Storage Location</label>
            <input type="text"
                   name="location"
                   id="location"
                   class="form-control @error('location') is-invalid @enderror"
                   value="{{ old('location', $editing ? $stock->location : '') }}"
                   placeholder="e.g., Warehouse A, Shelf 1">
            @error('location')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="col-md-4">
            <label for="expires_at" class="form-label">Expiry Date</label>
            <input type="date"
                   name="expires_at"
                   id="expires_at"
                   class="form-control @error('expires_at') is-invalid @enderror"
                   value="{{ old('expires_at', $editing && $stock->expires_at ? $stock->expires_at->format('Y-m-d') : '') }}">
            @error('expires_at')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <div class="col-md-4">
            <label for="supplier_id" class="form-label">Supplier</label>
            <select name="supplier_id" id="supplier_id" class="form-select @error('supplier_id') is-invalid @enderror">
                <option value="">Select a supplier...</option>
                @foreach($suppliers as $supplier)
                    <option value="{{ $supplier->id }}"
                            {{ old('supplier_id', $editing ? $stock->supplier_id : '') == $supplier->id ? 'selected' : '' }}>
                        {{ $supplier->name }}
                    </option>
                @endforeach
            </select>
            @error('supplier_id')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <div class="col-12">
            <label for="description" class="form-label">Description</label>
            <textarea name="description"
                      id="description"
                      class="form-control @error('description') is-invalid @enderror"
                      rows="3"
                      placeholder="Additional notes or description about this stock item...">{{ old('description', $editing ? $stock->description : '') }}</textarea>
            @error('description')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Auto-calculate total value when quantity or price changes
    function calculateTotalValue() {
        const quantity = parseFloat($('#quantity').val()) || 0;
        const buyingPrice = parseFloat($('#buying_price').val()) || 0;
        const totalValue = quantity * buyingPrice;

        // Show calculated value (you can add a display element for this)
        console.log('Total Value:', totalValue.toFixed(2));
    }

    $('#quantity, #buying_price').on('input', calculateTotalValue);

    // Auto-suggest selling price based on buying price (with markup)
    $('#buying_price').on('input', function() {
        const buyingPrice = parseFloat($(this).val()) || 0;
        const sellingPrice = $('#selling_price').val();

        // If selling price is empty, suggest a 20% markup
        if (!sellingPrice && buyingPrice > 0) {
            const suggestedPrice = (buyingPrice * 1.2).toFixed(2);
            $('#selling_price').val(suggestedPrice);
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        let isValid = true;



        if (!$('#quantity').val() || parseFloat($('#quantity').val()) <= 0) {
            $('#quantity').addClass('is-invalid');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields correctly.');
        }
    });

    // Remove validation errors on input
    $('.form-control, .form-select').on('input change', function() {
        $(this).removeClass('is-invalid');
    });
});
</script>
@endpush
