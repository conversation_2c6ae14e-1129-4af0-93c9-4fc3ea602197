<?php

namespace App\Http\Controllers;

use App\Models\Bom;
use App\Models\Branch;
use App\Models\Product;
use App\Models\ProductionOrder;
use App\Models\ProductionOrderItem;
use App\Models\JournalEntry;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProductionOrderController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', ProductionOrder::class);

        $search = $request->get('search', '');
        $productId = $request->get('product_id', '');
        $status = $request->get('status', '');
        $branchId = $request->get('branch_id', '');

        $productionOrders = ProductionOrder::search($search);
        
        if ($productId) {
            $productionOrders->where('product_id', $productId);
        }
        
        if ($status) {
            $productionOrders->where('status', $status);
        }
        
        if ($branchId) {
            $productionOrders->where('branch_id', $branchId);
        }
        
        $productionOrders = $productionOrders->latest()
            ->paginate()
            ->withQueryString();

        $products = Product::orderBy('name')
            ->pluck('name', 'id');
            
        $branches = Branch::orderBy('name')
            ->pluck('name', 'id');

        return view('app.production_orders.index', compact(
            'productionOrders', 
            'search', 
            'products',
            'branches',
            'productId',
            'status',
            'branchId'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', ProductionOrder::class);

        $products = Product::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');
            
        $branches = Branch::orderBy('name')
            ->pluck('name', 'id');

        return view('app.production_orders.create', compact('products', 'branches'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', ProductionOrder::class);

        $validated = $request->validate([
            'description' => 'nullable|string',
            'product_id' => 'required|tenant_exists:products,id',
            'bom_id' => 'required|tenant_exists:boms,id',
            'quantity' => 'required|numeric|min:0.01',
            'unit_of_measure' => 'required|string|max:255',
            'planned_start_date' => 'required|date',
            'planned_end_date' => 'required|date|after_or_equal:planned_start_date',
            'status' => 'required|in:draft,planned,in_progress,completed,cancelled',
            'branch_id' => 'nullable|tenant_exists:branches,id',
        ]);

        // Generate order number
        $latestOrder = ProductionOrder::latest()->first();
        $orderNumber = 'PO-' . date('Y') . '-' . sprintf('%06d', $latestOrder ? ($latestOrder->id + 1) : 1);
        $validated['order_number'] = $orderNumber;
        
        // Get BOM
        $bom = Bom::findOrFail($validated['bom_id']);
        
        // Calculate planned cost
        $plannedCost = $bom->total_cost * ($validated['quantity'] / $bom->quantity);
        $validated['planned_cost'] = $plannedCost;
        
        // Set actual cost and variance to 0 initially
        $validated['actual_cost'] = 0;
        $validated['cost_variance'] = 0;

        DB::beginTransaction();
        
        try {
            // Create production order
            $productionOrder = ProductionOrder::create($validated);
            
            // Create production order items from BOM items
            foreach ($bom->bomItems as $bomItem) {
                // Calculate item quantity based on production quantity
                $itemQuantity = $bomItem->quantity * ($validated['quantity'] / $bom->quantity);
                
                // Calculate item cost
                $itemCost = $bomItem->total_cost * ($validated['quantity'] / $bom->quantity);
                
                ProductionOrderItem::create([
                    'production_order_id' => $productionOrder->id,
                    'product_id' => $bomItem->product_id,
                    'bom_item_id' => $bomItem->id,
                    'item_type' => $bomItem->item_type,
                    'planned_quantity' => $itemQuantity,
                    'actual_quantity' => 0,
                    'quantity_variance' => 0,
                    'unit_of_measure' => $bomItem->unit_of_measure,
                    'planned_cost' => $itemCost,
                    'actual_cost' => 0,
                    'cost_variance' => 0,
                    'status' => 'pending',
                ]);
            }
            
            DB::commit();
            
            return redirect()
                ->route('production-orders.show', $productionOrder)
                ->withSuccess(__('crud.common.created'));
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ProductionOrder  $productionOrder
     * @return \Illuminate\Http\Response
     */
    public function show(ProductionOrder $productionOrder)
    {
        $this->authorize('view', $productionOrder);

        $productionOrderItems = $productionOrder->productionOrderItems()
            ->with('product')
            ->get();

        return view('app.production_orders.show', compact(
            'productionOrder', 
            'productionOrderItems'
        ));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\ProductionOrder  $productionOrder
     * @return \Illuminate\Http\Response
     */
    public function edit(ProductionOrder $productionOrder)
    {
        $this->authorize('update', $productionOrder);
        
        if (in_array($productionOrder->status, ['completed', 'cancelled'])) {
            return redirect()
                ->route('production-orders.show', $productionOrder)
                ->withError('Completed or cancelled production orders cannot be edited.');
        }

        $products = Product::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');
            
        $boms = Bom::where('product_id', $productionOrder->product_id)
            ->where('status', 'active')
            ->orderBy('name')
            ->pluck('name', 'id');
            
        $branches = Branch::orderBy('name')
            ->pluck('name', 'id');

        return view('app.production_orders.edit', compact(
            'productionOrder', 
            'products', 
            'boms',
            'branches'
        ));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ProductionOrder  $productionOrder
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ProductionOrder $productionOrder)
    {
        $this->authorize('update', $productionOrder);
        
        if (in_array($productionOrder->status, ['completed', 'cancelled'])) {
            return redirect()
                ->route('production-orders.show', $productionOrder)
                ->withError('Completed or cancelled production orders cannot be edited.');
        }

        $validated = $request->validate([
            'description' => 'nullable|string',
            'planned_start_date' => 'required|date',
            'planned_end_date' => 'required|date|after_or_equal:planned_start_date',
            'status' => 'required|in:draft,planned,in_progress,completed,cancelled',
            'branch_id' => 'nullable|tenant_exists:branches,id',
            'actual_start_date' => 'nullable|date',
            'actual_end_date' => 'nullable|date|after_or_equal:actual_start_date',
        ]);
        
        // If status is changing to completed, set actual end date
        if ($validated['status'] === 'completed' && $productionOrder->status !== 'completed') {
            $validated['actual_end_date'] = $validated['actual_end_date'] ?? now()->format('Y-m-d');
            
            // Check if all items are completed
            $pendingItems = $productionOrder->productionOrderItems()
                ->where('status', '!=', 'completed')
                ->count();
                
            if ($pendingItems > 0) {
                return redirect()
                    ->back()
                    ->withInput()
                    ->withError('All production order items must be completed before completing the order.');
            }
            
            // Set approved_by and approved_at
            $validated['approved_by'] = auth()->id();
            $validated['approved_at'] = now();
        }
        
        // If status is changing to in_progress, set actual start date
        if ($validated['status'] === 'in_progress' && $productionOrder->status !== 'in_progress') {
            $validated['actual_start_date'] = $validated['actual_start_date'] ?? now()->format('Y-m-d');
        }

        $productionOrder->update($validated);

        return redirect()
            ->route('production-orders.show', $productionOrder)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ProductionOrder  $productionOrder
     * @return \Illuminate\Http\Response
     */
    public function destroy(ProductionOrder $productionOrder)
    {
        $this->authorize('delete', $productionOrder);
        
        if ($productionOrder->status !== 'draft') {
            return redirect()
                ->route('production-orders.index')
                ->withError('Only draft production orders can be deleted.');
        }

        DB::beginTransaction();
        
        try {
            // Delete production order items
            $productionOrder->productionOrderItems()->delete();
            
            // Delete production order
            $productionOrder->delete();
            
            DB::commit();
            
            return redirect()
                ->route('production-orders.index')
                ->withSuccess(__('crud.common.removed'));
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
    
    /**
     * Show the form for updating production order items.
     *
     * @param  \App\Models\ProductionOrder  $productionOrder
     * @return \Illuminate\Http\Response
     */
    public function editItems(ProductionOrder $productionOrder)
    {
        $this->authorize('update', $productionOrder);
        
        if (in_array($productionOrder->status, ['draft', 'completed', 'cancelled'])) {
            return redirect()
                ->route('production-orders.show', $productionOrder)
                ->withError('Only planned or in-progress production orders can have items updated.');
        }

        $productionOrderItems = $productionOrder->productionOrderItems()
            ->with('product')
            ->get();

        return view('app.production_orders.edit_items', compact(
            'productionOrder', 
            'productionOrderItems'
        ));
    }
    
    /**
     * Update production order items.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ProductionOrder  $productionOrder
     * @return \Illuminate\Http\Response
     */
    public function updateItems(Request $request, ProductionOrder $productionOrder)
    {
        $this->authorize('update', $productionOrder);
        
        if (in_array($productionOrder->status, ['draft', 'completed', 'cancelled'])) {
            return redirect()
                ->route('production-orders.show', $productionOrder)
                ->withError('Only planned or in-progress production orders can have items updated.');
        }

        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|tenant_exists:production_order_items,id',
            'items.*.actual_quantity' => 'required|numeric|min:0',
            'items.*.actual_cost' => 'required|numeric|min:0',
            'items.*.status' => 'required|in:pending,issued,completed',
            'items.*.notes' => 'nullable|string',
        ]);

        DB::beginTransaction();
        
        try {
            $totalActualCost = 0;
            
            foreach ($validated['items'] as $item) {
                $productionOrderItem = ProductionOrderItem::find($item['id']);
                
                // Calculate variance
                $quantityVariance = $item['actual_quantity'] - $productionOrderItem->planned_quantity;
                $costVariance = $item['actual_cost'] - $productionOrderItem->planned_cost;
                
                $productionOrderItem->update([
                    'actual_quantity' => $item['actual_quantity'],
                    'quantity_variance' => $quantityVariance,
                    'actual_cost' => $item['actual_cost'],
                    'cost_variance' => $costVariance,
                    'status' => $item['status'],
                    'notes' => $item['notes'],
                ]);
                
                $totalActualCost += $item['actual_cost'];
            }
            
            // Update production order actual cost and variance
            $costVariance = $totalActualCost - $productionOrder->planned_cost;
            $productionOrder->update([
                'actual_cost' => $totalActualCost,
                'cost_variance' => $costVariance,
            ]);
            
            DB::commit();
            
            return redirect()
                ->route('production-orders.show', $productionOrder)
                ->withSuccess('Production order items have been updated successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
    
    /**
     * Complete the production order and create journal entries.
     *
     * @param  \App\Models\ProductionOrder  $productionOrder
     * @return \Illuminate\Http\Response
     */
    public function complete(ProductionOrder $productionOrder)
    {
        $this->authorize('update', $productionOrder);
        
        if ($productionOrder->status !== 'in_progress') {
            return redirect()
                ->route('production-orders.show', $productionOrder)
                ->withError('Only in-progress production orders can be completed.');
        }
        
        // Check if all items are completed
        $pendingItems = $productionOrder->productionOrderItems()
            ->where('status', '!=', 'completed')
            ->count();
            
        if ($pendingItems > 0) {
            return redirect()
                ->route('production-orders.show', $productionOrder)
                ->withError('All production order items must be completed before completing the order.');
        }

        DB::beginTransaction();
        
        try {
            // Create journal entry for production
            $this->createProductionJournalEntry($productionOrder);
            
            // Update production order status
            $productionOrder->update([
                'status' => 'completed',
                'actual_end_date' => now()->format('Y-m-d'),
                'approved_by' => auth()->id(),
                'approved_at' => now(),
            ]);
            
            DB::commit();
            
            return redirect()
                ->route('production-orders.show', $productionOrder)
                ->withSuccess('Production order has been completed successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
    
    /**
     * Create a journal entry for production.
     *
     * @param  \App\Models\ProductionOrder  $productionOrder
     * @return void
     */
    private function createProductionJournalEntry(ProductionOrder $productionOrder)
    {
        // Get accounts
        $rawMaterialsAccount = Account::where('code', 'like', '1%')
            ->where('name', 'like', '%Raw Materials%')
            ->first();
            
        $workInProgressAccount = Account::where('code', 'like', '1%')
            ->where('name', 'like', '%Work in Progress%')
            ->first();
            
        $finishedGoodsAccount = Account::where('code', 'like', '1%')
            ->where('name', 'like', '%Finished Goods%')
            ->first();
            
        if (!$rawMaterialsAccount || !$workInProgressAccount || !$finishedGoodsAccount) {
            throw new \Exception('Required inventory accounts not found.');
        }
        
        // Generate entry number
        $latestEntry = JournalEntry::latest()->first();
        $entryNumber = 'JE-' . date('Y') . '-' . sprintf('%06d', $latestEntry ? ($latestEntry->id + 1) : 1);
        
        // Create journal entry
        $journalEntry = JournalEntry::create([
            'entry_number' => $entryNumber,
            'entry_date' => now()->format('Y-m-d'),
            'reference_number' => 'PROD-' . $productionOrder->order_number,
            'description' => 'Production completion: ' . $productionOrder->product->name,
            'entry_type' => 'system',
            'status' => 'posted',
            'posted_by' => auth()->id(),
            'posted_at' => now(),
        ]);
        
        // Get material items
        $materialItems = $productionOrder->productionOrderItems()
            ->where('item_type', 'material')
            ->get();
            
        // Get labor and overhead items
        $laborOverheadItems = $productionOrder->productionOrderItems()
            ->whereIn('item_type', ['labor', 'overhead'])
            ->get();
            
        // Calculate costs
        $materialCost = $materialItems->sum('actual_cost');
        $laborOverheadCost = $laborOverheadItems->sum('actual_cost');
        $totalCost = $productionOrder->actual_cost;
        
        // Create journal entry lines
        
        // Credit raw materials
        if ($materialCost > 0) {
            $journalEntry->journalEntryLines()->create([
                'account_id' => $rawMaterialsAccount->id,
                'description' => 'Raw materials used in production',
                'debit' => 0,
                'credit' => $materialCost,
            ]);
        }
        
        // Credit labor and overhead
        if ($laborOverheadCost > 0) {
            $journalEntry->journalEntryLines()->create([
                'account_id' => $workInProgressAccount->id,
                'description' => 'Labor and overhead used in production',
                'debit' => 0,
                'credit' => $laborOverheadCost,
            ]);
        }
        
        // Debit finished goods
        $journalEntry->journalEntryLines()->create([
            'account_id' => $finishedGoodsAccount->id,
            'description' => 'Finished goods produced: ' . $productionOrder->product->name,
            'debit' => $totalCost,
            'credit' => 0,
        ]);
    }
    
    /**
     * Get BOMs for a product.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getBoms(Request $request)
    {
        $productId = $request->get('product_id');
        
        $boms = Bom::where('product_id', $productId)
            ->where('status', 'active')
            ->orderBy('name')
            ->get(['id', 'name', 'version', 'is_default']);
            
        return response()->json($boms);
    }
}
