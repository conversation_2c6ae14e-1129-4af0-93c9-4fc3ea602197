@extends('layouts.app')

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">folder</x-slot>
        <x-slot name="title">{{ $category->name }}</x-slot>
        <x-slot name="subtitle">Category Details</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('categories.index') }}">Categories</a></li>
            <li class="breadcrumb-item active" aria-current="page">View</li>
        </x-slot>
        <x-slot name="controls">
            @can('update', $category)
            <a href="{{ route('categories.edit', $category) }}" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> Edit Category
            </a>
            @endcan
            <a href="{{ route('categories.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Categories
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <div class="col-lg-8 mb-4">
            <!-- Card -->
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-info-circle me-2"></i>Category Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-sm-6">
                            <label class="form-label">@lang('crud.categories.inputs.name')</label>
                            <div class="mb-2">
                                <h5>{{ $category->name ?? '-' }}</h5>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label">@lang('crud.categories.inputs.status_id')</label>
                            <div class="mb-2">
                                <span class="badge bg-{{ optional($category->status)->name == 'Active' ? 'success' : 'secondary' }}">
                                    {{ optional($category->status)->name ?? '-' }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-sm-6">
                            <label class="form-label">Group</label>
                            <div class="mb-2">
                                <span class="badge bg-soft-primary">{{ $category->applied_to ?? '-' }}</span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label">Created</label>
                            <div class="mb-2">
                                <span>{{ $category->created_at->format('F d, Y') }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="form-label">@lang('crud.categories.inputs.description')</label>
                        <div class="mb-2">
                            <p>{{ $category->description ?? 'No description available.' }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Card -->
        </div>

        <div class="col-lg-4 mb-4">
            <!-- Card -->
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-gear me-2"></i>Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('categories.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i> Back to Categories
                        </a>

                        @can('update', $category)
                        <a href="{{ route('categories.edit', $category) }}" class="btn btn-primary">
                            <i class="bi bi-pencil-square me-1"></i> Edit Category
                        </a>
                        @endcan

                        @can('create', App\Models\Category::class)
                        <a href="{{ route('categories.create') }}" class="btn btn-outline-success">
                            <i class="bi bi-plus-circle me-1"></i> Create New Category
                        </a>
                        @endcan

                        @can('delete', $category)
                        <form action="{{ route('categories.destroy', $category) }}" method="POST" onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')">
                            @csrf @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="bi bi-trash me-1"></i> Delete Category
                            </button>
                        </form>
                        @endcan
                    </div>
                </div>
            </div>
            <!-- End Card -->
        </div>
    </div>

    <!-- Related Products Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-header-title">
                <i class="bi bi-box-seam me-2"></i>Related Products
            </h5>
        </div>
        <div class="card-body">
            @if(count($category->products ?? []) > 0)
                <div class="table-responsive">
                    <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                        <thead class="thead-light">
                            <tr>
                                <th>Product Name</th>
                                <th>SKU</th>
                                <th>Price</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($category->products ?? [] as $product)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <img class="avatar avatar-sm" src="{{ $product->image ?? asset('assets/img/160x160/img1.jpg') }}" alt="Image">
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h5 class="text-inherit mb-0">{{ $product->name }}</h5>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ $product->barcode }}</td>
                                <td>{{ _money($product->selling_price) }}</td>
                                <td>
                                    <span class="badge bg-{{ optional($product->status)->name == 'Active' ? 'success' : 'secondary' }}">
                                        {{ optional($product->status)->name ?? '-' }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ route('products.show', $product) }}" class="btn btn-white btn-sm">
                                        <i class="bi-eye me-1"></i> View
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="bi bi-box text-muted" style="font-size: 3rem;"></i>
                    <p class="mt-2">No products in this category</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
