@php $editing = isset($location) && $location->exists; @endphp

<div class="row">
    <!-- Basic Information Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-header-title">
                    <i class="bi bi-geo-alt me-2"></i>Basic Information
                </h5>
            </div>
            <div class="card-body">
                <!-- Location Name -->
                <div class="mb-3">
                    <label for="name" class="form-label">Location Name <span class="text-danger">*</span></label>
                    <input 
                        type="text" 
                        name="name" 
                        id="name" 
                        class="form-control @error('name') is-invalid @enderror" 
                        value="{{ old('name', ($editing ? $location->name : '')) }}"
                        placeholder="Enter location name"
                        required
                    >
                    @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">Enter a descriptive name for this location</small>
                </div>

                <!-- Location Code -->
                <div class="mb-3">
                    <label for="code" class="form-label">Location Code</label>
                    <input 
                        type="text" 
                        name="code" 
                        id="code" 
                        class="form-control @error('code') is-invalid @enderror" 
                        value="{{ old('code', ($editing ? $location->code : '')) }}"
                        placeholder="Enter location code (optional)"
                        maxlength="50"
                    >
                    @error('code')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">Optional unique code for this location (e.g., LOC001)</small>
                </div>

                <!-- Status -->
                <div class="mb-3">
                    <label for="status_id" class="form-label">Status <span class="text-danger">*</span></label>
                    <select 
                        name="status_id" 
                        id="status_id" 
                        class="form-select @error('status_id') is-invalid @enderror"
                        required
                    >
                        <option value="">Select Status</option>
                        @foreach($statuses as $value => $label)
                            <option 
                                value="{{ $value }}" 
                                {{ old('status_id', ($editing ? $location->status_id : '1')) == $value ? 'selected' : '' }}
                            >
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                    @error('status_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">Set the current status of this location</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Description Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-header-title">
                    <i class="bi bi-card-text me-2"></i>Description
                </h5>
            </div>
            <div class="card-body">
                <div>
                    <label for="description" class="form-label">Description</label>
                    <textarea 
                        name="description" 
                        id="description" 
                        class="form-control @error('description') is-invalid @enderror" 
                        rows="8" 
                        placeholder="Enter location description (optional)"
                        maxlength="1000"
                    >{{ old('description', ($editing ? $location->description : '')) }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">Provide additional details about this location (max 1000 characters)</small>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Auto-generate code from name if code is empty
    $('#name').on('input', function() {
        var name = $(this).val();
        var code = $('#code').val();
        
        if (!code && name) {
            // Generate code from name (first 3 letters + 3 digits)
            var generatedCode = name.substring(0, 3).toUpperCase() + '001';
            $('#code').attr('placeholder', 'Suggested: ' + generatedCode);
        }
    });

    // Character counter for description
    $('#description').on('input', function() {
        var maxLength = 1000;
        var currentLength = $(this).val().length;
        var remaining = maxLength - currentLength;
        
        var counterText = remaining + ' characters remaining';
        if (remaining < 0) {
            counterText = Math.abs(remaining) + ' characters over limit';
        }
        
        // Update or create counter
        var counter = $(this).siblings('.char-counter');
        if (counter.length === 0) {
            $(this).after('<small class="form-text text-muted char-counter">' + counterText + '</small>');
        } else {
            counter.text(counterText);
            counter.toggleClass('text-danger', remaining < 0);
            counter.toggleClass('text-muted', remaining >= 0);
        }
    });
});
</script>
@endpush
