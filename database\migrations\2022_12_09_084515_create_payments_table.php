<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->decimal('amount', 12, 2)->default(0)->nullable();
            $table->decimal('balance', 12, 2)->default(0)->nullable();
            $table->text('comment')->nullable();
            $table->text('description')->nullable();
            $table->unsignedBigInteger('paymentable_id');
            $table->string('paymentable_type');
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->index('paymentable_id');
            $table->index('paymentable_type');
            $table->string('reference_no')->nullable();
            $table->unsignedBigInteger('currency_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payments');
    }
};


/**
 * 
 * Acount NumberYear- month
 * Source/ Account
 * Date 
 * Description
 * Reference 
 * Posting
 * Postioning/ID settings
 * Depts
 * Credit
 * Net Change 
 * Balance
 */