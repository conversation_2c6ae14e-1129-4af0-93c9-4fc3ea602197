<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Unit;

use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UserUnitsTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create(['email' => '<EMAIL>']);

        Sanctum::actingAs($user, [], 'web');

        $this->seed(\Database\Seeders\PermissionsSeeder::class);

        $this->withoutExceptionHandling();
    }

    /**
     * @test
     */
    public function it_gets_user_units()
    {
        $user = User::factory()->create();
        $units = Unit::factory()
            ->count(2)
            ->create([
                'updated_by' => $user->id,
            ]);

        $response = $this->getJson(route('api.users.units.index', $user));

        $response->assertOk()->assertSee($units[0]->name);
    }

    /**
     * @test
     */
    public function it_stores_the_user_units()
    {
        $user = User::factory()->create();
        $data = Unit::factory()
            ->make([
                'updated_by' => $user->id,
            ])
            ->toArray();

        $response = $this->postJson(
            route('api.users.units.store', $user),
            $data
        );

        unset($data['created_by']);
        unset($data['updated_by']);

        $this->assertDatabaseHas('units', $data);

        $response->assertStatus(201)->assertJsonFragment($data);

        $unit = Unit::latest('id')->first();

        $this->assertEquals($user->id, $unit->updated_by);
    }
}
