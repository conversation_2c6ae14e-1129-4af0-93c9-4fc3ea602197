<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\EmployeeTimeRecord;
use App\Models\EmployeeTask;
use App\Models\WorkHoursConfiguration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TimeTrackingController extends Controller
{
    /**
     * Display the time tracking dashboard.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $employee = $user->employee;

        if (!$employee) {
            return redirect('/')->with('error', 'Employee profile not found. Please contact administrator.');
        }

        $today = today();
        $todayRecord = EmployeeTimeRecord::getTodayRecord($employee->id);
        
        // Get recent time records
        $recentRecords = $employee->timeRecords()
            ->with('tasks')
            ->orderBy('work_date', 'desc')
            ->limit(10)
            ->get();

        // Get current month summary
        $currentMonth = now()->month;
        $currentYear = now()->year;
        
        $monthSummary = [
            'total_hours' => $employee->timeRecords()
                ->whereYear('work_date', $currentYear)
                ->whereMonth('work_date', $currentMonth)
                ->sum('total_hours'),
            'regular_hours' => $employee->getRegularHoursForMonth($currentYear, $currentMonth),
            'overtime_hours' => $employee->getOvertimeHoursForMonth($currentYear, $currentMonth),
            'days_worked' => $employee->timeRecords()
                ->whereYear('work_date', $currentYear)
                ->whereMonth('work_date', $currentMonth)
                ->where('status', 'checked_out')
                ->count(),
        ];

        $workConfig = WorkHoursConfiguration::getActive();

        return view('app.time-tracking.index', compact(
            'employee', 
            'todayRecord', 
            'recentRecords', 
            'monthSummary', 
            'workConfig'
        ));
    }

    /**
     * Handle check-in.
     */
    public function checkIn(Request $request)
    {
        $user = auth()->user();
        $employee = $user->employee;

        if (!$employee) {
            return response()->json(['error' => 'Employee profile not found.'], 400);
        }

        try {
            DB::beginTransaction();

            $todayRecord = EmployeeTimeRecord::getTodayRecord($employee->id);
            
            if ($todayRecord && $todayRecord->check_in_time) {
                return response()->json(['error' => 'Already checked in today.'], 400);
            }

            if (!$todayRecord) {
                $todayRecord = EmployeeTimeRecord::getOrCreateTodayRecord($employee->id);
            }

            $todayRecord->checkIn();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Checked in successfully.',
                'check_in_time' => $todayRecord->formatted_check_in,
                'record' => $todayRecord
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to check in: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Handle check-out.
     */
    public function checkOut(Request $request)
    {
        $user = auth()->user();
        $employee = $user->employee;

        if (!$employee) {
            return response()->json(['error' => 'Employee profile not found.'], 400);
        }

        try {
            DB::beginTransaction();

            $todayRecord = EmployeeTimeRecord::getTodayRecord($employee->id);
            
            if (!$todayRecord || !$todayRecord->check_in_time) {
                return response()->json(['error' => 'Must check in first.'], 400);
            }

            if ($todayRecord->check_out_time) {
                return response()->json(['error' => 'Already checked out today.'], 400);
            }

            $todayRecord->checkOut();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Checked out successfully.',
                'check_out_time' => $todayRecord->formatted_check_out,
                'total_hours' => $todayRecord->total_hours,
                'regular_hours' => $todayRecord->regular_hours,
                'overtime_hours' => $todayRecord->overtime_hours,
                'record' => $todayRecord
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to check out: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Add daily comment.
     */
    public function addComment(Request $request)
    {
        $request->validate([
            'comment' => 'required|string|max:1000',
        ]);

        $user = auth()->user();
        $employee = $user->employee;

        if (!$employee) {
            return response()->json(['error' => 'Employee profile not found.'], 400);
        }

        $todayRecord = EmployeeTimeRecord::getOrCreateTodayRecord($employee->id);
        $todayRecord->daily_comment = $request->comment;
        $todayRecord->save();

        return response()->json([
            'success' => true,
            'message' => 'Comment added successfully.',
            'comment' => $todayRecord->daily_comment
        ]);
    }

    /**
     * Add task entry.
     */
    public function addTask(Request $request)
    {
        $request->validate([
            'task_description' => 'required|string|max:255',
            'time_spent' => 'required|numeric|min:0.1|max:24',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'notes' => 'nullable|string|max:500',
        ]);

        $user = auth()->user();
        $employee = $user->employee;

        if (!$employee) {
            return response()->json(['error' => 'Employee profile not found.'], 400);
        }

        try {
            DB::beginTransaction();

            $todayRecord = EmployeeTimeRecord::getOrCreateTodayRecord($employee->id);

            $task = new EmployeeTask([
                'employee_time_record_id' => $todayRecord->id,
                'task_description' => $request->task_description,
                'time_spent' => $request->time_spent,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'notes' => $request->notes,
                'created_by' => auth()->id(),
            ]);

            $task->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Task added successfully.',
                'task' => $task
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to add task: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get time records for a specific date range.
     */
    public function getRecords(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        $user = auth()->user();
        $employee = $user->employee;

        if (!$employee) {
            return response()->json(['error' => 'Employee profile not found.'], 400);
        }

        $records = $employee->timeRecords()
            ->with('tasks')
            ->whereBetween('work_date', [$request->start_date, $request->end_date])
            ->orderBy('work_date', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'records' => $records
        ]);
    }
}
