<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\EmployeeLoan;
use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\EmployeeLoanStoreRequest;
use App\Http\Requests\EmployeeLoanUpdateRequest;

class EmployeeLoanController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', EmployeeLoan::class);

        $search = $request->get('search', '');
        $status = $request->get('status', '');
        $employee_id = $request->get('employee_id', '');

        $loans = EmployeeLoan::with(['employee', 'approvedBy', 'createdBy'])
            ->when($search, function ($query, $search) {
                return $query->where(function ($q) use ($search) {
                    $q->where('loan_reference', 'like', "%{$search}%")
                      ->orWhere('purpose', 'like', "%{$search}%")
                      ->orWhereHas('employee', function ($eq) use ($search) {
                          $eq->where('name', 'like', "%{$search}%");
                      });
                });
            })
            ->when($status, function ($query, $status) {
                return $query->where('status', $status);
            })
            ->when($employee_id, function ($query, $employee_id) {
                return $query->where('employee_id', $employee_id);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        $employees = Employee::orderBy('name')->get();

        return view('employee-loans.index', compact('loans', 'employees', 'search', 'status', 'employee_id'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', EmployeeLoan::class);

        $employees = Employee::orderBy('name')->get();

        return view('employee-loans.create', compact('employees'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\EmployeeLoanStoreRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(EmployeeLoanStoreRequest $request)
    {
        $this->authorize('create', EmployeeLoan::class);

        $validated = $request->validated();
        $validated['created_by'] = auth()->id();
        $validated['loan_reference'] = EmployeeLoan::generateLoanReference();
        $validated['remaining_balance'] = $validated['loan_amount'];

        // Calculate total installments based on principal amount only
        // installment_amount now represents principal payment per month
        $validated['total_installments'] = ceil($validated['loan_amount'] / $validated['installment_amount']);

        // Calculate total interest due over the life of the loan
        if (isset($validated['interest_rate']) && $validated['interest_rate'] > 0) {
            $monthlyInterestRate = $validated['interest_rate'] / 100 / 12;
            $interestType = $validated['interest_type'] ?? 'simple';

            if ($interestType === 'simple') {
                // Simple interest: calculated on original loan amount
                $monthlyInterest = $validated['loan_amount'] * $monthlyInterestRate;
                $validated['total_interest_due'] = $monthlyInterest * $validated['total_installments'];
            } else {
                // Compound interest: calculate month by month
                // UPDATED: installment_amount is principal only, interest calculated separately
                $totalInterest = 0;
                $remainingBalance = $validated['loan_amount'];
                $principalPayment = $validated['installment_amount']; // Principal payment only

                for ($i = 0; $i < $validated['total_installments'] && $remainingBalance > 0; $i++) {
                    $monthlyInterest = $remainingBalance * $monthlyInterestRate;

                    $actualPrincipalPayment = min($principalPayment, $remainingBalance);
                    $totalInterest += $monthlyInterest;
                    $remainingBalance -= $actualPrincipalPayment;
                }

                $validated['total_interest_due'] = $totalInterest;
            }
        } else {
            $validated['total_interest_due'] = 0;
        }

        $loan = EmployeeLoan::create($validated);

        return redirect()
            ->route('employee-loans.show', $loan)
            ->withSuccess('Loan application created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\EmployeeLoan  $employeeLoan
     * @return \Illuminate\Http\Response
     */
    public function show(EmployeeLoan $employeeLoan)
    {
        $this->authorize('view', $employeeLoan);

        $employeeLoan->load(['employee', 'approvedBy', 'createdBy', 'updatedBy']);

        return view('employee-loans.show', compact('employeeLoan'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\EmployeeLoan  $employeeLoan
     * @return \Illuminate\Http\Response
     */
    public function edit(EmployeeLoan $employeeLoan)
    {
        $this->authorize('update', $employeeLoan);

        // Only allow editing if loan is pending
        if ($employeeLoan->status !== 'pending') {
            return redirect()
                ->route('employee-loans.show', $employeeLoan)
                ->withError('Only pending loans can be edited.');
        }

        $employees = Employee::orderBy('name')->get();

        return view('employee-loans.edit', compact('employeeLoan', 'employees'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\EmployeeLoanUpdateRequest  $request
     * @param  \App\Models\EmployeeLoan  $employeeLoan
     * @return \Illuminate\Http\Response
     */
    public function update(EmployeeLoanUpdateRequest $request, EmployeeLoan $employeeLoan)
    {
        $this->authorize('update', $employeeLoan);

        // Only allow updating if loan is pending
        if ($employeeLoan->status !== 'pending') {
            return redirect()
                ->route('employee-loans.show', $employeeLoan)
                ->withError('Only pending loans can be updated.');
        }

        $validated = $request->validated();
        $validated['updated_by'] = auth()->id();
        $validated['remaining_balance'] = $validated['loan_amount'];

        // Recalculate total installments
        $validated['total_installments'] = ceil($validated['loan_amount'] / $validated['installment_amount']);

        $employeeLoan->update($validated);

        return redirect()
            ->route('employee-loans.show', $employeeLoan)
            ->withSuccess('Loan updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EmployeeLoan  $employeeLoan
     * @return \Illuminate\Http\Response
     */
    public function destroy(EmployeeLoan $employeeLoan)
    {
        $this->authorize('delete', $employeeLoan);

        // Only allow deletion if loan is pending or cancelled
        if (!in_array($employeeLoan->status, ['pending', 'cancelled'])) {
            return redirect()
                ->route('employee-loans.index')
                ->withError('Only pending or cancelled loans can be deleted.');
        }

        $employeeLoan->delete();

        return redirect()
            ->route('employee-loans.index')
            ->withSuccess('Loan deleted successfully.');
    }

    /**
     * Approve a loan
     *
     * @param  \App\Models\EmployeeLoan  $employeeLoan
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function approve(EmployeeLoan $employeeLoan, Request $request)
    {
        $this->authorize('approve', $employeeLoan);

        if ($employeeLoan->status !== 'pending') {
            return redirect()
                ->route('employee-loans.show', $employeeLoan)
                ->withError('Only pending loans can be approved.');
        }

        $request->validate([
            'approval_notes' => 'nullable|string|max:1000',
        ]);

        DB::transaction(function () use ($employeeLoan, $request) {
            $employeeLoan->approve(auth()->id(), $request->approval_notes);
            $employeeLoan->activate();

            // Send approval notification
            $this->notificationService->sendLoanApprovalNotification($employeeLoan);
        });

        return redirect()
            ->route('employee-loans.show', $employeeLoan)
            ->withSuccess('Loan approved and activated successfully. Notification sent to employee.');
    }

    /**
     * Cancel a loan
     *
     * @param  \App\Models\EmployeeLoan  $employeeLoan
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function cancel(EmployeeLoan $employeeLoan, Request $request)
    {
        $this->authorize('cancel', $employeeLoan);

        if (!in_array($employeeLoan->status, ['pending', 'approved'])) {
            return redirect()
                ->route('employee-loans.show', $employeeLoan)
                ->withError('Only pending or approved loans can be cancelled.');
        }

        $request->validate([
            'cancellation_reason' => 'required|string|max:1000',
        ]);

        $employeeLoan->cancel($request->cancellation_reason);

        // Send cancellation notification
        $this->notificationService->sendLoanCancellationNotification($employeeLoan);

        return redirect()
            ->route('employee-loans.show', $employeeLoan)
            ->withSuccess('Loan cancelled successfully. Notification sent to employee.');
    }

    /**
     * Get loans for a specific employee (AJAX)
     *
     * @param  \App\Models\Employee  $employee
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEmployeeLoans(Employee $employee)
    {
        $loans = $employee->loans()
            ->select(['id', 'loan_reference', 'loan_amount', 'remaining_balance', 'status', 'created_at'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json($loans);
    }

    /**
     * Get loan statistics
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics()
    {
        $stats = [
            'total_loans' => EmployeeLoan::count(),
            'active_loans' => EmployeeLoan::where('status', 'active')->count(),
            'pending_loans' => EmployeeLoan::where('status', 'pending')->count(),
            'completed_loans' => EmployeeLoan::where('status', 'completed')->count(),
            'total_amount_disbursed' => EmployeeLoan::whereIn('status', ['active', 'completed'])->sum('loan_amount'),
            'total_amount_outstanding' => EmployeeLoan::where('status', 'active')->sum('remaining_balance'),
        ];

        return response()->json($stats);
    }
}
