@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">shield-plus</x-slot>
        <x-slot name="title">Create New Role</x-slot>
        <x-slot name="subtitle">Define a new role with specific permissions</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('roles.index') }}">Roles</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('roles.index') }}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-left me-1"></i> Back to Roles
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Role Create Form -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="card-header-title">
                        <i class="bi bi-plus-circle me-2"></i>Create New Role
                    </h5>
                    <p class="text-muted mb-0">Define a new role with specific permissions for your users</p>
                </div>
                <div class="col-auto">
                    <div class="d-flex gap-2">
                        <a href="{{ route('permissions.index') }}" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-key me-1"></i> Manage Permissions
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <x-form method="POST" action="{{ route('roles.store') }}">
                @include('app.roles.form-inputs')

                <div class="d-flex justify-content-between mt-4 pt-4 border-top">
                    <a href="{{ route('roles.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-x-circle me-1"></i> Cancel
                    </a>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-1"></i> Create Role
                    </button>
                </div>
            </x-form>
        </div>
    </div>

    <!-- Tips Card -->
    <div class="card mt-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">
                        <i class="bi bi-lightbulb me-2"></i>Best Practices
                    </h6>
                    <ul class="text-muted small mb-0">
                        <li>Use descriptive names like "Manager", "Editor", or "Viewer"</li>
                        <li>Start with minimal permissions and add more as needed</li>
                        <li>Test roles with a test user before assigning to real users</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="text-info">
                        <i class="bi bi-info-circle me-2"></i>Permission Groups
                    </h6>
                    <p class="text-muted small mb-0">
                        Permissions are organized by resource type. Use the group select buttons to quickly assign all permissions for a specific resource.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
