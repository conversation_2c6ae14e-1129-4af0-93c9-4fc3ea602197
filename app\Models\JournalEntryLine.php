<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;

class JournalEntryLine extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'journal_entry_id',
        'account_id',
        'description',
        'debit',
        'credit',
        'foreign_debit',
        'foreign_credit',
        'reference',
        'created_by',
        'updated_by',
        'business_type_id',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'debit' => 'decimal:2',
        'credit' => 'decimal:2',
        'foreign_debit' => 'decimal:2',
        'foreign_credit' => 'decimal:2',
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];

    public function journalEntry()
    {
        return $this->belongsTo(JournalEntry::class);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function bankReconciliationItems()
    {
        return $this->hasMany(BankReconciliationItem::class);
    }
}
