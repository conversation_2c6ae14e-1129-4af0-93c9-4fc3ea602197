@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">Accounts</x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Account::class)
            <a href="{{ route('accounts.create') }}" class="btn btn-info btn-sm">
                @lang('crud.common.create') Account
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->
    <div class="card card-table">
        <div class="table-responsive mt-3">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            Name
                        </th>
                        <th class="text-left">
                            Debit
                        </th>
                        <th class="text-left">
                            Credit
                        </th>
                        <th class="text-left">
                            Description
                        </th>
                        <th class="text-center">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($accounts as $account)
                    <tr>
                        <td>
                            <a href="/accounts/{{ $account->id }}"> {{ $account->name ?? '-' }}</a>
                        </td>
                        <td>
                            <span class="text-primary">
                                {{ _money($account->debit ) }}
                            </span>
                        </td>
                        <td>
                            <span class="text-primary">
                                {{ _money($account->credit ) }}
                            </span>
                        </td>
                        <td>{{ $account->description ?? '-' }}</td>
                        <td class="text-center" style="width: 134px;">
                            <div
                                role="group"
                                aria-label="Row Actions"
                                class="btn-group"
                            >
                                @can('update', $account)
                                <a
                                    href="{{ route('accounts.edit', $account) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        <!-- <i class="icon ion-md-create"></i> --> edit
                                    </button>
                                </a>
                                @endcan @can('view', $account)
                                <a
                                    href="{{ route('accounts.show', $account) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        <!-- <i class="icon ion-md-eye"></i> --> view
                                    </button>
                                </a>
                                @endcan @can('delete', $account)
                                <form
                                    action="{{ route('accounts.destroy', $account) }}"
                                    method="POST"
                                    onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                >
                                    @csrf @method('DELETE')
                                    <button
                                        type="submit"
                                        class="btn btn-light text-danger m-1"
                                    >
                                        <!-- <i class="icon ion-md-trash"></i> --> del
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                   
                    @endforelse
                </tbody>
                
            </table>
        </div>
    </div>
    

</div>
@endsection

@push('scripts')
<script>

    $(document).on('ready', function () {
      // INITIALIZATION OF DATERANGEPICKER

      dataTableBtn()  
    });
</script>

@endpush
