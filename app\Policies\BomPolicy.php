<?php

namespace App\Policies;

use App\Models\Bom;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class BomPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo('view-any Bom');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Bom  $bom
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Bom $bom)
    {
        return $user->hasPermissionTo('view Bom');
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo('create Bom');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Bom  $bom
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Bom $bom)
    {
        return $user->hasPermissionTo('update Bom');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Bom  $bom
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Bom $bom)
    {
        return $user->hasPermissionTo('delete Bom');
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Bom  $bom
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, Bom $bom)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Bom  $bom
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, Bom $bom)
    {
        return false;
    }
}
