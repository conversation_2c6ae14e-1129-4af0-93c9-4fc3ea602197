@php $editing = isset($user) @endphp

<!-- User Creation Mode Toggle -->
@if(!$editing)
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">User Creation Method</h5>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="creation_method" id="create_direct" value="direct" checked>
                    <label class="form-check-label" for="create_direct">
                        <i class="bi bi-person-plus me-1"></i>Create User Directly
                    </label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="creation_method" id="send_invitation" value="invitation">
                    <label class="form-check-label" for="send_invitation">
                        <i class="bi bi-envelope me-1"></i>Send Invitation Email
                    </label>
                </div>
                <small class="form-text text-muted">
                    Choose to create the user account directly or send an invitation email for them to create their own account.
                </small>
            </div>
        </div>
    </div>
</div>
@endif

<div class="row">

    <x-inputs.group class="col-sm-12 col-lg-6">
        <x-inputs.text
            name="phone"
            label="Phone"
            :value="old('phone', ($editing ? $user->phone : ''))"
            maxlength="255"
            placeholder="Phone"
            required
            disabled
        ></x-inputs.text>
    </x-inputs.group>

    <x-inputs.group class="col-sm-12 col-lg-6">
        <x-inputs.email
            name="email"
            label="Email"
            :value="old('email', ($editing ? $user->email : ''))"
            maxlength="255"
            placeholder="Email"
            disabled
            required
        ></x-inputs.email>
    </x-inputs.group>

    <x-inputs.group class="col-sm-12 col-lg-6">
        <x-inputs.text
            name="name"
            label="Name"
            :value="old('name', ($editing ? $user->name : ''))"
            maxlength="255"
            placeholder="Name"
            required
        ></x-inputs.text>
    </x-inputs.group>

    <!-- Password field - only for direct creation -->
    <!-- <x-inputs.group class="col-sm-12 col-lg-6" id="password-field">
        <x-inputs.password
            name="password"
            label="Password"
            maxlength="255"
            placeholder="Password"
            :required="!$editing"
        ></x-inputs.password>
    </x-inputs.group> -->

    <!-- Role selection for invitation -->
    @if(!$editing)
    <x-inputs.group class="col-sm-12 col-lg-6" id="role-field" style="display: none;">
        <label for="invitation_role" class="form-label">Role for Invitation</label>
        <select name="invitation_role" id="invitation_role" class="form-select">
            <option value="user">User</option>
            <option value="manager">Manager</option>
            <option value="admin">Admin</option>
        </select>
        <small class="form-text text-muted">The role this user will have when they accept the invitation.</small>
    </x-inputs.group>
    @endif

    <x-inputs.group class="col-sm-12 col-lg-6" id="warehouse-field">
        <x-inputs.select name="warehouse_id" label="Warehouse" required>
            @php $selected = old('warehouse_id', ($editing ? $user->warehouse_id : '')) @endphp
            <option disabled {{ empty($selected) ? 'selected' : '' }}>Please select the Warehouse</option>
            @foreach($warehouses as $value => $label)
            <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }} >{{ $label }}</option>
            @endforeach
            @if($warehouses->isEmpty())
            <option disabled>No warehouses available</option>
            @endif
        </x-inputs.select>
        <small class="form-text text-muted">
            <i class="bi bi-info-circle me-1"></i>
            <span id="warehouse-help-text">The warehouse this user will be assigned to</span>
            @if(config('app.debug'))
            <br><small class="text-warning">Debug: {{ $warehouses->count() }} warehouses available</small>
            @endif
        </small>
    </x-inputs.group>

    <x-inputs.group class="col-sm-12 col-lg-6" id="status-field">
        <x-inputs.select name="status_id" label="Status" required>
            @php $selected = old('status_id', ($editing ? $user->status_id : '')) @endphp
            <option disabled {{ empty($selected) ? 'selected' : '' }}>Please select the Status</option>
            @foreach($statuses as $value => $label)
            <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }} >{{ $label }}</option>
            @endforeach
        </x-inputs.select>
    </x-inputs.group>

    <!-- <x-inputs.group class="col-sm-12 col-lg-6">
        <x-inputs.select name="branch_id" label="Branch" required>
            @php $selected = old('branch_id', ($editing ? $user->branch_id : '')) @endphp
            <option disabled {{ empty($selected) ? 'selected' : '' }}>Please select the Branch</option>
            @foreach($branches as $value => $label)
            <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }} >{{ $label }}</option>
            @endforeach
        </x-inputs.select>
    </x-inputs.group> -->

    <x-inputs.group class="col-sm-12">
        <div
            x-data="imageViewer('{{ $editing ? $user->image : '' }}')"
        >
            <x-inputs.partials.label
                name="name"
                label="Name"
            ></x-inputs.partials.label
            ><br />

            <!-- Show the image -->
            <template x-if="imageUrl">
                <img
                    :src="imageUrl"
                    class="object-cover rounded border border-gray-200"
                    style="width: 100px; height: 100px;"
                />
            </template>

            <!-- Show the gray box when image is not available -->
            <template x-if="!imageUrl">
                <div
                    class="border rounded border-gray-200 bg-gray-100"
                    style="width: 100px; height: 100px;"
                ></div>
            </template>

            <div class="mt-2">
                <input type="file" name="image" id="name" @change="fileChosen" />
            </div>

            @error('name') @include('components.inputs.partials.error')
            @enderror
        </div>
    </x-inputs.group>

    <!-- Invitation Message - only for invitation method -->
    @if(!$editing)
    <x-inputs.group class="col-sm-12" id="invitation-message-field" style="display: none;">
        <label for="invitation_message" class="form-label">Personal Message (Optional)</label>
        <textarea name="invitation_message" id="invitation_message" class="form-control" rows="3"
                  placeholder="Add a personal message to include with the invitation email..."></textarea>
        <small class="form-text text-muted">This message will be included in the invitation email.</small>
    </x-inputs.group>
    @endif

    @if( auth()->user()->isSuperAdmin())
    <div class="form-group col-sm-12 mt-4" id="roles-field">
        <h4>Assign @lang('crud.roles.name')</h4>

        @foreach ($roles as $role)
        <div>
            <x-inputs.checkbox
                id="role{{ $role->id }}"
                name="roles[]"
                label="{{ ucfirst($role->name) }}"
                value="{{ $role->id }}"
                :checked="isset($user) ? $user->hasRole($role) : false"
                :add-hidden-value="false"
            ></x-inputs.checkbox>
        </div>
        @endforeach
    </div>
    @endif
</div>

@if(!$editing)
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const directRadio = document.getElementById('create_direct');
    const invitationRadio = document.getElementById('send_invitation');
    const passwordField = document.getElementById('password-field');
    const roleField = document.getElementById('role-field');
    const invitationMessageField = document.getElementById('invitation-message-field');
    const rolesField = document.getElementById('roles-field');
    const warehouseField = document.getElementById('warehouse-field');
    const statusField = document.getElementById('status-field');
    const warehouseHelpText = document.getElementById('warehouse-help-text');
    const passwordInput = document.querySelector('input[name="password"]');
    const warehouseSelect = document.querySelector('select[name="warehouse_id"]');
    const statusSelect = document.querySelector('select[name="status_id"]');
    const branchSelect = document.querySelector('select[name="branch_id"]');

    function toggleFields() {
        if (directRadio && directRadio.checked) {
            // Direct creation mode
            if (passwordField) passwordField.style.display = 'block';
            if (roleField) roleField.style.display = 'none';
            if (invitationMessageField) invitationMessageField.style.display = 'none';
            if (rolesField) rolesField.style.display = 'block';

            // Show all fields for direct creation
            if (warehouseField) warehouseField.style.display = 'block';
            if (statusField) statusField.style.display = 'block';

            // Update help text for direct creation
            if (warehouseHelpText) {
                warehouseHelpText.textContent = 'The warehouse this user will be assigned to';
            }

            // Make fields required for direct creation
            if (passwordInput) passwordInput.required = true;
            if (warehouseSelect) warehouseSelect.required = true;
            if (statusSelect) statusSelect.required = true;
            if (branchSelect) branchSelect.required = true;
        } else {
            // Invitation mode
            if (passwordField) passwordField.style.display = 'none';
            if (roleField) roleField.style.display = 'block';
            if (invitationMessageField) invitationMessageField.style.display = 'block';
            if (rolesField) rolesField.style.display = 'none';

            // Show warehouse field for invitation (required), hide status
            if (warehouseField) warehouseField.style.display = 'block';
            if (statusField) statusField.style.display = 'none';

            // Update help text for invitation
            if (warehouseHelpText) {
                warehouseHelpText.textContent = 'The warehouse the invited user will be assigned to (required for invitations)';
            }

            // Set field requirements for invitation
            if (passwordInput) passwordInput.required = false;
            if (warehouseSelect) warehouseSelect.required = true; // Warehouse is required for invitations
            if (statusSelect) statusSelect.required = false;
            if (branchSelect) branchSelect.required = false;
        }
    }

    directRadio.addEventListener('change', toggleFields);
    invitationRadio.addEventListener('change', toggleFields);

    // Initial state
    toggleFields();
});
</script>
@endpush
@endif
