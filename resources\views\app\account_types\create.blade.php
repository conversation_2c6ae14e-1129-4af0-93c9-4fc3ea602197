@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">journal-plus</x-slot>
        <x-slot name="title">Create New Account Type</x-slot>
        <x-slot name="subtitle">Add a new account type to the chart of accounts</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Accounting</a></li>
            <li class="breadcrumb-item"><a href="{{ route('account-types.index') }}">Account Types</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('account-types.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Account Types
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Form Card -->
    <div class="card">
        <div class="card-body">
            <x-form
                method="POST"
                action="{{ route('account-types.store') }}"
                class="mt-2"
            >
                @include('app.account_types.form-inputs')

                <div class="d-flex justify-content-end mt-4 border-top pt-4">
                    <a href="{{ route('account-types.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-x-circle me-1"></i> Cancel
                    </a>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-1"></i> Create Account Type
                    </button>
                </div>
            </x-form>
        </div>
    </div>
</div>
@endsection
