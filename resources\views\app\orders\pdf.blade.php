<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purchase Order #{{ $order->order_id }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        
        .company-info {
            flex: 1;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .company-address {
            color: #666;
            line-height: 1.6;
        }
        
        .order-info {
            text-align: right;
            flex: 1;
        }
        
        .order-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .order-details {
            color: #666;
        }
        
        .order-details div {
            margin-bottom: 5px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
            margin-top: 10px;
        }
        
        .status-approved {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-canceled {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        
        .supplier-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .supplier-name {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .supplier-address {
            color: #666;
            line-height: 1.6;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .items-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .items-table .text-center {
            text-align: center;
        }
        
        .items-table .text-right {
            text-align: right;
        }
        
        .summary-section {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        .summary-table {
            width: 300px;
            border-collapse: collapse;
        }
        
        .summary-table td {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        
        .summary-table .label {
            font-weight: bold;
            text-align: left;
        }
        
        .summary-table .value {
            text-align: right;
        }
        
        .summary-table .total-row {
            border-top: 2px solid #333;
            font-weight: bold;
            font-size: 14px;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            border-top: 1px solid #eee;
            padding-top: 20px;
            color: #666;
        }
        
        .footer h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .expiry-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .expiry-success {
            background-color: #d4edda;
            color: #155724;
        }
        
        .expiry-warning {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .expiry-danger {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .quantity-badge {
            background-color: #e3f2fd;
            color: #1976d2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="company-info">
                <div class="company-name">{{ auth()->user()->getGlobal("business") ?? 'Your Business' }}</div>
                <div class="company-address">
                    {{ auth()->user()->address ?? '' }}<br>
                    {{ auth()->user()->phone ?? '' }}<br>
                    {{ auth()->user()->email ?? '' }}
                </div>
            </div>
            <div class="order-info">
                <div class="order-title">Purchase Order #{{ $order->order_id }}</div>
                <div class="order-details">
                    <div><strong>Date Issued:</strong> {{ optional($order->created_at)->format('M d, Y') ?? '' }}</div>
                    <div><strong>Due Date:</strong> {{ optional($order->date_to)->format('M d, Y') ?? 'N/A' }}</div>
                    @if($order->approved_by)
                    <div><strong>Approved By:</strong> {{ optional($order->approvedBy)->name ?? '' }}</div>
                    <div><strong>Approval Date:</strong> {{ optional($order->approved_at)->format('M d, Y') ?? '' }}</div>
                    @endif
                </div>
                <span class="status-badge {{ $order->status_id == 13 ? 'status-canceled' : ($order->approved_by ? 'status-approved' : 'status-pending') }}">
                    {{ $order->status_id == 13 ? 'Canceled' : ($order->approved_by ? 'Approved' : 'Pending') }}
                </span>
            </div>
        </div>

        <!-- Supplier Information -->
        <div class="section">
            <div class="section-title">Supplier Information</div>
            <div class="supplier-info">
                <div class="supplier-name">{{ $order->supplier->name ?? 'N/A' }}</div>
                <div class="supplier-address">
                    {{ $order->supplier->address ?? 'No address provided' }}<br>
                    {{ $order->supplier->phone ?? 'No phone provided' }}<br>
                    {{ $order->supplier->email ?? 'No email provided' }}
                </div>
            </div>
        </div>

        <!-- Order Information -->
        @if($order->description)
        <div class="section">
            <div class="section-title">Order Notes</div>
            <div style="background-color: #e3f2fd; padding: 10px; border-radius: 5px; color: #1976d2;">
                {{ $order->description }}
            </div>
        </div>
        @endif

        <!-- Order Items -->
        <div class="section">
            <div class="section-title">Order Items</div>
            <table class="items-table">
                <thead>
                    <tr>
                        <th style="width: 35%">Item</th>
                        <th style="width: 12%">Unit</th>
                        <th style="width: 10%" class="text-center">Quantity</th>
                        <th style="width: 12%" class="text-right">Unit Price</th>
                        <th style="width: 16%" class="text-center">Expiration</th>
                        <th style="width: 15%" class="text-right">Total</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($order->stocks as $stock)
                    <tr>
                        <td>
                            <strong>{{ $stock->product->name ?? '-' }}</strong>
                            @if($stock->product->description)
                            <br><small style="color: #666;">{{ $stock->product->description }}</small>
                            @endif
                        </td>
                        <td>{{ $stock->unit->name ?? '-' }}</td>
                        <td class="text-center">
                            <span class="quantity-badge">{{ $stock->quantity ?? '0' }}</span>
                        </td>
                        <td class="text-right">{{ _money($stock->buying_price) }}</td>
                        <td class="text-center">
                            @if($stock->expires_at)
                                <span class="expiry-badge {{ $stock->expires_at->isPast() ? 'expiry-danger' : ($stock->expires_at->isToday() ? 'expiry-warning' : 'expiry-success') }}">
                                    {{ $stock->expires_at->format('M d, Y') }}
                                </span>
                            @else
                                <span style="color: #999;">No expiry</span>
                            @endif
                        </td>
                        <td class="text-right"><strong>{{ _money($stock->quantity * $stock->buying_price) }}</strong></td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Order Summary -->
        <div class="summary-section">
            <table class="summary-table">
                <tr>
                    <td class="label">Subtotal:</td>
                    <td class="value">{{ _money($order->sub_total) }}</td>
                </tr>
                <tr>
                    <td class="label">VAT ({{ auth()->user()->getGlobal('vat') ?? '0' }}%):</td>
                    <td class="value">{{ _money($order->vat) }}</td>
                </tr>
                <tr>
                    <td class="label">Discount:</td>
                    <td class="value" style="color: #dc3545;">-{{ _money($order->discount) }}</td>
                </tr>
                <tr class="total-row">
                    <td class="label">Total:</td>
                    <td class="value">{{ _money($order->amount_total) }}</td>
                </tr>
                <tr>
                    <td class="label">Amount Paid:</td>
                    <td class="value" style="color: #28a745;">{{ _money($order->amount_paid) }}</td>
                </tr>
                <tr>
                    <td class="label">Balance Due:</td>
                    <td class="value" style="color: {{ ($order->amount_total - $order->amount_paid) > 0 ? '#dc3545' : '#28a745' }};">
                        {{ _money($order->amount_total - $order->amount_paid) }}
                    </td>
                </tr>
            </table>
        </div>

        <!-- Footer -->
        <div class="footer">
            <h4>Thank you for your business!</h4>
            <p>If you have any questions concerning this purchase order, please contact us.</p>
            <small>&copy; {{ date('Y') }} {{ auth()->user()->getGlobal("business") ?? 'Your Business' }}</small>
        </div>
    </div>
</body>
</html>
