@extends('layouts.fullscreen')

@section('content')
<!-- Fullscreen Header -->
<div class="fullscreen-header">
    <div class="d-flex justify-content-between align-items-center w-100">
        <div>
            <h1 class="h4 mb-0">
                <i class="bi bi-pencil-square text-warning me-2"></i>Edit Sale #{{ $sale->id }}
            </h1>
            <small class="text-muted">
                <a href="{{ route('sales.index') }}" class="text-decoration-none">Sales</a> /
                <a href="{{ route('sales.show', $sale) }}" class="text-decoration-none">Sale #{{ $sale->id }}</a> / Edit
            </small>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('sales.show', $sale) }}" class="btn btn-outline-info">
                <i class="bi bi-eye me-1"></i>View Sale
            </a>
            <a href="{{ route('sales.create') }}" class="btn btn-outline-success">
                <i class="bi bi-plus me-1"></i>New Sale
            </a>
            <a href="{{ route('sales.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-list me-1"></i>All Sales
            </a>
        </div>
    </div>
</div>

<!-- Fullscreen Content -->
<div class="fullscreen-content">
    <x-form method="PUT" action="{{ route('sales.update', $sale) }}">
        @include('app.sales.form-inputs')

        <div class="mt-4 d-flex justify-content-between">
            <a href="{{ route('sales.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>Back to Sales
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-check-circle me-1"></i>Update Sale
            </button>
        </div>
    </x-form>
</div>
@endsection
