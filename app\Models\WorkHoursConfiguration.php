<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\CreatedUpdatedTrait;

class WorkHoursConfiguration extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'start_time',
        'end_time',
        'standard_hours_per_day',
        'overtime_rate',
        'is_active',
        'description',
        'created_by',
        'updated_by',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'start_time' => 'datetime:H:i:s',
        'end_time' => 'datetime:H:i:s',
        'standard_hours_per_day' => 'decimal:2',
        'overtime_rate' => 'decimal:2',
        'is_active' => 'boolean',
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];

    /**
     * Get the active work hours configuration.
     */
    public static function getActive()
    {
        return static::where('is_active', true)->first() ?? static::first();
    }

    /**
     * Calculate overtime hours based on total hours worked.
     */
    public function calculateOvertimeHours($totalHours)
    {
        return max(0, $totalHours - $this->standard_hours_per_day);
    }

    /**
     * Calculate regular hours based on total hours worked.
     */
    public function calculateRegularHours($totalHours)
    {
        return min($totalHours, $this->standard_hours_per_day);
    }

    /**
     * Check if a given time is within standard work hours.
     */
    public function isWithinWorkHours($time)
    {
        $timeOnly = date('H:i:s', strtotime($time));
        $startTime = date('H:i:s', strtotime($this->start_time));
        $endTime = date('H:i:s', strtotime($this->end_time));
        
        return $timeOnly >= $startTime && $timeOnly <= $endTime;
    }
}
