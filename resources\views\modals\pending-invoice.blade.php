<form action="" class="invoice-approval-form" method="POST" id="approve-invoice-el">
	@csrf
	<x-lg-modal>
		<x-slot name="class">pending-invoice-modal</x-slot>
		<x-slot name="title">Change Invoice Status</x-slot>

	    <div class="" v-if="invoice">
	    	<textarea name="description" class="form-control" placeholder="Comment ..." required></textarea> <br>
	    	<p> Please provide comment.</p>
	    	<div class="card">
	    		<div class="card-body">
	    			<div>Invoice Number: <span class="text-info" v-text="invoice.invoice_id"></span></div>
	    			<div>Invoice Total Amount: <span class="text-info"  v-text="invoice.amount_total"></span></div>
	    		</div>
	    	</div>
	     </div>
	     <div v-else>
	     		<h1 class="center">Loading...</h1>
	     </div>

		<x-slot name="footer">
			<div v-if="invoice" style="width: 100%;">
				<div v-if="invoice?.status_id != 10">
					<button type="submit" value="10" name="status_id" class="btn btn-primary mr-4"> Approve </button>
					<button type="submit" value="12" name="status_id" class="btn btn-danger mr-4"> Reject </button>
				</div>
				@if( auth()->user()->can('cancel invoices'))
				<button type="submit" value="13" name="status_id" class="btn btn-danger" style="float: right;"> Cancel Invoice</button>
				@endif
			</div>
		</x-slot>
	</x-lg-modal>
</form>




@push('scripts')

  <script type="text/javascript">
    
    new Vue({
      el: "#approve-invoice-el",
      data(){
        return{
          invoice: null,
        }
      },

      methods: {

        getInvoice(id) {
          axios.get('/ajax-invoice/' + id).then( res => {
          	this.invoice = res.data;
          	$(".invoice-approval-form").attr("action", "/approve-invoice/" + id);
          	console.log(this.invoice)
          }) 
        },

      },


      created(){
      	$("body").on("click", ".pending-invoice-btn", (e) => {
      		var id = $(e.target).attr("data-id");
      		this.getInvoice(id);
      	})
      }

    });

  </script>

@endpush
