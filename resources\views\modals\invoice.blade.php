<div id="preview-invoice-el">
	@csrf
	<x-lg-modal>
		<x-slot name="class">preview-invoice-modal</x-slot>
		<x-slot name="title">Invoice</x-slot>

      <div style="text-transform: uppercase!important;">

  	    <div class="invoice-print print" id="invoice-print" v-if="invoice"  style="max-width: 400px; margin: auto;">

          <div class="mb-4" style=" font-size: 11px;">
            <!-- Avatar -->
            <center>
              <!-- <div class="avatar avatar-xl avatar-circle avatar-centered mb-3"> -->
              <div class="">
                <img width="50" src="{{ auth()->user()->getGlobalInstance('logo')->path ?? asset('assets/svg/logos/logo-short.svg') }}" alt="Logo">
              </div>

              <div class="text-center">
                <span>{{ auth()->user()->getGlobal("business") ?? ''}}</span>
              </div>           
              <div class="text-center">
                <span>{{ auth()->user()->getGlobal("address") ?? ''}}</span>
              </div>                    
              <div class="text-center">
                <span>{{ auth()->user()->getGlobal("email") ?? ''}}</span>
              </div>              
              <div class="text-center">
                <span>{{ auth()->user()->getGlobal("phone") ?? ''}}</span>
              </div>

            </center>
            <!-- End Avatar -->

            <hr style="margin:5px">

            <div>
              <span class="text-uppercase"> DATE: </span>
              <span style="float: right;" v-text="invoice.created_at"></span>
            </div>
            <div>
              <span class="text-uppercase"> CUSTOMER:  </span>
              <span style="float: right;" v-text="invoice?.customer_name"></span>
            </div>            
            <div>
              <span class="text-uppercase"> RECEIPT #: </span>
              <span style="float: right;" v-text="invoice.invoice_id"></span>
            </div>
            <hr style="margin:5px">

            <table style=" " cellpadding="0" cellspacing="0" width="100%">
              <tr>
                <th style="text-align: left;">DESC</th>
                <th style="text-align: center;">UNIT</th>
                <th style="text-align: center;">QTY x PRICE</th>
                <th style="text-align: right;">AMOUNT</th>
              </tr>
              <tr v-for="(sale, index) in invoice.sales">
                <td style="text-align: left; font-size: 11px;">
                  <span v-text="sale.product.description"></span>
                </td>
                <td style="text-align:center;">
                  <span v-text="sale?.unit_name"></span>
                </td>                
                <td style="text-align:center;">
                  <span v-text="sale.quantity"></span> <i>x</i> <span v-text="formatNumber(sale.selling_price)"></span>
                </td>
                <td style="text-align: right; font-size: 11px;">
                  <span v-text="formatNumber(sale.selling_amount.toFixed(2))"></span>
                </td>
              </tr>
            </table>  



            <hr style="margin:5px">                 

            <table border="0" cellpadding="0" cellspacing="0" width="100%">

              <tr>
                <td align="left" width="75%" style=" font-size: 11px;"><strong>SUB TOTAL</strong></td>
                <td align="right" width="25%" style=" font-size: 11px;"><strong v-text="formatNumber(invoice.sub_total)"></strong></td>
              </tr>    

              <tr>
                <td align="left" width="75%" style=" font-size: 11px;"><strong>TOTAL VAT</strong></td>
                <td align="right" width="25%" style=" font-size: 11px;"><strong v-text="formatNumber(invoice.vat)"></strong></td>
              </tr>                         

              <tr>
                <td align="left" width="75%" style=" font-size: 11px;"><strong>TOTAL</strong></td>
                <td align="right" width="25%" style=" font-size: 11px;"><strong v-text="formatNumber((Number(invoice.sub_total) + Number(invoice.vat)).toFixed(2))"></strong></td>
              </tr>

              <tr>
                <td align="left" width="75%" style=" font-size: 11px;"><strong>DISCOUNT</strong></td>
                <td align="right" width="25%" style=" font-size: 11px;">-<strong v-text="formatNumber(invoice.discount)"></strong></td>
              </tr> 


              <tr>
                <td align="left" width="75%" style=" font-size: 11px;"><strong>TOTAL PAID</strong></td>
                <td align="right" width="25%" style=" font-size: 11px;"><strong v-text="formatNumber(invoice.amount_total)"></strong></td>
              </tr>


              <tr>
                <td align="left" width="75%" style=" font-size: 11px;"><strong>CASH PAID</strong></td>
                <td align="right" width="25%" style=" font-size: 11px;"><strong v-text="formatNumber(invoice.amount_paid)"></strong></td>
              </tr> 
            </table>

            <hr style="margin:5px">
            <div>
              <span class="text-uppercase"> Operator </span>
              <span style="float: right;" v-text="invoice?.prepared_by"></span>
            </div>            
            <hr style="margin:5px">
            <div style="text-align:center;">
              <span>END OF RECEIPT</span>
            </div>


          </div>

  	     </div>

  	     <div v-else>
  	     		<h1 class="center">Loading...</h1>
  	     </div>
      </div>

		<x-slot name="footer">
			<div v-if="invoice">
          <button class="btn btn-sm btn-soft-primary"  onclick="extractPrint('invoice-print')" href="javascript:;">
            <i class="bi-download me-1"></i> PRINT
          </button>                  
          <button v-if="auto" class="btn btn-sm btn-soft-primary"  onclick="createPDFfromHTML('invoice-print')" href="javascript:;">
            <i class="bi-download me-1"></i> PDF
          </button>                
          <a :href="'/invoices/' + invoice.id " class="btn btn-sm btn-soft-primary">
            <i class="bi-view me-1"></i> MORE
          </a>   
			</div>
		</x-slot>
	</x-lg-modal>
</div>




@push('scripts')

  <script type="text/javascript">
    
    new Vue({
      el: "#preview-invoice-el",
      data(){
        return{
          invoice: null,
          auto: false,
        }
      },

      methods: {

        getInvoice(id) {
          axios.get('/ajax-invoice/' + id).then( res => {
          	this.invoice = res.data;
          	if( this.auto ) {
              setTimeout( ()=> { 
                // $(".preview-invoice-modal").modal('show');
                window.extractPrint('invoice-print');
              }, 2000);
              this.auto = false;
            }
          }) 
        },

        formatNumber (num) {
            return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")
        },

      },

      created(){
        var $invoice = @json( session( "invoicable" ) ?? '' );
        if( $invoice ) {
          this.auto = true;
          this.getInvoice($invoice.id);
        }
        
        $("body").on("click", ".preview-invoice-btn", (e) => {
          var id = $(e.target).attr("data-id");
          this.getInvoice(id);
          var auto = $(e.target).attr("data-auto");
          if(auto){
            this.auto = true;
          }
        })

      }

    });

  </script>

@endpush
