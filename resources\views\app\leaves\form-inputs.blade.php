@php $editing = isset($leave) @endphp

<div class="row">

    <x-inputs.group class="col-sm-12 col-lg-6 mb-4">
        <x-inputs.text
            name="phone"
            label="Phone"
            :value="old('phone', ($editing ? $leave->phone : ''))"
            maxlength="255"
            placeholder="Phone"
        ></x-inputs.text>
    </x-inputs.group>

    <x-inputs.group class="col-sm-12 col-lg-6 mb-4">
        <x-inputs.date
            name="date_from"
            label="date_from"
            :value="old('date_from', ($editing ? $leave->date_from : ''))"
            maxlength="255"
            placeholder="date_from"
        ></x-inputs.date>
    </x-inputs.group>

    <x-inputs.group class="col-sm-12 col-lg-6 mb-4">
        <x-inputs.date
            name="date_to"
            label="date_to"
            :value="old('date_to', ($editing ? $leave->date_to : ''))"
            maxlength="255"
            placeholder="date_to"
        ></x-inputs.date>
    </x-inputs.group>


    <x-inputs.group class="col-sm-12 col-lg-6 mb-4">
        <x-inputs.text
            name="days"
            label="days"
            :value="old('days', ($editing ? $leave->days : ''))"
            maxlength="255"
            placeholder="days"
        ></x-inputs.text>
    </x-inputs.group>

    <x-inputs.group class="col-sm-12 col-lg-12">
        <x-inputs.textarea
            name="description"
            label="Comment"
            maxlength="255"
            >{{ old('description', ($editing ? $leave->description : ''))
            }}</x-inputs.textarea
        >
    </x-inputs.group>

</div>
