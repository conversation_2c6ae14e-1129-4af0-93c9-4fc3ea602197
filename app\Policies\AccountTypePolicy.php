<?php

namespace App\Policies;

use App\Models\User;
use App\Models\AccountType;
use Illuminate\Auth\Access\HandlesAuthorization;

class AccountTypePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo('list account-types');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\AccountType  $accountType
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, AccountType $accountType)
    {
        return $user->hasPermissionTo('view account-types');
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo('create account-types');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\AccountType  $accountType
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, AccountType $accountType)
    {
        return $user->hasPermissionTo('update account-types') && 
            (!$accountType->is_system || $user->hasRole('super-admin'));
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\AccountType  $accountType
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, AccountType $accountType)
    {
        return $user->hasPermissionTo('delete account-types') && 
            !$accountType->is_system && 
            $accountType->categories()->count() === 0;
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\AccountType  $accountType
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, AccountType $accountType)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\AccountType  $accountType
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, AccountType $accountType)
    {
        return false;
    }
}
