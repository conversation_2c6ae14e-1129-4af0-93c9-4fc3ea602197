@php $editing = isset($accountCategory) @endphp

<div class="row">
    <!-- Basic Information Card -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-header-title">
                    <i class="bi bi-info-circle me-2"></i>Basic Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-12 col-lg-6 mb-4">
                        <label for="name" class="form-label">Category Name</label>
                        <input
                            type="text"
                            name="name"
                            id="name"
                            class="form-control @error('name') is-invalid @enderror"
                            value="{{ old('name', ($editing ? $accountCategory->name : '')) }}"
                            maxlength="255"
                            placeholder="Enter category name"
                            required
                        >
                        @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">
                            A descriptive name for this account category
                        </small>
                    </div>
                    
                    <div class="col-sm-12 col-lg-6 mb-4">
                        <label for="code" class="form-label">Category Code</label>
                        <input
                            type="text"
                            name="code"
                            id="code"
                            class="form-control @error('code') is-invalid @enderror"
                            value="{{ old('code', ($editing ? $accountCategory->code : '')) }}"
                            maxlength="255"
                            placeholder="Enter category code"
                            required
                        >
                        @error('code')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">
                            A unique code for this category (e.g., CASH, AR, AP, INV)
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Classification Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-header-title">
                    <i class="bi bi-tag me-2"></i>Classification
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <label for="account_type_id" class="form-label">Account Type</label>
                    <div class="tom-select-custom">
                        <select name="account_type_id" id="account_type_id" required class="js-select form-select @error('account_type_id') is-invalid @enderror" data-hs-tom-select-options='{
                                    "placeholder": "Select an account type..."
                                  }'>
                            <option value="" disabled {{ empty(old('account_type_id', ($editing ? $accountCategory->account_type_id : ''))) ? 'selected' : '' }}>Select an account type</option>
                            @foreach($accountTypes as $value => $label)
                            <option value="{{ $value }}" {{ old('account_type_id', ($editing ? $accountCategory->account_type_id : '')) == $value ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                            @endforeach
                        </select>
                        @error('account_type_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <small class="form-text text-muted mt-2">
                        This determines the financial statement where accounts in this category will appear
                    </small>
                </div>
                
                <div class="form-check form-switch mb-3">
                    <input 
                        type="checkbox"
                        name="is_active"
                        id="is_active"
                        class="form-check-input"
                        value="1"
                        {{ $editing && $accountCategory->is_active ? 'checked' : '' }}
                    />
                    <label class="form-check-label" for="is_active">Active</label>
                    <div class="form-text text-muted">
                        Inactive categories cannot be used for new accounts
                    </div>
                </div>
                
                @if($editing)
                <div class="form-check form-switch">
                    <input 
                        type="checkbox"
                        name="is_system"
                        id="is_system"
                        class="form-check-input"
                        value="1"
                        {{ $editing && $accountCategory->is_system ? 'checked' : '' }}
                        {{ $editing && $accountCategory->is_system ? 'disabled' : '' }}
                    />
                    <label class="form-check-label" for="is_system">System Category</label>
                    <div class="form-text text-muted">
                        System categories cannot be deleted and are required for the accounting system
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Description Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-header-title">
                    <i class="bi bi-file-text me-2"></i>Description
                </h5>
            </div>
            <div class="card-body">
                <div>
                    <label for="description" class="form-label">Category Description</label>
                    <textarea 
                        name="description" 
                        id="description" 
                        class="form-control @error('description') is-invalid @enderror" 
                        rows="5" 
                        placeholder="Enter a description for this category"
                    >{{ old('description', ($editing ? $accountCategory->description : '')) }}</textarea>
                    @error('description')
                    <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted mt-2">
                        Provide a clear description of what this category is used for and any special considerations
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
