<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Debt;
use Illuminate\Auth\Access\HandlesAuthorization;

class DebtPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the Debt can view any models.
     *
     * @param  App\Models\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo('list debtors');
    }

    /**
     * Determine whether the Debt can view the model.
     *
     * @param  App\Models\User  $user
     * @param  App\Models\Debt  $model
     * @return mixed
     */
    public function view(User $user, Debt $model)
    {
        return $user->hasPermissionTo('view debtors');
    }

    /**
     * Determine whether the Debt can create models.
     *
     * @param  App\Models\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo('create debtors');
    }

    /**
     * Determine whether the Debt can update the model.
     *
     * @param  App\Models\User  $user
     * @param  App\Models\Debt  $model
     * @return mixed
     */
    public function update(User $user, Debt $model)
    {
        return $user->hasPermissionTo('update debtors');
    }

    /**
     * Determine whether the Debt can delete the model.
     *
     * @param  App\Models\User  $user
     * @param  App\Models\Debt  $model
     * @return mixed
     */
    public function delete(User $user, Debt $model)
    {
        return $user->hasPermissionTo('delete debtors');
    }

    /**
     * Determine whether the user can delete multiple instances of the model.
     *
     * @param  App\Models\User  $user
     * @param  App\Models\Debt  $model
     * @return mixed
     */
    public function deleteAny(User $user)
    {
        return $user->hasPermissionTo('delete debtors');
    }

    /**
     * Determine whether the Debt can restore the model.
     *
     * @param  App\Models\User  $user
     * @param  App\Models\Debt  $model
     * @return mixed
     */
    public function restore(User $user, Debt $model)
    {
        return false;
    }

    /**
     * Determine whether the Debt can permanently delete the model.
     *
     * @param  App\Models\User  $user
     * @param  App\Models\Debt  $model
     * @return mixed
     */
    public function forceDelete(User $user, Debt $model)
    {
        return false;
    }
}
