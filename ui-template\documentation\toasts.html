<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from htmlstream.com/front-dashboard/documentation/toasts.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 30 Aug 2022 07:13:04 GMT -->
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Toasts - Documentation | Front - Multipurpose Responsive Template</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="../favicon.ico">

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&amp;display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="../assets/css/vendor.min.css">

  <!-- CSS Front Template -->
  <link rel="stylesheet" href="../assets/css/theme.minc619.css?v=1.0">

  <link rel="preload" href="../assets/css/theme.min.css" data-hs-appearance="default" as="style">
  <link rel="preload" href="../assets/css/docs.css" data-hs-appearance="default" as="style">
  <link rel="preload" href="../assets/css/theme-dark.min.css" data-hs-appearance="dark" as="style">

  <style data-hs-appearance-onload-styles>
    *
    {
      transition: unset !important;
    }

    body
    {
      opacity: 0;
    }
  </style>

  <script>
            window.hs_config = {"autopath":"@@autopath","deleteLine":"hs-builder:delete","deleteLine:build":"hs-builder:build-delete","deleteLine:dist":"hs-builder:dist-delete","previewMode":false,"startPath":"/index.html","vars":{"themeFont":"https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap","version":"?v=1.0"},"layoutBuilder":{"extend":{"switcherSupport":true},"header":{"layoutMode":"default","containerMode":"container-fluid"},"sidebarLayout":"default"},"themeAppearance":{"layoutSkin":"default","sidebarSkin":"default","styles":{"colors":{"primary":"#377dff","transparent":"transparent","white":"#fff","dark":"132144","gray":{"100":"#f9fafc","900":"#1e2022"}},"font":"Inter"}},"languageDirection":{"lang":"en"},"skipFilesFromBundle":{"dist":["assets/js/hs.theme-appearance.js","assets/js/hs.theme-appearance-charts.html","assets/js/demo.js"],"build":["assets/css/theme.css","assets/vendor/hs-navbar-vertical-aside/dist/hs-navbar-vertical-aside-mini-cache.html","assets/js/demo.html","assets/css/theme-dark.html","assets/css/docs.html","assets/vendor/icon-set/style.html","assets/js/hs.theme-appearance.html","assets/js/hs.theme-appearance-charts.html","node_modules/chartjs-plugin-datalabels/dist/chartjs-plugin-datalabels.min.html","assets/js/demo.js"]},"minifyCSSFiles":["assets/css/theme.css","assets/css/theme-dark.css"],"copyDependencies":{"dist":{"*assets/js/theme-custom.js":""},"build":{"*assets/js/theme-custom.js":"","node_modules/bootstrap-icons/font/*fonts/**":"assets/css"}},"buildFolder":"","replacePathsToCDN":{},"directoryNames":{"src":"./src","dist":"./dist","build":"./build"},"fileNames":{"dist":{"js":"theme.min.js","css":"theme.min.css"},"build":{"css":"theme.min.css","js":"theme.min.js","vendorCSS":"vendor.min.css","vendorJS":"vendor.min.js"}},"fileTypes":"jpg|png|svg|mp4|webm|ogv|json"}
            window.hs_config.gulpRGBA = (p1) => {
  const options = p1.split(',')
  const hex = options[0].toString()
  const transparent = options[1].toString()

  var c;
  if(/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)){
    c= hex.substring(1).split('');
    if(c.length== 3){
      c= [c[0], c[0], c[1], c[1], c[2], c[2]];
    }
    c= '0x'+c.join('');
    return 'rgba('+[(c>>16)&255, (c>>8)&255, c&255].join(',')+',' + transparent + ')';
  }
  throw new Error('Bad Hex');
}
            window.hs_config.gulpDarken = (p1) => {
  const options = p1.split(',')

  let col = options[0].toString()
  let amt = -parseInt(options[1])
  var usePound = false

  if (col[0] == "#") {
    col = col.slice(1)
    usePound = true
  }
  var num = parseInt(col, 16)
  var r = (num >> 16) + amt
  if (r > 255) {
    r = 255
  } else if (r < 0) {
    r = 0
  }
  var b = ((num >> 8) & 0x00FF) + amt
  if (b > 255) {
    b = 255
  } else if (b < 0) {
    b = 0
  }
  var g = (num & 0x0000FF) + amt
  if (g > 255) {
    g = 255
  } else if (g < 0) {
    g = 0
  }
  return (usePound ? "#" : "") + (g | (b << 8) | (r << 16)).toString(16)
}
            window.hs_config.gulpLighten = (p1) => {
  const options = p1.split(',')

  let col = options[0].toString()
  let amt = parseInt(options[1])
  var usePound = false

  if (col[0] == "#") {
    col = col.slice(1)
    usePound = true
  }
  var num = parseInt(col, 16)
  var r = (num >> 16) + amt
  if (r > 255) {
    r = 255
  } else if (r < 0) {
    r = 0
  }
  var b = ((num >> 8) & 0x00FF) + amt
  if (b > 255) {
    b = 255
  } else if (b < 0) {
    b = 0
  }
  var g = (num & 0x0000FF) + amt
  if (g > 255) {
    g = 255
  } else if (g < 0) {
    g = 0
  }
  return (usePound ? "#" : "") + (g | (b << 8) | (r << 16)).toString(16)
}
            </script>
</head>

<body class="navbar-sidebar-aside-lg">

  <script src="../assets/js/hs.theme-appearance.js"></script>

  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand navbar-end navbar-light navbar-sticky-lg-top bg-white">
    <div class="container-fluid">
      <nav class="navbar-nav-wrap">
        <div class="row flex-grow-1">
          <!-- Default Logo -->
          <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
            <a class="navbar-brand" href="index.html" aria-label="Front">
              <img class="navbar-brand-logo" src="../assets/svg/logos/logo.svg" alt="Logo" data-hs-theme-appearance="default">
              <img class="navbar-brand-logo" src="../assets/svg/logos-light/logo.svg" alt="Logo" data-hs-theme-appearance="dark">
            </a>
            <a href="changelog.html">
              <span class="badge bg-soft-primary text-primary">v2.1</span>
            </a>
          </div>
          <!-- End Default Logo -->

          <div class="col-md px-lg-0">
            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
              <div class="d-none d-md-block">
                <!-- Search Form -->
                <form id="docsSearch" class="position-relative" data-hs-list-options='{
                       "searchMenu": true,
                       "keyboard": true,
                       "item": "searchTemplate",
                       "valueNames": ["component", "category", {"name": "link", "attr": "href"}],
                       "empty": "#searchNoResults"
                     }'>
                  <!-- Input Group -->
                  <div class="input-group input-group-merge navbar-input-group">
                    <div class="input-group-prepend input-group-text">
                      <i class="bi-search"></i>
                    </div>

                    <input type="search" class="search form-control form-control-sm" placeholder="Search in docs" aria-label="Search in docs">

                    <a class="input-group-append input-group-text" href="javascript:;">
                      <i id="clearSearchResultsIcon" class="bi-x" style="display: none;"></i>
                    </a>
                  </div>
                  <!-- End Input Group -->

                  <!-- List -->
                  <div class="list dropdown-menu navbar-dropdown-menu-borderless w-100 overflow-auto" style="max-height: 16rem;"></div>
                  <!-- End List -->

                  <!-- Empty -->
                  <div id="searchNoResults" style="display: none;">
                    <div class="text-center p-4">
                      <img class="mb-3" src="../assets/svg/illustrations/oc-error.svg" alt="Image Description" data-hs-theme-appearance="default" style="width: 7rem;">
                      <img class="mb-3" src="../assets/svg/illustrations-light/oc-error.svg" alt="Image Description" data-hs-theme-appearance="dark" style="width: 7rem;">
                      <p class="mb-0">No Results</p>
                    </div>
                  </div>
                  <!-- End Empty -->
                </form>
                <!-- End Search Form -->

                <!-- List Item Template -->
                <div class="d-none">
                  <div id="searchTemplate" class="dropdown-item">
                    <a class="d-block link" href="#">
                      <span class="category d-block fw-normal text-muted mb-1"></span>
                      <span class="component text-dark"></span>
                    </a>
                  </div>
                </div>
                <!-- End List Item Template -->
              </div>

              <!-- Navbar -->
              <ul class="navbar-nav p-0">
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://htmlstream.com/contact-us" target="_blank">
                    Get Support <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-primary btn-sm" href="../index.html">
                    <i class="bi-eye me-1"></i> Preview Demo
                  </a>
                </li>
              </ul>
              <!-- End Navbar -->
            </div>
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </nav>
    </div>
  </header>

  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <!-- Navbar -->
    <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end" data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
      <!-- Navbar Toggle -->
      <div class="d-grid flex-grow-1 px-2">
        <button type="button" class="navbar-toggler btn btn-white" data-bs-toggle="collapse" data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false" aria-controls="navbarVerticalNavMenu">
          <span class="d-flex justify-content-between align-items-center">
            <span class="h3 mb-0">Nav menu</span>

            <span class="navbar-toggler-default">
              <i class="bi-list"></i>
            </span>

            <span class="navbar-toggler-toggled">
              <i class="bi-x"></i>
            </span>
          </span>
        </button>
      </div>
      <!-- End Navbar Toggle -->

      <!-- Navbar Collapse -->
      <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
        <div class="navbar-brand-wrapper border-end" style="height: auto;">
          <!-- Default Logo -->
          <div class="d-flex align-items-center mb-3">
            <a class="navbar-brand" href="index.html" aria-label="Front">
              <img class="navbar-brand-logo" src="../assets/svg/logos/logo.svg" alt="Logo" data-hs-theme-appearance="default">
              <img class="navbar-brand-logo" src="../assets/svg/logos-light/logo.svg" alt="Logo" data-hs-theme-appearance="dark">
            </a>
            <a class="navbar-brand-badge" href="changelog.html">
              <span class="badge bg-soft-primary text-primary ms-2">v2.1</span>
            </a>
          </div>
          <!-- End Default Logo -->
        </div>

        <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
          <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
            <li class="nav-item">
              <span class="nav-subtitle">Documentation</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="index.html">Introduction</a>
            </li>

            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Getting started</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="getting-started.html">Getting Started</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="gulp.html">Gulp</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="darkmode.html">Dark Mode <span class="badge bg-soft-dark text-dark lh-base ms-1">New</span></a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="customization.html">Customization</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="credits.html">Credits</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="changelog.html">Changelog</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Design &amp; Graphics</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="bs-icons.html">Bootstrap Icons</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="illustrations.html">Illustrations</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Components</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="accordion.html">Accordion</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="alerts.html">Alerts</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="avatars.html">Avatars</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="badge.html">Badge</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="breadcrumb.html">Breadcrumb</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="buttons.html">Buttons</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="button-group.html">Button Group</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="cards.html">Cards</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="collapse.html">Collapse</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="column-divider.html">Column Divider</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="devices.html">Devices</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="divider.html">Divider</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="dropdowns.html">Dropdowns</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="icons.html">Icons</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="list-group.html">List Group</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="lists.html">Lists</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="legend-indicator.html">Legend Indicator</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="modal.html">Modal</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="offcanvas.html">Offcanvas</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="page-header.html">Page Header</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="pagination.html">Pagination</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="popovers.html">Popovers</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="progress.html">Progress</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="profile.html">Profile</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="shapes.html">Shapes</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sliding-img.html">Sliding Image</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="spinners.html">Spinners</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="steps.html">Steps</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="tab.html">Tab</a>
            </li>

            <li class="nav-item">
              <a class="nav-link active" href="toasts.html">Toasts</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="tooltips.html">Tooltips</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="typography.html">Typography</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Navbars</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="navbar.html">Navbar</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="navs.html">Navs</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="mega-menu.html">Mega Menu</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="navbar-vertical-aside.html">Navbar Vertical Aside</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="scrollspy.html">Scrollspy</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Tables</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="tables.html">Tables</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="datatables.html">Datatables</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="table-sticky-header.html">Sticky Header</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Basic forms</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="basic-forms.html">Basic Forms</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="checks-and-switches.html">Checks &amp; Switches</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="input-group.html">Input Group</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle">Advanced Forms</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="select.html">Advanced Select</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="datepicker.html">Datepicker (Flatpickr)</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="daterangepicker.html">Date Range Picker</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="calendar.html">Calendar (Fullcalendar)</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="file-attachments.html">File Attachments</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="dropzone.html">Drag’ n’ Drop File Uploads</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="quill.html">WYSIWYG Editor</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="quantity-counter.html">Quantity Counter</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="copy-to-clipboard.html">Copy to Clipboard</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="input-mask.html">Input Mask</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="step-forms.html">Step Forms (Wizards)</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="add-field.html">Add Field</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="toggle-password.html">Toggle Password</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="count-characters.html">Count Characters</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="form-search.html">Form Search</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="toggle-switch.html">Toggle Switch</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="google-recaptcha.html">Google reCAPTCHA</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle">Charts</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="chartjs.html">Chart.js</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="counter.html">Counter</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="circles.html">Circles.js (Pie Chart)</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle">Others</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="fslightbox.html">Fullscreen Lightbox</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="maps-leaflet.html">Leaflet</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="maps-jsvectormap.html">JSVectorMap</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sortablejs.html">SortableJS</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sticky-block.html">Sticky Block</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="go-to.html">Go To</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Utilities</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="backgrounds.html">Backgrounds</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="borders.html">Borders</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="colors.html">Colors</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="links.html">Links</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="position.html">Position</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="shadows.html">Shadows</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sizing.html">Sizing</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="spacing.html">Spacing</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="z-index.html">Z-index</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="opacity.html">Opacity</a>
            </li>
          </ul>
        </div>
      </div>
      <!-- End Navbar Collapse -->
    </nav>
    <!-- End Navbar -->

    <!-- Content -->
    <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
      <!-- Page Header -->
      <div class="docs-page-header">
        <div class="row align-items-center">
          <div class="col-sm">
            <h1 class="docs-page-header-title">Toasts</h1>
            <p class="docs-page-header-text">Push notifications to your visitors with a toast, a lightweight and easily customizable alert message.</p>
            <a class="link" href="https://getbootstrap.com/docs/5.0/components/toasts/" target="_blank">Bootstrap Toasts documentation <i class="bi-box-arrow-up-right"></i></a>
          </div>
        </div>
      </div>
      <!-- End Page Header -->

      <!-- Heading -->
      <h2 id="basic-example" class="hs-docs-heading">
        Basic example <a class="anchorjs-link" href="#basic-example" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab1" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab1" href="#nav-result1" data-bs-toggle="pill" data-bs-target="#nav-result1" role="tab" aria-controls="nav-result1" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab1" href="#nav-html1" data-bs-toggle="pill" data-bs-target="#nav-html1" role="tab" aria-controls="nav-html1" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent1">
          <div class="tab-pane fade p-4 show active" id="nav-result1" role="tabpanel" aria-labelledby="nav-resultTab1">
            <!-- Toast -->
            <div class="toast toast-show fade show" role="alert" aria-live="assertive" aria-atomic="true">
              <div class="toast-header">
                <div class="d-flex align-items-center flex-grow-1">
                  <div class="flex-shrink-0">
                    <img class="avatar avatar-sm avatar-circle" src="../assets/img/160x160/img4.jpg" alt="Image description">
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <h5 class="mb-0">Bob Dean</h5>
                    <small class="ms-auto">11 mins ago</small>
                  </div>
                  <div class="text-end">
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                  </div>
                </div>
              </div>
              <div class="toast-body">
                Hello, world! This is a toast message.
              </div>
            </div>
            <!-- End Toast -->
          </div>

          <div class="tab-pane fade" id="nav-html1" role="tabpanel" aria-labelledby="nav-htmlTab1">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Toast --&gt;
                &lt;div class="toast toast-show fade show" role="alert" aria-live="assertive" aria-atomic="true"&gt;
                  &lt;div class="toast-header"&gt;
                    &lt;div class="d-flex align-items-center flex-grow-1"&gt;
                      &lt;div class="flex-shrink-0"&gt;
                        &lt;img class="avatar avatar-sm avatar-circle" src="../assets/img/160x160/img4.jpg" alt="Image description"&gt;
                      &lt;/div&gt;
                      &lt;div class="flex-grow-1 ms-3"&gt;
                        &lt;h5 class="mb-0"&gt;Bob Dean&lt;/h5&gt;
                        &lt;small class="ms-auto"&gt;11 mins ago&lt;/small&gt;
                      &lt;/div&gt;
                      &lt;div class="text-end"&gt;
                        &lt;button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"&gt;&lt;/button&gt;
                      &lt;/div&gt;
                    &lt;/div&gt;
                  &lt;/div&gt;
                  &lt;div class="toast-body"&gt;
                    Hello, world! This is a toast message.
                  &lt;/div&gt;
                &lt;/div&gt;
                &lt;!-- End Toast --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="live" class="hs-docs-heading">
        Live <a class="anchorjs-link" href="#live" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Click the button the below to show as toast (positioning with our utilities in the lower right corner) that has been hidden by default with <code>.hide</code>.</p>

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab2" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab2" href="#nav-result2" data-bs-toggle="pill" data-bs-target="#nav-result2" role="tab" aria-controls="nav-result2" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab2" href="#nav-html2" data-bs-toggle="pill" data-bs-target="#nav-html2" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-jsTab2" href="#nav-js2" data-bs-toggle="pill" data-bs-target="#nav-js2" role="tab" aria-controls="nav-js2" aria-selected="false">JS</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent2">
          <div class="tab-pane fade p-4 show active" id="nav-result2" role="tabpanel" aria-labelledby="nav-resultTab2">
            <!-- Toast Luncher -->
            <button id="liveToastBtn" class="btn btn-primary">Toast</button>

            <!-- Toast -->
            <div id="liveToast" class="position-fixed toast hide" role="alert" aria-live="assertive" aria-atomic="true" style="top: 20px; right: 20px; z-index: 1000;">
              <div class="toast-header">
                <div class="d-flex align-items-center flex-grow-1">
                  <div class="flex-shrink-0">
                    <img class="avatar avatar-sm avatar-circle" src="../assets/img/160x160/img4.jpg" alt="Image description">
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <h5 class="mb-0">Bob Dean</h5>
                    <small class="ms-auto">11 mins ago</small>
                  </div>
                  <div class="text-end">
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                  </div>
                </div>
              </div>
              <div class="toast-body">
                Hello, world! This is a toast message.
              </div>
            </div>
            <!-- End Toast -->
          </div>

          <div class="tab-pane fade" id="nav-html2" role="tabpanel" aria-labelledby="nav-htmlTab2">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Toast Luncher --&gt;
                &lt;button id="liveToastBtn" class="btn btn-primary"&gt;Toast&lt;/button&gt;

                &lt;!-- Toast --&gt;
                &lt;div id="liveToast" class="position-fixed toast hide" role="alert" aria-live="assertive" aria-atomic="true" style="top: 20px; right: 20px; z-index: 1000;"&gt;
                  &lt;div class="toast-header"&gt;
                    &lt;div class="d-flex align-items-center flex-grow-1"&gt;
                      &lt;div class="flex-shrink-0"&gt;
                        &lt;img class="avatar avatar-sm avatar-circle" src="../assets/img/160x160/img4.jpg" alt="Image description"&gt;
                      &lt;/div&gt;
                      &lt;div class="flex-grow-1 ms-3"&gt;
                        &lt;h5 class="mb-0"&gt;Bob Dean&lt;/h5&gt;
                        &lt;small class="ms-auto"&gt;11 mins ago&lt;/small&gt;
                      &lt;/div&gt;
                      &lt;div class="text-end"&gt;
                        &lt;button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"&gt;&lt;/button&gt;
                      &lt;/div&gt;
                    &lt;/div&gt;
                  &lt;/div&gt;
                  &lt;div class="toast-body"&gt;
                    Hello, world! This is a toast message.
                  &lt;/div&gt;
                &lt;/div&gt;
                &lt;!-- End Toast --&gt;
              </code>
            </pre>
          </div>

          <div class="tab-pane fade" id="nav-js2" role="tabpanel" aria-labelledby="nav-jsTab2">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;script&gt;
                    // INITIALIZATION OF LIVE TOAST
                    // =======================================================
                    const liveToast = new bootstrap.Toast(document.querySelector('#liveToast'))
                    document.querySelector('#liveToastBtn').addEventListener('click', () => liveToast.show())
                &lt;/script&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="stacking" class="hs-docs-heading">
        Stacking <a class="anchorjs-link" href="#stacking" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>When you have multiple toasts, we default to vertically stacking them in a readable manner.</p>

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab3" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab3" href="#nav-result3" data-bs-toggle="pill" data-bs-target="#nav-result3" role="tab" aria-controls="nav-result3" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab3" href="#nav-html3" data-bs-toggle="pill" data-bs-target="#nav-html3" role="tab" aria-controls="nav-html3" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent3">
          <div class="tab-pane fade p-4 show active" id="nav-result3" role="tabpanel" aria-labelledby="nav-resultTab3">
            <div class="toast-container">
              <!-- Toast -->
              <div class="toast toast-show fade show" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                  <div class="d-flex align-items-center flex-grow-1">
                    <div class="flex-shrink-0">
                      <img class="avatar avatar-sm avatar-circle" src="../assets/img/160x160/img4.jpg" alt="Image description">
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h5 class="mb-0">Bob Dean</h5>
                      <small class="ms-auto">11 mins ago</small>
                    </div>
                    <div class="text-end">
                      <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                  </div>
                </div>
                <div class="toast-body">
                  See? Just like this.
                </div>
              </div>
              <!-- End Toast -->

              <!-- Toast -->
              <div class="toast toast-show fade show" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                  <div class="d-flex align-items-center flex-grow-1">
                    <div class="flex-shrink-0">
                      <img class="avatar avatar-sm avatar-circle" src="../assets/img/160x160/img9.jpg" alt="Image description">
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h5 class="mb-0">Ella Lauda</h5>
                      <small class="ms-auto">20 mins ago</small>
                    </div>
                    <div class="text-end">
                      <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                  </div>
                </div>
                <div class="toast-body">
                  Heads up, toasts will stack automatically
                </div>
              </div>
              <!-- End Toast -->
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html3" role="tabpanel" aria-labelledby="nav-htmlTab3">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;div class="toast-container"&gt;
                  &lt;!-- Toast --&gt;
                  &lt;div class="toast toast-show fade show" role="alert" aria-live="assertive" aria-atomic="true"&gt;
                    &lt;div class="toast-header"&gt;
                      &lt;div class="d-flex align-items-center flex-grow-1"&gt;
                        &lt;div class="flex-shrink-0"&gt;
                          &lt;img class="avatar avatar-sm avatar-circle" src="../assets/img/160x160/img4.jpg" alt="Image description"&gt;
                        &lt;/div&gt;
                        &lt;div class="flex-grow-1 ms-3"&gt;
                          &lt;h5 class="mb-0"&gt;Bob Dean&lt;/h5&gt;
                          &lt;small class="ms-auto"&gt;11 mins ago&lt;/small&gt;
                        &lt;/div&gt;
                        &lt;div class="text-end"&gt;
                          &lt;button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"&gt;&lt;/button&gt;
                        &lt;/div&gt;
                      &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="toast-body"&gt;
                      See? Just like this.
                    &lt;/div&gt;
                  &lt;/div&gt;
                  &lt;!-- End Toast --&gt;

                  &lt;!-- Toast --&gt;
                  &lt;div class="toast toast-show fade show" role="alert" aria-live="assertive" aria-atomic="true"&gt;
                    &lt;div class="toast-header"&gt;
                      &lt;div class="d-flex align-items-center flex-grow-1"&gt;
                        &lt;div class="flex-shrink-0"&gt;
                          &lt;img class="avatar avatar-sm avatar-circle" src="../assets/img/160x160/img9.jpg" alt="Image description"&gt;
                        &lt;/div&gt;
                        &lt;div class="flex-grow-1 ms-3"&gt;
                          &lt;h5 class="mb-0"&gt;Ella Lauda&lt;/h5&gt;
                          &lt;small class="ms-auto"&gt;20 mins ago&lt;/small&gt;
                        &lt;/div&gt;
                        &lt;div class="text-end"&gt;
                          &lt;button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"&gt;&lt;/button&gt;
                        &lt;/div&gt;
                      &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="toast-body"&gt;
                      Heads up, toasts will stack automatically
                    &lt;/div&gt;
                  &lt;/div&gt;
                  &lt;!-- End Toast --&gt;
                &lt;/div&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="placement" class="hs-docs-heading">
        Placement <a class="anchorjs-link" href="#placement" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Place toasts with custom CSS as you need them. The top right is often used for notifications, as is the top middle. If you’re only ever going to show one toast at a time, put the positioning styles right on the <code>.toast</code>.</p>

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab4" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab4" href="#nav-result4" data-bs-toggle="pill" data-bs-target="#nav-result4" role="tab" aria-controls="nav-result4" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab4" href="#nav-html4" data-bs-toggle="pill" data-bs-target="#nav-html4" role="tab" aria-controls="nav-html4" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent4">
          <div class="tab-pane fade p-4 show active" id="nav-result4" role="tabpanel" aria-labelledby="nav-resultTab4">
            <!-- Toast -->
            <div aria-live="polite" aria-atomic="true" style="position: relative; min-height: 200px;">
              <div class="toast toast-show fade show" role="alert" aria-live="assertive" aria-atomic="true" style="position: absolute; top: 0; right: 0;">
                <div class="toast-header">
                  <div class="d-flex align-items-center flex-grow-1">
                    <div class="flex-shrink-0">
                      <img class="avatar avatar-sm avatar-circle" src="../assets/img/160x160/img4.jpg" alt="Image description">
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h5 class="mb-0">Bob Dean</h5>
                      <small class="ms-auto">11 mins ago</small>
                    </div>
                    <div class="text-end">
                      <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                  </div>
                </div>
                <div class="toast-body">
                  Hello, world! This is a toast message.
                </div>
              </div>
            </div>
            <!-- End Toast -->
          </div>

          <div class="tab-pane fade" id="nav-html4" role="tabpanel" aria-labelledby="nav-htmlTab4">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Toast --&gt;
                &lt;div aria-live="polite" aria-atomic="true" style="position: relative; min-height: 200px;"&gt;
                  &lt;div class="toast toast-show fade show" role="alert" aria-live="assertive" aria-atomic="true" style="position: absolute; top: 0; right: 0;"&gt;
                    &lt;div class="toast-header"&gt;
                      &lt;div class="d-flex align-items-center flex-grow-1"&gt;
                        &lt;div class="flex-shrink-0"&gt;
                          &lt;img class="avatar avatar-sm avatar-circle" src="../assets/img/160x160/img4.jpg" alt="Image description"&gt;
                        &lt;/div&gt;
                        &lt;div class="flex-grow-1 ms-3"&gt;
                          &lt;h5 class="mb-0"&gt;Bob Dean&lt;/h5&gt;
                          &lt;small class="ms-auto"&gt;11 mins ago&lt;/small&gt;
                        &lt;/div&gt;
                        &lt;div class="text-end"&gt;
                          &lt;button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"&gt;&lt;/button&gt;
                        &lt;/div&gt;
                      &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="toast-body"&gt;
                      Hello, world! This is a toast message.
                    &lt;/div&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
                &lt;!-- End Toast --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->
    </div>
    <!-- End Content -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- ========== SECONDARY CONTENTS ========== -->
  <!-- Go To -->
  <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
    <i class="bi-chevron-up"></i>
  </a>
  <!-- ========== END SECONDARY CONTENTS ========== -->

  <!-- JS Implementing Plugins -->
  <script src="../assets/js/vendor.min.js"></script>

  <!-- JS Front -->
  <script src="../assets/js/theme.min.js"></script>

  <!-- JS Plugins Init. -->
  <script>
    (function() {
      // INITIALIZATION OF HEADER
      // =======================================================
      new HSHeader('#header').init()


      // INITIALIZATION OF NAV SCROLLER
      // =======================================================
      new HsNavScroller('.js-nav-scroller', {
        delay: 400,
        offset: 140
      })


      // INITIALIZATION OF LISTJS COMPONENT
      // =======================================================
      HSCore.components.HSList.init('#docsSearch');
      const docsSearch = HSCore.components.HSList.getItem('docsSearch');


      // GET JSON FILE RESULTS
      // =======================================================
      fetch('../assets/json/docs-search.json')
      .then(response => response.json())
      .then(data => {
        docsSearch.add(data)
      })


      // INITIALIZATION OF GO TO
      // =======================================================
      new HSGoTo('.js-go-to')

      // INITIALIZATION OF LIVE TOAST
      // =======================================================
      const liveToast = new bootstrap.Toast(document.querySelector('#liveToast'))
      document.querySelector('#liveToastBtn').addEventListener('click', () => liveToast.show())
    })()
  </script>
</body>

<!-- Mirrored from htmlstream.com/front-dashboard/documentation/toasts.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 30 Aug 2022 07:13:04 GMT -->
</html>