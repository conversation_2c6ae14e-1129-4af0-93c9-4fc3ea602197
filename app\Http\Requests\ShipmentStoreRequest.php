<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ShipmentStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'customer_id' => ['required', 'tenant_exists:customers,id'],
            'customer_name' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'reference_no' => ['nullable', 'string', 'max:255'],
            'tracking_number' => ['nullable', 'string', 'max:255'],
            'carrier' => ['nullable', 'string', 'max:255'],
            'shipping_method' => ['nullable', 'string', 'max:255'],
            'shipping_address' => ['nullable', 'string'],
            'estimated_delivery' => ['nullable', 'date'],
            'actual_delivery' => ['nullable', 'date'],
            'weight' => ['nullable', 'numeric', 'min:0'],
            'dimensions' => ['nullable', 'string', 'max:255'],
            'insurance_value' => ['nullable', 'numeric', 'min:0'],
            'special_instructions' => ['nullable', 'string'],
            'sub_total' => ['required', 'numeric', 'min:0'],
            'amount_total' => ['required', 'numeric', 'min:0'],
            'amount_paid' => ['nullable', 'numeric', 'min:0'],
            'vat' => ['nullable', 'numeric', 'min:0'],
            'discount' => ['nullable', 'numeric', 'min:0'],
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date'],
            'expires_at' => ['nullable', 'date'],
            'currency_id' => ['nullable', 'tenant_exists:currencies,id'],
            'shipment' => ['nullable', 'string'], // JSON data for items
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'customer_id' => 'customer',
            'sub_total' => 'subtotal',
            'amount_total' => 'total amount',
            'amount_paid' => 'amount paid',
            'tracking_number' => 'tracking number',
            'shipping_method' => 'shipping method',
            'shipping_address' => 'shipping address',
            'estimated_delivery' => 'estimated delivery date',
            'actual_delivery' => 'actual delivery date',
            'insurance_value' => 'insurance value',
            'special_instructions' => 'special instructions',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'customer_id.required' => 'Please select a customer for this shipment.',
            'customer_id.tenant_exists' => 'The selected customer does not exist.',
            'sub_total.required' => 'The subtotal is required.',
            'amount_total.required' => 'The total amount is required.',
            'weight.numeric' => 'The weight must be a valid number.',
            'insurance_value.numeric' => 'The insurance value must be a valid number.',
        ];
    }
}
