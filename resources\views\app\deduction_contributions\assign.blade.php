@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                Assign Employees to {{ $deductionContribution->name }}
            </h4>
        </div>

        <div class="card-body">
            <form action="{{ route('deduction-contributions.assign', $deductionContribution) }}" method="POST">
                @csrf

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="start_date">Start Date</label>
                            <input type="date" name="start_date" id="start_date" class="form-control" value="{{ old('start_date', date('Y-m-d')) }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="end_date">End Date (Optional)</label>
                            <input type="date" name="end_date" id="end_date" class="form-control" value="{{ old('end_date') }}">
                        </div>
                    </div>
                </div>

                <div class="form-group mb-4">
                    <label>Select Employees</label>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th width="50">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="select-all">
                                        </div>
                                    </th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Basic Pay</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($employees as $employee)
                                <tr>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="employee_ids[]" value="{{ $employee->id }}" {{ in_array($employee->id, $assignedEmployees) ? 'checked' : '' }}>
                                        </div>
                                    </td>
                                    <td>{{ $employee->name }}</td>
                                    <td>{{ $employee->email }}</td>
                                    <td>{{ number_format($employee->basic_pay, 2) }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ route('deduction-contributions.show', $deductionContribution) }}" class="btn btn-light">
                        <i class="bi bi-arrow-left"></i> Back
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Assign Employees
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    $(document).ready(function() {
        // Select/deselect all checkboxes
        $('#select-all').click(function() {
            $('input[name="employee_ids[]"]').prop('checked', this.checked);
        });

        // If all checkboxes are selected, check the select-all checkbox
        $('input[name="employee_ids[]"]').click(function() {
            if ($('input[name="employee_ids[]"]').length == $('input[name="employee_ids[]"]:checked').length) {
                $('#select-all').prop('checked', true);
            } else {
                $('#select-all').prop('checked', false);
            }
        });
    });
</script>
@endpush
@endsection
