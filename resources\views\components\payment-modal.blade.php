<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentModalLabel">
                    <i class="bi bi-credit-card me-2"></i>Record Payment
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            
            <form id="paymentForm" method="POST">
                @csrf
                <div class="modal-body">
                    <!-- Entity Info Card -->
                    <div class="card bg-light mb-4" id="entityInfoCard" style="display: none;">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-circle me-3" id="entityAvatar">
                                    <span class="avatar-initials" id="entityInitials"></span>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1" id="entityName"></h6>
                                    <small class="text-muted" id="entityType"></small>
                                </div>
                                <div class="text-end">
                                    <div class="h5 text-danger mb-0" id="totalOutstanding"></div>
                                    <small class="text-muted">Total Outstanding</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Type Selection (for general payments) -->
                    <div class="mb-3" id="paymentTypeSection">
                        <label class="form-label">Payment Type <span class="text-danger">*</span></label>
                        <div class="tom-select-custom">
                            <select class="form-select" name="payment_type" id="paymentType" required>
                                <option value="">Select Payment Type</option>
                                <option value="receivable">Receivable (Customer Payment)</option>
                                <option value="payable">Payable (Supplier Payment)</option>
                            </select>
                        </div>
                    </div>

                    <!-- Step 1: Entity Selection -->
                    <div class="mb-4" id="entitySelectionSection">
                        <div id="customerSection" style="display: none;">
                            <label class="form-label">Step 1: Select Customer <span class="text-danger">*</span></label>
                            <div class="tom-select-custom">
                                <select class="form-select" name="customer_id" id="customerId" required>
                                    <option value="">Choose the customer who is making the payment</option>
                                </select>
                            </div>
                            <small class="form-text text-muted">Select the customer who owes money (receivable)</small>
                        </div>

                        <div id="supplierSection" style="display: none;">
                            <label class="form-label">Step 1: Select Supplier <span class="text-danger">*</span></label>
                            <div class="tom-select-custom">
                                <select class="form-select" name="supplier_id" id="supplierId" required>
                                    <option value="">Choose the supplier to pay</option>
                                </select>
                            </div>
                            <small class="form-text text-muted">Select the supplier you need to pay (payable)</small>
                        </div>
                    </div>

                    <!-- Step 2: Invoice/Order Selection -->
                    <div class="mb-4" id="invoiceSection" style="display: none;">
                        <label class="form-label" id="invoiceLabel">Step 2: Select Invoice <span class="text-danger">*</span></label>
                        <div class="tom-select-custom">
                            <select class="form-select" name="invoice_id" id="invoiceSelect" required>
                                <option value="">First select a customer/supplier above</option>
                            </select>
                        </div>
                        <small class="form-text text-muted" id="invoiceHelpText">Choose which invoice/order to pay</small>
                    </div>

                    <!-- Invoice Details Card -->
                    <div class="card bg-light mb-3" id="invoiceDetailsCard" style="display: none;">
                        <div class="card-body">
                            <h6 class="card-title">Invoice Details</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <strong>Invoice Date:</strong> <span id="invoiceDate">-</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>Total Amount:</strong> <span id="totalAmount">-</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <strong>Amount Paid:</strong> <span id="paidAmount">-</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>Outstanding Balance:</strong> <span id="outstandingBalance" class="text-danger fw-bold">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Amount -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Payment Amount <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" name="amount" id="paymentAmount" 
                                       step="0.01" min="0.01" placeholder="0.00" required>
                            </div>
                            <small class="form-text text-muted">Maximum: <span id="maxAmount">-</span></small>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">Payment Method</label>
                            <select class="form-select" name="payment_method">
                                <option value="">Select Method</option>
                                <option value="cash">Cash</option>
                                <option value="bank_transfer">Bank Transfer</option>
                                <option value="credit_card">Credit Card</option>
                                <option value="debit_card">Debit Card</option>
                                <option value="check">Check</option>
                                <option value="mobile_payment">Mobile Payment</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>

                    <!-- Reference and Description -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Reference Number</label>
                            <input type="text" class="form-control" name="reference_no" placeholder="Payment reference">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Currency</label>
                            <select class="form-select" name="currency_id">
                                <option value="">Default Currency</option>
                                <!-- Currency options will be loaded dynamically -->
                            </select>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="2" 
                                  placeholder="Payment description or notes"></textarea>
                    </div>

                    <!-- Comments -->
                    <div class="mb-3">
                        <label class="form-label">Internal Comments</label>
                        <textarea class="form-control" name="comment" rows="2" 
                                  placeholder="Internal comments (not visible to customer/supplier)"></textarea>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-primary" id="submitPaymentBtn">
                        <i class="bi bi-credit-card me-1"></i>Record Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentModal = document.getElementById('paymentModal');
    const paymentForm = document.getElementById('paymentForm');
    const paymentType = document.getElementById('paymentType');
    const customerSection = document.getElementById('customerSection');
    const supplierSection = document.getElementById('supplierSection');
    const customerId = document.getElementById('customerId');
    const supplierId = document.getElementById('supplierId');
    const invoiceSection = document.getElementById('invoiceSection');
    const invoiceSelect = document.getElementById('invoiceSelect');
    const invoiceDetailsCard = document.getElementById('invoiceDetailsCard');
    const paymentAmount = document.getElementById('paymentAmount');
    const entityInfoCard = document.getElementById('entityInfoCard');

    // Payment type change handler
    paymentType.addEventListener('change', function() {
        const type = this.value;

        // Hide all sections
        customerSection.style.display = 'none';
        supplierSection.style.display = 'none';
        invoiceSection.style.display = 'none';
        invoiceDetailsCard.style.display = 'none';
        entityInfoCard.style.display = 'none';

        // Clear selections
        customerId.value = '';
        supplierId.value = '';
        invoiceSelect.innerHTML = '<option value="">First select a customer/supplier above</option>';

        if (type === 'receivable') {
            customerSection.style.display = 'block';
            document.getElementById('invoiceLabel').textContent = 'Step 2: Select Invoice';
            document.getElementById('invoiceHelpText').textContent = 'Choose which customer invoice to record payment for';
            loadCustomersInModal();
        } else if (type === 'payable') {
            supplierSection.style.display = 'block';
            document.getElementById('invoiceLabel').textContent = 'Step 2: Select Order/Bill';
            document.getElementById('invoiceHelpText').textContent = 'Choose which supplier bill/order to pay';
            loadSuppliersInModal();
        }
    });

    // Customer selection change
    customerId.addEventListener('change', function() {
        // Clear previous invoice selection
        invoiceSelect.innerHTML = '<option value="">Loading customer invoices...</option>';
        invoiceDetailsCard.style.display = 'none';

        if (this.value) {
            invoiceSection.style.display = 'block';
            loadCustomerInvoices(this.value);
        } else {
            invoiceSection.style.display = 'none';
            invoiceSelect.innerHTML = '<option value="">First select a customer above</option>';
        }
    });

    // Supplier selection change
    supplierId.addEventListener('change', function() {
        // Clear previous order selection
        invoiceSelect.innerHTML = '<option value="">Loading supplier orders...</option>';
        invoiceDetailsCard.style.display = 'none';

        if (this.value) {
            invoiceSection.style.display = 'block';
            loadSupplierOrders(this.value);
        } else {
            invoiceSection.style.display = 'none';
            invoiceSelect.innerHTML = '<option value="">First select a supplier above</option>';
        }
    });

    // Invoice selection change
    invoiceSelect.addEventListener('change', function() {
        if (this.value) {
            loadInvoiceDetails(this.value);
        } else {
            invoiceDetailsCard.style.display = 'none';
        }
    });

    // Payment amount validation
    paymentAmount.addEventListener('input', function() {
        const amount = parseFloat(this.value);
        const maxAmount = parseFloat(this.getAttribute('max'));
        
        if (amount > maxAmount) {
            this.setCustomValidity('Amount cannot exceed outstanding balance');
        } else {
            this.setCustomValidity('');
        }
    });

    // Form submission
    paymentForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitBtn = document.getElementById('submitPaymentBtn');
        
        // Disable submit button
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Processing...';
        
        // Submit payment
        fetch('{{ route("payments.store-ajax") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal and refresh page
                bootstrap.Modal.getInstance(paymentModal).hide();
                location.reload();
            } else {
                alert(data.message || 'Failed to record payment');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to record payment');
        })
        .finally(() => {
            // Re-enable submit button
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="bi bi-credit-card me-1"></i>Record Payment';
        });
    });

    // Helper functions for loading data

    function loadCustomerInvoices(customerId) {
        fetch(`{{ route('payments.customer-invoices') }}?customer_id=${customerId}`)
            .then(response => response.json())
            .then(data => {
                const select = document.getElementById('invoiceSelect');
                select.innerHTML = '<option value="">Choose which invoice to record payment for</option>';

                if (data.invoices && data.invoices.length > 0) {
                    data.invoices.forEach(invoice => {
                        const invoiceNumber = invoice.invoice_number || `Invoice #${invoice.id}`;
                        const invoiceDate = invoice.date || invoice.created_at;
                        const balance = parseFloat(invoice.balance || (invoice.amount_total - invoice.amount_paid));

                        select.innerHTML += `<option value="${invoice.id}"
                            data-total="${invoice.amount_total}"
                            data-paid="${invoice.amount_paid || 0}"
                            data-balance="${balance}"
                            data-date="${invoiceDate}">
                            ${invoiceNumber} - ${invoiceDate} (Outstanding: $${balance.toFixed(2)})
                        </option>`;
                    });
                } else {
                    select.innerHTML = '<option value="" disabled>This customer has no outstanding invoices</option>';
                }
            })
            .catch(error => {
                console.error('Error loading customer invoices:', error);
                document.getElementById('invoiceSelect').innerHTML = '<option value="" disabled>Error loading invoices</option>';
            });
    }

    function loadSupplierOrders(supplierId) {
        fetch(`{{ route('payments.supplier-bills') }}?supplier_id=${supplierId}`)
            .then(response => response.json())
            .then(data => {
                const select = document.getElementById('invoiceSelect');
                select.innerHTML = '<option value="">Choose which order/bill to pay</option>';

                if (data.bills && data.bills.length > 0) {
                    data.bills.forEach(bill => {
                        const orderNumber = bill.order_number || `Order #${bill.id}`;
                        const orderDate = bill.date || bill.created_at;
                        const balance = parseFloat(bill.balance || (bill.amount_total - bill.amount_paid));

                        select.innerHTML += `<option value="${bill.id}"
                            data-total="${bill.amount_total}"
                            data-paid="${bill.amount_paid || 0}"
                            data-balance="${balance}"
                            data-date="${orderDate}">
                            ${orderNumber} - ${orderDate} (Outstanding: $${balance.toFixed(2)})
                        </option>`;
                    });
                } else {
                    select.innerHTML = '<option value="" disabled>This supplier has no outstanding bills</option>';
                }
            })
            .catch(error => {
                console.error('Error loading supplier bills:', error);
                document.getElementById('invoiceSelect').innerHTML = '<option value="" disabled>Error loading orders</option>';
            });
    }

    function loadInvoiceDetails(invoiceId) {
        const selectedOption = document.querySelector(`#invoiceSelect option[value="${invoiceId}"]`);

        if (selectedOption) {
            const total = selectedOption.dataset.total;
            const paid = selectedOption.dataset.paid;
            const balance = selectedOption.dataset.balance;
            const date = selectedOption.dataset.date;

            document.getElementById('invoiceDate').textContent = date;
            document.getElementById('totalAmount').textContent = '$' + parseFloat(total).toFixed(2);
            document.getElementById('paidAmount').textContent = '$' + parseFloat(paid).toFixed(2);
            document.getElementById('outstandingBalance').textContent = '$' + parseFloat(balance).toFixed(2);
            document.getElementById('maxAmount').textContent = '$' + parseFloat(balance).toFixed(2);

            // Set max amount for payment input
            document.getElementById('paymentAmount').setAttribute('max', balance);

            document.getElementById('invoiceDetailsCard').style.display = 'block';
        }
    }
});

// Global function to open payment modal with pre-filled data
window.openPaymentModal = function(options = {}) {
    console.log('openPaymentModal called with options:', options);

    try {
        const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
        const form = document.getElementById('paymentForm');

        if (!modal || !form) {
            console.error('Modal or form not found');
            alert('Payment modal not found. Please refresh the page.');
            return;
        }

        // Reset form and hide all sections
        form.reset();
        document.getElementById('entityInfoCard').style.display = 'none';
        document.getElementById('invoiceDetailsCard').style.display = 'none';
        document.getElementById('invoiceSection').style.display = 'none';
        document.getElementById('customerSection').style.display = 'none';
        document.getElementById('supplierSection').style.display = 'none';

        // Reset dropdowns
        document.getElementById('customerId').innerHTML = '<option value="">Loading customers...</option>';
        document.getElementById('supplierId').innerHTML = '<option value="">Loading suppliers...</option>';
        document.getElementById('invoiceSelect').innerHTML = '<option value="">First select a customer/supplier above</option>';

        // Show/hide payment type section based on context
        const paymentTypeSection = document.getElementById('paymentTypeSection');
        const entitySelectionSection = document.getElementById('entitySelectionSection');

        if (options.paymentType) {
            console.log('Setting payment type to:', options.paymentType);
            // Hide payment type selection if coming from specific context
            paymentTypeSection.style.display = 'none';
            entitySelectionSection.style.display = 'block';

            document.getElementById('paymentType').value = options.paymentType;

            // Load appropriate entities and show section
            if (options.paymentType === 'receivable') {
                console.log('Loading customers for receivable payment');
                loadCustomersInModal(options.entityId);
                document.getElementById('customerSection').style.display = 'block';
            } else if (options.paymentType === 'payable') {
                console.log('Loading suppliers for payable payment');
                loadSuppliersInModal(options.entityId);
                document.getElementById('supplierSection').style.display = 'block';
            }
        } else {
            // Show payment type selection for general payments
            paymentTypeSection.style.display = 'block';
            entitySelectionSection.style.display = 'none';

            // Load both customers and suppliers for general use
            loadCustomersInModal();
            loadSuppliersInModal();
        }

        if (options.invoiceId) {
            setTimeout(() => {
                document.getElementById('invoiceSelect').value = options.invoiceId;
                document.getElementById('invoiceSelect').dispatchEvent(new Event('change'));
            }, 500);
        }

        // Show modal
        console.log('Showing payment modal');
        modal.show();

    } catch (error) {
        console.error('Error opening payment modal:', error);
        alert('Error opening payment modal: ' + error.message);
    }
};

// Load customers into modal dropdown
function loadCustomersInModal(selectedId = null) {
    const customersUrl = '{{ route("customers.api") }}';
    console.log('Loading customers from:', customersUrl);

    fetch(customersUrl)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(customers => {
            console.log('Customers loaded:', customers);
            const customerSelect = document.getElementById('customerId');

            // Check if response contains an error
            if (customers.error) {
                console.error('API Error:', customers.error);
                customerSelect.innerHTML = `<option value="">Error: ${customers.error}</option>`;
                return;
            }

            customerSelect.innerHTML = '<option value="">Choose the customer who is making the payment</option>';

            if (customers && customers.length > 0) {
                customers.forEach(customer => {
                    const option = document.createElement('option');
                    option.value = customer.id;
                    option.textContent = customer.name;
                    if (selectedId && customer.id == selectedId) {
                        option.selected = true;
                    }
                    customerSelect.appendChild(option);
                });
            } else {
                customerSelect.innerHTML = '<option value="">No customers found</option>';
            }

            // Trigger change event if a customer was pre-selected
            if (selectedId) {
                setTimeout(() => {
                    customerSelect.dispatchEvent(new Event('change'));
                }, 100);
            }
        })
        .catch(error => {
            console.error('Error loading customers:', error);
            document.getElementById('customerId').innerHTML = '<option value="">Error loading customers - Check console</option>';
        });
}

// Load suppliers into modal dropdown
function loadSuppliersInModal(selectedId = null) {
    const suppliersUrl = '{{ route("suppliers.api") }}';
    console.log('Loading suppliers from:', suppliersUrl);

    fetch(suppliersUrl)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(suppliers => {
            console.log('Suppliers loaded:', suppliers);
            const supplierSelect = document.getElementById('supplierId');

            // Check if response contains an error
            if (suppliers.error) {
                console.error('API Error:', suppliers.error);
                supplierSelect.innerHTML = `<option value="">Error: ${suppliers.error}</option>`;
                return;
            }

            supplierSelect.innerHTML = '<option value="">Choose the supplier to pay</option>';

            if (suppliers && suppliers.length > 0) {
                suppliers.forEach(supplier => {
                    const option = document.createElement('option');
                    option.value = supplier.id;
                    option.textContent = supplier.name;
                    if (selectedId && supplier.id == selectedId) {
                        option.selected = true;
                    }
                    supplierSelect.appendChild(option);
                });
            } else {
                supplierSelect.innerHTML = '<option value="">No suppliers found</option>';
            }

            // Trigger change event if a supplier was pre-selected
            if (selectedId) {
                setTimeout(() => {
                    supplierSelect.dispatchEvent(new Event('change'));
                }, 100);
            }
        })
        .catch(error => {
            console.error('Error loading suppliers:', error);
            document.getElementById('supplierId').innerHTML = '<option value="">Error loading suppliers - Check console</option>';
        });
}
</script>
