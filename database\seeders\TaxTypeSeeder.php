<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\TaxType;
use App\Models\Account;

class TaxTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get tax account
        $taxAccount = Account::where('code', '2103')->first();
        
        if (!$taxAccount) {
            return;
        }
        
        // Create default tax types
        $taxTypes = [
            [
                'name' => 'Sales Tax',
                'code' => 'ST',
                'description' => 'Standard sales tax',
                'tax_type' => 'sales_tax',
                'rate' => 0.07,
                'tax_account_id' => $taxAccount->id,
                'is_active' => true,
            ],
            [
                'name' => 'Value Added Tax',
                'code' => 'VAT',
                'description' => 'Value added tax',
                'tax_type' => 'vat',
                'rate' => 0.20,
                'tax_account_id' => $taxAccount->id,
                'is_active' => true,
            ],
            [
                'name' => 'Goods and Services Tax',
                'code' => 'GST',
                'description' => 'Goods and services tax',
                'tax_type' => 'sales_tax',
                'rate' => 0.05,
                'tax_account_id' => $taxAccount->id,
                'is_active' => true,
            ],
            [
                'name' => 'Income Tax Withholding',
                'code' => 'ITW',
                'description' => 'Income tax withholding',
                'tax_type' => 'withholding',
                'rate' => 0.10,
                'tax_account_id' => $taxAccount->id,
                'is_active' => true,
            ],
        ];

        foreach ($taxTypes as $taxType) {
            TaxType::updateOrCreate(
                ['code' => $taxType['code']],
                $taxType
            );
        }
    }
}
