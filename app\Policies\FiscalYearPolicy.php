<?php

namespace App\Policies;

use App\Models\User;
use App\Models\FiscalYear;
use Illuminate\Auth\Access\HandlesAuthorization;

class FiscalYearPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo('list fiscal-years');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\FiscalYear  $fiscalYear
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, FiscalYear $fiscalYear)
    {
        return $user->hasPermissionTo('view fiscal-years');
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo('create fiscal-years');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\FiscalYear  $fiscalYear
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, FiscalYear $fiscalYear)
    {
        return $user->hasPermissionTo('update fiscal-years') &&
            !$fiscalYear->is_closed;
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\FiscalYear  $fiscalYear
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, FiscalYear $fiscalYear)
    {
        return $user->hasPermissionTo('delete fiscal-years') &&
            !$fiscalYear->is_closed &&
            $fiscalYear->fiscalPeriods()->count() === 0 &&
            $fiscalYear->journalEntries()->count() === 0;
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\FiscalYear  $fiscalYear
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, FiscalYear $fiscalYear)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\FiscalYear  $fiscalYear
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, FiscalYear $fiscalYear)
    {
        return false;
    }

    /**
     * Determine whether the user can close the fiscal year.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\FiscalYear  $fiscalYear
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function close(User $user, FiscalYear $fiscalYear)
    {
        return $user->hasPermissionTo('close fiscal-years') &&
            $fiscalYear->status === 'open';
    }

    /**
     * Determine whether the user can reopen the fiscal year.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\FiscalYear  $fiscalYear
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function reopen(User $user, FiscalYear $fiscalYear)
    {
        return $user->hasPermissionTo('reopen fiscal-years') &&
            $fiscalYear->status === 'closed' &&
            $user->hasRole('super-admin');
    }
}
