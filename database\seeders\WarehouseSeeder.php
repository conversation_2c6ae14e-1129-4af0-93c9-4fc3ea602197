<?php

namespace Database\Seeders;

use App\Models\Warehouse;
use App\Models\Branch;
use App\Models\BusinessType;
use Illuminate\Database\Seeder;

class WarehouseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get the first business type and branch
        $businessType = BusinessType::first();
        $branch = Branch::first();

        if (!$businessType || !$branch) {
            $this->command->warn('No business type or branch found. Please run BusinessTypeSeeder and BranchSeeder first.');
            return;
        }

        $warehouses = [
            [
                'name' => 'Main Warehouse',
                'code' => 'WH001',
                'address' => '123 Main Street, Business District',
                'phone' => '+1234567890',
                'email' => '<EMAIL>',
                'description' => 'Primary warehouse for storing main inventory',
                'is_active' => true,
                'branch_id' => $branch->id,
                'business_type_id' => $businessType->id,
            ],
            [
                'name' => 'Secondary Warehouse',
                'code' => 'WH002',
                'address' => '456 Industrial Avenue, Warehouse District',
                'phone' => '+1234567891',
                'email' => '<EMAIL>',
                'description' => 'Secondary warehouse for overflow inventory',
                'is_active' => true,
                'branch_id' => $branch->id,
                'business_type_id' => $businessType->id,
            ],
            [
                'name' => 'Cold Storage Warehouse',
                'code' => 'WH003',
                'address' => '789 Cold Storage Lane, Industrial Zone',
                'phone' => '+1234567892',
                'email' => '<EMAIL>',
                'description' => 'Specialized warehouse for temperature-sensitive items',
                'is_active' => true,
                'branch_id' => $branch->id,
                'business_type_id' => $businessType->id,
            ],
        ];

        foreach ($warehouses as $warehouseData) {
            Warehouse::create($warehouseData);
        }

        $this->command->info('Warehouses seeded successfully!');
    }
}
