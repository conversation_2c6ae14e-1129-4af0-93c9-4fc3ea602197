<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;
use App\Traits\BranchTrait;

class Quotation extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;
    use BranchTrait;
    protected $connection = "tenant";

    protected $fillable = [
        'date_from',
        'date_to',
        'vat',
        'description',
        'sub_total',
        'amount_total',
        'customer_id',
        'customer_name',
        'amount_paid',
        'branch_id',
        'discount',
        'approved_by',
        'status_id',
        'created_by',
        'updated_by',
        'business_type_id',
        'warehouse_id',
        'expires_at',
        'currency_id',
        'reference_no',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
        'date_from' => 'datetime:Y-m-d',
        'date_to' => 'datetime:Y-m-d',
    ];

    protected $appends = ['quotation_id', 'prepared_by'];

    public function getPreparedByAttribute(){
        return $this->createdBy->name ?? '';
    }

    public function getQuotationIdAttribute(){
        return _pad( $this->id, 5, '0');
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }


    public function payments()
    {
        return $this->morphMany(Payment::class, 'paymentable');
    }
    
    public function sales()
    {
        return $this->morphMany(Sale::class, 'saleable');
    }
}
