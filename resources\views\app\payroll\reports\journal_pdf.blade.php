<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Payroll Journal Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
        }
        .container {
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .header p {
            margin: 5px 0;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table th, table td {
            border: 1px solid #ddd;
            padding: 6px;
            font-size: 10px;
        }
        table th {
            background-color: #f2f2f2;
            text-align: left;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .summary {
            margin-top: 20px;
        }
        .summary h2 {
            font-size: 16px;
            margin-bottom: 10px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Payroll Journal Report</h1>
            <p>Period: {{ $monthName }} {{ $year }}</p>
            <p>Generated on: {{ date('d M Y H:i:s') }}</p>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Employee</th>
                    <th>Basic Pay</th>
                    <th>Contributions</th>
                    <th>Gross Pay</th>
                    <th>PAYE Tax</th>
                    <th>Other Deductions</th>
                    <th>Total Deductions</th>
                    <th>Net Pay</th>
                </tr>
            </thead>
            <tbody>
                @forelse($payrolls as $payroll)
                <tr>
                    <td>{{ $payroll->employee->name }}</td>
                    <td class="text-right">{{ number_format($payroll->basic_pay, 2) }}</td>
                    <td class="text-right">{{ number_format($payroll->contributions->sum('amount'), 2) }}</td>
                    <td class="text-right">{{ number_format($payroll->gross, 2) }}</td>
                    <td class="text-right">{{ number_format($payroll->payee, 2) }}</td>
                    <td class="text-right">{{ number_format($payroll->deductions->sum('amount'), 2) }}</td>
                    <td class="text-right">{{ number_format($payroll->payee + $payroll->deductions->sum('amount'), 2) }}</td>
                    <td class="text-right">{{ number_format($payroll->net_pay, 2) }}</td>
                </tr>
                @empty
                <tr>
                    <td colspan="8" class="text-center">No payroll records found for this period.</td>
                </tr>
                @endforelse
            </tbody>
            <tfoot>
                <tr>
                    <th>Total</th>
                    <th class="text-right">{{ number_format($totalBasicPay, 2) }}</th>
                    <th class="text-right">{{ number_format($totalGrossPay - $totalBasicPay, 2) }}</th>
                    <th class="text-right">{{ number_format($totalGrossPay, 2) }}</th>
                    <th class="text-right">{{ number_format($payrolls->sum('payee'), 2) }}</th>
                    <th class="text-right">{{ number_format($totalDeductions - $payrolls->sum('payee'), 2) }}</th>
                    <th class="text-right">{{ number_format($totalDeductions, 2) }}</th>
                    <th class="text-right">{{ number_format($totalNetPay, 2) }}</th>
                </tr>
            </tfoot>
        </table>

        <div class="summary">
            <h2>Journal Entry Summary</h2>
            <table>
                <thead>
                    <tr>
                        <th>Account</th>
                        <th>Debit</th>
                        <th>Credit</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Salary Expense</td>
                        <td class="text-right">{{ number_format($totalGrossPay, 2) }}</td>
                        <td class="text-right">-</td>
                    </tr>
                    <tr>
                        <td>PAYE Tax Payable</td>
                        <td class="text-right">-</td>
                        <td class="text-right">{{ number_format($payrolls->sum('payee'), 2) }}</td>
                    </tr>
                    <tr>
                        <td>Deductions Payable</td>
                        <td class="text-right">-</td>
                        <td class="text-right">{{ number_format($totalDeductions - $payrolls->sum('payee'), 2) }}</td>
                    </tr>
                    <tr>
                        <td>Cash/Bank</td>
                        <td class="text-right">-</td>
                        <td class="text-right">{{ number_format($totalNetPay, 2) }}</td>
                    </tr>
                    <tr>
                        <th>Total</th>
                        <th class="text-right">{{ number_format($totalGrossPay, 2) }}</th>
                        <th class="text-right">{{ number_format($totalGrossPay, 2) }}</th>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="footer">
            <p>This is a computer-generated document. No signature is required.</p>
        </div>
    </div>
</body>
</html>
