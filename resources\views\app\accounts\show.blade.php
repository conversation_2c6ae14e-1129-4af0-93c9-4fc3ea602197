@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">{{ $account->name }}  Transactions</x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Transaction::class)
            <a href="/transactions/create?account_id={{ $account->id }}" class="btn btn-info btn-sm">
                @lang('crud.common.create') Transaction
            </a>
            @endcan            

            @can('create', App\Models\Account::class)
            <a href="{{ route('accounts.create') }}" class="btn btn-info btn-sm">
                @lang('crud.common.create') Account
            </a>
            @endcan
        </x-slot>
    </x-page-header>


    <div class="searchbar mt-4 mb-5">
        <form class="d-flex" id="filter">

            <div class="me-2 _200px">
                <label class="form-label">Date Range</label>
                <input type="hidden" name="from" value="{{ request()->from }}">
                <input type="hidden" name="to" value="{{ request()->to }}">
                <button id="js-daterangepicker-predefined" type="button" style="width:100%" class="btn btn-white">
                  <i class="bi-calendar-week me-1"></i>
                  <span class="js-daterangepicker-predefined-preview"></span>
                </button>
            </div>


            <div class="me-2">
                <label class="form-label">Search</label>
                <div class="tom-select-custom _200px">
                  <input class="form-control" name="search">
                </div>
            </div>


            <div class="">
                <label class="form-label transparent">_</label>
                <div class="input-group-append">
                    <button type="submit" class="btn btn-primary">
                        Search
                    </button>
                </div>
            </div>


        </form>
        <hr>
        <div>
            <table>
                <tr>
                    <th>Account:</th>
                    <td>{{ $account->name }}</td>
                </tr>                
                <tr>
                    <th>Credit:</th>
                    <td>{{ _money( $account->credit) }}</td>
                </tr>               
                <tr>
                    <th>Debit:</th>
                    <td>{{ _money( $account->debit) }}</td>
                </tr>                
                <tr>
                    <th>Balance:</th>
                    <td>{{ _money( $account->balance) }}</td>
                </tr>
            </table>
        </div>
    </div>


    <!-- End Page Header -->
    <div class="card card-table">
        <div class="table-responsive mt-3">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            Account
                        </th>
                        <th class="text-left">
                            Description
                        </th>
                        <th class="text-left">
                            Date
                        </th>
                        <th class="text-left">
                            Debit
                        </th>
                        <th class="text-left">
                            Credit
                        </th>
                        <th class="text-left">
                            Balance
                        </th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($transactions as $transaction)
                    <tr>
                        <td>{{ $transaction->account->name ?? '-' }}</td>
                        <td>{{ $transaction->description ?? '-' }}</td>
                        <td>{{ $transaction->date ?? '-' }}</td>
                        <td><span class="text-primary">{{ _money($transaction->debit) ?? 0 }}</span></td>
                        <td><span class="text-primary">{{ _money($transaction->credit) ?? 0 }}</span></td>
                        <td><span class="text-primary">{{ _money($transaction->balance) ?? 0 }}</span></td>
                        <td class="text-center" style="width: 134px;">
                            <div
                                role="group"
                                aria-label="Row Actions"
                                class="btn-group"
                            >
                                @can('update', $transaction)
                                <a
                                    href="/transactions/{{ $transaction->id }}/edit?account_id={{ $account->id }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        <!-- <i class="icon ion-md-create"></i> --> edit
                                    </button>
                                </a>
                                @endcan @can('view', $transaction)
<!--                                 <a
                                    href="{{ route('transactions.show', $transaction) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        view
                                     --></button>
                                </a>
                                @endcan @can('delete', $transaction)
                                <form
                                    action="{{ route('transactions.destroy', $transaction) }}"
                                    method="POST"
                                    onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                >
                                    @csrf @method('DELETE')
                                    <button
                                        type="submit"
                                        class="btn btn-light text-danger m-1"
                                    >
                                        del
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                   
                    @endforelse
                </tbody>
                
            </table>
        </div>
    </div>
    

</div>
@endsection




@push('scripts')
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    dataTableBtn(".js-datatable", null, [2, 'desc']);
  });
</script>


@endpush