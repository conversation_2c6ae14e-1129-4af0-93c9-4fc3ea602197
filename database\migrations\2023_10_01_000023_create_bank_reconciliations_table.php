<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bank_reconciliations', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('bank_account_id');
            $table->date('statement_date');
            $table->decimal('statement_balance', 20, 2);
            $table->decimal('book_balance', 20, 2);
            $table->decimal('reconciled_balance', 20, 2);
            $table->decimal('difference', 20, 2)->default(0);
            $table->string('status')->default('draft'); // draft, in_progress, completed
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unsignedBigInteger('completed_by')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->unsignedBigInteger('business_type_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->foreign('bank_account_id')
                ->references('id')
                ->on('bank_accounts')
                ->onDelete('cascade');
                
            $table->foreign('created_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('updated_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('completed_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('business_type_id')
                ->references('id')
                ->on('business_types')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bank_reconciliations');
    }
};
