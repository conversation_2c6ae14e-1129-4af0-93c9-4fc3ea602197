@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">pencil-square</x-slot>
        <x-slot name="title">Edit Supplier: {{ $supplier->name }}</x-slot>
        <x-slot name="subtitle">Update supplier information</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('suppliers.index') }}">Suppliers</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('suppliers.show', $supplier) }}" class="btn btn-outline-info me-2">
                <i class="bi bi-eye me-1"></i> View Supplier
            </a>
            <a href="{{ route('suppliers.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Suppliers
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <div class="col-lg-8 mb-3 mb-lg-0">
            <!-- Card -->
            <div class="card mb-3 mb-lg-5">
                <!-- Header -->
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-header-title">Supplier Information</h4>
                        <span class="badge bg-primary">ID: {{ $supplier->id }}</span>
                    </div>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <x-form
                        method="PUT"
                        action="{{ route('suppliers.update', $supplier) }}"
                        id="supplierForm"
                    >
                        @include('app.suppliers.form-inputs')
                    </x-form>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>

        <div class="col-lg-4">
            <!-- Card -->
            <div class="card">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Actions</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <!-- Form Group -->
                        <div class="mb-4">
                            <label class="form-label">Last Updated</label>
                            <div class="mb-2">
                                <span class="text-body">{{ $supplier->updated_at->format('F d, Y H:i') }}</span>
                            </div>
                        </div>
                        <!-- End Form Group -->

                        <div class="d-flex justify-content-end">
                            <a href="{{ route('suppliers.index') }}" class="btn btn-white me-2">Cancel</a>
                            <button type="submit" form="supplierForm" class="btn btn-primary">
                                <i class="bi-check-circle me-1"></i> Update
                            </button>
                        </div>

                        <hr class="my-4">

                        <div class="mt-2">
                            <a href="{{ route('suppliers.create') }}" class="btn btn-outline-primary btn-sm">
                                <i class="bi-plus-circle me-1"></i> Create New Supplier
                            </a>

                            @can('delete', $supplier)
                            <form action="{{ route('suppliers.destroy', $supplier) }}"
                                  method="POST"
                                  class="mt-2"
                                  onsubmit="return confirm('Are you sure you want to delete this supplier?')">
                                @csrf @method('DELETE')
                                <button type="submit" class="btn btn-outline-danger btn-sm">
                                    <i class="bi-trash me-1"></i> Delete
                                </button>
                            </form>
                            @endcan
                        </div>
                    </div>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->

            <!-- Card -->
            <div class="card mt-3">
                <!-- Header -->
                <div class="card-header">
                    <h4 class="card-header-title">Activity</h4>
                </div>
                <!-- End Header -->

                <!-- Body -->
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <span class="flex-shrink-0 me-3">
                            <i class="bi bi-box fs-2 text-primary"></i>
                        </span>
                        <div class="flex-grow-1">
                            <h5 class="mb-0">{{ count($supplier->orders ?? []) }} Orders</h5>
                            <small class="text-muted">Orders from this supplier</small>
                        </div>
                    </div>

                    <a href="{{ route('suppliers.show', $supplier) }}" class="btn btn-outline-primary btn-sm w-100">
                        <i class="bi-eye me-1"></i> View Details
                    </a>
                </div>
                <!-- End Body -->
            </div>
            <!-- End Card -->
        </div>
    </div>
</div>
@endsection
