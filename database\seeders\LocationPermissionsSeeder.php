<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class LocationPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create location permissions
        $permissions = [
            'view_any_location',
            'view_location',
            'create_location',
            'update_location',
            'delete_location',
            'restore_location',
            'force_delete_location',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permissions to roles
        $adminRole = Role::where('name', 'super-admin')->first();
        if ($adminRole) {
            $adminRole->givePermissionTo($permissions);
        }

        // Create a basic role for location management
        $locationManagerRole = Role::firstOrCreate(['name' => 'location-manager']);
        $locationManagerRole->givePermissionTo([
            'view_any_location',
            'view_location',
            'create_location',
            'update_location',
        ]);

        // Create a read-only role for locations
        $locationViewerRole = Role::firstOrCreate(['name' => 'location-viewer']);
        $locationViewerRole->givePermissionTo([
            'view_any_location',
            'view_location',
        ]);

        $this->command->info('Location permissions seeded successfully!');
    }
}
