<form action="/import-products" method="POST" id="import-product-el" enctype="multipart/form-data">
	@csrf
	<x-lg-modal>
		<x-slot name="class">import-products-modal</x-slot>
		<x-slot name="title">Import Products</x-slot>

		<div>
			<input type="file" name="products" class="form-control p-2">
			<p class="mt-2">Allowed file is Excel with Max Size of 20MBs.</p>
		</div>

		<x-slot name="footer">
				<button type="submit" class="btn btn-primary"> Upload </button>
				<!-- <a href="/product-template" class="btn btn-outline-primary"> Download Template </a> -->
		</x-slot>
	</x-lg-modal>
</form>




@push('scripts')

  <script type="text/javascript">
    
    // new Vue({
    //   el: "#approve-invoice-el",
    //   data(){
    //     return{
    //       invoice: null,
    //     }
    //   },

    //   methods: {

    //     getInvoice(id) {
    //       axios.get('/ajax-invoice/' + id).then( res => {
    //       	this.invoice = res.data;
    //       	$(".invoice-approval-form").attr("action", "/approve-invoice/" + id);
    //       	console.log(this.invoice)
    //       }) 
    //     },

    //   },


    //   mounted(){
    //   	$(".pending-invoice-btn").click( (e) => {
    //   		var id = $(e.target).attr("data-id");
    //   		this.getInvoice(id);
    //   	})
    //   }

    // });

  </script>

@endpush
