<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class InvoiceUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date'],
            'amount_total' => ['nullable', 'numeric'],
            'amount_paid' => ['nullable', 'numeric'],
            'discount' => ['nullable', 'numeric'],
            'customer_id' => ['nullable'],
            'customer_name' => ['nullable'],
            'sub_total' => ['nullable', 'numeric'],
            'vat' => ['nullable', 'numeric'],
            'description' => ['nullable', 'max:255', 'string'],
        ];
    }
}
