.toastify-custom {
    border-radius: 0.45rem;
    height: 30px;
    padding-top: 5px;
    padding-bottom: 5px;
    font-size: 14px;
}

button {
    border-radius: 0.45rem;
    font-weight: bold;
}

.dropdown-toggle::after {
    display: none;
}

.dropdown-menu {
    padding: 0;
    border-radius: 0.45rem;
}

.dropdown-menu a:first-of-type {
    border-radius: 0.45rem 0.45rem 0 0;
}

.dropdown-menu a:last-of-type {
    border-radius: 0 0 0.45rem 0.45rem;
}

table thead th {
    text-transform: uppercase;
    font-size: 0.7rem;
    color: dimgrey;
    background-color: #f4f7fa;
    padding-top: 0.45rem;
    padding-bottom: 0.45rem;
    border-top: none;
    letter-spacing: 0.05rem;
}

.table-hover tbody tr:hover {
    background-color: #f6fbff;
}

.card {
    border-radius: 0.45rem;
}

.card-header {
    background-color: #fff;
    padding: 0.75rem;
}

.card-header:first-child {
    border-radius: calc(0.45rem - 1px) calc(0.45rem - 1px) 0 0;
}

.btn {
    border-radius: 0.45rem;
    padding: 0.2rem 1.1rem;
}

.btn-refresh {
    font-size: 0.9rem;
    line-height: 1.6;
}

.btn-refresh.loading svg {
    animation: loading-spinner 1s linear infinite;
}

@keyframes loading-spinner {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.action-button svg {
    fill: dimgrey;
    fill-opacity: 0.3;
}

.action-button:hover svg {
    fill: #3c86cc;
    fill-opacity: 0.7;
}

a.action-button:hover {
    text-decoration: none;
}

.cancel-button {
    border: none;
}

.delete-button {
    font-weight: bold;
}

.modal-content {
    border-radius: 0.45rem;
    border-width: 0;
}

.modal-body {
    padding: 1.25rem;
}

.modal-footer {
    border-top: none;
    background-color: #f3f7fa;
    padding: 0.5rem 1rem;
    border-bottom-right-radius: 0.45rem;
    border-bottom-left-radius: 0.45rem;
}
