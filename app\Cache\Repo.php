<?php

namespace App\Cache;

use App\Models\Order;
use App\Models\Debt;
use App\Models\Sale;
use App\Models\Quotation;
use App\Models\Supplier;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\StockItem;
use App\Models\Stock;
use Carbon\Carbon;

/**
 * 
 */
class Repo
{
	
	
	public function dateFrom(){

		$days = 30;
		if( request()->is("/") && ! request()->from ){
			$days = 0;
		}

    	$date = request()->from ?? Carbon::now()->subDays($days)->format('Y-m-d') . " 00:00:00";;
        return $date;
    }

    public function dateTo(){
    	$date = request()->to ?? Carbon::now()->addDays(1)->format('Y-m-d') . " 23:00:00";
    	if( \Str::length( $date ) < 15 ) {
    		$date = $date . " 23:00:00";
    	}
        return $date;
    }


    public function getDay(){
    	$lastClosingDate = Invoice::latest()->whereNotNull("closed_at")->first();
    	if( $lastClosingDate ){
    		return $lastClosingDate->closed_at->format("Y-m-d");
    	} else {
    		return Date('Y-m-d');
    	}
    }

    public function getClosingDays(){
        return Invoice::whereNotNull("closed_at")
            ->select('id', 'closed_at', 'created_at')
            ->groupBy("closed_at")
            ->pluck("closed_at")
            ->toArray();
    }

    public function applyDayFilter($build){
    	if( request()->day ) {
    		$build->where('closed_at', request()->day );
    	}
    	return $build;
    }

    public function applyDateWithDayFilter($build){
    	if( request()->day ) {
    		$build->where('closed_at', request()->day );
    	} else {
    		$build->whereBetween('created_at', [ $this->dateFrom(), $this->dateTo() ]);
    	}
    	return $build;
    }

    public function applyDateFilter($build){
    	$build->whereBetween('created_at', [ $this->dateFrom(), $this->dateTo() ]);
    	return $build;
    }

	public function getReceivableMoney() {
		$sales =  $this->applyDateWithDayFilter( Invoice::query() );
		if( request()->product_id ) $sales->whereHas("sales", function($q){
			$q->where("product_id", request()->product_id );
		});
		return $this->getSaleMoney() - $this->getReceivedMoney() ;
	}

	public function getDiscountMoney() {
		$invoices =  $this->applyDateWithDayFilter( Invoice::query() );

		if( request()->product_id ) $invoices->whereHas("stockItems", function($q){
			$q->where("product_id", request()->product_id );
		});

		return $invoices->sum("discount");
	}

	public function getSaleMoney() {
		
		$sales =  $this->applyDateWithDayFilter( Invoice::query() );

		if( request()->product_id ) $sales->whereHas("sales", function($q){
			$q->where("product_id", request()->product_id );
		});

		$sales->whereNotNull("approved_by")->whereIn("status_id", [10]);

		return $sales->sum("amount_total");
	}

	public function getReceivedMoney() {
		$sales =  $this->applyDateWithDayFilter( Invoice::query() );

		if( request()->product_id ) $sales->whereHas("stockItems", function($q){
			$q->where("product_id", request()->product_id );
		});

		$sales->whereNotNull("approved_by")->whereIn("status_id", [10]);

		return $sales->sum("amount_paid");
	}


	public function getAnnualSales() {

        $data = [];
		$year = ( request()->year ) ? request()->year : date("Y");
        for ($i = 1; $i <= 12; $i++) {
            $sales = Invoice::whereMonth('created_at', $i)
            		->whereYear("created_at", $year)
            		->whereNotNull("approved_by")
            		->whereIn("status_id", [10]);
			$data[] =  $sales->sum('amount_total');
        }
        return $data;
	}

	public function getInvoiceCursor($status = 10){

		$invoices =  $this->applyDateWithDayFilter( Invoice::query() )
			->search( request()->search )
            ->latest();

		$invoices->where("status_id", $status);

		return $invoices;

	}

	public function getOrderCursor($status = 10){

		$orders =  $this->applyDateWithDayFilter( Order::query() )
			->search( request()->search )
            ->latest();

		// $orders->where("status_id", $status);

		return $orders;

	}

	public function getQuotationCursor($status = 10){

		$quotations =  $this->applyDateWithDayFilter( Quotation::query() )
			->search( request()->search )
            ->latest();

		// $quotations->where("status_id", $status);

		return $quotations;

	}

	public function inStockCursor() {
		$products = Product::query();
		// $products->where("stockItems", function($q) { $q->where('created_at', "<=", $this->dateTo() ); });
		if( request()->product_id ) $products->where("id", request()->product_id );
		if( request()->category_id ) $products->where("category_id", request()->category_id );
		return $products;
	}

	public function getAvailableStockQuantity() {
		$products = Product::query();
        if( request()->product_id ) $products->where("id", request()->product_id );
		return $products->get()->sum("total_stock");
	}

	public function getOpeningStockQty() {


		return $this->getAvailableStockQuantity();

		$orders = Sale::query()->where("saleable_type", "App\Models\Order");
		$orders->where('created_at', "<=", $this->dateTo() );

		$invoices = Sale::query()->where("saleable_type", "App\Models\Invoice");
		$invoices->where('created_at', "<=", $this->dateTo() );

		$orders->whereHas("saleable", function($q){
			$q->whereNotNull("approved_by")->whereIn("status_id", [10, 14]);
		});

		$invoices->whereHas("saleable", function($q){
			$q->whereNotNull("approved_by")->whereIn("status_id", [10]);
		});

		if( request()->user_id ) {
			$orders->where("created_by", request()->user_id );
			$invoices->where("created_by", request()->user_id );
		}

		if( request()->branch_id ){
			$orders->where("branch_id", request()->branch_id );
			$invoices->where("branch_id", request()->branch_id );
		}

		if( request()->product_id ) {
			$orders->where("product_id", request()->product_id );
			$invoices->where("product_id", request()->product_id );
		}

		$orders = $orders->sum("unit_quantity") * $orders->sum("quantity");
		$invoices = $invoices->sum("unit_quantity") * $invoices->sum("quantity");
		return  $orders - $invoices;	
	}

	public function getOpeningStockQtyValue() {

		$orders = Sale::query()->where("saleable_type", "App\Models\Order");
		$orders->where('created_at', "<=", $this->dateTo() );

		$invoices = Sale::query()->where("saleable_type", "App\Models\Invoice");
		$invoices->where('created_at', "<=", $this->dateTo() );


		$orders->whereHas("saleable", function($q) {
			$q->whereNotNull("approved_by")->whereIn("status_id", [10, 14]);
		});

		$invoices->whereHas("saleable", function($q) {
			$q->whereNotNull("approved_by")->whereIn("status_id", [10]);
		});
		
		if( request()->user_id ) {
			$orders->where("created_by", request()->user_id );
			$invoices->where("created_by", request()->user_id );
		}

		if( request()->branch_id ){
			$orders->where("branch_id", request()->branch_id );
			$invoices->where("branch_id", request()->branch_id );
		}

		if( request()->product_id ) {
			$orders->where("product_id", request()->product_id );
			$invoices->where("product_id", request()->product_id );
		}

		$orders = $orders->get()->sum("buying_amount_total");
		$invoices = $invoices->get()->sum("buying_amount_total");
		return  $orders - $invoices;	
	}


	public function getSoldSaleCursor() {

		$invoices = $this->applyDateWithDayFilter(Sale::query())->where("saleable_type", "App\Models\Invoice");
		if( request()->user_id ) $invoices->where("created_by", request()->user_id );
		if( request()->branch_id ) $invoices->where("branch_id", request()->branch_id );
		if( request()->product_id ) $invoices->where("product_id", request()->product_id );

		$invoices->whereHas("saleable", function($q){
			$q->whereNotNull("approved_by")->whereIn("status_id", [10]);
		});

		return $invoices;
	}

	public function getClosingStockQuantity() {
		// Option 2
		$stockItems = StockItem::whereNull("invoice_id");
		$stockItems->where('created_at', "<=", $this->dateTo() );

		if( request()->user_id ) {
			$stockItems->where("created_by", request()->user_id );
		}
		if( request()->branch_id ){
			$stockItems->where("branch_id", request()->branch_id );
		}
		if( request()->product_id ) {
			$stockItems->where("product_id", request()->product_id );
		}

		return $stockItems->count();
	}


	public function getClosingStockQtyValue() {

		$orders = Sale::query()->where("saleable_type", "App\Models\Order");
		$orders->where('created_at', "<=", $this->dateTo() );

		$invoices = Sale::query()->where("saleable_type", "App\Models\Invoice");
		$invoices->where('created_at', "<=", $this->dateTo() );

		$orders->whereHas("saleable", function($q) {
			$q->whereNotNull("approved_by")->whereIn("status_id", [10, 14]);
		});

		$invoices->whereHas("saleable", function($q) {
			$q->whereNotNull("approved_by")->whereIn("status_id", [10]);
		});

		if( request()->user_id ) {
			$orders->where("created_by", request()->user_id );
			$invoices->where("created_by", request()->user_id );
		}

		if( request()->branch_id ){
			$orders->where("branch_id", request()->branch_id );
			$invoices->where("branch_id", request()->branch_id );
		}

		if( request()->product_id ) {
			$orders->where("product_id", request()->product_id );
			$invoices->where("product_id", request()->product_id );
		}

		$orders = $orders->get()->sum("buying_amount_total");
		$invoices = $invoices->get()->sum("buying_amount_total");
		$result = $orders - $invoices;
		return  $result > 0 ? $result : 0 ;	
	}

	public function getTotalStockCursor(){
		// $stocks = $this->applyDateWithDayFilter( StockItem::query() )->pluck("id");
		$stockItems = StockItem::query();
		$stockItems->where('created_at', "<=", $this->dateTo() );
		if( request()->product_id ) $stockItems->where("product_id", request()->product_id );
		if( request()->branch_id )  $stockItems->where("branch_id", request()->branch_id );
		return $stockItems;
	}

	public function getSale() {

		$soldQuantity = $this->getSoldSaleCursor()->select(\DB::raw("unit_quantity * quantity AS quantity"))->get()->sum('quantity');
		$soldValue 	  = $this->getSoldSaleCursor()->get()->sum("selling_amount_total");
		$soldProfit   = $soldValue -  $this->getSoldSaleCursor()->get()->sum("buying_amount_total");

		$closingQuantity = $this->getAvailableStockQuantity();
		// $closingQuantity = $this->getClosingStockQuantity();
		$closingValue 	 = $this->getClosingStockQtyValue();
		$closingProfit   = 0;

		$openingQuantity = $closingQuantity +  $soldQuantity;
		$openingValue 	 = $this->getOpeningStockQtyValue();
		$openingProfit   = $soldProfit + $closingProfit;

		$totalStock = $this->getAvailableStockQuantity();

		$data = [

			"soldQuantity" => $soldQuantity,
			"soldValue"    => $soldValue,
			"soldProfit"   => $soldProfit,

			"closingQuantity" => $closingQuantity,
			"closingValue"    => $closingValue,
			"closingProfit"   => $closingProfit,

			"openingQuantity"  => $openingQuantity,
			"openingValue"  => $openingValue,
			"openingProfit"    => $openingProfit,

			"totalStock" => $totalStock,

		];
		return $data;
	}

	public function getPurchaseOrder() { 
		$orders = $this->applyDateFilter( Order::query() );
		if( request()->user_id ) $orders->where("created_by", request()->user_id );
		$orders->whereIn("status_id", [10]);
		$orders = $orders->get();
		return $orders;
	}


	public function getDebtCursor($type = null) {
		$debts = $this->applyDateFilter( Debt::search( request()->search ) )->latest();
		if($type ) $debts->where("type", $type);
		if( request()->user_id ) $debts->where("created_by", request()->user_id );
		if( request()->branch_id ) $debts->where("branch_id", request()->branch_id);
		if( request()->category ) $debts->where("category", request()->category);

		return $debts;		

	}

	public function getPaymentCursor($type = null) {
		$debts = Payment::search( request()->search );//$this->applyDateFilter( Payment::search( request()->search ) )->latest();
		// if($type ) $debts->where("type", $type);
		// if( request()->user_id ) $debts->where("created_by", request()->user_id );
		// if( request()->branch_id ) $debts->where("branch_id", request()->branch_id);
		// if( request()->category ) $debts->where("category", request()->category);

		return $debts;		

	}


	public function getStockCursor() {
		$stocks = Stock::search( request()->search )->latest();
		$stocks->where('created_at', "<=", $this->dateTo() );

		if( request()->branch_id ) $stocks->where("branch_id", request()->branch_id);
		if( request()->user_id ) $stocks->where("created_by", request()->user_id );

		return $stocks;		

	}


	public function getDebts($type = null) {
		$debts = $this->getDebtCursor($type)->get();
		return $debts;		
	}


	public function getCustomers(){
		return Customer::search( request()->search )->get();
	}


	public function getSuppliers(){
		return Supplier::search( request()->search )->get();
	}

	public function getProductsWithSales() {
		$sales = $this->applyDateFilter(Sale::query())->where("saleable_type", "App\Models\Invoice");

		$sales->whereHas("saleable", function($q){
			$q->whereNotNull("approved_by")->whereIn("status_id", [10]);
		});

		if( request()->user_id ) $sales->where("created_by", request()->user_id);
		$sales = $sales->pluck('product_id');
		$products = Product::whereIn("id", $sales );
		if( request()->product_id ) $products->where("id", request()->product_id );
		return $products->get();
	}




}