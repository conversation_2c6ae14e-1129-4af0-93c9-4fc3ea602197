<?php

return [
    'common' => [
        'actions' => 'Actions',
        'create' => 'Add',
        'edit' => 'Edit',
        'update' => 'Update',
        'new' => 'New',
        'cancel' => 'Cancel',
        'attach' => 'Attach',
        'detach' => 'Detach',
        'save' => 'Save',
        'delete' => 'Delete',
        'delete_selected' => 'Delete selected',
        'search' => 'Search...',
        'back' => 'Back to Index',
        'are_you_sure' => 'Are you sure?',
        'no_items_found' => 'No items found',
        'created' => 'Successfully created',
        'approved' => 'Approved successfully',
        'saved' => 'Saved successfully',
        'removed' => 'Successfully removed',
        'filter' => 'Filter',
        'export' => 'Export',
        'excel' => 'Excel',
        'pdf' => 'PDF',
        'csv' => 'CSV',
            'status' => 'Status',
        'all_statuses' => 'All Statuses',
        'sort_by' => 'Sort By',
            'name' => 'Name',
        'date_added' => 'Date Added',
        'view' => 'View',
        'manage_relationships' => 'Manage your customer relationships',
        'search_placeholder' => 'Name, email, phone...',
        'try_adjusting_search' => 'Try adjusting your search or filter to find what you\'re looking for.',
        ],

    'branches' => [
        'name' => 'Branches',
        'index_title' => 'Branches List',
        'new_title' => 'New Branch',
        'create_title' => 'Add Branch',
        'edit_title' => 'Edit Branch',
        'show_title' => 'Show Branch',
        'inputs' => [
            'name' => 'Name',
            'phone' => 'Phone',
            'email' => 'Email',
            'status_id' => 'Status',
            'address' => 'Address',
        ],
    ],



    'customers' => [
        'name' => 'Customers',
        'singular' => 'Customer',
        'index_title' => 'Customers List',
        'new_title' => 'New Customer',
        'create_title' => 'Add Customer',
        'edit_title' => 'Edit Customer',
        'show_title' => 'Show Customer',
        'search_label' => 'Search Customers',
        'list_title' => 'Customer List',
        'no_customers_found' => 'No customers found',
        'add_new_customer' => 'Add New Customer',
        'create_new_customer' => 'Create New Customer',
        'customer_details' => 'Customer Details',
        'customer_overview' => 'Customer Overview',
        'customer_information' => 'Customer Information',
        'customer_name' => 'Customer Name',
        'edit_customer' => 'Edit Customer',
        'view_customer' => 'View Customer',
        'back_to_customers' => 'Back to Customers',
        'customer_debtors' => 'Customer Debtors',
        'create_customer_debtor' => 'Create Customer Debtor',
        'inputs' => [
            'name' => 'Name',
            'phone' => 'Phone',
            'email' => 'Email',
            'status_id' => 'Status',
            'address' => 'Address',
            'description' => 'Description',
        ],
    ],

    'invoices' => [
        'name' => 'Invoices',
        'index_title' => 'Invoices List',
        'new_title' => 'New Invoice',
        'create_title' => 'Add Invoice',
        'edit_title' => 'Edit Invoice',
        'show_title' => 'Show Invoice',
        'inputs' => [
            'date_from' => 'Date From',
            'date_to' => 'Date To',
            'amount_total' => 'Amount Total',
            'amount_paid' => 'Amount Paid',
            'discount' => 'Discount',
            'vat' => 'Vat',
            'description' => 'Description',
        ],
    ],

    'users' => [
        'name' => 'Users',
        'index_title' => 'Users List',
        'new_title' => 'New User',
        'create_title' => 'Add User',
        'edit_title' => 'Edit User',
        'show_title' => 'Show User',
        'inputs' => [
            'name' => 'Name',
            'phone' => 'Phone',
            'email' => 'Email',
            'password' => 'Password',
            'status_id' => 'Status',
            'name' => 'Name',
            'branch_id' => 'Branch',
        ],
    ],

    'units' => [
        'name' => 'Units',
        'index_title' => 'Units List',
        'new_title' => 'New Unit',
        'create_title' => 'Add Unit',
        'edit_title' => 'Edit Unit',
        'show_title' => 'Show Unit',
        'inputs' => [
            'name' => 'Name',
            'quantity' => 'Quantity',
            'status_id' => 'Status',
        ],
    ],

    'orders' => [
        'name' => 'Orders',
        'index_title' => 'Orders List',
        'new_title' => 'New Order',
        'create_title' => 'Add Order',
        'edit_title' => 'Edit Order',
        'show_title' => 'Show Order',
        'inputs' => [
            'date_from' => 'Date From',
            'date_to' => 'Date To',
            'amount_paid' => 'Amount Paid',
            'amount_total' => 'Amount Total',
            'vat' => 'Vat',
            'discount' => 'Discount',
            'approved_by' => 'Approved By',
            'description' => 'Description',
        ],
    ],
    'quotations' => [
        'name' => 'Quotations',
        'index_title' => 'Quotations List',
        'new_title' => 'New Quotation',
        'create_title' => 'Add Quotation',
        'edit_title' => 'Edit Quotation',
        'show_title' => 'Show Quotation',
        'inputs' => [
            'date_from' => 'Date From',
            'date_to' => 'Date To',
            'amount_paid' => 'Amount Paid',
            'amount_total' => 'Amount Total',
            'vat' => 'Vat',
            'discount' => 'Discount',
            'approved_by' => 'Approved By',
            'description' => 'Description',
        ],
    ],

    'sales' => [
        'name' => 'Sales',
        'index_title' => 'Sales List',
        'new_title' => 'New Sale',
        'create_title' => 'Add Sale',
        'edit_title' => 'Edit Sale',
        'show_title' => 'Show Sale',
        'inputs' => [
            'product_name' => 'Product Name',
            'product_id' => 'Product',
            'unit_name' => 'Unit Name',
            'unit_id' => 'Unit',
            'price' => 'Unit Price',
            'discount' => 'Discount',
            'quantity' => 'Quantity',
            'expires_at' => 'Expires At',
            'description' => 'Description',
            'reference_no' => 'Reference No',
        ],
        'placeholders' => [
            'expires_at' => 'Select expiration date',
            'description' => 'Enter item description',
            'reference_no' => 'Enter reference number',
        ],
        'labels' => [
            'expires_at' => 'Expiration Date',
            'expires_at_optional' => 'Expiration Date (Optional)',
            'no_expiration' => 'No expiration date',
            'expires_on' => 'Expires on',
        ],
        'validation' => [
            'expires_at_future' => 'The expiration date must be in the future.',
            'expires_at_invalid' => 'Please enter a valid expiration date.',
        ],
        'messages' => [
            'expired' => 'This item has expired',
            'expires_soon' => 'This item expires soon',
            'expires_today' => 'This item expires today',
        ],
    ],

    'statuses' => [
        'name' => 'Statuses',
        'index_title' => 'Statuses List',
        'new_title' => 'New Status',
        'create_title' => 'Add Status',
        'edit_title' => 'Edit Status',
        'show_title' => 'Show Status',
        'inputs' => [
            'name' => 'Name',
            'class' => 'Class',
            'color' => 'Color',
        ],
    ],

    'stocks' => [
        'name' => 'Purchase Order',
        'index_title' => 'Purchase List',
        'new_title' => 'New Purchase',
        'create_title' => 'Add Purchase',
        'edit_title' => 'Edit Purchase',
        'show_title' => 'Show Purchase',
        'inputs' => [
            'product_id' => 'Product',
            'quantity' => 'Quantity',
            'balance' => 'Balance',
            'supplier_id' => 'Supplier',
            'stock_id' => 'Stock',
            'approved_by' => 'Approved By',
            'description' => 'Description',
        ],
    ],

    'categories' => [
        'name' => 'Categories',
        'index_title' => 'Categories List',
        'new_title' => 'New Category',
        'create_title' => 'Add Category',
        'edit_title' => 'Edit Category',
        'show_title' => 'Show Category',
        'inputs' => [
            'name' => 'Name',
            'status_id' => 'Status',
            'description' => 'Description',
        ],
    ],

    'products' => [
        'name' => 'Products',
        'index_title' => 'Products List',
        'new_title' => 'New Product',
        'create_title' => 'Add Product',
        'edit_title' => 'Edit Product',
        'show_title' => 'Show Product',
        'inputs' => [
            'name' => 'Name',
            'price' => 'Unit Price',
            'unit_id' => 'Unit',
            'status_id' => 'Status',
            'discount' => 'Discount',
            'category_id' => 'Category',
            'sell_type' => 'Sell Type',
        ],
    ],

    'roles' => [
        'name' => 'Roles',
        'index_title' => 'Roles List',
        'create_title' => 'Add Role',
        'edit_title' => 'Edit Role',
        'show_title' => 'Show Role',
        'inputs' => [
            'name' => 'Name',
        ],
    ],

    'permissions' => [
        'name' => 'Permissions',
        'index_title' => 'Permissions List',
        'create_title' => 'Add Permission',
        'edit_title' => 'Edit Permission',
        'show_title' => 'Show Permission',
        'inputs' => [
            'name' => 'Name',
        ],
    ],

    // Accounting Module Translations
    'account_types' => [
        'name' => 'Account Types',
        'index_title' => 'Account Types List',
        'new_title' => 'New Account Type',
        'create_title' => 'Create Account Type',
        'edit_title' => 'Edit Account Type',
        'show_title' => 'Show Account Type',
        'inputs' => [
            'name' => 'Name',
            'code' => 'Code',
            'classification' => 'Classification',
            'description' => 'Description',
            'is_active' => 'Active',
            'is_system' => 'System Account Type',
        ],
    ],

    'account_categories' => [
        'name' => 'Account Categories',
        'index_title' => 'Account Categories List',
        'new_title' => 'New Account Category',
        'create_title' => 'Create Account Category',
        'edit_title' => 'Edit Account Category',
        'show_title' => 'Show Account Category',
        'inputs' => [
            'name' => 'Name',
            'code' => 'Code',
            'account_type_id' => 'Account Type',
            'description' => 'Description',
            'is_active' => 'Active',
            'is_system' => 'System Account Category',
        ],
    ],

    'accounts' => [
        'name' => 'Chart of Accounts',
        'index_title' => 'Chart of Accounts',
        'new_title' => 'New Account',
        'create_title' => 'Create Account',
        'edit_title' => 'Edit Account',
        'show_title' => 'Show Account',
        'inputs' => [
            'name' => 'Name',
            'code' => 'Code',
            'account_type_id' => 'Account Type',
            'account_category_id' => 'Account Category',
            'description' => 'Description',
            'normal_balance' => 'Normal Balance',
            'is_active' => 'Active',
            'allows_manual_entries' => 'Allows Manual Entries',
            'is_system' => 'System Account',
            'balance' => 'Balance',
        ],
    ],

    'fiscal_years' => [
        'name' => 'Fiscal Years',
        'index_title' => 'Fiscal Years List',
        'new_title' => 'New Fiscal Year',
        'create_title' => 'Create Fiscal Year',
        'edit_title' => 'Edit Fiscal Year',
        'show_title' => 'Show Fiscal Year',
        'inputs' => [
            'name' => 'Name',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'status' => 'Status',
            'is_active' => 'Active',
            'description' => 'Description',
        ],
    ],

    'fiscal_periods' => [
        'name' => 'Fiscal Periods',
        'index_title' => 'Fiscal Periods List',
        'new_title' => 'New Fiscal Period',
        'create_title' => 'Create Fiscal Period',
        'edit_title' => 'Edit Fiscal Period',
        'show_title' => 'Show Fiscal Period',
        'inputs' => [
            'name' => 'Name',
            'fiscal_year_id' => 'Fiscal Year',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'period_number' => 'Period Number',
            'status' => 'Status',
            'is_active' => 'Active',
            'description' => 'Description',
        ],
    ],

    'journal_entries' => [
        'name' => 'Journal Entries',
        'index_title' => 'Journal Entries List',
        'new_title' => 'New Journal Entry',
        'create_title' => 'Create Journal Entry',
        'edit_title' => 'Edit Journal Entry',
        'show_title' => 'Show Journal Entry',
        'inputs' => [
            'entry_number' => 'Entry Number',
            'entry_date' => 'Entry Date',
            'fiscal_year_id' => 'Fiscal Year',
            'fiscal_period_id' => 'Fiscal Period',
            'reference_number' => 'Reference Number',
            'description' => 'Description',
            'entry_type' => 'Entry Type',
            'status' => 'Status',
            'posted_by' => 'Posted By',
            'posted_at' => 'Posted At',
        ],
    ],

    'currencies' => [
        'name' => 'Currencies',
        'index_title' => 'Currencies List',
        'new_title' => 'New Currency',
        'create_title' => 'Create Currency',
        'edit_title' => 'Edit Currency',
        'show_title' => 'Show Currency',
        'inputs' => [
            'name' => 'Name',
            'code' => 'Code',
            'symbol' => 'Symbol',
            'decimal_places' => 'Decimal Places',
            'is_default' => 'Default Currency',
            'is_active' => 'Active',
        ],
    ],

    'exchange_rates' => [
        'name' => 'Exchange Rates',
        'index_title' => 'Exchange Rates List',
        'new_title' => 'New Exchange Rate',
        'create_title' => 'Create Exchange Rate',
        'edit_title' => 'Edit Exchange Rate',
        'show_title' => 'Show Exchange Rate',
        'inputs' => [
            'from_currency_id' => 'From Currency',
            'to_currency_id' => 'To Currency',
            'rate_date' => 'Rate Date',
            'exchange_rate' => 'Exchange Rate',
            'is_active' => 'Active',
        ],
    ],

    'financial_reports' => [
        'name' => 'Financial Reports',
        'index_title' => 'Financial Reports',
        'trial_balance' => 'Trial Balance',
        'balance_sheet' => 'Balance Sheet',
        'income_statement' => 'Income Statement',
    ],

    'asset_categories' => [
        'name' => 'Asset Categories',
        'index_title' => 'Asset Categories List',
        'new_title' => 'New Asset Category',
        'create_title' => 'Create Asset Category',
        'edit_title' => 'Edit Asset Category',
        'show_title' => 'Show Asset Category',
        'inputs' => [
            'name' => 'Name',
            'code' => 'Code',
            'description' => 'Description',
            'asset_account_id' => 'Asset Account',
            'accumulated_depreciation_account_id' => 'Accumulated Depreciation Account',
            'depreciation_expense_account_id' => 'Depreciation Expense Account',
            'depreciation_method' => 'Depreciation Method',
            'useful_life_years' => 'Useful Life (Years)',
            'depreciation_rate' => 'Depreciation Rate',
            'is_active' => 'Active',
        ],
    ],

    'assets' => [
        'name' => 'Assets',
        'index_title' => 'Assets List',
        'new_title' => 'New Asset',
        'create_title' => 'Create Asset',
        'edit_title' => 'Edit Asset',
        'show_title' => 'Show Asset',
        'inputs' => [
            'name' => 'Name',
            'asset_number' => 'Asset Number',
            'asset_category_id' => 'Asset Category',
            'description' => 'Description',
            'acquisition_date' => 'Acquisition Date',
            'acquisition_cost' => 'Acquisition Cost',
            'salvage_value' => 'Salvage Value',
            'useful_life_years' => 'Useful Life (Years)',
            'depreciation_method' => 'Depreciation Method',
            'depreciation_rate' => 'Depreciation Rate',
            'accumulated_depreciation' => 'Accumulated Depreciation',
            'last_depreciation_date' => 'Last Depreciation Date',
            'next_depreciation_date' => 'Next Depreciation Date',
            'status' => 'Status',
            'location' => 'Location',
            'serial_number' => 'Serial Number',
            'warranty_expiry_date' => 'Warranty Expiry Date',
            'notes' => 'Notes',
        ],
    ],

    'asset_depreciations' => [
        'name' => 'Asset Depreciations',
        'index_title' => 'Asset Depreciations List',
        'new_title' => 'New Asset Depreciation',
        'create_title' => 'Create Asset Depreciation',
        'edit_title' => 'Edit Asset Depreciation',
        'show_title' => 'Show Asset Depreciation',
        'inputs' => [
            'asset_id' => 'Asset',
            'depreciation_date' => 'Depreciation Date',
            'depreciation_amount' => 'Depreciation Amount',
            'accumulated_depreciation' => 'Accumulated Depreciation',
            'book_value' => 'Book Value',
            'journal_entry_id' => 'Journal Entry',
            'notes' => 'Notes',
        ],
    ],

    'bank_accounts' => [
        'name' => 'Bank Accounts',
        'index_title' => 'Bank Accounts List',
        'new_title' => 'New Bank Account',
        'create_title' => 'Create Bank Account',
        'edit_title' => 'Edit Bank Account',
        'show_title' => 'Show Bank Account',
        'inputs' => [
            'name' => 'Name',
            'account_number' => 'Account Number',
            'bank_name' => 'Bank Name',
            'branch_name' => 'Branch Name',
            'swift_code' => 'SWIFT Code',
            'iban' => 'IBAN',
            'account_type' => 'Account Type',
            'gl_account_id' => 'GL Account',
            'currency_id' => 'Currency',
            'opening_balance' => 'Opening Balance',
            'opening_balance_date' => 'Opening Balance Date',
            'current_balance' => 'Current Balance',
            'last_reconciliation_date' => 'Last Reconciliation Date',
            'is_active' => 'Active',
            'description' => 'Description',
            'branch_id' => 'Branch',
        ],
    ],

    'bank_reconciliations' => [
        'name' => 'Bank Reconciliations',
        'index_title' => 'Bank Reconciliations List',
        'new_title' => 'New Bank Reconciliation',
        'create_title' => 'Create Bank Reconciliation',
        'edit_title' => 'Edit Bank Reconciliation',
        'show_title' => 'Show Bank Reconciliation',
        'inputs' => [
            'bank_account_id' => 'Bank Account',
            'statement_date' => 'Statement Date',
            'statement_balance' => 'Statement Balance',
            'book_balance' => 'Book Balance',
            'reconciled_balance' => 'Reconciled Balance',
            'difference' => 'Difference',
            'status' => 'Status',
            'completed_by' => 'Completed By',
            'completed_at' => 'Completed At',
            'notes' => 'Notes',
        ],
    ],

    'tax_types' => [
        'name' => 'Tax Types',
        'index_title' => 'Tax Types List',
        'new_title' => 'New Tax Type',
        'create_title' => 'Create Tax Type',
        'edit_title' => 'Edit Tax Type',
        'show_title' => 'Show Tax Type',
        'inputs' => [
            'name' => 'Name',
            'quantity' => 'Quantity',
            'status_id' => 'Status',
            'tax_account_id' => 'Tax Account',
            'quantity' => 'Quantity',
            'status_id' => 'Status',
            'quantity' => 'Quantity',
            'status_id' => 'Status',
        ],
    ],

    'tax_transactions' => [
        'name' => 'Tax Transactions',
        'index_title' => 'Tax Transactions List',
        'show_title' => 'Show Tax Transaction',
        ],
];
