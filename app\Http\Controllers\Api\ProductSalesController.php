<?php

namespace App\Http\Controllers\Api;

use App\Models\Product;
use Illuminate\Http\Request;
use App\Http\Resources\SaleResource;
use App\Http\Controllers\Controller;
use App\Http\Resources\SaleCollection;

class ProductSalesController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product $product
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, Product $product)
    {
        $this->authorize('view', $product);

        $search = $request->get('search', '');

        $sales = $product
            ->sales()
            ->search($search)
            ->latest()
            ->paginate();

        return new SaleCollection($sales);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Product $product
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, Product $product)
    {
        $this->authorize('create', Sale::class);

        $validated = $request->validate([
            'product_name' => ['required', 'max:255', 'string'],
            'unit_name' => ['nullable', 'max:255', 'string'],
            'unit_id' => ['nullable', 'exists:units,id'],
            'price' => ['nullable', 'numeric'],
            'discount' => ['nullable', 'numeric'],
            'quantity' => ['nullable', 'numeric'],
        ]);

        $sale = $product->sales()->create($validated);

        return new SaleResource($sale);
    }
}
