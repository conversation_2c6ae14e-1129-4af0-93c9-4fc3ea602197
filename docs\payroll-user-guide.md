# Payroll System User Guide

## Introduction

The Payroll Accounting Module is designed to streamline the process of managing employee payroll, including salary calculations, deductions, contributions, and generating payslips. This guide will walk you through the key features and processes of the payroll system.

## Getting Started

### Accessing the Payroll Module

1. Log in to the accounting system with your credentials
2. Navigate to the Payroll section from the main menu
3. You will see the Payroll Dashboard with options for various payroll functions

### User Roles and Permissions

The payroll system supports different user roles with varying levels of access:

- **Payroll Administrator**: Full access to all payroll functions
- **Payroll Processor**: Can process payroll but cannot modify configuration
- **Manager**: Can view reports and approve payroll
- **Employee**: Can view their own payslips

## Employee Management

### Adding a New Employee

1. Navigate to the Employees section
2. Click "Add New Employee"
3. Fill in the required information:
   - Personal details (name, address, contact information)
   - Employment details (position, department)
   - Salary information (basic pay, grade)
   - Bank details for payment
4. Click "Save" to create the employee record

### Updating Employee Information

1. Navigate to the Employees section
2. Search for the employee using the search function
3. Click on the employee's name to view their profile
4. Click "Edit" to modify their information
5. Make the necessary changes and click "Save"

## Payroll Setup

### Configuring Deductions and Contributions

1. Navigate to Payroll Setup > Deductions/Contributions
2. Click "Add New" to create a new deduction or contribution
3. Fill in the required information:
   - Name and code
   - Type (Deduction or Contribution)
   - Formula for calculation (e.g., "BASIC_PAY * 0.05" for a 5% deduction)
   - Application mode (Before or After tax)
4. Click "Save" to create the deduction/contribution

### Assigning Deductions/Contributions to Employees

1. Navigate to the employee's profile
2. Click on the "Deductions & Contributions" tab
3. Click "Add" to assign a deduction or contribution
4. Select the deduction/contribution from the dropdown
5. Specify the start date and end date (if applicable)
6. Click "Save" to assign it to the employee

## Payroll Processing

### Generating Monthly Payroll

1. Navigate to Payroll Processing > Generate Payroll
2. Select the month and year for the payroll
3. Click "Generate" to create payroll records for all employees
4. The system will automatically:
   - Create monthly payroll records for each employee
   - Calculate basic pay based on employee records
   - Apply all assigned deductions and contributions
   - Calculate tax (PAYE) based on gross salary
   - Determine the final net pay

### Reviewing and Approving Payroll

1. Navigate to Payroll Processing > Review Payroll
2. Select the month and year to review
3. Review the payroll summary showing:
   - Total employees processed
   - Total gross pay
   - Total deductions
   - Total net pay
4. Click "View Details" to see individual employee records
5. Make any necessary adjustments
6. Click "Approve" to finalize the payroll

## Payslips

### Generating Payslips

1. Navigate to Payroll Processing > Payslips
2. Select the month and year
3. Click "Generate Payslips" to create payslips for all employees
4. The system will create individual payslips showing:
   - Employee details
   - Pay period
   - Earnings breakdown
   - Deductions breakdown
   - Net pay

### Printing Payslips

1. Navigate to Payroll Processing > Payslips
2. Select the month and year
3. Click "View" next to an employee's name to see their payslip
4. Click the "Print" button to print the payslip
5. Alternatively, select multiple employees and click "Batch Print" to print multiple payslips

### Emailing Payslips

1. Navigate to Payroll Processing > Payslips
2. Select the month and year
3. Select the employees to email payslips to
4. Click "Email Payslips"
5. Confirm the action to send payslips to the employees' registered email addresses

## Payroll Journal Entries

### Creating Payroll Journal Entries

1. Navigate to Payroll Processing > Journal Entries
2. Select the month and year for the payroll
3. Click "Generate Journal Entries"
4. The system will create balanced journal entries for:
   - Salary expenses
   - Tax liabilities
   - Other deductions and contributions
5. Review the journal entries before posting

### Posting to General Ledger

1. Review the generated journal entries
2. Make any necessary adjustments
3. Click "Post to General Ledger" to finalize the entries
4. The system will update the general ledger accounts with the payroll transactions

## Reports

### Payroll Journal Report

1. Navigate to Reports > Payroll Journal
2. Select the reporting period (month, quarter, year)
3. Choose the output format (HTML, PDF, CSV)
4. Click "Generate Report"
5. The report will show all payroll journal entries for the selected period

### Payroll Liabilities Report

1. Navigate to Reports > Payroll Liabilities
2. Select the reporting period
3. Choose the output format
4. Click "Generate Report"
5. The report will show all outstanding payroll liabilities, including:
   - Tax liabilities
   - Benefit deductions
   - Other withholdings

### Employee Cost Summary

1. Navigate to Reports > Employee Cost Summary
2. Select the reporting period
3. Choose whether to group by department or show individual employees
4. Choose the output format
5. Click "Generate Report"
6. The report will show the total cost of employment, including:
   - Basic salary
   - Employer contributions
   - Benefits
   - Total cost per employee or department

## Year-End Processing

### Tax Year-End Procedures

1. Navigate to Payroll Processing > Year-End
2. Select the tax year to process
3. Click "Generate Tax Summaries"
4. Review the generated tax information for all employees
5. Generate tax certificates for employees
6. Submit required reports to tax authorities

### Archiving Payroll Data

1. Navigate to Payroll Processing > Year-End
2. Select the year to archive
3. Click "Archive Payroll Data"
4. Confirm the action
5. The system will archive the payroll data while maintaining it for reporting purposes

## Troubleshooting

### Common Issues and Solutions

1. **Incorrect Salary Calculation**
   - Verify the employee's basic pay is correct
   - Check all deductions and contributions are properly configured
   - Ensure the tax calculation formula is up to date

2. **Missing Employees in Payroll**
   - Verify the employee is active in the system
   - Check if the employee was hired after the payroll cut-off date
   - Ensure the employee is assigned to the correct department

3. **Journal Entry Errors**
   - Verify that all accounts are properly configured
   - Ensure debits equal credits in all journal entries
   - Check for any duplicate entries

### Getting Support

For additional support:
1. Refer to the online help system
2. Contact your system administrator
3. Submit a support ticket through the help desk
4. Email support at [<EMAIL>](mailto:<EMAIL>)

## Best Practices

1. **Regular Backups**
   - Ensure the system is backed up regularly
   - Test restoration procedures periodically

2. **Monthly Reconciliation**
   - Reconcile payroll accounts monthly
   - Verify payroll liabilities match withholdings

3. **Compliance Updates**
   - Keep tax tables and rates updated
   - Stay informed about changes in labor laws and regulations

4. **Security**
   - Regularly review user access and permissions
   - Ensure sensitive payroll data is properly secured
   - Use strong passwords and enable two-factor authentication where available
