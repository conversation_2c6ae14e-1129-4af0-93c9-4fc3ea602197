<?php

namespace App\Exports;

use App\Models\Product;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Support\Collection;

use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

use DB;


class ExportProducts implements FromCollection
// , WithHeadings
{
    use Exportable;

    /**
    * @return \Illuminate\Support\Collection
    */

    public function __construct(){
    }

    public function structure(){
    }

    public function collection()
    {


        return Product::get()->map( function($product) {
            return [
                "name" => $product->name,
                "quantity" => $product->total_stock,
            ];
        });
    }    

    public function headings(): array
    {
        return ["PRODUCT_NAME", "PRODUCT_QUANTITY"];
    }
}
