<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

use App\Traits\CreatedUpdatedTrait;

class EmployeeDeductionContribution extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'employee_id',
        'start_date',
        'start_month',
        'end_month',
        'end_date',
        'deduction_contribution_id',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
    ];


    protected $searchableFields = ['*'];


    protected $appends = ['name', 'formula', 'apply_mode'];

    public function payroll()
    {
        return $this->belongsTo(Payroll::class);
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function getNameAttribute() {
        return $this->deductionContribution->name;
    }

    public function getFormulaAttribute() {
        return $this->deductionContribution->formula;
    }

    public function getApplyModeAttribute() {
        return $this->deductionContribution->apply_mode;
    }

    public function deductionContribution()
    {
        return $this->belongsTo(DeductionContribution::class);
    }


}
