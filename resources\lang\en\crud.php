<?php

return [
    // Common UI translations
    'common' => [
        'actions' => 'Actions',
        'create' => 'Create',
        'edit' => 'Edit',
        'update' => 'Update',
        'show' => 'View',
        'new' => 'New',
        'cancel' => 'Cancel',
        'attach' => 'Attach',
        'detach' => 'Detach',
        'save' => 'Save',
        'delete' => 'Delete',
        'delete_selected' => 'Delete selected',
        'search' => 'Search...',
        'back' => 'Back to Index',
        'are_you_sure' => 'Are you sure?',
        'no_items_found' => 'No items found',
        'created' => 'Successfully created',
        'saved' => 'Saved successfully',
        'removed' => 'Successfully removed',
        'reset' => 'Reset', // Added for common UI actions
        'submit' => 'Submit', // Added for common UI actions
        'filter' => 'Filter', // Added for common UI actions
    ],

    // Navigation menu translations
    'nav' => [
        'dashboard' => 'Dashboard',
        'accounting' => 'Accounting',
        'inventory' => 'Inventory',
        'production' => 'Production',
        'reports' => 'Reports',
        'settings' => 'Settings',
    ],

    // Users module
    'users' => [
        'name' => 'Users',
        'index_title' => 'Users List',
        'new_title' => 'New User',
        'create_title' => 'Create User',
        'edit_title' => 'Edit User',
        'show_title' => 'User Details',
        'inputs' => [
            'name' => 'Name',
            'email' => 'Email',
            'password' => 'Password',
            'roles' => 'Roles',
        ],
    ],

    // Roles module
    'roles' => [
        'name' => 'Roles',
        'index_title' => 'Roles List',
        'new_title' => 'New Role',
        'create_title' => 'Create Role',
        'edit_title' => 'Edit Role',
        'show_title' => 'Role Details',
        'inputs' => [
            'name' => 'Name',
            'permissions' => 'Permissions',
        ],
    ],

    // Permissions module
    'permissions' => [
        'name' => 'Permissions',
        'index_title' => 'Permissions List',
        'new_title' => 'New Permission',
        'create_title' => 'Create Permission',
        'edit_title' => 'Edit Permission',
        'show_title' => 'Permission Details',
        'inputs' => [
            'name' => 'Name',
        ],
    ],

    // Branches module
    'branches' => [
        'name' => 'Branches',
        'index_title' => 'Branches List',
        'new_title' => 'New Branch',
        'create_title' => 'Create Branch',
        'edit_title' => 'Edit Branch',
        'show_title' => 'Branch Details',
        'inputs' => [
            'name' => 'Name',
            'code' => 'Code',
            'address' => 'Address',
            'phone' => 'Phone',
            'email' => 'Email',
            'is_active' => 'Active',
        ],
    ],

    // Business Types module
    'business_types' => [
        'name' => 'Business Types',
        'singular' => 'Business Type',
        'index_title' => 'Business Types List',
        'new_title' => 'New Business Type',
        'create_title' => 'Create Business Type',
        'edit_title' => 'Edit Business Type',
        'show_title' => 'Business Type Details',
        'create_new_business_type' => 'Create New Business Type',
        'business_type_details' => 'Business Type Details',
        'business_type_information' => 'Business Type Information',
        'additional_information' => 'Additional Information',
        'back_to_business_types' => 'Back to Business Types',
        'edit_business_type' => 'Edit Business Type',
        'view_business_type' => 'View Business Type',
        'tips' => [
            'title' => 'Tips',
            'descriptive_names' => 'Use descriptive names for business types',
            'appropriate_status' => 'Set appropriate status for activation',
            'detailed_descriptions' => 'Add detailed descriptions for clarity',
            'categorize_operations' => 'Consider how this will categorize your business operations',
        ],
        'inputs' => [
            'name' => 'Name',
            'status_id' => 'Status',
            'description' => 'Description',
        ],
    ],

    // Currencies module
    'currencies' => [
        'name' => 'Currencies',
        'index_title' => 'Currencies List',
        'new_title' => 'New Currency',
        'create_title' => 'Create Currency',
        'edit_title' => 'Edit Currency',
        'show_title' => 'Currency Details',
        'inputs' => [
            'name' => 'Name',
            'code' => 'Code',
            'symbol' => 'Symbol',
            'decimal_places' => 'Decimal Places',
            'exchange_rate' => 'Exchange Rate',
            'is_base_currency' => 'Base Currency',
            'is_default' => 'Default',
            'is_active' => 'Active',
            // Removed created_by and updated_by as they are typically system-managed
        ],
    ],

    // Exchange Rates module
    'exchange_rates' => [
        'name' => 'Exchange Rates',
        'index_title' => 'Exchange Rates List',
        'new_title' => 'New Exchange Rate',
        'create_title' => 'Create Exchange Rate',
        'edit_title' => 'Edit Exchange Rate',
        'show_title' => 'Exchange Rate Details',
        'inputs' => [
            'currency_id' => 'Currency',
            'rate_date' => 'Date',
            'rate' => 'Rate',
            'description' => 'Description',
            'is_active' => 'Active',
            // Removed created_by and updated_by as they are typically system-managed
        ],
    ],

    // Categories module
    'categories' => [
        'name' => 'Categories',
        'index_title' => 'Categories List',
        'new_title' => 'New Category',
        'create_title' => 'Create Category',
        'edit_title' => 'Edit Category',
        'show_title' => 'Category Details',
        'inputs' => [
            'name' => 'Name',
            'description' => 'Description',
            'status_id' => 'Status',
            'applied_to' => 'Applied To',
            // Removed created_by and updated_by as they are typically system-managed
        ],
    ],

    // Warehouses module
    'warehouses' => [
        'name' => 'Warehouses',
        'index_title' => 'Warehouses List',
        'new_title' => 'New Warehouse',
        'create_title' => 'Create Warehouse',
        'edit_title' => 'Edit Warehouse',
        'show_title' => 'Warehouse Details', // Fixed to align with other sections
        'inputs' => [
            'name' => 'Name',
            'code' => 'Code',
            'address' => 'Address',
            'phone' => 'Phone',
            'email' => 'Email',
            'manager_name' => 'Manager Name',
            'description' => 'Description',
            'is_active' => 'Active',
            'branch_id' => 'Branch',
            'image' => 'Image',
        ],
    ],

    // Fiscal Years module
    'fiscal_years' => [
        'name' => 'Fiscal Years',
        'index_title' => 'Fiscal Years List',
        'new_title' => 'New Fiscal Year',
        'create_title' => 'Create Fiscal Year',
        'edit_title' => 'Edit Fiscal Year',
        'show_title' => 'Fiscal Year Details',
        'inputs' => [
            'name' => 'Name',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'is_active' => 'Active',
            'is_closed' => 'Closed',
            'closed_date' => 'Closed Date',
            'closed_by' => 'Closed By',
            'description' => 'Description',
            // Removed created_by and updated_by as they are typically system-managed
        ],
    ],

    // Chart of Accounts module
    'chart_of_accounts' => [
        'name' => 'Chart of Accounts',
        'index_title' => 'Chart of Accounts',
        'new_title' => 'New Account', // Added for consistency
        'create_title' => 'Create Account', // Added for consistency
        'edit_title' => 'Edit Account', // Added for consistency
        'show_title' => 'Account Details', // Added for consistency
        'description' => 'Manage your financial accounts structure',
        'inputs' => [ // Added for consistency
            'name' => 'Name',
            'code' => 'Code',
            'description' => 'Description',
            'account_type_id' => 'Account Type',
            'account_category_id' => 'Account Category',
            'is_active' => 'Active',
            'is_system' => 'System',
            // Removed created_by and updated_by as they are typically system-managed
        ],
    ],

    // Account Types module
    'account_types' => [
        'name' => 'Account Types',
        'index_title' => 'Account Types List',
        'new_title' => 'New Account Type',
        'create_title' => 'Create Account Type',
        'edit_title' => 'Edit Account Type',
        'show_title' => 'Account Type Details',
        'inputs' => [
            'name' => 'Name',
            'code' => 'Code',
            'description' => 'Description',
            'classification' => 'Classification',
            'is_active' => 'Active',
            'is_system' => 'System',
            // Removed created_by and updated_by as they are typically system-managed
        ],
    ],

    // Account Categories module
    'account_categories' => [
        'name' => 'Account Categories',
        'index_title' => 'Account Categories List',
        'new_title' => 'New Account Category',
        'create_title' => 'Create Account Category',
        'edit_title' => 'Edit Account Category',
        'show_title' => 'Account Category Details',
        'inputs' => [
            'name' => 'Name',
            'code' => 'Code',
            'description' => 'Description',
            'account_type_id' => 'Account Type',
            'is_active' => 'Active',
            'is_system' => 'System',
            // Removed created_by and updated_by as they are typically system-managed
        ],
    ],

    // Accounts module
    'accounts' => [
        'name' => 'Accounts',
        'index_title' => 'Accounts List',
        'new_title' => 'New Account',
        'create_title' => 'Create Account',
        'edit_title' => 'Edit Account',
        'show_title' => 'Account Details',
        'inputs' => [
            'name' => 'Name',
            'code' => 'Code',
            'description' => 'Description',
            'account_type_id' => 'Account Type',
            'account_category_id' => 'Account Category',
            'is_active' => 'Active',
            'is_system' => 'System',
            // Removed created_by and updated_by as they are typically system-managed
        ],
    ],

    // Bills of Materials module
    'bill_of_materials' => [
        'name' => 'Bills of Materials',
        'index_title' => 'Bills of Materials List',
        'new_title' => 'New Bill of Materials',
        'create_title' => 'Create Bill of Materials',
        'edit_title' => 'Edit Bill of Materials',
        'show_title' => 'Bill of Materials Details',
        'inputs' => [
            'name' => 'Name',
            'code' => 'Code',
            'product_id' => 'Product',
            'description' => 'Description',
            'is_active' => 'Active',
        ],
    ],

    // Production Orders module
    'production_orders' => [
        'name' => 'Production Orders',
        'index_title' => 'Production Orders List',
        'new_title' => 'New Production Order',
        'create_title' => 'Create Production Order',
        'edit_title' => 'Edit Production Order',
        'show_title' => 'Production Order Details',
        'inputs' => [
            'order_number' => 'Order Number',
            'product_id' => 'Product',
            'bom_id' => 'Bill of Materials',
            'quantity' => 'Quantity',
            'unit_of_measure' => 'Unit of Measure',
            'planned_start_date' => 'Planned Start Date',
            'planned_end_date' => 'Planned End Date',
            'actual_start_date' => 'Actual Start Date',
            'actual_end_date' => 'Actual End Date',
            'planned_cost' => 'Planned Cost',
            'actual_cost' => 'Actual Cost',
            'cost_variance' => 'Cost Variance',
            'status' => 'Status',
            'branch_id' => 'Branch',
            'description' => 'Description',
        ],
    ],

    // Material Issues module
    'material_issues' => [
        'name' => 'Material Issues',
        'index_title' => 'Material Issues List',
        'new_title' => 'New Material Issue',
        'create_title' => 'Create Material Issue',
        'edit_title' => 'Edit Material Issue',
        'show_title' => 'Material Issue Details',
        'inputs' => [
            'issue_number' => 'Issue Number',
            'production_order_id' => 'Production Order',
            'issue_date' => 'Issue Date',
            'warehouse_id' => 'Warehouse',
            'status' => 'Status',
            'notes' => 'Notes',
        ],
    ],

    // WIP Tracking module
    'wip_tracking' => [
        'name' => 'WIP Tracking',
        'index_title' => 'WIP Tracking List',
        'new_title' => 'New WIP Tracking',
        'create_title' => 'Create WIP Tracking',
        'edit_title' => 'Edit WIP Tracking',
        'show_title' => 'WIP Tracking Details',
        'inputs' => [
            'tracking_number' => 'Tracking Number',
            'production_order_id' => 'Production Order',
            'product_id' => 'Product',
            'work_center' => 'Work Center',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'status' => 'Status',
            'notes' => 'Notes',
        ],
    ],
];