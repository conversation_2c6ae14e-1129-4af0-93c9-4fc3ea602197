@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                Employee Deductions & Contributions
            </h4>
        </div>

        <div class="card-body">
            <div class="mb-4">
                <form>
                    <div class="row">
                        <div class="col-md-5">
                            <div class="form-group">
                                <label for="employee_id">Employee</label>
                                <select name="employee_id" id="employee_id" class="form-control">
                                    <option value="">All Employees</option>
                                    @foreach($employees as $id => $name)
                                        <option value="{{ $id }}" {{ $employeeId == $id ? 'selected' : '' }}>{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <div class="form-group">
                                <label for="search">Search</label>
                                <input type="text" name="search" id="search" value="{{ $search ?? '' }}" class="form-control" placeholder="Search...">
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">Filter</button>
                        </div>
                    </div>
                </form>
            </div>

            <div class="table-responsive">
                <table class="table table-hover table-striped">
                    <thead>
                        <tr>
                            <th>Employee</th>
                            <th>Deduction/Contribution</th>
                            <th>Type</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($employeeDeductionContributions as $edc)
                        <tr>
                            <td>{{ $edc->employee->name ?? '-' }}</td>
                            <td>{{ $edc->deductionContribution->name ?? '-' }}</td>
                            <td>{{ $edc->deductionContribution->apply_mode ?? '-' }}</td>
                            <td>{{ $edc->start_date ? $edc->start_date->format('d M Y') : '-' }}</td>
                            <td>{{ $edc->end_date ? $edc->end_date->format('d M Y') : '-' }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ route('employee-deduction-contributions.show', $edc) }}" class="btn btn-sm btn-info">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ route('employee-deduction-contributions.edit', $edc) }}" class="btn btn-sm btn-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <form action="{{ route('employee-deduction-contributions.destroy', $edc) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this?');" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="text-center">No employee deduction/contribution assignments found.</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                {{ $employeeDeductionContributions->links() }}
            </div>

            <div class="mt-4">
                <a href="{{ route('employee-deduction-contributions.create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> Create New Assignment
                </a>
                @if($employeeId)
                <a href="{{ route('employees.deduction-contributions', $employeeId) }}" class="btn btn-success">
                    <i class="bi bi-people"></i> Bulk Assign for Selected Employee
                </a>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
