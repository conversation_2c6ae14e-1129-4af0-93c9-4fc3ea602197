package com.example


/**
* Homeflow Technologies | Order.
*
* @property id
* @property date_from
* @property date_to
* @property vat
* @property description
* @property amount_total
* @property amount_paid
* @property discount
* @property approved_by
* @property status_id
* @property created_by
* @property updated_by
*
* @constructor Create Order model
*/
data class Order( var date_from: String? = null, var date_to: String? = null, var vat: Double? = null, var description: String? = null, var amount_total: Double? = null, var amount_paid: Double? = null, var discount: Double? = null, var approved_by: Int? = null, var status_id: Int? = null, var created_by: Int? = null, var updated_by: Int? = null,
)
