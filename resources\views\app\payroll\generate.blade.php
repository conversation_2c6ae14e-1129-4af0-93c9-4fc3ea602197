@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                Generate Payroll
            </h4>
        </div>

        <div class="card-body">
            <form action="{{ route('payroll.process-generate') }}" method="POST">
                @csrf

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="month">Month</label>
                            <select name="month" id="month" class="form-control" required>
                                @foreach($months as $key => $value)
                                    <option value="{{ $key }}" {{ date('m') == $key ? 'selected' : '' }}>{{ $value }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="year">Year</label>
                            <select name="year" id="year" class="form-control" required>
                                @foreach($years as $key => $value)
                                    <option value="{{ $key }}" {{ date('Y') == $key ? 'selected' : '' }}>{{ $value }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <p><strong>Note:</strong> This will generate payroll records for all employees for the selected month and year. If a payroll record already exists for an employee in the selected period, it will not be overwritten.</p>
                    <p>Make sure all employees have their basic pay and deductions/contributions properly set up before generating payroll.</p>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ route('payroll.index') }}" class="btn btn-light">
                        <i class="bi bi-arrow-left"></i> Back
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Generate Payroll
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
