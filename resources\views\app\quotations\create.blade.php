@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-sm mb-2 mb-sm-0">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-no-gutter">
                        <li class="breadcrumb-item">
                            <a class="breadcrumb-link" href="{{ route('quotations.index') }}">Quotations</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">New Quotation</li>
                    </ol>
                </nav>
                <h1 class="page-header-title">
                    <i class="bi-file-earmark-plus me-2"></i>
                    Create New Quotation
                </h1>
            </div>
            <div class="col-sm-auto">
                <a href="{{ route('quotations.index') }}" class="btn btn-outline-secondary">
                    <i class="bi-arrow-left me-1"></i>
                    Back to Quotations
                </a>
            </div>
        </div>
    </div>
    <!-- End Page Header -->

    <x-form
        method="POST"
        action="{{ route('quotations.store') }}"
        class="mt-4"
    >
        @include('app.quotations.form-inputs')
    </x-form>

</div>
@endsection
