<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Status;
use App\Models\BusinessType;

use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class StatusBusinessTypesTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create(['email' => '<EMAIL>']);

        Sanctum::actingAs($user, [], 'web');

        $this->seed(\Database\Seeders\PermissionsSeeder::class);

        $this->withoutExceptionHandling();
    }

    /**
     * @test
     */
    public function it_gets_status_business_types()
    {
        $status = Status::factory()->create();
        $businessTypes = BusinessType::factory()
            ->count(2)
            ->create([
                'status_id' => $status->id,
            ]);

        $response = $this->getJson(
            route('api.statuses.business-types.index', $status)
        );

        $response->assertOk()->assertSee($businessTypes[0]->name);
    }

    /**
     * @test
     */
    public function it_stores_the_status_business_types()
    {
        $status = Status::factory()->create();
        $data = BusinessType::factory()
            ->make([
                'status_id' => $status->id,
            ])
            ->toArray();

        $response = $this->postJson(
            route('api.statuses.business-types.store', $status),
            $data
        );

        unset($data['created_by']);
        unset($data['updated_by']);

        $this->assertDatabaseHas('business_types', $data);

        $response->assertStatus(201)->assertJsonFragment($data);

        $businessType = BusinessType::latest('id')->first();

        $this->assertEquals($status->id, $businessType->status_id);
    }
}
