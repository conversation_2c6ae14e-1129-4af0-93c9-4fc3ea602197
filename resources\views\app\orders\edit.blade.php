@extends('layouts.fullscreen')

@section('content')
<!-- Fullscreen Header -->
<div class="fullscreen-header">
    <div class="d-flex justify-content-between align-items-center w-100">
        <div>
            <h1 class="h4 mb-0">
                <i class="bi bi-pencil-square text-warning me-2"></i>Edit Order #{{ $order->id }}
            </h1>
            <small class="text-muted">
                <a href="{{ route('orders.index') }}" class="text-decoration-none">Orders</a> /
                <a href="{{ route('orders.show', $order) }}" class="text-decoration-none">Order #{{ $order->id }}</a> / Edit
            </small>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('orders.show', $order) }}" class="btn btn-outline-info">
                <i class="bi bi-eye me-1"></i>View Order
            </a>
            <a href="{{ route('orders.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-list me-1"></i>All Orders
            </a>
        </div>
    </div>
</div>

<!-- Display Validation Errors -->
@if ($errors->any())
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <h6 class="alert-heading">
            <i class="bi bi-exclamation-triangle me-1"></i>
            Please fix the following errors:
        </h6>
        <ul class="mb-0">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

<!-- Fullscreen Content -->
<div class="fullscreen-content">
    <x-form method="PUT" action="{{ route('orders.update', $order) }}">
        @include('app.orders.form-inputs')
    </x-form>
</div>
@endsection
