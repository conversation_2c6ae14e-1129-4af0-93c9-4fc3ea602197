<?php

namespace App\Http\Controllers;

use App\Models\Currency;
use Illuminate\Http\Request;

class CurrencyController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Currency::class);

        $search = $request->get('search', '');

        $currencies = Currency::search($search)
            ->latest()
            ->paginate()
            ->withQueryString();

        return view('app.currencies.index', compact('currencies', 'search'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', Currency::class);

        return view('app.currencies.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', Currency::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:3|tenant_unique:currencies,code',
            'symbol' => 'required|string|max:10',
            'decimal_places' => 'required|integer|min:0|max:6',
            'is_base_currency' => 'boolean',
            'is_default' => 'boolean',
            'exchange_rate' => 'required|numeric|min:0',
            'is_active' => 'boolean',
        ]);

        // If this is set as base currency, update all other currencies
        if ($validated['is_base_currency'] ?? false) {
            Currency::where('is_base_currency', true)
                ->update(['is_base_currency' => false]);
        }

        // If this is set as default currency, update all other currencies
        if ($validated['is_default'] ?? false) {
            Currency::where('is_default', true)
                ->update(['is_default' => false]);
        }

        // Set default values for boolean fields if not provided
        $validated['is_base_currency'] = $validated['is_base_currency'] ?? false;
        $validated['is_default'] = $validated['is_default'] ?? false;
        $validated['is_active'] = $validated['is_active'] ?? true;

        // Set business_type_id if not automatically set by the trait
        if (auth()->check() && !isset($validated['business_type_id'])) {
            $validated['business_type_id'] = auth()->user()->business_type_id;
        }

        $currency = Currency::create($validated);

        return redirect()
            ->route('currencies.edit', $currency)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Currency  $currency
     * @return \Illuminate\Http\Response
     */
    public function show(Currency $currency)
    {
        $this->authorize('view', $currency);

        $exchangeRates = $currency->exchangeRates()
            ->latest('rate_date')
            ->paginate(10);

        return view('app.currencies.show', compact('currency', 'exchangeRates'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Currency  $currency
     * @return \Illuminate\Http\Response
     */
    public function edit(Currency $currency)
    {
        $this->authorize('update', $currency);

        return view('app.currencies.edit', compact('currency'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Currency  $currency
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Currency $currency)
    {
        $this->authorize('update', $currency);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:3|tenant_unique:currencies,code,' . $currency->id,
            'symbol' => 'required|string|max:10',
            'decimal_places' => 'required|integer|min:0|max:6',
            'is_base_currency' => 'boolean',
            'is_default' => 'boolean',
            'exchange_rate' => 'required|numeric|min:0',
            'is_active' => 'boolean',
        ]);

        // If this is set as base currency, update all other currencies
        if (($validated['is_base_currency'] ?? false) && !$currency->is_base_currency) {
            Currency::where('is_base_currency', true)
                ->update(['is_base_currency' => false]);
        }

        // If this is set as default currency, update all other currencies
        if (($validated['is_default'] ?? false) && !$currency->is_default) {
            Currency::where('is_default', true)
                ->update(['is_default' => false]);
        }

        // Explicitly set updated_by to the current authenticated user
        $validated['updated_by'] = auth()->id();

        // Set default values for boolean fields if not provided
        $validated['is_base_currency'] = $validated['is_base_currency'] ?? $currency->is_base_currency;
        $validated['is_default'] = $validated['is_default'] ?? $currency->is_default;
        $validated['is_active'] = $validated['is_active'] ?? $currency->is_active;

        $currency->update($validated);

        return redirect()
            ->route('currencies.edit', $currency)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Currency  $currency
     * @return \Illuminate\Http\Response
     */
    public function destroy(Currency $currency)
    {
        $this->authorize('delete', $currency);

        if ($currency->is_base_currency) {
            return redirect()
                ->route('currencies.index')
                ->withError('Base currency cannot be deleted.');
        }

        if ($currency->journalEntries()->count() > 0 || $currency->bankAccounts()->count() > 0) {
            return redirect()
                ->route('currencies.index')
                ->withError('Currency is in use and cannot be deleted.');
        }

        $currency->delete();

        return redirect()
            ->route('currencies.index')
            ->withSuccess(__('crud.common.removed'));
    }
}
