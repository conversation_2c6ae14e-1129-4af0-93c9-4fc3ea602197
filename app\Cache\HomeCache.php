<?php

namespace App\Cache;

use App\Models\Status;
use App\Models\Attendance;
use App\Models\Staff;
use App\Models\Gadget;
use App\Models\ActiveConfig;
use App\Models\Category;
use Carbon\Carbon;

/**
 * 
 */
class HomeCache
{

    public function dateFrom(){
        return  request()->from ?? Carbon::now()->format('Y-m-d');
    }

    public function dateTo(){
        return request()->to ?? Carbon::now()->addDays(1)->format('Y-m-d');
    }

	public function category(){
		$categories = Category::get();
		// foreach ( $categories as $category) {
		// 	$total = Attendance::whereHas("staff", function($q) use ($category) {
		// 		$q->where('category_id', $category->id);
		// 	})->count();
		// 	$category->total = $total;
		// }
		return [];
	}

	public function annualSummary($instance){
		if( ! $instance ) return [];
		$year = (request()->year) ? request()->year : date("Y");
		$totalArr = [
			"label" => "All Hours",
			"name" => "All Hours",
		];
		$workingArr = [
			"label" => "Working Hours",
			"name" => "Working Hours"
		];
        for ($month = 1; $month <= 12; $month++) {
			$cursor = $instance->attendances()->whereNotNull('time_out'); 
	        $cursor->whereMonth('created_at', '=', $month);
	        $cursor->whereYear('created_at', '=', $year);
	        $total   = $cursor->get()->map(function($row){ return $row->time_different; })->toArray();
	        $working = $cursor->get()->map(function($row){ return $row->working_hours; })->toArray();
	        $totalArr['data'][]   = array_sum($total);
	        $workingArr['data'][] = array_sum($working);
        }
        return [
        	$totalArr,
        	$workingArr
        ];
	}

	public function weeklySummary($instance){

		Carbon::setWeekStartsAt(Carbon::SUNDAY);
		Carbon::setWeekEndsAt(Carbon::SATURDAY);
		$now = Carbon::now();
		$year = (request()->year) ? request()->year : date("Y");
		$totalArr = [
			"label" => "All Hours",
			"name" => "All Hours",
		];
		$workingArr = [
			"label" => "Working Hours",
			"name" => "Working Hours",
		];
        for ($day = 0; $day <= 7; $day++) {
			$cursor = $instance->attendances()->whereNotNull('time_out'); 
			$date = $now->startOfWeek()->addDays($day);
	        $cursor->whereDate('created_at', '=', $date);
	        $total   = $cursor->get()->map(function($row){ return $row->time_different; })->toArray();
	        $working = $cursor->get()->map(function($row){ return $row->working_hours; })->toArray();
	        $totalArr['data'][]   = array_sum($total);
	        $workingArr['data'][] = array_sum($working);
        }
        return [
        	$totalArr,
        	$workingArr
        ];
	}

	public function monthlySummary(){
		$data = Attendance::query()
				->whereNotNull('time_out')
				->selectRaw('attendances.*, TIMESTAMPDIFF(hour, time_in, time_out) as hours'); 
		if( request()->from && request()->to ) {
        	$data->whereBetween('created_at', [ $this->dateFrom(), $this->dateTo() ]);
        } else {
        	$data->whereMonth('created_at', '=', date('m'));
        }

        if(request()->attendancable_type) $data->where("attendancable_type", request()->attendancable_type);
        if(request()->attendancable_id) $data->where("attendancable_id", request()->attendancable_id);

		$data = $data->get()->groupBy(function($val) {
      		return Carbon::parse($val->created_at)->format('Y-m-d');
		})->map( function($row){
			return $row->sum("hours");
		});
		return $data;
	}

	public function getItems(){

		$items   = Staff::with("category", "attendance")->search(request()->search);
		$cat_id = (request()->category_id) ? request()->category_id : ( Category::first()->id ?? null) ;
		if($cat_id) $items->where("category_id", $cat_id );

		$items = $items->paginate();
		if(request()->withAnnual){
			foreach($items as $item){
				$item->annual = $item->getAnnual();
			}			
		}
		return $items;
	}
	
	public function	summary($request){

		$entered = Attendance::where('status_id', 1)->count();
		$exited  = Attendance::where('status_id', 0)->count();
		$categories  = GadgetOwnerCategory::get();		
		$statuses  = Status::get();		

		return [
			'entered' => $entered,
			'exited' => $exited,
        	'items' => $this->getItems(),
        	'categories' => $categories,
        	'statuses' => $statuses,
        	'monthlySummary' => $this->monthlySummary(),
		];

	}

	public function getTotalStaff(){
		return GadgetOwner::count();
	}

	public function getTotalGadgets(){
		return Gadget::count();
	}

    public function getAttendances(){
    	if(request()->status == "IN") $status  = 1;
    	if(request()->status == "OUT") $status = 0;
    	$perPage = (request()->per_page) ? request()->per_page : 10;
         
        $attendances = Attendance::query();
        if(request()->attendancable_type) $attendances->where("attendancable_type", request()->attendancable_type);
        if(request()->status >= 0) $attendances->where("status_id", $status ?? '');
        if( request()->from && request()->to ) {
        	$attendances->whereBetween('created_at', [ $this->dateFrom(), $this->dateTo() ]);
        }
        $attendances = $attendances->orderBy("id", "DESC")->paginate($perPage)->withQueryString();
        return $attendances;
    }

    public function getConf($key) {
    	return ActiveConfig::where("key", $key)->value("value");
    }




}