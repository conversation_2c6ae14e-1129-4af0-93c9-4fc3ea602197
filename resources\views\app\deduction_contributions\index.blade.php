@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                {{ $type == 'deduction' ? 'Deductions' : 'Contributions' }}
            </h4>
        </div>

        <div class="card-body">
            <div class="mb-4">
                <form>
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="search">Search</label>
                                <input type="text" name="search" id="search" value="{{ $search ?? '' }}" class="form-control" placeholder="Search...">
                                <input type="hidden" name="type" value="{{ $type }}">
                            </div>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">Search</button>
                        </div>
                    </div>
                </form>
            </div>

            <div class="table-responsive">
                <table class="table table-hover table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Code</th>
                            <th>Formula</th>
                            <th>Apply At</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($deductionContributions as $deductionContribution)
                        <tr>
                            <td>{{ $deductionContribution->name }}</td>
                            <td>{{ $deductionContribution->code ?? '-' }}</td>
                            <td>{{ $deductionContribution->formula }}</td>
                            <td>{{ $deductionContribution->apply_at }}</td>
                            <td>{{ $deductionContribution->status->name ?? '-' }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ route('deduction-contributions.show', $deductionContribution) }}" class="btn btn-sm btn-info">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ route('deduction-contributions.edit', $deductionContribution) }}" class="btn btn-sm btn-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <a href="{{ route('deduction-contributions.assign-form', $deductionContribution) }}" class="btn btn-sm btn-success">
                                        <i class="bi bi-people"></i>
                                    </a>
                                    <form action="{{ route('deduction-contributions.destroy', $deductionContribution) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this?');" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="text-center">No {{ $type == 'deduction' ? 'deductions' : 'contributions' }} found.</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                {{ $deductionContributions->links() }}
            </div>

            <div class="mt-4">
                <a href="{{ route('deduction-contributions.create', ['type' => $type]) }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> Create New {{ $type == 'deduction' ? 'Deduction' : 'Contribution' }}
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
