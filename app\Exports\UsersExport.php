<?php

namespace App\Exports;

use App\Models\User;
use App\Models\Role;
use App\Models\SubSchools;
use App\Models\YearGroup;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Support\Collection;

use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

use DB;

class UsersExport implements FromCollection, WithHeadings
{
    use Exportable;
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {

        $search = request()->search;

        $users = User::search($search);

        $users = $users->with('roles')->get()->map(function($user){
            return [
                'name' => $user->name,
                'email' => $user->email,
                'roles' => (is_array($user->my_roles))? implode(',', $user->my_roles) : '-',
                'created_at' => $user->created_at,
            ];
        });

        return $users;
    }    

    public function headings(): array
    {
        return ['name', 'email', 'Roles', 'Created Date'];
    }
}
