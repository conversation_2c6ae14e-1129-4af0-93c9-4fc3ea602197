@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <x-page-header title="Loan Details - {{ $employeeLoan->loan_reference }}" />

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Loan Information</h5>
                    <div>
                        @can('update', $employeeLoan)
                            @if($employeeLoan->status == 'pending')
                                <a href="{{ route('employee-loans.edit', $employeeLoan) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                            @endif
                        @endcan
                        
                        @can('approve', $employeeLoan)
                            @if($employeeLoan->status == 'pending')
                                <button type="button" class="btn btn-sm btn-success" onclick="approveLoan()">
                                    <i class="fas fa-check"></i> Approve
                                </button>
                            @endif
                        @endcan
                        
                        @can('cancel', $employeeLoan)
                            @if(in_array($employeeLoan->status, ['pending', 'approved']))
                                <button type="button" class="btn btn-sm btn-danger" onclick="cancelLoan()">
                                    <i class="fas fa-times"></i> Cancel
                                </button>
                            @endif
                        @endcan
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Loan Reference:</strong></td>
                                    <td>{{ $employeeLoan->loan_reference }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Employee:</strong></td>
                                    <td>{{ $employeeLoan->employee->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Loan Amount:</strong></td>
                                    <td>{{ number_format($employeeLoan->loan_amount, 2) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Monthly Installment:</strong></td>
                                    <td>{{ number_format($employeeLoan->installment_amount, 2) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Remaining Balance:</strong></td>
                                    <td>{{ number_format($employeeLoan->remaining_balance, 2) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Paid:</strong></td>
                                    <td>{{ number_format($employeeLoan->total_paid, 2) }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ $employeeLoan->status == 'active' ? 'info' : ($employeeLoan->status == 'completed' ? 'success' : ($employeeLoan->status == 'pending' ? 'warning' : ($employeeLoan->status == 'approved' ? 'primary' : 'danger'))) }}">
                                            {{ ucfirst($employeeLoan->status) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Loan Date:</strong></td>
                                    <td>{{ $employeeLoan->loan_date->format('M d, Y') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>First Deduction:</strong></td>
                                    <td>{{ $employeeLoan->first_deduction_date->format('M d, Y') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Installments:</strong></td>
                                    <td>{{ $employeeLoan->total_installments }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Installments Paid:</strong></td>
                                    <td>{{ $employeeLoan->installments_paid }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Progress:</strong></td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {{ $employeeLoan->progress_percentage }}%"
                                                 aria-valuenow="{{ $employeeLoan->progress_percentage }}" 
                                                 aria-valuemin="0" aria-valuemax="100">
                                                {{ $employeeLoan->progress_percentage }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($employeeLoan->interest_rate > 0)
                        <div class="row">
                            <div class="col-md-12">
                                <h6>Interest Information</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Interest Rate:</strong></td>
                                        <td>{{ $employeeLoan->interest_rate }}%</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Interest Type:</strong></td>
                                        <td>{{ ucfirst($employeeLoan->interest_type) }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-12">
                            <h6>Loan Purpose</h6>
                            <p>{{ $employeeLoan->purpose }}</p>
                        </div>
                    </div>

                    @if($employeeLoan->terms_conditions)
                        <div class="row">
                            <div class="col-md-12">
                                <h6>Terms and Conditions</h6>
                                <p>{{ $employeeLoan->terms_conditions }}</p>
                            </div>
                        </div>
                    @endif

                    @if($employeeLoan->approval_notes)
                        <div class="row">
                            <div class="col-md-12">
                                <h6>Approval Notes</h6>
                                <p>{{ $employeeLoan->approval_notes }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Loan Payment History -->
            @if($employeeLoan->status == 'active' || $employeeLoan->status == 'completed')
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Payment History</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Payroll Period</th>
                                        <th>Amount Deducted</th>
                                        <th>Remaining Balance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @php
                                        $payrollItems = \App\Models\PayrollItem::where('loan_id', $employeeLoan->id)
                                            ->with('monthlyPayroll')
                                            ->orderBy('created_at', 'desc')
                                            ->get();
                                    @endphp
                                    @forelse($payrollItems as $item)
                                        <tr>
                                            <td>{{ $item->created_at->format('M d, Y') }}</td>
                                            <td>{{ $item->monthlyPayroll->date_from->format('M Y') }}</td>
                                            <td>{{ number_format($item->amount, 2) }}</td>
                                            <td>-</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="4" class="text-center">No payments recorded yet.</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Loan Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6>Loan Created</h6>
                                <p class="text-muted">{{ $employeeLoan->created_at->format('M d, Y H:i') }}</p>
                                <small>Created by {{ $employeeLoan->createdBy->name }}</small>
                            </div>
                        </div>

                        @if($employeeLoan->approved_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6>Loan Approved</h6>
                                    <p class="text-muted">{{ $employeeLoan->approved_at->format('M d, Y H:i') }}</p>
                                    <small>Approved by {{ $employeeLoan->approvedBy->name }}</small>
                                </div>
                            </div>
                        @endif

                        @if($employeeLoan->status == 'active')
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6>Loan Activated</h6>
                                    <p class="text-muted">Deductions started</p>
                                </div>
                            </div>
                        @endif

                        @if($employeeLoan->actual_completion_date)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6>Loan Completed</h6>
                                    <p class="text-muted">{{ $employeeLoan->actual_completion_date->format('M d, Y') }}</p>
                                </div>
                            </div>
                        @endif

                        @if($employeeLoan->status == 'cancelled')
                            <div class="timeline-item">
                                <div class="timeline-marker bg-danger"></div>
                                <div class="timeline-content">
                                    <h6>Loan Cancelled</h6>
                                    <p class="text-muted">{{ $employeeLoan->updated_at->format('M d, Y H:i') }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            @if($employeeLoan->expected_completion_date && $employeeLoan->status == 'active')
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">Expected Completion</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-center">
                            <strong>{{ $employeeLoan->expected_completion_date->format('M d, Y') }}</strong>
                        </p>
                        @if($employeeLoan->next_payment_date)
                            <p class="text-center text-muted">
                                Next payment: {{ $employeeLoan->next_payment_date->format('M d, Y') }}
                            </p>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Approve Loan</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ route('employee-loans.approve', $employeeLoan) }}">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="approval_notes">Approval Notes (Optional)</label>
                        <textarea name="approval_notes" id="approval_notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Approve Loan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Cancellation Modal -->
<div class="modal fade" id="cancellationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Loan</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ route('employee-loans.cancel', $employeeLoan) }}">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="cancellation_reason">Cancellation Reason *</label>
                        <textarea name="cancellation_reason" id="cancellation_reason" class="form-control" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-danger">Cancel Loan</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}
</style>
@endpush

@push('scripts')
<script>
function approveLoan() {
    $('#approvalModal').modal('show');
}

function cancelLoan() {
    $('#cancellationModal').modal('show');
}
</script>
@endpush
@endsection
