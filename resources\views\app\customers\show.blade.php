@extends('layouts.app')

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">person</x-slot>
        <x-slot name="title">{{ $customer->name }}</x-slot>
        <x-slot name="subtitle">@lang('crud.customers.customer_details')</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('customers.index') }}">@lang('crud.customers.name')</a></li>
            <li class="breadcrumb-item active" aria-current="page">View</li>
        </x-slot>
        <x-slot name="controls">
            @can('update', $customer)
            <a href="{{ route('customers.edit', $customer) }}" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> @lang('crud.customers.edit_customer')
            </a>
            @endcan
            <a href="{{ route('customers.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> @lang('crud.customers.back_to_customers')
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <div class="col-lg-4 mb-4">
            <!-- Customer Overview Card -->
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-person-circle me-2"></i>@lang('crud.customers.customer_overview')
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        @if($customer->image)
                            <img src="{{ $customer->image }}" alt="{{ $customer->name }}" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                        @else
                            <div class="d-flex align-items-center justify-content-center border rounded-circle bg-light mb-3 mx-auto" style="width: 150px; height: 150px;">
                                <i class="bi bi-person text-muted" style="font-size: 3rem;"></i>
                            </div>
                        @endif
                        <h4 class="mb-1">{{ $customer->name }}</h4>
                        <p class="text-muted">Customer #{{ $customer->id }}</p>

                        <div class="d-flex justify-content-center gap-2 mt-3">
                            @if($customer->phone)
                            <a href="tel:{{ $customer->phone }}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-telephone me-1"></i> Call
                            </a>
                            @endif

                            @if($customer->email)
                            <a href="mailto:{{ $customer->email }}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-envelope me-1"></i> Email
                            </a>
                            @endif
                        </div>
                    </div>

                    <hr>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Status:</span>
                            <span class="badge bg-{{ optional($customer->status)->name == 'Active' ? 'success' : 'secondary' }}">
                                {{ optional($customer->status)->name ?? 'N/A' }}
                            </span>
                        </div>

                        @if($customer->phone)
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Phone:</span>
                            <span>{{ $customer->phone }}</span>
                        </div>
                        @endif

                        @if($customer->email)
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Email:</span>
                            <span>{{ $customer->email }}</span>
                        </div>
                        @endif

                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Customer Since:</span>
                            <span>{{ $customer->created_at->format('M d, Y') }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Card -->
        </div>

        <div class="col-lg-8 mb-4">
            <!-- Customer Details Card -->
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-info-circle me-2"></i>Additional Information
                    </h5>
                </div>
                <div class="card-body">
                    @if($customer->address)
                    <div class="mb-4">
                        <h6 class="mb-2">Address</h6>
                        <p class="mb-0">{{ $customer->address }}</p>
                    </div>
                    <hr>
                    @endif

                    <div class="mb-4">
                        <h6 class="mb-2">Notes</h6>
                        <p class="mb-0">{{ $customer->description ?? 'No additional notes available.' }}</p>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card bg-soft-primary h-100">
                                <div class="card-body">
                                    <h6 class="card-subtitle mb-2">Total Purchases</h6>
                                    <h2 class="card-title mb-0">{{ count($customer->sales ?? []) }}</h2>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="card bg-soft-success h-100">
                                <div class="card-body">
                                    <h6 class="card-subtitle mb-2">Total Spent</h6>
                                    <h2 class="card-title mb-0">{{ _money($customer->invoices->sum('total_amount') ?? 0) }}</h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Card -->
        </div>
    </div>

    <!-- Recent Purchases Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-header-title">
                <i class="bi bi-receipt me-2"></i>Recent Purchases
            </h5>
        </div>
        <div class="card-body">
            @if(count($customer->sales ?? []) > 0)
                <div class="table-responsive">
                    <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                        <thead class="thead-light">
                            <tr>
                                <th>Invoice #</th>
                                <th>Date</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($customer->sales ?? [] as $sale)
                            <tr>
                                <td>#{{ $sale->id }}</td>
                                <td>{{ $sale->created_at->format('M d, Y') }}</td>
                                <td>{{ _money($sale->total_amount) }}</td>
                                <td>
                                    <span class="badge bg-success">
                                        {{ $sale->status }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ route('sales.show', $sale) }}" class="btn btn-white btn-sm">
                                        <i class="bi-eye me-1"></i> View
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="bi bi-receipt text-muted" style="font-size: 3rem;"></i>
                    <p class="mt-2">No purchase history for this customer</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="d-flex justify-content-between mt-2 mb-4">
        <div>
            <a href="{{ route('customers.index') }}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left me-1"></i> Back to Customers
            </a>

            @can('create', App\Models\Customer::class)
            <a href="{{ route('customers.create') }}" class="btn btn-outline-primary">
                <i class="bi bi-plus-circle me-1"></i> Create New Customer
            </a>
            @endcan
        </div>

        <div>
            @can('update', $customer)
            <a href="{{ route('customers.edit', $customer) }}" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> Edit
            </a>
            @endcan

            @can('delete', $customer)
            <form action="{{ route('customers.destroy', $customer) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this customer?')">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-danger">
                    <i class="bi bi-trash me-1"></i> Delete
                </button>
            </form>
            @endcan
        </div>
    </div>
</div>
@endsection
