@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">shield</x-slot>
        <x-slot name="title">Role: {{ _formatText($role->name) }}</x-slot>
        <x-slot name="subtitle">View role details and assigned permissions</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('roles.index') }}">Roles</a></li>
            <li class="breadcrumb-item active" aria-current="page">View</li>
        </x-slot>
        <x-slot name="controls">
            @if($role->name != 'super-admin')
                @can('update', $role)
                <a href="{{ route('roles.edit', $role) }}" class="btn btn-primary btn-sm me-2">
                    <i class="bi bi-pencil-square me-1"></i> Edit Role
                </a>
                @endcan
            @endif
            <a href="{{ route('roles.index') }}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-left me-1"></i> Back to Roles
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <!-- Role Overview Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-info-circle me-2"></i>Role Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar avatar-xl avatar-soft-primary avatar-circle mb-3">
                            <span class="avatar-initials">{{ strtoupper(substr($role->name, 0, 1)) }}</span>
                        </div>
                        <h4 class="mb-1">{{ _formatText($role->name) }}</h4>
                        @if($role->name == 'super-admin')
                        <span class="badge bg-danger">System Role</span>
                        @endif
                    </div>

                    <div class="mb-4">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Created:</span>
                            <span>{{ $role->created_at->format('M d, Y') }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Last Updated:</span>
                            <span>{{ $role->updated_at->format('M d, Y') }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Permissions:</span>
                            <span class="badge bg-primary">{{ $role->permissions->count() }}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Users with this role:</span>
                            <span class="badge bg-info">{{ $role->users->count() }}</span>
                        </div>
                    </div>

                    <hr>

                    <div class="d-grid gap-2">
                        @if($role->name != 'super-admin')
                            @can('update', $role)
                            <a href="{{ route('roles.edit', $role) }}" class="btn btn-outline-primary">
                                <i class="bi bi-pencil-square me-1"></i> Edit Role
                            </a>
                            @endcan
                            
                            @can('delete', $role)
                            <form action="{{ route('roles.destroy', $role) }}" method="POST" 
                                  onsubmit="return confirm('Are you sure you want to delete this role? This action cannot be undone.')">
                                @csrf @method('DELETE')
                                <button type="submit" class="btn btn-outline-danger w-100">
                                    <i class="bi bi-trash me-1"></i> Delete Role
                                </button>
                            </form>
                            @endcan
                        @else
                            <div class="alert alert-warning mb-0">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                This is a system role and cannot be modified or deleted.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions Card -->
        <div class="col-lg-8 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-header-title">
                            <i class="bi bi-shield-check me-2"></i>Assigned Permissions
                        </h5>
                        <span class="badge bg-primary">{{ $role->permissions->count() }} permissions</span>
                    </div>
                </div>
                <div class="card-body">
                    @php
                        $groupedPermissions = $role->permissions->groupBy(function($permission) {
                            $parts = explode('-', $permission->name);
                            return count($parts) > 1 ? $parts[1] : 'other';
                        });
                    @endphp

                    @if($role->permissions->count() > 0)
                        <div class="row">
                            @foreach($groupedPermissions as $group => $permissions)
                            <div class="col-md-6 mb-4">
                                <div class="card bg-light">
                                    <div class="card-header py-2">
                                        <h6 class="mb-0 text-capitalize">{{ $group }}</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-group list-group-flush">
                                            @foreach($permissions as $permission)
                                            <li class="list-group-item bg-transparent px-0 py-1 border-0">
                                                <i class="bi bi-check-circle-fill text-success me-2"></i>
                                                {{ ucfirst(str_replace('-', ' ', $permission->name)) }}
                                            </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="bi bi-shield-x text-muted" style="font-size: 3rem;"></i>
                            <p class="mt-3">This role has no permissions assigned.</p>
                            @if($role->name != 'super-admin')
                                @can('update', $role)
                                <a href="{{ route('roles.edit', $role) }}" class="btn btn-sm btn-primary">
                                    <i class="bi bi-plus-circle me-1"></i> Assign Permissions
                                </a>
                                @endcan
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Users with this Role Card -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-header-title">
                            <i class="bi bi-people me-2"></i>Users with this Role
                        </h5>
                        <span class="badge bg-info">{{ $role->users->count() }} users</span>
                    </div>
                </div>
                <div class="card-body">
                    @if($role->users->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Joined</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($role->users as $user)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-circle">
                                                    <span class="avatar-initials">{{ strtoupper(substr($user->name, 0, 1)) }}</span>
                                                </div>
                                                <span class="ms-2">{{ $user->name }}</span>
                                            </div>
                                        </td>
                                        <td>{{ $user->email }}</td>
                                        <td>{{ $user->created_at->format('M d, Y') }}</td>
                                        <td class="text-center">
                                            <a href="{{ route('users.show', $user) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                            <p class="mt-3">No users have been assigned this role yet.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="d-flex justify-content-between mt-2 mb-4">
        <div>
            <a href="{{ route('roles.index') }}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left me-1"></i> Back to Roles
            </a>

            @can('create', App\Models\Role::class)
            <a href="{{ route('roles.create') }}" class="btn btn-outline-primary">
                <i class="bi bi-plus-circle me-1"></i> Create New Role
            </a>
            @endcan
        </div>
    </div>
</div>
@endsection
