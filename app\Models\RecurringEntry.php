<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;

class RecurringEntry extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'description',
        'frequency',
        'interval',
        'start_date',
        'end_date',
        'last_generated_date',
        'next_generation_date',
        'is_active',
        'template_journal_entry_id',
        'created_by',
        'updated_by',
        'business_type_id',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'last_generated_date' => 'date',
        'next_generation_date' => 'date',
        'is_active' => 'boolean',
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];

    public function templateJournalEntry()
    {
        return $this->belongsTo(JournalEntry::class, 'template_journal_entry_id');
    }

    public function journalEntries()
    {
        return $this->hasMany(JournalEntry::class, 'recurring_entry_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
