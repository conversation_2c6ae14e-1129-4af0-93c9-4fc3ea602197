@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                Edit {{ $deductionContribution->apply_mode == 'DEDUCTION' ? 'Deduction' : 'Contribution' }}
            </h4>
        </div>

        <div class="card-body">
            <form action="{{ route('deduction-contributions.update', $deductionContribution) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="name">Name</label>
                            <input type="text" name="name" id="name" class="form-control" value="{{ old('name', $deductionContribution->name) }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="code">Code</label>
                            <input type="text" name="code" id="code" class="form-control" value="{{ old('code', $deductionContribution->code) }}">
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="formula">Formula/Amount</label>
                            <input type="text" name="formula" id="formula" class="form-control" value="{{ old('formula', $deductionContribution->formula) }}" required>
                            <small class="form-text text-muted">
                                Use BASIC_PAY as a variable. Example: BASIC_PAY * 0.05 for 5% of basic pay.
                            </small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="status_id">Status</label>
                            <select name="status_id" id="status_id" class="form-control" required>
                                @foreach($statuses as $id => $name)
                                    <option value="{{ $id }}" {{ old('status_id', $deductionContribution->status_id) == $id ? 'selected' : '' }}>{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="apply_at">Apply At</label>
                            <select name="apply_at" id="apply_at" class="form-control" required>
                                <option value="BEFORE" {{ old('apply_at', $deductionContribution->apply_at) == 'BEFORE' ? 'selected' : '' }}>Before Tax</option>
                                <option value="AFTER" {{ old('apply_at', $deductionContribution->apply_at) == 'AFTER' ? 'selected' : '' }}>After Tax</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="is_calculatable"> Affects Net Pay?</label>
                            <select name="is_calculatable" id="is_calculatable" class="form-control" required>
                                <option value="1" {{ old('is_calculatable', $deductionContribution->apply_at) == 1 ? 'selected' : '' }}>Yes</option>
                                <option value="0" {{ old('is_calculatable', $deductionContribution->apply_at) === 0 ? 'selected' : '' }}>No</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Type</label>
                            <input type="hidden" class="form-control" value="{{ $deductionContribution->apply_mode }}" readonly>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-4">
                    <label for="description">Description</label>
                    <textarea name="description" id="description" class="form-control" rows="3">{{ old('description', $deductionContribution->description) }}</textarea>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ route('deduction-contributions.index', ['type' => strtolower($deductionContribution->apply_mode)]) }}" class="btn btn-light">
                        <i class="bi bi-arrow-left"></i> Back
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Update
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
