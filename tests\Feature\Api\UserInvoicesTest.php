<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Invoice;

use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UserInvoicesTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create(['email' => '<EMAIL>']);

        Sanctum::actingAs($user, [], 'web');

        $this->seed(\Database\Seeders\PermissionsSeeder::class);

        $this->withoutExceptionHandling();
    }

    /**
     * @test
     */
    public function it_gets_user_invoices()
    {
        $user = User::factory()->create();
        $invoices = Invoice::factory()
            ->count(2)
            ->create([
                'approved_by' => $user->id,
            ]);

        $response = $this->getJson(route('api.users.invoices.index', $user));

        $response->assertOk()->assertSee($invoices[0]->date_from);
    }

    /**
     * @test
     */
    public function it_stores_the_user_invoices()
    {
        $user = User::factory()->create();
        $data = Invoice::factory()
            ->make([
                'approved_by' => $user->id,
            ])
            ->toArray();

        $response = $this->postJson(
            route('api.users.invoices.store', $user),
            $data
        );

        unset($data['created_by']);
        unset($data['updated_by']);
        unset($data['status_id']);
        unset($data['approved_by']);

        $this->assertDatabaseHas('invoices', $data);

        $response->assertStatus(201)->assertJsonFragment($data);

        $invoice = Invoice::latest('id')->first();

        $this->assertEquals($user->id, $invoice->approved_by);
    }
}
