<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('journal_entries', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('entry_number')->unique();
            $table->date('entry_date');
            $table->string('reference_number')->nullable();
            $table->text('description')->nullable();
            $table->string('entry_type')->default('manual'); // manual, system, recurring, etc.
            $table->string('status')->default('draft'); // draft, posted, approved, rejected
            $table->unsignedBigInteger('fiscal_year_id')->nullable();
            $table->unsignedBigInteger('fiscal_period_id')->nullable();
            $table->unsignedBigInteger('currency_id')->nullable();
            $table->decimal('exchange_rate', 20, 6)->default(1);
            $table->boolean('is_recurring')->default(false);
            $table->unsignedBigInteger('recurring_entry_id')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->unsignedBigInteger('posted_by')->nullable();
            $table->timestamp('posted_at')->nullable();
            $table->unsignedBigInteger('business_type_id')->nullable();
            $table->unsignedBigInteger('branch_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->foreign('fiscal_year_id')
                ->references('id')
                ->on('fiscal_years')
                ->onDelete('set null');
                
            $table->foreign('fiscal_period_id')
                ->references('id')
                ->on('fiscal_periods')
                ->onDelete('set null');
                
            $table->foreign('created_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('updated_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('approved_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('posted_by')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('business_type_id')
                ->references('id')
                ->on('business_types')
                ->onDelete('set null');
                
            $table->foreign('branch_id')
                ->references('id')
                ->on('branches')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('journal_entries');
    }
};
