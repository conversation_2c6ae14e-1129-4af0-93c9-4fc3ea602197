<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Payslip - {{ $payroll->employee->name ?? 'Employee' }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 11px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
            color: #333;
            background: white;
        }
        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            background: white;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #333;
            padding-bottom: 20px;
        }
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            color: #333;
            font-weight: bold;
        }
        .header .company-info {
            margin: 10px 0;
            font-size: 12px;
            color: #666;
        }
        .header .payslip-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin: 15px 0 5px 0;
        }
        .header .period {
            font-size: 13px;
            color: #666;
            font-weight: 500;
        }

        .employee-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 25px;
        }
        .employee-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 14px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
        }
        .employee-info {
            display: table;
            width: 100%;
        }
        .employee-row {
            display: table-row;
        }
        .employee-label, .employee-value {
            display: table-cell;
            padding: 4px 15px 4px 0;
            vertical-align: top;
        }
        .employee-label {
            font-weight: bold;
            width: 120px;
            color: #555;
        }

        .payroll-details {
            display: table;
            width: 100%;
            margin-bottom: 25px;
        }
        .earnings, .deductions {
            display: table-cell;
            width: 48%;
            vertical-align: top;
        }
        .earnings {
            padding-right: 2%;
        }
        .deductions {
            padding-left: 2%;
        }

        .section-title {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            margin: 0 0 1px 0;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11px;
        }
        table th, table td {
            border: 1px solid #dee2e6;
            padding: 6px 8px;
            text-align: left;
        }
        table th {
            background-color: #f8f9fa;
            font-weight: bold;
            font-size: 10px;
            text-transform: uppercase;
        }
        table td.amount {
            text-align: right;
            font-weight: 500;
        }
        table tfoot th {
            background-color: #333;
            color: white;
            font-weight: bold;
        }
        table tfoot td {
            background-color: #333;
            color: white;
            font-weight: bold;
            text-align: right;
        }

        .tax-breakdown {
            margin: 25px 0;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        .tax-breakdown table th {
            background-color: #f8f9fa;
        }

        .summary {
            margin: 30px 0;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        .summary .section-title {
            background: #333;
            color: white;
            font-size: 14px;
            padding: 12px;
        }
        .summary-content {
            padding: 20px;
            background: #f8f9fa;
        }
        .summary-row {
            display: table;
            width: 100%;
            margin-bottom: 10px;
        }
        .summary-row:last-child {
            margin-bottom: 0;
            border-top: 2px solid #007bff;
            padding-top: 10px;
            font-weight: bold;
            font-size: 14px;
        }
        .summary-label, .summary-value {
            display: table-cell;
            padding: 5px 0;
        }
        .summary-label {
            font-weight: bold;
            color: #555;
        }
        .summary-value {
            text-align: right;
            font-weight: 500;
        }
        .net-pay {
            color: #28a745;
            font-size: 16px;
        }

        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 9px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
        }

        .currency {
            font-weight: 500;
        }

        @media print {
            body { margin: 0; padding: 15px; }
            .container { max-width: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ config('app.name', 'Company Name') }}</h1>
            <div class="company-info">
                {{ auth()->user()->branch->address ?? 'Company Address' }}<br>
                Phone: {{ auth()->user()->branch->phone ?? '+****************' }}
            </div>
            <div class="payslip-title">PAYSLIP</div>
            <div class="period">
                Pay Period: {{ $payroll->date_from ? $payroll->date_from->format('F d, Y') : 'N/A' }} - {{ $payroll->date_to ? $payroll->date_to->format('F d, Y') : 'N/A' }}
            </div>
        </div>

        <div class="employee-section">
            <h3>Employee Information</h3>
            <div class="employee-info">
                <div class="employee-row">
                    <div class="employee-label">Employee Name:</div>
                    <div class="employee-value">{{ $payroll->employee->name ?? 'N/A' }}</div>
                </div>
                <div class="employee-row">
                    <div class="employee-label">Employee ID:</div>
                    <div class="employee-value">{{ $payroll->employee->id ?? 'N/A' }}</div>
                </div>
                <div class="employee-row">
                    <div class="employee-label">Position:</div>
                    <div class="employee-value">{{ $payroll->employee->position ?? 'N/A' }}</div>
                </div>
                <div class="employee-row">
                    <div class="employee-label">Grade:</div>
                    <div class="employee-value">{{ $payroll->employee->grade ?? 'N/A' }}</div>
                </div>
                <div class="employee-row">
                    <div class="employee-label">Email:</div>
                    <div class="employee-value">{{ $payroll->employee->email ?? 'N/A' }}</div>
                </div>
                <div class="employee-row">
                    <div class="employee-label">Phone:</div>
                    <div class="employee-value">{{ $payroll->employee->phone ?? 'N/A' }}</div>
                </div>
            </div>
        </div>

        <div class="payroll-details">
            <div class="earnings">
                <div class="section-title">EARNINGS</div>
                <table>
                    <thead>
                        <tr>
                            <th>Description</th>
                            <th style="text-align: right;">Amount (K)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Basic Pay</td>
                            <td class="amount currency">{{ number_format($payroll->basic_pay, 2) }}</td>
                        </tr>
                        @foreach($payroll->contributions as $contribution)
                        <tr>
                            <td>{{ $contribution->name }}</td>
                            <td class="amount currency">{{ number_format($contribution->amount, 2) }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                    <tfoot>
                        <tr>
                            <th>Total Earnings</th>
                            <td class="currency">{{ number_format($payroll->gross_pay ?: $payroll->gross, 2) }}</td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class="deductions">
                <div class="section-title">DEDUCTIONS</div>
                <table>
                    <thead>
                        <tr>
                            <th>Description</th>
                            <th style="text-align: right;">Amount (K)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>PAYE Tax</td>
                            <td class="amount currency">{{ number_format($payroll->tax_amount ?: $payroll->payee, 2) }}</td>
                        </tr>
                        @foreach($payroll->deductions as $deduction)
                        <tr>
                            <td>{{ $deduction->name }}</td>
                            <td class="amount currency">{{ number_format($deduction->amount, 2) }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                    <tfoot>
                        <tr>
                            <th>Total Deductions</th>
                            <td class="currency">{{ number_format(($payroll->tax_amount ?: $payroll->payee) + $payroll->deductions->sum('amount'), 2) }}</td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>


        <!-- Summary -->
        <div class="summary">
            <div class="section-title">PAY SUMMARY</div>
            <div class="summary-content">
                <div class="summary-row">
                    <div class="summary-label">Gross Pay:</div>
                    <div class="summary-value currency">K{{ number_format($payroll->gross_pay ?: $payroll->gross, 2) }}</div>
                </div>
                <div class="summary-row">
                    <div class="summary-label">Total Deductions:</div>
                    <div class="summary-value currency">K{{ number_format(($payroll->tax_amount ?: $payroll->payee) + $payroll->deductions->sum('amount'), 2) }}</div>
                </div>
                <div class="summary-row">
                    <div class="summary-label net-pay">Net Pay:</div>
                    <div class="summary-value currency net-pay">K{{ number_format($payroll->net_pay, 2) }}</div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p><strong>This is a computer-generated document. No signature is required.</strong></p>
            <p>Generated on: {{ now()->format('F d, Y \a\t H:i:s') }}</p>
            <p>Payroll processed by: {{ auth()->user()->name ?? 'System' }}</p>
        </div>
    </div>
</body>
</html>
