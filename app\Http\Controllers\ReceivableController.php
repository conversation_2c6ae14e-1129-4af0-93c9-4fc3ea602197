<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Facades\App\Libraries\InvoiceHandler;

class ReceivableController extends Controller
{
    /**
     * Display customer receivables (debtors)
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Customer::class);

        // Build query for customers with outstanding balances
        $query = Customer::with(['invoices' => function($q) {
            $q->whereRaw('amount_total > amount_paid');
        }])
        ->whereHas('invoices', function($q) {
            $q->whereRaw('amount_total > amount_paid');
        })
        ->when(!auth()->user()->isSuperAdmin(), function($query) {
            $query->where('branch_id', auth()->user()->branch_id);
        });

        // Apply filters
        $this->applyFilters($query, $request);

        // Get customers with outstanding balances
        $customers = $query->orderBy('name')->paginate(20);

        // Calculate totals for each customer
        $customers->getCollection()->transform(function ($customer) {
            $customer->total_outstanding = $customer->invoices->sum('balance');
            $customer->total_invoices = $customer->invoices->count();
            $customer->oldest_invoice = $customer->invoices->min('created_at');
            return $customer;
        });

        // Calculate analytics
        $analytics = $this->calculateReceivableAnalytics($request);

        // Get filter options
        $filterOptions = $this->getFilterOptions();

        return view('app.receivables.index', compact('customers', 'analytics', 'filterOptions'));
    }

    /**
     * Show customer receivable details
     */
    public function show(Customer $customer)
    {
        $this->authorize('view', $customer);

        // Load customer with outstanding invoices and payments
        $customer->load([
            'invoices' => function($q) {
                $q->whereRaw('amount_total > amount_paid')->orderBy('created_at', 'desc');
            },
            'invoices.payments',
            'invoices.currency'
        ]);

        // Calculate customer totals
        $customerTotals = [
            'total_outstanding' => $customer->invoices->sum('balance'),
            'total_invoiced' => $customer->invoices->sum('amount_total'),
            'total_paid' => $customer->invoices->sum('amount_paid'),
            'invoice_count' => $customer->invoices->count(),
            'oldest_invoice' => $customer->invoices->min('created_at'),
        ];

        return view('app.receivables.show', compact('customer', 'customerTotals'));
    }

    /**
     * Show payment form for customer receivables
     */
    public function createPayment(Customer $customer)
    {
        $this->authorize('create', Payment::class);

        // Get customer's outstanding invoices
        $invoices = $customer->invoices()
                            ->whereRaw('amount_total > amount_paid')
                            ->orderBy('created_at', 'desc')
                            ->get();

        $currencies = Currency::orderBy('name')->get();

        return view('app.receivables.create-payment', compact('customer', 'invoices', 'currencies'));
    }

    /**
     * Store payment for customer receivable
     */
    public function storePayment(Request $request, Customer $customer)
    {
        $this->authorize('create', Payment::class);

        $validated = $request->validate([
            'invoice_id' => 'required|tenant_exists:invoices,id',
            'amount' => 'required|numeric|min:0.01',
            'reference_no' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'comment' => 'nullable|string|max:1000',
            'payment_method' => 'nullable|string|max:100',
            'currency_id' => 'nullable|tenant_exists:currencies,id',
        ]);

        DB::beginTransaction();
        try {
            // Get the invoice and validate it belongs to the customer
            $invoice = Invoice::findOrFail($validated['invoice_id']);

            if ($invoice->customer_id !== $customer->id) {
                return back()
                    ->withInput()
                    ->with('error', 'Selected invoice does not belong to this customer.');
            }

            // Validate payment amount doesn't exceed balance
            if ($validated['amount'] > $invoice->balance) {
                return back()
                    ->withInput()
                    ->with('error', 'Payment amount cannot exceed the outstanding balance of ' . _money($invoice->balance));
            }

            // Create payment
            $paymentData = [
                'amount' => $validated['amount'],
                'description' => $validated['description'] ?? 'Payment from ' . $customer->name,
                'comment' => $validated['comment'],
                'reference_no' => $validated['reference_no'],
                'payment_method' => $validated['payment_method'],
                'currency_id' => $validated['currency_id'],
                'balance' => $invoice->balance - $validated['amount'],
                'payment_type' => 'receivable',
            ];

            $payment = InvoiceHandler::addPayment($invoice, $paymentData);

            // Update the invoice balance
            $invoice->increment('amount_paid', $validated['amount']);

            DB::commit();

            return redirect()
                ->route('receivables.show', $customer)
                ->with('success', 'Payment recorded successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()
                ->withInput()
                ->with('error', 'Failed to record payment: ' . $e->getMessage());
        }
    }

    /**
     * Apply filters to customer query
     */
    private function applyFilters($query, Request $request)
    {
        // Search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Outstanding amount range
        if ($request->filled('amount_from') || $request->filled('amount_to')) {
            $query->whereHas('invoices', function($q) use ($request) {
                $q->whereRaw('amount_total > amount_paid');
                if ($request->filled('amount_from')) {
                    $q->havingRaw('SUM(amount_total - amount_paid) >= ?', [$request->amount_from]);
                }
                if ($request->filled('amount_to')) {
                    $q->havingRaw('SUM(amount_total - amount_paid) <= ?', [$request->amount_to]);
                }
            });
        }

        // Date range for oldest invoice
        if ($request->filled('date_from')) {
            $query->whereHas('invoices', function($q) use ($request) {
                $q->where('created_at', '>=', $request->date_from);
            });
        }

        if ($request->filled('date_to')) {
            $query->whereHas('invoices', function($q) use ($request) {
                $q->where('created_at', '<=', $request->date_to);
            });
        }
    }

    /**
     * Calculate receivable analytics
     */
    private function calculateReceivableAnalytics(Request $request)
    {
        $baseQuery = Customer::whereHas('invoices', function($q) {
            $q->whereRaw('amount_total > amount_paid');
        });

        // Apply same filters as main query
        $this->applyFilters($baseQuery, $request);

        return [
            'total_customers' => $baseQuery->count(),
            'total_outstanding' => Invoice::whereRaw('amount_total > amount_paid')->sum(DB::raw('amount_total - amount_paid')),
            'avg_outstanding' => Invoice::whereRaw('amount_total > amount_paid')->avg(DB::raw('amount_total - amount_paid')),
            'overdue_invoices' => Invoice::whereRaw('amount_total > amount_paid')
                                        ->where('created_at', '<', now()->subDays(30))
                                        ->count(),
            'overdue_amount' => Invoice::whereRaw('amount_total > amount_paid')
                                      ->where('created_at', '<', now()->subDays(30))
                                      ->sum(DB::raw('amount_total - amount_paid')),
        ];
    }

    /**
     * Get filter options for dropdowns
     */
    private function getFilterOptions()
    {
        return [
            'currencies' => Currency::orderBy('name')->get(),
        ];
    }
}
