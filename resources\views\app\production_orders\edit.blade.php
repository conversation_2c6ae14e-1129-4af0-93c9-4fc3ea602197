@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
          <div class="col-sm mb-2 mb-sm-0">
            <h1 class="page-header-title">
                Edit Production Order
                <a href="{{ route('production-orders.index') }}" class="mr-4 float-right"
                    ><i class="icon ion-md-arrow-back"></i> Back
                </a>
            </h1>
          </div>
          <!-- End Col -->
        </div>
    </div>
    <!-- End Page Header -->

    <x-form
        method="PUT"
        action="{{ route('production-orders.update', $productionOrder) }}"
        class="mt-4"
    >
        <div class="row">
            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.text
                    name="order_number"
                    label="Order Number"
                    value="{{ $productionOrder->order_number }}"
                    readonly
                ></x-inputs.text>
            </x-inputs.group>

            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.text
                    name="product_name"
                    label="Product"
                    value="{{ $productionOrder->product->name }}"
                    readonly
                ></x-inputs.text>
                <input type="hidden" name="product_id" value="{{ $productionOrder->product_id }}">
            </x-inputs.group>

            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.text
                    name="quantity_display"
                    label="Quantity"
                    value="{{ $productionOrder->quantity }} {{ $productionOrder->unit_of_measure }}"
                    readonly
                ></x-inputs.text>
                <input type="hidden" name="quantity" value="{{ $productionOrder->quantity }}">
                <input type="hidden" name="unit_of_measure" value="{{ $productionOrder->unit_of_measure }}">
            </x-inputs.group>

            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.date
                    name="planned_start_date"
                    label="Planned Start Date"
                    value="{{ $productionOrder->planned_start_date->format('Y-m-d') }}"
                    required
                ></x-inputs.date>
            </x-inputs.group>

            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.date
                    name="planned_end_date"
                    label="Planned End Date"
                    value="{{ $productionOrder->planned_end_date->format('Y-m-d') }}"
                    required
                ></x-inputs.date>
            </x-inputs.group>

            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.select
                    name="status"
                    label="Status"
                    required
                >
                    <option value="draft" {{ $productionOrder->status == 'draft' ? 'selected' : '' }}>Draft</option>
                    <option value="planned" {{ $productionOrder->status == 'planned' ? 'selected' : '' }}>Planned</option>
                    <option value="in_progress" {{ $productionOrder->status == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                    <option value="completed" {{ $productionOrder->status == 'completed' ? 'selected' : '' }}>Completed</option>
                    <option value="cancelled" {{ $productionOrder->status == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                </x-inputs.select>
            </x-inputs.group>

            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.select
                    name="branch_id"
                    label="Branch"
                >
                    <option value="">Select Branch</option>
                    @foreach($branches as $id => $name)
                    <option value="{{ $id }}" {{ $productionOrder->branch_id == $id ? 'selected' : '' }}>
                        {{ $name }}
                    </option>
                    @endforeach
                </x-inputs.select>
            </x-inputs.group>

            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.date
                    name="actual_start_date"
                    label="Actual Start Date"
                    value="{{ $productionOrder->actual_start_date ? $productionOrder->actual_start_date->format('Y-m-d') : '' }}"
                ></x-inputs.date>
            </x-inputs.group>

            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.date
                    name="actual_end_date"
                    label="Actual End Date"
                    value="{{ $productionOrder->actual_end_date ? $productionOrder->actual_end_date->format('Y-m-d') : '' }}"
                ></x-inputs.date>
            </x-inputs.group>

            <x-inputs.group class="col-sm-12 mb-4">
                <x-inputs.textarea
                    name="description"
                    label="Description"
                    rows="3"
                >{{ $productionOrder->description }}</x-inputs.textarea>
            </x-inputs.group>
        </div>

        <div class="mt-4">
            <a
                href="{{ route('production-orders.index') }}"
                class="btn btn-light"
            >
                <i class="icon ion-md-return-left text-primary"></i>
                @lang('crud.common.back')
            </a>

            <a
                href="{{ route('production-orders.create') }}"
                class="btn btn-light"
            >
                @lang('crud.common.create')
            </a>

            <button type="submit" class="btn btn-primary float-right">
                @lang('crud.common.update')
            </button>
        </div>
    </x-form>
</div>
@endsection
