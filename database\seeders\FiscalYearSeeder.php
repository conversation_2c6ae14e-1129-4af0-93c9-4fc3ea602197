<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\FiscalYear;
use App\Models\FiscalPeriod;
use Carbon\Carbon;

class FiscalYearSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create current fiscal year
        $currentYear = Carbon::now()->year;
        $startDate = Carbon::createFromDate($currentYear, 1, 1);
        $endDate = Carbon::createFromDate($currentYear, 12, 31);

        $fiscalYear = FiscalYear::updateOrCreate(
            ['name' => "FY {$currentYear}"],
            [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'is_closed' => false,
                'is_active' => true,
                'description' => "Fiscal Year {$currentYear}",
            ]
        );

        // Create fiscal periods (months)
        $this->createFiscalPeriods($fiscalYear, $startDate, $endDate);

        // Create next fiscal year
        $nextYear = $currentYear + 1;
        $nextStartDate = Carbon::createFromDate($nextYear, 1, 1);
        $nextEndDate = Carbon::createFromDate($nextYear, 12, 31);

        $nextFiscalYear = FiscalYear::updateOrCreate(
            ['name' => "FY {$nextYear}"],
            [
                'start_date' => $nextStartDate,
                'end_date' => $nextEndDate,
                'is_closed' => false,
                'is_active' => false,
                'description' => "Fiscal Year {$nextYear}",
            ]
        );

        // Create fiscal periods for next year
        $this->createFiscalPeriods($nextFiscalYear, $nextStartDate, $nextEndDate);
    }

    /**
     * Create fiscal periods for a fiscal year.
     *
     * @param  \App\Models\FiscalYear  $fiscalYear
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @return void
     */
    private function createFiscalPeriods(FiscalYear $fiscalYear, Carbon $startDate, Carbon $endDate)
    {
        $currentDate = $startDate->copy();
        $periodNumber = 1;

        while ($currentDate->lessThan($endDate)) {
            $periodStartDate = $currentDate->copy()->startOfMonth();
            $periodEndDate = $currentDate->copy()->endOfMonth();

            // Ensure end date doesn't exceed fiscal year end date
            if ($periodEndDate->greaterThan($endDate)) {
                $periodEndDate = $endDate->copy();
            }

            $monthName = $periodStartDate->format('F');
            $year = $periodStartDate->format('Y');

            FiscalPeriod::updateOrCreate(
                [
                    'fiscal_year_id' => $fiscalYear->id,
                    'name' => "{$monthName} {$year}",
                ],
                [
                    'start_date' => $periodStartDate,
                    'end_date' => $periodEndDate,
                    'period_number' => $periodNumber,
                    'is_closed' => $fiscalYear->is_closed,
                    'is_active' => $fiscalYear->is_active,
                    'description' => "Period {$periodNumber} of Fiscal Year {$year}",
                ]
            );

            $currentDate->addMonth();
            $periodNumber++;

            // Break if we've gone beyond the fiscal year end date
            if ($currentDate->greaterThan($endDate)) {
                break;
            }
        }
    }
}
