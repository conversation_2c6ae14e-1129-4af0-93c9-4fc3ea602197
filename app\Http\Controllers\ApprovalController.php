<?php

namespace App\Http\Controllers;

use App\Models\Stock;
use App\Models\Order;
use App\Models\Warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ApprovalController extends Controller
{
    /**
     * Show all pending approvals for the current user
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Stock::class);

        $approvals = collect();
        $filter = $request->get('filter', 'all'); // all, transfers, stock, orders

        // Get Transfer Approvals
        if (in_array($filter, ['all', 'transfers'])) {
            $transferApprovals = $this->getTransferApprovals($request);
            $approvals = $approvals->merge($transferApprovals);
        }

        // Get Stock Approvals (for stock adjustments, etc.)
        if (in_array($filter, ['all', 'stock'])) {
            $stockApprovals = $this->getStockApprovals($request);
            $approvals = $approvals->merge($stockApprovals);
        }

        // Get Order Approvals
        if (in_array($filter, ['all', 'orders'])) {
            $orderApprovals = $this->getOrderApprovals($request);
            $approvals = $approvals->merge($orderApprovals);
        }

        // Sort by created_at desc
        $approvals = $approvals->sortByDesc('created_at');

        // Apply search filter
        if ($request->get('search')) {
            $search = strtolower($request->get('search'));
            $approvals = $approvals->filter(function($approval) use ($search) {
                return str_contains(strtolower($approval['title']), $search) ||
                       str_contains(strtolower($approval['description']), $search);
            });
        }

        // Paginate manually
        $page = $request->get('page', 1);
        $perPage = 20;
        $total = $approvals->count();
        $approvals = $approvals->forPage($page, $perPage)->values();

        // Create pagination info
        $pagination = [
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $total,
            'last_page' => ceil($total / $perPage),
            'from' => ($page - 1) * $perPage + 1,
            'to' => min($page * $perPage, $total)
        ];

        return view('app.approvals.index', compact('approvals', 'pagination', 'filter'));
    }

    /**
     * Get pending transfer approvals
     */
    private function getTransferApprovals(Request $request)
    {
        // Get warehouses managed by current user
        $managedWarehouses = $this->getManagedWarehouses();

        if ($managedWarehouses->isEmpty()) {
            return collect();
        }

        $transfers = Stock::with(['product', 'warehouse', 'branch', 'stock.warehouse', 'stock.branch', 'createdBy'])
                         ->where('quantity', '>', 0) // Transfer IN records
                         ->whereNotNull('stock_id') // Only transfer records
                         ->whereNull('approved_by') // Not yet approved
                         ->whereIn('warehouse_id', $managedWarehouses)
                         ->get();

        return $transfers->map(function($transfer) {
            return [
                'id' => $transfer->id,
                'type' => 'transfer',
                'title' => 'Stock Transfer: ' . $transfer->product->name,
                'description' => "Transfer {$transfer->quantity} units from {$transfer->stock->warehouse->name} to {$transfer->warehouse->name}",
                'amount' => $transfer->quantity,
                'currency' => null,
                'status' => 'pending',
                'created_at' => $transfer->created_at,
                'created_by' => $transfer->createdBy->name ?? 'System',
                'from_location' => $transfer->stock->warehouse->name,
                'to_location' => $transfer->warehouse->name,
                'product_name' => $transfer->product->name,
                'unit_price' => $transfer->buying_price,
                'total_value' => $transfer->quantity * $transfer->buying_price,
                'approval_url' => route('stocks.approve-transfer'),
                'rejection_url' => route('stocks.reject-transfer'),
                'details' => $transfer
            ];
        });
    }

    /**
     * Get pending stock approvals (adjustments, etc.)
     */
    private function getStockApprovals(Request $request)
    {
        // Get pending stock adjustments
        $adjustments = Stock::where('quantity', '<', 0)
                           ->whereNull('approved_by')
                           ->where(function($query) {
                               $query->where('description', 'like', 'STOCK_%')
                                     ->orWhere('description', 'like', '%ADJUSTMENT%')
                                     ->orWhere('description', 'like', '%COUNT%')
                                     ->orWhere('description', 'like', '%EXPIRY%')
                                     ->orWhere('description', 'like', '%SCRAP%')
                                     ->orWhere('description', 'like', '%LOSS%')
                                     ->orWhere('description', 'like', '%REPAIR%')
                                     ->orWhere('description', 'like', '%RECYCLE%')
                                     ->orWhere('description', 'like', '%WRITE_OFF%')
                                     ->orWhere('description', 'like', '%WRITE_DOWN%');
                           })
                           ->when(!auth()->user()->isSuperAdmin(), function($query) {
                               $query->where('branch_id', auth()->user()->branch_id);
                           })
                           ->with(['product', 'warehouse', 'createdBy'])
                           ->get();

        return $adjustments->map(function($adjustment) {
            $type = explode(':', $adjustment->description)[0] ?? 'ADJUSTMENT';
            $comment = explode(':', $adjustment->description, 2)[1] ?? '';

            return [
                'id' => $adjustment->id,
                'type' => 'stock_adjustment',
                'title' => 'Stock Adjustment: ' . $adjustment->product->name,
                'description' => "Adjust {$adjustment->quantity} units in {$adjustment->warehouse->name} ({$type})",
                'amount' => abs($adjustment->quantity),
                'currency' => null,
                'status' => 'pending',
                'created_at' => $adjustment->created_at,
                'created_by' => $adjustment->createdBy->name ?? 'System',
                'from_location' => null,
                'to_location' => $adjustment->warehouse->name,
                'product_name' => $adjustment->product->name,
                'adjustment_type' => $type,
                'comment' => trim($comment),
                'unit_price' => $adjustment->buying_price,
                'total_value' => abs($adjustment->quantity) * $adjustment->buying_price,
                'approval_url' => route('stocks.approve-adjustment'),
                'rejection_url' => null, // No rejection for adjustments yet
                'details' => $adjustment
            ];
        });
    }

    /**
     * Get pending order approvals
     */
    private function getOrderApprovals(Request $request)
    {
        // Check if user can approve orders
        if (!$this->canApproveOrders()) {
            return collect();
        }

        // Get pending orders that need approval
        $orders = Order::with(['supplier', 'branch', 'createdBy', 'status'])
                      ->where(function($query) {
                          // Orders above certain amount need approval
                          $query->where('amount_total', '>', 1000) // Configurable threshold
                                ->whereNull('approved_by');
                      })
                      ->when(!auth()->user()->isSuperAdmin(), function($query) {
                          $query->where('branch_id', auth()->user()->branch_id);
                      })
                      ->get();

        return $orders->map(function($order) {
            $supplierName = $order->supplier ? $order->supplier->name : ($order->supplier_name ?: 'Unknown Supplier');

            return [
                'id' => $order->id,
                'type' => 'order',
                'title' => 'Order #' . $order->order_id,
                'description' => "Purchase order for {$supplierName}",
                'amount' => $order->amount_total,
                'currency' => '$',
                'status' => 'pending',
                'created_at' => $order->created_at,
                'created_by' => $order->createdBy ? $order->createdBy->name : 'System',
                'from_location' => null,
                'to_location' => $order->branch ? $order->branch->name : 'Unknown Branch',
                'supplier_name' => $supplierName,
                'order_id' => $order->order_id,
                'approval_url' => route('orders.approve', $order),
                'rejection_url' => route('orders.reject', $order),
                'details' => $order
            ];
        });
    }

    /**
     * Get warehouses managed by current user
     */
    private function getManagedWarehouses()
    {
        if (auth()->user()->isSuperAdmin()) {
            return Warehouse::pluck('id');
        }

        return Warehouse::where(function($query) {
            $query->where('manager_id', auth()->id())
                  ->orWhere('created_by', auth()->id())
                  ->orWhere('branch_id', auth()->user()->branch_id);
        })->pluck('id');
    }

    /**
     * Check if user can approve orders
     */
    private function canApproveOrders()
    {
        return auth()->user()->isSuperAdmin() || 
               auth()->user()->hasRole(['manager', 'supervisor', 'accountant']);
    }

    /**
     * Get approval counts by type
     */
    public function getCounts()
    {
        $counts = [
            'transfers' => 0,
            'stock' => 0,
            'orders' => 0,
            'total' => 0
        ];

        // Transfer approvals count
        $managedWarehouses = $this->getManagedWarehouses();
        if ($managedWarehouses->isNotEmpty()) {
            $counts['transfers'] = Stock::where('quantity', '>', 0)
                                       ->whereNotNull('stock_id')
                                       ->whereNull('approved_by')
                                       ->whereIn('warehouse_id', $managedWarehouses)
                                       ->count();
        }

        // Stock adjustments count
        $counts['stock'] = Stock::where('quantity', '<', 0)
                               ->whereNull('approved_by')
                               ->where(function($query) {
                                   $query->where('description', 'like', 'STOCK_%')
                                         ->orWhere('description', 'like', '%ADJUSTMENT%')
                                         ->orWhere('description', 'like', '%COUNT%')
                                         ->orWhere('description', 'like', '%EXPIRY%')
                                         ->orWhere('description', 'like', '%SCRAP%')
                                         ->orWhere('description', 'like', '%LOSS%')
                                         ->orWhere('description', 'like', '%REPAIR%')
                                         ->orWhere('description', 'like', '%RECYCLE%')
                                         ->orWhere('description', 'like', '%WRITE_OFF%')
                                         ->orWhere('description', 'like', '%WRITE_DOWN%');
                               })
                               ->when(!auth()->user()->isSuperAdmin(), function($query) {
                                   $query->where('branch_id', auth()->user()->branch_id);
                               })
                               ->count();

        // Order approvals count
        if ($this->canApproveOrders()) {
            $counts['orders'] = Order::where('amount_total', '>', 1000)
                                    ->whereNull('approved_by')
                                    ->when(!auth()->user()->isSuperAdmin(), function($query) {
                                        $query->where('branch_id', auth()->user()->branch_id);
                                    })
                                    ->count();
        }

        $counts['total'] = $counts['transfers'] + $counts['stock'] + $counts['orders'];

        return response()->json($counts);
    }

    /**
     * Approve an order
     */
    public function approveOrder(Request $request, Order $order)
    {
        $this->authorize('update', $order);

        $request->validate([
            'approval_notes' => 'nullable|string|max:500'
        ]);

        try {
            if (!$this->canApproveOrders()) {
                throw new \Exception('You are not authorized to approve orders.');
            }

            if ($order->approved_by) {
                throw new \Exception('This order has already been approved.');
            }

            // Use the existing ProductHandler to approve the order
            \App\Libraries\ProductHandler::approveOrder($order);

            return response()->json([
                'success' => true,
                'message' => 'Order approved successfully.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Approval failed: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Reject an order
     */
    public function rejectOrder(Request $request, Order $order)
    {
        $this->authorize('update', $order);

        $request->validate([
            'rejection_reason' => 'required|string|max:500'
        ]);

        try {
            if (!$this->canApproveOrders()) {
                throw new \Exception('You are not authorized to reject orders.');
            }

            if ($order->approved_by) {
                throw new \Exception('Cannot reject an already approved order.');
            }

            // Update order with rejection info
            $order->update([
                'description' => $order->description . ' | REJECTED: ' . $request->rejection_reason,
                'status_id' => 14, // Assuming 14 is cancelled/rejected status
                'updated_by' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Order rejected successfully.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Rejection failed: ' . $e->getMessage()
            ], 400);
        }
    }
}
