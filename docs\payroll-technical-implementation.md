# Payroll System Technical Implementation Guide

## Database Schema

The payroll system is built on the following database tables:

### 1. `employees`
```sql
CREATE TABLE employees (
    id bigint(20) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name varchar(255) NULL,
    address varchar(255) NULL,
    phone varchar(255) NULL,
    emergency_phone varchar(255) NULL,
    emergency_address varchar(255) NULL,
    grade varchar(255) NULL,
    email varchar(255) NULL,
    description text NULL,
    medical_description text NULL,
    basic_pay decimal(20,2) DEFAULT 0,
    created_by bigint(20) UNSIGNED NULL,
    updated_by bigint(20) UNSIGNED NULL,
    approved_by bigint(20) UNSIGNED NULL,
    user_id bigint(20) UNSIGNED NULL,
    created_at timestamp NULL,
    updated_at timestamp NULL,
    deleted_at timestamp NULL
);
```

### 2. `monthly_payrolls`
```sql
CREATE TABLE monthly_payrolls (
    id int(10) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name varchar(255) NULL,
    basic_pay decimal(20,2) NULL DEFAULT 0,
    net_pay decimal(20,2) NULL DEFAULT 0,
    date_from date NULL,
    date_to date NULL,
    created_by int(10) UNSIGNED NULL,
    updated_by int(10) UNSIGNED NULL,
    approved_by int(10) UNSIGNED NULL,
    employee_id int(10) UNSIGNED NULL,
    created_at timestamp NULL,
    updated_at timestamp NULL,
    deleted_at timestamp NULL
);
```

### 3. `payroll_items`
```sql
CREATE TABLE payroll_items (
    id int(10) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name varchar(255) NULL,
    amount decimal(20,2) DEFAULT 0,
    formula varchar(255) NULL,
    type varchar(255) NULL,
    description text NULL,
    created_by int(10) UNSIGNED NULL,
    updated_by int(10) UNSIGNED NULL,
    approved_by int(10) UNSIGNED NULL,
    deduction_contribution_id int(10) UNSIGNED NULL,
    monthly_payroll_id int(10) UNSIGNED NULL,
    created_at timestamp NULL,
    updated_at timestamp NULL,
    deleted_at timestamp NULL
);
```

### 4. `deduction_contributions`
```sql
CREATE TABLE deduction_contributions (
    id int(10) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name varchar(255) NOT NULL,
    code varchar(255) NULL,
    formula varchar(255) NULL,
    apply_mode varchar(255) NULL,
    apply_at varchar(255) DEFAULT 'BEFORE',
    description text NULL,
    created_by int(10) UNSIGNED NULL,
    updated_by int(10) UNSIGNED NULL,
    status_id int(10) UNSIGNED NULL,
    created_at timestamp NULL,
    updated_at timestamp NULL,
    deleted_at timestamp NULL
);
```

### 5. `employee_deduction_contributions`
```sql
CREATE TABLE employee_deduction_contributions (
    id int(10) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    start_date date NULL,
    end_date date NULL,
    start_month varchar(255) NULL,
    end_month varchar(255) NULL,
    number_of_installment int(10) UNSIGNED DEFAULT 1,
    employee_id int(10) UNSIGNED NULL,
    deduction_contribution_id int(10) UNSIGNED NULL,
    created_by int(10) UNSIGNED NULL,
    updated_by int(10) UNSIGNED NULL,
    created_at timestamp NULL,
    updated_at timestamp NULL,
    deleted_at timestamp NULL
);
```

## Model Relationships

### Employee Model
```php
// Relationship with monthly payrolls
public function monthlyPayrolls()
{
    return $this->hasMany(MonthlyPayroll::class);
}

// Relationship with employee deduction contributions
public function employeeDeductionContributions()
{
    return $this->hasMany(EmployeeDeductionContribution::class);
}
```

### MonthlyPayroll Model
```php
// Relationship with employee
public function employee()
{
    return $this->belongsTo(Employee::class);
}

// Relationship with payroll items
public function payrollItems()
{
    return $this->hasMany(PayrollItem::class);
}

// Relationship with contributions (filtered payroll items)
public function contributions()
{
    return $this->hasMany(PayrollItem::class)->where('type', 'CONTRIBUTION');
}

// Relationship with deductions (filtered payroll items)
public function deductions()
{
    return $this->hasMany(PayrollItem::class)->where('type', 'DEDUCTION');
}
```

### PayrollItem Model
```php
// Relationship with monthly payroll
public function monthlyPayroll()
{
    return $this->belongsTo(MonthlyPayroll::class);
}
```

### DeductionContribution Model
```php
// Relationship with employee deduction contributions
public function employeeDeductionContributions()
{
    return $this->hasMany(EmployeeDeductionContribution::class);
}
```

## Key Calculations

### Gross Pay Calculation
```php
public function getGrossAttribute() {
    return $this->basic_pay + $this->contributions()->sum("amount");
}
```

### Net Pay Calculation
```php
public function getNetPayAttribute($value) {
    return $this->gross -
        $this->payee -
        $this->deductions()->sum("amount") +
        $this->payrollItems()->whereNull("deduction_contribution_id")->sum("amount");
}
```

### PAYE Tax Calculation
```php
public function payeeTax($grossSalary) {
    // Define the tax brackets and rates
    $taxBrackets = [
        100000 => 0.0,   // 0% tax on the first K100,000
        350000 => 0.25,  // 25% tax on the next K350,000
        2050000 => 0.30, // 30% tax on the next K2,050,000
        2500000 => 0.35, // 35% tax on the next K2,500,000
    ];

    // Initialize the PAYE amount
    $paye = 0;

    // Calculate PAYE based on the tax brackets and rates
    foreach ($taxBrackets as $bracket => $rate) {
        if ($grossSalary <= 0) {
            break; // No more tax to calculate
        }

        if ($grossSalary > $bracket) {
            $taxableAmount = $bracket;
        } else {
            $taxableAmount = $grossSalary;
        }

        $taxAmount = $taxableAmount * $rate;
        $paye += $taxAmount;
        $grossSalary -= $taxableAmount;
    }
    return $paye;
}
```

## Payroll Generation Process

The payroll generation process is handled by the `EmployeeHandler` class:

```php
public function generatePayroll($data) {
    $employees = Employee::get();
    foreach($employees as $employee) {
        $month = _from($data, 'month');
        $monthlyPayroll = MonthlyPayroll::where("employee_id", $employee->id)
            ->whereYear("date_from", date("Y"))
            ->whereMonth("date_from", $month)
            ->first();
        $dateFrom = new \Carbon\Carbon(date('Y') . '-' . $month .'-01');
        $dateTo = $dateFrom->addDays(29);
        $params = [
            "name" => $employee->name,
            "date_from" => $dateFrom,
            "date_to" => $dateTo,
            "basic_pay" => $employee->basic_pay,
            "employee_id" => $employee->id,
        ];
        if(!$monthlyPayroll) {
            $monthlyPayroll = MonthlyPayroll::create($params);
            $this->calculatePay($monthlyPayroll);
        }
    }
}
```

## Payroll Calculation Process

The calculation of individual payroll items is handled by:

```php
public function calculatePay($monthlyPayroll) {
    $employee = $monthlyPayroll->employee;
    $employeeDeductionContributions = $employee->employeeDeductionContributions;
    $monthlyPayroll->payrollItems()->whereNotNull("deduction_contribution_id")->delete();
    foreach($employeeDeductionContributions as $employeeDeductionContribution) {
        $amount = $this->calculateFormula($employee, $employeeDeductionContribution->formula);
        $params = [
            "name" => $employeeDeductionContribution->name,
            "description" => $employeeDeductionContribution->name,
            "amount" => $amount,
            "type" => $employeeDeductionContribution->apply_mode,
            "formula" => $employeeDeductionContribution->format,
            "approved_by" => auth()->id(),
            "deduction_contribution_id" => $employeeDeductionContribution->deduction_contribution_id,
            "monthly_payroll_id" => $monthlyPayroll->id,
        ];
        $monthlyPayroll->payrollItems()->create($params);
    }
}
```

## Formula Calculation

Formulas for deductions and contributions are calculated using:

```php
public function calculateFormula($employee, $formula) {
    $formula = str_replace("BASIC_PAY", $employee->basic_pay, $formula);
    $formula = $this->calculateVat($employee, $formula);
    $amount = eval("return $formula;") ?? 0;
    return $amount;
}
```

## Payslip Generation

Payslips are generated using a Blade template that displays:
- Employee information
- Earnings (basic pay and contributions)
- Deductions (PAYE tax and other deductions)
- Gross and net pay calculations

The payslip can be printed or potentially emailed to employees.

## Integration with Journal Entries

Payroll transactions should be posted to the general ledger through journal entries. The system uses the `JournalEntry` and `JournalEntryLine` models to create balanced accounting entries.

## Future Enhancements

1. **Automated Journal Entry Creation**: Automatically create journal entries when payroll is processed
2. **Payroll Approval Workflow**: Implement multi-level approval for payroll processing
3. **Enhanced Reporting**: Develop additional reports for payroll analysis and compliance
4. **Bank Integration**: Direct deposit functionality for employee payments
5. **Historical Data Analysis**: Tools for analyzing payroll trends over time
