<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class BomPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create BOM permissions
        $permissions = [
            'view-any Bom',
            'view Bom',
            'create Bom',
            'update Bom',
            'delete Bom',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permissions to super-admin role
        $adminRole = Role::where('name', 'super-admin')->first();
        if ($adminRole) {
            $adminRole->givePermissionTo($permissions);
        }

        // Assign permissions to production-manager role
        $productionManagerRole = Role::firstOrCreate(['name' => 'production-manager']);
        $productionManagerRole->givePermissionTo([
            'view-any Bom',
            'view Bom',
            'create Bom',
            'update Bo<PERSON>',
        ]);

        // Assign permissions to accountant role
        $accountantRole = Role::where('name', 'accountant')->first();
        if ($accountantRole) {
            $accountantRole->givePermissionTo([
                'view-any Bom',
                'view Bom',
            ]);
        }
    }
}
