@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">
                <a href="{{ route('boms.index') }}" class="mr-4"
                    ><i class="icon ion-md-arrow-back"></i
                ></a>
                Edit Bill of Materials
            </h4>

            <x-form
                method="PUT"
                action="{{ route('boms.update', $bom) }}"
                class="mt-4"
            >
                @include('app.boms.form-inputs')

                <div class="mt-4">
                    <a
                        href="{{ route('boms.index') }}"
                        class="btn btn-light"
                    >
                        <i class="icon ion-md-return-left text-primary"></i>
                        @lang('crud.common.back')
                    </a>

                    <a
                        href="{{ route('boms.create') }}"
                        class="btn btn-light"
                    >
                        <i class="icon ion-md-add text-primary"></i>
                        @lang('crud.common.create')
                    </a>

                    <button type="submit" class="btn btn-primary float-right">
                        <i class="icon ion-md-save"></i>
                        @lang('crud.common.update')
                    </button>
                </div>
            </x-form>

            <hr class="mt-5">

            <div class="mt-4">
                <h5>BOM Items</h5>

                <div class="d-flex justify-content-between mb-3">
                    <span>Total Items: {{ $bom->bomItems->count() }}</span>
                    <a href="{{ route('boms.edit-items', $bom) }}" class="btn btn-primary btn-sm">
                        <i class="icon ion-md-create"></i> Edit Items
                    </a>
                </div>

                @if($bom->bomItems->count() > 0)
                <div class="table-responsive">
                    <table class="table table-borderless table-hover">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Quantity</th>
                                <th>Unit</th>
                                <th>Cost Per Unit</th>
                                <th>Total Cost</th>
                                <th>Type</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($bom->bomItems as $item)
                            <tr>
                                <td>{{ optional($item->product)->name ?? '-' }}</td>
                                <td>{{ number_format($item->quantity, 2) }}</td>
                                <td>{{ $item->unit_of_measure }}</td>
                                <td>{{ number_format($item->cost_per_unit, 2) }}</td>
                                <td>{{ number_format($item->total_cost, 2) }}</td>
                                <td>{{ ucfirst($item->item_type) }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="4" class="text-right">Total:</th>
                                <th>{{ number_format($bom->total_cost, 2) }}</th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                @else
                <div class="alert alert-info">
                    No items added to this BOM yet. Click "Edit Items" to add components.
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
