<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payroll_items', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name')->nullable();
            $table->decimal('amount', 20, 2)->default(0);
            $table->string('formula')->nullable();
            $table->string('type')->nullable();
            $table->unsignedInteger('is_calculatable')->default(1);
            $table->text('description')->nullable();
            $table->unsignedInteger('created_by')->nullable();
            $table->unsignedInteger('updated_by')->nullable();
            $table->unsignedInteger('approved_by')->nullable();
            $table->unsignedInteger('deduction_contribution_id')->nullable();
            $table->unsignedBigInteger('loan_id')->nullable();
            $table->unsignedInteger('monthly_payroll_id')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index('loan_id');
            $table->index('deduction_contribution_id');
            $table->index('monthly_payroll_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payroll_items');
    }
};
