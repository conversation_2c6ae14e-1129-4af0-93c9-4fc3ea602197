@extends('layouts.app')

@push('styles')
<style>
    .content {
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
    }

    .content.loaded {
        opacity: 1;
    }

    .transfer-card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.75rem;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .transfer-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .transfer-in {
        border-left: 4px solid #198754;
        background-color: rgba(25, 135, 84, 0.05);
    }

    .transfer-out {
        border-left: 4px solid #dc3545;
        background-color: rgba(220, 53, 69, 0.05);
    }

    .transfer-badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.5rem;
        font-weight: 500;
    }

    .transfer-direction {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .transfer-arrow {
        font-size: 1.25rem;
        color: #6c757d;
    }
</style>
@endpush

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">arrow-left-right</x-slot>
        <x-slot name="title">Stock Transfers</x-slot>
        <x-slot name="subtitle">Track stock movements between warehouses and branches</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('stocks.index') }}">Stock Inventory</a></li>
            <li class="breadcrumb-item active" aria-current="page">Stock Transfers</li>
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group">
                <a href="{{ route('stocks.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Stock
                </a>

                @php
                    // More flexible warehouse management check
                    $managedWarehouses = 0;
                    $managedWarehouseIds = collect();

                    if (auth()->user()->isSuperAdmin()) {
                        $managedWarehouses = \App\Models\Warehouse::count();
                        $managedWarehouseIds = \App\Models\Warehouse::pluck('id');
                    } else {
                        $managedWarehouses = \App\Models\Warehouse::where(function($query) {
                            $query->where('manager_id', auth()->id())
                                  ->orWhere('created_by', auth()->id())
                                  ->orWhere('branch_id', auth()->user()->branch_id);
                        })->count();

                        $managedWarehouseIds = \App\Models\Warehouse::where(function($query) {
                            $query->where('manager_id', auth()->id())
                                  ->orWhere('created_by', auth()->id())
                                  ->orWhere('branch_id', auth()->user()->branch_id);
                        })->pluck('id');
                    }

                @endphp

                @if($managedWarehouses > 0)
                <a href="{{ route('approvals.index', ['filter' => 'transfers']) }}" class="btn btn-warning btn-sm position-relative">
                    <i class="bi bi-clock-history me-1"></i>Pending Approvals
                </a>
                @endif

                @can('create', App\Models\Stock::class)
                <a href="{{ route('stocks.create-transfer') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>New Transfer
                </a>
                @endcan
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ route('stocks.transfers') }}" method="GET" class="row g-3 align-items-end">
                <!-- Search -->
                <div class="col-md-4">
                    <label class="form-label small text-muted mb-1">
                        <i class="bi bi-search me-1"></i>Search Products
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" name="search"
                               placeholder="Product name, description, barcode..."
                               value="{{ request('search') }}">
                    </div>
                </div>

                <!-- From Warehouse Filter -->
                <div class="col-md-3">
                    <label class="form-label small text-muted mb-1">
                        <i class="bi bi-box-arrow-up me-1"></i>Source Warehouse
                    </label>
                    <select class="form-select" name="from_warehouse">
                        <option value="">All Sources</option>
                        @foreach($filterOptions['warehouses'] as $warehouse)
                            <option value="{{ $warehouse->id }}" {{ request('from_warehouse') == $warehouse->id ? 'selected' : '' }}>
                                {{ $warehouse->name }}
                                @if($warehouse->branch)
                                    - {{ $warehouse->branch->name }}
                                @endif
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- To Warehouse Filter -->
                <div class="col-md-3">
                    <label class="form-label small text-muted mb-1">
                        <i class="bi bi-box-arrow-in-down me-1"></i>Destination Warehouse
                    </label>
                    <select class="form-select" name="to_warehouse">
                        <option value="">All Destinations</option>
                        @foreach($filterOptions['warehouses'] as $warehouse)
                            <option value="{{ $warehouse->id }}" {{ request('to_warehouse') == $warehouse->id ? 'selected' : '' }}>
                                {{ $warehouse->name }}
                                @if($warehouse->branch)
                                    - {{ $warehouse->branch->name }}
                                @endif
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Status Filter -->
                <div class="col-md-2">
                    <label class="form-label small text-muted mb-1">
                        <i class="bi bi-check-circle me-1"></i>Status
                    </label>
                    <select class="form-select" name="status">
                        <option value="">All Status</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>
                            🕐 Pending
                        </option>
                        <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>
                            ✅ Approved
                        </option>
                    </select>
                </div>

                <!-- Date From -->
                <div class="col-md-3">
                    <label class="form-label small text-muted mb-1">
                        <i class="bi bi-calendar-event me-1"></i>From
                    </label>
                    <input type="date" class="form-control" name="date_from" value="{{ request('date_from') }}">
                </div>

                <!-- Date To -->
                <div class="col-md-3">
                    <label class="form-label small text-muted mb-1">
                        <i class="bi bi-calendar-check me-1"></i>To
                    </label>
                    <input type="date" class="form-control" name="date_to" value="{{ request('date_to') }}">
                </div>

                <!-- Action Buttons -->
                <div class="col-md-3 d-flex gap-2 align-items-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-funnel me-1"></i>Apply
                    </button>
                    <a href="{{ route('stocks.transfers') }}" class="btn btn-outline-secondary" title="Clear all filters">
                        <i class="bi bi-arrow-clockwise"></i>
                    </a>
                    @if(request()->hasAny(['search', 'from_warehouse', 'to_warehouse', 'status', 'date_from', 'date_to']))
                        <span class="badge bg-info align-self-center">
                            <i class="bi bi-funnel-fill me-1"></i>Active
                        </span>
                    @endif
                </div>
            </form>
        </div>
    </div>
    <!-- End Filters -->



    <!-- Transfer History -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history me-2"></i>Transfer History
                    @if(request()->hasAny(['search', 'from_warehouse', 'to_warehouse', 'status', 'date_from', 'date_to']))
                        <span class="badge bg-info ms-2">Filtered</span>
                    @endif
                </h5>
                <div class="d-flex gap-2">
                    @if($transfers->total() > 0)
                        <button class="btn btn-outline-success btn-sm" onclick="exportTransfers()">
                            <i class="bi bi-download me-1"></i>Export
                        </button>
                    @endif
                </div>
            </div>
        </div>

        <div class="card-body">
            @forelse($transfers as $transfer)
            <div class="transfer-card {{ $transfer->quantity > 0 ? 'transfer-in' : 'transfer-out' }} p-3 mb-3">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                @if($transfer->quantity > 0)
                                    <div class="avatar avatar-xs avatar-soft-success avatar-circle">
                                        <span class="avatar-initials"><i class="bi bi-arrow-down"></i></span>
                                    </div>
                                @else
                                    <div class="avatar avatar-xs avatar-soft-danger avatar-circle">
                                        <span class="avatar-initials"><i class="bi bi-arrow-up"></i></span>
                                    </div>
                                @endif
                            </div>
                            <div>
                                <h6 class="mb-0">{{ $transfer->product->name ?? 'Unknown Product' }}</h6>
                                <small class="text-muted">{{ $transfer->created_at->format('M d, Y H:i') }}</small>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="transfer-direction">
                            <span class="fw-semibold">{{ $transfer->stock->warehouse->name ?? 'Unknown' }}</span>
                            <i class="bi bi-arrow-right transfer-arrow"></i>
                            <span class="fw-semibold">{{ $transfer->warehouse->name ?? 'Unknown' }}</span>
                        </div>
     
                    </div>

                    <div class="col-md-2 text-center">
                        <span class="fw-bold {{ $transfer->quantity > 0 ? 'text-success' : 'text-danger' }}">
                            {{ $transfer->quantity > 0 ? '+' : '' }}{{ number_format(abs($transfer->quantity)) }}
                        </span>
                        <br>
                        <span class="badge transfer-badge {{ $transfer->quantity > 0 ? 'bg-success' : 'bg-danger' }}">
                            {{ $transfer->quantity > 0 ? 'IN' : 'OUT' }}
                        </span>
                        @if($transfer->quantity > 0)
                            <br>
                            @if($transfer->approved_by)
                                <span class="badge bg-success mt-1">
                                    <i class="bi bi-check-circle me-1"></i>Approved
                                </span>
                            @else
                                <span class="badge bg-warning text-dark mt-1">
                                    <i class="bi bi-clock-history me-1"></i>Pending
                                </span>
                            @endif
                        @endif
                    </div>

                    <div class="col-md-3">
                        <small class="text-muted d-block">{{ $transfer->description }}</small>
                        <small class="text-muted">By: {{ $transfer->createdBy->name ?? 'System' }}</small>
                        @if($transfer->approved_by && $transfer->quantity > 0)
                            <br>
                            <small class="text-success">
                                <i class="bi bi-check-circle me-1"></i>
                                Approved by: {{ $transfer->approvedBy->name ?? 'Unknown' }}
                            </small>
                        @endif
                    </div>

                    <div class="col-md-1 text-end">
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button"
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a href="{{ route('stocks.show', $transfer) }}" class="dropdown-item">
                                        <i class="bi bi-eye me-2"></i>View Details
                                    </a>
                                </li>
                                @if($transfer->stock)
                                <li>
                                    <a href="{{ route('stocks.show', $transfer->stock) }}" class="dropdown-item">
                                        <i class="bi bi-box me-2"></i>View Original Stock
                                    </a>
                                </li>
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            @empty
            <div class="text-center py-5">
                <x-empty-state
                    icon="arrow-left-right"
                    title="No transfers found"
                    message="No stock transfers match your search criteria or no transfers have been made yet."
                />
                @can('create', App\Models\Stock::class)
                <a href="{{ route('stocks.create-transfer') }}" class="btn btn-primary mt-3">
                    <i class="bi bi-plus-circle me-1"></i>Create First Transfer
                </a>
                @endcan
            </div>
            @endforelse
        </div>

        <!-- Enhanced Pagination -->
        @if($transfers->hasPages() || $transfers->total() > 0)
        <div class="card-footer">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center gap-3">
                        <!-- Results Info -->
                        <span class="text-muted small">
                            Showing {{ $transfers->firstItem() ?? 0 }} to {{ $transfers->lastItem() ?? 0 }}
                            of {{ $transfers->total() }} transfers
                        </span>

                        <!-- Per Page Selector -->
                        <div class="d-flex align-items-center gap-2">
                            <label class="form-label small text-muted mb-0">Per page:</label>
                            <select class="form-select form-select-sm" style="width: auto;" onchange="changePerPage(this.value)">
                                <option value="10" {{ request('per_page',10) == 10 ? 'selected' : '' }}>10</option>
                                <option value="20" {{ request('per_page') == 20 ? 'selected' : '' }}>20</option>
                                <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                                <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100</option>
                            </select>
                        </div>

                        <!-- Quick Page Jump -->
                        @if($transfers->lastPage() > 5)
                        <div class="d-flex align-items-center gap-2">
                            <label class="form-label small text-muted mb-0">Go to page:</label>
                            <input type="number" class="form-control form-control-sm" style="width: 70px;"
                                   min="1" max="{{ $transfers->lastPage() }}"
                                   placeholder="{{ $transfers->currentPage() }}"
                                   onkeypress="if(event.key==='Enter') goToPage(this.value)">
                        </div>
                        @endif
                    </div>
                </div>

                <div class="col-md-6">
                    @if($transfers->hasPages())
                        <div class="d-flex justify-content-end">
                            {{ $transfers->onEachSide(10)->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
        @endif
    </div>
    <!-- End Transfer History -->




</div>
@endsection





@push('scripts')
<script>
$(document).ready(function() {
    // Show content after page loads to prevent flash
    $('.content').addClass('loaded');

    // Auto-refresh every 30 seconds for real-time updates
    setInterval(function() {
        if (!$('.dropdown-menu:visible').length) {
            // Only refresh if no dropdowns are open
            // location.reload();
        }
    }, 30000);
});

// Function to change per page value
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.set('page', 1); // Reset to first page
    window.location.href = url.toString();
}

// Function to go to specific page
function goToPage(page) {
    const url = new URL(window.location);
    url.searchParams.set('page', page);
    window.location.href = url.toString();
}

// Function to export transfers
function exportTransfers() {
    const url = new URL('{{ route("stocks.transfers.export") }}');
    // Copy current filters
    const currentParams = new URLSearchParams(window.location.search);
    currentParams.forEach((value, key) => {
        if (key !== 'page' && key !== 'per_page') { // Exclude pagination parameters for export
            url.searchParams.set(key, value);
        }
    });
    window.open(url.toString(), '_blank');
}
</script>
@endpush

