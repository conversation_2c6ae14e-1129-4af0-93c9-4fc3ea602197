<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProductUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', 'max:255', 'string'],
            'selling_price' => ['nullable', 'numeric'],
            'buying_price' => ['nullable', 'numeric'],
            'unit_id' => ['nullable', 'tenant_exists:units,id'],
            'status_id' => ['nullable', 'tenant_exists:statuses,id'],
            'discount' => ['nullable', 'numeric'],
            'category_id' => ['nullable', 'tenant_exists:categories,id'],
            'barcode' => ['nullable'],
            'description' => ['nullable'],
            'sell_type' => ['nullable'],
            'vat_applied' => ['nullable'],
        ];
    }
}
