@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">folder</x-slot>
        <x-slot name="title">{{ $accountCategory->name }}</x-slot>
        <x-slot name="subtitle">Account Category Details</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Accounting</a></li>
            <li class="breadcrumb-item"><a href="{{ route('account-categories.index') }}">Account Categories</a></li>
            <li class="breadcrumb-item active" aria-current="page">View</li>
        </x-slot>
        <x-slot name="controls">
            @can('update', $accountCategory)
            <a href="{{ route('account-categories.edit', $accountCategory) }}" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> Edit Category
            </a>
            @endcan
            <a href="{{ route('account-categories.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Categories
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="row">
        <!-- Category Overview Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-info-circle me-2"></i>Category Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        @php
                            $accountType = $accountCategory->accountType;
                            $color = 'primary';
                            if ($accountType) {
                                $classColors = [
                                    'asset' => 'success',
                                    'liability' => 'danger',
                                    'equity' => 'info',
                                    'revenue' => 'primary',
                                    'expense' => 'warning'
                                ];
                                $color = $classColors[$accountType->classification] ?? 'primary';
                            }
                        @endphp
                        <div class="avatar avatar-xl avatar-circle mb-3 mx-auto bg-soft-{{ $color }}">
                            <span class="avatar-initials text-{{ $color }}">{{ substr($accountCategory->name, 0, 1) }}</span>
                        </div>
                        <h4 class="mb-1">{{ $accountCategory->name }}</h4>
                        <p class="text-muted">
                            <span class="badge bg-soft-{{ $color }} text-{{ $color }}">
                                {{ optional($accountCategory->accountType)->name ?? 'No Account Type' }}
                            </span>
                        </p>
                    </div>

                    <hr>

                    <div class="mb-4">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Code:</span>
                            <span class="badge bg-soft-primary">{{ $accountCategory->code ?? 'N/A' }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Status:</span>
                            <span class="badge bg-{{ $accountCategory->is_active ? 'success' : 'danger' }}">
                                {{ $accountCategory->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                        @if(isset($accountCategory->is_system))
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Type:</span>
                            <span class="badge bg-{{ $accountCategory->is_system ? 'info' : 'secondary' }}">
                                {{ $accountCategory->is_system ? 'System' : 'User-defined' }}
                            </span>
                        </div>
                        @endif
                    </div>

                    <hr>

                    <h6 class="mb-3">Description</h6>
                    <p class="mb-0">{{ $accountCategory->description ?? 'No description available.' }}</p>
                </div>
            </div>
        </div>

        <!-- Category Statistics Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-graph-up me-2"></i>Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="card card-dashed h-100">
                                <div class="card-body d-flex justify-content-center flex-column text-center">
                                    <h2 class="mb-1">{{ $accountCategory->accounts()->count() }}</h2>
                                    <span class="text-muted">Accounts</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3">System Information</h6>
                    <div class="d-flex justify-content-between mb-1">
                        <span class="text-muted">Created:</span>
                        <span>{{ $accountCategory->created_at->format('M d, Y h:i A') }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span class="text-muted">Last Updated:</span>
                        <span>{{ $accountCategory->updated_at->format('M d, Y h:i A') }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Information Card -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-header-title">
                        <i class="bi bi-link-45deg me-2"></i>Related Information
                    </h5>
                </div>
                <div class="card-body">
                    <h6 class="mb-3">Account Type</h6>
                    @if($accountCategory->accountType)
                    <div class="d-flex align-items-center mb-4">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-xs avatar-circle bg-soft-{{ $color }}">
                                <span class="avatar-initials text-{{ $color }}">{{ substr($accountCategory->accountType->name, 0, 1) }}</span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">{{ $accountCategory->accountType->name }}</h6>
                            <small class="text-muted">{{ ucfirst($accountCategory->accountType->classification) }}</small>
                        </div>
                        <div>
                            <a href="{{ route('account-types.show', $accountCategory->accountType) }}" class="btn btn-soft-primary btn-xs">
                                <i class="bi bi-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                    @else
                    <p class="text-muted mb-4">No account type associated with this category.</p>
                    @endif

                    <h6 class="mb-3">Financial Statement</h6>
                    <p class="mb-4">
                        @if($accountCategory->accountType)
                        @php
                            $statements = [
                                'asset' => 'Balance Sheet (Assets)',
                                'liability' => 'Balance Sheet (Liabilities)',
                                'equity' => 'Balance Sheet (Equity)',
                                'revenue' => 'Income Statement (Revenue)',
                                'expense' => 'Income Statement (Expenses)'
                            ];
                        @endphp
                        This category appears on the <strong>{{ $statements[$accountCategory->accountType->classification] ?? 'Unknown' }}</strong>.
                        @else
                        No financial statement information available.
                        @endif
                    </p>

                    <div class="mt-4">
                        <a href="#accounts" class="btn btn-soft-info btn-sm mb-2">
                            <i class="bi bi-journal-bookmark me-1"></i> View Accounts
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @can('view-any', App\Models\Account::class)
    <div class="card mb-4" id="accounts">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">
                        <i class="bi bi-journal-bookmark me-2"></i>Accounts
                    </h4>
                </div>
                <div class="col-auto">
                    @can('create', App\Models\Account::class)
                    <a href="{{ route('accounts.create') }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-plus-circle me-1"></i> Add Account
                    </a>
                    @endcan
                </div>
            </div>
        </div>
        <div class="card-body">
            <livewire:account-category-accounts-detail :accountCategory="$accountCategory" />
        </div>
    </div>
    @endcan

    <!-- Action Buttons -->
    <div class="d-flex justify-content-between mt-2 mb-4">
        <div>
            <a href="{{ route('account-categories.index') }}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left me-1"></i> Back to Categories
            </a>

            @can('create', App\Models\AccountCategory::class)
            <a href="{{ route('account-categories.create') }}" class="btn btn-outline-primary">
                <i class="bi bi-plus-circle me-1"></i> Create New Category
            </a>
            @endcan
        </div>

        <div>
            @can('update', $accountCategory)
            <a href="{{ route('account-categories.edit', $accountCategory) }}" class="btn btn-primary me-2">
                <i class="bi bi-pencil-square me-1"></i> Edit
            </a>
            @endcan
            
            @can('delete', $accountCategory)
            <form action="{{ route('account-categories.destroy', $accountCategory) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this account category?')">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-danger" {{ isset($accountCategory->is_system) && $accountCategory->is_system ? 'disabled' : '' }}>
                    <i class="bi bi-trash me-1"></i> Delete
                </button>
            </form>
            @endcan
        </div>
    </div>
</div>
@endsection
