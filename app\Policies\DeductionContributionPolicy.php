<?php

namespace App\Policies;

use App\Models\User;
use App\Models\DeductionContribution;
use Illuminate\Auth\Access\HandlesAuthorization;

class DeductionContributionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo('list deduction-contributions');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\DeductionContribution  $deductionContribution
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, DeductionContribution $deductionContribution)
    {
        return $user->hasPermissionTo('view deduction-contributions');
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo('create deduction-contributions');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\DeductionContribution  $deductionContribution
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, DeductionContribution $deductionContribution)
    {
        return $user->hasPermissionTo('update deduction-contributions');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\DeductionContribution  $deductionContribution
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, DeductionContribution $deductionContribution)
    {
        return $user->hasPermissionTo('delete deduction-contributions');
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\DeductionContribution  $deductionContribution
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, DeductionContribution $deductionContribution)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\DeductionContribution  $deductionContribution
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, DeductionContribution $deductionContribution)
    {
        return false;
    }
}
