<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Stock;
use App\Models\Status;

use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class StatusStocksTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create(['email' => '<EMAIL>']);

        Sanctum::actingAs($user, [], 'web');

        $this->seed(\Database\Seeders\PermissionsSeeder::class);

        $this->withoutExceptionHandling();
    }

    /**
     * @test
     */
    public function it_gets_status_stocks()
    {
        $status = Status::factory()->create();
        $stocks = Stock::factory()
            ->count(2)
            ->create([
                'status_id' => $status->id,
            ]);

        $response = $this->getJson(route('api.statuses.stocks.index', $status));

        $response->assertOk()->assertSee($stocks[0]->description);
    }

    /**
     * @test
     */
    public function it_stores_the_status_stocks()
    {
        $status = Status::factory()->create();
        $data = Stock::factory()
            ->make([
                'status_id' => $status->id,
            ])
            ->toArray();

        $response = $this->postJson(
            route('api.statuses.stocks.store', $status),
            $data
        );

        unset($data['created_by']);
        unset($data['updated_by']);
        unset($data['status_id']);

        $this->assertDatabaseHas('stocks', $data);

        $response->assertStatus(201)->assertJsonFragment($data);

        $stock = Stock::latest('id')->first();

        $this->assertEquals($status->id, $stock->status_id);
    }
}
