@php $editing = isset($businessType) @endphp

<div class="row">
    <!-- Business Type Information -->
    <div class="col-sm-12 mb-4">
        <h5>@lang('crud.business_types.business_type_information')</h5>
        <hr>
    </div>

    <!-- Basic Details -->
    <div class="col-sm-12 mb-4">
        <div class="row">
            <div class="col-sm-12 col-lg-6 mb-4">
                <label for="name" class="form-label">@lang('crud.business_types.inputs.name') <i class="bi-asterisk text-danger small"></i></label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-building"></i></span>
                    <input type="text" class="form-control form-control-lg @error('name') is-invalid @enderror"
                        name="name" id="name" placeholder="Enter business type name"
                        value="{{ old('name', ($editing ? $businessType->name : '')) }}"
                        required maxlength="255">
                </div>
                @error('name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <small class="form-text text-muted">Choose a descriptive name for this business type</small>
            </div>

            <div class="col-sm-12 col-lg-6 mb-4">
                <label for="status_id" class="form-label">@lang('crud.business_types.inputs.status_id')</label>
                <div class="tom-select-custom">
                    <select class="js-select form-select @error('status_id') is-invalid @enderror"
                        name="status_id" id="status_id"
                        data-hs-tom-select-options='{
                            "placeholder": "Select a status..."
                        }'>
                        @php $selected = old('status_id', ($editing ? $businessType->status_id : '')) @endphp
                        <option value="" disabled {{ empty($selected) ? 'selected' : '' }}>Please select the Status</option>
                        @foreach($statuses as $value => $label)
                        <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }}>{{ $label }}</option>
                        @endforeach
                    </select>
                </div>
                @error('status_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <small class="form-text text-muted">Set the status to control availability</small>
            </div>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="col-sm-12 mb-4">
        <h5>@lang('crud.business_types.additional_information')</h5>
        <hr>
    </div>

    <div class="col-sm-12">
        <label for="description" class="form-label">@lang('crud.business_types.inputs.description')</label>
        <textarea class="form-control @error('description') is-invalid @enderror"
            name="description" id="description" rows="4"
            placeholder="Enter a detailed description of this business type">{{ old('description', ($editing ? $businessType->description : '')) }}</textarea>
        @error('description')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
        <small class="form-text text-muted">Provide a clear description of what this business type represents and how it will be used</small>
    </div>
</div>
