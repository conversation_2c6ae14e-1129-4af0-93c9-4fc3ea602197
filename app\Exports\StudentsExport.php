<?php

namespace App\Exports;

use App\Models\Students;
use App\Models\Schools;
use App\Models\SubSchools;
use App\Models\YearGroup;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Support\Collection;

use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class StudentsExport implements FromCollection, WithHeadings
{
    use Exportable;
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {

        $subSchools = SubSchools::get();
        $subSchool = SubSchools::where('id', request()->sub_school)->first();
        $yearGroups = ($subSchool) ? $subSchool->yearGroups : [] ;
        $yearGroup = YearGroup::where('id', request()->year_group)->first();
        if($yearGroup){ 
            $allStudents = $yearGroup->students()->search(request()->search);
        } else{
            if( $subSchool )
                $allStudents = Students::with('yearGroups')->whereHas('yearGroups', function($q) use ($subSchool) {
                    $q->whereIn('year_groups.id', $subSchool->yearGroups->pluck('id')->toArray());
                });
            else{
                $allStudents = Students::where('id', false);
            }
        }
        $data =$allStudents->select([
            'students.firstname',
            'students.lastname',
            'students.date_of_birth',
            'students.gender',
            'students.next_of_kin',
            'students.created_at',
        ])->get()->makeHidden('fullname');
        return $data;
    }    

    public function headings(): array
    {
        return ['Firstname', 'Lastname', 'Date of Birth', 'Gender', 'Next of Kin', 'Created Date'];
    }
}
