@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <x-page-header title="New Loan Application" />

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Loan Application Form</h5>
                </div>

                <div class="card-body">
                    <form method="POST" action="{{ route('employee-loans.store') }}">
                        @csrf

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="employee_id">Employee *</label>
                                    <select name="employee_id" id="employee_id" class="form-control @error('employee_id') is-invalid @enderror" required>
                                        <option value="">Select Employee</option>
                                        @foreach($employees as $employee)
                                            <option value="{{ $employee->id }}" {{ old('employee_id') == $employee->id ? 'selected' : '' }}>
                                                {{ $employee->name }} - {{ $employee->email }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('employee_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="loan_date">Loan Date *</label>
                                    <input type="date" name="loan_date" id="loan_date"
                                           class="form-control @error('loan_date') is-invalid @enderror"
                                           value="{{ old('loan_date', date('Y-m-d')) }}" required>
                                    @error('loan_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="loan_amount">Loan Amount *</label>
                                    <input type="number" name="loan_amount" id="loan_amount"
                                           class="form-control @error('loan_amount') is-invalid @enderror"
                                           value="{{ old('loan_amount') }}" step="0.01" min="1" required>
                                    @error('loan_amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="installment_amount">Monthly Principal Payment *</label>
                                    <input type="number" name="installment_amount" id="installment_amount"
                                           class="form-control @error('installment_amount') is-invalid @enderror"
                                           value="{{ old('installment_amount') }}" step="0.01" min="1" required>
                                    @error('installment_amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        Principal amount to be deducted monthly. Interest will be calculated and deducted separately.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="first_deduction_date">First Deduction Date *</label>
                                    <input type="date" name="first_deduction_date" id="first_deduction_date"
                                           class="form-control @error('first_deduction_date') is-invalid @enderror"
                                           value="{{ old('first_deduction_date') }}" required>
                                    @error('first_deduction_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        The date when the first installment will be deducted from payroll.
                                    </small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="estimated_installments">Estimated Installments</label>
                                    <input type="number" id="estimated_installments" class="form-control" readonly>
                                    <small class="form-text text-muted">
                                        Calculated automatically based on loan amount and installment.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="interest_rate">Interest Rate (%)</label>
                                    <input type="number" name="interest_rate" id="interest_rate"
                                           class="form-control @error('interest_rate') is-invalid @enderror"
                                           value="{{ old('interest_rate', 0) }}" step="0.01" min="0" max="100">
                                    @error('interest_rate')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="interest_type">Interest Type</label>
                                    <select name="interest_type" id="interest_type" class="form-control @error('interest_type') is-invalid @enderror">
                                        <option value="simple" {{ old('interest_type', 'simple') == 'simple' ? 'selected' : '' }}>Simple Interest</option>
                                        <option value="compound" {{ old('interest_type') == 'compound' ? 'selected' : '' }}>Compound Interest</option>
                                    </select>
                                    @error('interest_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="purpose">Loan Purpose *</label>
                            <textarea name="purpose" id="purpose"
                                      class="form-control @error('purpose') is-invalid @enderror"
                                      rows="3" required>{{ old('purpose') }}</textarea>
                            @error('purpose')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="terms_conditions">Terms and Conditions</label>
                            <textarea name="terms_conditions" id="terms_conditions"
                                      class="form-control @error('terms_conditions') is-invalid @enderror"
                                      rows="4">{{ old('terms_conditions') }}</textarea>
                            @error('terms_conditions')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Submit Loan Application
                            </button>
                            <a href="{{ route('employee-loans.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Loans
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Loan Calculator</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6>Loan Summary</h6>
                        <p class="mb-1"><strong>Loan Amount:</strong> <span id="calc_loan_amount">0.00</span></p>
                        <p class="mb-1"><strong>Monthly Principal Payment:</strong> <span id="calc_installment">0.00</span></p>
                        <p class="mb-1"><strong>Number of Installments:</strong> <span id="calc_installments">0</span></p>
                        <p class="mb-0"><strong>Expected Completion:</strong> <span id="calc_completion">-</span></p>
                    </div>

                    <div class="alert alert-warning">
                        <h6>Important Notes</h6>
                        <ul class="mb-0">
                            <li>Loan deductions will start from the specified first deduction date</li>
                            <li>Principal and interest are deducted separately during payroll generation</li>
                            <li>Monthly principal payment reduces the loan balance</li>
                            <li>Interest is calculated monthly and deducted as a separate item</li>
                            <li>The final installment will be adjusted if the remaining balance is less than the principal payment</li>
                            <li>Employee can only have one active loan at a time</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loanAmountInput = document.getElementById('loan_amount');
    const installmentInput = document.getElementById('installment_amount');
    const firstDeductionInput = document.getElementById('first_deduction_date');
    const estimatedInstallmentsInput = document.getElementById('estimated_installments');

    function calculateLoan() {
        const loanAmount = parseFloat(loanAmountInput.value) || 0;
        const installmentAmount = parseFloat(installmentInput.value) || 0;
        const firstDeductionDate = firstDeductionInput.value;

        // Update calculator display
        document.getElementById('calc_loan_amount').textContent = loanAmount.toLocaleString('en-US', {minimumFractionDigits: 2});
        document.getElementById('calc_installment').textContent = installmentAmount.toLocaleString('en-US', {minimumFractionDigits: 2});

        if (loanAmount > 0 && installmentAmount > 0) {
            const installments = Math.ceil(loanAmount / installmentAmount);
            estimatedInstallmentsInput.value = installments;
            document.getElementById('calc_installments').textContent = installments;

            // Calculate expected completion date
            if (firstDeductionDate) {
                const startDate = new Date(firstDeductionDate);
                const completionDate = new Date(startDate);
                completionDate.setMonth(completionDate.getMonth() + installments - 1);
                document.getElementById('calc_completion').textContent = completionDate.toLocaleDateString();
            }
        } else {
            estimatedInstallmentsInput.value = '';
            document.getElementById('calc_installments').textContent = '0';
            document.getElementById('calc_completion').textContent = '-';
        }
    }

    // Set minimum date for first deduction (next month)
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    nextMonth.setDate(1);
    firstDeductionInput.min = nextMonth.toISOString().split('T')[0];

    // Set default first deduction date
    if (!firstDeductionInput.value) {
        firstDeductionInput.value = nextMonth.toISOString().split('T')[0];
    }

    // Add event listeners
    loanAmountInput.addEventListener('input', calculateLoan);
    installmentInput.addEventListener('input', calculateLoan);
    firstDeductionInput.addEventListener('change', calculateLoan);

    // Initial calculation
    calculateLoan();
});
</script>
@endpush
@endsection
