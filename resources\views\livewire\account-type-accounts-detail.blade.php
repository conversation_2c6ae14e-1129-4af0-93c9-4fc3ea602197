<div>
    <div class="mb-4">
        @can('create', App\Models\Account::class)
        <button class="btn btn-primary" wire:click="newAccount">
            <i class="icon ion-md-add"></i>
            @lang('crud.common.new')
        </button>
        @endcan
    </div>

    <x-modal wire:model="showingModal">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ $modalTitle }}</h5>
                <button
                    type="button"
                    class="close"
                    data-dismiss="modal"
                    aria-label="Close"
                >
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="modal-body">
                <div>
                    <x-inputs.group class="col-sm-12">
                        <x-inputs.select
                            name="accountCategoryId"
                            label="Account Category"
                            wire:model="accountCategoryId"
                        >
                            <option value="" disabled>Please select</option>
                            @foreach($accountCategories as $id => $name)
                            <option value="{{ $id }}">{{ $name }}</option>
                            @endforeach
                        </x-inputs.select>
                    </x-inputs.group>

                    <x-inputs.group class="col-sm-12">
                        <x-inputs.text
                            name="accountName"
                            label="Name"
                            wire:model="accountName"
                            maxlength="255"
                            placeholder="Name"
                        ></x-inputs.text>
                    </x-inputs.group>

                    <x-inputs.group class="col-sm-12">
                        <x-inputs.text
                            name="accountCode"
                            label="Code"
                            wire:model="accountCode"
                            maxlength="255"
                            placeholder="Code"
                        ></x-inputs.text>
                    </x-inputs.group>

                    <x-inputs.group class="col-sm-12">
                        <x-inputs.textarea
                            name="accountDescription"
                            label="Description"
                            wire:model="accountDescription"
                            maxlength="255"
                        ></x-inputs.textarea>
                    </x-inputs.group>

                    <x-inputs.group class="col-sm-12">
                        <x-inputs.select
                            name="accountNormalBalance"
                            label="Normal Balance"
                            wire:model="accountNormalBalance"
                        >
                            <option value="debit">Debit</option>
                            <option value="credit">Credit</option>
                        </x-inputs.select>
                    </x-inputs.group>

                    <x-inputs.group class="col-sm-12">
                        <x-inputs.checkbox
                            id="accountIsActive"
                            name="accountIsActive"
                            label="Active"
                            wire:model="accountIsActive"
                        ></x-inputs.checkbox>
                    </x-inputs.group>

                    <x-inputs.group class="col-sm-12">
                        <x-inputs.checkbox
                            id="accountAllowsManualEntries"
                            name="accountAllowsManualEntries"
                            label="Allows Manual Entries"
                            wire:model="accountAllowsManualEntries"
                        ></x-inputs.checkbox>
                    </x-inputs.group>
                </div>
            </div>

            <div class="modal-footer">
                <button
                    type="button"
                    class="btn btn-light float-left"
                    wire:click="$toggle('showingModal')"
                >
                    <i class="icon ion-md-close"></i>
                    @lang('crud.common.cancel')
                </button>

                <button type="button" class="btn btn-primary" wire:click="createAccount">
                    <i class="icon ion-md-save"></i>
                    @lang('crud.common.create')
                </button>
            </div>
        </div>
    </x-modal>

    <div class="table-responsive">
        <table class="table table-borderless table-hover">
            <thead>
                <tr>
                    <th class="text-left">
                        @lang('crud.accounts.inputs.code')
                    </th>
                    <th class="text-left">
                        @lang('crud.accounts.inputs.name')
                    </th>
                    <th class="text-left">
                        @lang('crud.accounts.inputs.account_category_id')
                    </th>
                    <th class="text-center">
                        @lang('crud.accounts.inputs.normal_balance')
                    </th>
                    <th class="text-center">
                        @lang('crud.accounts.inputs.is_active')
                    </th>
                    <th></th>
                </tr>
            </thead>
            <tbody class="text-gray-600">
                @forelse($accounts as $account)
                <tr class="hover:bg-gray-100">
                    <td class="text-left">
                        {{ $account->code ?? '-' }}
                    </td>
                    <td class="text-left">
                        {{ $account->name ?? '-' }}
                    </td>
                    <td class="text-left">
                        {{ optional($account->accountCategory)->name ?? '-' }}
                    </td>
                    <td class="text-center">
                        {{ ucfirst($account->normal_balance) ?? '-' }}
                    </td>
                    <td class="text-center">
                        <span class="badge {{ $account->is_active ? 'badge-success' : 'badge-danger' }}">
                            {{ $account->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </td>
                    <td class="text-right" style="width: 134px;">
                        <div
                            role="group"
                            aria-label="Row Actions"
                            class="relative inline-flex align-middle"
                        >
                            @can('update', $account)
                            <a
                                href="{{ route('accounts.edit', $account) }}"
                                class="mr-1"
                            >
                                <button
                                    type="button"
                                    class="btn btn-light"
                                >
                                    <i class="icon ion-md-create"></i>
                                </button>
                            </a>
                            @endcan @can('view', $account)
                            <a
                                href="{{ route('accounts.show', $account) }}"
                                class="mr-1"
                            >
                                <button
                                    type="button"
                                    class="btn btn-light"
                                >
                                    <i class="icon ion-md-eye"></i>
                                </button>
                            </a>
                            @endcan
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="6">
                        @lang('crud.common.no_items_found')
                    </td>
                </tr>
                @endforelse
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="6">
                        <div class="mt-10 px-4">
                            {{ $accounts->render() }}
                        </div>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>
