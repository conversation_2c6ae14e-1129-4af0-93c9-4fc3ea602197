<?php

namespace Database\Seeders;

use App\Models\Location;
use App\Models\Status;
use App\Models\BusinessType;
use Illuminate\Database\Seeder;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get the first business type and active status
        $businessType = BusinessType::first();
        $activeStatus = Status::where('id', 1)->first();

        if (!$businessType || !$activeStatus) {
            $this->command->warn('No business type or active status found. Please run BusinessTypeSeeder and StatusSeeder first.');
            return;
        }

        $locations = [
            [
                'name' => 'Main Office',
                'code' => 'LOC001',
                'description' => 'Primary business location and headquarters',
                'status_id' => $activeStatus->id,
                'business_type_id' => $businessType->id,
                'created_by' => 1,
                'updated_by' => 1,
            ],
            [
                'name' => 'Warehouse District',
                'code' => 'LOC002',
                'description' => 'Main storage and distribution center',
                'status_id' => $activeStatus->id,
                'business_type_id' => $businessType->id,
                'created_by' => 1,
                'updated_by' => 1,
            ],
            [
                'name' => 'Retail Store Front',
                'code' => 'LOC003',
                'description' => 'Customer-facing retail location',
                'status_id' => $activeStatus->id,
                'business_type_id' => $businessType->id,
                'created_by' => 1,
                'updated_by' => 1,
            ],
            [
                'name' => 'Manufacturing Plant',
                'code' => 'LOC004',
                'description' => 'Production and manufacturing facility',
                'status_id' => $activeStatus->id,
                'business_type_id' => $businessType->id,
                'created_by' => 1,
                'updated_by' => 1,
            ],
            [
                'name' => 'Remote Office',
                'code' => 'LOC005',
                'description' => 'Secondary office location for remote operations',
                'status_id' => $activeStatus->id,
                'business_type_id' => $businessType->id,
                'created_by' => 1,
                'updated_by' => 1,
            ],
        ];

        foreach ($locations as $locationData) {
            Location::create($locationData);
        }

        $this->command->info('Locations seeded successfully!');
    }
}
