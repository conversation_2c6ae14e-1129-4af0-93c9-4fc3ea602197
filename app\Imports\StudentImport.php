<?php

namespace App\Imports;

use App\Models\Student;
use App\Models\StudentLevel;
use App\Models\Level;
use App\Models\Sponsor;
use App\Models\Program;
use Maatwebsite\Excel\Concerns\ToModel;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithMappedCells;

use Facades\App\Libraries\ProgramHandler;


class StudentImport implements ToCollection
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    // public function mapping(): array
    // {
    //     return [
    //         'name'  => 'B1',
    //         'email' => 'B2',
    //     ];
    // }

    public $level_id = 24;

    public function getName($str, $key){
        $arr = _explode($str, " ");
        return _from($arr, $key);
    }


    public function getDate($value)
    {
        try{
            $value = trim($value);
            $date = \Carbon\Carbon::parse($value);
            return ($value) ? $date->format('Y-m-d') : null;            
        }catch(\Exception $ex){
            return null;
        }
    }

    public function sponsor($name){
        if(trim($name) =="Self Sponsored") return null;
        $sponsor = Sponsor::where("name", $name)->first();
        if($sponsor){
            return $sponsor;
        }else{
            return Sponsor::create([
                "name" => $name,
                "phone" => "**** *** ***",
                "phone" => "****@mail.com",
            ]);
        }
    }

    public function level(){
        return Level::where("id", $this->level_id)->first();
    }

    public function program(){
        return $this->level()->program ?? null;
    }

    public function academicPeriod(){
        return $this->program()->academicPeriodLast()->id ?? null;
    }

    public function collection(Collection $rows)
    {
         // Validator::make($rows->toArray(), [
         //     '4' => 'required',
         // ])->validate();


        foreach ($rows as $row) {
            $firstname = $this->getName(trim($row[1]), 0);
            $lastname = str_replace($firstname, "", $row[1]);

            $student = Student::where([
                "firstname" => $firstname,
                "lastname" => $lastname,
            ])->first();
            if( ! $student && $firstname && $lastname){

                $param = [
                    "firstname" => $firstname,
                    "lastname" => $lastname,
                    "gender" => ($row[2] == "M") ? "Male" : "Female",
                    "phone" => $row[3],
                    "date_of_birth" => $this->getDate($row[4]),
                    "current_address" => $row[5],
                    "boarding" => ($row[6] == "B") ? 1 : 0,
                    "sponsor" => $row[7],
                    "total" => $row[8],
                    "paid" => $row[9],
                    "receipt_number" => $row[10],
                    "balance" => $row[11],
                    "email" => $row[12],
                    "comment" => $row[13],
                    "level_id" => $this->level_id,
                ];

                $student = Student::create($param);

                if($student->level) {
                    ProgramHandler::addStudentsToLevel(
                        $student->level, 
                        [$student->id],
                        $this->academicPeriod(),
                        _from($param, "boarding")
                    );
                    $sponsor = $this->sponsor(_from($param, "sponsor"));
                    if($sponsor) {
                        StudentLevel::where("student_id", $student->id)
                            ->where("level_id", $student->level_id)
                            ->first()
                            ->sponsors()
                            ->sync($sponsor->id);
                    }
                }

            }
        }

    }



    // public function model(array $row)
    // {
    //     $firstname = $this->getName($row[1], 0)
    //     $lastname = $this->getName($row[1], 1)
    //     $student = Student::where([
    //         "firstname" => $firstname,
    //         "lastname" => $lastname,
    //     ])->first();
    //     if( ! $student && $firstname && $lastname){
    //         $param = [
    //             "firstname" => $firstname,
    //             "lastname" => $lastname,
    //             "gender" => ($row[1] == "M") ? "Male" : "Female",
    //             "phone" => $row[3],
    //             "date_of_birth" => ($row[4]) ? $row[4] : null,
    //             "current_address" => $row[5],
    //             "boarding" => ($row[6] == "B") ? 1 : 0,
    //             "sponsor" => $row[7],
    //             "total" => $row[8],
    //             "paid" => $row[9],
    //             "receipt_number" => $row[10],
    //             "balance" => $row[11],
    //             "email" => $row[12],
    //             "comment" => $row[13],
    //         ];

    //         // dd($param);
    //         return new Student($param);
    //     }
    // }


}
