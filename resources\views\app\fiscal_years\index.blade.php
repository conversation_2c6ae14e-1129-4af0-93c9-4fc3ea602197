@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">
            <i class="bi bi-calendar-range me-2"></i>Fiscal Years
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group" role="group">
                @can('create', App\Models\FiscalYear::class)
                <a href="{{ route('fiscal-years.create') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>Add Fiscal Year
                </a>
                @endcan
                <a href="{{ route('fiscal-periods.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-calendar3 me-1"></i>Fiscal Periods
                </a>
                <button type="button" class="btn btn-outline-info btn-sm" onclick="window.print()">
                    <i class="bi bi-printer me-1"></i>Print
                </button>
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Filters Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>Filters & Search
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('fiscal-years.index') }}">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">Search</label>
                        <input type="text" name="search" class="form-control"
                               placeholder="Search fiscal years..." value="{{ $search ?? '' }}">
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="open" {{ request('status') == 'open' ? 'selected' : '' }}>Open</option>
                            <option value="closed" {{ request('status') == 'closed' ? 'selected' : '' }}>Closed</option>
                            <option value="locked" {{ request('status') == 'locked' ? 'selected' : '' }}>Locked</option>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">Active</label>
                        <select name="is_active" class="form-select">
                            <option value="">All</option>
                            <option value="1" {{ request('is_active') == '1' ? 'selected' : '' }}>Active</option>
                            <option value="0" {{ request('is_active') == '0' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>

                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>Filter
                        </button>
                        <a href="{{ route('fiscal-years.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Fiscal Years Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>Fiscal Years
            </h5>
            <span class="badge bg-secondary">{{ $fiscalYears->total() }} years</span>
        </div>

        <div class="table-responsive">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Fiscal Year</th>
                        <th>Period</th>
                        <th>Duration</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Active</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($fiscalYears as $fiscalYear)
                    <tr>
                        <td>
                            <div>
                                <strong>
                                    <a href="{{ route('fiscal-years.show', $fiscalYear) }}" class="text-decoration-none">
                                        {{ $fiscalYear->name }}
                                    </a>
                                </strong>
                                @if($fiscalYear->description)
                                    <br><small class="text-muted">{{ $fiscalYear->description }}</small>
                                @endif
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>{{ $fiscalYear->start_date->format('M d, Y') }}</strong>
                                <br>
                                <small class="text-muted">to {{ $fiscalYear->end_date->format('M d, Y') }}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                @php
                                    $duration = $fiscalYear->start_date->diffInDays($fiscalYear->end_date) + 1;
                                    $months = round($duration / 30.44);
                                @endphp
                                <span class="fw-bold">{{ $duration }} days</span>
                                <br>
                                <small class="text-muted">{{ $months }} months</small>
                            </div>
                        </td>
                        <td class="text-center">
                            @switch($fiscalYear->status)
                                @case('open')
                                    <span class="badge bg-success">Open</span>
                                    @break
                                @case('closed')
                                    <span class="badge bg-danger">Closed</span>
                                    @break
                                @case('locked')
                                    <span class="badge bg-warning">Locked</span>
                                    @break
                                @default
                                    <span class="badge bg-secondary">{{ ucfirst($fiscalYear->status) }}</span>
                            @endswitch
                        </td>
                        <td class="text-center">
                            @if($fiscalYear->is_active)
                                <span class="badge bg-success">
                                    <i class="bi bi-check-circle me-1"></i>Active
                                </span>
                            @else
                                <span class="badge bg-secondary">
                                    <i class="bi bi-x-circle me-1"></i>Inactive
                                </span>
                            @endif
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                @can('view', $fiscalYear)
                                <a href="{{ route('fiscal-years.show', $fiscalYear) }}"
                                   class="btn btn-sm btn-outline-info" title="View Details">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @endcan

                                @can('update', $fiscalYear)
                                <a href="{{ route('fiscal-years.edit', $fiscalYear) }}"
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                @endcan

                                @can('delete', $fiscalYear)
                                <form method="POST" action="{{ route('fiscal-years.destroy', $fiscalYear) }}"
                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this fiscal year?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-calendar-range display-4 d-block mb-2"></i>
                                <p class="mb-0">No fiscal years found.</p>
                                @can('create', App\Models\FiscalYear::class)
                                <a href="{{ route('fiscal-years.create') }}" class="btn btn-primary btn-sm mt-2">
                                    <i class="bi bi-plus-circle me-1"></i>Create First Fiscal Year
                                </a>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($fiscalYears->hasPages())
        <div class="card-footer">
            {{ $fiscalYears->appends(request()->query())->links() }}
        </div>
        @endif
    </div>

    <!-- Summary Cards -->
    @if($fiscalYears->count() > 0)
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $fiscalYears->where('status', 'open')->count() }}</h3>
                    <small>Open Years</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $fiscalYears->where('status', 'closed')->count() }}</h3>
                    <small>Closed Years</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $fiscalYears->where('is_active', true)->count() }}</h3>
                    <small>Active Years</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ $fiscalYears->count() }}</h3>
                    <small>Total Years</small>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@if(session('success'))
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div class="toast show" role="alert">
        <div class="toast-header">
            <i class="bi bi-check-circle-fill text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            {{ session('success') }}
        </div>
    </div>
</div>
@endif

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toast after 5 seconds
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        }, 5000);
    });
});
</script>
@endpush
