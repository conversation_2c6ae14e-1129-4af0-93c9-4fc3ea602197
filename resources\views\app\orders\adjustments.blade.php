@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">arrow-return-left</x-slot>
        <x-slot name="title">Order Adjustments</x-slot>
        <x-slot name="subtitle">Manage returns, refunds, and stock adjustments</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('orders.index') }}">Orders</a></li>
            <li class="breadcrumb-item active" aria-current="page">Adjustments</li>
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group">
                <a href="{{ route('orders.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Orders
                </a>
                @can('create', App\Models\Order::class)
                <a href="{{ route('orders.create-adjustment') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>New Adjustment
                </a>
                @endcan
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Adjustments Overview Cards -->
    <div class="row mb-4">
        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-soft-danger avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-arrow-return-left"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1">{{ $adjustments->where('adjustment_type', 'return')->count() }}</h4>
                            <span class="d-block">Returns</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-soft-warning avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-cash-coin"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1">{{ $adjustments->where('adjustment_type', 'refund')->count() }}</h4>
                            <span class="d-block">Refunds</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-sm-6 col-lg-3 mb-3 mb-sm-0">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-soft-info avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-boxes"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1">{{ $adjustments->where('adjustment_type', 'stock_adjustment')->count() }}</h4>
                            <span class="d-block">Stock Adjustments</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-sm-6 col-lg-3">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-soft-success avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-currency-dollar"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1">{{ _money($adjustments->sum('amount_total')) }}</h4>
                            <span class="d-block">Total Adjustment Value</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Adjustments Overview Cards -->

    <!-- Adjustments Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-header-title">
                    <i class="bi bi-list-ul me-2"></i>Order Adjustments
                </h5>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-download me-1"></i>Export
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                        <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-excel me-2"></i>Excel</a></li>
                        <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-pdf me-2"></i>PDF</a></li>
                        <li><a class="dropdown-item" href="#"><i class="bi bi-printer me-2"></i>Print</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th style="width: 12%">Adjustment ID</th>
                        <th style="width: 15%">Type</th>
                        <th style="width: 12%">Original Order</th>
                        <th style="width: 15%">Supplier</th>
                        <th style="width: 12%">Amount</th>
                        <th style="width: 20%">Reason</th>
                        <th style="width: 10%">Date</th>
                        <th style="width: 4%" class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($adjustments as $adjustment)
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0 me-2">
                                    <div class="avatar avatar-xs avatar-soft-danger avatar-circle">
                                        <span class="avatar-initials"><i class="bi bi-arrow-return-left"></i></span>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <span class="fw-semibold">ADJ-{{ _pad($adjustment->id, 5, '0') }}</span>
                                </div>
                            </div>
                        </td>
                        <td>
                            @switch($adjustment->adjustment_type)
                                @case('return')
                                    <span class="badge bg-danger">Return Order</span>
                                    @break
                                @case('refund')
                                    <span class="badge bg-warning">Refund Order</span>
                                    @break
                                @case('stock_adjustment')
                                    <span class="badge bg-info">Stock Adjustment</span>
                                    @break
                                @default
                                    <span class="badge bg-secondary">Adjustment</span>
                            @endswitch
                        </td>
                        <td>
                            @if($adjustment->originalOrder)
                                <a href="{{ route('orders.show', $adjustment->originalOrder) }}" class="text-decoration-none">
                                    PO-{{ _pad($adjustment->originalOrder->id, 5, '0') }}
                                </a>
                            @else
                                <span class="text-muted">-</span>
                            @endif
                        </td>
                        <td>{{ $adjustment->supplier->name ?? '-' }}</td>
                        <td>
                            <span class="fw-semibold text-danger">{{ _money($adjustment->amount_total) }}</span>
                        </td>
                        <td>
                            <span title="{{ $adjustment->adjustment_reason }}">
                                {{ Str::limit($adjustment->adjustment_reason, 30) }}
                            </span>
                        </td>
                        <td>{{ $adjustment->created_at->format('M d, Y') }}</td>
                        <td class="text-center">
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" 
                                        id="adjustmentActions{{ $adjustment->id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-three-dots"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adjustmentActions{{ $adjustment->id }}">
                                    @can('view', $adjustment)
                                    <li>
                                        <a href="{{ route('orders.show', $adjustment) }}" class="dropdown-item">
                                            <i class="bi bi-eye me-2"></i>View Details
                                        </a>
                                    </li>
                                    @endcan
                                    
                                    @can('update', $adjustment)
                                    <li>
                                        <a href="{{ route('orders.edit', $adjustment) }}" class="dropdown-item">
                                            <i class="bi bi-pencil me-2"></i>Edit Adjustment
                                        </a>
                                    </li>
                                    @endcan
                                    
                                    @can('delete', $adjustment)
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form action="{{ route('orders.destroy', $adjustment) }}" method="POST" class="d-inline w-100"
                                              onsubmit="return confirm('Are you sure you want to delete this adjustment?')">
                                            @csrf @method('DELETE')
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="bi bi-trash me-2"></i>Delete Adjustment
                                            </button>
                                        </form>
                                    </li>
                                    @endcan
                                </ul>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="text-center p-5">
                            <x-empty-state 
                                icon="arrow-return-left" 
                                title="No adjustments found" 
                                message="No order adjustments have been created yet."
                            />
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    <!-- End Adjustments Table -->
</div>
<!-- End Content -->

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize DataTable
    dataTableBtn();
    
    // Prevent dropdown from closing when clicking on form elements
    $('.dropdown-menu').on('click', 'form', function(e) {
        e.stopPropagation();
    });
});
</script>
@endpush

@endsection
