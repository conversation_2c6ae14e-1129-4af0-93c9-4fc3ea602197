<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\PermissionRegistrar;

class EmployeeLoanPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Reset cached roles and permissions
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        // Create employee loan permissions
        $this->createEmployeeLoanPermissions();

        // Assign permissions to roles
        $this->assignPermissionsToRoles();
    }

    /**
     * Create employee loan permissions.
     *
     * @return void
     */
    private function createEmployeeLoanPermissions()
    {
        // Standard CRUD permissions for employee loans
        $this->createResourcePermissions('employee-loans');
        
        // Special permissions for loan management
        $this->createPermission('approve employee-loans');
        $this->createPermission('cancel employee-loans');
        $this->createPermission('view employee-loan-statistics');
        $this->createPermission('export employee-loan-reports');
    }

    /**
     * Create standard CRUD permissions for a resource.
     *
     * @param string $resource
     * @return void
     */
    private function createResourcePermissions($resource)
    {
        $this->createPermission("list {$resource}");
        $this->createPermission("view {$resource}");
        $this->createPermission("create {$resource}");
        $this->createPermission("update {$resource}");
        $this->createPermission("delete {$resource}");
    }

    /**
     * Create a single permission if it doesn't exist.
     *
     * @param string $name
     * @return void
     */
    private function createPermission($name)
    {
        Permission::firstOrCreate(['name' => $name]);
    }

    /**
     * Assign permissions to roles.
     *
     * @return void
     */
    private function assignPermissionsToRoles()
    {
        // Get or create roles
        $superAdmin = Role::firstOrCreate(['name' => 'super-admin']);
        $hrManager = Role::firstOrCreate(['name' => 'hr-manager']);
        $payrollManager = Role::firstOrCreate(['name' => 'payroll-manager']);
        $financeManager = Role::firstOrCreate(['name' => 'finance-manager']);
        $loanOfficer = Role::firstOrCreate(['name' => 'loan-officer']);
        $employee = Role::firstOrCreate(['name' => 'employee']);

        // Super Admin gets all permissions
        $superAdmin->givePermissionTo(Permission::all());

        // HR Manager permissions - Full loan management
        $hrManagerPermissions = [
            'list employee-loans',
            'view employee-loans',
            'create employee-loans',
            'update employee-loans',
            'delete employee-loans',
            'approve employee-loans',
            'cancel employee-loans',
            'view employee-loan-statistics',
            'export employee-loan-reports',
        ];
        $hrManager->givePermissionTo($hrManagerPermissions);

        // Payroll Manager permissions - View and manage active loans for payroll processing
        $payrollManagerPermissions = [
            'list employee-loans',
            'view employee-loans',
            'view employee-loan-statistics',
            'export employee-loan-reports',
        ];
        $payrollManager->givePermissionTo($payrollManagerPermissions);

        // Finance Manager permissions - Approve and monitor loans
        $financeManagerPermissions = [
            'list employee-loans',
            'view employee-loans',
            'approve employee-loans',
            'cancel employee-loans',
            'view employee-loan-statistics',
            'export employee-loan-reports',
        ];
        $financeManager->givePermissionTo($financeManagerPermissions);

        // Loan Officer permissions - Create and manage loan applications
        $loanOfficerPermissions = [
            'list employee-loans',
            'view employee-loans',
            'create employee-loans',
            'update employee-loans',
            'view employee-loan-statistics',
        ];
        $loanOfficer->givePermissionTo($loanOfficerPermissions);

        // Employee permissions - View their own loans only (this would need additional logic in policies)
        $employeePermissions = [
            'view employee-loans', // Limited to own loans through policy logic
        ];
        $employee->givePermissionTo($employeePermissions);

        // Also assign loan permissions to existing payroll-related roles
        $this->assignToExistingPayrollRoles();
    }

    /**
     * Assign loan permissions to existing payroll-related roles.
     *
     * @return void
     */
    private function assignToExistingPayrollRoles()
    {
        // If there are existing payroll roles, give them appropriate loan permissions
        $existingRoles = [
            'accountant' => [
                'list employee-loans',
                'view employee-loans',
                'view employee-loan-statistics',
            ],
            'manager' => [
                'list employee-loans',
                'view employee-loans',
                'approve employee-loans',
                'view employee-loan-statistics',
            ],
            'supervisor' => [
                'list employee-loans',
                'view employee-loans',
                'view employee-loan-statistics',
            ],
        ];

        foreach ($existingRoles as $roleName => $permissions) {
            $role = Role::where('name', $roleName)->first();
            if ($role) {
                $role->givePermissionTo($permissions);
            }
        }
    }
}
