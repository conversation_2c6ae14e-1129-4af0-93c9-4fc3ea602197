@php
    $editing = isset($order) && $order->exists;
    $isAdjustment = isset($isAdjustment) && $isAdjustment;
    $originalItems = $originalItems ?? [];
@endphp

<div id="order-form">
  <!-- Basic Order Info -->
  <div class="card my-4">
      <div class="card-header">
          <h5 class="card-title mb-0">
              <i class="bi bi-file-text me-2"></i>Order Information
          </h5>
      </div>
      <div class="card-body">
          <div class="row g-3">
            
              <!-- Product Search -->
              <div class="col-md-4">
                  <label class="form-label">Search & Add Product</label>
                  <select v-model="selectedProduct" @change="addProductToOrder" class="form-select">
                      <option value="">Type to search and select products...</option>
                      <option v-for="product in products" :key="product.id" :value="product.id">
                          @{{ product.name }} - @{{ product.description }}
                      </option>
                  </select>
              </div>

              <!-- Supplier -->
              <div class="col-md-4">
                  <label for="supplier_id" class="form-label">Supplier</label>
                  <select name="supplier_id" v-model="order.supplier_id" class="form-select @error('supplier_id') is-invalid @enderror">
                      <option value="">Select Supplier (Optional)</option>
                      <option v-for="supplier in suppliers" :key="supplier.id" :value="supplier.id" v-text="supplier.name"></option>
                  </select>
                  @error('supplier_id')
                      <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
              </div>

              <!-- Order Date -->
              <div class="col-md-4">
                  <label for="order_date" class="form-label">Order Date</label>
                  <input type="date"
                        name="order_date"
                        class="form-control @error('order_date') is-invalid @enderror"
                        value="{{ old('order_date', $editing ? $order->created_at->format('Y-m-d') : date('Y-m-d')) }}">
                  @error('order_date')
                      <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
              </div>

              <!-- Description -->
              <div class="col-12">
                  <label for="description" class="form-label">Description</label>
                  <textarea name="description"
                            v-model="order.description"
                            class="form-control @error('description') is-invalid @enderror"
                            rows="2"
                            placeholder="Optional order notes or description"></textarea>
                  @error('description')
                      <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
              </div>
          </div>
      </div>
  </div>

<!-- Order Items - Full Width -->
<div class="card mb-4">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>Order Items
            </h5>
            <div class="d-flex gap-2 align-items-center">
                <span class="badge bg-primary">
                    @{{ order.items.length }} item(s)
                </span>
                <button type="button" class="btn btn-outline-primary btn-sm" @click="addEmptyItem">
                    <i class="bi bi-plus me-1"></i>Add Item
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
                <!-- Order Items Table -->
                <div v-if="order.items.length > 0" class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 25%">Product <span class="text-danger">*</span></th>
                                <th style="width: 10%">Unit <span class="text-danger">*</span></th>
                                <th style="width: 12%">Location</th>
                                <th style="width: 8%">Qty <span class="text-danger">*</span></th>
                                <th style="width: 10%">Price</th>
                                <th style="width: 10%">Total</th>
                                <th style="width: 12%">Supplier</th>
                                <th style="width: 8%">Discount</th>
                                <th style="width: 10%">Expiry</th>
                                <th style="width: 5%"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(item, index) in order.items" :key="index" class="order-item">
                                <!-- Product -->
                                <td>
                                    <select v-model="item.product_id"
                                            @change="updateProductInfo(index)"
                                            :name="'items[' + index + '][product_id]'"
                                            class="form-select form-select-sm"
                                            required>
                                        <option value="">Select Product</option>
                                        <option v-for="product in products" :key="product.id" :value="product.id" v-text="product.name"></option>
                                    </select>
                                </td>

                                <!-- Unit -->
                                <td>
                                    <select v-model="item.unit_id"
                                            @change="updateUnitPrice(index)"
                                            :name="'items[' + index + '][unit_id]'"
                                            class="form-select form-select-sm"
                                            required>
                                        <option value="">Select Unit</option>
                                        <option v-for="unit in getProductUnits(item.product_id)" :key="unit.id" :value="unit.id" v-text="unit.name"></option>
                                    </select>
                                </td>

                                <!-- Location -->
                                <td>
                                    <select v-model="item.location"
                                            :name="'items[' + index + '][location]'"
                                            class="form-select form-select-sm">
                                            <option value="" selected disable>Select Location</option>
                                        <option v-for="location in locations" :key="location.name" :value="location.name" v-text="location.name"></option>
                                    </select>
                                </td>

                                <!-- Quantity -->
                                <td>
                                    <input type="number"
                                           v-model="item.quantity"
                                           @input="calculateItemTotal(index)"
                                           :name="'items[' + index + '][quantity]'"
                                           class="form-control form-control-sm"
                                           step="0.01"
                                           min="0.01"
                                           required>
                                </td>

                                <!-- Unit Price -->
                                <td>
                                    <div class="input-group input-group-sm">
                                        <input type="number"
                                               v-model="item.buying_price"
                                               @input="calculateItemTotal(index)"
                                               :name="'items[' + index + '][buying_price]'"
                                               class="form-control"
                                               step="0.01"
                                               min="0">
                                    </div>
                                </td>

                                <!-- Total -->
                                <td>
                                    <div class="input-group input-group-sm">
                                        <input type="text"
                                               :value="formatNumber(item.total)"
                                               class="form-control"
                                               readonly>
                                    </div>
                                </td>

                                <!-- Supplier -->
                                <td>
                                    <select v-model="item.supplier_id"
                                            :name="'items[' + index + '][supplier_id]'"
                                            class="form-select form-select-sm">
                                        <option value="">Supplier</option>
                                        <option v-for="supplier in suppliers" :key="supplier.id" :value="supplier.id" v-text="supplier.name"></option>
                                    </select>
                                </td>

                                <!-- Discount -->
                                <td>
                                    <div class="input-group input-group-sm">
                                        <input type="number"
                                               v-model="item.discount"
                                               @input="calculateItemTotal(index)"
                                               :name="'items[' + index + '][discount]'"
                                               class="form-control"
                                               step="0.01"
                                               min="0">
                                    </div>
                                </td>

                                <!-- Expiry Date -->
                                <td>
                                    <input type="date"
                                           v-model="item.expires_at"
                                           :name="'items[' + index + '][expires_at]'"
                                           class="form-control form-control-sm">
                                </td>

                                <!-- Remove Button -->
                                <td>
                                    <button type="button"
                                            class="btn btn-outline-danger btn-sm"
                                            @click="removeItem(index)"
                                            title="Remove Item">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Empty State -->
                <div v-else class="text-center py-4 text-muted">
                    <i class="bi bi-cart-plus fs-1 mb-3"></i>
                    <h6>No items added yet</h6>
                    <p class="mb-0">Select a product above to start building your order.</p>
                </div>
            </div>
        </div>

<!-- Order Summary - Full Width Below Items -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-calculator me-2"></i>Order Summary & Payment
        </h5>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <!-- Left Column - Totals -->
            <div class="col-md-6">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Subtotal</label>
                        <div class="input-group">
                            <input type="number"
                                   name="sub_total"
                                   :value="formatNumber(order.sub_total)"
                                   class="form-control @error('sub_total') is-invalid @enderror"
                                   readonly>
                        </div>
                        @error('sub_total')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">VAT</label>
                        <div class="input-group">
                            <input type="number"
                                   name="vat"
                                   v-model="order.vat"
                                   class="form-control @error('vat') is-invalid @enderror"
                                   step="0.01">
                        </div>
                        @error('vat')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Discount</label>
                        <div class="input-group">
                            <input type="number"
                                   name="discount"
                                   v-model="order.discount"
                                   class="form-control @error('discount') is-invalid @enderror"
                                   step="0.01">
                        </div>
                        @error('discount')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label fw-bold">Total Amount</label>
                        <div class="input-group">
                            <input type="number"
                                   name="amount_total"
                                   :value="formatNumber(order.amount_total)"
                                   class="form-control fw-bold @error('amount_total') is-invalid @enderror"
                                   readonly>
                        </div>
                        @error('amount_total')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Right Column - Payment & Actions -->
            <div class="col-md-6">
                <div class="row g-3">
                    <div class="col-md-12">
                        <label class="form-label">Amount Paid</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number"
                                   name="amount_paid"
                                   v-model="order.amount_paid"
                                   class="form-control @error('amount_paid') is-invalid @enderror"
                                   step="0.01">
                        </div>
                        @error('amount_paid')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Balance Display -->
                    <div class="col-12" v-if="balanceStatus">
                        <div :class="'alert mb-0 ' + balanceStatus.class">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold">@{{ balanceStatus.text }}:</span>
                                <span class="fw-bold">$@{{ formatNumber(balanceStatus.amount) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="col-12">
                        <div class="d-flex gap-2 justify-content-between align-items-center">
                            <!-- Calculation Status -->
                            <div :class="calculationStatus.class + ' small d-flex align-items-center'">
                                <i :class="calculationStatus.icon + ' me-1'"></i>
                                @{{ calculationStatus.text }}
                            </div>

                            <!-- Action Buttons -->
                            <div class="d-flex gap-2">
                                <a href="{{ route('orders.index') }}" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-circle me-1"></i>Cancel
                                </a>

                                <!-- Manual save button trigger (for edge cases) -->
                                <button type="button"
                                        class="btn btn-outline-warning btn-sm"
                                        v-show="!isFormValid && !showSaveButtonFallback"
                                        @click="showSaveButtonFallback = true"
                                        title="Force show save button">
                                    <i class="bi bi-unlock me-1"></i>Enable Save
                                </button>

                                <!-- Show save button when calculations are valid or after timeout -->
                                <button type="submit"
                                        class="btn btn-primary"
                                        v-show="isFormValid || showSaveButtonFallback">
                                    <i class="bi bi-check-circle me-1"></i>{{ $editing ? 'Update Order' : 'Create Order' }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>




@push('scripts')
<script>
new Vue({
    el: '#order-form',
    data() {
        return {
            order: {
                supplier_id: '{{ old("supplier_id", $editing ? ($order->supplier_id ?? "") : "") }}',
                description: '{{ old("description", $editing ? ($order->description ?? "") : "") }}',
                sub_total: {{ old('sub_total', $editing ? ($order->sub_total ?? 0) : 0) }},
                vat: {{ old('vat', $editing ? ($order->vat ?? 0) : 0) }},
                discount: {{ old('discount', $editing ? ($order->discount ?? 0) : 0) }},
                amount_total: {{ old('amount_total', $editing ? ($order->amount_total ?? 0) : 0) }},
                amount_paid: {{ old('amount_paid', $editing ? ($order->amount_paid ?? 0) : 0) }},
                items: {!! json_encode($editing && isset($order) && $order->stocks ? $order->stocks->map(function($sale) {
                    return [
                        'product_id' => $sale->product_id,
                        'unit_id' => $sale->unit_id,
                        'supplier_id' => $sale->supplier_id,
                        'location' => $sale->location ?? '',
                        'quantity' => $sale->quantity,
                        'buying_price' => $sale->buying_price,
                        'discount' => $sale->discount ?? 0,
                        'expires_at' => $sale->expires_at ? $sale->expires_at->format('Y-m-d') : '',
                        'total' => ($sale->quantity * $sale->buying_price) - ($sale->discount ?? 0)
                    ];
                })->toArray() : (!empty($originalItems) ? $originalItems : [])) !!}
            },
            selectedProduct: '',
            products: @json($products),
            suppliers: @json(App\Models\Supplier::orderBy('name')->get()),
            locations: @json(App\Models\Location::orderBy('name')->get()),
            nextIndex: {{ $editing && isset($order) && $order->stocks ? $order->stocks->count() : (!empty($originalItems) ? count($originalItems) : 0) }},
            isAdjustment: {{ $isAdjustment ? 'true' : 'false' }},
            showSaveButtonFallback: false
        }
    },

    computed: {
        balance() {
            return this.order.amount_total - this.order.amount_paid;
        },

        balanceStatus() {
            if (this.balance > 0) return { class: 'alert-warning', text: 'Balance Due', amount: this.balance };
            if (this.balance < 0) return { class: 'alert-info', text: 'Change Due', amount: Math.abs(this.balance) };
            if (this.order.amount_total > 0) return { class: 'alert-success', text: 'Fully Paid', amount: 0 };
            return null;
        },

        isFormValid() {
            // Check if form has valid items and calculations are done
            const hasValidItems = this.order.items.length > 0 &&
                                  this.order.items.some(item =>
                                      item.product_id &&
                                      item.unit_id &&
                                      parseFloat(item.quantity) > 0
                                  );

            // Check if totals are calculated
            // For adjustments, sub_total will be negative, so check absolute value
            const calculationsValid = hasValidItems ?
                (this.isAdjustment ? this.order.sub_total < 0 : this.order.sub_total > 0) :
                true;

            return hasValidItems && calculationsValid;
        },

        calculationStatus() {
            if (this.order.items.length === 0) {
                return { text: 'Add items to start', class: 'text-muted', icon: 'bi-info-circle' };
            }

            const validItems = this.order.items.filter(item =>
                item.product_id && item.unit_id && parseFloat(item.quantity) > 0
            );

            if (validItems.length === 0) {
                return { text: 'Complete item details', class: 'text-warning', icon: 'bi-exclamation-triangle' };
            }

            // Check if calculations are complete (positive for orders, negative for adjustments)
            const calculationsComplete = this.isAdjustment ?
                this.order.sub_total < 0 :
                this.order.sub_total > 0;

            if (calculationsComplete) {
                return {
                    text: this.isAdjustment ? 'Adjustment ready to save' : 'Ready to save',
                    class: 'text-success',
                    icon: 'bi-check-circle'
                };
            }

            return { text: 'Calculating...', class: 'text-info', icon: 'bi-arrow-clockwise' };
        }
    },

    methods: {
        addProductToOrder() {
            if (!this.selectedProduct) return;

            const product = this.products.find(p => p.id == this.selectedProduct);
            if (!product) return;

            // Check if product already exists
            const existingIndex = this.order.items.findIndex(item => item.product_id == product.id);
            if (existingIndex !== -1) {
                // Increase quantity if product already exists
                this.order.items[existingIndex].quantity = parseFloat(this.order.items[existingIndex].quantity) + 1;
                this.calculateItemTotal(existingIndex);
            } else {
                // Add new item
                this.order.items.push({
                    product_id: product.id,
                    unit_id: '',
                    supplier_id: '',
                    location: '',
                    quantity: 1,
                    buying_price: product.buying_price || 0,
                    discount: 0,
                    expires_at: '',
                    total: product.buying_price || 0
                });
            }

            // Reset selector
            this.selectedProduct = '';
            this.calculateTotals();
        },

        addEmptyItem() {
            this.order.items.push({
                product_id: '',
                unit_id: '',
                supplier_id: '',
                location: '',
                quantity: 1,
                buying_price: 0,
                discount: 0,
                expires_at: '',
                total: 0
            });
        },

        removeItem(index) {
            this.order.items.splice(index, 1);
            this.calculateTotals();
        },

        updateProductInfo(index) {
            const item = this.order.items[index];
            const product = this.products.find(p => p.id == item.product_id);

            if (product) {
                // Update price
                item.buying_price = product.buying_price || 0;

                // Reset unit selection
                item.unit_id = '';

                // Calculate item total
                this.calculateItemTotal(index);
            }
        },

        updateUnitPrice(index) {
            const item = this.order.items[index];
            const product = this.products.find(p => p.id == item.product_id);

            if (product && product.units) {
                const unit = product.units.find(u => u.id == item.unit_id);
                if (unit) {
                    // Update price based on unit
                    item.buying_price = unit.pivot?.buying_price || unit.buying_price || product.buying_price || 0;
                    this.calculateItemTotal(index);
                }
            }
        },

        calculateItemTotal(index) {
            const item = this.order.items[index];
            const quantity = parseFloat(item.quantity) || 0;
            const price = parseFloat(item.buying_price) || 0;
            const discount = parseFloat(item.discount) || 0;

            item.total = (quantity * price) - discount;
            this.calculateTotals();
        },

        calculateTotals() {
            // Calculate subtotal from all items
            let subtotal = this.order.items.reduce((sum, item) => {
                return sum + (parseFloat(item.total) || 0);
            }, 0);

            // For adjustments, make values negative
            if (this.isAdjustment) {
                subtotal = -Math.abs(subtotal);
            }

            this.order.sub_total = subtotal;

            // Calculate final total
            const vat = parseFloat(this.order.vat) || 0;
            const discount = parseFloat(this.order.discount) || 0;
            this.order.amount_total = this.order.sub_total + vat - discount;

            // For adjustments, ensure negative values
            if (this.isAdjustment) {
                this.order.amount_total = -Math.abs(this.order.amount_total);
            }
        },

        getProductUnits(productId) {
            const product = this.products.find(p => p.id == productId);
            return product ? product.units || [] : [];
        },

        formatNumber(number) {
            return parseFloat(number || 0).toFixed(2);
        }
    },

    mounted() {
        // Initialize with at least one item if none exist
        if (this.order.items.length === 0) {
            // this.addEmptyItem();
        }

        // Initial calculation (automatic via watchers and computed properties)
        this.calculateTotals();

        // Fallback: Show save button after 3 seconds if it's still not visible
        // This ensures users can always save even if there are edge cases in validation
        setTimeout(() => {
            if (!this.isFormValid) {
                this.showSaveButtonFallback = true;
            }
        }, 3000);
    },

    watch: {
        'order.vat': function() {
            this.calculateTotals();
        },
        'order.discount': function() {
            this.calculateTotals();
        },
        'order.amount_paid': function() {
            this.calculateTotals();
        }
    }
});
</script>
@endpush
