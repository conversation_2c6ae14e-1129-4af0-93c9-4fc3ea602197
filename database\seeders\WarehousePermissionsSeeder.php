<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class WarehousePermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create warehouse permissions
        $permissions = [
            'view-any Warehouse',
            'view Warehouse',
            'create Warehouse',
            'update Warehouse',
            'delete Warehouse',
            'delete-any Warehouse',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Assign permissions to super-admin role
        $adminRole = Role::where('name', 'super-admin')->first();
        if ($adminRole) {
            $adminRole->givePermissionTo($permissions);
        }

        // Assign permissions to warehouse-manager role
        $warehouseManagerRole = Role::firstOrCreate(['name' => 'warehouse-manager']);
        $warehouseManagerRole->givePermissionTo([
            'view-any Warehouse',
            'view Warehouse',
            'create Warehouse',
            'update Warehouse',
        ]);

        // Assign permissions to accountant role
        $accountantRole = Role::where('name', 'accountant')->first();
        if ($accountantRole) {
            $accountantRole->givePermissionTo([
                'view-any Warehouse',
                'view Warehouse',
            ]);
        }
    }
}
