@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-edit text-primary"></i> Edit Loan Application
            </h1>
            <p class="text-muted mb-0">{{ $employeeLoan->loan_reference }} - {{ $employeeLoan->employee->name }}</p>
        </div>
        <div>
            <a href="{{ route('employee-loans.show', $employeeLoan) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Details
            </a>
        </div>
    </div>

    @if($employeeLoan->status !== 'pending')
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i>
        This loan is no longer in pending status and cannot be edited.
    </div>
    @else
    <form action="{{ route('employee-loans.update', $employeeLoan) }}" method="POST" id="loanForm" novalidate>
        @csrf
        @method('PUT')
        
        <div class="row">
            <!-- Left Column -->
            <div class="col-lg-8">
                <!-- Loan Information Card -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i> Loan Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="employee_id" class="form-label">
                                        Employee <span class="text-danger">*</span>
                                    </label>
                                    <select name="employee_id" id="employee_id" 
                                            class="form-select @error('employee_id') is-invalid @enderror" required>
                                        <option value="">Select Employee</option>
                                        @foreach($employees as $employee)
                                            <option value="{{ $employee->id }}" 
                                                    {{ (old('employee_id', $employeeLoan->employee_id) == $employee->id) ? 'selected' : '' }}
                                                    data-email="{{ $employee->email }}"
                                                    data-basic-pay="{{ $employee->basic_pay }}">
                                                {{ $employee->name }} - K{{ number_format($employee->basic_pay, 2) }}/month
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('employee_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="loan_amount" class="form-label">
                                        Loan Amount (K) <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" name="loan_amount" id="loan_amount" 
                                           class="form-control @error('loan_amount') is-invalid @enderror"
                                           value="{{ old('loan_amount', $employeeLoan->loan_amount) }}" step="0.01" min="1" max="10000000" required>
                                    @error('loan_amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Maximum recommended: <span id="max-recommended">-</span></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="installment_amount" class="form-label">
                                        Monthly Installment (K) <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" name="installment_amount" id="installment_amount" 
                                           class="form-control @error('installment_amount') is-invalid @enderror"
                                           value="{{ old('installment_amount', $employeeLoan->installment_amount) }}" step="0.01" min="1" required>
                                    @error('installment_amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Maximum recommended: <span id="max-installment">-</span></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="total_installments" class="form-label">Total Installments</label>
                                    <input type="number" id="total_installments" class="form-control" readonly>
                                    <div class="form-text">Calculated automatically</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="loan_date" class="form-label">
                                        Application Date <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" name="loan_date" id="loan_date" 
                                           class="form-control @error('loan_date') is-invalid @enderror"
                                           value="{{ old('loan_date', $employeeLoan->loan_date->format('Y-m-d')) }}" max="{{ date('Y-m-d') }}" required>
                                    @error('loan_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_deduction_date" class="form-label">
                                        First Deduction Date <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" name="first_deduction_date" id="first_deduction_date" 
                                           class="form-control @error('first_deduction_date') is-invalid @enderror"
                                           value="{{ old('first_deduction_date', $employeeLoan->first_deduction_date->format('Y-m-d')) }}" 
                                           min="{{ date('Y-m-d', strtotime('+1 day')) }}" required>
                                    @error('first_deduction_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="purpose" class="form-label">
                                Loan Purpose <span class="text-danger">*</span>
                            </label>
                            <textarea name="purpose" id="purpose" 
                                      class="form-control @error('purpose') is-invalid @enderror"
                                      rows="3" maxlength="1000" required>{{ old('purpose', $employeeLoan->purpose) }}</textarea>
                            @error('purpose')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Optional Settings -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog"></i> Optional Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="interest_rate" class="form-label">Interest Rate (%)</label>
                                    <input type="number" name="interest_rate" id="interest_rate" 
                                           class="form-control @error('interest_rate') is-invalid @enderror"
                                           value="{{ old('interest_rate', $employeeLoan->interest_rate) }}" step="0.01" min="0" max="100">
                                    @error('interest_rate')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="interest_type" class="form-label">Interest Type</label>
                                    <select name="interest_type" id="interest_type" 
                                            class="form-select @error('interest_type') is-invalid @enderror">
                                        <option value="simple" {{ old('interest_type', $employeeLoan->interest_type) === 'simple' ? 'selected' : '' }}>Simple Interest</option>
                                        <option value="compound" {{ old('interest_type', $employeeLoan->interest_type) === 'compound' ? 'selected' : '' }}>Compound Interest</option>
                                    </select>
                                    @error('interest_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="terms_conditions" class="form-label">Terms and Conditions</label>
                            <textarea name="terms_conditions" id="terms_conditions" 
                                      class="form-control @error('terms_conditions') is-invalid @enderror"
                                      rows="4" maxlength="2000">{{ old('terms_conditions', $employeeLoan->terms_conditions) }}</textarea>
                            @error('terms_conditions')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Summary -->
            <div class="col-lg-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-calculator"></i> Loan Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="loan-summary">
                            <div class="row mb-3">
                                <div class="col-6"><strong>Employee:</strong></div>
                                <div class="col-6" id="summary-employee">{{ $employeeLoan->employee->name }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-6"><strong>Loan Amount:</strong></div>
                                <div class="col-6" id="summary-amount">K{{ number_format($employeeLoan->loan_amount, 2) }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-6"><strong>Monthly Payment:</strong></div>
                                <div class="col-6" id="summary-installment">K{{ number_format($employeeLoan->installment_amount, 2) }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-6"><strong>Total Payments:</strong></div>
                                <div class="col-6" id="summary-installments">{{ $employeeLoan->total_installments }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-6"><strong>Duration:</strong></div>
                                <div class="col-6" id="summary-duration">{{ $employeeLoan->total_installments }} months</div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-6"><strong>Expected Completion:</strong></div>
                                <div class="col-6" id="summary-completion">{{ $employeeLoan->expected_completion_date ? $employeeLoan->expected_completion_date->format('M d, Y') : '-' }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Status -->
                <div class="card shadow-sm">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i> Current Status
                        </h6>
                    </div>
                    <div class="card-body">
                        <p><strong>Status:</strong> 
                            <span class="badge bg-warning">{{ ucfirst($employeeLoan->status) }}</span>
                        </p>
                        <p><strong>Reference:</strong> {{ $employeeLoan->loan_reference }}</p>
                        <p><strong>Created:</strong> {{ $employeeLoan->created_at->format('M d, Y') }}</p>
                        @if($employeeLoan->createdBy)
                        <p><strong>Created By:</strong> {{ $employeeLoan->createdBy->name }}</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-between mt-4">
            <a href="{{ route('employee-loans.show', $employeeLoan) }}" class="btn btn-light">
                <i class="fas fa-arrow-left"></i> Back to Details
            </a>
            <div>
                <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                    <i class="fas fa-undo"></i> Reset Changes
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update Loan
                </button>
            </div>
        </div>
    </form>
    @endif
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const employeeSelect = document.getElementById('employee_id');
    const loanAmountInput = document.getElementById('loan_amount');
    const installmentInput = document.getElementById('installment_amount');
    const firstDeductionInput = document.getElementById('first_deduction_date');
    
    employeeSelect.addEventListener('change', updateRecommendations);
    loanAmountInput.addEventListener('input', updateSummary);
    installmentInput.addEventListener('input', updateSummary);
    firstDeductionInput.addEventListener('change', updateSummary);
    
    function updateRecommendations() {
        const selectedOption = employeeSelect.options[employeeSelect.selectedIndex];
        if (selectedOption.value) {
            const basicPay = parseFloat(selectedOption.dataset.basicPay) || 0;
            const maxLoan = basicPay * 6;
            const maxInstallment = basicPay * 0.3;
            
            document.getElementById('max-recommended').textContent = `K${maxLoan.toLocaleString()}`;
            document.getElementById('max-installment').textContent = `K${maxInstallment.toLocaleString()}`;
            
            updateSummary();
        } else {
            document.getElementById('max-recommended').textContent = '-';
            document.getElementById('max-installment').textContent = '-';
        }
    }
    
    function updateSummary() {
        const selectedOption = employeeSelect.options[employeeSelect.selectedIndex];
        const loanAmount = parseFloat(loanAmountInput.value) || 0;
        const installmentAmount = parseFloat(installmentInput.value) || 0;
        const firstDeductionDate = firstDeductionInput.value;
        
        document.getElementById('summary-employee').textContent = selectedOption.text || '-';
        document.getElementById('summary-amount').textContent = `K${loanAmount.toLocaleString()}`;
        document.getElementById('summary-installment').textContent = `K${installmentAmount.toLocaleString()}`;
        
        const totalInstallments = installmentAmount > 0 ? Math.ceil(loanAmount / installmentAmount) : 0;
        document.getElementById('total_installments').value = totalInstallments;
        document.getElementById('summary-installments').textContent = totalInstallments;
        document.getElementById('summary-duration').textContent = `${totalInstallments} months`;
        
        if (firstDeductionDate && totalInstallments > 0) {
            const startDate = new Date(firstDeductionDate);
            const completionDate = new Date(startDate);
            completionDate.setMonth(completionDate.getMonth() + totalInstallments - 1);
            document.getElementById('summary-completion').textContent = completionDate.toLocaleDateString();
        } else {
            document.getElementById('summary-completion').textContent = '-';
        }
    }
    
    updateRecommendations();
});

function resetForm() {
    if (confirm('Are you sure you want to reset all changes? This will restore the original values.')) {
        location.reload();
    }
}
</script>
@endpush
@endsection
