<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from htmlstream.com/front-dashboard/documentation/select.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 30 Aug 2022 07:13:05 GMT -->
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Title -->
  <title>Advanced Select - Documentation | Front - Multipurpose Responsive Template</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="../favicon.ico">

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&amp;display=swap" rel="stylesheet">

  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="../assets/css/vendor.min.css">

  <!-- CSS Front Template -->
  <link rel="stylesheet" href="../assets/css/theme.minc619.css?v=1.0">

  <link rel="preload" href="../assets/css/theme.min.css" data-hs-appearance="default" as="style">
  <link rel="preload" href="../assets/css/docs.css" data-hs-appearance="default" as="style">
  <link rel="preload" href="../assets/css/theme-dark.min.css" data-hs-appearance="dark" as="style">

  <style data-hs-appearance-onload-styles>
    *
    {
      transition: unset !important;
    }

    body
    {
      opacity: 0;
    }
  </style>

  <script>
            window.hs_config = {"autopath":"@@autopath","deleteLine":"hs-builder:delete","deleteLine:build":"hs-builder:build-delete","deleteLine:dist":"hs-builder:dist-delete","previewMode":false,"startPath":"/index.html","vars":{"themeFont":"https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap","version":"?v=1.0"},"layoutBuilder":{"extend":{"switcherSupport":true},"header":{"layoutMode":"default","containerMode":"container-fluid"},"sidebarLayout":"default"},"themeAppearance":{"layoutSkin":"default","sidebarSkin":"default","styles":{"colors":{"primary":"#377dff","transparent":"transparent","white":"#fff","dark":"132144","gray":{"100":"#f9fafc","900":"#1e2022"}},"font":"Inter"}},"languageDirection":{"lang":"en"},"skipFilesFromBundle":{"dist":["assets/js/hs.theme-appearance.js","assets/js/hs.theme-appearance-charts.html","assets/js/demo.js"],"build":["assets/css/theme.css","assets/vendor/hs-navbar-vertical-aside/dist/hs-navbar-vertical-aside-mini-cache.html","assets/js/demo.html","assets/css/theme-dark.html","assets/css/docs.html","assets/vendor/icon-set/style.html","assets/js/hs.theme-appearance.html","assets/js/hs.theme-appearance-charts.html","node_modules/chartjs-plugin-datalabels/dist/chartjs-plugin-datalabels.min.html","assets/js/demo.js"]},"minifyCSSFiles":["assets/css/theme.css","assets/css/theme-dark.css"],"copyDependencies":{"dist":{"*assets/js/theme-custom.js":""},"build":{"*assets/js/theme-custom.js":"","node_modules/bootstrap-icons/font/*fonts/**":"assets/css"}},"buildFolder":"","replacePathsToCDN":{},"directoryNames":{"src":"./src","dist":"./dist","build":"./build"},"fileNames":{"dist":{"js":"theme.min.js","css":"theme.min.css"},"build":{"css":"theme.min.css","js":"theme.min.js","vendorCSS":"vendor.min.css","vendorJS":"vendor.min.js"}},"fileTypes":"jpg|png|svg|mp4|webm|ogv|json"}
            window.hs_config.gulpRGBA = (p1) => {
  const options = p1.split(',')
  const hex = options[0].toString()
  const transparent = options[1].toString()

  var c;
  if(/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)){
    c= hex.substring(1).split('');
    if(c.length== 3){
      c= [c[0], c[0], c[1], c[1], c[2], c[2]];
    }
    c= '0x'+c.join('');
    return 'rgba('+[(c>>16)&255, (c>>8)&255, c&255].join(',')+',' + transparent + ')';
  }
  throw new Error('Bad Hex');
}
            window.hs_config.gulpDarken = (p1) => {
  const options = p1.split(',')

  let col = options[0].toString()
  let amt = -parseInt(options[1])
  var usePound = false

  if (col[0] == "#") {
    col = col.slice(1)
    usePound = true
  }
  var num = parseInt(col, 16)
  var r = (num >> 16) + amt
  if (r > 255) {
    r = 255
  } else if (r < 0) {
    r = 0
  }
  var b = ((num >> 8) & 0x00FF) + amt
  if (b > 255) {
    b = 255
  } else if (b < 0) {
    b = 0
  }
  var g = (num & 0x0000FF) + amt
  if (g > 255) {
    g = 255
  } else if (g < 0) {
    g = 0
  }
  return (usePound ? "#" : "") + (g | (b << 8) | (r << 16)).toString(16)
}
            window.hs_config.gulpLighten = (p1) => {
  const options = p1.split(',')

  let col = options[0].toString()
  let amt = parseInt(options[1])
  var usePound = false

  if (col[0] == "#") {
    col = col.slice(1)
    usePound = true
  }
  var num = parseInt(col, 16)
  var r = (num >> 16) + amt
  if (r > 255) {
    r = 255
  } else if (r < 0) {
    r = 0
  }
  var b = ((num >> 8) & 0x00FF) + amt
  if (b > 255) {
    b = 255
  } else if (b < 0) {
    b = 0
  }
  var g = (num & 0x0000FF) + amt
  if (g > 255) {
    g = 255
  } else if (g < 0) {
    g = 0
  }
  return (usePound ? "#" : "") + (g | (b << 8) | (r << 16)).toString(16)
}
            </script>
</head>

<body class="navbar-sidebar-aside-lg">

  <script src="../assets/js/hs.theme-appearance.js"></script>

  <!-- ========== HEADER ========== -->
  <header id="header" class="navbar navbar-expand navbar-end navbar-light navbar-sticky-lg-top bg-white">
    <div class="container-fluid">
      <nav class="navbar-nav-wrap">
        <div class="row flex-grow-1">
          <!-- Default Logo -->
          <div class="docs-navbar-sidebar-container d-flex align-items-center mb-2 mb-lg-0">
            <a class="navbar-brand" href="index.html" aria-label="Front">
              <img class="navbar-brand-logo" src="../assets/svg/logos/logo.svg" alt="Logo" data-hs-theme-appearance="default">
              <img class="navbar-brand-logo" src="../assets/svg/logos-light/logo.svg" alt="Logo" data-hs-theme-appearance="dark">
            </a>
            <a href="changelog.html">
              <span class="badge bg-soft-primary text-primary">v2.1</span>
            </a>
          </div>
          <!-- End Default Logo -->

          <div class="col-md px-lg-0">
            <div class="d-flex justify-content-between align-items-center px-lg-5 px-xl-10">
              <div class="d-none d-md-block">
                <!-- Search Form -->
                <form id="docsSearch" class="position-relative" data-hs-list-options='{
                       "searchMenu": true,
                       "keyboard": true,
                       "item": "searchTemplate",
                       "valueNames": ["component", "category", {"name": "link", "attr": "href"}],
                       "empty": "#searchNoResults"
                     }'>
                  <!-- Input Group -->
                  <div class="input-group input-group-merge navbar-input-group">
                    <div class="input-group-prepend input-group-text">
                      <i class="bi-search"></i>
                    </div>

                    <input type="search" class="search form-control form-control-sm" placeholder="Search in docs" aria-label="Search in docs">

                    <a class="input-group-append input-group-text" href="javascript:;">
                      <i id="clearSearchResultsIcon" class="bi-x" style="display: none;"></i>
                    </a>
                  </div>
                  <!-- End Input Group -->

                  <!-- List -->
                  <div class="list dropdown-menu navbar-dropdown-menu-borderless w-100 overflow-auto" style="max-height: 16rem;"></div>
                  <!-- End List -->

                  <!-- Empty -->
                  <div id="searchNoResults" style="display: none;">
                    <div class="text-center p-4">
                      <img class="mb-3" src="../assets/svg/illustrations/oc-error.svg" alt="Image Description" data-hs-theme-appearance="default" style="width: 7rem;">
                      <img class="mb-3" src="../assets/svg/illustrations-light/oc-error.svg" alt="Image Description" data-hs-theme-appearance="dark" style="width: 7rem;">
                      <p class="mb-0">No Results</p>
                    </div>
                  </div>
                  <!-- End Empty -->
                </form>
                <!-- End Search Form -->

                <!-- List Item Template -->
                <div class="d-none">
                  <div id="searchTemplate" class="dropdown-item">
                    <a class="d-block link" href="#">
                      <span class="category d-block fw-normal text-muted mb-1"></span>
                      <span class="component text-dark"></span>
                    </a>
                  </div>
                </div>
                <!-- End List Item Template -->
              </div>

              <!-- Navbar -->
              <ul class="navbar-nav p-0">
                <li class="nav-item">
                  <a class="btn btn-ghost-secondary btn-sm" href="https://htmlstream.com/contact-us" target="_blank">
                    Get Support <i class="bi-box-arrow-up-right ms-1"></i>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="btn btn-primary btn-sm" href="../index.html">
                    <i class="bi-eye me-1"></i> Preview Demo
                  </a>
                </li>
              </ul>
              <!-- End Navbar -->
            </div>
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </nav>
    </div>
  </header>

  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content" role="main">
    <!-- Navbar -->
    <nav class="js-nav-scroller navbar navbar-expand-lg navbar-sidebar navbar-vertical navbar-light bg-white border-end" data-hs-nav-scroller-options='{
            "type": "vertical",
            "target": ".navbar-nav .active",
            "offset": 80
           }'>
      <!-- Navbar Toggle -->
      <div class="d-grid flex-grow-1 px-2">
        <button type="button" class="navbar-toggler btn btn-white" data-bs-toggle="collapse" data-bs-target="#navbarVerticalNavMenu" aria-label="Toggle navigation" aria-expanded="false" aria-controls="navbarVerticalNavMenu">
          <span class="d-flex justify-content-between align-items-center">
            <span class="h3 mb-0">Nav menu</span>

            <span class="navbar-toggler-default">
              <i class="bi-list"></i>
            </span>

            <span class="navbar-toggler-toggled">
              <i class="bi-x"></i>
            </span>
          </span>
        </button>
      </div>
      <!-- End Navbar Toggle -->

      <!-- Navbar Collapse -->
      <div id="navbarVerticalNavMenu" class="collapse navbar-collapse">
        <div class="navbar-brand-wrapper border-end" style="height: auto;">
          <!-- Default Logo -->
          <div class="d-flex align-items-center mb-3">
            <a class="navbar-brand" href="index.html" aria-label="Front">
              <img class="navbar-brand-logo" src="../assets/svg/logos/logo.svg" alt="Logo" data-hs-theme-appearance="default">
              <img class="navbar-brand-logo" src="../assets/svg/logos-light/logo.svg" alt="Logo" data-hs-theme-appearance="dark">
            </a>
            <a class="navbar-brand-badge" href="changelog.html">
              <span class="badge bg-soft-primary text-primary ms-2">v2.1</span>
            </a>
          </div>
          <!-- End Default Logo -->
        </div>

        <div class="docs-navbar-sidebar-aside-body navbar-sidebar-aside-body">
          <ul id="navbarSettings" class="navbar-nav nav nav-vertical nav-tabs nav-tabs-borderless nav-sm">
            <li class="nav-item">
              <span class="nav-subtitle">Documentation</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="index.html">Introduction</a>
            </li>

            <li class="nav-item my-2 my-lg-5"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Getting started</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="getting-started.html">Getting Started</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="gulp.html">Gulp</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="darkmode.html">Dark Mode <span class="badge bg-soft-dark text-dark lh-base ms-1">New</span></a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="customization.html">Customization</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="credits.html">Credits</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="changelog.html">Changelog</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Design &amp; Graphics</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="bs-icons.html">Bootstrap Icons</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="illustrations.html">Illustrations</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <span class="nav-subtitle">Components</span>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="accordion.html">Accordion</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="alerts.html">Alerts</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="avatars.html">Avatars</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="badge.html">Badge</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="breadcrumb.html">Breadcrumb</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="buttons.html">Buttons</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="button-group.html">Button Group</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="cards.html">Cards</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="collapse.html">Collapse</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="column-divider.html">Column Divider</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="devices.html">Devices</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="divider.html">Divider</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="dropdowns.html">Dropdowns</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="icons.html">Icons</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="list-group.html">List Group</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="lists.html">Lists</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="legend-indicator.html">Legend Indicator</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="modal.html">Modal</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="offcanvas.html">Offcanvas</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="page-header.html">Page Header</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="pagination.html">Pagination</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="popovers.html">Popovers</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="progress.html">Progress</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="profile.html">Profile</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="shapes.html">Shapes</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sliding-img.html">Sliding Image</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="spinners.html">Spinners</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="steps.html">Steps</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="tab.html">Tab</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="toasts.html">Toasts</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="tooltips.html">Tooltips</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="typography.html">Typography</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Navbars</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="navbar.html">Navbar</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="navs.html">Navs</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="mega-menu.html">Mega Menu</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="navbar-vertical-aside.html">Navbar Vertical Aside</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="scrollspy.html">Scrollspy</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Tables</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="tables.html">Tables</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="datatables.html">Datatables</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="table-sticky-header.html">Sticky Header</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Basic forms</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="basic-forms.html">Basic Forms</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="checks-and-switches.html">Checks &amp; Switches</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="input-group.html">Input Group</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle">Advanced Forms</small>
            </li>

            <li class="nav-item">
              <a class="nav-link active" href="select.html">Advanced Select</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="datepicker.html">Datepicker (Flatpickr)</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="daterangepicker.html">Date Range Picker</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="calendar.html">Calendar (Fullcalendar)</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="file-attachments.html">File Attachments</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="dropzone.html">Drag’ n’ Drop File Uploads</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="quill.html">WYSIWYG Editor</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="quantity-counter.html">Quantity Counter</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="copy-to-clipboard.html">Copy to Clipboard</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="input-mask.html">Input Mask</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="step-forms.html">Step Forms (Wizards)</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="add-field.html">Add Field</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="toggle-password.html">Toggle Password</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="count-characters.html">Count Characters</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="form-search.html">Form Search</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="toggle-switch.html">Toggle Switch</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="google-recaptcha.html">Google reCAPTCHA</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle">Charts</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="chartjs.html">Chart.js</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="counter.html">Counter</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="circles.html">Circles.js (Pie Chart)</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle">Others</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="fslightbox.html">Fullscreen Lightbox</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="maps-leaflet.html">Leaflet</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="maps-jsvectormap.html">JSVectorMap</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sortablejs.html">SortableJS</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sticky-block.html">Sticky Block</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="go-to.html">Go To</a>
            </li>

            <li class="nav-item my-4"></li>

            <li class="nav-item">
              <small class="nav-subtitle d-block">Utilities</small>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="backgrounds.html">Backgrounds</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="borders.html">Borders</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="colors.html">Colors</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="links.html">Links</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="position.html">Position</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="shadows.html">Shadows</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="sizing.html">Sizing</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="spacing.html">Spacing</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="z-index.html">Z-index</a>
            </li>

            <li class="nav-item">
              <a class="nav-link " href="opacity.html">Opacity</a>
            </li>
          </ul>
        </div>
      </div>
      <!-- End Navbar Collapse -->
    </nav>
    <!-- End Navbar -->

    <!-- Content -->
    <div class="navbar-sidebar-aside-content content-space-1 content-space-md-2 px-lg-5 px-xl-10">
      <!-- Page Header -->
      <div class="docs-page-header">
        <div class="row align-items-center">
          <div class="col-sm">
            <h1 class="docs-page-header-title">Advanced Select</h1>
            <p class="docs-page-header-text">The jQuery replacement for select boxes.</p>
            <a class="link" href="https://tom-select.js.org/examples/" target="_blank">Tom-select.js documentation <i class="bi-box-arrow-up-right"></i></a>
          </div>
        </div>
      </div>
      <!-- End Page Header -->

      <!-- Heading -->
      <h2 id="how-to-use" class="hs-docs-heading">
        How to use <a class="anchorjs-link" href="#how-to-use" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Copy-paste the stylesheet <code>&lt;link&gt;</code> into your <code>&lt;head&gt;</code> to load the CSS.</p>

      <pre class="rounded mb-4">
        <code class="language-html" data-lang="html">
          &lt;link rel="stylesheet" href="./node_modules/tom-select/dist/css/tom-select.bootstrap5.css"&gt;
        </code>
      </pre>

      <p>Copy-paste the following <code>&lt;script&gt;</code> near the end of your pages under <em><u>JS Implementing Plugins</u></em> to enable it.</p>

      <pre class="rounded mb-4">
        <code class="language-html" data-lang="html">
          &lt;script src="./node_modules/tom-select/dist/js/tom-select.complete.min.js"&gt;&lt;/script&gt;
        </code>
      </pre>

      <p>Copy-paste the following <code>&lt;script&gt;</code> near the end of your pages under <em><u>JS Front</u></em> to enable it.</p>

      <pre class="rounded mb-4">
        <code class="language-html" data-lang="html">
          &lt;script src="../assets/js/hs.tom-select.js"&gt;&lt;/script&gt;
        </code>
      </pre>

      <p>Copy-paste the init function under <em><u>JS Plugins Init.</u></em>, before the closing <code>&lt;/body&gt;</code> tag, to enable it.</p>

      <pre class="rounded">
        <code class="language-html" data-lang="html">
          &lt;script&gt;
            (function() {
              // INITIALIZATION OF SELECT
              // =======================================================
              HSCore.components.HSTomSelect.init('.js-select')
            });
          &lt;/script&gt;
        </code>
      </pre>

      <!-- Heading -->
      <h2 id="basic-example" class="hs-docs-heading">
        Basic example <a class="anchorjs-link" href="#basic-example" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab1" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab1" href="#nav-result1" data-bs-toggle="pill" data-bs-target="#nav-result1" role="tab" aria-controls="nav-result1" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab1" href="#nav-html1" data-bs-toggle="pill" data-bs-target="#nav-html1" role="tab" aria-controls="nav-html1" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent1">
          <div class="tab-pane fade p-4 show active" id="nav-result1" role="tabpanel" aria-labelledby="nav-resultTab1">
            <div style="max-width: 20rem;">
              <!-- Select -->
              <div class="tom-select-custom">
                <select class="js-select form-select" autocomplete="off" data-hs-tom-select-options='{
                          "placeholder": "Select a person...",
                          "hideSearch": true
                        }'>
                  <option value="">Select a person...</option>
                  <option value="4">Thomas Edison</option>
                  <option value="1">Nikola</option>
                  <option value="3">Nikola Tesla</option>
                  <option value="5">Arnold Schwarzenegger</option>
                </select>
              </div>
              <!-- End Select -->
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html1" role="tabpanel" aria-labelledby="nav-htmlTab1">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Select --&gt;
                &lt;div class="tom-select-custom"&gt;
                  &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot;
                        data-hs-tom-select-options='{
                          "placeholder": "Select a person...",
                          &quot;hideSearch&quot;: true
                        }'&gt;
                    &lt;option value=&quot;&quot;&gt;Select a person...&lt;/option&gt;
                    &lt;option value=&quot;4&quot;&gt;Thomas Edison&lt;/option&gt;
                    &lt;option value=&quot;1&quot;&gt;Nikola&lt;/option&gt;
                    &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                    &lt;option value=&quot;5&quot;&gt;Arnold Schwarzenegger&lt;/option&gt;
                  &lt;/select&gt;
                &lt;/div&gt;
                &lt;!-- End Select --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="basic-example-1" class="hs-docs-heading">
        Custom placeholder <a class="anchorjs-link" href="#basic-example-1" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab14" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab14" href="#nav-result14" data-bs-toggle="pill" data-bs-target="#nav-result14" role="tab" aria-controls="nav-result14" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab14" href="#nav-html14" data-bs-toggle="pill" data-bs-target="#nav-html14" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent14">
          <div class="tab-pane fade p-4 show active" id="nav-result14" role="tabpanel" aria-labelledby="nav-resultTab14">
            <!-- Select -->
            <div class="tom-select-custom">
              <select class="js-select form-select" autocomplete="off" data-hs-tom-select-options='{
                          "placeholder": "<div><i class=\"bi-person me-2\"></i> Select member</div>",
                          "hideSearch": true,
                          "width": "20rem"
                        }'>
                <option value=""></option>
                <option value="4">Thomas Edison</option>
                <option value="1">Nikola</option>
                <option value="3">Nikola Tesla</option>
                <option value="5">Arnold Schwarzenegger</option>
              </select>
            </div>
            <!-- End Select -->
          </div>

          <div class="tab-pane fade" id="nav-html14" role="tabpanel" aria-labelledby="nav-htmlTab14">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Select --&gt;
                &lt;div class="tom-select-custom"&gt;
                  &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot;
                          data-hs-tom-select-options='{
                              "placeholder": &quot;&lt;div&gt;&lt;i class=\&quot;bi-person me-2\&quot;&gt;&lt;/i&gt; Select member&lt;/div&gt;&quot;,
                              &quot;hideSearch&quot;: true,
                              &quot;width&quot;: &quot;20rem&quot;
                            }'&gt;
                    &lt;option value=&quot;&quot;&gt;&lt;/option&gt;
                    &lt;option value=&quot;4&quot;&gt;Thomas Edison&lt;/option&gt;
                    &lt;option value=&quot;1&quot;&gt;Nikola&lt;/option&gt;
                    &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                    &lt;option value=&quot;5&quot;&gt;Arnold Schwarzenegger&lt;/option&gt;
                  &lt;/select&gt;
                &lt;/div&gt;
                &lt;!-- End Select --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="dropdown-width-example" class="hs-docs-heading">
        Dropdown width <a class="anchorjs-link" href="#dropdown-width-example" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab13" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab13" href="#nav-result13" data-bs-toggle="pill" data-bs-target="#nav-result13" role="tab" aria-controls="nav-result13" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab13" href="#nav-html13" data-bs-toggle="pill" data-bs-target="#nav-html13" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent13">
          <div class="tab-pane fade p-4 show active" id="nav-result13" role="tabpanel" aria-labelledby="nav-resultTab13">
            <div class="input-group">
              <input type="text" class="form-control" name="fullName" placeholder="Search name or emails" aria-label="Search name or emails">

              <!-- Select -->
              <div class="tom-select-custom">
                <select class="js-select form-select" autocomplete="off" data-hs-tom-select-options='{
                                "dropdownWidth": "300px",
                                "dropdownLeft": true
                              }'>
                  <option value="4" selected>Thomas Edison</option>
                  <option value="1">Nikola</option>
                  <option value="3">Nikola Tesla</option>
                  <option value="5">Arnold Schwarzenegger</option>
                </select>
              </div>
              <!-- End Select -->

              <a class="btn btn-primary" href="javascript:;">Invite</a>
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html13" role="tabpanel" aria-labelledby="nav-htmlTab13">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;div class=&quot;input-group&quot;&gt;
                  &lt;input type=&quot;text&quot; class=&quot;form-control&quot; name=&quot;fullName&quot; placeholder=&quot;Search name or emails&quot; aria-label=&quot;Search name or emails&quot; aria-describedby=&quot;fullName&quot;&gt;

                  &lt;!-- Select --&gt;
                  &lt;div class="tom-select-custom"&gt;
                    &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot;
                            data-hs-tom-select-options='{
                                    &quot;dropdownWidth&quot;: &quot;300px&quot;,
                                    &quot;dropdownLeft&quot;: true
                                  }'&gt;
                      &lt;option value=&quot;4&quot; selected&gt;Thomas Edison&lt;/option&gt;
                      &lt;option value=&quot;1&quot;&gt;Nikola&lt;/option&gt;
                      &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                      &lt;option value=&quot;5&quot;&gt;Arnold Schwarzenegger&lt;/option&gt;
                    &lt;/select&gt;
                  &lt;/div&gt;
                  &lt;!-- End Select --&gt;

                  &lt;a class=&quot;btn btn-primary&quot; href=&quot;javascript:;&quot;&gt;Invite&lt;/a&gt;
                &lt;/div&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="with-append-and-prepend-example" class="hs-docs-heading">
        With append and prepend <a class="anchorjs-link" href="#with-append-and-prepend-example" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab15" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab15" href="#nav-result15" data-bs-toggle="pill" data-bs-target="#nav-result15" role="tab" aria-controls="nav-result15" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab15" href="#nav-html15" data-bs-toggle="pill" data-bs-target="#nav-html15" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent15">
          <div class="tab-pane fade p-4 show active" id="nav-result15" role="tabpanel" aria-labelledby="nav-resultTab15">
            <div style="width: 40rem;">
              <!-- Prepend -->
              <div class="input-group mb-3">
                <!-- Select -->
                <div class="tom-select-custom">
                  <select class="js-select form-select" autocomplete="off" data-hs-tom-select-options='{
                                "dropdownWidth": "300px"
                              }'>
                    <option value="4" selected>Thomas Edison</option>
                    <option value="1">Nikola</option>
                    <option value="3">Nikola Tesla</option>
                    <option value="5">Arnold Schwarzenegger</option>
                  </select>
                </div>
                <!-- End Select -->

                <input type="text" class="form-control" name="fullName" placeholder="Search name or emails" aria-label="Search name or emails">

                <a class="btn btn-primary" href="javascript:;">Search</a>
              </div>
              <div>
                <!-- End Prepend -->

                <!-- Append -->
                <div class="input-group">
                  <input type="text" class="form-control" name="fullName" placeholder="Search name or emails" aria-label="Search name or emails">

                  <!-- Select -->
                  <div class="tom-select-custom">
                    <select class="js-select form-select" autocomplete="off" data-hs-tom-select-options='{
                                "dropdownWidth": "300px",
                                "dropdownLeft": true
                              }'>
                      <option value="4" selected>Thomas Edison</option>
                      <option value="1">Nikola</option>
                      <option value="3">Nikola Tesla</option>
                      <option value="5">Arnold Schwarzenegger</option>
                    </select>
                  </div>
                  <!-- End Select -->

                  <a class="btn btn-primary" href="javascript:;">Search</a>
                </div>
                <!-- End Append -->
              </div>
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html15" role="tabpanel" aria-labelledby="nav-htmlTab15">
            <pre>
              <code class="language-markup" data-lang="html">
              &lt;!-- Prepend --&gt;
                &lt;div class=&quot;input-group mb-3&quot;&gt;
                  &lt;!-- Select --&gt;
                  &lt;div class="tom-select-custom"&gt;
                    &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot;
                            data-hs-tom-select-options='{
                                  &quot;dropdownWidth&quot;: &quot;300px&quot;
                                }'&gt;
                      &lt;option value=&quot;4&quot; selected&gt;Thomas Edison&lt;/option&gt;
                      &lt;option value=&quot;1&quot;&gt;Nikola&lt;/option&gt;
                      &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                      &lt;option value=&quot;5&quot;&gt;Arnold Schwarzenegger&lt;/option&gt;
                    &lt;/select&gt;
                  &lt;/div&gt;
                  &lt;!-- End Select --&gt;

                  &lt;input type=&quot;text&quot; class=&quot;form-control&quot; name=&quot;fullName&quot; placeholder=&quot;Search name or emails&quot; aria-label=&quot;Search name or emails&quot; aria-describedby=&quot;fullName&quot;&gt;

                  &lt;a class=&quot;btn btn-primary&quot; href=&quot;javascript:;&quot;&gt;Search&lt;/a&gt;
                &lt;/div&gt;&lt;div&gt;
                &lt;!-- End Prepend --&gt;

                &lt;!-- Append --&gt;
                &lt;div class=&quot;input-group&quot;&gt;
                  &lt;input type=&quot;text&quot; class=&quot;form-control&quot; name=&quot;fullName&quot; placeholder=&quot;Search name or emails&quot; aria-label=&quot;Search name or emails&quot; aria-describedby=&quot;fullName&quot;&gt;

                  &lt;!-- Select --&gt;
                  &lt;div class="tom-select-custom"&gt;
                    &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot;
                            data-hs-tom-select-options='{
                                  &quot;dropdownWidth&quot;: &quot;300px&quot;,
                                  &quot;dropdownLeft&quot;: true
                                }'&gt;
                      &lt;option value=&quot;4&quot; selected&gt;Thomas Edison&lt;/option&gt;
                      &lt;option value=&quot;1&quot;&gt;Nikola&lt;/option&gt;
                      &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                      &lt;option value=&quot;5&quot;&gt;Arnold Schwarzenegger&lt;/option&gt;
                    &lt;/select&gt;
                  &lt;/div&gt;
                  &lt;!-- End Select --&gt;

                  &lt;a class=&quot;btn btn-primary&quot; href=&quot;javascript:;&quot;&gt;Search&lt;/a&gt;
                &lt;/div&gt;
                &lt;!-- End Append --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="multiple-example" class="hs-docs-heading">
        Multiple <a class="anchorjs-link" href="#multiple-example" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab6" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab6" href="#nav-result6" data-bs-toggle="pill" data-bs-target="#nav-result6" role="tab" aria-controls="nav-result6" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab6" href="#nav-html6" data-bs-toggle="pill" data-bs-target="#nav-html6" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent6">
          <div class="tab-pane fade p-6 show active" id="nav-result6" role="tabpanel" aria-labelledby="nav-resultTab6">
            <div style="max-width: 40rem;">
              <!-- Select -->
              <div class="tom-select-custom tom-select-custom-with-tags">
                <select class="js-select form-select" autocomplete="off" multiple data-hs-tom-select-options='{
                          "placeholder": "Select a person..."
                        }'>
                  <option value="">Select a person...</option>
                  <option value="4">Thomas Edison</option>
                  <option value="1">Nikola</option>
                  <option value="3">Nikola Tesla</option>
                  <option value="5">Arnold Schwarzenegger</option>
                </select>
              </div>
              <!-- End Select -->
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html6" role="tabpanel" aria-labelledby="nav-htmlTab6">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Select --&gt;
                &lt;div class="tom-select-custom tom-select-custom-with-tags"&gt;
                  &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot; multiple
                          data-hs-tom-select-options='{
                            "placeholder": "Select a person..."
                          }'&gt;
                    &lt;option value=&quot;&quot;&gt;Select a person...&lt;/option&gt;
                    &lt;option value=&quot;4&quot;&gt;Thomas Edison&lt;/option&gt;
                    &lt;option value=&quot;1&quot;&gt;Nikola&lt;/option&gt;
                    &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                    &lt;option value=&quot;5&quot;&gt;Arnold Schwarzenegger&lt;/option&gt;
                  &lt;/select&gt;
                &lt;/div&gt;
                &lt;!-- End Select --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="multiple-selection-example" class="hs-docs-heading">
        Multiple selection <a class="anchorjs-link" href="#multiple-selection-example" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab2" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab2" href="#nav-result2" data-bs-toggle="pill" data-bs-target="#nav-result2" role="tab" aria-controls="nav-result2" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab2" href="#nav-html2" data-bs-toggle="pill" data-bs-target="#nav-html2" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent2">
          <div class="tab-pane fade p-4 show active" id="nav-result2" role="tabpanel" aria-labelledby="nav-resultTab2">
            <div style="max-width: 40rem;">
              <!-- Select -->
              <div class="tom-select-custom">
                <select class="js-select form-select" autocomplete="off" multiple data-hs-tom-select-options='{
                          "singleMultiple": true,
                          "hideSelected": false,
                          "placeholder": "Select user"
                        }'>
                  <option value="">Select a person...</option>
                  <option value="4" selected>Thomas Edison</option>
                  <option value="1" selected>Nikola</option>
                  <option value="3">Nikola Tesla</option>
                  <option value="5">Arnold Schwarzenegger</option>
                </select>
              </div>
              <!-- End Select -->
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html2" role="tabpanel" aria-labelledby="nav-htmlTab2">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Select --&gt;
                &lt;div class="tom-select-custom"&gt;
                  &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot; multiple
                          data-hs-tom-select-options='{
                            &quot;singleMultiple&quot;: true,
                            &quot;hideSelected&quot;: false,
                            &quot;placeholder&quot;: &quot;Select user&quot;
                          }'&gt;
                     &lt;option value=&quot;&quot;&gt;Select a person...&lt;/option&gt;
                     &lt;option value=&quot;4&quot; selected&gt;Thomas Edison&lt;/option&gt;
                     &lt;option value=&quot;1&quot; selected&gt;Nikola&lt;/option&gt;
                     &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                     &lt;option value=&quot;5&quot;&gt;Arnold Schwarzenegger&lt;/option&gt;
                  &lt;/select&gt;
                &lt;/div&gt;
                &lt;!-- End Select --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="with-search-inside-dropdown-example" class="hs-docs-heading">
        With search inside dropdown <a class="anchorjs-link" href="#with-search-inside-dropdown-example" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab16" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab16" href="#nav-result16" data-bs-toggle="pill" data-bs-target="#nav-result16" role="tab" aria-controls="nav-result16" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab16" href="#nav-html16" data-bs-toggle="pill" data-bs-target="#nav-html16" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent16">
          <div class="tab-pane fade p-4 show active" id="nav-result16" role="tabpanel" aria-labelledby="nav-resultTab16">
            <div style="max-width: 40rem;">
              <!-- Select -->
              <div class="tom-select-custom">
                <select class="js-select form-select" autocomplete="off" data-hs-tom-select-options='{
                              "placeholder": "Select user..."
                            }'>
                  <option value="">Select a person...</option>
                  <option value="4">Thomas Edison</option>
                  <option value="1">Nikola</option>
                  <option value="3">Nikola Tesla</option>
                  <option value="5">Arnold Schwarzenegger</option>
                </select>
              </div>
              <!-- End Select -->
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html16" role="tabpanel" aria-labelledby="nav-htmlTab16">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Select --&gt;
                &lt;div class="tom-select-custom"&gt;
                  &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot;
                          data-hs-tom-select-options='{
                            &quot;placeholder&quot;: &quot;Select user...&quot;
                          }'&gt;
                    &lt;option value=&quot;&quot;&gt;Select a person...&lt;/option&gt;
                    &lt;option value=&quot;4&quot;&gt;Thomas Edison&lt;/option&gt;
                    &lt;option value=&quot;1&quot;&gt;Nikola&lt;/option&gt;
                    &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                    &lt;option value=&quot;5&quot;&gt;Arnold Schwarzenegger&lt;/option&gt;
                  &lt;/select&gt;
                &lt;/div&gt;
                &lt;!-- End Select --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="with-search-inside-select-box-example" class="hs-docs-heading">
        With search inside select box <a class="anchorjs-link" href="#with-search-inside-select-box-example" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab17" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab17" href="#nav-result17" data-bs-toggle="pill" data-bs-target="#nav-result17" role="tab" aria-controls="nav-result17" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab17" href="#nav-html17" data-bs-toggle="pill" data-bs-target="#nav-html17" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent17">
          <div class="tab-pane fade p-4 show active" id="nav-result17" role="tabpanel" aria-labelledby="nav-resultTab17">
            <div style="max-width: 40rem;">
              <!-- Select -->
              <div class="tom-select-custom">
                <select class="js-select form-select" autocomplete="off" data-hs-tom-select-options='{
                          "searchInDropdown": false,
                          "hidePlaceholderOnSearch": true,
                          "placeholder": "Select a person..."
                        }'>
                  <option value="">Select a person...</option>
                  <option value="4">Thomas Edison</option>
                  <option value="1">Nikola</option>
                  <option value="3">Nikola Tesla</option>
                  <option value="5">Arnold Schwarzenegger</option>
                </select>
              </div>
              <!-- End Select -->
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html17" role="tabpanel" aria-labelledby="nav-htmlTab17">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Select --&gt;
                &lt;div class="tom-select-custom"&gt;
                  &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot;
                          data-hs-tom-select-options='{
                            &quot;searchInDropdown&quot;: false,
                            &quot;hidePlaceholderOnSearch&quot;: true,
                            "placeholder": &quot;Select a person...&quot;
                          }'&gt;
                    &lt;option value=&quot;&quot;&gt;Select a person...&lt;/option&gt;
                    &lt;option value=&quot;4&quot;&gt;Thomas Edison&lt;/option&gt;
                    &lt;option value=&quot;1&quot;&gt;Nikola&lt;/option&gt;
                    &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                    &lt;option value=&quot;5&quot;&gt;Arnold Schwarzenegger&lt;/option&gt;
                  &lt;/select&gt;
                &lt;/div&gt;
                &lt;!-- End Select --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="with-icon-example" class="hs-docs-heading">
        With Icon <a class="anchorjs-link" href="#with-icon-example" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab8" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab8" href="#nav-result8" data-bs-toggle="pill" data-bs-target="#nav-result8" role="tab" aria-controls="nav-result8" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab8" href="#nav-html8" data-bs-toggle="pill" data-bs-target="#nav-html8" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent8">
          <div class="tab-pane fade p-4 show active" id="nav-result8" role="tabpanel" aria-labelledby="nav-resultTab8">
            <div style="max-width: 40rem;">
              <!-- Select -->
              <div class="tom-select-custom">
                <select class="js-select form-select">
                  <option value="privacy1" data-option-template='<div class="d-flex align-items-start"><div class="flex-shrink-0"><i class="bi-globe"></i></div><div class="flex-grow-1 ms-2"><span class="d-block fw-semibold">Anyone</span><span class="tom-select-custom-hide small">Visible to anyone who can view your content. Accessible by installed apps.</span></div></div>'>Anyone</option>
                  <option value="privacy2" data-option-template='<div class="d-flex align-items-start"><div class="flex-shrink-0"><i class="bi-lock"></i></div><div class="flex-grow-1 ms-2"><span class="d-block fw-semibold">Only you</span><span class="tom-select-custom-hide small">Only visible to you.</span></div></div>'>Only you</option>
                </select>
              </div>
              <!-- End Select -->
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html8" role="tabpanel" aria-labelledby="nav-htmlTab8">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Select --&gt;
                &lt;div class="tom-select-custom"&gt;
                  &lt;select class=&quot;js-select form-select&quot;&gt;
                    &lt;option value="privacy1" data-option-template='&lt;div class="d-flex align-items-start"&gt;&lt;div class="flex-shrink-0"&gt;&lt;i class="bi-globe"&gt;&lt;/i&gt;&lt;/div&gt;&lt;div class="flex-grow-1 ms-2"&gt;&lt;span class="d-block fw-semibold"&gt;Anyone&lt;/span&gt;&lt;span class="tom-select-custom-hide small"&gt;Visible to anyone who can view your content. Accessible by installed apps.&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;'&gt;Anyone&lt;/option&gt;
                    &lt;option value="privacy2" data-option-template='&lt;div class="d-flex align-items-start"&gt;&lt;div class="flex-shrink-0"&gt;&lt;i class="bi-lock"&gt;&lt;/i&gt;&lt;/div&gt;&lt;div class="flex-grow-1 ms-2"&gt;&lt;span class="d-block fw-semibold"&gt;Only you&lt;/span&gt;&lt;span class="tom-select-custom-hide small"&gt;Only visible to you.&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;'&gt;Only you&lt;/option&gt;
                  &lt;/select&gt;
                &lt;/div&gt;
                &lt;!-- End Select --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="optgroup-example" class="hs-docs-heading">
        Optgroup <a class="anchorjs-link" href="#optgroup-example" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab3" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab3" href="#nav-result3" data-bs-toggle="pill" data-bs-target="#nav-result3" role="tab" aria-controls="nav-result3" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab3" href="#nav-html3" data-bs-toggle="pill" data-bs-target="#nav-html3" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent3">
          <div class="tab-pane fade p-4 show active" id="nav-result3" role="tabpanel" aria-labelledby="nav-resultTab3">
            <div style="max-width: 40rem;">
              <!-- Select -->
              <div class="tom-select-custom">
                <select class="js-select form-select" autocomplete="off" data-hs-tom-select-options='{
                          "placeholder": "Select gear"
                        }'>
                  <option value="">Select gear...</option>
                  <optgroup label="Climbing">
                    <option value="pitons">Pitons</option>
                    <option value="cams">Cams</option>
                    <option value="nuts">Nuts</option>
                    <option value="bolts">Bolts</option>
                    <option value="stoppers">Stoppers</option>
                    <option value="sling">Sling</option>
                  </optgroup>
                  <optgroup label="Skiing">
                    <option value="skis">Skis</option>
                    <option value="skins">Skins</option>
                    <option value="poles">Poles</option>
                  </optgroup>
                </select>
              </div>
              <!-- End Select -->
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html3" role="tabpanel" aria-labelledby="nav-htmlTab3">
            <pre>
              <code class="language-markup" data-lang="html">
               &lt;!-- Select --&gt;
               &lt;div class="tom-select-custom"&gt;
                &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot;
                        data-hs-tom-select-options='{
                          "placeholder": &quot;Select a person...&quot;
                        }'&gt;
                  &lt;option value=&quot;&quot;&gt;Select gear...&lt;/option&gt;
                  &lt;optgroup label=&quot;Climbing&quot;&gt;
                    &lt;option value=&quot;pitons&quot;&gt;Pitons&lt;/option&gt;
                    &lt;option value=&quot;cams&quot;&gt;Cams&lt;/option&gt;
                    &lt;option value=&quot;nuts&quot;&gt;Nuts&lt;/option&gt;
                    &lt;option value=&quot;bolts&quot;&gt;Bolts&lt;/option&gt;
                    &lt;option value=&quot;stoppers&quot;&gt;Stoppers&lt;/option&gt;
                    &lt;option value=&quot;sling&quot;&gt;Sling&lt;/option&gt;
                  &lt;/optgroup&gt;
                  &lt;optgroup label=&quot;Skiing&quot;&gt;
                    &lt;option value=&quot;skis&quot;&gt;Skis&lt;/option&gt;
                    &lt;option value=&quot;skins&quot;&gt;Skins&lt;/option&gt;
                    &lt;option value=&quot;poles&quot;&gt;Poles&lt;/option&gt;
                  &lt;/optgroup&gt;
                &lt;/select&gt;
              &lt;/div&gt;
              &lt;!-- End Select --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="custom-tags-example" class="hs-docs-heading">
        Custom tags <a class="anchorjs-link" href="#custom-tags-example" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab5" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab5" href="#nav-result5" data-bs-toggle="pill" data-bs-target="#nav-result5" role="tab" aria-controls="nav-result5" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab5" href="#nav-html5" data-bs-toggle="pill" data-bs-target="#nav-html5" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent5">
          <div class="tab-pane fade p-5 show active" id="nav-result5" role="tabpanel" aria-labelledby="nav-resultTab5">
            <div style="max-width: 40rem;">
              <!-- Select -->
              <div class="tom-select-custom">
                <select class="js-select form-select" autocomplete="off" multiple data-hs-tom-select-options='{
                    "create": true,
                    "placeholder": "Create custom tag..."
                  }'>
                </select>
              </div>
              <!-- End Select -->
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html5" role="tabpanel" aria-labelledby="nav-htmlTab5">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Select --&gt;
                &lt;div class="tom-select-custom"&gt;
                  &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot; multiple
                    data-hs-tom-select-options='{
                      &quot;create&quot;: true,
                      "placeholder": "Create custom tag..."
                    }'&gt;
                  &lt;/select&gt;
                &lt;/div&gt;
                &lt;!-- End Select --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="with-images-example" class="hs-docs-heading">
        With images <a class="anchorjs-link" href="#with-images-example" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab4" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab4" href="#nav-result4" data-bs-toggle="pill" data-bs-target="#nav-result4" role="tab" aria-controls="nav-result4" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab4" href="#nav-html4" data-bs-toggle="pill" data-bs-target="#nav-html4" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent4">
          <div class="tab-pane fade p-4 show active" id="nav-result4" role="tabpanel" aria-labelledby="nav-resultTab4">
            <div style="max-width: 40rem;">
              <!-- Select -->
              <div class="tom-select-custom">
                <select class="js-select form-select" id="locationLabel">
                  <option value="AF" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/af.svg" alt="Afghanistan Flag" /><span class="text-truncate">Afghanistan</span></span>'>Afghanistan</option>
                  <option value="AX" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ax.svg" alt="Aland Islands Flag" /><span class="text-truncate">Aland Islands</span></span>'>Aland Islands</option>
                  <option value="AL" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/al.svg" alt="Albania Flag" /><span class="text-truncate">Albania</span></span>'>Albania</option>
                  <option value="DZ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/dz.svg" alt="Algeria Flag" /><span class="text-truncate">Algeria</span></span>'>Algeria</option>
                  <option value="AS" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/as.svg" alt="American Samoa Flag" /><span class="text-truncate">American Samoa</span></span>'>American Samoa</option>
                  <option value="AD" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ad.svg" alt="Andorra Flag" /><span class="text-truncate">Andorra</span></span>'>Andorra</option>
                  <option value="AO" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ao.svg" alt="Angola Flag" /><span class="text-truncate">Angola</span></span>'>Angola</option>
                  <option value="AI" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ai.svg" alt="Anguilla Flag" /><span class="text-truncate">Anguilla</span></span>'>Anguilla</option>
                  <option value="AG" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ag.svg" alt="Antigua and Barbuda Flag" /><span class="text-truncate">Antigua and Barbuda</span></span>'>Antigua and Barbuda</option>
                  <option value="AR" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ar.svg" alt="Argentina Flag" /><span class="text-truncate">Argentina</span></span>'>Argentina</option>
                  <option value="AM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/am.svg" alt="Armenia Flag" /><span class="text-truncate">Armenia</span></span>'>Armenia</option>
                  <option value="AW" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/aw.svg" alt="Aruba Flag" /><span class="text-truncate">Aruba</span></span>'>Aruba</option>
                  <option value="AU" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/au.svg" alt="Australia Flag" /><span class="text-truncate">Australia</span></span>'>Australia</option>
                  <option value="AT" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/at.svg" alt="Austria Flag" /><span class="text-truncate">Austria</span></span>'>Austria</option>
                  <option value="AZ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/az.svg" alt="Azerbaijan Flag" /><span class="text-truncate">Azerbaijan</span></span>'>Azerbaijan</option>
                  <option value="BS" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bs.svg" alt="Bahamas Flag" /><span class="text-truncate">Bahamas</span></span>'>Bahamas</option>
                  <option value="BH" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bh.svg" alt="Bahrain Flag" /><span class="text-truncate">Bahrain</span></span>'>Bahrain</option>
                  <option value="BD" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bd.svg" alt="Bangladesh Flag" /><span class="text-truncate">Bangladesh</span></span>'>Bangladesh</option>
                  <option value="BB" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bb.svg" alt="Barbados Flag" /><span class="text-truncate">Barbados</span></span>'>Barbados</option>
                  <option value="BY" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/by.svg" alt="Belarus Flag" /><span class="text-truncate">Belarus</span></span>'>Belarus</option>
                  <option value="BE" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/be.svg" alt="Belgium Flag" /><span class="text-truncate">Belgium</span></span>'>Belgium</option>
                  <option value="BZ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bz.svg" alt="Belize Flag" /><span class="text-truncate">Belize</span></span>'>Belize</option>
                  <option value="BJ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bj.svg" alt="Benin Flag" /><span class="text-truncate">Benin</span></span>'>Benin</option>
                  <option value="BM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bm.svg" alt="Bermuda Flag" /><span class="text-truncate">Bermuda</span></span>'>Bermuda</option>
                  <option value="BT" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bt.svg" alt="Bhutan Flag" /><span class="text-truncate">Bhutan</span></span>'>Bhutan</option>
                  <option value="BO" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bo.svg" alt="Bolivia (Plurinational State of) Flag" /><span class="text-truncate">Bolivia (Plurinational State of)</span></span>'>Bolivia (Plurinational State of)</option>
                  <option value="BQ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bq.svg" alt="Bonaire, Sint Eustatius and Saba Flag" /><span class="text-truncate">Bonaire, Sint Eustatius and Saba</span></span>'>Bonaire, Sint Eustatius and Saba</option>
                  <option value="BA" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ba.svg" alt="Bosnia and Herzegovina Flag" /><span class="text-truncate">Bosnia and Herzegovina</span></span>'>Bosnia and Herzegovina</option>
                  <option value="BW" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bw.svg" alt="Botswana Flag" /><span class="text-truncate">Botswana</span></span>'>Botswana</option>
                  <option value="BR" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/br.svg" alt="Brazil Flag" /><span class="text-truncate">Brazil</span></span>'>Brazil</option>
                  <option value="IO" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/io.svg" alt="British Indian Ocean Territory Flag" /><span class="text-truncate">British Indian Ocean Territory</span></span>'>British Indian Ocean Territory</option>
                  <option value="BN" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bn.svg" alt="Brunei Darussalam Flag" /><span class="text-truncate">Brunei Darussalam</span></span>'>Brunei Darussalam</option>
                  <option value="BG" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bg.svg" alt="Bulgaria Flag" /><span class="text-truncate">Bulgaria</span></span>'>Bulgaria</option>
                  <option value="BF" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bf.svg" alt="Burkina Faso Flag" /><span class="text-truncate">Burkina Faso</span></span>'>Burkina Faso</option>
                  <option value="BI" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bi.svg" alt="Burundi Flag" /><span class="text-truncate">Burundi</span></span>'>Burundi</option>
                  <option value="CV" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/cv.svg" alt="Cabo Verde Flag" /><span class="text-truncate">Cabo Verde</span></span>'>Cabo Verde</option>
                  <option value="KH" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/kh.svg" alt="Cambodia Flag" /><span class="text-truncate">Cambodia</span></span>'>Cambodia</option>
                  <option value="CM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/cm.svg" alt="Cameroon Flag" /><span class="text-truncate">Cameroon</span></span>'>Cameroon</option>
                  <option value="CA" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ca.svg" alt="Canada Flag" /><span class="text-truncate">Canada</span></span>'>Canada</option>
                  <option value="KY" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ky.svg" alt="Cayman Islands Flag" /><span class="text-truncate">Cayman Islands</span></span>'>Cayman Islands</option>
                  <option value="CF" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle shape-4.svg2" src="../assets/vendor/flag-icon-css/flags/1x1/cf.svg" alt="Central African Republic Flag" /><span class="text-truncate">Central African Republic</span></span>'>Central African Republic</option>
                  <option value="TD" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/td.svg" alt="Chad Flag" /><span class="text-truncate">Chad</span></span>'>Chad</option>
                  <option value="CL" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/cl.svg" alt="Chile Flag" /><span class="text-truncate">Chile</span></span>'>Chile</option>
                  <option value="CN" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/cn.svg" alt="China Flag" /><span class="text-truncate">China</span></span>'>China</option>
                  <option value="CX" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/cx.svg" alt="Christmas Island Flag" /><span class="text-truncate">Christmas Island</span></span>'>Christmas Island</option>
                  <option value="CC" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/cc.svg" alt="Cocos (Keeling) Islands Flag" /><span class="text-truncate">Cocos (Keeling) Islands</span></span>'>Cocos (Keeling) Islands</option>
                  <option value="CO" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/co.svg" alt="Colombia Flag" /><span class="text-truncate">Colombia</span></span>'>Colombia</option>
                  <option value="KM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/km.svg" alt="Comoros Flag" /><span class="text-truncate">Comoros</span></span>'>Comoros</option>
                  <option value="CK" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ck.svg" alt="Cook Islands Flag" /><span class="text-truncate">Cook Islands</span></span>'>Cook Islands</option>
                  <option value="CR" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/cr.svg" alt="Costa Rica Flag" /><span class="text-truncate">Costa Rica</span></span>'>Costa Rica</option>
                  <option value="HR" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/hr.svg" alt="Croatia Flag" /><span class="text-truncate">Croatia</span></span>'>Croatia</option>
                  <option value="CU" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/cu.svg" alt="Cuba Flag" /><span class="text-truncate">Cuba</span></span>'>Cuba</option>
                  <option value="CW" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/cw.svg" alt="Curaçao Flag" /><span class="text-truncate">Curaçao</span></span>'>Curaçao</option>
                  <option value="CY" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/cy.svg" alt="Cyprus Flag" /><span class="text-truncate">Cyprus</span></span>'>Cyprus</option>
                  <option value="CZ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/cz.svg" alt="Czech Republic Flag" /><span class="text-truncate">Czech Republic</span></span>'>Czech Republic</option>
                  <option value="CI" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ci.svg" alt=Côte d&apos;Ivoire Flag" /><span class="text-truncate">Côte d&apos;Ivoire</span></span>'>Côte d'Ivoire</option>
                  <option value="CD" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/cd.svg" alt="Democratic Republic of the Congo Flag" /><span class="text-truncate">Democratic Republic of the Congo</span></span>'>Democratic Republic of the Congo</option>
                  <option value="DK" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/dk.svg" alt="Denmark Flag" /><span class="text-truncate">Denmark</span></span>'>Denmark</option>
                  <option value="DJ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/dj.svg" alt="Djibouti Flag" /><span class="text-truncate">Djibouti</span></span>'>Djibouti</option>
                  <option value="DM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/dm.svg" alt="Dominica Flag" /><span class="text-truncate">Dominica</span></span>'>Dominica</option>
                  <option value="DO" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/do.svg" alt="Dominican Republic Flag" /><span class="text-truncate">Dominican Republic</span></span>'>Dominican Republic</option>
                  <option value="EC" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ec.svg" alt="Ecuador Flag" /><span class="text-truncate">Ecuador</span></span>'>Ecuador</option>
                  <option value="EG" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/eg.svg" alt="Egypt Flag" /><span class="text-truncate">Egypt</span></span>'>Egypt</option>
                  <option value="SV" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sv.svg" alt="El Salvador Flag" /><span class="text-truncate">El Salvador</span></span>'>El Salvador</option>
                  <option value="GB-ENG" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gb-eng.svg" alt="England Flag" /><span class="text-truncate">England</span></span>'>England</option>
                  <option value="GQ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gq.svg" alt="Equatorial Guinea Flag" /><span class="text-truncate">Equatorial Guinea</span></span>'>Equatorial Guinea</option>
                  <option value="ER" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/er.svg" alt="Eritrea Flag" /><span class="text-truncate">Eritrea</span></span>'>Eritrea</option>
                  <option value="EE" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ee.svg" alt="Estonia Flag" /><span class="text-truncate">Estonia</span></span>'>Estonia</option>
                  <option value="ET" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/et.svg" alt="Ethiopia Flag" /><span class="text-truncate">Ethiopia</span></span>'>Ethiopia</option>
                  <option value="FK" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/fk.svg" alt="Falkland Islands Flag" /><span class="text-truncate">Falkland Islands</span></span>'>Falkland Islands</option>
                  <option value="FO" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/fo.svg" alt="Faroe Islands Flag" /><span class="text-truncate">Faroe Islands</span></span>'>Faroe Islands</option>
                  <option value="FM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/fm.svg" alt="Federated States of Micronesia Flag" /><span class="text-truncate">Federated States of Micronesia</span></span>'>Federated States of Micronesia</option>
                  <option value="FJ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/fj.svg" alt="Fiji Flag" /><span class="text-truncate">Fiji</span></span>'>Fiji</option>
                  <option value="FI" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/fi.svg" alt="Finland Flag" /><span class="text-truncate">Finland</span></span>'>Finland</option>
                  <option value="FR" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/fr.svg" alt="France Flag" /><span class="text-truncate">France</span></span>'>France</option>
                  <option value="GF" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gf.svg" alt="French Guiana Flag" /><span class="text-truncate">French Guiana</span></span>'>French Guiana</option>
                  <option value="PF" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/pf.svg" alt="French Polynesia Flag" /><span class="text-truncate">French Polynesia</span></span>'>French Polynesia</option>
                  <option value="TF" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/tf.svg" alt="French Southern Territories Flag" /><span class="text-truncate">French Southern Territories</span></span>'>French Southern Territories</option>
                  <option value="GA" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ga.svg" alt="Gabon Flag" /><span class="text-truncate">Gabon</span></span>'>Gabon</option>
                  <option value="GM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gm.svg" alt="Gambia Flag" /><span class="text-truncate">Gambia</span></span>'>Gambia</option>
                  <option value="GE" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ge.svg" alt="Georgia Flag" /><span class="text-truncate">Georgia</span></span>'>Georgia</option>
                  <option value="DE" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/de.svg" alt="Germany Flag" /><span class="text-truncate">Germany</span></span>'>Germany</option>
                  <option value="GH" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gh.svg" alt="Ghana Flag" /><span class="text-truncate">Ghana</span></span>'>Ghana</option>
                  <option value="GI" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gi.svg" alt="Gibraltar Flag" /><span class="text-truncate">Gibraltar</span></span>'>Gibraltar</option>
                  <option value="GR" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gr.svg" alt="Greece Flag" /><span class="text-truncate">Greece</span></span>'>Greece</option>
                  <option value="GL" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gl.svg" alt="Greenland Flag" /><span class="text-truncate">Greenland</span></span>'>Greenland</option>
                  <option value="GD" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gd.svg" alt="Grenada Flag" /><span class="text-truncate">Grenada</span></span>'>Grenada</option>
                  <option value="GP" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gp.svg" alt="Guadeloupe Flag" /><span class="text-truncate">Guadeloupe</span></span>'>Guadeloupe</option>
                  <option value="GU" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gu.svg" alt="Guam Flag" /><span class="text-truncate">Guam</span></span>'>Guam</option>
                  <option value="GT" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gt.svg" alt="Guatemala Flag" /><span class="text-truncate">Guatemala</span></span>'>Guatemala</option>
                  <option value="GG" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gg.svg" alt="Guernsey Flag" /><span class="text-truncate">Guernsey</span></span>'>Guernsey</option>
                  <option value="GN" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gn.svg" alt="Guinea Flag" /><span class="text-truncate">Guinea</span></span>'>Guinea</option>
                  <option value="GW" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gw.svg" alt="Guinea-Bissau Flag" /><span class="text-truncate">Guinea-Bissau</span></span>'>Guinea-Bissau</option>
                  <option value="GY" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gy.svg" alt="Guyana Flag" /><span class="text-truncate">Guyana</span></span>'>Guyana</option>
                  <option value="HT" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ht.svg" alt="Haiti Flag" /><span class="text-truncate">Haiti</span></span>'>Haiti</option>
                  <option value="VA" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/va.svg" alt="Holy See Flag" /><span class="text-truncate">Holy See</span></span>'>Holy See</option>
                  <option value="HN" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/hn.svg" alt="Honduras Flag" /><span class="text-truncate">Honduras</span></span>'>Honduras</option>
                  <option value="HK" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/hk.svg" alt="Hong Kong Flag" /><span class="text-truncate">Hong Kong</span></span>'>Hong Kong</option>
                  <option value="HU" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/hu.svg" alt="Hungary Flag" /><span class="text-truncate">Hungary</span></span>'>Hungary</option>
                  <option value="IS" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/is.svg" alt="Iceland Flag" /><span class="text-truncate">Iceland</span></span>'>Iceland</option>
                  <option value="IN" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/in.svg" alt="India Flag" /><span class="text-truncate">India</span></span>'>India</option>
                  <option value="ID" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/id.svg" alt="Indonesia Flag" /><span class="text-truncate">Indonesia</span></span>'>Indonesia</option>
                  <option value="IR" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ir.svg" alt="Iran (Islamic Republic of) Flag" /><span class="text-truncate">Iran (Islamic Republic of)</span></span>'>Iran (Islamic Republic of)</option>
                  <option value="IQ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/iq.svg" alt="Iraq Flag" /><span class="text-truncate">Iraq</span></span>'>Iraq</option>
                  <option value="IE" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ie.svg" alt="Ireland Flag" /><span class="text-truncate">Ireland</span></span>'>Ireland</option>
                  <option value="IM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/im.svg" alt="Isle of Man Flag" /><span class="text-truncate">Isle of Man</span></span>'>Isle of Man</option>
                  <option value="IL" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/il.svg" alt="Israel Flag" /><span class="text-truncate">Israel</span></span>'>Israel</option>
                  <option value="IT" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/it.svg" alt="Italy Flag" /><span class="text-truncate">Italy</span></span>'>Italy</option>
                  <option value="JM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/jm.svg" alt="Jamaica Flag" /><span class="text-truncate">Jamaica</span></span>'>Jamaica</option>
                  <option value="JP" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/jp.svg" alt="Japan Flag" /><span class="text-truncate">Japan</span></span>'>Japan</option>
                  <option value="JE" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/je.svg" alt="Jersey Flag" /><span class="text-truncate">Jersey</span></span>'>Jersey</option>
                  <option value="JO" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/jo.svg" alt="Jordan Flag" /><span class="text-truncate">Jordan</span></span>'>Jordan</option>
                  <option value="KZ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/kz.svg" alt="Kazakhstan Flag" /><span class="text-truncate">Kazakhstan</span></span>'>Kazakhstan</option>
                  <option value="KE" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ke.svg" alt="Kenya Flag" /><span class="text-truncate">Kenya</span></span>'>Kenya</option>
                  <option value="KI" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ki.svg" alt="Kiribati Flag" /><span class="text-truncate">Kiribati</span></span>'>Kiribati</option>
                  <option value="KW" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/kw.svg" alt="Kuwait Flag" /><span class="text-truncate">Kuwait</span></span>'>Kuwait</option>
                  <option value="KG" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/kg.svg" alt="Kyrgyzstan Flag" /><span class="text-truncate">Kyrgyzstan</span></span>'>Kyrgyzstan</option>
                  <option value="LA" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/la.svg" alt="Laos Flag" /><span class="text-truncate">Laos</span></span>'>Laos</option>
                  <option value="LV" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/lv.svg" alt="Latvia Flag" /><span class="text-truncate">Latvia</span></span>'>Latvia</option>
                  <option value="LB" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/lb.svg" alt="Lebanon Flag" /><span class="text-truncate">Lebanon</span></span>'>Lebanon</option>
                  <option value="LS" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ls.svg" alt="Lesotho Flag" /><span class="text-truncate">Lesotho</span></span>'>Lesotho</option>
                  <option value="LR" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/lr.svg" alt="Liberia Flag" /><span class="text-truncate">Liberia</span></span>'>Liberia</option>
                  <option value="LY" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ly.svg" alt="Libya Flag" /><span class="text-truncate">Libya</span></span>'>Libya</option>
                  <option value="LI" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/li.svg" alt="Liechtenstein Flag" /><span class="text-truncate">Liechtenstein</span></span>'>Liechtenstein</option>
                  <option value="LT" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/lt.svg" alt="Lithuania Flag" /><span class="text-truncate">Lithuania</span></span>'>Lithuania</option>
                  <option value="LU" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/lu.svg" alt="Luxembourg Flag" /><span class="text-truncate">Luxembourg</span></span>'>Luxembourg</option>
                  <option value="MO" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mo.svg" alt="Macau Flag" /><span class="text-truncate">Macau</span></span>'>Macau</option>
                  <option value="MG" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mg.svg" alt="Madagascar Flag" /><span class="text-truncate">Madagascar</span></span>'>Madagascar</option>
                  <option value="MW" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mw.svg" alt="Malawi Flag" /><span class="text-truncate">Malawi</span></span>'>Malawi</option>
                  <option value="MY" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/my.svg" alt="Malaysia Flag" /><span class="text-truncate">Malaysia</span></span>'>Malaysia</option>
                  <option value="MV" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mv.svg" alt="Maldives Flag" /><span class="text-truncate">Maldives</span></span>'>Maldives</option>
                  <option value="ML" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ml.svg" alt="Mali Flag" /><span class="text-truncate">Mali</span></span>'>Mali</option>
                  <option value="MT" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mt.svg" alt="Malta Flag" /><span class="text-truncate">Malta</span></span>'>Malta</option>
                  <option value="MH" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mh.svg" alt="Marshall Islands Flag" /><span class="text-truncate">Marshall Islands</span></span>'>Marshall Islands</option>
                  <option value="MQ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mq.svg" alt="Martinique Flag" /><span class="text-truncate">Martinique</span></span>'>Martinique</option>
                  <option value="MR" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mr.svg" alt="Mauritania Flag" /><span class="text-truncate">Mauritania</span></span>'>Mauritania</option>
                  <option value="MU" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mu.svg" alt="Mauritius Flag" /><span class="text-truncate">Mauritius</span></span>'>Mauritius</option>
                  <option value="YT" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/yt.svg" alt="Mayotte Flag" /><span class="text-truncate">Mayotte</span></span>'>Mayotte</option>
                  <option value="MX" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mx.svg" alt="Mexico Flag" /><span class="text-truncate">Mexico</span></span>'>Mexico</option>
                  <option value="MD" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/md.svg" alt="Moldova Flag" /><span class="text-truncate">Moldova</span></span>'>Moldova</option>
                  <option value="MC" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mc.svg" alt="Monaco Flag" /><span class="text-truncate">Monaco</span></span>'>Monaco</option>
                  <option value="MN" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mn.svg" alt="Mongolia Flag" /><span class="text-truncate">Mongolia</span></span>'>Mongolia</option>
                  <option value="ME" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/me.svg" alt="Montenegro Flag" /><span class="text-truncate">Montenegro</span></span>'>Montenegro</option>
                  <option value="MS" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ms.svg" alt="Montserrat Flag" /><span class="text-truncate">Montserrat</span></span>'>Montserrat</option>
                  <option value="MA" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ma.svg" alt="Morocco Flag" /><span class="text-truncate">Morocco</span></span>'>Morocco</option>
                  <option value="MZ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mz.svg" alt="Mozambique Flag" /><span class="text-truncate">Mozambique</span></span>'>Mozambique</option>
                  <option value="MM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mm.svg" alt="Myanmar Flag" /><span class="text-truncate">Myanmar</span></span>'>Myanmar</option>
                  <option value="NA" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/na.svg" alt="Namibia Flag" /><span class="text-truncate">Namibia</span></span>'>Namibia</option>
                  <option value="NR" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/nr.svg" alt="Nauru Flag" /><span class="text-truncate">Nauru</span></span>'>Nauru</option>
                  <option value="NP" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/np.svg" alt="Nepal Flag" /><span class="text-truncate">Nepal</span></span>'>Nepal</option>
                  <option value="NL" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/nl.svg" alt="Netherlands Flag" /><span class="text-truncate">Netherlands</span></span>'>Netherlands</option>
                  <option value="NC" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/nc.svg" "alt="New Caledonia Flag" /><span class="text-truncate">New Caledonia</span></span>'>New Caledonia</option>
                  <option value="NZ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/nz.svg" alt="New Zealand Flag" /><span class="text-truncate">New Zealand</span></span>'>New Zealand</option>
                  <option value="NI" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ni.svg" alt="Nicaragua Flag" /><span class="text-truncate">Nicaragua</span></span>'>Nicaragua</option>
                  <option value="NE" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ne.svg" alt="Niger Flag" /><span class="text-truncate">Niger</span></span>'>Niger</option>
                  <option value="NG" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ng.svg" alt="Nigeria Flag" /><span class="text-truncate">Nigeria</span></span>'>Nigeria</option>
                  <option value="NU" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/nu.svg" alt="Niue Flag" /><span class="text-truncate">Niue</span></span>'>Niue</option>
                  <option value="NF" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/nf.svg" alt="Norfolk Island Flag" /><span class="text-truncate">Norfolk Island</span></span>'>Norfolk Island</option>
                  <option value="KP" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/kp.svg" alt="North Korea Flag" /><span class="text-truncate">North Korea</span></span>'>North Korea</option>
                  <option value="MK" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mk.svg" alt="North Macedonia Flag" /><span class="text-truncate">North Macedonia</span></span>'>North Macedonia</option>
                  <option value="GB-NIR" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gb-nir.svg" alt="Northern Ireland Flag" /><span class="text-truncate">Northern Ireland</span></span>'>Northern Ireland</option>
                  <option value="MP" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mp.svg" alt="Northern Mariana Islands Flag" /><span class="text-truncate">Northern Mariana Islands</span></span>'>Northern Mariana Islands</option>
                  <option value="NO" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/no.svg" alt="Norway Flag" /><span class="text-truncate">Norway</span></span>'>Norway</option>
                  <option value="OM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/om.svg" alt="Oman Flag" /><span class="text-truncate">Oman</span></span>'>Oman</option>
                  <option value="PK" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/pk.svg" alt="Pakistan Flag" /><span class="text-truncate">Pakistan</span></span>'>Pakistan</option>
                  <option value="PW" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/pw.svg" alt="Palau Flag" /><span class="text-truncate">Palau</span></span>'>Palau</option>
                  <option value="PA" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/pa.svg" alt="Panama Flag" /><span class="text-truncate">Panama</span></span>'>Panama</option>
                  <option value="PG" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/pg.svg" alt="Papua New Guinea Flag" /><span class="text-truncate">Papua New Guinea</span></span>'>Papua New Guinea</option>
                  <option value="PY" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/py.svg" alt="Paraguay Flag" /><span class="text-truncate">Paraguay</span></span>'>Paraguay</option>
                  <option value="PE" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/pe.svg" alt="Peru Flag" /><span class="text-truncate">Peru</span></span>'>Peru</option>
                  <option value="PH" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ph.svg" alt="Philippines Flag" /><span class="text-truncate">Philippines</span></span>'>Philippines</option>
                  <option value="PN" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/pn.svg" alt="Pitcairn Flag" /><span class="text-truncate">Pitcairn</span></span>'>Pitcairn</option>
                  <option value="PL" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/pl.svg" alt="Poland Flag" /><span class="text-truncate">Poland</span></span>'>Poland</option>
                  <option value="PT" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/pt.svg" alt="Portugal Flag" /><span class="text-truncate">Portugal</span></span>'>Portugal</option>
                  <option value="PR" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/pr.svg" alt="Puerto Rico Flag" /><span class="text-truncate">Puerto Rico</span></span>'>Puerto Rico</option>
                  <option value="QA" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/qa.svg" alt="Qatar Flag" /><span class="text-truncate">Qatar</span></span>'>Qatar</option>
                  <option value="CG" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/cg.svg" alt="Republic of the Congo Flag" /><span class="text-truncate">Republic of the Congo</span></span>'>Republic of the Congo</option>
                  <option value="RO" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ro.svg" alt="Romania Flag" /><span class="text-truncate">Romania</span></span>'>Romania</option>
                  <option value="RU" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ru.svg" alt="Russia Flag" /><span class="text-truncate">Russia</span></span>'>Russia</option>
                  <option value="RW" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/rw.svg" alt="Rwanda Flag" /><span class="text-truncate">Rwanda</span></span>'>Rwanda</option>
                  <option value="RE" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/re.svg" alt="Réunion Flag" /><span class="text-truncate">Réunion</span></span>'>Réunion</option>
                  <option value="BL" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/bl.svg" alt="Saint Barthélemy Flag" /><span class="text-truncate">Saint Barthélemy</span></span>'>Saint Barthélemy</option>
                  <option value="SH" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sh.svg" alt="Saint Helena, Ascension and Tristan da Cunha Flag" /><span class="text-truncate">Saint Helena, Ascension and Tristan da Cunha</span></span>'>Saint Helena, Ascension and Tristan da Cunha</option>
                  <option value="KN" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/kn.svg" alt="Saint Kitts and Nevis Flag" /><span class="text-truncate">Saint Kitts and Nevis</span></span>'>Saint Kitts and Nevis</option>
                  <option value="LC" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/lc.svg" alt="Saint Lucia Flag" /><span class="text-truncate">Saint Lucia</span></span>'>Saint Lucia</option>
                  <option value="MF" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/mf.svg" alt="Saint Martin Flag" /><span class="text-truncate">Saint Martin</span></span>'>Saint Martin</option>
                  <option value="PM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/pm.svg" alt="Saint Pierre and Miquelon Flag" /><span class="text-truncate">Saint Pierre and Miquelon</span></span>'>Saint Pierre and Miquelon</option>
                  <option value="VC" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/vc.svg" alt="Saint Vincent and the Grenadines Flag" /><span class="text-truncate">Saint Vincent and the Grenadines</span></span>'>Saint Vincent and the Grenadines</option>
                  <option value="WS" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ws.svg" alt="Samoa Flag" /><span class="text-truncate">Samoa</span></span>'>Samoa</option>
                  <option value="SM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sm.svg" "alt="San Marino Flag" /><span class="text-truncate">San Marino</span></span>'>San Marino</option>
                  <option value="ST" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/st.svg" "alt="Sao Tome and Principe Flag" /><span class="text-truncate">Sao Tome and Principe</span></span>'>Sao Tome and Principe</option>
                  <option value="SA" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sa.svg" alt="Saudi Arabia Flag" /><span class="text-truncate">Saudi Arabia</span></span>'>Saudi Arabia</option>
                  <option value="GB-SCT" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gb-sct.svg" alt="Scotland Flag" /><span class="text-truncate">Scotland</span></span>'>Scotland</option>
                  <option value="SN" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sn.svg" alt="Senegal Flag" /><span class="text-truncate">Senegal</span></span>'>Senegal</option>
                  <option value="RS" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/rs.svg" alt="Serbia Flag" /><span class="text-truncate">Serbia</span></span>'>Serbia</option>
                  <option value="SC" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sc.svg" alt="Seychelles Flag" /><span class="text-truncate">Seychelles</span></span>'>Seychelles</option>
                  <option value="SL" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sl.svg" alt="Sierra Leone Flag" /><span class="text-truncate">Sierra Leone</span></span>'>Sierra Leone</option>
                  <option value="SG" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sg.svg" alt="Singapore Flag" /><span class="text-truncate">Singapore</span></span>'>Singapore</option>
                  <option value="SX" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sx.svg" alt="Sint Maarten Flag" /><span class="text-truncate">Sint Maarten</span></span>'>Sint Maarten</option>
                  <option value="SK" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sk.svg" alt="Slovakia Flag" /><span class="text-truncate">Slovakia</span></span>'>Slovakia</option>
                  <option value="SI" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/si.svg" alt="Slovenia Flag" /><span class="text-truncate">Slovenia</span></span>'>Slovenia</option>
                  <option value="SB" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sb.svg" alt="Solomon Islands Flag" /><span class="text-truncate">Solomon Islands</span></span>'>Solomon Islands</option>
                  <option value="SO" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/so.svg" alt="Somalia Flag" /><span class="text-truncate">Somalia</span></span>'>Somalia</option>
                  <option value="ZA" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/za.svg" alt="South Africa Flag" /><span class="text-truncate">South Africa</span></span>'>South Africa</option>
                  <option value="GS" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gs.svg" alt="South Georgia and the South Sandwich Islands Flag" /><span class="text-truncate">South Georgia and the South Sandwich Islands</span></span>'>South Georgia and the South Sandwich Islands</option>
                  <option value="KR" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/kr.svg" alt="South Korea Flag" /><span class="text-truncate">South Korea</span></span>'>South Korea</option>
                  <option value="SS" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ss.svg" alt="South Sudan Flag" /><span class="text-truncate">South Sudan</span></span>'>South Sudan</option>
                  <option value="ES" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/es.svg" alt="Spain Flag" /><span class="text-truncate">Spain</span></span>'>Spain</option>
                  <option value="LK" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/lk.svg" "alt="Sri Lanka Flag" /><span class="text-truncate">Sri Lanka</span></span>'>Sri Lanka</option>
                  <option value="PS" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ps.svg" alt="State of Palestine Flag" /><span class="text-truncate">State of Palestine</span></span>'>State of Palestine</option>
                  <option value="SD" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sd.svg" alt="Sudan Flag" /><span class="text-truncate">Sudan</span></span>'>Sudan</option>
                  <option value="SR" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sr.svg" alt="Suriname Flag" /><span class="text-truncate">Suriname</span></span>'>Suriname</option>
                  <option value="SJ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sj.svg" alt="Svalbard and Jan Mayen Flag" /><span class="text-truncate">Svalbard and Jan Mayen</span></span>'>Svalbard and Jan Mayen</option>
                  <option value="SZ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sz.svg" alt="Swaziland Flag" /><span class="text-truncate">Swaziland</span></span>'>Swaziland</option>
                  <option value="SE" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/se.svg" alt="Sweden Flag" /><span class="text-truncate">Sweden</span></span>'>Sweden</option>
                  <option value="CH" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ch.svg" alt="Switzerland Flag" /><span class="text-truncate">Switzerland</span></span>'>Switzerland</option>
                  <option value="SY" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/sy.svg" alt="Syrian Arab Republic Flag" /><span class="text-truncate">Syrian Arab Republic</span></span>'>Syrian Arab Republic</option>
                  <option value="TW" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/tw.svg" alt="Taiwan Flag" /><span class="text-truncate">Taiwan</span></span>'>Taiwan</option>
                  <option value="TJ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/tj.svg" alt="Tajikistan Flag" /><span class="text-truncate">Tajikistan</span></span>'>Tajikistan</option>
                  <option value="TZ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/tz.svg" alt="Tanzania Flag" /><span class="text-truncate">Tanzania</span></span>'>Tanzania</option>
                  <option value="TH" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/th.svg" alt="Thailand Flag" /><span class="text-truncate">Thailand</span></span>'>Thailand</option>
                  <option value="TL" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/tl.svg" alt="Timor-Leste Flag" /><span class="text-truncate">Timor-Leste</span></span>'>Timor-Leste</option>
                  <option value="TG" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/tg.svg" alt="Togo Flag" /><span class="text-truncate">Togo</span></span>'>Togo</option>
                  <option value="TK" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/tk.svg" alt="Tokelau Flag" /><span class="text-truncate">Tokelau</span></span>'>Tokelau</option>
                  <option value="TO" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/to.svg" alt="Tonga Flag" /><span class="text-truncate">Tonga</span></span>'>Tonga</option>
                  <option value="TT" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/tt.svg" alt="Trinidad and Tobago Flag" /><span class="text-truncate">Trinidad and Tobago</span></span>'>Trinidad and Tobago</option>
                  <option value="TN" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/tn.svg" alt="Tunisia Flag" /><span class="text-truncate">Tunisia</span></span>'>Tunisia</option>
                  <option value="TR" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/tr.svg" alt="Turkey Flag" /><span class="text-truncate">Turkey</span></span>'>Turkey</option>
                  <option value="TM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/tm.svg" alt="Turkmenistan Flag" /><span class="text-truncate">Turkmenistan</span></span>'>Turkmenistan</option>
                  <option value="TC" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/tc.svg" alt="Turks and Caicos Islands Flag" /><span class="text-truncate">Turks and Caicos Islands</span></span>'>Turks and Caicos Islands</option>
                  <option value="TV" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/tv.svg" alt="Tuvalu Flag" /><span class="text-truncate">Tuvalu</span></span>'>Tuvalu</option>
                  <option value="UG" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ug.svg" alt="Uganda Flag" /><span class="text-truncate">Uganda</span></span>'>Uganda</option>
                  <option value="UA" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ua.svg" alt="Ukraine Flag" /><span class="text-truncate">Ukraine</span></span>'>Ukraine</option>
                  <option value="AE" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ae.svg" alt="United Arab Emirates Flag" /><span class="text-truncate">United Arab Emirates</span></span>'>United Arab Emirates</option>
                  <option value="GB" selected data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gb.svg" alt="United Kingdom Flag" /><span class="text-truncate">United Kingdom</span></span>'>United Kingdom</option>
                  <option value="UM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/um.svg" alt="United States Minor Outlying Islands Flag" /><span class="text-truncate">United States Minor Outlying Islands</span></span>'>United States Minor Outlying Islands</option>
                  <option value="US" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/us.svg" alt="United States of America Flag" /><span class="text-truncate">United States of America</span></span>'>United States of America</option>
                  <option value="UY" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/uy.svg" alt="Uruguay Flag" /><span class="text-truncate">Uruguay</span></span>'>Uruguay</option>
                  <option value="UZ" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/uz.svg" alt="Uzbekistan Flag" /><span class="text-truncate">Uzbekistan</span></span>'>Uzbekistan</option>
                  <option value="VU" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/vu.svg" alt="Vanuatu Flag" /><span class="text-truncate">Vanuatu</span></span>'>Vanuatu</option>
                  <option value="VE" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ve.svg" alt="Venezuela (Bolivarian Republic of) Flag" /><span class="text-truncate">Venezuela (Bolivarian Republic of)</span></span>'>Venezuela (Bolivarian Republic of)</option>
                  <option value="VN" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/vn.svg" alt="Vietnam Flag" /><span class="text-truncate">Vietnam</span></span>'>Vietnam</option>
                  <option value="VG" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/vg.svg" alt="Virgin Islands (British) Flag" /><span class="text-truncate">Virgin Islands (British)</span></span>'>Virgin Islands (British)</option>
                  <option value="VI" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/vi.svg" alt="Virgin Islands (U.S.) Flag" /><span class="text-truncate">Virgin Islands (U.S.)</span></span>'>Virgin Islands (U.S.)</option>
                  <option value="GB-WLS" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/gb-wls.svg" alt="Wales Flag" /><span class="text-truncate">Wales</span></span>'>Wales</option>
                  <option value="WF" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/wf.svg" alt="Wallis and Futuna Flag" /><span class="text-truncate">Wallis and Futuna</span></span>'>Wallis and Futuna</option>
                  <option value="EH" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/eh.svg" alt="Western Sahara Flag" /><span class="text-truncate">Western Sahara</span></span>'>Western Sahara</option>
                  <option value="YE" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/ye.svg" alt="Yemen Flag" /><span class="text-truncate">Yemen</span></span>'>Yemen</option>
                  <option value="ZM" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/zm.svg" alt="Zambia Flag" /><span class="text-truncate">Zambia</span></span>'>Zambia</option>
                  <option value="ZW" data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="../assets/vendor/flag-icon-css/flags/1x1/zw.svg" alt="Zimbabwe Flag" /><span class="text-truncate">Zimbabwe</span></span>'>Zimbabwe</option>
                </select>
              </div>
              <!-- End Select -->
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html4" role="tabpanel" aria-labelledby="nav-htmlTab4">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Select --&gt;
                &lt;div class="tom-select-custom"&gt;
                  &lt;select class=&quot;js-select form-select&quot; id=&quot;locationLabel&quot;&gt;
                    &lt;option value=&quot;AF&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/af.svg&quot; alt=&quot;Afghanistan Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Afghanistan&lt;/span&gt;&lt;/span&gt;'&gt;Afghanistan&lt;/option&gt;
                    &lt;option value=&quot;AX&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ax.svg&quot; alt=&quot;Aland Islands Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Aland Islands&lt;/span&gt;&lt;/span&gt;'&gt;Aland Islands&lt;/option&gt;
                    &lt;option value=&quot;AL&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/al.svg&quot; alt=&quot;Albania Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Albania&lt;/span&gt;&lt;/span&gt;'&gt;Albania&lt;/option&gt;
                    &lt;option value=&quot;DZ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/dz.svg&quot; alt=&quot;Algeria Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Algeria&lt;/span&gt;&lt;/span&gt;'&gt;Algeria&lt;/option&gt;
                    &lt;option value=&quot;AS&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/as.svg&quot; alt=&quot;American Samoa Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;American Samoa&lt;/span&gt;&lt;/span&gt;'&gt;American Samoa&lt;/option&gt;
                    &lt;option value=&quot;AD&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ad.svg&quot; alt=&quot;Andorra Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Andorra&lt;/span&gt;&lt;/span&gt;'&gt;Andorra&lt;/option&gt;
                    &lt;option value=&quot;AO&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ao.svg&quot; alt=&quot;Angola Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Angola&lt;/span&gt;&lt;/span&gt;'&gt;Angola&lt;/option&gt;
                    &lt;option value=&quot;AI&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ai.svg&quot; alt=&quot;Anguilla Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Anguilla&lt;/span&gt;&lt;/span&gt;'&gt;Anguilla&lt;/option&gt;
                    &lt;option value=&quot;AG&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ag.svg&quot; alt=&quot;Antigua and Barbuda Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Antigua and Barbuda&lt;/span&gt;&lt;/span&gt;'&gt;Antigua and Barbuda&lt;/option&gt;
                    &lt;option value=&quot;AR&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ar.svg&quot; alt=&quot;Argentina Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Argentina&lt;/span&gt;&lt;/span&gt;'&gt;Argentina&lt;/option&gt;
                    &lt;option value=&quot;AM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/am.svg&quot; alt=&quot;Armenia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Armenia&lt;/span&gt;&lt;/span&gt;'&gt;Armenia&lt;/option&gt;
                    &lt;option value=&quot;AW&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/aw.svg&quot; alt=&quot;Aruba Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Aruba&lt;/span&gt;&lt;/span&gt;'&gt;Aruba&lt;/option&gt;
                    &lt;option value=&quot;AU&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/au.svg&quot; alt=&quot;Australia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Australia&lt;/span&gt;&lt;/span&gt;'&gt;Australia&lt;/option&gt;
                    &lt;option value=&quot;AT&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/at.svg&quot; alt=&quot;Austria Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Austria&lt;/span&gt;&lt;/span&gt;'&gt;Austria&lt;/option&gt;
                    &lt;option value=&quot;AZ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/az.svg&quot; alt=&quot;Azerbaijan Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Azerbaijan&lt;/span&gt;&lt;/span&gt;'&gt;Azerbaijan&lt;/option&gt;
                    &lt;option value=&quot;BS&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bs.svg&quot; alt=&quot;Bahamas Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Bahamas&lt;/span&gt;&lt;/span&gt;'&gt;Bahamas&lt;/option&gt;
                    &lt;option value=&quot;BH&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bh.svg&quot; alt=&quot;Bahrain Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Bahrain&lt;/span&gt;&lt;/span&gt;'&gt;Bahrain&lt;/option&gt;
                    &lt;option value=&quot;BD&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bd.svg&quot; alt=&quot;Bangladesh Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Bangladesh&lt;/span&gt;&lt;/span&gt;'&gt;Bangladesh&lt;/option&gt;
                    &lt;option value=&quot;BB&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bb.svg&quot; alt=&quot;Barbados Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Barbados&lt;/span&gt;&lt;/span&gt;'&gt;Barbados&lt;/option&gt;
                    &lt;option value=&quot;BY&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/by.svg&quot; alt=&quot;Belarus Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Belarus&lt;/span&gt;&lt;/span&gt;'&gt;Belarus&lt;/option&gt;
                    &lt;option value=&quot;BE&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/be.svg&quot; alt=&quot;Belgium Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Belgium&lt;/span&gt;&lt;/span&gt;'&gt;Belgium&lt;/option&gt;
                    &lt;option value=&quot;BZ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bz.svg&quot; alt=&quot;Belize Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Belize&lt;/span&gt;&lt;/span&gt;'&gt;Belize&lt;/option&gt;
                    &lt;option value=&quot;BJ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bj.svg&quot; alt=&quot;Benin Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Benin&lt;/span&gt;&lt;/span&gt;'&gt;Benin&lt;/option&gt;
                    &lt;option value=&quot;BM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bm.svg&quot; alt=&quot;Bermuda Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Bermuda&lt;/span&gt;&lt;/span&gt;'&gt;Bermuda&lt;/option&gt;
                    &lt;option value=&quot;BT&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bt.svg&quot; alt=&quot;Bhutan Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Bhutan&lt;/span&gt;&lt;/span&gt;'&gt;Bhutan&lt;/option&gt;
                    &lt;option value=&quot;BO&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bo.svg&quot; alt=&quot;Bolivia (Plurinational State of) Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Bolivia (Plurinational State of)&lt;/span&gt;&lt;/span&gt;'&gt;Bolivia (Plurinational State of)&lt;/option&gt;
                    &lt;option value=&quot;BQ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bq.svg&quot; alt=&quot;Bonaire, Sint Eustatius and Saba Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Bonaire, Sint Eustatius and Saba&lt;/span&gt;&lt;/span&gt;'&gt;Bonaire, Sint Eustatius and Saba&lt;/option&gt;
                    &lt;option value=&quot;BA&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ba.svg&quot; alt=&quot;Bosnia and Herzegovina Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Bosnia and Herzegovina&lt;/span&gt;&lt;/span&gt;'&gt;Bosnia and Herzegovina&lt;/option&gt;
                    &lt;option value=&quot;BW&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bw.svg&quot; alt=&quot;Botswana Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Botswana&lt;/span&gt;&lt;/span&gt;'&gt;Botswana&lt;/option&gt;
                    &lt;option value=&quot;BR&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/br.svg&quot; alt=&quot;Brazil Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Brazil&lt;/span&gt;&lt;/span&gt;'&gt;Brazil&lt;/option&gt;
                    &lt;option value=&quot;IO&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/io.svg&quot; alt=&quot;British Indian Ocean Territory Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;British Indian Ocean Territory&lt;/span&gt;&lt;/span&gt;'&gt;British Indian Ocean Territory&lt;/option&gt;
                    &lt;option value=&quot;BN&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bn.svg&quot; alt=&quot;Brunei Darussalam Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Brunei Darussalam&lt;/span&gt;&lt;/span&gt;'&gt;Brunei Darussalam&lt;/option&gt;
                    &lt;option value=&quot;BG&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bg.svg&quot; alt=&quot;Bulgaria Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Bulgaria&lt;/span&gt;&lt;/span&gt;'&gt;Bulgaria&lt;/option&gt;
                    &lt;option value=&quot;BF&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bf.svg&quot; alt=&quot;Burkina Faso Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Burkina Faso&lt;/span&gt;&lt;/span&gt;'&gt;Burkina Faso&lt;/option&gt;
                    &lt;option value=&quot;BI&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bi.svg&quot; alt=&quot;Burundi Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Burundi&lt;/span&gt;&lt;/span&gt;'&gt;Burundi&lt;/option&gt;
                    &lt;option value=&quot;CV&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/cv.svg&quot; alt=&quot;Cabo Verde Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Cabo Verde&lt;/span&gt;&lt;/span&gt;'&gt;Cabo Verde&lt;/option&gt;
                    &lt;option value=&quot;KH&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/kh.svg&quot; alt=&quot;Cambodia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Cambodia&lt;/span&gt;&lt;/span&gt;'&gt;Cambodia&lt;/option&gt;
                    &lt;option value=&quot;CM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/cm.svg&quot; alt=&quot;Cameroon Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Cameroon&lt;/span&gt;&lt;/span&gt;'&gt;Cameroon&lt;/option&gt;
                    &lt;option value=&quot;CA&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ca.svg&quot; alt=&quot;Canada Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Canada&lt;/span&gt;&lt;/span&gt;'&gt;Canada&lt;/option&gt;
                    &lt;option value=&quot;KY&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ky.svg&quot; alt=&quot;Cayman Islands Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Cayman Islands&lt;/span&gt;&lt;/span&gt;'&gt;Cayman Islands&lt;/option&gt;
                    &lt;option value=&quot;CF&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle shape-4.svg2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/cf.svg&quot; alt=&quot;Central African Republic Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Central African Republic&lt;/span&gt;&lt;/span&gt;'&gt;Central African Republic&lt;/option&gt;
                    &lt;option value=&quot;TD&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/td.svg&quot; alt=&quot;Chad Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Chad&lt;/span&gt;&lt;/span&gt;'&gt;Chad&lt;/option&gt;
                    &lt;option value=&quot;CL&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/cl.svg&quot; alt=&quot;Chile Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Chile&lt;/span&gt;&lt;/span&gt;'&gt;Chile&lt;/option&gt;
                    &lt;option value=&quot;CN&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/cn.svg&quot; alt=&quot;China Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;China&lt;/span&gt;&lt;/span&gt;'&gt;China&lt;/option&gt;
                    &lt;option value=&quot;CX&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/cx.svg&quot; alt=&quot;Christmas Island Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Christmas Island&lt;/span&gt;&lt;/span&gt;'&gt;Christmas Island&lt;/option&gt;
                    &lt;option value=&quot;CC&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/cc.svg&quot; alt=&quot;Cocos (Keeling) Islands Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Cocos (Keeling) Islands&lt;/span&gt;&lt;/span&gt;'&gt;Cocos (Keeling) Islands&lt;/option&gt;
                    &lt;option value=&quot;CO&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/co.svg&quot; alt=&quot;Colombia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Colombia&lt;/span&gt;&lt;/span&gt;'&gt;Colombia&lt;/option&gt;
                    &lt;option value=&quot;KM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/km.svg&quot; alt=&quot;Comoros Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Comoros&lt;/span&gt;&lt;/span&gt;'&gt;Comoros&lt;/option&gt;
                    &lt;option value=&quot;CK&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ck.svg&quot; alt=&quot;Cook Islands Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Cook Islands&lt;/span&gt;&lt;/span&gt;'&gt;Cook Islands&lt;/option&gt;
                    &lt;option value=&quot;CR&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/cr.svg&quot; alt=&quot;Costa Rica Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Costa Rica&lt;/span&gt;&lt;/span&gt;'&gt;Costa Rica&lt;/option&gt;
                    &lt;option value=&quot;HR&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/hr.svg&quot; alt=&quot;Croatia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Croatia&lt;/span&gt;&lt;/span&gt;'&gt;Croatia&lt;/option&gt;
                    &lt;option value=&quot;CU&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/cu.svg&quot; alt=&quot;Cuba Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Cuba&lt;/span&gt;&lt;/span&gt;'&gt;Cuba&lt;/option&gt;
                    &lt;option value=&quot;CW&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/cw.svg&quot; alt=&quot;Cura&ccedil;ao Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Cura&ccedil;ao&lt;/span&gt;&lt;/span&gt;'&gt;Cura&ccedil;ao&lt;/option&gt;
                    &lt;option value=&quot;CY&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/cy.svg&quot; alt=&quot;Cyprus Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Cyprus&lt;/span&gt;&lt;/span&gt;'&gt;Cyprus&lt;/option&gt;
                    &lt;option value=&quot;CZ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/cz.svg&quot; alt=&quot;Czech Republic Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Czech Republic&lt;/span&gt;&lt;/span&gt;'&gt;Czech Republic&lt;/option&gt;
                    &lt;option value=&quot;CI&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ci.svg&quot; alt=C&ocirc;te d&amp;apos;Ivoire Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;C&ocirc;te d&amp;apos;Ivoire&lt;/span&gt;&lt;/span&gt;'&gt;C&ocirc;te d'Ivoire&lt;/option&gt;
                    &lt;option value=&quot;CD&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/cd.svg&quot; alt=&quot;Democratic Republic of the Congo Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Democratic Republic of the Congo&lt;/span&gt;&lt;/span&gt;'&gt;Democratic Republic of the Congo&lt;/option&gt;
                    &lt;option value=&quot;DK&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/dk.svg&quot; alt=&quot;Denmark Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Denmark&lt;/span&gt;&lt;/span&gt;'&gt;Denmark&lt;/option&gt;
                    &lt;option value=&quot;DJ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/dj.svg&quot; alt=&quot;Djibouti Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Djibouti&lt;/span&gt;&lt;/span&gt;'&gt;Djibouti&lt;/option&gt;
                    &lt;option value=&quot;DM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/dm.svg&quot; alt=&quot;Dominica Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Dominica&lt;/span&gt;&lt;/span&gt;'&gt;Dominica&lt;/option&gt;
                    &lt;option value=&quot;DO&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/do.svg&quot; alt=&quot;Dominican Republic Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Dominican Republic&lt;/span&gt;&lt;/span&gt;'&gt;Dominican Republic&lt;/option&gt;
                    &lt;option value=&quot;EC&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ec.svg&quot; alt=&quot;Ecuador Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Ecuador&lt;/span&gt;&lt;/span&gt;'&gt;Ecuador&lt;/option&gt;
                    &lt;option value=&quot;EG&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/eg.svg&quot; alt=&quot;Egypt Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Egypt&lt;/span&gt;&lt;/span&gt;'&gt;Egypt&lt;/option&gt;
                    &lt;option value=&quot;SV&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sv.svg&quot; alt=&quot;El Salvador Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;El Salvador&lt;/span&gt;&lt;/span&gt;'&gt;El Salvador&lt;/option&gt;
                    &lt;option value=&quot;GB-ENG&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gb-eng.svg&quot; alt=&quot;England Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;England&lt;/span&gt;&lt;/span&gt;'&gt;England&lt;/option&gt;
                    &lt;option value=&quot;GQ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gq.svg&quot; alt=&quot;Equatorial Guinea Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Equatorial Guinea&lt;/span&gt;&lt;/span&gt;'&gt;Equatorial Guinea&lt;/option&gt;
                    &lt;option value=&quot;ER&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/er.svg&quot; alt=&quot;Eritrea Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Eritrea&lt;/span&gt;&lt;/span&gt;'&gt;Eritrea&lt;/option&gt;
                    &lt;option value=&quot;EE&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ee.svg&quot; alt=&quot;Estonia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Estonia&lt;/span&gt;&lt;/span&gt;'&gt;Estonia&lt;/option&gt;
                    &lt;option value=&quot;ET&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/et.svg&quot; alt=&quot;Ethiopia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Ethiopia&lt;/span&gt;&lt;/span&gt;'&gt;Ethiopia&lt;/option&gt;
                    &lt;option value=&quot;FK&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/fk.svg&quot; alt=&quot;Falkland Islands Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Falkland Islands&lt;/span&gt;&lt;/span&gt;'&gt;Falkland Islands&lt;/option&gt;
                    &lt;option value=&quot;FO&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/fo.svg&quot; alt=&quot;Faroe Islands Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Faroe Islands&lt;/span&gt;&lt;/span&gt;'&gt;Faroe Islands&lt;/option&gt;
                    &lt;option value=&quot;FM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/fm.svg&quot; alt=&quot;Federated States of Micronesia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Federated States of Micronesia&lt;/span&gt;&lt;/span&gt;'&gt;Federated States of Micronesia&lt;/option&gt;
                    &lt;option value=&quot;FJ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/fj.svg&quot; alt=&quot;Fiji Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Fiji&lt;/span&gt;&lt;/span&gt;'&gt;Fiji&lt;/option&gt;
                    &lt;option value=&quot;FI&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/fi.svg&quot; alt=&quot;Finland Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Finland&lt;/span&gt;&lt;/span&gt;'&gt;Finland&lt;/option&gt;
                    &lt;option value=&quot;FR&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/fr.svg&quot; alt=&quot;France Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;France&lt;/span&gt;&lt;/span&gt;'&gt;France&lt;/option&gt;
                    &lt;option value=&quot;GF&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gf.svg&quot; alt=&quot;French Guiana Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;French Guiana&lt;/span&gt;&lt;/span&gt;'&gt;French Guiana&lt;/option&gt;
                    &lt;option value=&quot;PF&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/pf.svg&quot; alt=&quot;French Polynesia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;French Polynesia&lt;/span&gt;&lt;/span&gt;'&gt;French Polynesia&lt;/option&gt;
                    &lt;option value=&quot;TF&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/tf.svg&quot; alt=&quot;French Southern Territories Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;French Southern Territories&lt;/span&gt;&lt;/span&gt;'&gt;French Southern Territories&lt;/option&gt;
                    &lt;option value=&quot;GA&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ga.svg&quot; alt=&quot;Gabon Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Gabon&lt;/span&gt;&lt;/span&gt;'&gt;Gabon&lt;/option&gt;
                    &lt;option value=&quot;GM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gm.svg&quot; alt=&quot;Gambia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Gambia&lt;/span&gt;&lt;/span&gt;'&gt;Gambia&lt;/option&gt;
                    &lt;option value=&quot;GE&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ge.svg&quot; alt=&quot;Georgia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Georgia&lt;/span&gt;&lt;/span&gt;'&gt;Georgia&lt;/option&gt;
                    &lt;option value=&quot;DE&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/de.svg&quot; alt=&quot;Germany Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Germany&lt;/span&gt;&lt;/span&gt;'&gt;Germany&lt;/option&gt;
                    &lt;option value=&quot;GH&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gh.svg&quot; alt=&quot;Ghana Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Ghana&lt;/span&gt;&lt;/span&gt;'&gt;Ghana&lt;/option&gt;
                    &lt;option value=&quot;GI&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gi.svg&quot; alt=&quot;Gibraltar Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Gibraltar&lt;/span&gt;&lt;/span&gt;'&gt;Gibraltar&lt;/option&gt;
                    &lt;option value=&quot;GR&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gr.svg&quot; alt=&quot;Greece Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Greece&lt;/span&gt;&lt;/span&gt;'&gt;Greece&lt;/option&gt;
                    &lt;option value=&quot;GL&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gl.svg&quot; alt=&quot;Greenland Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Greenland&lt;/span&gt;&lt;/span&gt;'&gt;Greenland&lt;/option&gt;
                    &lt;option value=&quot;GD&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gd.svg&quot; alt=&quot;Grenada Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Grenada&lt;/span&gt;&lt;/span&gt;'&gt;Grenada&lt;/option&gt;
                    &lt;option value=&quot;GP&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gp.svg&quot; alt=&quot;Guadeloupe Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Guadeloupe&lt;/span&gt;&lt;/span&gt;'&gt;Guadeloupe&lt;/option&gt;
                    &lt;option value=&quot;GU&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gu.svg&quot; alt=&quot;Guam Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Guam&lt;/span&gt;&lt;/span&gt;'&gt;Guam&lt;/option&gt;
                    &lt;option value=&quot;GT&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gt.svg&quot; alt=&quot;Guatemala Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Guatemala&lt;/span&gt;&lt;/span&gt;'&gt;Guatemala&lt;/option&gt;
                    &lt;option value=&quot;GG&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gg.svg&quot; alt=&quot;Guernsey Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Guernsey&lt;/span&gt;&lt;/span&gt;'&gt;Guernsey&lt;/option&gt;
                    &lt;option value=&quot;GN&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gn.svg&quot; alt=&quot;Guinea Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Guinea&lt;/span&gt;&lt;/span&gt;'&gt;Guinea&lt;/option&gt;
                    &lt;option value=&quot;GW&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gw.svg&quot; alt=&quot;Guinea-Bissau Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Guinea-Bissau&lt;/span&gt;&lt;/span&gt;'&gt;Guinea-Bissau&lt;/option&gt;
                    &lt;option value=&quot;GY&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gy.svg&quot; alt=&quot;Guyana Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Guyana&lt;/span&gt;&lt;/span&gt;'&gt;Guyana&lt;/option&gt;
                    &lt;option value=&quot;HT&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ht.svg&quot; alt=&quot;Haiti Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Haiti&lt;/span&gt;&lt;/span&gt;'&gt;Haiti&lt;/option&gt;
                    &lt;option value=&quot;VA&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/va.svg&quot; alt=&quot;Holy See Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Holy See&lt;/span&gt;&lt;/span&gt;'&gt;Holy See&lt;/option&gt;
                    &lt;option value=&quot;HN&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/hn.svg&quot; alt=&quot;Honduras Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Honduras&lt;/span&gt;&lt;/span&gt;'&gt;Honduras&lt;/option&gt;
                    &lt;option value=&quot;HK&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/hk.svg&quot; alt=&quot;Hong Kong Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Hong Kong&lt;/span&gt;&lt;/span&gt;'&gt;Hong Kong&lt;/option&gt;
                    &lt;option value=&quot;HU&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/hu.svg&quot; alt=&quot;Hungary Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Hungary&lt;/span&gt;&lt;/span&gt;'&gt;Hungary&lt;/option&gt;
                    &lt;option value=&quot;IS&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/is.svg&quot; alt=&quot;Iceland Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Iceland&lt;/span&gt;&lt;/span&gt;'&gt;Iceland&lt;/option&gt;
                    &lt;option value=&quot;IN&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/in.svg&quot; alt=&quot;India Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;India&lt;/span&gt;&lt;/span&gt;'&gt;India&lt;/option&gt;
                    &lt;option value=&quot;ID&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/id.svg&quot; alt=&quot;Indonesia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Indonesia&lt;/span&gt;&lt;/span&gt;'&gt;Indonesia&lt;/option&gt;
                    &lt;option value=&quot;IR&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ir.svg&quot; alt=&quot;Iran (Islamic Republic of) Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Iran (Islamic Republic of)&lt;/span&gt;&lt;/span&gt;'&gt;Iran (Islamic Republic of)&lt;/option&gt;
                    &lt;option value=&quot;IQ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/iq.svg&quot; alt=&quot;Iraq Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Iraq&lt;/span&gt;&lt;/span&gt;'&gt;Iraq&lt;/option&gt;
                    &lt;option value=&quot;IE&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ie.svg&quot; alt=&quot;Ireland Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Ireland&lt;/span&gt;&lt;/span&gt;'&gt;Ireland&lt;/option&gt;
                    &lt;option value=&quot;IM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/im.svg&quot; alt=&quot;Isle of Man Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Isle of Man&lt;/span&gt;&lt;/span&gt;'&gt;Isle of Man&lt;/option&gt;
                    &lt;option value=&quot;IL&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/il.svg&quot; alt=&quot;Israel Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Israel&lt;/span&gt;&lt;/span&gt;'&gt;Israel&lt;/option&gt;
                    &lt;option value=&quot;IT&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/it.svg&quot; alt=&quot;Italy Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Italy&lt;/span&gt;&lt;/span&gt;'&gt;Italy&lt;/option&gt;
                    &lt;option value=&quot;JM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/jm.svg&quot; alt=&quot;Jamaica Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Jamaica&lt;/span&gt;&lt;/span&gt;'&gt;Jamaica&lt;/option&gt;
                    &lt;option value=&quot;JP&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/jp.svg&quot; alt=&quot;Japan Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Japan&lt;/span&gt;&lt;/span&gt;'&gt;Japan&lt;/option&gt;
                    &lt;option value=&quot;JE&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/je.svg&quot; alt=&quot;Jersey Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Jersey&lt;/span&gt;&lt;/span&gt;'&gt;Jersey&lt;/option&gt;
                    &lt;option value=&quot;JO&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/jo.svg&quot; alt=&quot;Jordan Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Jordan&lt;/span&gt;&lt;/span&gt;'&gt;Jordan&lt;/option&gt;
                    &lt;option value=&quot;KZ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/kz.svg&quot; alt=&quot;Kazakhstan Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Kazakhstan&lt;/span&gt;&lt;/span&gt;'&gt;Kazakhstan&lt;/option&gt;
                    &lt;option value=&quot;KE&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ke.svg&quot; alt=&quot;Kenya Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Kenya&lt;/span&gt;&lt;/span&gt;'&gt;Kenya&lt;/option&gt;
                    &lt;option value=&quot;KI&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ki.svg&quot; alt=&quot;Kiribati Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Kiribati&lt;/span&gt;&lt;/span&gt;'&gt;Kiribati&lt;/option&gt;
                    &lt;option value=&quot;KW&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/kw.svg&quot; alt=&quot;Kuwait Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Kuwait&lt;/span&gt;&lt;/span&gt;'&gt;Kuwait&lt;/option&gt;
                    &lt;option value=&quot;KG&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/kg.svg&quot; alt=&quot;Kyrgyzstan Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Kyrgyzstan&lt;/span&gt;&lt;/span&gt;'&gt;Kyrgyzstan&lt;/option&gt;
                    &lt;option value=&quot;LA&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/la.svg&quot; alt=&quot;Laos Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Laos&lt;/span&gt;&lt;/span&gt;'&gt;Laos&lt;/option&gt;
                    &lt;option value=&quot;LV&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/lv.svg&quot; alt=&quot;Latvia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Latvia&lt;/span&gt;&lt;/span&gt;'&gt;Latvia&lt;/option&gt;
                    &lt;option value=&quot;LB&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/lb.svg&quot; alt=&quot;Lebanon Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Lebanon&lt;/span&gt;&lt;/span&gt;'&gt;Lebanon&lt;/option&gt;
                    &lt;option value=&quot;LS&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ls.svg&quot; alt=&quot;Lesotho Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Lesotho&lt;/span&gt;&lt;/span&gt;'&gt;Lesotho&lt;/option&gt;
                    &lt;option value=&quot;LR&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/lr.svg&quot; alt=&quot;Liberia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Liberia&lt;/span&gt;&lt;/span&gt;'&gt;Liberia&lt;/option&gt;
                    &lt;option value=&quot;LY&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ly.svg&quot; alt=&quot;Libya Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Libya&lt;/span&gt;&lt;/span&gt;'&gt;Libya&lt;/option&gt;
                    &lt;option value=&quot;LI&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/li.svg&quot; alt=&quot;Liechtenstein Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Liechtenstein&lt;/span&gt;&lt;/span&gt;'&gt;Liechtenstein&lt;/option&gt;
                    &lt;option value=&quot;LT&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/lt.svg&quot; alt=&quot;Lithuania Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Lithuania&lt;/span&gt;&lt;/span&gt;'&gt;Lithuania&lt;/option&gt;
                    &lt;option value=&quot;LU&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/lu.svg&quot; alt=&quot;Luxembourg Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Luxembourg&lt;/span&gt;&lt;/span&gt;'&gt;Luxembourg&lt;/option&gt;
                    &lt;option value=&quot;MO&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mo.svg&quot; alt=&quot;Macau Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Macau&lt;/span&gt;&lt;/span&gt;'&gt;Macau&lt;/option&gt;
                    &lt;option value=&quot;MG&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mg.svg&quot; alt=&quot;Madagascar Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Madagascar&lt;/span&gt;&lt;/span&gt;'&gt;Madagascar&lt;/option&gt;
                    &lt;option value=&quot;MW&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mw.svg&quot; alt=&quot;Malawi Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Malawi&lt;/span&gt;&lt;/span&gt;'&gt;Malawi&lt;/option&gt;
                    &lt;option value=&quot;MY&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/my.svg&quot; alt=&quot;Malaysia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Malaysia&lt;/span&gt;&lt;/span&gt;'&gt;Malaysia&lt;/option&gt;
                    &lt;option value=&quot;MV&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mv.svg&quot; alt=&quot;Maldives Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Maldives&lt;/span&gt;&lt;/span&gt;'&gt;Maldives&lt;/option&gt;
                    &lt;option value=&quot;ML&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ml.svg&quot; alt=&quot;Mali Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Mali&lt;/span&gt;&lt;/span&gt;'&gt;Mali&lt;/option&gt;
                    &lt;option value=&quot;MT&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mt.svg&quot; alt=&quot;Malta Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Malta&lt;/span&gt;&lt;/span&gt;'&gt;Malta&lt;/option&gt;
                    &lt;option value=&quot;MH&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mh.svg&quot; alt=&quot;Marshall Islands Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Marshall Islands&lt;/span&gt;&lt;/span&gt;'&gt;Marshall Islands&lt;/option&gt;
                    &lt;option value=&quot;MQ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mq.svg&quot; alt=&quot;Martinique Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Martinique&lt;/span&gt;&lt;/span&gt;'&gt;Martinique&lt;/option&gt;
                    &lt;option value=&quot;MR&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mr.svg&quot; alt=&quot;Mauritania Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Mauritania&lt;/span&gt;&lt;/span&gt;'&gt;Mauritania&lt;/option&gt;
                    &lt;option value=&quot;MU&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mu.svg&quot; alt=&quot;Mauritius Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Mauritius&lt;/span&gt;&lt;/span&gt;'&gt;Mauritius&lt;/option&gt;
                    &lt;option value=&quot;YT&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/yt.svg&quot; alt=&quot;Mayotte Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Mayotte&lt;/span&gt;&lt;/span&gt;'&gt;Mayotte&lt;/option&gt;
                    &lt;option value=&quot;MX&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mx.svg&quot; alt=&quot;Mexico Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Mexico&lt;/span&gt;&lt;/span&gt;'&gt;Mexico&lt;/option&gt;
                    &lt;option value=&quot;MD&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/md.svg&quot; alt=&quot;Moldova Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Moldova&lt;/span&gt;&lt;/span&gt;'&gt;Moldova&lt;/option&gt;
                    &lt;option value=&quot;MC&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mc.svg&quot; alt=&quot;Monaco Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Monaco&lt;/span&gt;&lt;/span&gt;'&gt;Monaco&lt;/option&gt;
                    &lt;option value=&quot;MN&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mn.svg&quot; alt=&quot;Mongolia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Mongolia&lt;/span&gt;&lt;/span&gt;'&gt;Mongolia&lt;/option&gt;
                    &lt;option value=&quot;ME&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/me.svg&quot; alt=&quot;Montenegro Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Montenegro&lt;/span&gt;&lt;/span&gt;'&gt;Montenegro&lt;/option&gt;
                    &lt;option value=&quot;MS&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ms.svg&quot; alt=&quot;Montserrat Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Montserrat&lt;/span&gt;&lt;/span&gt;'&gt;Montserrat&lt;/option&gt;
                    &lt;option value=&quot;MA&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ma.svg&quot; alt=&quot;Morocco Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Morocco&lt;/span&gt;&lt;/span&gt;'&gt;Morocco&lt;/option&gt;
                    &lt;option value=&quot;MZ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mz.svg&quot; alt=&quot;Mozambique Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Mozambique&lt;/span&gt;&lt;/span&gt;'&gt;Mozambique&lt;/option&gt;
                    &lt;option value=&quot;MM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mm.svg&quot; alt=&quot;Myanmar Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Myanmar&lt;/span&gt;&lt;/span&gt;'&gt;Myanmar&lt;/option&gt;
                    &lt;option value=&quot;NA&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/na.svg&quot; alt=&quot;Namibia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Namibia&lt;/span&gt;&lt;/span&gt;'&gt;Namibia&lt;/option&gt;
                    &lt;option value=&quot;NR&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/nr.svg&quot; alt=&quot;Nauru Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Nauru&lt;/span&gt;&lt;/span&gt;'&gt;Nauru&lt;/option&gt;
                    &lt;option value=&quot;NP&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/np.svg&quot; alt=&quot;Nepal Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Nepal&lt;/span&gt;&lt;/span&gt;'&gt;Nepal&lt;/option&gt;
                    &lt;option value=&quot;NL&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/nl.svg&quot; alt=&quot;Netherlands Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Netherlands&lt;/span&gt;&lt;/span&gt;'&gt;Netherlands&lt;/option&gt;
                    &lt;option value=&quot;NC&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/nc.svg&quot; &quot;alt=&quot;New Caledonia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;New Caledonia&lt;/span&gt;&lt;/span&gt;'&gt;New Caledonia&lt;/option&gt;
                    &lt;option value=&quot;NZ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/nz.svg&quot; alt=&quot;New Zealand Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;New Zealand&lt;/span&gt;&lt;/span&gt;'&gt;New Zealand&lt;/option&gt;
                    &lt;option value=&quot;NI&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ni.svg&quot; alt=&quot;Nicaragua Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Nicaragua&lt;/span&gt;&lt;/span&gt;'&gt;Nicaragua&lt;/option&gt;
                    &lt;option value=&quot;NE&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ne.svg&quot; alt=&quot;Niger Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Niger&lt;/span&gt;&lt;/span&gt;'&gt;Niger&lt;/option&gt;
                    &lt;option value=&quot;NG&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ng.svg&quot; alt=&quot;Nigeria Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Nigeria&lt;/span&gt;&lt;/span&gt;'&gt;Nigeria&lt;/option&gt;
                    &lt;option value=&quot;NU&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/nu.svg&quot; alt=&quot;Niue Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Niue&lt;/span&gt;&lt;/span&gt;'&gt;Niue&lt;/option&gt;
                    &lt;option value=&quot;NF&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/nf.svg&quot; alt=&quot;Norfolk Island Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Norfolk Island&lt;/span&gt;&lt;/span&gt;'&gt;Norfolk Island&lt;/option&gt;
                    &lt;option value=&quot;KP&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/kp.svg&quot; alt=&quot;North Korea Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;North Korea&lt;/span&gt;&lt;/span&gt;'&gt;North Korea&lt;/option&gt;
                    &lt;option value=&quot;MK&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mk.svg&quot; alt=&quot;North Macedonia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;North Macedonia&lt;/span&gt;&lt;/span&gt;'&gt;North Macedonia&lt;/option&gt;
                    &lt;option value=&quot;GB-NIR&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gb-nir.svg&quot; alt=&quot;Northern Ireland Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Northern Ireland&lt;/span&gt;&lt;/span&gt;'&gt;Northern Ireland&lt;/option&gt;
                    &lt;option value=&quot;MP&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mp.svg&quot; alt=&quot;Northern Mariana Islands Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Northern Mariana Islands&lt;/span&gt;&lt;/span&gt;'&gt;Northern Mariana Islands&lt;/option&gt;
                    &lt;option value=&quot;NO&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/no.svg&quot; alt=&quot;Norway Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Norway&lt;/span&gt;&lt;/span&gt;'&gt;Norway&lt;/option&gt;
                    &lt;option value=&quot;OM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/om.svg&quot; alt=&quot;Oman Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Oman&lt;/span&gt;&lt;/span&gt;'&gt;Oman&lt;/option&gt;
                    &lt;option value=&quot;PK&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/pk.svg&quot; alt=&quot;Pakistan Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Pakistan&lt;/span&gt;&lt;/span&gt;'&gt;Pakistan&lt;/option&gt;
                    &lt;option value=&quot;PW&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/pw.svg&quot; alt=&quot;Palau Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Palau&lt;/span&gt;&lt;/span&gt;'&gt;Palau&lt;/option&gt;
                    &lt;option value=&quot;PA&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/pa.svg&quot; alt=&quot;Panama Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Panama&lt;/span&gt;&lt;/span&gt;'&gt;Panama&lt;/option&gt;
                    &lt;option value=&quot;PG&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/pg.svg&quot; alt=&quot;Papua New Guinea Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Papua New Guinea&lt;/span&gt;&lt;/span&gt;'&gt;Papua New Guinea&lt;/option&gt;
                    &lt;option value=&quot;PY&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/py.svg&quot; alt=&quot;Paraguay Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Paraguay&lt;/span&gt;&lt;/span&gt;'&gt;Paraguay&lt;/option&gt;
                    &lt;option value=&quot;PE&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/pe.svg&quot; alt=&quot;Peru Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Peru&lt;/span&gt;&lt;/span&gt;'&gt;Peru&lt;/option&gt;
                    &lt;option value=&quot;PH&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ph.svg&quot; alt=&quot;Philippines Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Philippines&lt;/span&gt;&lt;/span&gt;'&gt;Philippines&lt;/option&gt;
                    &lt;option value=&quot;PN&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/pn.svg&quot; alt=&quot;Pitcairn Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Pitcairn&lt;/span&gt;&lt;/span&gt;'&gt;Pitcairn&lt;/option&gt;
                    &lt;option value=&quot;PL&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/pl.svg&quot; alt=&quot;Poland Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Poland&lt;/span&gt;&lt;/span&gt;'&gt;Poland&lt;/option&gt;
                    &lt;option value=&quot;PT&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/pt.svg&quot; alt=&quot;Portugal Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Portugal&lt;/span&gt;&lt;/span&gt;'&gt;Portugal&lt;/option&gt;
                    &lt;option value=&quot;PR&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/pr.svg&quot; alt=&quot;Puerto Rico Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Puerto Rico&lt;/span&gt;&lt;/span&gt;'&gt;Puerto Rico&lt;/option&gt;
                    &lt;option value=&quot;QA&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/qa.svg&quot; alt=&quot;Qatar Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Qatar&lt;/span&gt;&lt;/span&gt;'&gt;Qatar&lt;/option&gt;
                    &lt;option value=&quot;CG&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/cg.svg&quot; alt=&quot;Republic of the Congo Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Republic of the Congo&lt;/span&gt;&lt;/span&gt;'&gt;Republic of the Congo&lt;/option&gt;
                    &lt;option value=&quot;RO&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ro.svg&quot; alt=&quot;Romania Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Romania&lt;/span&gt;&lt;/span&gt;'&gt;Romania&lt;/option&gt;
                    &lt;option value=&quot;RU&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ru.svg&quot; alt=&quot;Russia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Russia&lt;/span&gt;&lt;/span&gt;'&gt;Russia&lt;/option&gt;
                    &lt;option value=&quot;RW&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/rw.svg&quot; alt=&quot;Rwanda Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Rwanda&lt;/span&gt;&lt;/span&gt;'&gt;Rwanda&lt;/option&gt;
                    &lt;option value=&quot;RE&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/re.svg&quot; alt=&quot;R&eacute;union Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;R&eacute;union&lt;/span&gt;&lt;/span&gt;'&gt;R&eacute;union&lt;/option&gt;
                    &lt;option value=&quot;BL&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/bl.svg&quot; alt=&quot;Saint Barth&eacute;lemy Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Saint Barth&eacute;lemy&lt;/span&gt;&lt;/span&gt;'&gt;Saint Barth&eacute;lemy&lt;/option&gt;
                    &lt;option value=&quot;SH&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sh.svg&quot; alt=&quot;Saint Helena, Ascension and Tristan da Cunha Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Saint Helena, Ascension and Tristan da Cunha&lt;/span&gt;&lt;/span&gt;'&gt;Saint Helena, Ascension and Tristan da Cunha&lt;/option&gt;
                    &lt;option value=&quot;KN&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/kn.svg&quot; alt=&quot;Saint Kitts and Nevis Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Saint Kitts and Nevis&lt;/span&gt;&lt;/span&gt;'&gt;Saint Kitts and Nevis&lt;/option&gt;
                    &lt;option value=&quot;LC&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/lc.svg&quot; alt=&quot;Saint Lucia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Saint Lucia&lt;/span&gt;&lt;/span&gt;'&gt;Saint Lucia&lt;/option&gt;
                    &lt;option value=&quot;MF&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/mf.svg&quot; alt=&quot;Saint Martin Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Saint Martin&lt;/span&gt;&lt;/span&gt;'&gt;Saint Martin&lt;/option&gt;
                    &lt;option value=&quot;PM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/pm.svg&quot; alt=&quot;Saint Pierre and Miquelon Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Saint Pierre and Miquelon&lt;/span&gt;&lt;/span&gt;'&gt;Saint Pierre and Miquelon&lt;/option&gt;
                    &lt;option value=&quot;VC&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/vc.svg&quot; alt=&quot;Saint Vincent and the Grenadines Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Saint Vincent and the Grenadines&lt;/span&gt;&lt;/span&gt;'&gt;Saint Vincent and the Grenadines&lt;/option&gt;
                    &lt;option value=&quot;WS&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ws.svg&quot; alt=&quot;Samoa Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Samoa&lt;/span&gt;&lt;/span&gt;'&gt;Samoa&lt;/option&gt;
                    &lt;option value=&quot;SM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sm.svg&quot; &quot;alt=&quot;San Marino Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;San Marino&lt;/span&gt;&lt;/span&gt;'&gt;San Marino&lt;/option&gt;
                    &lt;option value=&quot;ST&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/st.svg&quot; &quot;alt=&quot;Sao Tome and Principe Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Sao Tome and Principe&lt;/span&gt;&lt;/span&gt;'&gt;Sao Tome and Principe&lt;/option&gt;
                    &lt;option value=&quot;SA&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sa.svg&quot; alt=&quot;Saudi Arabia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Saudi Arabia&lt;/span&gt;&lt;/span&gt;'&gt;Saudi Arabia&lt;/option&gt;
                    &lt;option value=&quot;GB-SCT&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gb-sct.svg&quot; alt=&quot;Scotland Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Scotland&lt;/span&gt;&lt;/span&gt;'&gt;Scotland&lt;/option&gt;
                    &lt;option value=&quot;SN&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sn.svg&quot; alt=&quot;Senegal Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Senegal&lt;/span&gt;&lt;/span&gt;'&gt;Senegal&lt;/option&gt;
                    &lt;option value=&quot;RS&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/rs.svg&quot; alt=&quot;Serbia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Serbia&lt;/span&gt;&lt;/span&gt;'&gt;Serbia&lt;/option&gt;
                    &lt;option value=&quot;SC&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sc.svg&quot; alt=&quot;Seychelles Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Seychelles&lt;/span&gt;&lt;/span&gt;'&gt;Seychelles&lt;/option&gt;
                    &lt;option value=&quot;SL&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sl.svg&quot; alt=&quot;Sierra Leone Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Sierra Leone&lt;/span&gt;&lt;/span&gt;'&gt;Sierra Leone&lt;/option&gt;
                    &lt;option value=&quot;SG&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sg.svg&quot; alt=&quot;Singapore Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Singapore&lt;/span&gt;&lt;/span&gt;'&gt;Singapore&lt;/option&gt;
                    &lt;option value=&quot;SX&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sx.svg&quot; alt=&quot;Sint Maarten Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Sint Maarten&lt;/span&gt;&lt;/span&gt;'&gt;Sint Maarten&lt;/option&gt;
                    &lt;option value=&quot;SK&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sk.svg&quot; alt=&quot;Slovakia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Slovakia&lt;/span&gt;&lt;/span&gt;'&gt;Slovakia&lt;/option&gt;
                    &lt;option value=&quot;SI&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/si.svg&quot; alt=&quot;Slovenia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Slovenia&lt;/span&gt;&lt;/span&gt;'&gt;Slovenia&lt;/option&gt;
                    &lt;option value=&quot;SB&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sb.svg&quot; alt=&quot;Solomon Islands Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Solomon Islands&lt;/span&gt;&lt;/span&gt;'&gt;Solomon Islands&lt;/option&gt;
                    &lt;option value=&quot;SO&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/so.svg&quot; alt=&quot;Somalia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Somalia&lt;/span&gt;&lt;/span&gt;'&gt;Somalia&lt;/option&gt;
                    &lt;option value=&quot;ZA&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/za.svg&quot; alt=&quot;South Africa Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;South Africa&lt;/span&gt;&lt;/span&gt;'&gt;South Africa&lt;/option&gt;
                    &lt;option value=&quot;GS&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gs.svg&quot; alt=&quot;South Georgia and the South Sandwich Islands Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;South Georgia and the South Sandwich Islands&lt;/span&gt;&lt;/span&gt;'&gt;South Georgia and the South Sandwich Islands&lt;/option&gt;
                    &lt;option value=&quot;KR&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/kr.svg&quot; alt=&quot;South Korea Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;South Korea&lt;/span&gt;&lt;/span&gt;'&gt;South Korea&lt;/option&gt;
                    &lt;option value=&quot;SS&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ss.svg&quot; alt=&quot;South Sudan Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;South Sudan&lt;/span&gt;&lt;/span&gt;'&gt;South Sudan&lt;/option&gt;
                    &lt;option value=&quot;ES&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/es.svg&quot; alt=&quot;Spain Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Spain&lt;/span&gt;&lt;/span&gt;'&gt;Spain&lt;/option&gt;
                    &lt;option value=&quot;LK&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/lk.svg&quot; &quot;alt=&quot;Sri Lanka Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Sri Lanka&lt;/span&gt;&lt;/span&gt;'&gt;Sri Lanka&lt;/option&gt;
                    &lt;option value=&quot;PS&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ps.svg&quot; alt=&quot;State of Palestine Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;State of Palestine&lt;/span&gt;&lt;/span&gt;'&gt;State of Palestine&lt;/option&gt;
                    &lt;option value=&quot;SD&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sd.svg&quot; alt=&quot;Sudan Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Sudan&lt;/span&gt;&lt;/span&gt;'&gt;Sudan&lt;/option&gt;
                    &lt;option value=&quot;SR&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sr.svg&quot; alt=&quot;Suriname Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Suriname&lt;/span&gt;&lt;/span&gt;'&gt;Suriname&lt;/option&gt;
                    &lt;option value=&quot;SJ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sj.svg&quot; alt=&quot;Svalbard and Jan Mayen Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Svalbard and Jan Mayen&lt;/span&gt;&lt;/span&gt;'&gt;Svalbard and Jan Mayen&lt;/option&gt;
                    &lt;option value=&quot;SZ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sz.svg&quot; alt=&quot;Swaziland Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Swaziland&lt;/span&gt;&lt;/span&gt;'&gt;Swaziland&lt;/option&gt;
                    &lt;option value=&quot;SE&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/se.svg&quot; alt=&quot;Sweden Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Sweden&lt;/span&gt;&lt;/span&gt;'&gt;Sweden&lt;/option&gt;
                    &lt;option value=&quot;CH&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ch.svg&quot; alt=&quot;Switzerland Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Switzerland&lt;/span&gt;&lt;/span&gt;'&gt;Switzerland&lt;/option&gt;
                    &lt;option value=&quot;SY&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/sy.svg&quot; alt=&quot;Syrian Arab Republic Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Syrian Arab Republic&lt;/span&gt;&lt;/span&gt;'&gt;Syrian Arab Republic&lt;/option&gt;
                    &lt;option value=&quot;TW&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/tw.svg&quot; alt=&quot;Taiwan Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Taiwan&lt;/span&gt;&lt;/span&gt;'&gt;Taiwan&lt;/option&gt;
                    &lt;option value=&quot;TJ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/tj.svg&quot; alt=&quot;Tajikistan Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Tajikistan&lt;/span&gt;&lt;/span&gt;'&gt;Tajikistan&lt;/option&gt;
                    &lt;option value=&quot;TZ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/tz.svg&quot; alt=&quot;Tanzania Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Tanzania&lt;/span&gt;&lt;/span&gt;'&gt;Tanzania&lt;/option&gt;
                    &lt;option value=&quot;TH&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/th.svg&quot; alt=&quot;Thailand Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Thailand&lt;/span&gt;&lt;/span&gt;'&gt;Thailand&lt;/option&gt;
                    &lt;option value=&quot;TL&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/tl.svg&quot; alt=&quot;Timor-Leste Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Timor-Leste&lt;/span&gt;&lt;/span&gt;'&gt;Timor-Leste&lt;/option&gt;
                    &lt;option value=&quot;TG&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/tg.svg&quot; alt=&quot;Togo Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Togo&lt;/span&gt;&lt;/span&gt;'&gt;Togo&lt;/option&gt;
                    &lt;option value=&quot;TK&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/tk.svg&quot; alt=&quot;Tokelau Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Tokelau&lt;/span&gt;&lt;/span&gt;'&gt;Tokelau&lt;/option&gt;
                    &lt;option value=&quot;TO&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/to.svg&quot; alt=&quot;Tonga Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Tonga&lt;/span&gt;&lt;/span&gt;'&gt;Tonga&lt;/option&gt;
                    &lt;option value=&quot;TT&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/tt.svg&quot; alt=&quot;Trinidad and Tobago Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Trinidad and Tobago&lt;/span&gt;&lt;/span&gt;'&gt;Trinidad and Tobago&lt;/option&gt;
                    &lt;option value=&quot;TN&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/tn.svg&quot; alt=&quot;Tunisia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Tunisia&lt;/span&gt;&lt;/span&gt;'&gt;Tunisia&lt;/option&gt;
                    &lt;option value=&quot;TR&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/tr.svg&quot; alt=&quot;Turkey Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Turkey&lt;/span&gt;&lt;/span&gt;'&gt;Turkey&lt;/option&gt;
                    &lt;option value=&quot;TM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/tm.svg&quot; alt=&quot;Turkmenistan Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Turkmenistan&lt;/span&gt;&lt;/span&gt;'&gt;Turkmenistan&lt;/option&gt;
                    &lt;option value=&quot;TC&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/tc.svg&quot; alt=&quot;Turks and Caicos Islands Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Turks and Caicos Islands&lt;/span&gt;&lt;/span&gt;'&gt;Turks and Caicos Islands&lt;/option&gt;
                    &lt;option value=&quot;TV&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/tv.svg&quot; alt=&quot;Tuvalu Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Tuvalu&lt;/span&gt;&lt;/span&gt;'&gt;Tuvalu&lt;/option&gt;
                    &lt;option value=&quot;UG&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ug.svg&quot; alt=&quot;Uganda Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Uganda&lt;/span&gt;&lt;/span&gt;'&gt;Uganda&lt;/option&gt;
                    &lt;option value=&quot;UA&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ua.svg&quot; alt=&quot;Ukraine Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Ukraine&lt;/span&gt;&lt;/span&gt;'&gt;Ukraine&lt;/option&gt;
                    &lt;option value=&quot;AE&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ae.svg&quot; alt=&quot;United Arab Emirates Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;United Arab Emirates&lt;/span&gt;&lt;/span&gt;'&gt;United Arab Emirates&lt;/option&gt;
                    &lt;option value=&quot;GB&quot; selected data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gb.svg&quot; alt=&quot;United Kingdom Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;United Kingdom&lt;/span&gt;&lt;/span&gt;'&gt;United Kingdom&lt;/option&gt;
                    &lt;option value=&quot;UM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/um.svg&quot; alt=&quot;United States Minor Outlying Islands Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;United States Minor Outlying Islands&lt;/span&gt;&lt;/span&gt;'&gt;United States Minor Outlying Islands&lt;/option&gt;
                    &lt;option value=&quot;US&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/us.svg&quot; alt=&quot;United States of America Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;United States of America&lt;/span&gt;&lt;/span&gt;'&gt;United States of America&lt;/option&gt;
                    &lt;option value=&quot;UY&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/uy.svg&quot; alt=&quot;Uruguay Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Uruguay&lt;/span&gt;&lt;/span&gt;'&gt;Uruguay&lt;/option&gt;
                    &lt;option value=&quot;UZ&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/uz.svg&quot; alt=&quot;Uzbekistan Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Uzbekistan&lt;/span&gt;&lt;/span&gt;'&gt;Uzbekistan&lt;/option&gt;
                    &lt;option value=&quot;VU&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/vu.svg&quot; alt=&quot;Vanuatu Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Vanuatu&lt;/span&gt;&lt;/span&gt;'&gt;Vanuatu&lt;/option&gt;
                    &lt;option value=&quot;VE&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ve.svg&quot; alt=&quot;Venezuela (Bolivarian Republic of) Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Venezuela (Bolivarian Republic of)&lt;/span&gt;&lt;/span&gt;'&gt;Venezuela (Bolivarian Republic of)&lt;/option&gt;
                    &lt;option value=&quot;VN&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/vn.svg&quot; alt=&quot;Vietnam Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Vietnam&lt;/span&gt;&lt;/span&gt;'&gt;Vietnam&lt;/option&gt;
                    &lt;option value=&quot;VG&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/vg.svg&quot; alt=&quot;Virgin Islands (British) Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Virgin Islands (British)&lt;/span&gt;&lt;/span&gt;'&gt;Virgin Islands (British)&lt;/option&gt;
                    &lt;option value=&quot;VI&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/vi.svg&quot; alt=&quot;Virgin Islands (U.S.) Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Virgin Islands (U.S.)&lt;/span&gt;&lt;/span&gt;'&gt;Virgin Islands (U.S.)&lt;/option&gt;
                    &lt;option value=&quot;GB-WLS&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/gb-wls.svg&quot; alt=&quot;Wales Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Wales&lt;/span&gt;&lt;/span&gt;'&gt;Wales&lt;/option&gt;
                    &lt;option value=&quot;WF&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/wf.svg&quot; alt=&quot;Wallis and Futuna Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Wallis and Futuna&lt;/span&gt;&lt;/span&gt;'&gt;Wallis and Futuna&lt;/option&gt;
                    &lt;option value=&quot;EH&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/eh.svg&quot; alt=&quot;Western Sahara Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Western Sahara&lt;/span&gt;&lt;/span&gt;'&gt;Western Sahara&lt;/option&gt;
                    &lt;option value=&quot;YE&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/ye.svg&quot; alt=&quot;Yemen Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Yemen&lt;/span&gt;&lt;/span&gt;'&gt;Yemen&lt;/option&gt;
                    &lt;option value=&quot;ZM&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/zm.svg&quot; alt=&quot;Zambia Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Zambia&lt;/span&gt;&lt;/span&gt;'&gt;Zambia&lt;/option&gt;
                    &lt;option value=&quot;ZW&quot; data-option-template='&lt;span class=&quot;d-flex align-items-center&quot;&gt;&lt;img class=&quot;avatar avatar-xss avatar-circle me-2&quot; src=&quot;../node_modules/flag-icon-css/flags/1x1/zw.svg&quot; alt=&quot;Zimbabwe Flag&quot; /&gt;&lt;span class=&quot;text-truncate&quot;&gt;Zimbabwe&lt;/span&gt;&lt;/span&gt;'&gt;Zimbabwe&lt;/option&gt;
                  &lt;/select&gt;
                &lt;/div&gt;
                &lt;!-- End Select --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="dropup" class="hs-docs-heading">
        Dropup <a class="anchorjs-link" href="#dropup" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>Trigger dropdown menus above elements by adding <code>"dropup": true</code> to the <code>data-hs-tom-select-options='{}'</code> attribute.</p>

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab11" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab11" href="#nav-result11" data-bs-toggle="pill" data-bs-target="#nav-result11" role="tab" aria-controls="nav-result11" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab11" href="#nav-html11" data-bs-toggle="pill" data-bs-target="#nav-html11" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent11">
          <div class="tab-pane fade p-4 show active" id="nav-result11" role="tabpanel" aria-labelledby="nav-resultTab11">
            <div style="max-width: 40rem;">
              <!-- Select -->
              <div class="tom-select-custom">
                <select class="js-select form-select" autocomplete="off" data-hs-tom-select-options='{
                          "placeholder": "Select a person...",
                          "hideSearch": true,
                          "dropup": true
                        }'>
                  <option value="">Select a person...</option>
                  <option value="4">Thomas Edison</option>
                  <option value="1">Nikola</option>
                  <option value="3">Nikola Tesla</option>
                  <option value="5">Arnold Schwarzenegger</option>
                </select>
              </div>
              <!-- End Select -->
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html11" role="tabpanel" aria-labelledby="nav-htmlTab11">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Select --&gt;
                &lt;div class="tom-select-custom"&gt;
                  &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot;
                        data-hs-tom-select-options='{
                          "placeholder": "Select a person...",
                          &quot;hideSearch&quot;: true,
                          "dropup": true
                        }'&gt;
                    &lt;option value=&quot;&quot;&gt;Select a person...&lt;/option&gt;
                    &lt;option value=&quot;4&quot;&gt;Thomas Edison&lt;/option&gt;
                    &lt;option value=&quot;1&quot;&gt;Nikola&lt;/option&gt;
                    &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                    &lt;option value=&quot;5&quot;&gt;Arnold Schwarzenegger&lt;/option&gt;
                  &lt;/select&gt;
                &lt;/div&gt;
                &lt;!-- End Select --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="alignment" class="hs-docs-heading">
        Alignment <a class="anchorjs-link" href="#alignment" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>By default, a dropdown menu is automatically positioned 100% from the top and along the left side of its parent. You can change this with the directional <code>.tom-select-custom-end</code> classes.</p>

      <p>Add <code>.tom-select-custom-end</code> to a <code>.tom-select-custom</code> to right align the select menu.</p>

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab12" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab12" href="#nav-result12" data-bs-toggle="pill" data-bs-target="#nav-result12" role="tab" aria-controls="nav-result12" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab12" href="#nav-html12" data-bs-toggle="pill" data-bs-target="#nav-html12" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent12">
          <div class="tab-pane fade p-4 show active" id="nav-result12" role="tabpanel" aria-labelledby="nav-resultTab12">
            <div class="ms-auto" style="max-width: 40rem;">
              <!-- Select -->
              <div class="tom-select-custom tom-select-custom-end">
                <select class="js-select form-select" autocomplete="off" data-hs-tom-select-options='{
                          "placeholder": "Select a person...",
                          "hideSearch": true,
                          "dropdownWidth": "11rem"
                        }'>
                  <option value="">Select a person...</option>
                  <option value="4">Thomas Edison</option>
                  <option value="1">Nikola</option>
                  <option value="3">Nikola Tesla</option>
                  <option value="5">Arnold</option>
                </select>
              </div>
              <!-- End Select -->
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html12" role="tabpanel" aria-labelledby="nav-htmlTab12">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Select --&gt;
                &lt;div class="tom-select-custom tom-select-custom-end"&gt;
                  &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot;
                        data-hs-tom-select-options='{
                          "placeholder": "Select a person...",
                          &quot;hideSearch&quot;: true,
                          "dropdownWidth": "11rem"
                        }'&gt;
                    &lt;option value=&quot;&quot;&gt;Select a person...&lt;/option&gt;
                    &lt;option value=&quot;4&quot;&gt;Thomas Edison&lt;/option&gt;
                    &lt;option value=&quot;1&quot;&gt;Nikola&lt;/option&gt;
                    &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                    &lt;option value=&quot;5&quot;&gt;Arnold&lt;/option&gt;
                  &lt;/select&gt;
                &lt;/div&gt;
                &lt;!-- End Select --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="responsive-alignment" class="hs-docs-heading">
        Responsive alignment <a class="anchorjs-link" href="#responsive-alignment" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <p>To align right the dropdown menu with the given breakpoint or larger, add <code>.tom-select-custom{-sm|-md|-lg|-xl|-xxl}-end</code>.</p>

      <p>Resize the window to see it in action.</p>

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab13" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab13" href="#nav-result13" data-bs-toggle="pill" data-bs-target="#nav-result13" role="tab" aria-controls="nav-result13" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab13" href="#nav-html13" data-bs-toggle="pill" data-bs-target="#nav-html13" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent13">
          <div class="tab-pane fade p-4 show active" id="nav-result13" role="tabpanel" aria-labelledby="nav-resultTab13">
            <div class="ms-auto" style="max-width: 40rem;">
              <!-- Select -->
              <div class="tom-select-custom tom-select-custom-lg-end">
                <select class="js-select form-select" autocomplete="off" data-hs-tom-select-options='{
                          "placeholder": "Select a person...",
                          "hideSearch": true,
                          "dropdownWidth": "11rem"
                        }'>
                  <option value="">Select a person...</option>
                  <option value="4">Thomas Edison</option>
                  <option value="1">Nikola</option>
                  <option value="3">Nikola Tesla</option>
                  <option value="5">Arnold</option>
                </select>
              </div>
              <!-- End Select -->
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html13" role="tabpanel" aria-labelledby="nav-htmlTab13">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- Select --&gt;
                &lt;div class="tom-select-custom tom-select-custom-lg-end"&gt;
                  &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot;
                        data-hs-tom-select-options='{
                          "placeholder": "Select a person...",
                          &quot;hideSearch&quot;: true,
                          "dropdownWidth": "11rem"
                        }'&gt;
                    &lt;option value=&quot;&quot;&gt;Select a person...&lt;/option&gt;
                    &lt;option value=&quot;4&quot;&gt;Thomas Edison&lt;/option&gt;
                    &lt;option value=&quot;1&quot;&gt;Nikola&lt;/option&gt;
                    &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                    &lt;option value=&quot;5&quot;&gt;Arnold&lt;/option&gt;
                  &lt;/select&gt;
                &lt;/div&gt;
                &lt;!-- End Select --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="sizing-example" class="hs-docs-heading">
        Sizing <a class="anchorjs-link" href="#sizing-example" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab9" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab9" href="#nav-result9" data-bs-toggle="pill" data-bs-target="#nav-result9" role="tab" aria-controls="nav-result9" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab9" href="#nav-html9" data-bs-toggle="pill" data-bs-target="#nav-html9" role="tab" aria-controls="nav-html2" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent9">
          <div class="tab-pane fade p-4 show active" id="nav-result9" role="tabpanel" aria-labelledby="nav-resultTab9">
            <div class="mb-3">
              <div style="max-width: 40rem;">
                <!-- Select -->
                <div class="tom-select-custom">
                  <select class="js-select form-select form-select-sm" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a person..."
                          }'>
                    <option value="">Select a person...</option>
                    <option value="4">Thomas Edison</option>
                    <option value="1">Nikola</option>
                    <option value="3">Nikola Tesla</option>
                    <option value="5">Arnold Schwarzenegger</option>
                  </select>
                </div>
                <!-- End Select -->
              </div>
            </div>

            <div class="mb-3">
              <div style="max-width: 40rem;">
                <!-- Select -->
                <div class="tom-select-custom">
                  <select class="js-select form-select" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a person..."
                          }'>
                    <option value="">Select a person...</option>
                    <option value="4">Thomas Edison</option>
                    <option value="1">Nikola</option>
                    <option value="3">Nikola Tesla</option>
                    <option value="5">Arnold Schwarzenegger</option>
                  </select>
                </div>
                <!-- End Select -->
              </div>
            </div>

            <div style="max-width: 40rem;">
              <!-- Select -->
              <div class="tom-select-custom">
                <select class="js-select form-select form-select-lg" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a person..."
                          }'>
                  <option value="">Select a person...</option>
                  <option value="4">Thomas Edison</option>
                  <option value="1">Nikola</option>
                  <option value="3">Nikola Tesla</option>
                  <option value="5">Arnold Schwarzenegger</option>
                </select>
              </div>
              <!-- End Select -->
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html9" role="tabpanel" aria-labelledby="nav-htmlTab9">
            <pre>
              <code class="language-markup" data-lang="html">
                  &lt;div class=&quot;mb-3&quot;&gt;
                    &lt;div style=&quot;max-width: 40rem;&quot;&gt;
                      &lt;!-- Select --&gt;
                      &lt;div class="tom-select-custom"&gt;
                        &lt;select class=&quot;js-select form-select form-select-sm&quot; autocomplete=&quot;off&quot;
                                data-hs-tom-select-options='{
                                  "placeholder": "Select a person..."
                                }'&gt;
                          &lt;option value=&quot;&quot;&gt;Select a person...&lt;/option&gt;
                          &lt;option value=&quot;4&quot;&gt;Thomas Edison&lt;/option&gt;
                          &lt;option value=&quot;1&quot;&gt;Nikola&lt;/option&gt;
                          &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                          &lt;option value=&quot;5&quot;&gt;Arnold Schwarzenegger&lt;/option&gt;
                        &lt;/select&gt;
                      &lt;/div&gt;
                      &lt;!-- End Select --&gt;
                    &lt;/div&gt;
                  &lt;/div&gt;

                  &lt;div class=&quot;mb-3&quot;&gt;
                    &lt;div style=&quot;max-width: 40rem;&quot;&gt;
                      &lt;!-- Select --&gt;
                      &lt;div class="tom-select-custom"&gt;
                        &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot;
                                data-hs-tom-select-options='{
                                  "placeholder": "Select a person..."
                                }'&gt;
                          &lt;option value=&quot;&quot;&gt;Select a person...&lt;/option&gt;
                          &lt;option value=&quot;4&quot;&gt;Thomas Edison&lt;/option&gt;
                          &lt;option value=&quot;1&quot;&gt;Nikola&lt;/option&gt;
                          &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                          &lt;option value=&quot;5&quot;&gt;Arnold Schwarzenegger&lt;/option&gt;
                        &lt;/select&gt;
                      &lt;/div&gt;
                      &lt;!-- End Select --&gt;
                    &lt;/div&gt;
                  &lt;/div&gt;

                  &lt;div style=&quot;max-width: 40rem;&quot;&gt;
                    &lt;!-- Select --&gt;
                    &lt;div class="tom-select-custom"&gt;
                      &lt;select class=&quot;js-select form-select form-select-lg&quot; autocomplete=&quot;off&quot;
                              data-hs-tom-select-options='{
                                "placeholder": "Select a person..."
                              }'&gt;
                        &lt;option value=&quot;&quot;&gt;Select a person...&lt;/option&gt;
                        &lt;option value=&quot;4&quot;&gt;Thomas Edison&lt;/option&gt;
                        &lt;option value=&quot;1&quot;&gt;Nikola&lt;/option&gt;
                        &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                        &lt;option value=&quot;5&quot;&gt;Arnold Schwarzenegger&lt;/option&gt;
                      &lt;/select&gt;
                    &lt;/div&gt;
                    &lt;!-- End Select --&gt;
                  &lt;/div&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="modal-example" class="hs-docs-heading">
        Modal example <a class="anchorjs-link" href="#modal-example" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab10" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab10" href="#nav-result10" data-bs-toggle="pill" data-bs-target="#nav-result10" role="tab" aria-controls="nav-result10" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab10" href="#nav-html10" data-bs-toggle="pill" data-bs-target="#nav-html10" role="tab" aria-controls="nav-html10" aria-selected="false">HTML</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent10">
          <div class="tab-pane fade p-4 show active" id="nav-result10" role="tabpanel" aria-labelledby="nav-resultTab10">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exampleModal">Open modal</button>

            <!-- Modal -->
            <div class="modal fade" id="exampleModal" aria-labelledby="exampleModalLabel" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Select</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>

                  <div class="modal-body">
                    <!-- Select -->
                    <div class="tom-select-custom">
                      <select class="js-select form-select" autocomplete="off">
                        <option value="">Select a person...</option>
                        <option value="4">Thomas Edison</option>
                        <option value="1">Nikola</option>
                        <option value="3">Nikola Tesla</option>
                        <option value="5">Arnold Schwarzenegger</option>
                      </select>
                    </div>
                    <!-- End Select -->
                  </div>

                  <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary">Save changes</button>
                  </div>
                </div>
              </div>
            </div>
            <!-- End Modal -->
          </div>

          <div class="tab-pane fade" id="nav-html10" role="tabpanel" aria-labelledby="nav-htmlTab10">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exampleModal"&gt;Open modal&lt;/button&gt;

                &lt;!-- Modal --&gt;
                &lt;div class="modal fade" id="exampleModal" aria-labelledby="exampleModalLabel" aria-hidden="true"&gt;
                  &lt;div class="modal-dialog"&gt;
                    &lt;div class="modal-content"&gt;
                      &lt;div class="modal-header"&gt;
                        &lt;h5 class="modal-title" id="exampleModalLabel"&gt;Select&lt;/h5&gt;
                        &lt;button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"&gt;&lt;/button&gt;
                      &lt;/div&gt;

                      &lt;div class="modal-body"&gt;
                        &lt;!-- Select --&gt;
                        &lt;div class="tom-select-custom"&gt;
                         &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot;
                                 data-hs-tom-select-options='{
                                   "placeholder": "Select a person..."
                                 }'&gt;
                           &lt;option value=&quot;&quot;&gt;Select a person...&lt;/option&gt;
                           &lt;option value=&quot;4&quot;&gt;Thomas Edison&lt;/option&gt;
                           &lt;option value=&quot;1&quot;&gt;Nikola&lt;/option&gt;
                           &lt;option value=&quot;3&quot;&gt;Nikola Tesla&lt;/option&gt;
                           &lt;option value=&quot;5&quot;&gt;Arnold Schwarzenegger&lt;/option&gt;
                          &lt;/select&gt;
                        &lt;/div&gt;
                        &lt;!-- End Select --&gt;
                      &lt;/div&gt;

                      &lt;div class="modal-footer"&gt;
                        &lt;button type="button" class="btn btn-white" data-bs-dismiss="modal"&gt;Close&lt;/button&gt;
                        &lt;button type="button" class="btn btn-primary"&gt;Save changes&lt;/button&gt;
                      &lt;/div&gt;
                    &lt;/div&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
                &lt;!-- End Modal --&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="validation" class="hs-docs-heading">
        Validation <a class="anchorjs-link" href="#validation" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Header -->
        <div class="card-header">
          <!-- Nav -->
          <ul class="nav nav-segment" id="navTab10" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="nav-resultTab18" href="#nav-result18" data-bs-toggle="pill" data-bs-target="#nav-result18" role="tab" aria-controls="nav-result18" aria-selected="true">Preview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-htmlTab18" href="#nav-html18" data-bs-toggle="pill" data-bs-target="#nav-html18" role="tab" aria-controls="nav-html18" aria-selected="false">HTML</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="nav-jsTab18" href="#nav-js18" data-bs-toggle="pill" data-bs-target="#nav-js18" role="tab" aria-controls="nav-js18" aria-selected="false">JS</a>
            </li>
          </ul>
          <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Tab Content -->
        <div class="tab-content" id="navTabContent10">
          <div class="tab-pane fade p-4 show active" id="nav-result18" role="tabpanel" aria-labelledby="nav-resultTab18">
            <div style="max-width: 40rem;">
              <form class="js-validate needs-validation" novalidate>
                <div class="mb-4">
                  <!-- Select -->
                  <div class="tom-select-custom" data-hs-validation-validate-class>
                    <select class="js-select form-select" autocomplete="off" required data-hs-tom-select-options='{
                            "placeholder": "Select a cluster",
                            "hideSearch": true
                          }'>
                      <option value="">Select a cluster</option>
                      <option value="4">NS 1</option>
                      <option value="1">NS 2</option>
                      <option value="3">NS 3</option>
                      <option value="5">NS 4</option>
                    </select>
                  </div>
                  <!-- End Select -->

                  <span class="invalid-feedback">Please select a valid cluster.</span>
                </div>

                <button type="submit" class="btn btn-primary">Validate</button>
              </form>
            </div>
          </div>

          <div class="tab-pane fade" id="nav-html18" role="tabpanel" aria-labelledby="nav-htmlTab18">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;form&gt;
                  &lt;div class=&quot;mb-4&quot;&gt;
                    &lt;label class=&quot;form-label w-100&quot; for=&quot;signupSrPassword&quot; tabindex=&quot;0&quot;&gt;Cluster&lt;/label&gt;

                    &lt;!-- Select --&gt;
                    &lt;div class=&quot;tom-select-custom&quot; data-hs-validation-validate-class&gt;
                      &lt;select class=&quot;js-select form-select&quot; autocomplete=&quot;off&quot;
                              required
                              data-hs-tom-select-options='{
                                &quot;placeholder&quot;: &quot;Select a cluster&quot;,
                                &quot;hideSearch&quot;: true
                              }'&gt;
                        &lt;option value=&quot;&quot;&gt;Select a cluster&lt;/option&gt;
                        &lt;option value=&quot;4&quot;&gt;NS 1&lt;/option&gt;
                        &lt;option value=&quot;1&quot;&gt;NS 2&lt;/option&gt;
                        &lt;option value=&quot;3&quot;&gt;NS 3&lt;/option&gt;
                        &lt;option value=&quot;5&quot;&gt;NS 4&lt;/option&gt;
                      &lt;/select&gt;
                    &lt;/div&gt;
                    &lt;!-- End Select --&gt;

                    &lt;span class=&quot;invalid-feedback&quot;&gt;Please select a valid cluster.&lt;/span&gt;
                  &lt;/div&gt;

                  &lt;button type=&quot;submit&quot; class=&quot;btn btn-primary&quot;&gt;Validate&lt;/button&gt;
                &lt;/form&gt;
              </code>
            </pre>
          </div>

          <div class="tab-pane fade" id="nav-js18" role="tabpanel" aria-labelledby="nav-jsTab18">
            <pre>
              <code class="language-markup" data-lang="html">
                &lt;!-- JS Front --&gt;
                &lt;script src=&quot;../assets/js/hs.bs-validation.js&quot;&gt;&lt;/script&gt;
              </code>
            </pre>
          </div>
        </div>
        <!-- End Tab Content -->
      </div>
      <!-- End Card -->

      <!-- Heading -->
      <h2 id="methods" class="hs-docs-heading">
        Methods <a class="anchorjs-link" href="#methods" aria-label="Anchor" data-anchorjs-icon="#"></a>
      </h2>
      <!-- End Heading -->

      <!-- Card -->
      <div class="card">
        <!-- Table -->
        <div class="table-responsive">
          <table class="table">
            <thead class="thead-light">
              <tr>
                <th>Parameters</th>
                <th style="width: 50%;">Description</th>
                <th class="text-nowrap">Default value</th>
              </tr>
            </thead>

            <tbody>
              <tr>
                <td>
                  <p><code>width</code></p>
                </td>
                <td>Set width for select field.</td>
                <td><code>null</code></td>
              </tr>

              <tr>
                <td>
                  <p><code>dropdownWidth</code></p>
                </td>
                <td>Set width for dropdown.</td>
                <td><code>null</code></td>
              </tr>

              <tr>
                <td>
                  <p><code>dropdownLeft</code></p>
                </td>
                <td>Align the drawdown to the left when the hs_smart_position plugin is enabled.</td>
                <td><code>false</code></td>
              </tr>

              <tr>
                <td>
                  <p><code>hideSearch</code></p>
                </td>
                <td>Remove search field.</td>
                <td><code>false</code></td>
              </tr>

              <tr>
                <td>
                  <p><code>disableSearch</code></p>
                </td>
                <td>Set disabled state for search field.</td>
                <td><code>false</code></td>
              </tr>

              <tr>
                <td>
                  <p><code>singleMultiple</code></p>
                </td>
                <td>Enable "singleMultiple" mode for multiple select.</td>
                <td><code>false</code></td>
              </tr>

              <tr>
                <td>
                  <p><code>plugins</code></p>
                </td>
                <td>Tom sleect and custom plugins.</td>
                <td><code>{hs_smart_position: {}}</code></td>
              </tr>

              <tr>
                <td>
                  <p><code>searchInDropdown</code></p>
                </td>
                <td>Insert search field in dropdow.</td>
                <td><code>true</code></td>
              </tr>

              <tr>
                <td>
                  <p><code>hidePlaceholderOnSearch</code></p>
                </td>
                <td>Hide placeholder on searhc or if item is seleted. <code>searchInDropdown</code> should be <code>false</code>.</td>
                <td><code>false</code></td>
              </tr>

              <tr>
                <td>
                  <p><code>dropup</code></p>
                </td>
                <td>Place the menu on top.</td>
                <td><code>false</code></td>
              </tr>

              <tr>
                <td>
                  <p><code>render</code></p>
                </td>
                <td>Custom render.</td>
                <td><code>
                render: {
                  'option': function(data, escape) {
                    return data.optionTemplate || `&lt;div&gt;${data.text}&lt;/div&gt;`
                  },
                  'item': function(data, escape) {
                    return data.optionTemplate || `&lt;div&gt;${data.text}&lt;/div&gt;`
                  }
                }
              </code></td>
              </tr>
            </tbody>
          </table>
        </div>
        <!-- End Table -->
      </div>
      <!-- End Card -->
    </div>
    <!-- End Content -->
  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- ========== SECONDARY CONTENTS ========== -->
  <!-- Go To -->
  <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
    <i class="bi-chevron-up"></i>
  </a>
  <!-- ========== END SECONDARY CONTENTS ========== -->

  <!-- JS Implementing Plugins -->
  <script src="../assets/js/vendor.min.js"></script>

  <!-- JS Front -->
  <script src="../assets/js/theme.min.js"></script>

  <!-- JS Plugins Init. -->
  <script>
    (function() {
      // INITIALIZATION OF HEADER
      // =======================================================
      new HSHeader('#header').init()


      // INITIALIZATION OF NAV SCROLLER
      // =======================================================
      new HsNavScroller('.js-nav-scroller', {
        delay: 400,
        offset: 140
      })


      // INITIALIZATION OF LISTJS COMPONENT
      // =======================================================
      HSCore.components.HSList.init('#docsSearch');
      const docsSearch = HSCore.components.HSList.getItem('docsSearch');


      // GET JSON FILE RESULTS
      // =======================================================
      fetch('../assets/json/docs-search.json')
      .then(response => response.json())
      .then(data => {
        docsSearch.add(data)
      })


      // INITIALIZATION OF GO TO
      // =======================================================
      new HSGoTo('.js-go-to')


      // INITIALIZATION OF SELECT
      // =======================================================
      HSCore.components.HSTomSelect.init('.js-select')

      // INITIALIZATION OF BOOTSTRAP VALIDATION
      // =======================================================
      HSBsValidation.init('.js-validate', {
        onSubmit: data => {
          data.event.preventDefault()
          alert('Submited')
        }
      })
    })()
  </script>
</body>

<!-- Mirrored from htmlstream.com/front-dashboard/documentation/select.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 30 Aug 2022 07:13:05 GMT -->
</html>