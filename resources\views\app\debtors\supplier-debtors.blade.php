@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">Supplier Debtors</x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Debt::class)
            <a href="" class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target=".supplier-debtor-modal">
                <!-- <i class="icon ion-md-add"></i> -->
                @lang('crud.common.create') Supplier Debtor
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="searchbar mt-4 mb-5">
        <form class="d-flex" id="filter">

            <div class="me-3 _200px">
                <input type="hidden" name="from" value="{{ request()->from }}">
                <input type="hidden" name="to" value="{{ request()->to }}">
                <label class="form-label">Dates</label>
                <button id="js-daterangepicker-predefined" type="button" class="btn btn-white _100">
                  <i class="bi-calendar-week me-1"></i>
                  <span class="js-daterangepicker-predefined-preview"></span>
                </button>
            </div>
            
            <div class="_200px me-3">
                <label class="form-label">Branch</label>
                <div class="tom-select-custom _100">
                  <select class="js-select form-select branch-filter" name="branch_id" autocomplete="off" data-hs-tom-select-options='{
                    "placeholder": "Select a branch..."
                  }'>
                  <option></option>
                  @foreach( App\Models\Branch::get() as $branch)
                    <option value="{{ $branch->id }}" @if($branch->id == request()->branch_id ) selected @endif>
                      {{ $branch->name }}
                    </option>
                  @endforeach
                  </select>
                </div>
            </div>

            <div class="me-3 _200px">
                <label class="form-label">Category</label>
                <div class="tom-select-custom me-2 _100">
                  <select class="js-select form-select category-filter" name="category_id" autocomplete="off" data-hs-tom-select-options='{
                    "placeholder": "Select a category..."
                  }'>
                  <option></option>
                  @foreach( App\Models\Category::where('applied_to', 'debtors')->get() as $category)
                    <option value="{{ $category->id }}" @if($category->id == request()->category_id ) selected @endif>
                      {{ $category->name }}
                    </option>
                  @endforeach
                  </select>
                </div>
            </div>
            
            <div>
                <label class="form-label transparent">_</label>
               <div class="input-group-append me-2">
                    <button type="submit" class="btn btn-primary">
                        Search
                    </button>
                </div> 
            </div>
            
        </form>
    </div>



    <!-- Table -->
    <div class="card card-table">
        <div class="table-responsive mt-3">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">

                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            Operator
                        </th>                      
                        <th class="text-left">
                            Date
                        </th>                 
                        <th class="text-left">
                            Customer
                        </th>
                        <th class="text-left">
                            Branch
                        </th>
                        <th class="text-left">
                            Category
                        </th>
                        <th class="text-left">
                            Total Amount
                        </th>                      
                        <th class="text-left">
                            Balance
                        </th>                            
                        <th class="text-left">
                            Description
                        </th>
                        <th class="text-center">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($debtors as $debtor)
                    <tr>
                        <td>{{ $debtor->createdBy->name ?? '-' }}</td>
                        <td>{{ $debtor->created_at ?? '-' }}</td>
                        <td>{{ $debtor->debtable->name ?? '-' }}</td>
                        <td>{{ $debtor->branch->name ?? '-' }}</td>
                        <td>{{ $debtor->category ?? '-' }}</td>
                        <td>{{ _money( $debtor->amount_total ) ?? '-' }}</td>
                        <td>{{ _money( $debtor->balance ) ?? '-' }}</td>
                        <td>{{ $debtor->description ?? '-' }}</td>

                        <td class="text-center" style="width: 134px;">
                            <div role="group" aria-label="Row Actions" class="btn-group">

                                @if( $debtor->amount_total > $debtor->amount_paid)
                            
                                    @can('update', $debtor)
                                    <a href="#">
                                        <button type="button" data-bs-toggle="modal" data-id="{{ $debtor->id }}" data-bs-target=".settle-debtor-modal" class="btn btn-outline-primary m-1 settle-debtor-btn">
                                            Settle Debt
                                        </button>
                                    </a>
                                    @endcan   
                                @endcan

                                    @can('update', $debtor)
                                    <a href="{{ route('debts.show', $debtor) }}">
                                        <button type="button" class="btn btn-light m-1">
                                            View
                                        </button>
                                    </a>
                                    @endcan 

                                @if( $debtor->amount_total > $debtor->amount_paid)
                                    @can('update', $debtor)
                                    <a href="{{ route('debts.edit', $debtor) }}?type=customer">
                                        <button type="button" class="btn btn-light m-1">
                                            edit
                                        </button>
                                    </a>
                                    @endcan 


                                    @can('delete', $debtor)
                                    <form
                                        action="{{ route('debts.destroy', $debtor) }}"
                                        method="POST"
                                        onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                    >
                                        @csrf @method('DELETE')
                                        <button
                                            type="submit"
                                            class="btn btn-light text-danger m-1"
                                        >
                                            <!-- <i class="icon ion-md-trash"></i> --> del
                                        </button>
                                    </form>
                                    @endcan
                                @endif

                            </div>
                        </td>
                    </tr>
                    @empty
                   
                    @endforelse

                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td>
                            <span class="h4 text-info"> {{ _money( $debtors->sum('amount_total') ) }} </span>
                        </td>                    
                        <td>
                            <span class="h4 text-info"> {{ _money( $debtors->sum('amount_total') - $debtors->sum('amount_paid') ) }} </span>
                        </td>
                        <td></td>
                        <td></td>
                    </tr>

                </tbody>

            </table>

        </div>
    </div>



</div>
@endsection





@push('scripts')
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
        dataTableBtn()   
    });
</script>

@endpush