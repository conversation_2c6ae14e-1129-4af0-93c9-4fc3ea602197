@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON>er -->
    <x-page-header>
        <x-slot name="title">Accounts</x-slot>
        <x-slot name="controls">
            
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <div class="searchbar mt-4 mb-5">
        <form class="d-flex" id="filter">

          <div class="me-3">
            <label class="form-label">Type</label>
            <div class="tom-select-custom _200px">
              <select class="js-select form-select branch-filter" onclick="filter.submit()" name="type" autocomplete="off" data-hs-tom-select-options='{
                "placeholder": "Select a Debtor Type..."
              }'>
                <option value="customers" @if('customers' == request()->type ) selected @endif> Customers </option>
                <option value="suppliers" @if('suppliers' == request()->type ) selected @endif> Suppliers </option>
              </select>
            </div>
          </div>


          <div class="me-3">
            <label class="form-label">{{ ucfirst( request()->type ?? 'Reference Entity' ) }}</label>
              <div class="tom-select-custom _200px">
                <select class="js-select form-select branch-filter" name="debtable_id" autocomplete="off" data-hs-tom-select-options='{
                  "placeholder": "Select a branch..."
                }'>
                <option></option>
                @foreach( $debtables as $debtable)
                  <option value="{{ $debtable->id }}" @if($debtable->id == request()->debtable_id ) selected @endif>
                    {{ $debtable->name }}
                  </option>
                @endforeach
                </select>
              </div>
          </div>

          <div class="me-3 _200px">
            <input type="hidden" name="from" value="{{ request()->from }}">
            <input type="hidden" name="to" value="{{ request()->to }}">
            <label class="form-label">Dates</label>
            <button id="js-daterangepicker-predefined" type="button" class="btn btn-white _100">
              <i class="bi-calendar-week me-1"></i>
              <span class="js-daterangepicker-predefined-preview"></span>
            </button>
          </div>



          <div class="me-3">
            <label class="form-label">Branch</label>
              <div class="tom-select-custom _200px">
                <select class="js-select form-select branch-filter" name="branch_id" autocomplete="off" data-hs-tom-select-options='{
                  "placeholder": "Select a branch..."
                }'>
                <option></option>
                @foreach( App\Models\Branch::get() as $branch)
                  <option value="{{ $branch->id }}" @if($branch->id == request()->branch_id ) selected @endif>
                    {{ $branch->name }}
                  </option>
                @endforeach
                </select>
              </div>
          </div>

          <div class="me-3">
            <label class="form-label">Category</label>
            <div class="tom-select-custom _200px">
              <select class="js-select form-select category-filter" name="category" autocomplete="off" data-hs-tom-select-options='{
                "placeholder": "Select a category..."
              }'>
              <option value="0">All Categories</option>
              @foreach( App\Models\Category::where("applied_to", "debtors")->get() as $category)
                <option value="{{ $category->name }}" @if($category->name == request()->category ) selected @endif>
                  {{ $category->name }}
                </option>
              @endforeach
              </select>
            </div>
          </div>


          <div class="">
              <label class="form-label transparent">_</label>
              <div class="input-group-append">
                  <button type="submit" class="btn btn-primary">
                      Search
                  </button>
              </div>
          </div>


        </form>
    </div>


    <!-- Table -->
    <div class="card card-table">
        <div class="table-responsive mt-3">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">

                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            #
                        </th>    
                        <th class="text-left">
                            Reference Name
                        </th>                  
                        <th class="text-left">
                            Description
                        </th>                                        
    
                        <th class="text-right">
                            Total
                        </th>                         
                        <th class="text-right">
                            Paid
                        </th>                      
                        <th class="text-right">
                            Balance
                        </th>                            

                    </tr>
                </thead>
                <tbody>
                    @forelse($accounts as $key => $account)
                    <tr>
                        <td class="text-left">{{ $key ?? '-' }}</td>
                        <td class="text-left">

                          <a href="/account-details/{{ $account[0]->id }}">
                            <button type="button" class="btn btn-sm btn-light mr-2">
                                Details
                            </button>
                          </a>
                          {{ $account[0]->debtable->name ?? '-' }}
                        </td>
                        <td class="text-left">
                            {{ $account[0]->description ?? '-' }}
                        </td>
                        <td class="text-right text-success">
                          {{ _money($account->sum('amount_total') ) }}
                        </td>
                        <td class="text-right text-danger">
                          {{ _money($account->sum('amount_paid') ) }}
                        </td>
                        <td class="text-right text-dark">
                            {{ _money( $account->sum('balance') ) }}
                        </td>

                    </tr>
                    @empty
                    @endforelse

                </tbody>
                <tfoot>
<!--                     <tr>
                        <td></td>
                        <td>Total</td>
                        <td></td>
                        <td>
                            <span class="h4 text-info"> {{ _money( $accounts->sum('amount_total') ) }} </span>
                        </td>                    
                        <td>
                            <span class="h4 text-info"> {{ _money( $accounts->sum('amount') ) }} </span>
                        </td>
                        <td>
                            <span class="h4 text-info"> {{ _money( $accounts->sum('amount_total') - $accounts->sum('amount_paid') ) }} </span>
                        </td>
                    </tr> -->

                </tfoot>

            </table>

        </div>
    </div>



</div>
@endsection





@push('scripts')
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
   dataTableBtn()
  });
</script>


@endpush