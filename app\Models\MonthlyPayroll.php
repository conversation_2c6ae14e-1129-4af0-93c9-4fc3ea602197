<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

use App\Traits\CreatedUpdatedTrait;

class MonthlyPayroll extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'basic_pay',
        'gross_pay',
        'net_pay',
        'total_deductions',
        'total_allowances',
        'overtime_pay',
        'overtime_hours',
        'tax_amount',
        'status',
        'date_to',
        'date_from',
        'approved_by',
        'approved_at',
        'created_by',
        'updated_by',
        'employee_id',
    ];


    protected $casts = [
        'date_from' => 'datetime:Y-m-d',
        'date_to' => 'datetime:Y-m-d',
        'approved_at' => 'datetime',
        'basic_pay' => 'decimal:2',
        'gross_pay' => 'decimal:2',
        'total_deductions' => 'decimal:2',
        'total_allowances' => 'decimal:2',
        'overtime_pay' => 'decimal:2',
        'overtime_hours' => 'decimal:2',
        'tax_amount' => 'decimal:2',
    ];

    protected $appends = ['month', 'gross', 'payee', 'net_pay_calculated'];

    protected $searchableFields = ['*'];

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function getMonthAttribute($value) {
        return $this->date_from ? $this->date_from->format("M") : "-";
    }

    public function getPayeeAttribute() {
        return \Facades\App\Libraries\EmployeeHandler::payeeTax( $this->gross );
    }

    public function getNetPayAttribute($value) {
        // If net_pay is stored in database and greater than 0, return it, otherwise calculate
        if ($value !== null && $value > 0) {
            return $value;
        }

        // Calculate net pay from stored values or fallback to computed values
        $grossPay = $this->gross_pay ?: $this->gross;
        $totalDeductions = $this->total_deductions ?: $this->deductions()->sum('amount');
        $totalAllowances = $this->total_allowances ?: $this->contributions()->sum('amount');
        $taxAmount = $this->tax_amount ?: $this->payee;

        return $grossPay - $totalDeductions - $taxAmount + $totalAllowances;
    }

    public function getGrossAttribute() {
        // If gross_pay is stored in database, return it, otherwise calculate
        if ($this->gross_pay !== null) {
            return $this->gross_pay;
        }

        return $this->basic_pay + $this->contributions()->sum("amount");
    }

    public function calculateNetPay() {
        $grossPay = $this->gross_pay ?: $this->basic_pay;
        $totalDeductions = $this->total_deductions ?: 0;
        $totalAllowances = $this->total_allowances ?: 0;
        $taxAmount = $this->tax_amount ?: 0;

        return $grossPay - $totalDeductions - $taxAmount + $totalAllowances;
    }

    public function getNetPayCalculatedAttribute() {
        return $this->calculateNetPay();
    }

    public function payrollItems()
    {
        return $this->hasMany(PayrollItem::class);
    }

    public function contributions()
    {
        return $this->hasMany(PayrollItem::class)->where("type", "CONTRIBUTION");
        // ->whereHas('deductionContribution', function($query) {
        //     $query->where('apply_mode', 'CONTRIBUTION');
        // });
    }

    public function deductions()
    {
        return $this->hasMany(PayrollItem::class)->where("type", "DEDUCTION");
        // ->whereHas('deductionContribution', function($query) {
        //     $query->where('apply_mode', 'DEDUCTION');
        // });
    }


}
