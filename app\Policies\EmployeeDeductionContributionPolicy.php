<?php

namespace App\Policies;

use App\Models\User;
use App\Models\EmployeeDeductionContribution;
use Illuminate\Auth\Access\HandlesAuthorization;

class EmployeeDeductionContributionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo('list employee-deduction-contributions');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\EmployeeDeductionContribution  $employeeDeductionContribution
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, EmployeeDeductionContribution $employeeDeductionContribution)
    {
        return $user->hasPermissionTo('view employee-deduction-contributions');
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo('create employee-deduction-contributions');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\EmployeeDeductionContribution  $employeeDeductionContribution
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, EmployeeDeductionContribution $employeeDeductionContribution)
    {
        return $user->hasPermissionTo('update employee-deduction-contributions');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\EmployeeDeductionContribution  $employeeDeductionContribution
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, EmployeeDeductionContribution $employeeDeductionContribution)
    {
        return $user->hasPermissionTo('delete employee-deduction-contributions');
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\EmployeeDeductionContribution  $employeeDeductionContribution
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, EmployeeDeductionContribution $employeeDeductionContribution)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\EmployeeDeductionContribution  $employeeDeductionContribution
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, EmployeeDeductionContribution $employeeDeductionContribution)
    {
        return false;
    }
}
