@extends('layouts.app')

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">people</x-slot>
        <x-slot name="title">Receivables</x-slot>
        <x-slot name="subtitle">Customer debtors and outstanding invoices</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Receivables</li>
        </x-slot>
    </x-page-header>

    <!-- Analytics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-sm avatar-soft-primary avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-people"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <span class="d-block h5 mb-0">{{ number_format($analytics['total_customers']) }}</span>
                            <span class="d-block fs-6 text-muted">Customer Debtors</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-sm avatar-soft-success avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-currency-dollar"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <span class="d-block h5 mb-0">{{ _money($analytics['total_outstanding']) }}</span>
                            <span class="d-block fs-6 text-muted">Total Outstanding</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-sm avatar-soft-warning avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-exclamation-triangle"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <span class="d-block h5 mb-0">{{ number_format($analytics['overdue_invoices']) }}</span>
                            <span class="d-block fs-6 text-muted">Overdue Invoices</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-sm avatar-soft-danger avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-clock"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <span class="d-block h5 mb-0">{{ _money($analytics['overdue_amount']) }}</span>
                            <span class="d-block fs-6 text-muted">Overdue Amount</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="card-header-title">
                <i class="bi bi-funnel me-2"></i>Filters & Search
            </h6>
        </div>
        <div class="card-body">
            <form action="{{ route('receivables.index') }}" method="GET" class="row g-3">
                <!-- Search -->
                <div class="col-md-4">
                    <label class="form-label">Search</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" name="search"
                               placeholder="Search customers..."
                               value="{{ request('search') }}">
                    </div>
                </div>

                <!-- Amount From -->
                <div class="col-md-2">
                    <label class="form-label">Amount From</label>
                    <input type="number" class="form-control" name="amount_from" step="0.01" value="{{ request('amount_from') }}">
                </div>

                <!-- Amount To -->
                <div class="col-md-2">
                    <label class="form-label">Amount To</label>
                    <input type="number" class="form-control" name="amount_to" step="0.01" value="{{ request('amount_to') }}">
                </div>

                <!-- Date From -->
                <div class="col-md-2">
                    <label class="form-label">Date From</label>
                    <input type="date" class="form-control" name="date_from" value="{{ request('date_from') }}">
                </div>

                <!-- Date To -->
                <div class="col-md-2">
                    <label class="form-label">Date To</label>
                    <input type="date" class="form-control" name="date_to" value="{{ request('date_to') }}">
                </div>

                <!-- Action Buttons -->
                <div class="col-12 d-flex gap-2 justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-funnel me-1"></i>Apply Filters
                    </button>
                    <a href="{{ route('receivables.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-x-circle me-1"></i>Clear All
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Customer Debtors Table -->
    <div class="card">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">Customer Debtors</h4>
                </div>
                <div class="col-auto">
                    <span class="text-muted">{{ $customers->total() }} customer(s) with outstanding balances</span>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                    <thead class="thead-light">
                        <tr>
                            <th style="width: 25%">Customer</th>
                            <th style="width: 15%">Contact</th>
                            <th style="width: 12%">Invoices</th>
                            <th style="width: 15%">Outstanding</th>
                            <th style="width: 15%">Oldest Invoice</th>
                            <th style="width: 10%">Status</th>
                            <th style="width: 12%" class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($customers as $customer)
                        <tr>
                            <!-- Customer -->
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0 me-3">
                                        <div class="avatar avatar-sm avatar-circle">
                                            <span class="avatar-initials bg-primary text-white">
                                                {{ substr($customer->name, 0, 1) }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0">{{ $customer->name }}</h6>
                                        @if($customer->company)
                                            <small class="text-muted">{{ $customer->company }}</small>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            
                            <!-- Contact -->
                            <td>
                                @if($customer->email)
                                    <span class="fw-semibold">{{ $customer->email }}</span>
                                @endif
                                @if($customer->phone)
                                    <small class="text-muted d-block">{{ $customer->phone }}</small>
                                @endif
                            </td>
                            
                            <!-- Invoices -->
                            <td>
                                <span class="fw-semibold">{{ $customer->total_invoices }}</span>
                                <small class="text-muted d-block">Outstanding</small>
                            </td>
                            
                            <!-- Outstanding Amount -->
                            <td>
                                <span class="fw-semibold text-danger h6">{{ _money($customer->total_outstanding) }}</span>
                            </td>
                            
                            <!-- Oldest Invoice -->
                            <td>
                                @if($customer->oldest_invoice)
                                    <span class="fw-semibold">{{ \Carbon\Carbon::parse($customer->oldest_invoice)->format('M d, Y') }}</span>
                                    <small class="text-muted d-block">{{ \Carbon\Carbon::parse($customer->oldest_invoice)->diffForHumans() }}</small>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            
                            <!-- Status -->
                            <td>
                                @if($customer->oldest_invoice && \Carbon\Carbon::parse($customer->oldest_invoice)->addDays(60)->isPast())
                                    <span class="badge bg-danger">Aging</span>
                                @elseif($customer->oldest_invoice && \Carbon\Carbon::parse($customer->oldest_invoice)->addDays(30)->isPast())
                                    <span class="badge bg-warning">Old</span>
                                @else
                                    <span class="badge bg-success">Current</span>
                                @endif
                            </td>
                            
                            <!-- Actions -->
                            <td class="text-center">
                                <div class="d-flex gap-1 justify-content-center">
                                    <a href="{{ route('receivables.show', $customer) }}"
                                       class="btn btn-outline-primary btn-sm"
                                       title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <button type="button"
                                            class="btn btn-outline-success btn-sm"
                                            title="Record Payment"
                                            onclick="openPaymentModal({
                                                paymentType: 'receivable',
                                                entityId: '{{ $customer->id }}'
                                            })">
                                        <i class="bi bi-credit-card"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center p-5">
                                <x-empty-state
                                    icon="people"
                                    title="No customer debtors found"
                                    description="No customers have outstanding invoices matching your current filters."
                                />
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        
        @if($customers->hasPages())
        <div class="card-footer">
            {{ $customers->appends(request()->query())->links() }}
        </div>
        @endif
    </div>
</div>

<!-- Include Payment Modal -->
<x-payment-modal />

@push('scripts')
<script>
$(document).ready(function() {
    // Date range validation
    $('input[name="date_from"]').on('change', function() {
        const dateTo = $('input[name="date_to"]').val();
        if (dateTo && $(this).val() > dateTo) {
            alert('Start date cannot be later than end date');
            $(this).val('');
        }
    });
    
    $('input[name="date_to"]').on('change', function() {
        const dateFrom = $('input[name="date_from"]').val();
        if (dateFrom && $(this).val() < dateFrom) {
            alert('End date cannot be earlier than start date');
            $(this).val('');
        }
    });

    // Amount range validation
    $('input[name="amount_from"]').on('change', function() {
        const amountTo = $('input[name="amount_to"]').val();
        if (amountTo && parseFloat($(this).val()) > parseFloat(amountTo)) {
            alert('Minimum amount cannot be greater than maximum amount');
            $(this).val('');
        }
    });
    
    $('input[name="amount_to"]').on('change', function() {
        const amountFrom = $('input[name="amount_from"]').val();
        if (amountFrom && parseFloat($(this).val()) < parseFloat(amountFrom)) {
            alert('Maximum amount cannot be less than minimum amount');
            $(this).val('');
        }
    });
});
</script>
@endpush
@endsection
