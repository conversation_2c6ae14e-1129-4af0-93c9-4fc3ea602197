<?php

namespace App\Policies;

use App\Models\User;
use App\Models\WorkHoursConfiguration;
use Illuminate\Auth\Access\HandlesAuthorization;

class WorkHoursConfigurationPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_work_hours_configuration');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, WorkHoursConfiguration $workHoursConfiguration): bool
    {
        return $user->can('view_work_hours_configuration');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_work_hours_configuration');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, WorkHoursConfiguration $workHoursConfiguration): bool
    {
        return $user->can('update_work_hours_configuration');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, WorkHoursConfiguration $workHoursConfiguration): bool
    {
        return $user->can('delete_work_hours_configuration');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, WorkHoursConfiguration $workHoursConfiguration): bool
    {
        return $user->can('restore_work_hours_configuration');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, WorkHoursConfiguration $workHoursConfiguration): bool
    {
        return $user->can('force_delete_work_hours_configuration');
    }
}
