@php $editing = isset($customer) @endphp

<div class="row">
    <!-- Customer Information -->
    <div class="col-sm-12 mb-4">
        <h5>@lang('crud.customers.customer_information')</h5>
        <hr>
    </div>

    <!-- Customer Image -->
    <div class="col-sm-12 col-lg-3 mb-4 text-center">
        <div
            x-data="imageViewer('{{ $editing ? $customer->image : '' }}')"
            class="d-flex flex-column align-items-center"
        >
            <label class="form-label">Customer Photo</label>

            <!-- Show the image -->
            <template x-if="imageUrl">
                <img
                    :src="imageUrl"
                    class="img-fluid rounded-circle border border-2 mb-3"
                    style="width: 150px; height: 150px; object-fit: cover;"
                />
            </template>

            <!-- Show the gray box when image is not available -->
            <template x-if="!imageUrl">
                <div
                    class="d-flex align-items-center justify-content-center border rounded-circle bg-light mb-3 mx-auto"
                    style="width: 150px; height: 150px;"
                >
                    <i class="bi bi-person text-muted" style="font-size: 3rem;"></i>
                </div>
            </template>

            <div class="mt-2">
                <input type="file" name="image" id="image" class="form-control" @change="fileChosen" />
                <small class="form-text text-muted">Upload a customer photo (optional)</small>
            </div>

            @error('image')
                <div class="invalid-feedback d-block">{{ $message }}</div>
            @enderror
        </div>
    </div>

    <!-- Basic Details -->
    <div class="col-sm-12 col-lg-9 mb-4">
        <div class="row">
            <div class="col-sm-12 col-lg-6 mb-4">
                <label for="name" class="form-label">@lang('crud.customers.customer_name') <i class="bi-asterisk text-danger small"></i></label>
                <input type="text" class="form-control form-control-lg @error('name') is-invalid @enderror"
                    name="name" id="name" placeholder="Enter customer name"
                    value="{{ old('name', ($editing ? $customer->name : '')) }}"
                    required maxlength="255">
                @error('name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="col-sm-12 col-lg-6 mb-4">
                <label for="status_id" class="form-label">Status <i class="bi-asterisk text-danger small"></i></label>
                <div class="tom-select-custom">
                    <select class="js-select form-select @error('status_id') is-invalid @enderror"
                        name="status_id" id="status_id" required
                        data-hs-tom-select-options='{
                            "placeholder": "Select a status..."
                        }'>
                        @php $selected = old('status_id', ($editing ? $customer->status_id : '')) @endphp
                        <option value="" disabled {{ empty($selected) ? 'selected' : '' }}>Please select the Status</option>
                        @foreach($statuses as $value => $label)
                        <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }}>{{ $label }}</option>
                        @endforeach
                    </select>
                </div>
                @error('status_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="col-sm-12 col-lg-6 mb-4">
                <label for="phone" class="form-label">Phone Number</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-telephone"></i></span>
                    <input type="text" class="form-control @error('phone') is-invalid @enderror"
                        name="phone" id="phone" placeholder="Enter phone number"
                        value="{{ old('phone', ($editing ? $customer->phone : '')) }}"
                        maxlength="255">
                </div>
                @error('phone')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="col-sm-12 col-lg-6 mb-4">
                <label for="email" class="form-label">Email Address</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                        name="email" id="email" placeholder="Enter email address"
                        value="{{ old('email', ($editing ? $customer->email : '')) }}"
                        maxlength="255">
                </div>
                @error('email')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="col-sm-12 mb-4">
        <h5>Additional Information</h5>
        <hr>
    </div>

    <div class="col-sm-12 mb-4">
        <label for="address" class="form-label">Address</label>
        <textarea class="form-control @error('address') is-invalid @enderror"
            name="address" id="address" rows="2"
            placeholder="Enter customer address">{{ old('address', ($editing ? $customer->address : '')) }}</textarea>
        @error('address')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="col-sm-12">
        <label for="description" class="form-label">Notes</label>
        <textarea class="form-control @error('description') is-invalid @enderror"
            name="description" id="description" rows="3"
            placeholder="Enter additional notes about this customer">{{ old('description', ($editing ? $customer->description : '')) }}</textarea>
        @error('description')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
        <small class="form-text text-muted">Add any additional information that might be helpful for your team</small>
    </div>
</div>
