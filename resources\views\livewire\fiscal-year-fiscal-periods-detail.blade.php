<div>
    <div class="mb-4">
        @can('create', App\Models\FiscalPeriod::class)
        <button class="btn btn-primary" wire:click="newFiscalPeriod">
            <i class="icon ion-md-add"></i>
            @lang('crud.common.new')
        </button>
        @endcan
    </div>

    <x-modal wire:model="showingModal">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ $modalTitle }}</h5>
                <button
                    type="button"
                    class="close"
                    data-dismiss="modal"
                    aria-label="Close"
                >
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="modal-body">
                <div>
                    <x-inputs.group class="col-sm-12">
                        <x-inputs.text
                            name="fiscalPeriodName"
                            label="Name"
                            wire:model="fiscalPeriodName"
                            maxlength="255"
                            placeholder="Name"
                        ></x-inputs.text>
                    </x-inputs.group>

                    <x-inputs.group class="col-sm-12">
                        <x-inputs.date
                            name="fiscalPeriodStartDate"
                            label="Start Date"
                            wire:model="fiscalPeriodStartDate"
                            max="255"
                        ></x-inputs.date>
                    </x-inputs.group>

                    <x-inputs.group class="col-sm-12">
                        <x-inputs.date
                            name="fiscalPeriodEndDate"
                            label="End Date"
                            wire:model="fiscalPeriodEndDate"
                            max="255"
                        ></x-inputs.date>
                    </x-inputs.group>

                    <x-inputs.group class="col-sm-12">
                        <x-inputs.checkbox
                            id="fiscalPeriodIsClosed"
                            name="fiscalPeriodIsClosed"
                            label="Closed"
                            wire:model="fiscalPeriodIsClosed"
                        ></x-inputs.checkbox>
                    </x-inputs.group>

                    <x-inputs.group class="col-sm-12">
                        <x-inputs.checkbox
                            id="fiscalPeriodIsActive"
                            name="fiscalPeriodIsActive"
                            label="Active"
                            wire:model="fiscalPeriodIsActive"
                        ></x-inputs.checkbox>
                    </x-inputs.group>

                    <x-inputs.group class="col-sm-12">
                        <x-inputs.textarea
                            name="fiscalPeriodDescription"
                            label="Description"
                            wire:model="fiscalPeriodDescription"
                            maxlength="255"
                        ></x-inputs.textarea>
                    </x-inputs.group>
                </div>
            </div>

            <div class="modal-footer">
                <button
                    type="button"
                    class="btn btn-light float-left"
                    wire:click="$toggle('showingModal')"
                >
                    <i class="icon ion-md-close"></i>
                    @lang('crud.common.cancel')
                </button>

                <button type="button" class="btn btn-primary" wire:click="createFiscalPeriod">
                    <i class="icon ion-md-save"></i>
                    @lang('crud.common.create')
                </button>
            </div>
        </div>
    </x-modal>

    <div class="table-responsive">
        <table class="table table-borderless table-hover">
            <thead>
                <tr>
                    <th class="text-left">
                        @lang('crud.fiscal_periods.inputs.name')
                    </th>
                    <th class="text-left">
                        @lang('crud.fiscal_periods.inputs.start_date')
                    </th>
                    <th class="text-left">
                        @lang('crud.fiscal_periods.inputs.end_date')
                    </th>
                    <th class="text-center">
                        Status
                    </th>
                    <th class="text-center">
                        @lang('crud.fiscal_periods.inputs.is_active')
                    </th>
                    <th></th>
                </tr>
            </thead>
            <tbody class="text-gray-600">
                @forelse($fiscalPeriods as $fiscalPeriod)
                <tr class="hover:bg-gray-100">
                    <td class="text-left">
                        {{ $fiscalPeriod->name ?? '-' }}
                    </td>
                    <td class="text-left">
                        {{ $fiscalPeriod->start_date->format('Y-m-d') ?? '-' }}
                    </td>
                    <td class="text-left">
                        {{ $fiscalPeriod->end_date->format('Y-m-d') ?? '-' }}
                    </td>
                    <td class="text-center">
                        <span class="badge {{ $fiscalPeriod->is_closed ? 'badge-danger' : 'badge-success' }}">
                            {{ $fiscalPeriod->is_closed ? 'Closed' : 'Open' }}
                        </span>
                    </td>
                    <td class="text-center">
                        <span class="badge {{ $fiscalPeriod->is_active ? 'badge-success' : 'badge-danger' }}">
                            {{ $fiscalPeriod->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </td>
                    <td class="text-right" style="width: 134px;">
                        <div
                            role="group"
                            aria-label="Row Actions"
                            class="relative inline-flex align-middle"
                        >
                            @can('update', $fiscalPeriod)
                            <a
                                href="{{ route('fiscal-periods.edit', $fiscalPeriod) }}"
                                class="mr-1"
                            >
                                <button
                                    type="button"
                                    class="btn btn-light"
                                >
                                    <i class="icon ion-md-create"></i>
                                </button>
                            </a>
                            @endcan @can('view', $fiscalPeriod)
                            <a
                                href="{{ route('fiscal-periods.show', $fiscalPeriod) }}"
                                class="mr-1"
                            >
                                <button
                                    type="button"
                                    class="btn btn-light"
                                >
                                    <i class="icon ion-md-eye"></i>
                                </button>
                            </a>
                            @endcan
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="6">
                        @lang('crud.common.no_items_found')
                    </td>
                </tr>
                @endforelse
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="6">
                        <div class="mt-10 px-4">
                            {{ $fiscalPeriods->render() }}
                        </div>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>
