<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

use Facades\App\Cache\Repo;

class Customer extends Model implements HasMedia
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use InteractsWithMedia;
    // use BusinessTypeTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'phone',
        'email',
        'address',
        'description',
        'created_by',
        'updated_by',
        'status_id',
        'business_type_id',

    ];
    
    protected $casts = [
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];
    
    protected $searchableFields = ['*'];

    protected $appends = ['image'];

    public function getImageAttribute(){
        $file = $this->file();
        return $file ? $file->getUrl()  : '/assets/img/160x160/img1.jpg';
    }

    public function file($collection = "image"){
        return $this->getMedia($collection)->first();
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function debtors()
    {
        return $this->morphMany(Debt::class, 'debtable');
    }    
    
    public function getInvoice() {
        request()->merge(["customer_id" => $this->id]);
        return Repo::getInvoice();
    }


}
