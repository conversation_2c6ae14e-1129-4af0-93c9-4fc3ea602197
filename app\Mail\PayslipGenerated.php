<?php

namespace App\Mail;

use App\Models\MonthlyPayroll;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Barryvdh\DomPDF\Facade\Pdf;

class PayslipGenerated extends Mailable
{
    use Queueable, SerializesModels;

    public $payroll;

    /**
     * Create a new message instance.
     *
     * @param \App\Models\MonthlyPayroll $payroll
     * @return void
     */
    public function __construct(MonthlyPayroll $payroll)
    {
        $this->payroll = $payroll;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $subject = 'Your Payslip for ' . $this->payroll->date_from->format('F Y');

        $mail = $this->subject($subject)
                     ->view('emails.payslip-generated')
                     ->with([
                         'payroll' => $this->payroll,
                         'employee' => $this->payroll->employee,
                         'period' => $this->payroll->date_from->format('F Y'),
                     ]);

        // Generate and attach PDF on-demand
        try {
            $pdfContent = $this->generatePDF();
            if ($pdfContent) {
                $filename = 'payslip_' . $this->payroll->employee->name . '_' . $this->payroll->date_from->format('Y-m') . '.pdf';
                $mail->attachData($pdfContent, $filename, [
                    'mime' => 'application/pdf',
                ]);
            }
        } catch (\Exception $e) {
            \Log::error("Failed to generate PDF for payroll ID {$this->payroll->id}: " . $e->getMessage());
        }

        return $mail;
    }

    /**
     * Generate PDF for the payslip
     *
     * @return void
     */
    private function generatePDF()
    {
        try {
            $this->payroll->load('employee', 'payrollItems', 'contributions', 'deductions');

            $pdf = PDF::loadView('app.payroll.payslip_pdf', ['payroll' => $this->payroll]);

            // Generate PDF content directly in memory
            $this->pdfContent = $pdf->output();

            \Log::info("PDF generated successfully for payroll ID: " . $this->payroll->id);

        } catch (\Exception $e) {
            \Log::error("Failed to generate PDF for payroll ID {$this->payroll->id}: " . $e->getMessage());
            $this->pdfContent = null;
            throw $e;
        }
    }


}
