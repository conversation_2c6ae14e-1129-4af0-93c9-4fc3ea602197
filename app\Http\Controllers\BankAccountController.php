<?php

namespace App\Http\Controllers;

use App\Models\Account;
use App\Models\BankAccount;
use App\Models\Currency;
use Illuminate\Http\Request;

class BankAccountController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', BankAccount::class);

        $search = $request->get('search', '');
        $isActive = $request->get('is_active', '');

        $bankAccounts = BankAccount::search($search);
        
        if ($isActive !== '') {
            $bankAccounts->where('is_active', $isActive);
        }
        
        $bankAccounts = $bankAccounts->latest()
            ->paginate()
            ->withQueryString();

        return view('app.bank_accounts.index', compact(
            'bankAccounts', 
            'search',
            'isActive'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', BankAccount::class);

        $glAccounts = Account::where('is_active', true)
            ->whereHas('accountType', function ($query) {
                $query->where('classification', 'asset');
            })
            ->orderBy('code')
            ->get()
            ->pluck('full_name', 'id');
            
        $currencies = Currency::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.bank_accounts.create', compact('glAccounts', 'currencies'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', BankAccount::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'account_number' => 'required|string|max:255',
            'bank_name' => 'required|string|max:255',
            'branch_name' => 'nullable|string|max:255',
            'swift_code' => 'nullable|string|max:255',
            'iban' => 'nullable|string|max:255',
            'account_type' => 'required|string|in:checking,savings,money_market,certificate_of_deposit,other',
            'gl_account_id' => 'required|exists:accounts,id',
            'currency_id' => 'nullable|exists:currencies,id',
            'opening_balance' => 'required|numeric',
            'opening_balance_date' => 'required|date',
            'is_active' => 'boolean',
            'description' => 'nullable|string',
        ]);

        // Set current balance to opening balance initially
        $validated['current_balance'] = $validated['opening_balance'];

        $bankAccount = BankAccount::create($validated);

        return redirect()
            ->route('bank-accounts.edit', $bankAccount)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\BankAccount  $bankAccount
     * @return \Illuminate\Http\Response
     */
    public function show(BankAccount $bankAccount)
    {
        $this->authorize('view', $bankAccount);

        $bankReconciliations = $bankAccount->bankReconciliations()
            ->latest('statement_date')
            ->paginate(10);
            
        // Get recent transactions
        $recentTransactions = $bankAccount->glAccount->journalEntryLines()
            ->with('journalEntry')
            ->whereHas('journalEntry', function ($query) {
                $query->where('status', 'posted');
            })
            ->latest('created_at')
            ->take(20)
            ->get();

        return view('app.bank_accounts.show', compact(
            'bankAccount', 
            'bankReconciliations',
            'recentTransactions'
        ));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\BankAccount  $bankAccount
     * @return \Illuminate\Http\Response
     */
    public function edit(BankAccount $bankAccount)
    {
        $this->authorize('update', $bankAccount);

        $glAccounts = Account::where('is_active', true)
            ->whereHas('accountType', function ($query) {
                $query->where('classification', 'asset');
            })
            ->orderBy('code')
            ->get()
            ->pluck('full_name', 'id');
            
        $currencies = Currency::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.bank_accounts.edit', compact(
            'bankAccount', 
            'glAccounts', 
            'currencies'
        ));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\BankAccount  $bankAccount
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, BankAccount $bankAccount)
    {
        $this->authorize('update', $bankAccount);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'account_number' => 'required|string|max:255',
            'bank_name' => 'required|string|max:255',
            'branch_name' => 'nullable|string|max:255',
            'swift_code' => 'nullable|string|max:255',
            'iban' => 'nullable|string|max:255',
            'account_type' => 'required|string|in:checking,savings,money_market,certificate_of_deposit,other',
            'gl_account_id' => 'required|exists:accounts,id',
            'currency_id' => 'nullable|exists:currencies,id',
            'is_active' => 'boolean',
            'description' => 'nullable|string',
        ]);

        $bankAccount->update($validated);

        return redirect()
            ->route('bank-accounts.edit', $bankAccount)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\BankAccount  $bankAccount
     * @return \Illuminate\Http\Response
     */
    public function destroy(BankAccount $bankAccount)
    {
        $this->authorize('delete', $bankAccount);

        if ($bankAccount->bankReconciliations()->count() > 0) {
            return redirect()
                ->route('bank-accounts.index')
                ->withError('Bank account has reconciliations and cannot be deleted.');
        }

        $bankAccount->delete();

        return redirect()
            ->route('bank-accounts.index')
            ->withSuccess(__('crud.common.removed'));
    }
}
