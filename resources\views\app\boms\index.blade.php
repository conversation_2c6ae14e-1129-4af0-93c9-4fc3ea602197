@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-body">
            <div style="display: flex; justify-content: space-between;">
                <h4 class="card-title">Bills of Materials</h4>
            </div>

            <div class="searchbar mt-4 mb-5">
                <div class="row">
                    <div class="col-md-6">
                        <form>
                            <div class="input-group">
                                <input
                                    id="indexSearch"
                                    type="text"
                                    name="search"
                                    placeholder="{{ __('crud.common.search') }}"
                                    value="{{ $search ?? '' }}"
                                    class="form-control"
                                    autocomplete="off"
                                />
                                <div class="input-group-append">
                                    <button
                                        type="submit"
                                        class="btn btn-primary"
                                    >
                                        <i class="icon ion-md-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-6 text-right">
                        @can('create', App\Models\Bom::class)
                        <a
                            href="{{ route('boms.create') }}"
                            class="btn btn-primary"
                        >
                            <i class="icon ion-md-add"></i>
                            @lang('crud.common.create')
                        </a>
                        @endcan
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-6">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="product_id">Product</label>
                                    <select name="product_id" id="product_id" class="form-control">
                                        <option value="">All Products</option>
                                        @foreach($products as $id => $name)
                                            <option value="{{ $id }}" {{ $productId == $id ? 'selected' : '' }}>
                                                {{ $name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select name="status" id="status" class="form-control">
                                        <option value="">All Statuses</option>
                                        <option value="draft" {{ $status == 'draft' ? 'selected' : '' }}>Draft</option>
                                        <option value="active" {{ $status == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ $status == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="{{ route('boms.index') }}" class="btn btn-secondary">Reset</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-borderless table-hover">
                    <thead>
                        <tr>
                            <th>BOM Number</th>
                            <th>Name</th>
                            <th>Product</th>
                            <th>Version</th>
                            <th>Status</th>
                            <th>Default</th>
                            <th>Total Cost</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($boms as $bom)
                        <tr>
                            <td>{{ $bom->bom_number ?? '-' }}</td>
                            <td>{{ $bom->name ?? '-' }}</td>
                            <td>{{ optional($bom->product)->name ?? '-' }}</td>
                            <td>{{ $bom->version ?? '-' }}</td>
                            <td>
                                <span class="badge badge-{{ 
                                    $bom->status == 'active' ? 'success' : 
                                    ($bom->status == 'draft' ? 'warning' : 'danger') 
                                }}">
                                    {{ ucfirst($bom->status) }}
                                </span>
                            </td>
                            <td>
                                @if($bom->is_default)
                                    <span class="badge badge-success">Default</span>
                                @else
                                    <span class="badge badge-secondary">No</span>
                                @endif
                            </td>
                            <td>{{ number_format($bom->total_cost, 2) }}</td>
                            <td class="text-center" style="width: 134px;">
                                <div
                                    role="group"
                                    aria-label="Row Actions"
                                    class="btn-group"
                                >
                                    @can('update', $bom)
                                    <a href="{{ route('boms.edit', $bom) }}">
                                        <button
                                            type="button"
                                            class="btn btn-light"
                                        >
                                            <i class="icon ion-md-create"></i>
                                        </button>
                                    </a>
                                    @endcan
                                    @can('view', $bom)
                                    <a href="{{ route('boms.show', $bom) }}">
                                        <button
                                            type="button"
                                            class="btn btn-light"
                                        >
                                            <i class="icon ion-md-eye"></i>
                                        </button>
                                    </a>
                                    @endcan
                                    @can('update', $bom)
                                    <a href="{{ route('boms.edit-items', $bom) }}">
                                        <button
                                            type="button"
                                            class="btn btn-light"
                                            title="Edit Items"
                                        >
                                            <i class="icon ion-md-list"></i>
                                        </button>
                                    </a>
                                    @endcan
                                    @can('create', App\Models\Bom::class)
                                    <form
                                        action="{{ route('boms.copy', $bom) }}"
                                        method="POST"
                                        onsubmit="return confirm('Are you sure you want to copy this BOM?')"
                                    >
                                        @csrf
                                        <button
                                            type="submit"
                                            class="btn btn-light"
                                            title="Copy BOM"
                                        >
                                            <i class="icon ion-md-copy"></i>
                                        </button>
                                    </form>
                                    @endcan
                                    @can('delete', $bom)
                                    <form
                                        action="{{ route('boms.destroy', $bom) }}"
                                        method="POST"
                                        onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                    >
                                        @csrf @method('DELETE')
                                        <button
                                            type="submit"
                                            class="btn btn-light text-danger"
                                        >
                                            <i class="icon ion-md-trash"></i>
                                        </button>
                                    </form>
                                    @endcan
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8">
                                @lang('crud.common.no_items_found')
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="8">{!! $boms->render() !!}</td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection
