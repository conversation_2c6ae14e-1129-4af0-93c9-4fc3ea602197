@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="bi bi-calculator me-2"></i>Tax Brackets
                        </h4>
                        <div class="d-flex gap-2">
                            <a href="{{ route('tax-brackets.preview') }}" class="btn btn-outline-info">
                                <i class="bi bi-eye me-1"></i>Preview Calculator
                            </a>
                            @can('create', App\Models\TaxBracket::class)
                            <a href="{{ route('tax-brackets.create') }}" class="btn btn-primary">
                                <i class="bi bi-plus-lg me-1"></i>New Tax Bracket
                            </a>
                            @endcan
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Search Form -->
                    <form method="GET" action="{{ route('tax-brackets.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="Search tax brackets..." value="{{ $search }}">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="bi bi-search"></i>
                                    </button>
                                    @if($search)
                                    <a href="{{ route('tax-brackets.index') }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x"></i>
                                    </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Tax Brackets Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Order</th>
                                    <th>Name</th>
                                    <th>Min Amount</th>
                                    <th>Max Amount</th>
                                    <th>Rate</th>
                                    <th>Status</th>
                                    <th>Description</th>
                                    <th class="text-end">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($taxBrackets as $taxBracket)
                                <tr class="{{ !$taxBracket->is_active ? 'table-secondary' : '' }}">
                                    <td>
                                        <span class="badge bg-primary">{{ $taxBracket->order }}</span>
                                    </td>
                                    <td>
                                        <strong>{{ $taxBracket->name ?: 'Bracket ' . $taxBracket->order }}</strong>
                                    </td>
                                    <td>
                                        <span class="text-success">K{{ $taxBracket->formatted_min_amount }}</span>
                                    </td>
                                    <td>
                                        @if($taxBracket->max_amount)
                                            <span class="text-danger">K{{ $taxBracket->formatted_max_amount }}</span>
                                        @else
                                            <span class="text-muted">Unlimited</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $taxBracket->formatted_rate }}</span>
                                    </td>
                                    <td>
                                        @if($taxBracket->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-secondary">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ Str::limit($taxBracket->description, 50) }}</small>
                                    </td>
                                    <td class="text-end">
                                        <div class="btn-group" role="group">
                                            @can('view', $taxBracket)
                                            <a href="{{ route('tax-brackets.show', $taxBracket) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            @endcan

                                            @can('update', $taxBracket)
                                            <a href="{{ route('tax-brackets.edit', $taxBracket) }}" 
                                               class="btn btn-sm btn-outline-secondary">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            @endcan

                                            @can('delete', $taxBracket)
                                            <form action="{{ route('tax-brackets.destroy', $taxBracket) }}" 
                                                  method="POST" class="d-inline">
                                                @csrf @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger"
                                                        onclick="return confirm('Are you sure you want to delete this tax bracket?')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </form>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="text-center">
                                            <i class="bi bi-calculator display-4 text-muted"></i>
                                            <h5 class="mt-2">No tax brackets found</h5>
                                            <p class="text-muted">Create your first tax bracket to get started.</p>
                                            @can('create', App\Models\TaxBracket::class)
                                            <a href="{{ route('tax-brackets.create') }}" class="btn btn-primary">
                                                <i class="bi bi-plus-lg me-1"></i>Create Tax Bracket
                                            </a>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($taxBrackets->hasPages())
                    <div class="d-flex justify-content-center">
                        {{ $taxBrackets->links() }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
