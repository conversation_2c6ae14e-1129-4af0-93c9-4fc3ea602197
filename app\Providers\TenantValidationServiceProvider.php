<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Validator;
use Spatie\Multitenancy\Models\Tenant;

class TenantValidationServiceProvider extends ServiceProvider
{
    public function boot()
    {
        // Existing tenant_exists rule
        Validator::extend('tenant_exists', function ($attribute, $value, $parameters, $validator) {
            // Ensure at least one parameter (table name) is provided
            if (empty($parameters[0])) {
                return false;
            }

            $table = $parameters[0];
            $column = $parameters[1] ?? 'id';

            // Use the tenant database connection
            return \DB::connection('tenant')
                ->table($table)
                ->where($column, $value)
                ->exists();
        }, 'The :attribute does not exist in the tenant database.');

        // New tenant_unique rule
        Validator::extend('tenant_unique', function ($attribute, $value, $parameters, $validator) {
            // Ensure at least one parameter (table name) is provided
            if (empty($parameters[0])) {
                return false;
            }

            $table = $parameters[0];
            $column = $parameters[1] ?? $attribute;
            $exceptId = $parameters[2] ?? null; // Optional ID to exclude (for updates)

            // Build the query on the tenant database connection
            $query = \DB::connection('tenant')
                ->table($table)
                ->where($column, $value);

            // Exclude the current record if an ID is provided (for updates)
            if ($exceptId) {
                $query->where('id', '!=', $exceptId);
            }

            // Check if any record exists with the given value
            return !$query->exists();
        }, 'The :attribute has already been taken in the tenant database.');
    }
}