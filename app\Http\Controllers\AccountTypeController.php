<?php

namespace App\Http\Controllers;

use App\Models\AccountType;
use Illuminate\Http\Request;

class AccountTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', AccountType::class);

        $search = $request->get('search', '');

        $accountTypes = AccountType::search($search)
            ->latest()
            ->paginate()
            ->withQueryString();

        return view('app.account_types.index', compact('accountTypes', 'search'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', AccountType::class);

        return view('app.account_types.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', AccountType::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:255|unique:account_types,code',
            'classification' => 'required|string|in:asset,liability,equity,revenue,expense',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'is_system' => 'boolean',
        ]);

        $accountType = AccountType::create($validated);

        return redirect()
            ->route('account-types.edit', $accountType)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\AccountType  $accountType
     * @return \Illuminate\Http\Response
     */
    public function show(AccountType $accountType)
    {
        $this->authorize('view', $accountType);

        return view('app.account_types.show', compact('accountType'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\AccountType  $accountType
     * @return \Illuminate\Http\Response
     */
    public function edit(AccountType $accountType)
    {
        $this->authorize('update', $accountType);

        return view('app.account_types.edit', compact('accountType'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\AccountType  $accountType
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, AccountType $accountType)
    {
        $this->authorize('update', $accountType);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:255|unique:account_types,code,' . $accountType->id,
            'classification' => 'required|string|in:asset,liability,equity,revenue,expense',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'is_system' => 'boolean',
        ]);

        $accountType->update($validated);

        return redirect()
            ->route('account-types.edit', $accountType)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\AccountType  $accountType
     * @return \Illuminate\Http\Response
     */
    public function destroy(AccountType $accountType)
    {
        $this->authorize('delete', $accountType);

        if ($accountType->is_system) {
            return redirect()
                ->route('account-types.index')
                ->withError('System account types cannot be deleted.');
        }

        if ($accountType->accounts()->count() > 0 || $accountType->categories()->count() > 0) {
            return redirect()
                ->route('account-types.index')
                ->withError('Account type is in use and cannot be deleted.');
        }

        $accountType->delete();

        return redirect()
            ->route('account-types.index')
            ->withSuccess(__('crud.common.removed'));
    }
}
