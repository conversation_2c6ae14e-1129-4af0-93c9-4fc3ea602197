@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                Edit Employee Deduction/Contribution Assignment
            </h4>
        </div>

        <div class="card-body">
            <form action="{{ route('employee-deduction-contributions.update', $employeeDeductionContribution) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="employee_id">Employee</label>
                            <select name="employee_id" id="employee_id" class="form-control" required>
                                <option value="">Select Employee</option>
                                @foreach($employees as $id => $name)
                                    <option value="{{ $id }}" {{ old('employee_id', $employeeDeductionContribution->employee_id) == $id ? 'selected' : '' }}>{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="deduction_contribution_id">Deduction/Contribution</label>
                            <select name="deduction_contribution_id" id="deduction_contribution_id" class="form-control" required>
                                <option value="">Select Deduction/Contribution</option>
                                @foreach($deductionContributions as $id => $name)
                                    <option value="{{ $id }}" {{ old('deduction_contribution_id', $employeeDeductionContribution->deduction_contribution_id) == $id ? 'selected' : '' }}>{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="start_date">Start Date</label>
                            <input type="date" name="start_date" id="start_date" class="form-control" value="{{ old('start_date', $employeeDeductionContribution->start_date ? $employeeDeductionContribution->start_date->format('Y-m-d') : '') }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="end_date">End Date (Optional)</label>
                            <input type="date" name="end_date" id="end_date" class="form-control" value="{{ old('end_date', $employeeDeductionContribution->end_date ? $employeeDeductionContribution->end_date->format('Y-m-d') : '') }}">
                        </div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <p><strong>Note:</strong> Updating this assignment will affect future payroll calculations for this employee. It will not affect payroll records that have already been generated.</p>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ route('employee-deduction-contributions.index', ['employee_id' => $employeeDeductionContribution->employee_id]) }}" class="btn btn-light">
                        <i class="bi bi-arrow-left"></i> Back
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Update
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
