<?php

namespace App\Policies;

use App\Models\User;
use App\Models\AccountCategory;
use Illuminate\Auth\Access\HandlesAuthorization;

class AccountCategoryPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo('list account-categories');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\AccountCategory  $accountCategory
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, AccountCategory $accountCategory)
    {
        return $user->hasPermissionTo('view account-categories');
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo('create account-categories');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\AccountCategory  $accountCategory
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, AccountCategory $accountCategory)
    {
        return $user->hasPermissionTo('update account-categories') && 
            (!$accountCategory->is_system || $user->hasRole('super-admin'));
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\AccountCategory  $accountCategory
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, AccountCategory $accountCategory)
    {
        return $user->hasPermissionTo('delete account-categories') && 
            !$accountCategory->is_system && 
            $accountCategory->accounts()->count() === 0;
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\AccountCategory  $accountCategory
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, AccountCategory $accountCategory)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\AccountCategory  $accountCategory
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, AccountCategory $accountCategory)
    {
        return false;
    }
}
