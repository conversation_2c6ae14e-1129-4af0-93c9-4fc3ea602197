<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class PayrollCompletedNotification extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $payrolls;
    public $period;
    public $summary;

    /**
     * Create a new message instance.
     *
     * @param \Illuminate\Support\Collection $payrolls
     * @param string $period
     * @return void
     */
    public function __construct(Collection $payrolls, $period)
    {
        $this->payrolls = $payrolls;
        $this->period = $period;
        $this->summary = $this->calculateSummary();
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('Payroll Processing Completed - ' . $this->period)
                    ->view('emails.payroll-completed')
                    ->with([
                        'payrolls' => $this->payrolls,
                        'period' => $this->period,
                        'summary' => $this->summary,
                    ]);
    }

    /**
     * Calculate payroll summary
     *
     * @return array
     */
    private function calculateSummary()
    {
        return [
            'total_employees' => $this->payrolls->count(),
            'total_gross_pay' => $this->payrolls->sum('gross_pay'),
            'total_deductions' => $this->payrolls->sum('total_deductions'),
            'total_tax' => $this->payrolls->sum('tax_amount'),
            'total_net_pay' => $this->payrolls->sum('net_pay'),
            'total_overtime_hours' => $this->payrolls->sum('overtime_hours'),
            'total_overtime_pay' => $this->payrolls->sum('overtime_pay'),
        ];
    }
}
