<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->date('date_from')->nullable();
            $table->date('date_to')->nullable();
            $table->datetime('closed_at')->nullable();
            $table
                ->decimal('vat', 12, 2)
                ->default(0)
                ->nullable();
            $table->text('description')->nullable();
            $table
                ->decimal('sub_total', 12, 2)
                ->default(0)
                ->nullable();   
            $table
                ->decimal('amount_total', 12, 2)
                ->default(0)
                ->nullable();
            $table
                ->decimal('amount_paid', 12, 2)
                ->default(0)
                ->nullable();
            $table
                ->decimal('discount', 12, 2)
                ->default(0)
                ->nullable();
            $table->text('supplier_name')->nullable();
            $table->string('reference_no')->nullable();
            $table->unsignedBigInteger('warehouse_id')->nullable();
            $table->unsignedBigInteger('branch_id')->nullable();
            $table->unsignedBigInteger('supplier_id')->nullable();
            $table->unsignedBigInteger('order_id')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->unsignedBigInteger('status_id')->default(10)->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unsignedBigInteger('business_type_id')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->unsignedBigInteger('currency_id')->nullable();

            // Order adjustment fields
            $table->boolean('is_adjustment')->default(false);
            $table->unsignedBigInteger('original_order_id')->nullable();
            $table->string('adjustment_type')->nullable(); // return, refund, stock_adjustment
            $table->text('adjustment_reason')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Indexes for adjustment fields
            $table->index(['is_adjustment', 'adjustment_type']);
            $table->index('original_order_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders');
    }
};
