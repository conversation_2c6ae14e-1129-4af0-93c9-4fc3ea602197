<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use App\Models\Tenant;


class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    public function login(Request $request){
        $credentials = $request->validate([
            'email' => 'required',
            'password' => 'required',
        ]);

        $tenant = Tenant::where("email", $credentials['email'])->first();

        if(!$tenant) {
            return redirect("/login")->with("error", "Account not found");
        }
        
        $tenant->makeCurrent();

        if (
            auth()->attempt($credentials) ||
            auth()->attempt([
                "name" => $request->email,
                "password" => $request->password,
            ])    ||      
            auth()->attempt([
                "phone" => $request->email,
                "password" => $request->password,
            ])
        ) {
            // Cache the tenant name for future requests
            cache([_authId() => $tenant->domain]);
            $tenant->switching();
            return redirect("/");
        }

        return redirect("/login")->with("error", "Invalid Login");

    }

}
