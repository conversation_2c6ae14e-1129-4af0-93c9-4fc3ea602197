@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">pencil-square</x-slot>
        <x-slot name="title">Edit Account Category: {{ $accountCategory->name }}</x-slot>
        <x-slot name="subtitle">Update account category information</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Accounting</a></li>
            <li class="breadcrumb-item"><a href="{{ route('account-categories.index') }}">Account Categories</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('account-categories.show', $accountCategory) }}" class="btn btn-outline-info me-2">
                <i class="bi bi-eye me-1"></i> View Category
            </a>
            <a href="{{ route('account-categories.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Categories
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Form Card -->
    <form method="POST" action="{{ route('account-categories.update', $accountCategory) }}">
        @csrf
        @method('PUT')
        
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-header-title">Account Category Information</h5>
                    <span class="badge bg-primary">ID: {{ $accountCategory->id }}</span>
                </div>
            </div>
            <div class="card-body">
                @include('app.account_categories.form-inputs')
            </div>
        </div>

        <div class="d-flex justify-content-between mt-4">
            <div>
                <a href="{{ route('account-categories.index') }}" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-arrow-left me-1"></i> Back
                </a>

                <a href="{{ route('account-categories.create') }}" class="btn btn-outline-primary">
                    <i class="bi bi-plus-circle me-1"></i> New Category
                </a>
            </div>

            <button type="submit" class="btn btn-success">
                <i class="bi bi-check-circle me-1"></i> Save Changes
            </button>
        </div>
    </form>
</div>
@endsection
