<?php

namespace App\Http\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\AccountType;
use App\Models\Account;
use App\Models\AccountCategory;

class AccountTypeAccountsDetail extends Component
{
    use WithPagination;

    public AccountType $accountType;
    public $accountCategoryId = null;
    public $accountName;
    public $accountCode;
    public $accountDescription;
    public $accountIsActive = true;
    public $accountAllowsManualEntries = true;
    public $accountNormalBalance = 'debit';

    public $showingModal = false;
    public $modalTitle = 'New Account';

    protected $rules = [
        'accountCategoryId' => ['required', 'exists:account_categories,id'],
        'accountName' => ['required', 'max:255', 'string'],
        'accountCode' => ['required', 'max:255', 'string'],
        'accountDescription' => ['nullable', 'max:255', 'string'],
        'accountIsActive' => ['boolean'],
        'accountAllowsManualEntries' => ['boolean'],
        'accountNormalBalance' => ['required', 'in:debit,credit'],
    ];

    public function mount(AccountType $accountType)
    {
        $this->accountType = $accountType;
    }

    public function render()
    {
        $accounts = Account::where('account_type_id', $this->accountType->id)
            ->paginate(20);
            
        $accountCategories = AccountCategory::where('account_type_id', $this->accountType->id)
            ->where('is_active', true)
            ->orderBy('name')
            ->get()
            ->pluck('name', 'id');

        return view('livewire.account-type-accounts-detail', [
            'accounts' => $accounts,
            'accountCategories' => $accountCategories,
        ]);
    }

    public function newAccount()
    {
        $this->modalTitle = trans('crud.accounts.create_title');
        $this->resetAccountData();

        $this->showingModal = true;
    }

    public function resetAccountData()
    {
        $this->accountCategoryId = null;
        $this->accountName = '';
        $this->accountCode = '';
        $this->accountDescription = '';
        $this->accountIsActive = true;
        $this->accountAllowsManualEntries = true;
        $this->accountNormalBalance = 'debit';
    }

    public function createAccount()
    {
        $this->validate();

        $account = Account::create([
            'account_type_id' => $this->accountType->id,
            'account_category_id' => $this->accountCategoryId,
            'name' => $this->accountName,
            'code' => $this->accountCode,
            'description' => $this->accountDescription,
            'is_active' => $this->accountIsActive,
            'allows_manual_entries' => $this->accountAllowsManualEntries,
            'normal_balance' => $this->accountNormalBalance,
        ]);

        $this->showingModal = false;

        $this->emit('refreshParent');
        $this->resetAccountData();
    }
}
