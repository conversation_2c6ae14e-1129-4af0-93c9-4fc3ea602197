@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">cart</x-slot>
        <x-slot name="title">Sales Management</x-slot>
        <x-slot name="subtitle">Track and manage all sales transactions</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item active" aria-current="page">Sales</li>
        </x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Sale::class)
            <a href="{{ route('sales.create') }}" class="btn btn-primary btn-sm">
                <i class="bi bi-plus-circle me-1"></i> New Sale
            </a>
            @endcan
            <button type="button" class="btn btn-outline-primary btn-sm ms-2" data-bs-toggle="modal" data-bs-target="#salesReportModal">
                <i class="bi bi-file-earmark-bar-graph me-1"></i> Generate Report
            </button>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Sales Overview Cards -->
    <div class="row mb-4">
        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-soft-primary avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-currency-dollar"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1">{{ _money($sales->sum('price') * $sales->sum('quantity')) }}</h4>
                            <span class="d-block">Total Sales Revenue</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-soft-info avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-box-seam"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1">{{ $sales->count() }}</h4>
                            <span class="d-block">Total Sales Transactions</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-sm-6 col-lg-3 mb-3 mb-sm-0">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-soft-success avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-tags"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1">{{ _money($sales->sum('discount')) }}</h4>
                            <span class="d-block">Total Discounts Applied</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-sm-6 col-lg-3">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-soft-warning avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-graph-up-arrow"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1">{{ $sales->sum('quantity') }}</h4>
                            <span class="d-block">Total Units Sold</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Sales Overview Cards -->

    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ route('sales.index') }}" method="GET" class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="indexSearch" name="search" 
                               placeholder="Search by product name or ID..." value="{{ $search ?? '' }}">
                    </div>
                </div>
                
                <div class="col-md-3">
                    <select class="form-select js-select" name="date_range" data-placeholder="Filter by date">
                        <option value="">All Time</option>
                        <option value="today" {{ request('date_range') == 'today' ? 'selected' : '' }}>Today</option>
                        <option value="yesterday" {{ request('date_range') == 'yesterday' ? 'selected' : '' }}>Yesterday</option>
                        <option value="this_week" {{ request('date_range') == 'this_week' ? 'selected' : '' }}>This Week</option>
                        <option value="last_week" {{ request('date_range') == 'last_week' ? 'selected' : '' }}>Last Week</option>
                        <option value="this_month" {{ request('date_range') == 'this_month' ? 'selected' : '' }}>This Month</option>
                        <option value="last_month" {{ request('date_range') == 'last_month' ? 'selected' : '' }}>Last Month</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <select class="form-select js-select" name="sort_by" data-placeholder="Sort by">
                        <option value="latest" {{ request('sort_by') == 'latest' ? 'selected' : '' }}>Latest First</option>
                        <option value="oldest" {{ request('sort_by') == 'oldest' ? 'selected' : '' }}>Oldest First</option>
                        <option value="price_high" {{ request('sort_by') == 'price_high' ? 'selected' : '' }}>Price (High to Low)</option>
                        <option value="price_low" {{ request('sort_by') == 'price_low' ? 'selected' : '' }}>Price (Low to High)</option>
                        <option value="quantity_high" {{ request('sort_by') == 'quantity_high' ? 'selected' : '' }}>Quantity (High to Low)</option>
                        <option value="quantity_low" {{ request('sort_by') == 'quantity_low' ? 'selected' : '' }}>Quantity (Low to High)</option>
                    </select>
                </div>
                
                <div class="col-md-2 d-flex">
                    <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                </div>
            </form>
        </div>
    </div>
    <!-- End Filters and Search -->

    <!-- Sales Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-header-title">
                    <i class="bi bi-list-ul me-2"></i>Sales Transactions
                </h5>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-download me-1"></i> Export
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                        <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-excel me-2"></i>Excel</a></li>
                        <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-pdf me-2"></i>PDF</a></li>
                        <li><a class="dropdown-item" href="#"><i class="bi bi-printer me-2"></i>Print</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Product</th>
                        <th>Unit</th>
                        <th class="text-center">Quantity</th>
                        <th class="text-end">Unit Price</th>
                        <th class="text-end">Discount</th>
                        <th class="text-end">Total</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($sales as $sale)
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar avatar-soft-primary avatar-circle">
                                        <span class="avatar-initials">{{ substr($sale->product_name ?? 'P', 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-0">{{ $sale->product_name ?? '-' }}</h6>
                                    <small class="text-muted">ID: {{ optional($sale->product)->id ?? '-' }}</small>
                                </div>
                            </div>
                        </td>
                        <td>{{ $sale->unit_name ?? optional($sale->unit)->name ?? '-' }}</td>
                        <td class="text-center">
                            <span class="badge bg-soft-info">{{ $sale->quantity ?? '0' }}</span>
                        </td>
                        <td class="text-end">{{ _money($sale->price) ?? '-' }}</td>
                        <td class="text-end">{{ _money($sale->discount) ?? '-' }}</td>
                        <td class="text-end fw-semibold">{{ _money(($sale->price * $sale->quantity) - $sale->discount) }}</td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                @can('view', $sale)
                                <a href="{{ route('sales.show', $sale) }}" class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" title="View Details">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @endcan
                                
                                @can('update', $sale)
                                <a href="{{ route('sales.edit', $sale) }}" class="btn btn-outline-secondary btn-sm" data-bs-toggle="tooltip" title="Edit Sale">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                @endcan
                                
                                @can('delete', $sale)
                                <form action="{{ route('sales.destroy', $sale) }}" method="POST" class="d-inline" 
                                      onsubmit="return confirm('Are you sure you want to delete this sale? This action cannot be undone.')">
                                    @csrf @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger btn-sm" data-bs-toggle="tooltip" title="Delete Sale">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center p-5">
                            <x-empty-state 
                                icon="cart" 
                                title="No sales found" 
                                message="No sales match your search criteria or no sales have been recorded yet."
                                action="{{ can('create', App\Models\Sale::class) ? 'true' : 'false' }}"
                                actionUrl="{{ route('sales.create') }}"
                                actionText="Create New Sale"
                            />
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        <div class="card-footer">
            <div class="d-flex justify-content-center">
                {!! $sales->withQueryString()->links() !!}
            </div>
        </div>
    </div>
    <!-- End Sales Table -->
</div>
<!-- End Content -->

<!-- Sales Report Modal -->
<div class="modal fade" id="salesReportModal" tabindex="-1" aria-labelledby="salesReportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="salesReportModalLabel">Generate Sales Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="reportForm">
                    <div class="mb-3">
                        <label for="reportType" class="form-label">Report Type</label>
                        <select class="form-select" id="reportType" name="report_type">
                            <option value="summary">Sales Summary</option>
                            <option value="detailed">Detailed Sales Report</option>
                            <option value="product">Sales by Product</option>
                            <option value="daily">Daily Sales</option>
                            <option value="monthly">Monthly Sales</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="dateRange" class="form-label">Date Range</label>
                        <select class="form-select" id="dateRange" name="date_range">
                            <option value="today">Today</option>
                            <option value="yesterday">Yesterday</option>
                            <option value="this_week">This Week</option>
                            <option value="last_week">Last Week</option>
                            <option value="this_month" selected>This Month</option>
                            <option value="last_month">Last Month</option>
                            <option value="this_year">This Year</option>
                            <option value="custom">Custom Range</option>
                        </select>
                    </div>
                    
                    <div class="row mb-3 custom-date-range d-none">
                        <div class="col-md-6">
                            <label for="startDate" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="startDate" name="start_date">
                        </div>
                        <div class="col-md-6">
                            <label for="endDate" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="endDate" name="end_date">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="fileFormat" class="form-label">File Format</label>
                        <select class="form-select" id="fileFormat" name="file_format">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="generateReportBtn">Generate Report</button>
            </div>
        </div>
    </div>
</div>
<!-- End Sales Report Modal -->

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
        
        // Handle date range selection
        $('#dateRange').change(function() {
            if ($(this).val() === 'custom') {
                $('.custom-date-range').removeClass('d-none');
            } else {
                $('.custom-date-range').addClass('d-none');
            }
        });
        
        // Handle report generation
        $('#generateReportBtn').click(function() {
            // Here you would normally submit the form or make an AJAX request
            // For now, we'll just show an alert
            alert('Report generation functionality will be implemented here.');
            $('#salesReportModal').modal('hide');
        });
    });
</script>
@endpush

@endsection
