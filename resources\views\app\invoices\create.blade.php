@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid" id="invoice-el">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">receipt</x-slot>
        <x-slot name="title">Create New Invoice</x-slot>
        <x-slot name="subtitle">Generate a new sales invoice</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('invoices.index') }}">Invoices</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('invoices.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Invoices
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->
    
    <x-form
        method="POST"
        action="{{ route('invoices.store') }}"
    >
        @include('app.invoices.form-inputs')
    </x-form>
</div>
@endsection

@push('scripts')
<script type="text/javascript">
    $("document").ready(function() {
        var $invoice = @json($invoicable ?? '');
        if ($invoice) {
            // Handle any pre-loaded invoice data
        }
        
        // Set focus on barcode scanner input
        setTimeout(function() {
            document.getElementById('input-scan')?.focus();
        }, 500);
    });
</script>
@endpush