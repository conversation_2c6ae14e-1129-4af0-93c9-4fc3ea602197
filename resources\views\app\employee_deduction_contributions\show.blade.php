@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                Employee Deduction/Contribution Assignment Details
            </h4>
        </div>

        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>Employee Information</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th>Name</th>
                            <td>{{ $employeeDeductionContribution->employee->name ?? '-' }}</td>
                        </tr>
                        <tr>
                            <th>Email</th>
                            <td>{{ $employeeDeductionContribution->employee->email ?? '-' }}</td>
                        </tr>
                        <tr>
                            <th>Basic Pay</th>
                            <td>{{ number_format($employeeDeductionContribution->employee->basic_pay ?? 0, 2) }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h5>Deduction/Contribution Information</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th>Name</th>
                            <td>{{ $employeeDeductionContribution->deductionContribution->name ?? '-' }}</td>
                        </tr>
                        <tr>
                            <th>Type</th>
                            <td>{{ $employeeDeductionContribution->deductionContribution->apply_mode ?? '-' }}</td>
                        </tr>
                        <tr>
                            <th>Formula</th>
                            <td>{{ $employeeDeductionContribution->deductionContribution->formula ?? '-' }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <h5>Assignment Details</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th>Start Date</th>
                            <td>{{ $employeeDeductionContribution->start_date ? $employeeDeductionContribution->start_date->format('d M Y') : '-' }}</td>
                        </tr>
                        <tr>
                            <th>End Date</th>
                            <td>{{ $employeeDeductionContribution->end_date ? $employeeDeductionContribution->end_date->format('d M Y') : 'No End Date' }}</td>
                        </tr>
                        <tr>
                            <th>Created By</th>
                            <td>{{ $employeeDeductionContribution->createdBy->name ?? '-' }}</td>
                        </tr>
                        <tr>
                            <th>Created At</th>
                            <td>{{ $employeeDeductionContribution->created_at ? $employeeDeductionContribution->created_at->format('d M Y H:i:s') : '-' }}</td>
                        </tr>
                        <tr>
                            <th>Updated At</th>
                            <td>{{ $employeeDeductionContribution->updated_at ? $employeeDeductionContribution->updated_at->format('d M Y H:i:s') : '-' }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="mt-4">
                <div class="d-flex justify-content-between">
                    <a href="{{ route('employee-deduction-contributions.index', ['employee_id' => $employeeDeductionContribution->employee_id]) }}" class="btn btn-light">
                        <i class="bi bi-arrow-left"></i> Back
                    </a>
                    <div>
                        <a href="{{ route('employee-deduction-contributions.edit', $employeeDeductionContribution) }}" class="btn btn-primary">
                            <i class="bi bi-pencil"></i> Edit
                        </a>
                        <form action="{{ route('employee-deduction-contributions.destroy', $employeeDeductionContribution) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this assignment?');">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash"></i> Delete
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
