<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Asset extends Model implements HasMedia
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;
    use InteractsWithMedia;

    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'asset_number',
        'description',
        'asset_category_id',
        'acquisition_date',
        'acquisition_cost',
        'salvage_value',
        'depreciable_cost',
        'accumulated_depreciation',
        'current_value',
        'useful_life_years',
        'depreciation_rate',
        'depreciation_method',
        'last_depreciation_date',
        'next_depreciation_date',
        'status',
        'disposal_date',
        'disposal_amount',
        'disposal_notes',
        'location_id',
        'created_by',
        'updated_by',
        'business_type_id',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'acquisition_date' => 'date',
        'acquisition_cost' => 'decimal:2',
        'salvage_value' => 'decimal:2',
        'depreciable_cost' => 'decimal:2',
        'accumulated_depreciation' => 'decimal:2',
        'current_value' => 'decimal:2',
        'useful_life_years' => 'integer',
        'depreciation_rate' => 'decimal:4',
        'last_depreciation_date' => 'date',
        'next_depreciation_date' => 'date',
        'disposal_date' => 'date',
        'disposal_amount' => 'decimal:2',
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];

    protected $appends = ['image'];

    public function getImageAttribute(){
        $file = $this->file();
        return $file ? $file->getUrl() : '';
    }

    public function file($collection = "image"){
        return $this->getMedia($collection)->first();
    }

    public function assetCategory()
    {
        return $this->belongsTo(AssetCategory::class);
    }

    public function location()
    {
        return $this->belongsTo(Branch::class, 'location_id');
    }

    public function assetDepreciations()
    {
        return $this->hasMany(AssetDepreciation::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
