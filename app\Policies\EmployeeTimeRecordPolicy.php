<?php

namespace App\Policies;

use App\Models\User;
use App\Models\EmployeeTimeRecord;
use Illuminate\Auth\Access\HandlesAuthorization;

class EmployeeTimeRecordPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_employee_time_record');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, EmployeeTimeRecord $employeeTimeRecord): bool
    {
        // Users can view their own time records or if they have permission
        return $user->employee && $user->employee->id === $employeeTimeRecord->employee_id 
            || $user->can('view_employee_time_record');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // Users can create their own time records or if they have permission
        return $user->employee || $user->can('create_employee_time_record');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, EmployeeTimeRecord $employeeTimeRecord): bool
    {
        // Users can update their own time records or if they have permission
        return $user->employee && $user->employee->id === $employeeTimeRecord->employee_id 
            || $user->can('update_employee_time_record');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, EmployeeTimeRecord $employeeTimeRecord): bool
    {
        return $user->can('delete_employee_time_record');
    }
}
