@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">pencil-square</x-slot>
        <x-slot name="title">Edit Account Type: {{ $accountType->name }}</x-slot>
        <x-slot name="subtitle">Update account type information</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Accounting</a></li>
            <li class="breadcrumb-item"><a href="{{ route('account-types.index') }}">Account Types</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('account-types.show', $accountType) }}" class="btn btn-outline-info me-2">
                <i class="bi bi-eye me-1"></i> View Account Type
            </a>
            <a href="{{ route('account-types.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Account Types
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Form Card -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-header-title">Account Type Information</h5>
                <span class="badge bg-primary">ID: {{ $accountType->id }}</span>
            </div>
        </div>
        <div class="card-body">
            <x-form
                method="PUT"
                action="{{ route('account-types.update', $accountType) }}"
                class="mt-2"
            >
                @include('app.account_types.form-inputs')

                <div class="d-flex justify-content-between mt-4 border-top pt-4">
                    <div>
                        <a href="{{ route('account-types.index') }}" class="btn btn-outline-secondary me-2">
                            <i class="bi bi-arrow-left me-1"></i> Back
                        </a>

                        <a href="{{ route('account-types.create') }}" class="btn btn-outline-primary">
                            <i class="bi bi-plus-circle me-1"></i> New Account Type
                        </a>
                    </div>

                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-1"></i> Save Changes
                    </button>
                </div>
            </x-form>
        </div>
    </div>
</div>
@endsection
