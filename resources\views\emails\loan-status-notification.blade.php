@extends('emails.layout')

@section('title', 'Loan Status Update - ' . $loan->loan_reference)

@section('content')
<div class="greeting">
    Hello {{ $employee->name }},
</div>

<div class="content">
    <p>We are writing to inform you about an update to your loan application.</p>
</div>

@if($status === 'approved')
<div class="success-box">
    <h3 style="margin-bottom: 15px; color: #2e7d32;">✅ Loan Approved!</h3>
    <p><strong>{{ $statusMessage }}</strong></p>
</div>
@elseif($status === 'rejected' || $status === 'cancelled')
<div class="error-box">
    <h3 style="margin-bottom: 15px; color: #d32f2f;">❌ Loan {{ ucfirst($status) }}</h3>
    <p><strong>{{ $statusMessage }}</strong></p>
</div>
@elseif($status === 'completed')
<div class="success-box">
    <h3 style="margin-bottom: 15px; color: #2e7d32;">🎉 Loan Completed!</h3>
    <p><strong>{{ $statusMessage }}</strong></p>
</div>
@else
<div class="info-box">
    <h3 style="margin-bottom: 15px;">📋 Loan Status Update</h3>
    <p><strong>{{ $statusMessage }}</strong></p>
</div>
@endif

<div class="info-box">
    <h4 style="margin-bottom: 15px;">📄 Loan Details</h4>
    <table class="details-table">
        <tr>
            <td><strong>Loan Reference:</strong></td>
            <td>{{ $loan->loan_reference }}</td>
        </tr>
        <tr>
            <td><strong>Loan Amount:</strong></td>
            <td class="amount">K{{ number_format($loan->loan_amount, 2) }}</td>
        </tr>
        <tr>
            <td><strong>Monthly Installment:</strong></td>
            <td class="amount">K{{ number_format($loan->installment_amount, 2) }}</td>
        </tr>
        @if($status !== 'rejected' && $status !== 'cancelled')
        <tr>
            <td><strong>Remaining Balance:</strong></td>
            <td class="amount">K{{ number_format($loan->remaining_balance, 2) }}</td>
        </tr>
        <tr>
            <td><strong>Total Paid:</strong></td>
            <td class="amount">K{{ number_format($loan->total_paid, 2) }}</td>
        </tr>
        @endif
        <tr>
            <td><strong>Current Status:</strong></td>
            <td>
                <span style="
                    padding: 4px 12px; 
                    border-radius: 20px; 
                    font-size: 12px; 
                    font-weight: bold;
                    background-color: {{ $status === 'approved' ? '#e8f5e8' : ($status === 'completed' ? '#e8f5e8' : ($status === 'rejected' || $status === 'cancelled' ? '#ffebee' : '#e3f2fd')) }};
                    color: {{ $status === 'approved' ? '#2e7d32' : ($status === 'completed' ? '#2e7d32' : ($status === 'rejected' || $status === 'cancelled' ? '#d32f2f' : '#1976d2')) }};
                ">
                    {{ strtoupper($status) }}
                </span>
            </td>
        </tr>
        @if($loan->loan_date)
        <tr>
            <td><strong>Application Date:</strong></td>
            <td>{{ $loan->loan_date->format('M d, Y') }}</td>
        </tr>
        @endif
        @if($loan->first_deduction_date && $status === 'approved')
        <tr>
            <td><strong>First Deduction Date:</strong></td>
            <td>{{ $loan->first_deduction_date->format('M d, Y') }}</td>
        </tr>
        @endif
        @if($loan->actual_completion_date && $status === 'completed')
        <tr>
            <td><strong>Completion Date:</strong></td>
            <td>{{ $loan->actual_completion_date->format('M d, Y') }}</td>
        </tr>
        @endif
    </table>
</div>

@if($loan->purpose)
<div class="content">
    <h4>Loan Purpose:</h4>
    <p style="font-style: italic; background-color: #f8f9fa; padding: 15px; border-radius: 4px;">
        "{{ $loan->purpose }}"
    </p>
</div>
@endif

@if($loan->approval_notes && in_array($status, ['approved', 'rejected', 'cancelled']))
<div class="info-box">
    <h4 style="margin-bottom: 10px;">📝 {{ $status === 'approved' ? 'Approval' : 'Additional' }} Notes:</h4>
    <p style="font-style: italic;">{{ $loan->approval_notes }}</p>
</div>
@endif

@if($status === 'approved')
<div class="warning-box">
    <h4 style="margin-bottom: 10px;">⚠️ Important Information</h4>
    <ul style="margin: 10px 0; padding-left: 20px;">
        <li>Monthly deductions will begin from your next payroll cycle</li>
        <li>Deductions will be automatically processed each month</li>
        <li>You will receive notifications when payments are processed</li>
        <li>Contact HR immediately if you have any concerns</li>
    </ul>
</div>
@elseif($status === 'completed')
<div class="success-box">
    <h4 style="margin-bottom: 10px;">🎊 Congratulations!</h4>
    <p>You have successfully completed your loan payments. No further deductions will be made from your salary.</p>
    <p style="margin-top: 10px;">Thank you for your commitment to timely payments!</p>
</div>
@endif

<div style="text-align: center; margin: 30px 0;">
    <a href="{{ route('employee-loans.show', $loan) }}" class="btn btn-primary">
        View Loan Details
    </a>
    @if(in_array($status, ['approved', 'completed']))
        <a href="#" class="btn btn-success">
            Download Loan Document
        </a>
    @endif
</div>

<div class="content">
    <p>If you have any questions about this loan or need assistance, please contact our HR department.</p>
    
    <p><strong>HR Contact Information:</strong><br>
    Email: <EMAIL><br>
    Phone: +****************</p>
</div>
@endsection
