@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">arrow-return-left</x-slot>
        <x-slot name="title">Create Order Adjustment</x-slot>
        <x-slot name="subtitle">Create returns, refunds, or stock adjustments with negative values</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('orders.index') }}">Orders</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create Adjustment</li>
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group">
                <a href="{{ route('orders.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Orders
                </a>
                @if($originalOrder)
                <a href="{{ route('orders.show', $originalOrder) }}" class="btn btn-outline-info btn-sm">
                    <i class="bi bi-eye me-1"></i>View Original Order
                </a>
                @endif
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    @if($originalOrder)
    <!-- Original Order Info -->
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <i class="bi bi-info-circle me-2"></i>
            <div>
                <strong>Creating adjustment for Order #{{ $originalOrder->order_id }}</strong>
                <br>
                <small>Original Amount: {{ _money($originalOrder->amount_total) }} | 
                       Supplier: {{ $originalOrder->supplier->name ?? 'N/A' }} | 
                       Date: {{ $originalOrder->created_at->format('M d, Y') }}</small>
            </div>
        </div>
    </div>
    @endif

    <!-- Adjustment Form -->
    <form action="{{ route('orders.store-adjustment') }}" method="POST">
        @csrf
        
        @if($originalOrder)
        <input type="hidden" name="original_order_id" value="{{ $originalOrder->id }}">
        @endif

        <!-- Adjustment Type & Reason -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear me-2"></i>Adjustment Details
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="adjustment_type" class="form-label">Adjustment Type <span class="text-danger">*</span></label>
                        <select name="adjustment_type" class="form-select @error('adjustment_type') is-invalid @enderror" required>
                            <option value="">Select adjustment type...</option>
                            <option value="return" {{ old('adjustment_type') == 'return' ? 'selected' : '' }}>Return Order</option>
                            <option value="refund" {{ old('adjustment_type') == 'refund' ? 'selected' : '' }}>Refund Order</option>
                            <option value="stock_adjustment" {{ old('adjustment_type') == 'stock_adjustment' ? 'selected' : '' }}>Stock Adjustment</option>
                        </select>
                        @error('adjustment_type')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-8">
                        <label for="adjustment_reason" class="form-label">Adjustment Reason <span class="text-danger">*</span></label>
                        <input type="text" name="adjustment_reason" 
                               class="form-control @error('adjustment_reason') is-invalid @enderror"
                               value="{{ old('adjustment_reason') }}"
                               placeholder="Explain the reason for this adjustment..."
                               required>
                        @error('adjustment_reason')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Include the order form inputs -->
        @include('app.orders.form-inputs', [
            'editing' => false,
            'order' => null,
            'isAdjustment' => true,
            'originalItems' => $originalItems ?? []
        ])
    </form>
</div>
<!-- End Content -->
@endsection

@push('styles')
<style>
    .adjustment-notice {
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
        border: none;
    }
    
    .negative-value {
        color: #dc3545;
        font-weight: 600;
    }
    
    .original-order-ref {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 1rem;
        margin-bottom: 1rem;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add adjustment-specific styling
    const formTitle = document.querySelector('.card-title');
    if (formTitle && formTitle.textContent.includes('Order Information')) {
        formTitle.innerHTML = '<i class="bi bi-arrow-return-left me-2"></i>Adjustment Information';
    }
    
    // Add notice about negative values
    const orderSummary = document.querySelector('.card-body');
    if (orderSummary) {
        const notice = document.createElement('div');
        notice.className = 'alert alert-warning mb-3';
        notice.innerHTML = `
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>Note:</strong> All quantities and amounts will be automatically converted to negative values for this adjustment.
        `;
        orderSummary.insertBefore(notice, orderSummary.firstChild);
    }
});
</script>
@endpush
