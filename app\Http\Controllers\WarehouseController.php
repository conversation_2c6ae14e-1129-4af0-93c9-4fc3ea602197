<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Branch;
use App\Models\Product;
use App\Models\Stock;
use App\Models\StockItem;
use App\Models\Warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class WarehouseController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Warehouse::class);

        $search = $request->get('search', '');
        $status = $request->get('status', '');
        $branch = $request->get('branch', '');

        $query = Warehouse::search($search);
        
        // Filter by status if provided
        if ($status !== '') {
            $query->where('is_active', $status === 'active');
        }
        
        // Filter by branch if provided
        if ($branch) {
            $query->where('branch_id', $branch);
        }
        
        $warehouses = $query->latest()
            ->paginate(10)
            ->withQueryString();
            
        // Get branches for filter dropdown
        $branches = Branch::pluck('name', 'id');
        
        // Get warehouse statistics
        $totalWarehouses = Warehouse::count();
        $activeWarehouses = Warehouse::where('is_active', true)->count();
        $inactiveWarehouses = Warehouse::where('is_active', false)->count();
        
        return view('app.warehouses.index', compact(
            'warehouses', 
            'search', 
            'branches', 
            'totalWarehouses', 
            'activeWarehouses', 
            'inactiveWarehouses'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', Warehouse::class);

        $branches = Branch::pluck('name', 'id');
        $users = User::pluck('name', 'id');
        $managers = User::pluck('name', 'id');

        return view('app.warehouses.create', compact('branches', 'users', 'managers'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', Warehouse::class);


        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|tenant_unique:warehouses,code',
            'address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'manager_id' => 'nullable|max:255',
            'description' => 'nullable|string|max:255',
            'is_active' => 'nullable',
            'branch_id' => 'required|tenant_exists:branches,id',
        ]);


        $warehouse = Warehouse::create($validated);

        if ($request->hasFile('image')) {
            $warehouse->addMediaFromRequest('image')->toMediaCollection('image');
        }

        return redirect()
            ->route('warehouses.edit', $warehouse)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Warehouse  $warehouse
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Warehouse $warehouse)
    {
        $this->authorize('view', $warehouse);
        
        // Get warehouse stock statistics
        $totalStockItems = $warehouse->total_stock_items;
        $totalProducts = $warehouse->total_products;
        $stockValue = $warehouse->stock_value;
        
        // Get products in this warehouse with stock levels
        $products = Product::select('products.*', DB::raw('COUNT(stock_items.id) as stock_count'))
            ->leftJoin('stock_items', function($join) use ($warehouse) {
                $join->on('products.id', '=', 'stock_items.product_id')
                    ->where('stock_items.branch_id', $warehouse->branch_id)
                    ->whereNull('stock_items.invoice_id');
            })
            ->groupBy('products.id', 'products.name', 'products.description', 'products.business_type_id', 'products.created_at', 'products.updated_at')
            ->orderBy('stock_count', 'desc')
            ->paginate(10);
            
        // Get low stock products
        $lowStockThreshold = 5; // This could be a setting
        $lowStockProducts = $warehouse->getLowStockProducts($lowStockThreshold);
        
        // Get recent stock movements
        $recentStockItems = StockItem::where('branch_id', $warehouse->branch_id)
            ->with(['product', 'createdBy'])
            ->latest()
            ->take(10)
            ->get();
            
        return view('app.warehouses.show', compact(
            'warehouse',
            'totalStockItems',
            'totalProducts',
            'stockValue',
            'products',
            'lowStockProducts',
            'lowStockThreshold',
            'recentStockItems'
        ));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Warehouse  $warehouse
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Warehouse $warehouse)
    {
        $this->authorize('update', $warehouse);

        $branches = Branch::pluck('name', 'id');
        $users = User::pluck('name', 'id');
        $managers = User::pluck('name', 'id');

        return view('app.warehouses.edit', compact('warehouse', 'branches', 'users', 'managers'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Warehouse  $warehouse
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Warehouse $warehouse)
    {
        $this->authorize('update', $warehouse);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|tenant_unique:warehouses,code,'.$warehouse->id,
            'address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'manager_name' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'branch_id' => 'required|tenant_exists:branches,id',
        ]);

        $warehouse->update($validated);

        if ($request->hasFile('image')) {
            $warehouse->clearMediaCollection('image');
            $warehouse->addMediaFromRequest('image')->toMediaCollection('image');
        }

        return redirect()
            ->route('warehouses.edit', $warehouse)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Warehouse  $warehouse
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Warehouse $warehouse)
    {
        $this->authorize('delete', $warehouse);

        // Check if warehouse has stock items
        $stockItemCount = $warehouse->stockItems()->count();
        if ($stockItemCount > 0) {
            return redirect()
                ->route('warehouses.show', $warehouse)
                ->withError("Cannot delete warehouse with {$stockItemCount} stock items. Transfer or remove stock first.");
        }

        $warehouse->delete();

        return redirect()
            ->route('warehouses.index')
            ->withSuccess(__('crud.common.removed'));
    }
    
    /**
     * Display the warehouse inventory.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Warehouse  $warehouse
     * @return \Illuminate\Http\Response
     */
    public function inventory(Request $request, Warehouse $warehouse)
    {
        $this->authorize('view', $warehouse);
        
        $search = $request->get('search', '');
        $category = $request->get('category', '');
        $sortBy = $request->get('sort_by', 'stock_count');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $query = Product::select('products.*', DB::raw('COUNT(stock_items.id) as stock_count'))
            ->leftJoin('stock_items', function($join) use ($warehouse) {
                $join->on('products.id', '=', 'stock_items.product_id')
                    ->where('stock_items.branch_id', $warehouse->branch_id)
                    ->whereNull('stock_items.invoice_id');
            })
            ->groupBy('products.id', 'products.name', 'products.sku', 'products.description', 'products.business_type_id', 'products.created_at', 'products.updated_at');
            
        // Apply search filter
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('products.name', 'like', "%{$search}%")
                  ->orWhere('products.sku', 'like', "%{$search}%")
                  ->orWhere('products.description', 'like', "%{$search}%");
            });
        }
        
        // Apply category filter
        if ($category) {
            $query->where('products.category_id', $category);
        }
        
        // Apply sorting
        if ($sortBy === 'name') {
            $query->orderBy('products.name', $sortOrder);
        } elseif ($sortBy === 'sku') {
            $query->orderBy('products.sku', $sortOrder);
        } elseif ($sortBy === 'stock_count') {
            $query->orderBy('stock_count', $sortOrder);
        }
        
        $products = $query->paginate(15)->withQueryString();
        
        // Get categories for filter dropdown
        $categories = \App\Models\Category::pluck('name', 'id');
        
        return view('app.warehouses.inventory', compact(
            'warehouse',
            'products',
            'search',
            'category',
            'categories',
            'sortBy',
            'sortOrder'
        ));
    }
    
    /**
     * Display the stock transfer form.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function transferForm(Request $request)
    {
        $this->authorize('create', Stock::class);
        
        $sourceWarehouseId = $request->get('source', auth()->user()->branch_id);
        $destinationWarehouseId = $request->get('destination', '');
        
        $sourceWarehouse = Warehouse::where('branch_id', $sourceWarehouseId)->first();
        $destinationWarehouse = $destinationWarehouseId ? Warehouse::where('branch_id', $destinationWarehouseId)->first() : null;
        
        // Get warehouses for dropdown
        $warehouses = Warehouse::where('is_active', true)
            ->where('branch_id', '!=', $sourceWarehouseId)
            ->get()
            ->pluck('name', 'branch_id');
            
        // Get products in source warehouse with stock levels
        $products = [];
        if ($sourceWarehouse) {
            $products = Product::select('products.*', DB::raw('COUNT(stock_items.id) as stock_count'))
                ->join('stock_items', function($join) use ($sourceWarehouse) {
                    $join->on('products.id', '=', 'stock_items.product_id')
                        ->where('stock_items.branch_id', $sourceWarehouse->branch_id)
                        ->whereNull('stock_items.invoice_id');
                })
                ->groupBy('products.id', 'products.name', 'products.sku', 'products.description', 'products.business_type_id', 'products.created_at', 'products.updated_at')
                ->having('stock_count', '>', 0)
                ->orderBy('products.name')
                ->get();
        }
        
        return view('app.warehouses.transfer', compact(
            'sourceWarehouse',
            'destinationWarehouse',
            'warehouses',
            'products'
        ));
    }
    
    /**
     * Process the stock transfer.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function processTransfer(Request $request)
    {
        $this->authorize('create', Stock::class);
        
        $validated = $request->validate([
            'source_warehouse_id' => 'required|tenant_exists:warehouses,branch_id',
            'destination_warehouse_id' => 'required|tenant_exists:warehouses,branch_id|different:source_warehouse_id',
            'products' => 'required|array',
            'products.*' => 'tenant_exists:products,id',
            'quantities' => 'required|array',
            'quantities.*' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:255',
        ]);
        
        $sourceWarehouse = Warehouse::where('branch_id', $validated['source_warehouse_id'])->first();
        $destinationWarehouse = Warehouse::where('branch_id', $validated['destination_warehouse_id'])->first();
        
        // Begin transaction
        DB::beginTransaction();
        
        try {
            // Create a transfer record
            $transfer = new \App\Models\Transfer();
            $transfer->source_branch_id = $validated['source_warehouse_id'];
            $transfer->destination_branch_id = $validated['destination_warehouse_id'];
            $transfer->notes = $validated['notes'] ?? 'Stock transfer';
            $transfer->created_by = auth()->id();
            $transfer->save();
            
            // Process each product
            foreach ($validated['products'] as $index => $productId) {
                $quantity = $validated['quantities'][$index] ?? 0;
                if ($quantity <= 0) continue;
                
                // Check if source warehouse has enough stock
                if (!$sourceWarehouse->hasProduct($productId, $quantity)) {
                    throw new \Exception("Insufficient stock for product ID: {$productId}");
                }
                
                // Get the product
                $product = Product::findOrFail($productId);
                
                // Get stock items to transfer
                $stockItems = StockItem::where('product_id', $productId)
                    ->where('branch_id', $sourceWarehouse->branch_id)
                    ->whereNull('invoice_id')
                    ->limit($quantity)
                    ->get();
                
                // Update each stock item
                foreach ($stockItems as $item) {
                    $item->branch_id = $destinationWarehouse->branch_id;
                    $item->updated_by = auth()->id();
                    $item->save();
                    
                    // Create transfer item record
                    $transfer->items()->create([
                        'product_id' => $productId,
                        'stock_item_id' => $item->id,
                        'created_by' => auth()->id(),
                    ]);
                }
            }
            
            DB::commit();
            
            return redirect()
                ->route('warehouses.inventory', $destinationWarehouse)
                ->withSuccess("Successfully transferred stock to {$destinationWarehouse->name}");
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError("Transfer failed: " . $e->getMessage());
        }
    }
    
    /**
     * Display the stock adjustment form.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Warehouse  $warehouse
     * @return \Illuminate\Http\Response
     */
    public function adjustmentForm(Request $request, Warehouse $warehouse)
    {
        $this->authorize('update', $warehouse);
        
        $products = Product::orderBy('name')->get();
        
        return view('app.warehouses.adjustment', compact('warehouse', 'products'));
    }
    
    /**
     * Process the stock adjustment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Warehouse  $warehouse
     * @return \Illuminate\Http\Response
     */
    public function processAdjustment(Request $request, Warehouse $warehouse)
    {
        $this->authorize('update', $warehouse);
        
        $validated = $request->validate([
            'product_id' => 'required|tenant_exists:products,id',
            'adjustment_type' => 'required|in:add,remove',
            'quantity' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
        ]);
        
        $product = Product::findOrFail($validated['product_id']);
        $quantity = $validated['quantity'];
        
        // Begin transaction
        DB::beginTransaction();
        
        try {
            if ($validated['adjustment_type'] === 'add') {
                // Create a new stock record
                $stock = Stock::create([
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'balance' => $quantity,
                    'description' => "Stock adjustment: {$validated['reason']}",
                    'selling_price' => $product->selling_price,
                    'buying_price' => $product->buying_price,
                    'branch_id' => $warehouse->branch_id,
                    'created_by' => auth()->id(),
                    'status_id' => 1, // Assuming 1 is active status
                ]);
                
                // Generate stock items
                for ($i = 0; $i < $quantity; $i++) {
                    $uuid = time() . '-' . mt_rand();
                    StockItem::create([
                        'selling_price' => $product->selling_price,
                        'buying_price' => $product->buying_price,
                        'product_id' => $product->id,
                        'stock_id' => $stock->id,
                        'branch_id' => $warehouse->branch_id,
                        'created_by' => auth()->id(),
                        'code' => $uuid,
                        'description' => "Stock adjustment: {$validated['reason']}",
                    ]);
                }
                
                $message = "Added {$quantity} units of {$product->name} to inventory";
                
            } else { // remove
                // Check if warehouse has enough stock
                $currentStock = $warehouse->getProductStock($product->id);
                if ($currentStock < $quantity) {
                    throw new \Exception("Cannot remove {$quantity} units. Only {$currentStock} units available.");
                }
                
                // Get stock items to remove
                $stockItems = StockItem::where('product_id', $product->id)
                    ->where('branch_id', $warehouse->branch_id)
                    ->whereNull('invoice_id')
                    ->limit($quantity)
                    ->get();
                
                // Create adjustment record
                $adjustment = new \App\Models\StockAdjustment();
                $adjustment->warehouse_id = $warehouse->id;
                $adjustment->product_id = $product->id;
                $adjustment->quantity = $quantity;
                $adjustment->adjustment_type = 'remove';
                $adjustment->reason = $validated['reason'];
                $adjustment->created_by = auth()->id();
                $adjustment->save();
                
                // Soft delete the stock items
                foreach ($stockItems as $item) {
                    $item->delete();
                    
                    // Record in adjustment items
                    $adjustment->items()->create([
                        'stock_item_id' => $item->id,
                        'created_by' => auth()->id(),
                    ]);
                }
                
                $message = "Removed {$quantity} units of {$product->name} from inventory";
            }
            
            DB::commit();
            
            return redirect()
                ->route('warehouses.inventory', $warehouse)
                ->withSuccess($message);
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError("Adjustment failed: " . $e->getMessage());
        }
    }
}
