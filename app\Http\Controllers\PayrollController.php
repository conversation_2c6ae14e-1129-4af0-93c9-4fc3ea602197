<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\MonthlyPayroll;
use App\Models\PayrollItem;
use App\Models\DeductionContribution;
use App\Models\JournalEntry;
use App\Models\FiscalYear;
use App\Models\FiscalPeriod;
use App\Models\Account;
use App\Libraries\EmployeeHandler;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use PDF;

class PayrollController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }
    /**
     * Display a listing of the payrolls.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', MonthlyPayroll::class);

        $search = $request->get('search', '');
        $month = $request->get('month', date('m'));
        $year = $request->get('year', date('Y'));

        $payrolls = MonthlyPayroll::search($search)
            ->whereYear('date_from', $year)
            ->whereMonth('date_from', $month)
            ->with('employee')
            ->latest()
            ->paginate()
            ->withQueryString();

        $months = [
            '01' => 'January',
            '02' => 'February',
            '03' => 'March',
            '04' => 'April',
            '05' => 'May',
            '06' => 'June',
            '07' => 'July',
            '08' => 'August',
            '09' => 'September',
            '10' => 'October',
            '11' => 'November',
            '12' => 'December',
        ];

        $years = range(date('Y') - 5, date('Y') + 5);
        $years = array_combine($years, $years);

        return view('app.payroll.index', compact('payrolls', 'search', 'months', 'years', 'month', 'year'));
    }

    /**
     * Show the form for generating payroll.
     *
     * @return \Illuminate\Http\Response
     */
    public function generate()
    {
        $this->authorize('create', MonthlyPayroll::class);

        $months = [
            '01' => 'January',
            '02' => 'February',
            '03' => 'March',
            '04' => 'April',
            '05' => 'May',
            '06' => 'June',
            '07' => 'July',
            '08' => 'August',
            '09' => 'September',
            '10' => 'October',
            '11' => 'November',
            '12' => 'December',
        ];

        $years = range(date('Y') - 5, date('Y') + 5);
        $years = array_combine($years, $years);

        return view('app.payroll.generate', compact('months', 'years'));
    }

    /**
     * Process payroll generation.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function processGenerate(Request $request)
    {
        $this->authorize('create', MonthlyPayroll::class);

        $validated = $request->validate([
            'month' => 'required|string',
            'year' => 'required|string',
        ]);

        try {
            $employeeHandler = new EmployeeHandler();
            $employeeHandler->generatePayroll($validated);

            // Get the generated payrolls for this period
            $payrolls = MonthlyPayroll::whereYear('date_from', $validated['year'])
                ->whereMonth('date_from', $validated['month'])
                ->with('employee')
                ->get();

            // Send payslips to all employees
            if ($payrolls->count() > 0) {
                $emailResults = $this->notificationService->sendBulkPayslips($payrolls);

                // Send completion notification to managers
                $period = date('F Y', strtotime($validated['year'] . '-' . $validated['month'] . '-01'));
                $this->notificationService->sendPayrollCompletionNotification($payrolls, $period);

                $successMessage = "Payroll generated successfully. Payslips sent to {$emailResults['sent']} employees.";
                if ($emailResults['failed'] > 0) {
                    $successMessage .= " {$emailResults['failed']} emails failed to send.";
                }
            } else {
                $successMessage = 'Payroll generated successfully.';
            }

            return redirect()
                ->route('payroll.index', ['month' => $validated['month'], 'year' => $validated['year']])
                ->withSuccess($successMessage);
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->withError('Error generating payroll: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified payroll.
     *
     * @param \App\Models\MonthlyPayroll $payroll
     * @return \Illuminate\Http\Response
     */
    public function show(MonthlyPayroll $payroll)
    {
        $this->authorize('view', $payroll);

        $payroll->load('employee', 'payrollItems');

        return view('app.payroll.show', compact('payroll'));
    }

    /**
     * Show the form for editing the specified payroll.
     *
     * @param \App\Models\MonthlyPayroll $payroll
     * @return \Illuminate\Http\Response
     */
    public function edit(MonthlyPayroll $payroll)
    {
        $this->authorize('update', $payroll);

        $payroll->load('employee', 'deductions', 'contributions');

        $deductionContributions = DeductionContribution::where('status_id', 1)
            ->orderBy('name')
            ->get();


        return view('app.payroll.edit', compact('payroll', 'deductionContributions'));
    }

    /**
     * Update the specified payroll.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\MonthlyPayroll $payroll
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, MonthlyPayroll $payroll)
    {
        $this->authorize('update', $payroll);

        $validated = $request->validate([
            'basic_pay' => 'required|numeric|min:0',
            'items' => 'nullable|array',
            'items.*.id' => 'nullable|tenant_exists:payroll_items,id',
            'items.*.name' => 'required|string|max:255',
            'items.*.amount' => 'required|numeric|min:0',
            'items.*.type' => 'required|in:DEDUCTION,CONTRIBUTION',
        ]);

        DB::beginTransaction();

        try {
            // Update basic pay
            $payroll->update([
                'basic_pay' => $validated['basic_pay'],
                'updated_by' => auth()->id(),
            ]);

            // Get existing item IDs to track which ones to keep
            $existingItemIds = $payroll->payrollItems->pluck('id')->toArray();
            $updatedItemIds = [];

            // Update or create payroll items
            if (isset($validated['items']) && !empty($validated['items'])) {
                foreach ($validated['items'] as $item) {
                    if (isset($item['id']) && !empty($item['id'])) {
                        // Update existing item
                        $payrollItem = PayrollItem::find($item['id']);
                        if ($payrollItem && $payrollItem->monthly_payroll_id == $payroll->id) {
                            $payrollItem->update([
                                'name' => trim($item['name']),
                                'amount' => $item['amount'],
                                'type' => $item['type'],
                                'updated_by' => auth()->id(),
                            ]);
                            $updatedItemIds[] = $item['id'];
                        }
                    } else {
                        // Create new item
                        $newItem = PayrollItem::create([
                            'name' => trim($item['name']),
                            'amount' => $item['amount'],
                            'type' => $item['type'],
                            'monthly_payroll_id' => $payroll->id,
                            'created_by' => auth()->id(),
                        ]);
                        $updatedItemIds[] = $newItem->id;
                    }
                }
            }

            // Delete items that were removed
            $itemsToDelete = array_diff($existingItemIds, $updatedItemIds);
            if (!empty($itemsToDelete)) {
                PayrollItem::whereIn('id', $itemsToDelete)
                    ->where('monthly_payroll_id', $payroll->id)
                    ->delete();
            }

            // Recalculate payroll totals
            $this->recalculatePayroll($payroll);

            DB::commit();

            return redirect()
                ->route('payroll.show', $payroll)
                ->withSuccess('Payroll updated successfully.');
        } catch (\Exception $e) {
            DB::rollback();

            \Log::error('Payroll update error: ' . $e->getMessage(), [
                'payroll_id' => $payroll->id,
                'user_id' => auth()->id(),
                'request_data' => $request->all()
            ]);

            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred while updating payroll: ' . $e->getMessage());
        }
    }

    /**
     * Recalculate payroll totals after updating items
     */
    private function recalculatePayroll(MonthlyPayroll $payroll)
    {
        $payroll->load('payrollItems');

        $contributions = $payroll->payrollItems->where('type', 'CONTRIBUTION');
        $deductions = $payroll->payrollItems->where('type', 'DEDUCTION');

        $totalContributions = $contributions->sum('amount');
        $totalDeductions = $deductions->sum('amount');

        $grossPay = $payroll->basic_pay + $totalContributions;

        // Calculate tax using the dynamic tax bracket system
        $taxAmount = \App\Models\TaxBracket::calculateTax($grossPay);

        $netPay = $grossPay - $taxAmount - $totalDeductions;

        $payroll->update([
            'gross_pay' => $grossPay,
            'tax_amount' => $taxAmount,
            'payee' => $taxAmount, // For backward compatibility
            'net_pay' => $netPay,
            'updated_by' => auth()->id(),
        ]);
    }

    /**
     * Generate payslip for the specified payroll.
     *
     * @param \App\Models\MonthlyPayroll $payroll
     * @return \Illuminate\Http\Response
     */
    public function payslip(MonthlyPayroll $payroll)
    {
        $this->authorize('view', $payroll);

        $payroll->load('employee', 'payrollItems', 'contributions', 'deductions');

        $pdf = PDF::loadView('app.payroll.payslip_pdf', compact('payroll'));
        return $pdf->download('payslip_' . $payroll->employee->name . '_' . $payroll->date_from->format('Y-m') . '.pdf');
    }

    /**
     * Email payslip to the employee.
     *
     * @param \App\Models\MonthlyPayroll $payroll
     * @return \Illuminate\Http\Response
     */
    public function emailPayslip(MonthlyPayroll $payroll)
    {
        $this->authorize('view', $payroll);

        try {
            $success = $this->notificationService->sendPayslip($payroll);

            if ($success) {
                return redirect()
                    ->back()
                    ->withSuccess("Payslip emailed successfully to {$payroll->employee->name}.");
            } else {
                return redirect()
                    ->back()
                    ->withError("Failed to send payslip email to {$payroll->employee->name}. Please check the email configuration.");
            }
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withError('Error sending payslip email: ' . $e->getMessage());
        }
    }

    /**
     * Send payslips to all employees for a specific period.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function bulkEmailPayslips(Request $request)
    {
        $this->authorize('view-any', MonthlyPayroll::class);

        $validated = $request->validate([
            'month' => 'required|string',
            'year' => 'required|string',
        ]);

        try {
            $payrolls = MonthlyPayroll::whereYear('date_from', $validated['year'])
                ->whereMonth('date_from', $validated['month'])
                ->with('employee')
                ->get();

            if ($payrolls->isEmpty()) {
                return redirect()
                    ->back()
                    ->withError('No payroll records found for the selected period.');
            }

            $emailResults = $this->notificationService->sendBulkPayslips($payrolls);

            $period = date('F Y', strtotime($validated['year'] . '-' . $validated['month'] . '-01'));
            $successMessage = "Bulk email completed for {$period}. Sent: {$emailResults['sent']}, Failed: {$emailResults['failed']}.";

            if ($emailResults['failed'] > 0) {
                $successMessage .= " Some emails failed to send. Please check the logs for details.";
            }

            return redirect()
                ->back()
                ->withSuccess($successMessage);
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withError('Error sending bulk payslip emails: ' . $e->getMessage());
        }
    }

    /**
     * Generate journal entries for payroll.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function journalEntries(Request $request)
    {
        $this->authorize('create', JournalEntry::class);

        $validated = $request->validate([
            'month' => 'required|string',
            'year' => 'required|string',
            'fiscal_year_id' => 'required|tenant_exists:fiscal_years,id',
            'fiscal_period_id' => 'required|tenant_exists:fiscal_periods,id',
        ]);

        $month = $validated['month'];
        $year = $validated['year'];

        $payrolls = MonthlyPayroll::whereYear('date_from', $year)
            ->whereMonth('date_from', $month)
            ->with('employee', 'payrollItems', 'contributions', 'deductions')
            ->get();

        if ($payrolls->isEmpty()) {
            return redirect()
                ->back()
                ->withError('No payroll data found for the selected period.');
        }

        DB::beginTransaction();

        try {
            // Create journal entry
            $journalEntry = JournalEntry::create([
                'entry_number' => 'PR-' . $year . $month . '-' . sprintf('%06d', JournalEntry::count() + 1),
                'entry_date' => date('Y-m-d'),
                'reference_number' => 'PAYROLL-' . $year . '-' . $month,
                'description' => 'Payroll for ' . date('F Y', strtotime($year . '-' . $month . '-01')),
                'entry_type' => 'payroll',
                'status' => 'posted',
                'fiscal_year_id' => $validated['fiscal_year_id'],
                'fiscal_period_id' => $validated['fiscal_period_id'],
                'created_by' => auth()->id(),
            ]);

            // TODO: Add journal entry lines based on payroll data
            // This would require setting up the appropriate accounts for:
            // - Salary Expense
            // - Payroll Tax Liability
            // - Employee Benefits Liability
            // - Cash/Bank Account

            DB::commit();

            return redirect()
                ->route('journal-entries.show', $journalEntry)
                ->withSuccess('Journal entry created successfully.');
        } catch (\Exception $e) {
            DB::rollback();

            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Show form for creating journal entries.
     *
     * @return \Illuminate\Http\Response
     */
    public function createJournalEntries()
    {
        $this->authorize('create', JournalEntry::class);

        $months = [
            '01' => 'January',
            '02' => 'February',
            '03' => 'March',
            '04' => 'April',
            '05' => 'May',
            '06' => 'June',
            '07' => 'July',
            '08' => 'August',
            '09' => 'September',
            '10' => 'October',
            '11' => 'November',
            '12' => 'December',
        ];

        $years = range(date('Y') - 5, date('Y') + 5);
        $years = array_combine($years, $years);

        $fiscalYears = FiscalYear::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        $fiscalPeriods = FiscalPeriod::where('is_active', true)
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.payroll.journal_entries', compact('months', 'years', 'fiscalYears', 'fiscalPeriods'));
    }
}
