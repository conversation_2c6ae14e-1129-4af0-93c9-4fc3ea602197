@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{{ route('work-hours-configurations.index') }}">Work Hours Configurations</a>
                        </li>
                        <li class="breadcrumb-item active">Create</li>
                    </ol>
                </nav>
                <h1 class="page-header-title">Create Work Hours Configuration</h1>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Configuration Details</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('work-hours-configurations.store') }}">
                        @csrf

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Configuration Name</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                        id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" 
                                            name="is_active" value="1" {{ old('is_active') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Set as Active Configuration
                                        </label>
                                    </div>
                                    <small class="text-muted">
                                        Only one configuration can be active at a time
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_time" class="form-label">Start Time</label>
                                    <input type="time" class="form-control @error('start_time') is-invalid @enderror" 
                                        id="start_time" name="start_time" value="{{ old('start_time', '09:00') }}" required>
                                    @error('start_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_time" class="form-label">End Time</label>
                                    <input type="time" class="form-control @error('end_time') is-invalid @enderror" 
                                        id="end_time" name="end_time" value="{{ old('end_time', '17:00') }}" required>
                                    @error('end_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="standard_hours_per_day" class="form-label">Standard Hours per Day</label>
                                    <input type="number" class="form-control @error('standard_hours_per_day') is-invalid @enderror" 
                                        id="standard_hours_per_day" name="standard_hours_per_day" 
                                        value="{{ old('standard_hours_per_day', '8.00') }}" 
                                        step="0.25" min="1" max="24" required>
                                    @error('standard_hours_per_day')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">
                                        Hours worked beyond this will be considered overtime
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="overtime_rate" class="form-label">Overtime Rate Multiplier</label>
                                    <input type="number" class="form-control @error('overtime_rate') is-invalid @enderror" 
                                        id="overtime_rate" name="overtime_rate" 
                                        value="{{ old('overtime_rate', '1.50') }}" 
                                        step="0.25" min="1" max="5" required>
                                    @error('overtime_rate')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">
                                        e.g., 1.5 means overtime is paid at 1.5x regular rate
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description (Optional)</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                id="description" name="description" rows="3">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Create Configuration
                            </button>
                            <a href="{{ route('work-hours-configurations.index') }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Configuration Guide</h5>
                </div>
                <div class="card-body">
                    <h6>Work Hours</h6>
                    <p class="small text-muted">
                        Set the standard work hours for your organization. 
                        Employees checking in before start time or after end time will have those hours counted as overtime.
                    </p>

                    <h6>Standard Hours per Day</h6>
                    <p class="small text-muted">
                        The number of hours an employee is expected to work per day. 
                        Any hours beyond this will be calculated as overtime.
                    </p>

                    <h6>Overtime Rate</h6>
                    <p class="small text-muted">
                        The multiplier applied to overtime hours for payroll calculation. 
                        Common rates are 1.5x (time and a half) or 2.0x (double time).
                    </p>

                    <h6>Active Configuration</h6>
                    <p class="small text-muted">
                        Only one configuration can be active at a time. 
                        The active configuration is used for all overtime calculations.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
