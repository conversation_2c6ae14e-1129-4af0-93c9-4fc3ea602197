@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-header-title">Time Tracking</h1>
                <p class="page-header-text">Track your daily work hours and tasks</p>
            </div>
        </div>
    </div>

    <!-- Today's Status Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Today's Status - {{ now()->format('F j, Y') }}</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div id="time-status">
                                @if($todayRecord)
                                    @if($todayRecord->check_in_time && !$todayRecord->check_out_time)
                                        <div class="alert alert-success">
                                            <i class="bi bi-clock"></i> Checked in at {{ $todayRecord->formatted_check_in }}
                                        </div>
                                    @elseif($todayRecord->check_in_time && $todayRecord->check_out_time)
                                        <div class="alert alert-info">
                                            <i class="bi bi-check-circle"></i> Work completed for today
                                            <br>
                                            <small>
                                                In: {{ $todayRecord->formatted_check_in }} | 
                                                Out: {{ $todayRecord->formatted_check_out }} | 
                                                Total: {{ number_format($todayRecord->total_hours, 2) }}h
                                            </small>
                                        </div>
                                    @else
                                        <div class="alert alert-warning">
                                            <i class="bi bi-exclamation-triangle"></i> Not checked in yet
                                        </div>
                                    @endif
                                @else
                                    <div class="alert alert-warning">
                                        <i class="bi bi-exclamation-triangle"></i> Not checked in yet
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <button id="check-in-btn" class="btn btn-success" 
                                    @if($todayRecord && $todayRecord->check_in_time) disabled @endif>
                                    <i class="bi bi-play-circle"></i> Check In
                                </button>
                                <button id="check-out-btn" class="btn btn-danger" 
                                    @if(!$todayRecord || !$todayRecord->check_in_time || $todayRecord->check_out_time) disabled @endif>
                                    <i class="bi bi-stop-circle"></i> Check Out
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Row -->
    <div class="row mb-4">
        <!-- Add Task Card -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Add Task</h5>
                </div>
                <div class="card-body">
                    <form id="add-task-form">
                        <div class="mb-3">
                            <label for="task_description" class="form-label">Task Description</label>
                            <input type="text" class="form-control" id="task_description" name="task_description" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="time_spent" class="form-label">Time Spent (hours)</label>
                                <input type="number" class="form-control" id="time_spent" name="time_spent" step="0.25" min="0.25" required>
                            </div>
                            <div class="col-md-6">
                                <label for="start_time" class="form-label">Start Time (optional)</label>
                                <input type="time" class="form-control" id="start_time" name="start_time">
                            </div>
                        </div>
                        <div class="mt-3">
                            <label for="notes" class="form-label">Notes (optional)</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                        </div>
                        <div class="mt-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> Add Task
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Add Comment Card -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Daily Comment</h5>
                </div>
                <div class="card-body">
                    <form id="add-comment-form">
                        <div class="mb-3">
                            <label for="daily_comment" class="form-label">Comment for Today</label>
                            <textarea class="form-control" id="daily_comment" name="comment" rows="4" 
                                placeholder="Add notes about your work today...">{{ $todayRecord->daily_comment ?? '' }}</textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-chat-left-text"></i> Save Comment
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">This Month Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h3 class="text-primary">{{ number_format($monthSummary['total_hours'], 1) }}</h3>
                                <p class="text-muted mb-0">Total Hours</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h3 class="text-success">{{ number_format($monthSummary['regular_hours'], 1) }}</h3>
                                <p class="text-muted mb-0">Regular Hours</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h3 class="text-warning">{{ number_format($monthSummary['overtime_hours'], 1) }}</h3>
                                <p class="text-muted mb-0">Overtime Hours</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h3 class="text-info">{{ $monthSummary['days_worked'] }}</h3>
                            <p class="text-muted mb-0">Days Worked</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Records -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Recent Time Records</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive" style="min-height: 200px;">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Check In</th>
                                    <th>Check Out</th>
                                    <th>Total Hours</th>
                                    <th>Regular</th>
                                    <th>Overtime</th>
                                    <th>Status</th>
                                    <th>Tasks</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentRecords as $record)
                                <tr>
                                    <td>{{ $record->work_date->format('M j, Y') }}</td>
                                    <td>{{ $record->formatted_check_in ?? '-' }}</td>
                                    <td>{{ $record->formatted_check_out ?? '-' }}</td>
                                    <td>{{ number_format($record->total_hours, 2) }}h</td>
                                    <td>{{ number_format($record->regular_hours, 2) }}h</td>
                                    <td>{{ number_format($record->overtime_hours, 2) }}h</td>
                                    <td>
                                        @if($record->status == 'checked_out')
                                            <span class="badge bg-success">Complete</span>
                                        @elseif($record->status == 'checked_in')
                                            <span class="badge bg-warning">In Progress</span>
                                        @else
                                            <span class="badge bg-secondary">Incomplete</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $record->tasks->count() }} tasks</span>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center text-muted">No time records found</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Check In
    $('#check-in-btn').click(function() {
        $.post('{{ route("time-tracking.check-in") }}', {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                location.reload();
            }
        })
        .fail(function(xhr) {
            const error = xhr.responseJSON?.error || 'Failed to check in';
            alert(error);
        });
    });

    // Check Out
    $('#check-out-btn').click(function() {
        $.post('{{ route("time-tracking.check-out") }}', {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                location.reload();
            }
        })
        .fail(function(xhr) {
            const error = xhr.responseJSON?.error || 'Failed to check out';
            alert(error);
        });
    });

    // Add Task
    $('#add-task-form').submit(function(e) {
        e.preventDefault();
        
        $.post('{{ route("time-tracking.add-task") }}', {
            _token: '{{ csrf_token() }}',
            task_description: $('#task_description').val(),
            time_spent: $('#time_spent').val(),
            start_time: $('#start_time').val(),
            notes: $('#notes').val()
        })
        .done(function(response) {
            if (response.success) {
                $('#add-task-form')[0].reset();
                location.reload();
            }
        })
        .fail(function(xhr) {
            const error = xhr.responseJSON?.error || 'Failed to add task';
            alert(error);
        });
    });

    // Add Comment
    $('#add-comment-form').submit(function(e) {
        e.preventDefault();
        
        $.post('{{ route("time-tracking.add-comment") }}', {
            _token: '{{ csrf_token() }}',
            comment: $('#daily_comment').val()
        })
        .done(function(response) {
            if (response.success) {
                alert('Comment saved successfully!');
            }
        })
        .fail(function(xhr) {
            const error = xhr.responseJSON?.error || 'Failed to save comment';
            alert(error);
        });
    });
});
</script>
@endpush
@endsection
