@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="bi bi-calculator me-2"></i>Tax Bracket Details
                        </h4>
                        <div class="d-flex gap-2">
                            @can('update', $taxBracket)
                            <a href="{{ route('tax-brackets.edit', $taxBracket) }}" class="btn btn-outline-primary">
                                <i class="bi bi-pencil me-1"></i>Edit
                            </a>
                            @endcan
                            <a href="{{ route('tax-brackets.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-1"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Basic Information</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ $taxBracket->name ?: 'Bracket ' . $taxBracket->order }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Order:</strong></td>
                                    <td><span class="badge bg-primary">{{ $taxBracket->order }}</span></td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @if($taxBracket->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-secondary">Inactive</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-md-6">
                            <h6 class="text-muted">Tax Configuration</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Minimum Amount:</strong></td>
                                    <td><span class="text-success">K{{ $taxBracket->formatted_min_amount }}</span></td>
                                </tr>
                                <tr>
                                    <td><strong>Maximum Amount:</strong></td>
                                    <td>
                                        @if($taxBracket->max_amount)
                                            <span class="text-danger">K{{ $taxBracket->formatted_max_amount }}</span>
                                        @else
                                            <span class="text-muted">Unlimited</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Tax Rate:</strong></td>
                                    <td><span class="badge bg-info fs-6">{{ $taxBracket->formatted_rate }}</span></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($taxBracket->description)
                    <div class="mt-4">
                        <h6 class="text-muted">Description</h6>
                        <p class="text-muted">{{ $taxBracket->description }}</p>
                    </div>
                    @endif

                    <!-- Tax Calculation Examples -->
                    <div class="mt-4">
                        <h6 class="text-muted">Tax Calculation Examples</h6>
                        <div class="row">
                            @php
                                $examples = [50000, 150000, 500000, 1000000, 3000000];
                            @endphp
                            @foreach($examples as $example)
                            @php
                                $breakdown = App\Models\TaxBracket::getTaxBreakdown($example);
                                $bracketTax = 0;
                                foreach($breakdown['breakdown'] as $item) {
                                    if($item['bracket']->id === $taxBracket->id) {
                                        $bracketTax = $item['tax_amount'];
                                        break;
                                    }
                                }
                            @endphp
                            <div class="col-md-4 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">K{{ number_format($example) }}</h6>
                                        <p class="card-text">
                                            <small class="text-muted">Tax from this bracket:</small><br>
                                            <strong class="text-primary">K{{ number_format($bracketTax, 2) }}</strong>
                                        </p>
                                        <small class="text-muted">
                                            Total Tax: K{{ number_format($breakdown['total_tax'], 2) }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Interactive Calculator -->
                    <div class="mt-4">
                        <h6 class="text-muted">Tax Calculator</h6>
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="calc_amount" class="form-label">Gross Salary (K)</label>
                                        <input type="number" id="calc_amount" class="form-control" 
                                               placeholder="Enter amount" min="0" step="0.01">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Tax Breakdown</label>
                                        <div id="calc_result" class="form-control-plaintext">
                                            <span class="text-muted">Enter amount to see calculation</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Audit Information -->
                    <div class="mt-4">
                        <h6 class="text-muted">Audit Information</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <strong>Created:</strong> {{ $taxBracket->created_at->format('M d, Y H:i') }}<br>
                                    @if($taxBracket->createdBy)
                                        <strong>Created by:</strong> {{ $taxBracket->createdBy->name }}
                                    @endif
                                </small>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <strong>Updated:</strong> {{ $taxBracket->updated_at->format('M d, Y H:i') }}<br>
                                    @if($taxBracket->updatedBy)
                                        <strong>Updated by:</strong> {{ $taxBracket->updatedBy->name }}
                                    @endif
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const calcAmount = document.getElementById('calc_amount');
    const calcResult = document.getElementById('calc_result');
    
    function updateCalculation() {
        const amount = parseFloat(calcAmount.value);
        if (!amount || amount <= 0) {
            calcResult.innerHTML = '<span class="text-muted">Enter amount to see calculation</span>';
            return;
        }
        
        // Make AJAX call to get tax breakdown
        fetch(`{{ route('tax-brackets.calculate-tax') }}?amount=${amount}`)
            .then(response => response.json())
            .then(data => {
                let html = '<div class="small">';
                
                // Find this bracket's contribution
                const thisBracket = data.breakdown.find(item => item.bracket.id === {{ $taxBracket->id }});
                
                if (thisBracket && thisBracket.tax_amount > 0) {
                    html += `
                        <div class="d-flex justify-content-between">
                            <span>This bracket tax:</span>
                            <strong class="text-primary">K${thisBracket.tax_amount.toLocaleString('en-US', {minimumFractionDigits: 2})}</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Taxable amount:</span>
                            <span>K${thisBracket.taxable_amount.toLocaleString('en-US', {minimumFractionDigits: 2})}</span>
                        </div>
                        <hr class="my-1">
                    `;
                } else {
                    html += '<div class="text-muted">No tax from this bracket</div><hr class="my-1">';
                }
                
                html += `
                    <div class="d-flex justify-content-between">
                        <span>Total tax:</span>
                        <strong>K${data.total_tax.toLocaleString('en-US', {minimumFractionDigits: 2})}</strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Net amount:</span>
                        <strong class="text-success">K${data.net_amount.toLocaleString('en-US', {minimumFractionDigits: 2})}</strong>
                    </div>
                </div>`;
                
                calcResult.innerHTML = html;
            })
            .catch(error => {
                calcResult.innerHTML = '<span class="text-danger">Error calculating tax</span>';
            });
    }
    
    calcAmount.addEventListener('input', updateCalculation);
});
</script>
@endpush
@endsection
