@extends('layouts.app')

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">person</x-slot>
        <x-slot name="title">{{ $customer->name }}</x-slot>
        <x-slot name="subtitle">Customer receivables and outstanding invoices</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('receivables.index') }}">Receivables</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ $customer->name }}</li>
        </x-slot>
        <x-slot name="controls">
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-primary btn-sm"
                        onclick="openPaymentModal({
                            paymentType: 'receivable',
                            entityId: '{{ $customer->id }}'
                        })">
                    <i class="bi bi-credit-card me-1"></i>Record Payment
                </button>
                <a href="{{ route('receivables.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Receivables
                </a>
            </div>
        </x-slot>
    </x-page-header>

    <div class="row">
        <!-- Customer Information -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-person me-2"></i>Customer Information
                    </h4>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-4">
                        <div class="avatar avatar-lg avatar-circle me-3">
                            <span class="avatar-initials bg-primary text-white h4 mb-0">
                                {{ substr($customer->name, 0, 1) }}
                            </span>
                        </div>
                        <div>
                            <h5 class="mb-1">{{ $customer->name }}</h5>
                            @if($customer->company)
                                <p class="text-muted mb-0">{{ $customer->company }}</p>
                            @endif
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            @if($customer->email)
                            <div class="mb-3">
                                <label class="form-label text-muted">Email</label>
                                <div class="fw-semibold">{{ $customer->email }}</div>
                            </div>
                            @endif

                            @if($customer->phone)
                            <div class="mb-3">
                                <label class="form-label text-muted">Phone</label>
                                <div class="fw-semibold">{{ $customer->phone }}</div>
                            </div>
                            @endif

                            @if($customer->address)
                            <div class="mb-3">
                                <label class="form-label text-muted">Address</label>
                                <div class="fw-semibold">{{ $customer->address }}</div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Outstanding Summary -->
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-graph-up me-2"></i>Outstanding Summary
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <div class="h4 text-danger mb-1">{{ _money($customerTotals['total_outstanding']) }}</div>
                                <div class="text-muted small">Outstanding</div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <div class="h6 mb-1">{{ $customerTotals['invoice_count'] }}</div>
                                <div class="text-muted small">Invoices</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-3">
                                <div class="h6 mb-1">{{ _money($customerTotals['total_invoiced']) }}</div>
                                <div class="text-muted small">Total Invoiced</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-3">
                                <div class="h6 mb-1 text-success">{{ _money($customerTotals['total_paid']) }}</div>
                                <div class="text-muted small">Total Paid</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Outstanding Invoices -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="row justify-content-between align-items-center flex-grow-1">
                        <div class="col-md">
                            <h4 class="card-header-title">
                                <i class="bi bi-receipt me-2"></i>Outstanding Invoices
                            </h4>
                        </div>
                        <div class="col-auto">
                            <span class="text-muted">{{ $customer->invoices->count() }} outstanding invoice(s)</span>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                            <thead class="thead-light">
                                <tr>
                                    <th style="width: 15%">Invoice #</th>
                                    <th style="width: 12%">Date</th>
                                    <th style="width: 12%">Due Date</th>
                                    <th style="width: 15%">Total</th>
                                    <th style="width: 15%">Paid</th>
                                    <th style="width: 15%">Balance</th>
                                    <th style="width: 10%">Status</th>
                                    <th style="width: 10%" class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($customer->invoices as $invoice)
                                <tr>
                                    <!-- Invoice Number -->
                                    <td>
                                        <span class="fw-semibold">#{{ $invoice->id }}</span>
                                        @if($invoice->invoice_number)
                                            <small class="text-muted d-block">{{ $invoice->invoice_number }}</small>
                                        @endif
                                    </td>
                                    
                                    <!-- Date -->
                                    <td>
                                        <span class="fw-semibold">{{ $invoice->created_at->format('M d, Y') }}</span>
                                    </td>
                                    
                                    <!-- Due Date -->
                                    <td>
                                        @if(isset($invoice->due_date) && $invoice->due_date)
                                            <span class="fw-semibold {{ $invoice->due_date->isPast() ? 'text-danger' : '' }}">
                                                {{ $invoice->due_date->format('M d, Y') }}
                                            </span>
                                            @if($invoice->due_date->isPast())
                                                <small class="text-danger d-block">{{ $invoice->due_date->diffForHumans() }}</small>
                                            @endif
                                        @else
                                            <span class="text-muted">Not set</span>
                                        @endif
                                    </td>
                                    
                                    <!-- Total -->
                                    <td>
                                        <span class="fw-semibold">{{ _money($invoice->amount_total) }}</span>
                                    </td>
                                    
                                    <!-- Paid -->
                                    <td>
                                        <span class="fw-semibold text-success">{{ _money($invoice->amount_paid) }}</span>
                                    </td>
                                    
                                    <!-- Balance -->
                                    <td>
                                        <span class="fw-semibold text-danger">{{ _money($invoice->balance) }}</span>
                                    </td>
                                    
                                    <!-- Status -->
                                    <td>
                                        @if($invoice->balance <= 0)
                                            <span class="badge bg-success">Paid</span>
                                        @elseif(isset($invoice->due_date) && $invoice->due_date && $invoice->due_date->isPast())
                                            <span class="badge bg-danger">Overdue</span>
                                        @elseif($invoice->created_at->addDays(30)->isPast())
                                            <span class="badge bg-warning">Aging</span>
                                        @elseif($invoice->amount_paid > 0)
                                            <span class="badge bg-info">Partial</span>
                                        @else
                                            <span class="badge bg-secondary">Outstanding</span>
                                        @endif
                                    </td>
                                    
                                    <!-- Actions -->
                                    <td class="text-center">
                                        <div class="d-flex gap-1 justify-content-center">
                                            <a href="{{ route('invoices.show', $invoice) }}"
                                               class="btn btn-outline-primary btn-sm"
                                               title="View Invoice">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <button type="button"
                                                    class="btn btn-outline-success btn-sm"
                                                    title="Record Payment"
                                                    onclick="openPaymentModal({
                                                        paymentType: 'receivable',
                                                        entityId: '{{ $customer->id }}',
                                                        invoiceId: '{{ $invoice->id }}'
                                                    })">
                                                <i class="bi bi-credit-card"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center p-5">
                                        <x-empty-state
                                            icon="receipt"
                                            title="No outstanding invoices"
                                            description="This customer has no outstanding invoices."
                                        />
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Recent Payments -->
            @if($customer->invoices->flatMap->payments->count() > 0)
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-credit-card me-2"></i>Recent Payments
                    </h4>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                            <thead class="thead-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Invoice</th>
                                    <th>Amount</th>
                                    <th>Reference</th>
                                    <th>Method</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($customer->invoices->flatMap->payments->sortByDesc('created_at')->take(5) as $payment)
                                <tr>
                                    <td>{{ $payment->created_at->format('M d, Y') }}</td>
                                    <td>#{{ $payment->paymentable_id }}</td>
                                    <td><span class="fw-semibold text-success">{{ _money($payment->amount) }}</span></td>
                                    <td>{{ $payment->reference_no ?? '-' }}</td>
                                    <td>{{ $payment->payment_method ?? '-' }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Include Payment Modal -->
<x-payment-modal />
@endsection
