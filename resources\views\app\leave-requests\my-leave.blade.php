@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="title">Leave List</x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Leave::class)
            <a href="{{ route('leaves.create') }}" class="btn btn-info btn-sm">
                @lang('crud.common.create') Leave
            </a>
            @endcan
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->
    <div class="card card-table">
        <div class="table-responsive mt-3">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-left">
                            Name
                        </th>
                        <th class="text-left">
                            Date from
                        </th>
                        <th class="text-left">
                            Date to
                        </th>
                        <th class="text-left">
                            Days
                        </th>
                        <th class="text-left">
                            Balance
                        </th>
                        <th class="text-left">
                            @lang('crud.common.actions')
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($leaveRequests as $leave)
                    <tr>

                        <td>{{ $leave->name ?? '-' }}</td>
                        <td>{{ $leave->date_from ?? '-' }}</td>
                        <td>{{ $leave->date_to ?? '-' }}</td>
                        <td>{{ $leave->days ?? '-' }}</td>
                        <td>{{ $leave->balance ?? '-' }}</td>
                        <td class="text-center" style="width: 134px;">
                            <div
                                role="group"
                                aria-label="Row Actions"
                                class="btn-group"
                            >
                                @can('update', $leave)
                                <a
                                    href="{{ route('leaves.edit', $leave) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        <!-- <i class="icon ion-md-create"></i> --> edit
                                    </button>
                                </a>
                                @endcan @can('view', $leave)
                                <a
                                    href="{{ route('leaves.show', $leave) }}"
                                >
                                    <button
                                        type="button"
                                        class="btn btn-light m-1"
                                    >
                                        <!-- <i class="icon ion-md-eye"></i> --> view
                                    </button>
                                </a>
                                @endcan @can('delete', $leave)
                                <form
                                    action="{{ route('leaves.destroy', $leave) }}"
                                    method="POST"
                                    onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                >
                                    @csrf @method('DELETE')
                                    <button
                                        type="submit"
                                        class="btn btn-light text-danger m-1"
                                    >
                                        <!-- <i class="icon ion-md-trash"></i> --> del
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @empty
                   
                    @endforelse
                </tbody>
                
            </table>
        </div>
    </div>
    

</div>
@endsection

@push('scripts')
<script>

    $(document).on('ready', function () {
      // INITIALIZATION OF DATERANGEPICKER

      dataTableBtn()  
    });
</script>

@endpush
