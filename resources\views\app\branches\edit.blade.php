@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">pencil-square</x-slot>
        <x-slot name="title">Edit Branch: {{ $branch->name }}</x-slot>
        <x-slot name="subtitle">Update branch information</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('branches.index') }}">Branches</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('branches.show', $branch) }}" class="btn btn-outline-info me-2">
                <i class="bi bi-eye me-1"></i> View Branch
            </a>
            <a href="{{ route('branches.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Branches
            </a>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Form Card -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-header-title">Branch Information</h5>
                <span class="badge bg-primary">ID: {{ $branch->id }}</span>
            </div>
        </div>
        <div class="card-body">
            <x-form
                method="PUT"
                action="{{ route('branches.update', $branch) }}"
                class="mt-2"
            >
                @include('app.branches.form-inputs')

                <div class="d-flex justify-content-between mt-4 border-top pt-4">
                    <div>
                        <a href="{{ route('branches.index') }}" class="btn btn-outline-secondary me-2">
                            <i class="bi bi-arrow-left me-1"></i> Back
                        </a>

                        <a href="{{ route('branches.create') }}" class="btn btn-outline-primary">
                            <i class="bi bi-plus-circle me-1"></i> New Branch
                        </a>
                    </div>

                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-1"></i> Save Changes
                    </button>
                </div>
            </x-form>
        </div>
    </div>
</div>
@endsection
