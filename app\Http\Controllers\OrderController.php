<?php

namespace App\Http\Controllers;

use App\Models\Unit;
use App\Models\User;
use App\Models\Supplier;
use App\Models\Order;
use App\Models\Sale;
use App\Models\Product;
use Illuminate\Http\Request;
use App\Http\Requests\OrderStoreRequest;
use App\Http\Requests\OrderUpdateRequest;
use App\Models\Location;

use Facades\App\Libraries\ProductHandler;
use Facades\App\Libraries\InvoiceHandler;
use Facades\App\Cache\Repo;

class OrderController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Order::class);

        $orders = Repo::getOrderCursor()->whereIn('status_id', [10])->get();

        return view('app.orders.index', compact('orders'));
    }    

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function canceledOrders(Request $request)
    {
        $this->authorize('view-any', Order::class);

        $orders = Repo::getOrderCursor()->whereIn('status_id', [13])->get();

        return view('app.orders.canceled-orders', compact('orders'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function stockAdjustments(Request $request)
    {
        $this->authorize('view-any', Order::class);

        $orders = Repo::getOrderCursor()->whereIn('status_id', [14])->get();

        return view('app.orders.stock-adjustments', compact('orders'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Order::class);

        $users = User::pluck('name', 'id');
        $suppliers = Supplier::pluck('name', 'id');
        $products = Product::with("unit", "units")->get();
        $locations = Location::get();

        return view('app.orders.create', compact('users','suppliers', 'products', 'locations'));
    }

    /**
     * @param \App\Http\Requests\OrderStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(OrderStoreRequest $request)
    {
        $this->authorize('create', Order::class);

        $validated = $request->validated();

        if( ! auth()->user()->branch_id ) return redirect()->back()->with("error", "Please Select Branch first");

        // Build order data from individual form fields
        $data = [
            'supplier_id' => $request->supplier_id,
            'description' => $request->description,
            'sub_total' => $request->sub_total ?? 0,
            'vat' => $request->vat ?? 0,
            'discount' => $request->discount ?? 0,
            'amount_total' => $request->amount_total ?? 0,
            'amount_paid' => $request->amount_paid ?? 0,
            'created_by' => auth()->id(),
            'business_type_id' => auth()->user()->business_type_id,
            'branch_id' => auth()->user()->branch_id,
        ];

        // Handle supplier creation if needed
        if( $request->supplier_id ){
            $supplier = Supplier::find( $request->supplier_id );
            if (!$supplier) {
                $supplier = Supplier::create( ["name" => $request->supplier_id] );
            }
            $data["supplier_id"] = $supplier->id;
            $data["supplier_name"] = $supplier->name;
        }

        $order = Order::create( $data );

        // Handle order items
        $items = $request->items ?? [];

        foreach ($items as $item) {

            $product = Product::find($item['product_id']);
            if( $product ) {
                $saleData = [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'description' => $product->description,
                    'unit_id' => $item['unit_id'] ?? null,
                    'supplier_id' => $item['supplier_id'] ?? null,
                    'location' => $item['location'] ?? null,
                    'quantity' => $item['quantity'] ?? 1,
                    'buying_price' => $item['buying_price'] ?? 0,
                    'type' => "STOCK_PURCHASE",
                    'discount' => $item['discount'] ?? 0,
                    'expires_at' => $item['expires_at'] ?? null,
                    'created_by' => auth()->id(),
                    'business_type_id' => auth()->user()->business_type_id,
                ];

                // Add unit information
                if (!empty($item['unit_id'])) {
                    $unit = Unit::find($item['unit_id']);
                    if( $unit ) {
                        $saleData['unit_name'] = $unit->name;
                        $saleData['unit_quantity'] = $unit->quantity;
                        $saleData['balance'] = $item['quantity'] * $unit->quantity;
                    }
                }

                $order->stocks()->create($saleData);
            }
        }

        return redirect()
            ->route('orders.show', $order)
            ->with('success', 'Order created successfully!');
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Order $order
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Order $order)
    {
        $this->authorize('view', $order);
        return view('app.orders.show', compact('order'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Order $order
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Order $order)
    {
        $this->authorize('update', $order);

        $suppliers = Supplier::pluck('name', 'id');
        $products = Product::with("unit", "units")->get();
        $order->stocks;
        $locations = Location::get();

        return view('app.orders.edit', compact('order', 'suppliers', 'products' , 'locations'));
    }

    /**
     * @param \App\Http\Requests\OrderUpdateRequest $request
     * @param \App\Models\Order $order
     * @return \Illuminate\Http\Response
     */
    public function update(OrderUpdateRequest $request, Order $order)
    {
        $this->authorize('update', $order);

        $validated = $request->validated();

        if( ! auth()->user()->branch_id ) return redirect()->back()->with("error", "Please Select Branch first");

        // Build order data from individual form fields
        $data = [
            'supplier_id' => $request->supplier_id,
            'description' => $request->description,
            'sub_total' => $request->sub_total ?? 0,
            'vat' => $request->vat ?? 0,
            'discount' => $request->discount ?? 0,
            'amount_total' => $request->amount_total ?? 0,
            'amount_paid' => $request->amount_paid ?? 0,
            'updated_by' => auth()->id(),
        ];

        // Handle supplier creation if needed
        if( $request->supplier_id ){
            $supplier = Supplier::find( $request->supplier_id );
            if (!$supplier) {
                $supplier = Supplier::create( ["name" => $request->supplier_id] );
            }
            $data["supplier_id"] = $supplier->id;
            $data["supplier_name"] = $supplier->name;
        }

        $order->update($data);
        $order->payments()->delete();
        InvoiceHandler::addPayment($order, [
            "amount" => $order->amount_paid,
            "balance" => $order->amount_total - $order->amount_paid
        ]);

        // Handle order items - delete existing and recreate
        $order->stocks()->delete();
        $items = $request->items ?? [];

        if( count( $items ) ) $order->update(["approved_by" => null]);

        foreach ($items as $item) {
            if( !empty($item['product_id']) ) {
                $saleData = [
                    'product_id' => $item['product_id'],
                    'unit_id' => $item['unit_id'] ?? null,
                    'supplier_id' => $item['supplier_id'] ?? null,
                    'location' => $item['location'] ?? null,
                    'quantity' => $item['quantity'] ?? 1,
                    'buying_price' => $item['buying_price'] ?? 0,
                    'discount' => $item['discount'] ?? 0,
                    'expires_at' => $item['expires_at'] ?? null,
                    'updated_by' => auth()->id(),
                    'business_type_id' => auth()->user()->business_type_id,
                ];

                // Add unit information
                if (!empty($item['unit_id'])) {
                    $unit = Unit::find($item['unit_id']);
                    if( $unit ) {
                        $saleData['unit_name'] = $unit->name;
                        $saleData['unit_quantity'] = $unit->quantity;
                        $saleData['balance'] = $item['quantity'] * $unit->quantity;
                    }
                }

                $order->stocks()->create($saleData);
            }
        }

        return redirect()
            ->route('orders.show', $order)
            ->with('success', 'Order updated successfully!');
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Order $order
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Order $order)
    {
        $this->authorize('delete', $order);

        $order->stocks()->delete();
        $order->delete();

        return redirect()
            ->route('orders.index')
            ->withSuccess(__('crud.common.removed'));
    }

    public function approve(Request $request, Order $order){
        if( $request->status_id == 13) {
            $order->update(['status_id' => $request->status_id ]);
        } else {
            ProductHandler::approveOrder($order);
        }
        return redirect()->back()->withSuccess(__('crud.common.approved'));
    }

    public function reject(Request $request, Order $order){
        $this->authorize('update', $order);

        $request->validate([
            'rejection_reason' => 'required|string|max:500'
        ]);

        // Update order with rejection info
        $order->update([
            'description' => $order->description . ' | REJECTED: ' . $request->rejection_reason,
            'status_id' => 14, // Assuming 14 is cancelled/rejected status
            'updated_by' => auth()->id(),
        ]);

        return redirect()->back()->withSuccess('Order rejected successfully.');
    }

    public function ajaxOrder(Request $request, Order $order){
        $order->payments;
        $order->stocks;
        $order->supplier;
        return response( $order );
    }

    public function importOrder(Request $request){

        \Excel::import(new \App\Imports\ImportOrder, $request->products);

        $orders = Order::where("sub_total", 0)->get();

        foreach($orders as $order){

            if( $order ) {

                $sub_total = $order->stocks()->get()->sum("buying_amount");
                $discount = $order->stocks()->get()->sum("discount");
                $amount_total = $sub_total - $discount;
                $data = [];
                $data["sub_total"] = $sub_total;    
                $data["amount_total"] = $amount_total;    
                $data["amount_paid"] = $amount_total;    
                $data["discount"] = $discount;   

                $order->update($data);
                $order->payments()->delete();
                InvoiceHandler::addPayment($order, [
                    "amount" => $order->amount_paid,
                    "balance" => $order->amount_total - $order->amount_paid
                ]);            
            }

        }

        return redirect()
            ->back()
            ->withSuccess("Products Imported Successfully");    
    }


    public function stockAdjustmentSave(OrderStoreRequest $request) {
        $this->authorize('create', Order::class);
        ProductHandler::stockAdjustment();
        return redirect()->back()->withSuccess("Stock Adjusted Successfully.");
    }

    public function stockAdjustment(Request $request) {
        $this->authorize('create', Order::class);

        $users = User::pluck('name', 'id');
        $suppliers = Supplier::pluck('name', 'id');
        $products = Product::with("unit", "units")->get();

        return view(
            'app.orders.stock-adjustment',
            compact('users','suppliers', 'products')
        );
    }





    /**
     * Show create adjustment form
     */
    public function createAdjustment(Request $request, Order $originalOrder = null)
    {
        $this->authorize('create', Order::class);

        $users = User::pluck('name', 'id');
        $suppliers = Supplier::pluck('name', 'id');
        $products = Product::with("unit", "units")->get();
        $locations = Location::get();

        // If original order is provided, load its items for adjustment
        $originalItems = [];
        if ($originalOrder) {
            $originalOrder->load('stocks.product', 'stocks.unit');
            $originalItems = $originalOrder->stocks->map(function($stock) {
                return [
                    'product_id' => $stock->product_id,
                    'unit_id' => $stock->unit_id,
                    'supplier_id' => $stock->supplier_id,
                    'location' => $stock->location,
                    'quantity' => $stock->quantity,
                    'buying_price' => $stock->buying_price,
                    'discount' => $stock->discount ?? 0,
                    'expires_at' => $stock->expires_at ? $stock->expires_at->format('Y-m-d') : '',
                    'total' => ($stock->quantity * $stock->buying_price) - ($stock->discount ?? 0)
                ];
            })->toArray();
        }

        return view('app.orders.create-adjustment', compact(
            'users', 'suppliers', 'products', 'locations', 'originalOrder', 'originalItems'
        ));
    }

    /**
     * Store adjustment order
     */
    public function storeAdjustment(OrderStoreRequest $request)
    {
        $this->authorize('create', Order::class);

        if (!auth()->user()->branch_id) {
            return redirect()->back()->with("error", "Please Select Branch first");
        }

        // Build adjustment order data
        $data = [
            'supplier_id' => $request->supplier_id,
            'description' => $request->description,
            'sub_total' => -abs($request->sub_total ?? 0), // Make negative
            'vat' => -abs($request->vat ?? 0), // Make negative
            'discount' => -abs($request->discount ?? 0), // Make negative
            'amount_total' => -abs($request->amount_total ?? 0), // Make negative
            'amount_paid' => -abs($request->amount_paid ?? 0), // Make negative
            'created_by' => auth()->id(),
            'business_type_id' => auth()->user()->business_type_id,
            'branch_id' => auth()->user()->branch_id,
            'is_adjustment' => true,
            'original_order_id' => $request->original_order_id,
            'adjustment_type' => $request->adjustment_type ?? 'return',
            'adjustment_reason' => $request->adjustment_reason,
        ];

        // Handle supplier
        if ($request->supplier_id) {
            $supplier = Supplier::find($request->supplier_id);
            if (!$supplier) {
                $supplier = Supplier::create(["name" => $request->supplier_id]);
            }
            $data["supplier_id"] = $supplier->id;
            $data["supplier_name"] = $supplier->name;
        }

        $adjustmentOrder = Order::create($data);

        // Handle adjustment items with negative quantities
        $items = $request->items ?? [];

        foreach ($items as $item) {
            if (!empty($item['product_id'])) {
                $saleData = [
                    'product_id' => $item['product_id'],
                    'unit_id' => $item['unit_id'] ?? null,
                    'supplier_id' => $item['supplier_id'] ?? null,
                    'location' => $item['location'] ?? null,
                    'quantity' => -abs($item['quantity'] ?? 1), // Make negative
                    'buying_price' => $item['buying_price'] ?? 0,
                    'discount' => $item['discount'] ?? 0,
                    'expires_at' => $item['expires_at'] ?? null,
                    'created_by' => auth()->id(),
                    'business_type_id' => auth()->user()->business_type_id,
                ];

                // Add unit information
                if (!empty($item['unit_id'])) {
                    $unit = Unit::find($item['unit_id']);
                    if ($unit) {
                        $saleData['unit_name'] = $unit->name;
                        $saleData['unit_quantity'] = $unit->quantity;
                    }
                }

                $adjustmentOrder->stocks()->create($saleData);
            }
        }

        return redirect()
            ->route('orders.show', $adjustmentOrder)
            ->with('success', 'Adjustment order created successfully!');
    }

    /**
     * Show adjustments index
     */
    public function adjustments(Request $request)
    {
        $this->authorize('view-any', Order::class);

        $adjustments = Order::where('is_adjustment', true)
                           ->with(['originalOrder', 'supplier', 'createdBy'])
                           ->orderBy('created_at', 'desc')
                           ->get();

        return view('app.orders.adjustments', compact('adjustments'));
    }

    /**
     * Download order as PDF
     *
     * @param \App\Models\Order $order
     * @return \Illuminate\Http\Response
     */
    public function downloadPdf(Order $order)
    {
        $this->authorize('view', $order);

        $order->load(['stocks.product', 'stocks.unit', 'supplier', 'createdBy', 'approvedBy', 'payments']);

        $pdf = \PDF::loadView('app.orders.pdf', compact('order'));

        return $pdf->download('purchase_order_' . $order->order_id . '.pdf');
    }

}
