<?php
require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

try {
    // Create admin user
    $admin = User::updateOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => 'Admin User',
            'password' => Hash::make('password'),
            'status_id' => 1,
        ]
    );
    
    echo "Created admin user\n";
    
    // Create roles
    $roles = [
        'super-admin',
        'admin',
        'accountant',
        'finance-manager',
        'asset-manager',
        'tax-manager',
        'budget-manager',
        'production-manager',
        'sales',
        'warehouse-manager',
    ];
    
    foreach ($roles as $roleName) {
        Role::firstOrCreate(['name' => $roleName]);
        echo "Created role: {$roleName}\n";
    }
    
    // Create accounting permissions
    $permissions = [
        // Chart of Accounts
        'view account-types',
        'create account-types',
        'update account-types',
        'delete account-types',
        'view account-categories',
        'create account-categories',
        'update account-categories',
        'delete account-categories',
        'view accounts',
        'create accounts',
        'update accounts',
        'delete accounts',
        
        // Journal Entries
        'view journal-entries',
        'create journal-entries',
        'update journal-entries',
        'delete journal-entries',
        'post journal-entries',
        'approve journal-entries',
        
        // Fiscal Years
        'view fiscal-years',
        'create fiscal-years',
        'update fiscal-years',
        'delete fiscal-years',
        'close fiscal-years',
        
        // Assets
        'view assets',
        'create assets',
        'update assets',
        'delete assets',
        'depreciate assets',
        'dispose assets',
        
        // Bank Accounts
        'view bank-accounts',
        'create bank-accounts',
        'update bank-accounts',
        'delete bank-accounts',
        'reconcile bank-accounts',
        
        // Financial Reports
        'view balance-sheet',
        'view income-statement',
        'view cash-flow-statement',
        'view trial-balance',
        'view general-ledger',
        
        // Tax Management
        'view tax-types',
        'create tax-types',
        'update tax-types',
        'delete tax-types',
        'view tax-transactions',
        'create tax-transactions',
        
        // Budgeting
        'view budgets',
        'create budgets',
        'update budgets',
        'delete budgets',
        
        // Manufacturing
        'view boms',
        'create boms',
        'update boms',
        'delete boms',
        'view production-orders',
        'create production-orders',
        'update production-orders',
        'delete production-orders',
    ];
    
    foreach ($permissions as $permissionName) {
        Permission::firstOrCreate(['name' => $permissionName]);
        echo "Created permission: {$permissionName}\n";
    }
    
    // Assign all permissions to super-admin
    $superAdminRole = Role::findByName('super-admin');
    $superAdminRole->syncPermissions(Permission::all());
    echo "Assigned all permissions to super-admin role\n";
    
    // Assign super-admin role to admin user
    $admin->assignRole('super-admin');
    echo "Assigned super-admin role to admin user\n";
    
    // Assign specific permissions to accountant role
    $accountantRole = Role::findByName('accountant');
    $accountantPermissions = [
        'view account-types',
        'view account-categories',
        'view accounts',
        'create accounts',
        'update accounts',
        'view journal-entries',
        'create journal-entries',
        'update journal-entries',
        'view fiscal-years',
        'view bank-accounts',
        'view balance-sheet',
        'view income-statement',
        'view cash-flow-statement',
        'view trial-balance',
        'view general-ledger',
        'view tax-types',
        'view tax-transactions',
        'create tax-transactions',
    ];
    $accountantRole->syncPermissions($accountantPermissions);
    echo "Assigned permissions to accountant role\n";
    
    echo "Setup complete!\n";
    
} catch (\Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
