<?php

namespace App\Http\Controllers;

use App\Models\MonthlyPayroll;
use App\Models\Employee;
use Illuminate\Http\Request;
use PDF;

class PayrollReportController extends Controller
{
    /**
     * Display the payroll journal report form.
     *
     * @return \Illuminate\Http\Response
     */
    public function journalReportForm()
    {
        $this->authorize('view-any', MonthlyPayroll::class);

        $months = [
            '01' => 'January',
            '02' => 'February',
            '03' => 'March',
            '04' => 'April',
            '05' => 'May',
            '06' => 'June',
            '07' => 'July',
            '08' => 'August',
            '09' => 'September',
            '10' => 'October',
            '11' => 'November',
            '12' => 'December',
        ];

        $years = range(date('Y') - 5, date('Y') + 5);
        $years = array_combine($years, $years);

        return view('app.payroll.reports.journal_form', compact('months', 'years'));
    }

    /**
     * Generate the payroll journal report.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function journalReport(Request $request)
    {
        $this->authorize('view-any', MonthlyPayroll::class);

        $validated = $request->validate([
            'month' => 'required|string',
            'year' => 'required|string',
            'format' => 'required|in:html,pdf,csv',
        ]);

        $month = $validated['month'];
        $year = $validated['year'];

        $payrolls = MonthlyPayroll::whereYear('date_from', $year)
            ->whereMonth('date_from', $month)
            ->with('employee', 'payrollItems', 'contributions', 'deductions')
            ->get();

        $totalBasicPay = $payrolls->sum('basic_pay');
        $totalGrossPay = $payrolls->sum(function ($payroll) {
            return $payroll->gross;
        });
        $totalDeductions = $payrolls->sum(function ($payroll) {
            return $payroll->deductions->sum('amount') + $payroll->payee;
        });
        $totalNetPay = $payrolls->sum(function ($payroll) {
            return $payroll->net_pay;
        });

        $data = [
            'payrolls' => $payrolls,
            'totalBasicPay' => $totalBasicPay,
            'totalGrossPay' => $totalGrossPay,
            'totalDeductions' => $totalDeductions,
            'totalNetPay' => $totalNetPay,
            'month' => $month,
            'year' => $year,
            'monthName' => date('F', mktime(0, 0, 0, $month, 10)),
        ];

        if ($validated['format'] === 'html') {
            return view('app.payroll.reports.journal', $data);
        } elseif ($validated['format'] === 'pdf') {
            $pdf = PDF::loadView('app.payroll.reports.journal_pdf', $data);
            return $pdf->download('payroll_journal_' . $year . '_' . $month . '.pdf');
        } else {
            // CSV format
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="payroll_journal_' . $year . '_' . $month . '.csv"',
            ];

            $callback = function () use ($payrolls, $totalBasicPay, $totalGrossPay, $totalDeductions, $totalNetPay) {
                $file = fopen('php://output', 'w');
                
                // Add headers
                fputcsv($file, ['Employee', 'Basic Pay', 'Gross Pay', 'Deductions', 'Net Pay']);
                
                // Add data rows
                foreach ($payrolls as $payroll) {
                    fputcsv($file, [
                        $payroll->employee->name,
                        $payroll->basic_pay,
                        $payroll->gross,
                        $payroll->deductions->sum('amount') + $payroll->payee,
                        $payroll->net_pay,
                    ]);
                }
                
                // Add totals
                fputcsv($file, ['Total', $totalBasicPay, $totalGrossPay, $totalDeductions, $totalNetPay]);
                
                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        }
    }

    /**
     * Display the payroll liabilities report form.
     *
     * @return \Illuminate\Http\Response
     */
    public function liabilitiesReportForm()
    {
        $this->authorize('view-any', MonthlyPayroll::class);

        $months = [
            '01' => 'January',
            '02' => 'February',
            '03' => 'March',
            '04' => 'April',
            '05' => 'May',
            '06' => 'June',
            '07' => 'July',
            '08' => 'August',
            '09' => 'September',
            '10' => 'October',
            '11' => 'November',
            '12' => 'December',
        ];

        $years = range(date('Y') - 5, date('Y') + 5);
        $years = array_combine($years, $years);

        return view('app.payroll.reports.liabilities_form', compact('months', 'years'));
    }

    /**
     * Generate the payroll liabilities report.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function liabilitiesReport(Request $request)
    {
        $this->authorize('view-any', MonthlyPayroll::class);

        $validated = $request->validate([
            'month' => 'required|string',
            'year' => 'required|string',
            'format' => 'required|in:html,pdf,csv',
        ]);

        $month = $validated['month'];
        $year = $validated['year'];

        $payrolls = MonthlyPayroll::whereYear('date_from', $year)
            ->whereMonth('date_from', $month)
            ->with('employee', 'deductions')
            ->get();

        // Group deductions by type
        $liabilities = [];
        $totalLiabilities = 0;

        foreach ($payrolls as $payroll) {
            // Add PAYE tax
            if (!isset($liabilities['PAYE Tax'])) {
                $liabilities['PAYE Tax'] = 0;
            }
            $liabilities['PAYE Tax'] += $payroll->payee;
            $totalLiabilities += $payroll->payee;

            // Add other deductions
            foreach ($payroll->deductions as $deduction) {
                if (!isset($liabilities[$deduction->name])) {
                    $liabilities[$deduction->name] = 0;
                }
                $liabilities[$deduction->name] += $deduction->amount;
                $totalLiabilities += $deduction->amount;
            }
        }

        $data = [
            'liabilities' => $liabilities,
            'totalLiabilities' => $totalLiabilities,
            'month' => $month,
            'year' => $year,
            'monthName' => date('F', mktime(0, 0, 0, $month, 10)),
        ];

        if ($validated['format'] === 'html') {
            return view('app.payroll.reports.liabilities', $data);
        } elseif ($validated['format'] === 'pdf') {
            $pdf = PDF::loadView('app.payroll.reports.liabilities_pdf', $data);
            return $pdf->download('payroll_liabilities_' . $year . '_' . $month . '.pdf');
        } else {
            // CSV format
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="payroll_liabilities_' . $year . '_' . $month . '.csv"',
            ];

            $callback = function () use ($liabilities, $totalLiabilities) {
                $file = fopen('php://output', 'w');
                
                // Add headers
                fputcsv($file, ['Liability Type', 'Amount']);
                
                // Add data rows
                foreach ($liabilities as $name => $amount) {
                    fputcsv($file, [$name, $amount]);
                }
                
                // Add total
                fputcsv($file, ['Total', $totalLiabilities]);
                
                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        }
    }

    /**
     * Display the employee cost summary report form.
     *
     * @return \Illuminate\Http\Response
     */
    public function costSummaryForm()
    {
        $this->authorize('view-any', MonthlyPayroll::class);

        $months = [
            '01' => 'January',
            '02' => 'February',
            '03' => 'March',
            '04' => 'April',
            '05' => 'May',
            '06' => 'June',
            '07' => 'July',
            '08' => 'August',
            '09' => 'September',
            '10' => 'October',
            '11' => 'November',
            '12' => 'December',
        ];

        $years = range(date('Y') - 5, date('Y') + 5);
        $years = array_combine($years, $years);

        return view('app.payroll.reports.cost_summary_form', compact('months', 'years'));
    }

    /**
     * Generate the employee cost summary report.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function costSummary(Request $request)
    {
        $this->authorize('view-any', MonthlyPayroll::class);

        $validated = $request->validate([
            'month' => 'required|string',
            'year' => 'required|string',
            'format' => 'required|in:html,pdf,csv',
        ]);

        $month = $validated['month'];
        $year = $validated['year'];

        $payrolls = MonthlyPayroll::whereYear('date_from', $year)
            ->whereMonth('date_from', $month)
            ->with('employee', 'contributions')
            ->get();

        $employeeCosts = [];
        $totalCost = 0;

        foreach ($payrolls as $payroll) {
            $basicPay = $payroll->basic_pay;
            $contributions = $payroll->contributions->sum('amount');
            $totalEmployeeCost = $basicPay + $contributions;
            
            $employeeCosts[] = [
                'employee' => $payroll->employee,
                'basic_pay' => $basicPay,
                'contributions' => $contributions,
                'total_cost' => $totalEmployeeCost,
            ];
            
            $totalCost += $totalEmployeeCost;
        }

        $data = [
            'employeeCosts' => $employeeCosts,
            'totalCost' => $totalCost,
            'month' => $month,
            'year' => $year,
            'monthName' => date('F', mktime(0, 0, 0, $month, 10)),
        ];

        if ($validated['format'] === 'html') {
            return view('app.payroll.reports.cost_summary', $data);
        } elseif ($validated['format'] === 'pdf') {
            $pdf = PDF::loadView('app.payroll.reports.cost_summary_pdf', $data);
            return $pdf->download('employee_cost_summary_' . $year . '_' . $month . '.pdf');
        } else {
            // CSV format
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="employee_cost_summary_' . $year . '_' . $month . '.csv"',
            ];

            $callback = function () use ($employeeCosts, $totalCost) {
                $file = fopen('php://output', 'w');
                
                // Add headers
                fputcsv($file, ['Employee', 'Basic Pay', 'Contributions', 'Total Cost']);
                
                // Add data rows
                foreach ($employeeCosts as $cost) {
                    fputcsv($file, [
                        $cost['employee']->name,
                        $cost['basic_pay'],
                        $cost['contributions'],
                        $cost['total_cost'],
                    ]);
                }
                
                // Add total
                fputcsv($file, ['Total', '', '', $totalCost]);
                
                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        }
    }
}
