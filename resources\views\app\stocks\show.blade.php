@extends('layouts.app')

@push('styles')
<style>
    .content {
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
    }

    .content.loaded {
        opacity: 1;
    }

    .stock-status-badge {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 500;
    }

    .stock-info-card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.75rem;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .stock-info-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .info-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
    }

    .info-value {
        font-size: 1.125rem;
        font-weight: 500;
        color: #212529;
    }

    .stock-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .avatar-lg {
        width: 4rem;
        height: 4rem;
        font-size: 1.5rem;
    }
</style>
@endpush

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">box</x-slot>
        <x-slot name="title">Stock Details</x-slot>
        <x-slot name="subtitle">View detailed information about this stock item</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('stocks.index') }}">Stock Inventory</a></li>
            <li class="breadcrumb-item active" aria-current="page">Stock Details</li>
        </x-slot>
        <x-slot name="controls">
            <div class="btn-group">
                <a href="{{ route('stocks.index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Stock
                </a>
                @can('update', $stock)
                <a href="{{ route('stocks.edit', $stock) }}" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-pencil me-1"></i>Edit Stock
                </a>
                @endcan
                @can('create', App\Models\Stock::class)
                <a href="{{ route('stocks.create') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle me-1"></i>Add New Stock
                </a>
                @endcan
            </div>
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Stock Header Card -->
    <div class="stock-header">
        <div class="row align-items-center">
            <div class="col-auto">
                <div class="avatar avatar-lg avatar-circle bg-white text-primary">
                    <span class="avatar-initials">
                        @if($stock->quantity <= 0)
                            <i class="bi bi-x-circle"></i>
                        @elseif($stock->quantity <= 5)
                            <i class="bi bi-exclamation-triangle"></i>
                        @else
                            <i class="bi bi-check-circle"></i>
                        @endif
                    </span>
                </div>
            </div>
            <div class="col">
                <h2 class="mb-1">{{ $stock->product->name ?? 'Unknown Product' }}</h2>
                <p class="mb-0 opacity-75">
                    {{ $stock->product->description ?? 'No description available' }}
                </p>
            </div>
            <div class="col-auto">
                @if($stock->quantity <= 0)
                    <span class="badge bg-danger stock-status-badge">Out of Stock</span>
                @elseif($stock->quantity <= 5)
                    <span class="badge bg-warning stock-status-badge">Low Stock</span>
                @else
                    <span class="badge bg-success stock-status-badge">In Stock</span>
                @endif
            </div>
        </div>
    </div>

    <!-- Stock Information Cards -->
    <div class="row g-4">
        <!-- Quantity Information -->
        <div class="col-md-6 col-lg-4">
            <div class="card stock-info-card h-100">
                <div class="card-body text-center">
                    <div class="avatar avatar-circle bg-primary bg-soft-primary mb-3 mx-auto">
                        <span class="avatar-initials"><i class="bi bi-boxes"></i></span>
                    </div>
                    <div class="info-label">Current Quantity</div>
                    <div class="info-value">{{ number_format($stock->quantity) }}</div>
                    @if($stock->unit_name)
                        <small class="text-muted">{{ $stock->unit_name }}</small>
                    @endif
                </div>
            </div>
        </div>

        <!-- Balance Information -->
        <div class="col-md-6 col-lg-4">
            <div class="card stock-info-card h-100">
                <div class="card-body text-center">
                    <div class="avatar avatar-circle bg-success bg-soft-success mb-3 mx-auto">
                        <span class="avatar-initials"><i class="bi bi-graph-up"></i></span>
                    </div>
                    <div class="info-label">Available Balance</div>
                    <div class="info-value">{{ number_format($stock->balance ?? $stock->quantity) }}</div>
                    @if($stock->unit_name)
                        <small class="text-muted">{{ $stock->unit_name }}</small>
                    @endif
                </div>
            </div>
        </div>

        <!-- Value Information -->
        <div class="col-md-6 col-lg-4">
            <div class="card stock-info-card h-100">
                <div class="card-body text-center">
                    <div class="avatar avatar-circle bg-warning bg-soft-warning mb-3 mx-auto">
                        <span class="avatar-initials"><i class="bi bi-currency-dollar"></i></span>
                    </div>
                    <div class="info-label">Total Value</div>
                    <div class="info-value">{{ _money(($stock->quantity ?? 0) * ($stock->buying_price ?? 0)) }}</div>
                    <small class="text-muted">@ {{ _money($stock->buying_price ?? 0) }} each</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Information -->
    <div class="row g-4 mt-2">
        <!-- Product Details -->
        <div class="col-lg-6">
            <div class="card stock-info-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-box me-2"></i>Product Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="info-label">Product Name</div>
                            <div class="info-value">{{ $stock->product->name ?? '-' }}</div>
                        </div>
                        <div class="col-6">
                            <div class="info-label">Category</div>
                            <div class="info-value">{{ $stock->product->category->name ?? '-' }}</div>
                        </div>
                        <div class="col-6">
                            <div class="info-label">Unit</div>
                            <div class="info-value">{{ $stock->unit_name ?? '-' }}</div>
                        </div>
                        @if($stock->expires_at)
                        <div class="col-12">
                            <div class="info-label">Expiry Date</div>
                            <div class="info-value">{{ $stock->expires_at->format('M d, Y') }}</div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Details -->
        <div class="col-lg-6">
            <div class="card stock-info-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-building me-2"></i>Business Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="info-label">Supplier</div>
                            <div class="info-value">{{ $stock->supplier->name ?? '-' }}</div>
                        </div>
                        <div class="col-6">
                            <div class="info-label">Branch</div>
                            <div class="info-value">{{ $stock->branch->name ?? '-' }}</div>
                        </div>
                        <div class="col-6">
                            <div class="info-label">Location</div>
                            <div class="info-value">{{ $stock->location ?? '-' }}</div>
                        </div>
                        <div class="col-6">
                            <div class="info-label">Added On</div>
                            <div class="info-value">{{ $stock->created_at->format('M d, Y') }}</div>
                        </div>
                        <div class="col-6">
                            <div class="info-label">Added By</div>
                            <div class="info-value">{{ $stock->createdBy->name ?? '-' }}</div>
                        </div>
                        @if($stock->description)
                        <div class="col-12">
                            <div class="info-label">Description</div>
                            <div class="info-value">{{ $stock->description }}</div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Show content after page loads to prevent flash
    $('.content').addClass('loaded');
});
</script>
@endpush
