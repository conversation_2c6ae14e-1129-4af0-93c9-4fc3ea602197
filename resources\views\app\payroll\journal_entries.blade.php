@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                Create Journal Entries for Payroll
            </h4>
        </div>

        <div class="card-body">
            <form action="{{ route('payroll.journal-entries') }}" method="POST">
                @csrf

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="month">Month</label>
                            <select name="month" id="month" class="form-control" required>
                                @foreach($months as $key => $value)
                                    <option value="{{ $key }}" {{ date('m') == $key ? 'selected' : '' }}>{{ $value }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="year">Year</label>
                            <select name="year" id="year" class="form-control" required>
                                @foreach($years as $key => $value)
                                    <option value="{{ $key }}" {{ date('Y') == $key ? 'selected' : '' }}>{{ $value }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="fiscal_year_id">Fiscal Year</label>
                            <select name="fiscal_year_id" id="fiscal_year_id" class="form-control" required>
                                @foreach($fiscalYears as $id => $name)
                                    <option value="{{ $id }}">{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="fiscal_period_id">Fiscal Period</label>
                            <select name="fiscal_period_id" id="fiscal_period_id" class="form-control" required>
                                @foreach($fiscalPeriods as $id => $name)
                                    <option value="{{ $id }}">{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <p><strong>Note:</strong> This will create journal entries for all payroll records in the selected month and year. The journal entries will include:</p>
                    <ul>
                        <li>Debit to Salary Expense for the total gross pay</li>
                        <li>Credit to Payroll Tax Liability for the total PAYE tax</li>
                        <li>Credit to various Deduction Liabilities for the total deductions</li>
                        <li>Credit to Cash/Bank for the total net pay</li>
                    </ul>
                    <p>Make sure all payroll records for the selected period are finalized before creating journal entries.</p>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ route('payroll.index') }}" class="btn btn-light">
                        <i class="bi bi-arrow-left"></i> Back
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-journal-text"></i> Create Journal Entries
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    $(document).ready(function() {
        // Update fiscal periods when fiscal year changes
        $('#fiscal_year_id').change(function() {
            const fiscalYearId = $(this).val();
            
            // Make AJAX request to get fiscal periods for the selected fiscal year
            $.get('/get-fiscal-periods', { fiscal_year_id: fiscalYearId }, function(data) {
                let options = '';
                $.each(data, function(id, name) {
                    options += `<option value="${id}">${name}</option>`;
                });
                $('#fiscal_period_id').html(options);
            });
        });
    });
</script>
@endpush
@endsection
