@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
          <div class="col-sm mb-2 mb-sm-0">
            <h1 class="page-header-title">
                New Production Order
                <a href="{{ route('production-orders.index') }}" class="mr-4 float-right"
                    ><i class="icon ion-md-arrow-back"></i> Back
                </a>
            </h1>
          </div>
          <!-- End Col -->
        </div>
    </div>
    <!-- End Page Header -->

    <x-form
        method="POST"
        action="{{ route('production-orders.store') }}"
        class="mt-4"
    >
        <div class="row">
            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.select
                    name="product_id"
                    label="Product"
                    required
                    id="product-select"
                >
                    <option value="">Select Product</option>
                    @foreach($products as $id => $name)
                    <option value="{{ $id }}" {{ old('product_id') == $id ? 'selected' : '' }}>
                        {{ $name }}
                    </option>
                    @endforeach
                </x-inputs.select>
            </x-inputs.group>

            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.select
                    name="bom_id"
                    label="Bill of Materials"
                    required
                    id="bom-select"
                >
                    <option value="">Select BOM</option>
                </x-inputs.select>
            </x-inputs.group>

            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.number
                    name="quantity"
                    label="Quantity"
                    value="{{ old('quantity') }}"
                    required
                    min="0.01"
                    step="0.01"
                ></x-inputs.number>
            </x-inputs.group>

            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.text
                    name="unit_of_measure"
                    label="Unit of Measure"
                    value="{{ old('unit_of_measure') }}"
                    required
                ></x-inputs.text>
            </x-inputs.group>

            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.date
                    name="planned_start_date"
                    label="Planned Start Date"
                    value="{{ old('planned_start_date', date('Y-m-d')) }}"
                    required
                ></x-inputs.date>
            </x-inputs.group>

            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.date
                    name="planned_end_date"
                    label="Planned End Date"
                    value="{{ old('planned_end_date', date('Y-m-d', strtotime('+7 days'))) }}"
                    required
                ></x-inputs.date>
            </x-inputs.group>

            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.select
                    name="status"
                    label="Status"
                    required
                >
                    <option value="draft" {{ old('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                    <option value="planned" {{ old('status') == 'planned' ? 'selected' : '' }}>Planned</option>
                </x-inputs.select>
            </x-inputs.group>

            <x-inputs.group class="col-sm-6 mb-4">
                <x-inputs.select
                    name="branch_id"
                    label="Branch"
                >
                    <option value="">Select Branch</option>
                    @foreach($branches as $id => $name)
                    <option value="{{ $id }}" {{ old('branch_id') == $id ? 'selected' : '' }}>
                        {{ $name }}
                    </option>
                    @endforeach
                </x-inputs.select>
            </x-inputs.group>

            <x-inputs.group class="col-sm-12 mb-4">
                <x-inputs.textarea
                    name="description"
                    label="Description"
                    rows="3"
                >{{ old('description') }}</x-inputs.textarea>
            </x-inputs.group>
        </div>

        <div class="mt-4">
            <a
                href="{{ route('production-orders.index') }}"
                class="btn btn-light"
            >
                @lang('crud.common.back')
            </a>

            <button type="submit" class="btn btn-primary float-right">
                @lang('crud.common.create')
            </button>
        </div>
    </x-form>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // When product is selected, load BOMs
        $('#product-select').change(function() {
            var productId = $(this).val();
            if (productId) {
                $.ajax({
                    url: "{{ route('production-orders.get-boms') }}",
                    type: "GET",
                    data: { product_id: productId },
                    success: function(data) {
                        $('#bom-select').empty();
                        $('#bom-select').append('<option value="">Select BOM</option>');
                        $.each(data, function(key, value) {
                            var isDefault = value.is_default ? ' (Default)' : '';
                            $('#bom-select').append('<option value="' + value.id + '">' + value.name + ' v' + value.version + isDefault + '</option>');
                        });
                    }
                });
            } else {
                $('#bom-select').empty();
                $('#bom-select').append('<option value="">Select BOM</option>');
            }
        });
    });
</script>
@endpush
