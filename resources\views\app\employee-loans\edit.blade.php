@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <x-page-header title="Edit Loan Application - {{ $employeeLoan->loan_reference }}" />

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Edit Loan Application</h5>
                </div>

                <div class="card-body">
                    <form method="POST" action="{{ route('employee-loans.update', $employeeLoan) }}">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="employee_id">Employee *</label>
                                    <select name="employee_id" id="employee_id" class="form-control @error('employee_id') is-invalid @enderror" required>
                                        <option value="">Select Employee</option>
                                        @foreach($employees as $employee)
                                            <option value="{{ $employee->id }}" {{ old('employee_id', $employeeLoan->employee_id) == $employee->id ? 'selected' : '' }}>
                                                {{ $employee->name }} - {{ $employee->email }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('employee_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="loan_date">Loan Date *</label>
                                    <input type="date" name="loan_date" id="loan_date"
                                           class="form-control @error('loan_date') is-invalid @enderror"
                                           value="{{ old('loan_date', $employeeLoan->loan_date->format('Y-m-d')) }}" required>
                                    @error('loan_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="loan_amount">Loan Amount *</label>
                                    <input type="number" name="loan_amount" id="loan_amount"
                                           class="form-control @error('loan_amount') is-invalid @enderror"
                                           value="{{ old('loan_amount', $employeeLoan->loan_amount) }}" step="0.01" min="1" required>
                                    @error('loan_amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="installment_amount">Monthly Principal Payment *</label>
                                    <input type="number" name="installment_amount" id="installment_amount"
                                           class="form-control @error('installment_amount') is-invalid @enderror"
                                           value="{{ old('installment_amount', $employeeLoan->installment_amount) }}" step="0.01" min="1" required>
                                    @error('installment_amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        Principal amount to be deducted monthly. Interest will be calculated and deducted separately.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="first_deduction_date">First Deduction Date *</label>
                                    <input type="date" name="first_deduction_date" id="first_deduction_date"
                                           class="form-control @error('first_deduction_date') is-invalid @enderror"
                                           value="{{ old('first_deduction_date', $employeeLoan->first_deduction_date->format('Y-m-d')) }}" required>
                                    @error('first_deduction_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="estimated_installments">Estimated Installments</label>
                                    <input type="number" id="estimated_installments" class="form-control" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="interest_rate">Interest Rate (%)</label>
                                    <input type="number" name="interest_rate" id="interest_rate"
                                           class="form-control @error('interest_rate') is-invalid @enderror"
                                           value="{{ old('interest_rate', $employeeLoan->interest_rate) }}" step="0.01" min="0" max="100">
                                    @error('interest_rate')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="interest_type">Interest Type</label>
                                    <select name="interest_type" id="interest_type" class="form-control @error('interest_type') is-invalid @enderror">
                                        <option value="simple" {{ old('interest_type', $employeeLoan->interest_type) == 'simple' ? 'selected' : '' }}>Simple Interest</option>
                                        <option value="compound" {{ old('interest_type', $employeeLoan->interest_type) == 'compound' ? 'selected' : '' }}>Compound Interest</option>
                                    </select>
                                    @error('interest_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="purpose">Loan Purpose *</label>
                            <textarea name="purpose" id="purpose"
                                      class="form-control @error('purpose') is-invalid @enderror"
                                      rows="3" required>{{ old('purpose', $employeeLoan->purpose) }}</textarea>
                            @error('purpose')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="terms_conditions">Terms and Conditions</label>
                            <textarea name="terms_conditions" id="terms_conditions"
                                      class="form-control @error('terms_conditions') is-invalid @enderror"
                                      rows="4">{{ old('terms_conditions', $employeeLoan->terms_conditions) }}</textarea>
                            @error('terms_conditions')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Loan Application
                            </button>
                            <a href="{{ route('employee-loans.show', $employeeLoan) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Loan Details
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Current Loan Details</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Reference:</strong></td>
                            <td>{{ $employeeLoan->loan_reference }}</td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>
                                <span class="badge badge-warning">{{ ucfirst($employeeLoan->status) }}</span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Created:</strong></td>
                            <td>{{ $employeeLoan->created_at->format('M d, Y') }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">Loan Calculator</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6>Updated Summary</h6>
                        <p class="mb-1"><strong>Loan Amount:</strong> <span id="calc_loan_amount">{{ number_format($employeeLoan->loan_amount, 2) }}</span></p>
                        <p class="mb-1"><strong>Monthly Principal Payment:</strong> <span id="calc_installment">{{ number_format($employeeLoan->installment_amount, 2) }}</span></p>
                        <p class="mb-1"><strong>Number of Installments:</strong> <span id="calc_installments">{{ $employeeLoan->total_installments }}</span></p>
                        <p class="mb-0"><strong>Expected Completion:</strong> <span id="calc_completion">-</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loanAmountInput = document.getElementById('loan_amount');
    const installmentInput = document.getElementById('installment_amount');
    const firstDeductionInput = document.getElementById('first_deduction_date');
    const estimatedInstallmentsInput = document.getElementById('estimated_installments');

    function calculateLoan() {
        const loanAmount = parseFloat(loanAmountInput.value) || 0;
        const installmentAmount = parseFloat(installmentInput.value) || 0;
        const firstDeductionDate = firstDeductionInput.value;

        // Update calculator display
        document.getElementById('calc_loan_amount').textContent = loanAmount.toLocaleString('en-US', {minimumFractionDigits: 2});
        document.getElementById('calc_installment').textContent = installmentAmount.toLocaleString('en-US', {minimumFractionDigits: 2});

        if (loanAmount > 0 && installmentAmount > 0) {
            const installments = Math.ceil(loanAmount / installmentAmount);
            estimatedInstallmentsInput.value = installments;
            document.getElementById('calc_installments').textContent = installments;

            // Calculate expected completion date
            if (firstDeductionDate) {
                const startDate = new Date(firstDeductionDate);
                const completionDate = new Date(startDate);
                completionDate.setMonth(completionDate.getMonth() + installments - 1);
                document.getElementById('calc_completion').textContent = completionDate.toLocaleDateString();
            }
        } else {
            estimatedInstallmentsInput.value = '';
            document.getElementById('calc_installments').textContent = '0';
            document.getElementById('calc_completion').textContent = '-';
        }
    }

    // Add event listeners
    loanAmountInput.addEventListener('input', calculateLoan);
    installmentInput.addEventListener('input', calculateLoan);
    firstDeductionInput.addEventListener('change', calculateLoan);

    // Initial calculation
    calculateLoan();
});
</script>
@endpush
@endsection
