<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use App\Models\User;
use App\Models\Tenant;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;


class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    public function showRegistrationForm (){
        return view('auth.register');
    }
    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array  $data
     * @return \App\Models\User
     */
    function register(Request $request)
    {

        $data = $request->validate([
            'name' => ['required'],
            'phone' => ['nullable'],
            'email' => ['unique:tenants,email', 'email'],
            'domain' =>  ['unique:tenants,domain'],
            'password' => ['required'],
        ]);
        $data['password'] = Hash::make($data['password']);
        $user = \Facades\App\Libraries\UserHandler::createUser($data);
        Auth::login($user);
        return redirect("/");
    }
}
