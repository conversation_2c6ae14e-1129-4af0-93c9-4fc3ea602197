@php
$itemIndex = $index ?? 0;
$itemSale = $sale ?? null;
@endphp

<div class="row g-3">
    <div class="col-md-4">
        <label class="form-label">Product <span class="text-danger">*</span></label>
        <select name="items[{{ $itemIndex }}][product_id]" class="form-select @error('items.'.$itemIndex.'.product_id') is-invalid @enderror" required onchange="updateProductInfo(this, {{ $itemIndex }})">
            <option value="">Select Product</option>
            @foreach($products as $product)
                <option value="{{ $product->id }}" 
                        data-price="{{ $product->buying_price }}" 
                        data-units="{{ json_encode($product->units) }}"
                        {{ old('items.'.$itemIndex.'.product_id', $itemSale?->product_id) == $product->id ? 'selected' : '' }}>
                    {{ $product->name }}
                </option>
            @endforeach
        </select>
        @error('items.'.$itemIndex.'.product_id')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>
    
    <div class="col-md-2">
        <label class="form-label">Unit <span class="text-danger">*</span></label>
        <select name="items[{{ $itemIndex }}][unit_id]" class="form-select @error('items.'.$itemIndex.'.unit_id') is-invalid @enderror" required>
            <option value="">Select Unit</option>
            @if($itemSale && $itemSale->product && $itemSale->product->units)
                @foreach($itemSale->product->units as $unit)
                    <option value="{{ $unit->id }}" {{ old('items.'.$itemIndex.'.unit_id', $itemSale?->unit_id) == $unit->id ? 'selected' : '' }}>
                        {{ $unit->name }}
                    </option>
                @endforeach
            @endif
        </select>
        @error('items.'.$itemIndex.'.unit_id')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>
    
    <div class="col-md-2">
        <label class="form-label">Quantity <span class="text-danger">*</span></label>
        <input type="number" 
               name="items[{{ $itemIndex }}][quantity]" 
               class="form-control @error('items.'.$itemIndex.'.quantity') is-invalid @enderror" 
               step="0.01" 
               min="0.01" 
               value="{{ old('items.'.$itemIndex.'.quantity', $itemSale?->quantity ?? '1') }}" 
               required 
               onchange="calculateItemTotal({{ $itemIndex }})">
        @error('items.'.$itemIndex.'.quantity')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>
    
    <div class="col-md-2">
        <label class="form-label">Unit Price</label>
        <div class="input-group">
            <span class="input-group-text">$</span>
            <input type="number" 
                   name="items[{{ $itemIndex }}][buying_price]" 
                   class="form-control @error('items.'.$itemIndex.'.buying_price') is-invalid @enderror" 
                   step="0.01" 
                   min="0" 
                   value="{{ old('items.'.$itemIndex.'.buying_price', $itemSale?->buying_price ?? '0.00') }}" 
                   onchange="calculateItemTotal({{ $itemIndex }})">
        </div>
        @error('items.'.$itemIndex.'.buying_price')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>
    
    <div class="col-md-2">
        <label class="form-label">Total</label>
        <div class="input-group">
            <span class="input-group-text">$</span>
            <input type="text" class="form-control item-total" readonly value="0.00">
        </div>
    </div>
    
    <div class="col-12">
        <div class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Supplier</label>
                <select name="items[{{ $itemIndex }}][supplier_id]" class="form-select @error('items.'.$itemIndex.'.supplier_id') is-invalid @enderror">
                    <option value="">Select Supplier</option>
                    @foreach(App\Models\Supplier::orderBy('name')->get() as $supplier)
                        <option value="{{ $supplier->id }}" {{ old('items.'.$itemIndex.'.supplier_id', $itemSale?->supplier_id) == $supplier->id ? 'selected' : '' }}>
                            {{ $supplier->name }}
                        </option>
                    @endforeach
                </select>
                @error('items.'.$itemIndex.'.supplier_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="col-md-3">
                <label class="form-label">Discount</label>
                <div class="input-group">
                    <span class="input-group-text">$</span>
                    <input type="number" 
                           name="items[{{ $itemIndex }}][discount]" 
                           class="form-control @error('items.'.$itemIndex.'.discount') is-invalid @enderror" 
                           step="0.01" 
                           min="0" 
                           value="{{ old('items.'.$itemIndex.'.discount', $itemSale?->discount ?? '0.00') }}" 
                           onchange="calculateItemTotal({{ $itemIndex }})">
                </div>
                @error('items.'.$itemIndex.'.discount')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="col-md-3">
                <label class="form-label">Expiry Date</label>
                <input type="date" 
                       name="items[{{ $itemIndex }}][expires_at]" 
                       class="form-control @error('items.'.$itemIndex.'.expires_at') is-invalid @enderror"
                       value="{{ old('items.'.$itemIndex.'.expires_at', $itemSale?->expires_at?->format('Y-m-d')) }}">
                @error('items.'.$itemIndex.'.expires_at')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="col-md-3 d-flex align-items-end">
                <button type="button" class="btn btn-outline-danger w-100" onclick="removeOrderItem(this)">
                    <i class="bi bi-trash me-1"></i>Remove Item
                </button>
            </div>
        </div>
    </div>
</div>
