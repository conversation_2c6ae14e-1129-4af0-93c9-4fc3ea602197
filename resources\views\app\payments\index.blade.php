@extends('layouts.app')

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">credit-card</x-slot>
        <x-slot name="title">Payments & Transactions</x-slot>
        <x-slot name="subtitle">Manage all payment records and transactions</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Payments</li>
        </x-slot>
        <x-slot name="controls">
            @can('create', App\Models\Payment::class)
            <button type="button" class="btn btn-primary btn-sm"
                    onclick="openPaymentModal()">
                <i class="bi bi-plus-circle me-1"></i>Record Payment
            </button>
            @endcan
        </x-slot>
    </x-page-header>

    <!-- Analytics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-sm avatar-soft-primary avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-credit-card"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <span class="d-block h5 mb-0">{{ number_format($analytics['total_payments']) }}</span>
                            <span class="d-block fs-6 text-muted">Total Payments</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-sm avatar-soft-success avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-currency-dollar"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <span class="d-block h5 mb-0">{{ _money($analytics['total_amount']) }}</span>
                            <span class="d-block fs-6 text-muted">Total Amount</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-sm avatar-soft-info avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-calendar-day"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <span class="d-block h5 mb-0">{{ number_format($analytics['payments_today']) }}</span>
                            <span class="d-block fs-6 text-muted">Today's Payments</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avatar avatar-sm avatar-soft-warning avatar-circle">
                                <span class="avatar-initials"><i class="bi bi-graph-up"></i></span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <span class="d-block h5 mb-0">{{ _money($analytics['avg_payment']) }}</span>
                            <span class="d-block fs-6 text-muted">Average Payment</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="card-header-title">
                <i class="bi bi-funnel me-2"></i>Filters & Search
            </h6>
        </div>
        <div class="card-body">
            <form action="{{ route('payments.index') }}" method="GET" class="row g-3">
                <!-- Search -->
                <div class="col-md-4">
                    <label class="form-label">Search</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" name="search"
                               placeholder="Search payments, references..."
                               value="{{ request('search') }}">
                    </div>
                </div>

                <!-- Payment Type -->
                <div class="col-md-4">
                    <label class="form-label">Payment Type</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select" name="payment_type" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select payment type..."
                        }'>
                            <option value="">All Types</option>
                            @foreach($filterOptions['payment_types'] as $key => $label)
                                <option value="{{ $key }}" {{ request('payment_type') == $key ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <!-- Currency -->
                <div class="col-md-4">
                    <label class="form-label">Currency</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select" name="currency_id" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select currency..."
                        }'>
                            <option value="">All Currencies</option>
                            @foreach($filterOptions['currencies'] as $currency)
                                <option value="{{ $currency->id }}" {{ request('currency_id') == $currency->id ? 'selected' : '' }}>
                                    {{ $currency->name }} ({{ $currency->code }})
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <!-- Date From -->
                <div class="col-md-3">
                    <label class="form-label">Date From</label>
                    <input type="date" class="form-control" name="date_from" value="{{ request('date_from') }}">
                </div>

                <!-- Date To -->
                <div class="col-md-3">
                    <label class="form-label">Date To</label>
                    <input type="date" class="form-control" name="date_to" value="{{ request('date_to') }}">
                </div>

                <!-- Amount From -->
                <div class="col-md-3">
                    <label class="form-label">Amount From</label>
                    <input type="number" class="form-control" name="amount_from" step="0.01" value="{{ request('amount_from') }}">
                </div>

                <!-- Amount To -->
                <div class="col-md-3">
                    <label class="form-label">Amount To</label>
                    <input type="number" class="form-control" name="amount_to" step="0.01" value="{{ request('amount_to') }}">
                </div>

                <!-- Action Buttons -->
                <div class="col-12 d-flex gap-2 justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-funnel me-1"></i>Apply Filters
                    </button>
                    <a href="{{ route('payments.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-x-circle me-1"></i>Clear All
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="card">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">Payment Records</h4>
                </div>
                <div class="col-auto">
                    <span class="text-muted">{{ $payments->total() }} payment(s) found</span>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                    <thead class="thead-light">
                        <tr>
                            <th style="width: 12%">Date</th>
                            <th style="width: 15%">Reference</th>
                            <th style="width: 15%">Type</th>
                            <th style="width: 20%">Description</th>
                            <th style="width: 12%">Amount</th>
                            <th style="width: 10%">Currency</th>
                            <th style="width: 10%">Created By</th>
                            <th style="width: 6%" class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($payments as $payment)
                        <tr>
                            <!-- Date -->
                            <td>
                                <span class="fw-semibold">{{ $payment->created_at->format('M d, Y') }}</span>
                                <small class="text-muted d-block">{{ $payment->created_at->format('h:i A') }}</small>
                            </td>
                            
                            <!-- Reference -->
                            <td>
                                @if($payment->reference_no)
                                    <span class="fw-semibold">{{ $payment->reference_no }}</span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            
                            <!-- Type -->
                            <td>
                                @if(isset($payment->payment_type))
                                    @if($payment->payment_type === 'receivable')
                                        <span class="badge bg-success">Receivable</span>
                                    @elseif($payment->payment_type === 'payable')
                                        <span class="badge bg-warning">Payable</span>
                                    @else
                                        <span class="badge bg-primary">{{ $payment->payment_type }}</span>
                                    @endif
                                @else
                                    <span class="badge bg-secondary">Payment</span>
                                @endif
                                @if($payment->paymentable)
                                    <small class="text-muted d-block">Invoice #{{ $payment->paymentable_id }}</small>
                                @endif
                            </td>
                            
                            <!-- Description -->
                            <td>
                                <span class="fw-semibold">{{ $payment->transaction_description }}</span>
                                @if($payment->comment && $payment->comment !== $payment->description)
                                    <small class="text-muted d-block">{{ Str::limit($payment->comment, 50) }}</small>
                                @endif
                            </td>
                            
                            <!-- Amount -->
                            <td>
                                <span class="fw-semibold text-success">{{ _money($payment->amount) }}</span>
                            </td>
                            
                            <!-- Currency -->
                            <td>
                                @if($payment->currency)
                                    <span class="badge bg-light text-dark">{{ $payment->currency->code }}</span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            
                            <!-- Created By -->
                            <td>
                                @if($payment->createdBy)
                                    <span class="fw-semibold">{{ $payment->createdBy->name }}</span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            
                            <!-- Actions -->
                            <td class="text-center">
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button"
                                            id="paymentActions{{ $payment->id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="paymentActions{{ $payment->id }}">
                                        @can('view', $payment)
                                        <li>
                                            <a href="{{ route('payments.show', $payment) }}" class="dropdown-item">
                                                <i class="bi bi-eye me-2"></i>View Details
                                            </a>
                                        </li>
                                        @endcan
                                        @can('update', $payment)
                                        <li>
                                            <a href="{{ route('payments.edit', $payment) }}" class="dropdown-item">
                                                <i class="bi bi-pencil me-2"></i>Edit Payment
                                            </a>
                                        </li>
                                        @endcan
                                        @can('delete', $payment)
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <form action="{{ route('payments.destroy', $payment) }}" method="POST" class="d-inline w-100"
                                                  onsubmit="return confirm('Are you sure you want to delete this payment? This action cannot be undone.')">
                                                @csrf @method('DELETE')
                                                <button type="submit" class="dropdown-item text-danger">
                                                    <i class="bi bi-trash me-2"></i>Delete Payment
                                                </button>
                                            </form>
                                        </li>
                                        @endcan
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center p-5">
                                <x-empty-state
                                    icon="credit-card"
                                    title="No payments found"
                                    description="No payment records match your current filters."
                                    :action-url="route('payments.create')"
                                    action-text="Record Payment"
                                />
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        
        @if($payments->hasPages())
        <div class="card-footer">
            {{ $payments->appends(request()->query())->links() }}
        </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize Tom Select dropdowns
    if (typeof HSCore !== 'undefined' && HSCore.components && HSCore.components.HSTomSelect) {
        HSCore.components.HSTomSelect.init('.js-select');
    }

    // Date range validation
    $('input[name="date_from"]').on('change', function() {
        const dateTo = $('input[name="date_to"]').val();
        if (dateTo && $(this).val() > dateTo) {
            alert('Start date cannot be later than end date');
            $(this).val('');
        }
    });
    
    $('input[name="date_to"]').on('change', function() {
        const dateFrom = $('input[name="date_from"]').val();
        if (dateFrom && $(this).val() < dateFrom) {
            alert('End date cannot be earlier than start date');
            $(this).val('');
        }
    });

    // Amount range validation
    $('input[name="amount_from"]').on('change', function() {
        const amountTo = $('input[name="amount_to"]').val();
        if (amountTo && parseFloat($(this).val()) > parseFloat(amountTo)) {
            alert('Minimum amount cannot be greater than maximum amount');
            $(this).val('');
        }
    });
    
    $('input[name="amount_to"]').on('change', function() {
        const amountFrom = $('input[name="amount_from"]').val();
        if (amountFrom && parseFloat($(this).val()) < parseFloat(amountFrom)) {
            alert('Maximum amount cannot be less than minimum amount');
            $(this).val('');
        }
    });
});
</script>
@endpush

<!-- Include Payment Modal -->
<x-payment-modal />
@endsection
