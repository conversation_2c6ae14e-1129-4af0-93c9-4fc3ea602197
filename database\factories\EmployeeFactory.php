<?php

namespace Database\Factories;

use App\Models\Employee;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class EmployeeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Employee::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'address' => $this->faker->address(),
            'emergency_phone' => $this->faker->phoneNumber(),
            'emergency_address' => $this->faker->address(),
            'description' => $this->faker->jobTitle() . ' - ' . $this->faker->sentence(),
            'basic_pay' => $this->faker->numberBetween(30000, 150000),
            'medical_description' => $this->faker->optional()->sentence(),
            'grade' => $this->faker->randomElement([
                'Junior',
                'Senior',
                'Manager',
                'Senior Manager',
                'Director',
                'Executive'
            ]),
            'user_id' => null, // Can be set manually when needed
            'created_by' => function () {
                return User::factory()->create()->id;
            },
            'updated_by' => null,
            'approved_by' => null,
        ];
    }

    /**
     * Indicate that the employee should be linked to a user account.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function withUser()
    {
        return $this->state(function (array $attributes) {
            $user = User::factory()->create([
                'name' => $attributes['name'],
                'email' => $attributes['email'],
            ]);

            return [
                'user_id' => $user->id,
            ];
        });
    }

    /**
     * Indicate that the employee should have a high salary.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function highSalary()
    {
        return $this->state(function (array $attributes) {
            return [
                'basic_pay' => $this->faker->numberBetween(100000, 300000),
                'grade' => $this->faker->randomElement(['Senior Manager', 'Director', 'Executive']),
            ];
        });
    }

    /**
     * Indicate that the employee should have a low salary.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function lowSalary()
    {
        return $this->state(function (array $attributes) {
            return [
                'basic_pay' => $this->faker->numberBetween(20000, 50000),
                'grade' => $this->faker->randomElement(['Junior', 'Senior']),
            ];
        });
    }

    /**
     * Indicate that the employee should be a manager.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function manager()
    {
        return $this->state(function (array $attributes) {
            return [
                'basic_pay' => $this->faker->numberBetween(80000, 200000),
                'grade' => $this->faker->randomElement(['Manager', 'Senior Manager', 'Director']),
                'description' => 'Management role - ' . $this->faker->sentence(),
            ];
        });
    }
}
