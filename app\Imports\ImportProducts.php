<?php

namespace App\Imports;

use App\Models\Product;
use App\Models\Category;
use App\Models\Stock;
use App\Models\Unit;
use Maatwebsite\Excel\Concerns\ToModel;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class ImportProducts implements ToModel, WithHeadingRow
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */

    public function getCategory($row){
        $category = null;
        if( _from($row, "category") ) {
            $category = Category::where("name", _from($row, "category") )->first();
            if( $category ) {
                $category->update(["name" => _from($row, "category")]);
            } else {
                Category::create(["name" => _from($row, "category")]);
            }
        }
        return $category;
    }

    public function setUnits($product, $row){

        $units = ['unit1', 'unit2', 'unit3', 'unit4', 'unit5', 'unit6', 'unit6', 'unit7', 'unit8', 'unit9', 'unit10'];

        foreach ($units as $unitKey) {

            $name     = _from($row, $unitKey);
            $quantity = _from($row, $unitKey . "items");
            $selling_price = _from($row, $unitKey . "sellingprice");
            $buying_price  = _from($row, $unitKey . "buyingprice");

            if($name) {

                $params = [
                    "name" => $name,
                    "quantity" => $quantity,
                    "buying_price" => $buying_price,
                    "selling_price" => $selling_price,
                    "product_id" => $product->id,
                    "status_id" => 1,
                ];  

                $unit = Unit::where("name", $name)->where("product_id", $product->id)->first();
                if( $unit ) $unit->update($params);
                else $unit = Unit::create($params);

            }

        }

    }


    public function model(array $row)
    {

        $branch = request()->branch_id ?? auth()->user()->branch_id;
        $category = $this->getCategory($row);

        $params = [
            "name" => _from($row, "item"),
            "description" => _from($row, "description"),
        ];

        $product = Product::where($params)->first();
        $params["category_id"]   = $category->id ?? null;
        $params["buying_price"]  = _from($row, "bprice");
        $params["selling_price"] = _from($row, "sprice");
        $params["vat_applied"]   = _from($row, "vat")  == 'YES' ? 1 : 0;

        if(_from( $row, "item" ) ){
            if( $product ) {
                $product->update($params);
            } else {
                $product = Product::create($params);
            }
        }
        if( $product ) $this->setUnits($product, $row);


    }

    private function cleanValue($value){


    }

    public function insertRows($branch, $row){

    }



}


// Array
// (
//     [id] => 1198
//     [student_name] => Susan January
//     [organizing_an_office] => 
//     [performing_reception_duties] => 
//     [handling_mail] => 
//     [performing_computer_operations] => 
//     [filing_and_reproducing_documents] => 
//     [grade] => 
//     [testing] => 
// )


    // public function collection(Collection $rows)
    // {
    //     $data = [];

    //     foreach ($rows as $row) 
    //     {
    //         $data[] = array(
    //                 'CustomerName'  => $row[0],
    //                 'Gender'        => $row[1],
    //                 'Address'       => $row[2],
    //                 'City'          => $row[3],
    //                 'PostalCode'    => $row[4],
    //                 'Country'       => $row[5],
    //             );
    //     }

    //     DB::table('tbl_customer')->insert($data);
    // }