<?php

namespace App\Http\Controllers;

use App\Models\Asset;
use App\Models\AssetDepreciation;
use App\Models\JournalEntry;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AssetDepreciationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', AssetDepreciation::class);

        $search = $request->get('search', '');
        $assetId = $request->get('asset_id', '');
        $fromDate = $request->get('from_date', '');
        $toDate = $request->get('to_date', '');

        $assetDepreciations = AssetDepreciation::search($search);
        
        if ($assetId) {
            $assetDepreciations->where('asset_id', $assetId);
        }
        
        if ($fromDate) {
            $assetDepreciations->where('depreciation_date', '>=', $fromDate);
        }
        
        if ($toDate) {
            $assetDepreciations->where('depreciation_date', '<=', $toDate);
        }
        
        $assetDepreciations = $assetDepreciations->latest('depreciation_date')
            ->paginate()
            ->withQueryString();

        $assets = Asset::orderBy('name')
            ->pluck('name', 'id');

        return view('app.asset_depreciations.index', compact(
            'assetDepreciations', 
            'search', 
            'assets',
            'assetId',
            'fromDate',
            'toDate'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', AssetDepreciation::class);

        $assets = Asset::where('status', 'active')
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.asset_depreciations.create', compact('assets'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', AssetDepreciation::class);

        $validated = $request->validate([
            'asset_id' => 'required|exists:assets,id',
            'depreciation_date' => 'required|date',
            'notes' => 'nullable|string',
        ]);

        // Get asset
        $asset = Asset::findOrFail($validated['asset_id']);
        
        // Check if asset is active
        if ($asset->status !== 'active') {
            return redirect()
                ->back()
                ->withInput()
                ->withError('Only active assets can be depreciated.');
        }
        
        // Check if depreciation date is valid
        $depreciationDate = new \DateTime($validated['depreciation_date']);
        $acquisitionDate = new \DateTime($asset->acquisition_date);
        
        if ($depreciationDate < $acquisitionDate) {
            return redirect()
                ->back()
                ->withInput()
                ->withError('Depreciation date cannot be before acquisition date.');
        }
        
        // Check if depreciation already exists for this date
        $existingDepreciation = AssetDepreciation::where('asset_id', $asset->id)
            ->where('depreciation_date', $validated['depreciation_date'])
            ->first();
            
        if ($existingDepreciation) {
            return redirect()
                ->back()
                ->withInput()
                ->withError('Depreciation already exists for this date.');
        }
        
        // Calculate depreciation amount
        $depreciationAmount = $this->calculateDepreciation($asset, $validated['depreciation_date']);
        
        if ($depreciationAmount <= 0) {
            return redirect()
                ->back()
                ->withInput()
                ->withError('Depreciation amount is zero or negative.');
        }
        
        // Calculate new accumulated depreciation and book value
        $accumulatedDepreciation = $asset->accumulated_depreciation + $depreciationAmount;
        $bookValue = $asset->acquisition_cost - $accumulatedDepreciation;
        
        DB::beginTransaction();
        
        try {
            // Create journal entry for depreciation
            $journalEntry = $this->createDepreciationJournalEntry($asset, $validated['depreciation_date'], $depreciationAmount);
            
            // Create asset depreciation record
            $assetDepreciation = AssetDepreciation::create([
                'asset_id' => $asset->id,
                'depreciation_date' => $validated['depreciation_date'],
                'depreciation_amount' => $depreciationAmount,
                'accumulated_depreciation' => $accumulatedDepreciation,
                'book_value' => $bookValue,
                'journal_entry_id' => $journalEntry->id,
                'notes' => $validated['notes'],
            ]);
            
            // Update asset
            $asset->update([
                'accumulated_depreciation' => $accumulatedDepreciation,
                'last_depreciation_date' => $validated['depreciation_date'],
                'next_depreciation_date' => $this->calculateNextDepreciationDate($validated['depreciation_date']),
            ]);
            
            DB::commit();
            
            return redirect()
                ->route('asset-depreciations.show', $assetDepreciation)
                ->withSuccess(__('crud.common.created'));
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\AssetDepreciation  $assetDepreciation
     * @return \Illuminate\Http\Response
     */
    public function show(AssetDepreciation $assetDepreciation)
    {
        $this->authorize('view', $assetDepreciation);

        return view('app.asset_depreciations.show', compact('assetDepreciation'));
    }

    /**
     * Show the form for running batch depreciation.
     *
     * @return \Illuminate\Http\Response
     */
    public function batchForm()
    {
        $this->authorize('create', AssetDepreciation::class);

        return view('app.asset_depreciations.batch');
    }
    
    /**
     * Run batch depreciation for all assets.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function runBatch(Request $request)
    {
        $this->authorize('create', AssetDepreciation::class);

        $validated = $request->validate([
            'depreciation_date' => 'required|date',
        ]);

        // Get all active assets due for depreciation
        $assets = Asset::where('status', 'active')
            ->where(function ($query) use ($validated) {
                $query->where('next_depreciation_date', '<=', $validated['depreciation_date'])
                    ->orWhereNull('next_depreciation_date');
            })
            ->get();
            
        if ($assets->isEmpty()) {
            return redirect()
                ->back()
                ->withInput()
                ->withError('No assets are due for depreciation.');
        }
        
        $successCount = 0;
        $errorCount = 0;
        $errors = [];
        
        DB::beginTransaction();
        
        try {
            foreach ($assets as $asset) {
                try {
                    // Calculate depreciation amount
                    $depreciationAmount = $this->calculateDepreciation($asset, $validated['depreciation_date']);
                    
                    if ($depreciationAmount <= 0) {
                        $errorCount++;
                        $errors[] = "Asset {$asset->name}: Depreciation amount is zero or negative.";
                        continue;
                    }
                    
                    // Calculate new accumulated depreciation and book value
                    $accumulatedDepreciation = $asset->accumulated_depreciation + $depreciationAmount;
                    $bookValue = $asset->acquisition_cost - $accumulatedDepreciation;
                    
                    // Create journal entry for depreciation
                    $journalEntry = $this->createDepreciationJournalEntry($asset, $validated['depreciation_date'], $depreciationAmount);
                    
                    // Create asset depreciation record
                    AssetDepreciation::create([
                        'asset_id' => $asset->id,
                        'depreciation_date' => $validated['depreciation_date'],
                        'depreciation_amount' => $depreciationAmount,
                        'accumulated_depreciation' => $accumulatedDepreciation,
                        'book_value' => $bookValue,
                        'journal_entry_id' => $journalEntry->id,
                        'notes' => 'Batch depreciation',
                    ]);
                    
                    // Update asset
                    $asset->update([
                        'accumulated_depreciation' => $accumulatedDepreciation,
                        'last_depreciation_date' => $validated['depreciation_date'],
                        'next_depreciation_date' => $this->calculateNextDepreciationDate($validated['depreciation_date']),
                    ]);
                    
                    $successCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    $errors[] = "Asset {$asset->name}: {$e->getMessage()}";
                }
            }
            
            DB::commit();
            
            $message = "Depreciation completed for {$successCount} assets.";
            if ($errorCount > 0) {
                $message .= " {$errorCount} assets had errors.";
            }
            
            return redirect()
                ->route('asset-depreciations.index')
                ->withSuccess($message);
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
    
    /**
     * Calculate depreciation amount for an asset.
     *
     * @param  \App\Models\Asset  $asset
     * @param  string  $depreciationDate
     * @return float
     */
    private function calculateDepreciation(Asset $asset, $depreciationDate)
    {
        $depreciationMethod = $asset->depreciation_method;
        $depreciableValue = $asset->acquisition_cost - $asset->salvage_value;
        $usefulLifeYears = $asset->useful_life_years;
        $depreciationRate = $asset->depreciation_rate;
        
        // Get current book value
        $currentBookValue = $asset->acquisition_cost - $asset->accumulated_depreciation;
        
        // Calculate remaining useful life
        $acquisitionDate = new \DateTime($asset->acquisition_date);
        $depreciationDateTime = new \DateTime($depreciationDate);
        $lastDepreciationDate = $asset->last_depreciation_date ? new \DateTime($asset->last_depreciation_date) : null;
        
        // Calculate months since last depreciation
        $monthsSinceLastDepreciation = 1;
        if ($lastDepreciationDate) {
            $interval = $lastDepreciationDate->diff($depreciationDateTime);
            $monthsSinceLastDepreciation = $interval->y * 12 + $interval->m;
        }
        
        // Calculate total months since acquisition
        $totalMonthsSinceAcquisition = $acquisitionDate->diff($depreciationDateTime)->y * 12 + $acquisitionDate->diff($depreciationDateTime)->m;
        
        // Calculate depreciation amount based on method
        $depreciationAmount = 0;
        
        if ($depreciationMethod === 'straight_line') {
            // Monthly depreciation = (Cost - Salvage) / (Useful Life in months)
            $monthlyDepreciation = $depreciableValue / ($usefulLifeYears * 12);
            $depreciationAmount = $monthlyDepreciation * $monthsSinceLastDepreciation;
        } elseif ($depreciationMethod === 'declining_balance') {
            // Monthly rate = Annual Rate / 12
            $monthlyRate = $depreciationRate / 12;
            
            // Depreciation = Book Value * Monthly Rate * Months
            $depreciationAmount = $currentBookValue * $monthlyRate * $monthsSinceLastDepreciation;
        } elseif ($depreciationMethod === 'sum_of_years_digits') {
            // This is more complex and would need a more detailed implementation
            // For now, we'll use a simplified version
            $totalMonths = $usefulLifeYears * 12;
            $remainingMonths = $totalMonths - $totalMonthsSinceAcquisition;
            
            if ($remainingMonths <= 0) {
                return 0;
            }
            
            $sumOfMonths = ($totalMonths * ($totalMonths + 1)) / 2;
            $monthlyDepreciation = ($depreciableValue * $remainingMonths) / $sumOfMonths;
            
            $depreciationAmount = $monthlyDepreciation * $monthsSinceLastDepreciation;
        }
        
        // Ensure we don't depreciate below salvage value
        if ($currentBookValue - $depreciationAmount < $asset->salvage_value) {
            $depreciationAmount = $currentBookValue - $asset->salvage_value;
        }
        
        return max(0, $depreciationAmount);
    }
    
    /**
     * Calculate the next depreciation date.
     *
     * @param  string  $currentDepreciationDate
     * @return string
     */
    private function calculateNextDepreciationDate($currentDepreciationDate)
    {
        $date = new \DateTime($currentDepreciationDate);
        $date->modify('+1 month');
        return $date->format('Y-m-d');
    }
    
    /**
     * Create a journal entry for asset depreciation.
     *
     * @param  \App\Models\Asset  $asset
     * @param  string  $depreciationDate
     * @param  float  $depreciationAmount
     * @return \App\Models\JournalEntry
     */
    private function createDepreciationJournalEntry(Asset $asset, $depreciationDate, $depreciationAmount)
    {
        $assetCategory = $asset->assetCategory;
        
        // Generate entry number
        $latestEntry = JournalEntry::latest()->first();
        $entryNumber = 'JE-' . date('Y') . '-' . sprintf('%06d', $latestEntry ? ($latestEntry->id + 1) : 1);
        
        // Create journal entry
        $journalEntry = JournalEntry::create([
            'entry_number' => $entryNumber,
            'entry_date' => $depreciationDate,
            'reference_number' => 'ASSET-DEPR-' . $asset->asset_number,
            'description' => 'Depreciation for asset: ' . $asset->name,
            'entry_type' => 'system',
            'status' => 'posted',
            'posted_by' => auth()->id(),
            'posted_at' => now(),
        ]);
        
        // Create journal entry lines
        
        // Debit depreciation expense
        $journalEntry->journalEntryLines()->create([
            'account_id' => $assetCategory->depreciation_expense_account_id,
            'description' => 'Depreciation expense for ' . $asset->name,
            'debit' => $depreciationAmount,
            'credit' => 0,
        ]);
        
        // Credit accumulated depreciation
        $journalEntry->journalEntryLines()->create([
            'account_id' => $assetCategory->accumulated_depreciation_account_id,
            'description' => 'Accumulated depreciation for ' . $asset->name,
            'debit' => 0,
            'credit' => $depreciationAmount,
        ]);
        
        return $journalEntry;
    }
}
