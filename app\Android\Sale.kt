package com.example


/**
* Homeflow Technologies | Sale.
*
* @property id
* @property product_name
* @property product_id
* @property unit_name
* @property unit_id
* @property price
* @property discount
* @property quantity
* @property saleable_id
* @property saleable_type
*
* @constructor Create Sale model
*/
data class Sale( var product_name: String? = null, var product_id: Int? = null, var unit_name: String? = null, var unit_id: Int? = null, var price: Double? = null, var discount: Double? = null, var quantity: Int? = null, var saleable_id: Int? = null, var saleable_type: String? = null,
)
