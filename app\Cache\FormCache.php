<?php

namespace App\Cache;

use App\Models\Form;
use App\Models\Approval;
use App\Models\FormResponse;

use Filament\{Tables, Forms};
use Illuminate\Database\Eloquent\Model;
use Livewire\Component;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

/**
 * 
 */
class FormCache
{
	
    public function contributionDeductionForm()
    {
        return [

            Forms\Components\TextInput::make('name')
                ->required()
                ->columnSpan([
                    'default' => 6,
                    'md' => 6,
                    'lg' => 6,
                ]),

            Forms\Components\TextInput::make('code')
                ->columnSpan([
                    'default' => 6,
                    'md' => 6,
                    'lg' => 6,
                ]),

            Forms\Components\TextInput::make('formula')
                ->label("Formula or Fix Amount")
                ->hint("Variables include BASIC_PAY, NET_PAY")
                ->columnSpan([
                    'default' => 6,
                    'md' => 6,
                    'lg' => 6,
                ]),

            // Forms\Components\Select::make('apply_mode')
            //     ->options(fn()=>[ "CONTRIBUTION" => "CONTRIBUTION",  "DEDUCTION" => "DEDUCTION"])
            //     ->required()
            //     ->columnSpan([
            //         'default' => 6,
            //         'md' => 6,
            //         'lg' => 6,
            //     ]),

            Forms\Components\Select::make('apply_at')
                ->options(fn()=>[ "GROSS" => "GROSS",  "NET" => "NET"])
                ->required()
                ->columnSpan([
                    'default' => 6,
                    'md' => 6,
                    'lg' => 6,
                ]),

            Forms\Components\Textarea::make('description')
                ->maxLength(65535)
                ->columnSpan([
                    'default' => 12,
                    'md' => 12,
                    'lg' => 12,
                ]),
        ];
    }



    
    public function attachContributionDeductionForm($hidden = [])
    {
        return [

            \Filament\Forms\Components\Select::make('payrolls')
                ->required()
                ->multiple()
                ->label("Employee")
                ->hidden(_from($hidden, 'payrolls'))
                ->options(function(){
                    return \App\Models\Payroll::get()->pluck("name", "id");
                })
                ->columnSpan([
                    'default' => 6,
                    'md' => 6,
                    'lg' => 6,
                ]),

            \Filament\Forms\Components\Select::make('contributionDeductions')
                ->required()
                ->multiple()
                ->label("Earnings / Deductions")
                ->hidden(_from($hidden, 'contributionDeductions'))
                ->options(function(){
                    return \App\Models\DeductionContribution::get()->pluck("name", "id");
                })
                ->columnSpan([
                    'default' => 6,
                    'md' => 6,
                    'lg' => 6,
                ]),
                
            Forms\Components\Select::make('start_month')
                ->options(function(){
                    return [
                        "JANUARY",
                        "FEBRUARY",
                        "MARCH",
                        "APRIL",
                        "MAY",
                        "JUNE",
                        "JULY",
                        "AUGUST",
                        "SEPTEMBER",
                        "OCTOBER",
                        "NOVEMBER",
                        "DECEMBER",
                    ];
                })
                ->columnSpan([
                    'default' => 6,
                    'md' => 6,
                    'lg' => 6,
                ]),

            Forms\Components\TextInput::make('number_of_installment')
                ->numeric()
                ->label("Number of installment")
                ->columnSpan([
                    'default' => 6,
                    'md' => 6,
                    'lg' => 6,
                ]),
            // Forms\Components\DatePicker::make('end_date')
            //     ->columnSpan([
            //         'default' => 6,
            //         'md' => 6,
            //         'lg' => 6,
            //     ]),

        ];
    }




    
    public function attachContributionForm($hidden = [])
    {
        return [

            \Filament\Forms\Components\Select::make('payrolls')
                ->required()
                ->multiple()
                ->label("Employee")
                ->hidden(_from($hidden, 'payrolls'))
                ->options(function(){
                    return \App\Models\Payroll::get()->pluck("name", "id");
                })
                ->columnSpan([
                    'default' => 6,
                    'md' => 6,
                    'lg' => 6,
                ]),

            \Filament\Forms\Components\Select::make('contributions')
                ->required()
                ->multiple()
                ->extraAttributes(['class' => 'mb-4'])
                ->label("Earnings")
                ->hidden(_from($hidden, 'contributions'))
                ->options(function(){
                    return \App\Models\DeductionContribution::where("apply_mode", "CONTRIBUTION")->pluck("name", "id");
                })
                ->columnSpan([
                    'default' => 6,
                    'md' => 6,
                    'lg' => 6,
                ]),
                
                
            // Forms\Components\Select::make('start_month')
            //     ->options(function(){
            //         return [
            //             "JANUARY",
            //             "FEBRUARY",
            //             "MARCH",
            //             "APRIL",
            //             "MAY",
            //             "JUNE",
            //             "JULY",
            //             "AUGUST",
            //             "SEPTEMBER",
            //             "OCTOBER",
            //             "NOVEMBER",
            //             "DECEMBER",
            //         ];
            //     })
            //     ->columnSpan([
            //         'default' => 6,
            //         'md' => 6,
            //         'lg' => 6,
            //     ]),

            // Forms\Components\TextInput::make('number_of_installment')
            //     ->numeric()
            //     ->label("Number of installment")
            //     ->columnSpan([
            //         'default' => 6,
            //         'md' => 6,
            //         'lg' => 6,
            //     ]),
            // Forms\Components\DatePicker::make('end_date')
            //     ->columnSpan([
            //         'default' => 6,
            //         'md' => 6,
            //         'lg' => 6,
            //     ]),

        ];
    }




    
    public function attachDeductionForm($hidden = [])
    {
        return [

            \Filament\Forms\Components\Select::make('payrolls')
                ->required()
                ->multiple()
                ->label("Employee")
                ->hidden(_from($hidden, 'payrolls'))
                ->options(function(){
                    return \App\Models\Payroll::get()->pluck("name", "id");
                })
                ->columnSpan([
                    'default' => 6,
                    'md' => 6,
                    'lg' => 6,
                ]),

            \Filament\Forms\Components\Select::make('deductions')
                ->required()
                ->multiple()
                ->label("Deductions")
                ->hidden(_from($hidden, 'deductions'))
                ->extraAttributes(['class' => 'mb-4'])
                ->options(function(){
                    return \App\Models\DeductionContribution::where("apply_mode", "DEDUCTION")->pluck("name", "id");
                })
                ->columnSpan([
                    'default' => 6,
                    'md' => 6,
                    'lg' => 6,
                ]),
                
                
            // Forms\Components\Select::make('start_month')
            //     ->options(function(){
            //         return [
            //             "JANUARY",
            //             "FEBRUARY",
            //             "MARCH",
            //             "APRIL",
            //             "MAY",
            //             "JUNE",
            //             "JULY",
            //             "AUGUST",
            //             "SEPTEMBER",
            //             "OCTOBER",
            //             "NOVEMBER",
            //             "DECEMBER",
            //         ];
            //     })
            //     ->columnSpan([
            //         'default' => 6,
            //         'md' => 6,
            //         'lg' => 6,
            //     ]),

            // Forms\Components\TextInput::make('number_of_installment')
            //     ->numeric()
            //     ->label("Number of installment")
            //     ->columnSpan([
            //         'default' => 6,
            //         'md' => 6,
            //         'lg' => 6,
            //     ]),
            // Forms\Components\DatePicker::make('end_date')
            //     ->columnSpan([
            //         'default' => 6,
            //         'md' => 6,
            //         'lg' => 6,
            //     ]),

        ];
    }




    
    public function employeeForm($hidden = [])
    {
        return [

            Forms\Components\Select::make('user_id')
                ->label("User")
                // ->rules(['unique:payrolls,staff_id'])
                ->options(fn($record)=> \App\Models\User::pluck('name', 'id'))
                ->preload()
                ->required()
                ->searchable(),

            Forms\Components\TextInput::make('name')
                ->label("Fullname")
                ->required(),

            Forms\Components\TextInput::make('basic_pay')
                ->numeric(),

            // Forms\Components\TextInput::make('position'),
            Forms\Components\TextInput::make('grade'),
            // Forms\Components\Textarea::make('description')
            //     ->maxLength(65535),


            // SpatieMediaLibraryFileUpload::make('attachments')
            //     ->multiple()
            //     ->enableReordering(),

            // \Filament\Forms\Components\FileUpload::make('attachments')
            //         ->multiple()
            //         ->preserveFilenames(),
                // ->columnSpan([
                //     'default' => 6,
                //     'md' => 6,
                //     'lg' => 6,
                // ]),                


            Forms\Components\Repeater::make('bankDetails')
                ->label("Employee Bank Details")
                ->relationship()
                ->schema([
                    Forms\Components\TextInput::make('bank_name')->required(),
                    Forms\Components\TextInput::make('account_number')->required(),
                    Forms\Components\TextInput::make('account_name')->required(),
                    Forms\Components\TextInput::make('branch'),
                    Forms\Components\Toggle::make('active'),
                ])
                // ->disableItemCreation()
                // ->disableItemDeletion()
                // ->disableItemMovement()
                ->columns(2)
                ->collapsible(),

            Forms\Components\Repeater::make('leaves')
                ->label("Employee Leave")
                ->relationship()
                ->schema([
                    Forms\Components\TextInput::make('name')
                        ->label("Leave Name")
                        ->required(),
                    Forms\Components\TextInput::make('number_of_days')
                        ->label("Total Days")
                        ->required(),                                
                    Forms\Components\TextInput::make('days_utilized')
                        ->label("Days Utilized")
                        ->required(),
                ])
                // ->disableItemCreation()
                // ->disableItemDeletion()
                // ->disableItemMovement()
                ->columns(3)
                ->collapsible(),

        ];
    }





}