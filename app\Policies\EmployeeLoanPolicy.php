<?php

namespace App\Policies;

use App\Models\User;
use App\Models\EmployeeLoan;
use Illuminate\Auth\Access\HandlesAuthorization;

class EmployeeLoanPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->can('list employee-loans');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\EmployeeLoan  $employeeLoan
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, EmployeeLoan $employeeLoan)
    {
        return $user->can('view employee-loans');
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->can('create employee-loans');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\EmployeeLoan  $employeeLoan
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, EmployeeLoan $employeeLoan)
    {
        return $user->can('update employee-loans');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\EmployeeLoan  $employeeLoan
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, EmployeeLoan $employeeLoan)
    {
        return $user->can('delete employee-loans');
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\EmployeeLoan  $employeeLoan
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, EmployeeLoan $employeeLoan)
    {
        return $user->can('restore employee-loans');
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\EmployeeLoan  $employeeLoan
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, EmployeeLoan $employeeLoan)
    {
        return $user->can('force-delete employee-loans');
    }

    /**
     * Determine whether the user can approve the loan.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\EmployeeLoan  $employeeLoan
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function approve(User $user, EmployeeLoan $employeeLoan)
    {
        return $user->can('approve employee-loans');
    }

    /**
     * Determine whether the user can cancel the loan.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\EmployeeLoan  $employeeLoan
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function cancel(User $user, EmployeeLoan $employeeLoan)
    {
        return $user->can('cancel employee-loans');
    }
}
