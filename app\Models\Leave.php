<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

use App\Traits\CreatedUpdatedTrait;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Leave extends Model implements HasMedia
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use InteractsWithMedia;
    protected $connection = "tenant";

    protected $fillable = [
        'name',
        'number_of_days',
        'days_utilized',
        'employee_id',
        'updated_by',
        'leave_id',
        'employee_id',
        'year',
    ];
    protected $searchableFields = ['*'];

    public function leaveRequests()
    {
        return $this->hasMany(LeaveRequest::class);
    }

    public function getDaysUtilizedAttribute($value) {
        return $this->leaveRequests()->sum("days");
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }



}
