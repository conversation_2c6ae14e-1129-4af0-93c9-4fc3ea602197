<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = User::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => $this->faker->name(),
            'phone' => $this->faker->phoneNumber,
            'email' => $this->faker->unique->email,
            'email_verified_at' => now(),
            'password' => \Hash::make('password'),
            'remember_token' => Str::random(10),
            // 'status_id' => \App\Models\Status::factory(),
            // 'created_by' => function () {
            //     return \App\Models\User::factory()->create([
            //         'created_by' => null,
            //         'updated_by' => null,
            //     ])->id;
            // },
            // 'updated_by' => function () {
            //     return \App\Models\User::factory()->create([
            //         'created_by' => null,
            //         'updated_by' => null,
            //     ])->id;
            // },
            // 'branch_id' => \App\Models\Branch::factory(),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function unverified()
    {
        return $this->state(function (array $attributes) {
            return [
                'email_verified_at' => null,
            ];
        });
    }
}
