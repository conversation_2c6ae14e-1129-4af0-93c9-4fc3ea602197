<?php

namespace App\Http\Controllers;

use App\Models\DeductionContribution;
use App\Models\Status;
use App\Models\Employee;
use App\Models\EmployeeDeductionContribution;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DeductionContributionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', DeductionContribution::class);

        $search = $request->get('search', '');
        $type = $request->get('type', '');

        $query = DeductionContribution::search($search);
        
        if ($type === 'deduction') {
            $query->where('apply_mode', 'DEDUCTION');
        } elseif ($type === 'contribution') {
            $query->where('apply_mode', 'CONTRIBUTION');
        }

        $deductionContributions = $query->latest()
            ->paginate()
            ->withQueryString();

        return view('app.deduction_contributions.index', compact('deductionContributions', 'search', 'type'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', DeductionContribution::class);

        $statuses = Status::pluck('name', 'id');
        $type = request('type', 'deduction');

        return view('app.deduction_contributions.create', compact('statuses', 'type'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', DeductionContribution::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:255',
            'formula' => 'required|string|max:255',
            'apply_mode' => 'required|in:DEDUCTION,CONTRIBUTION',
            'apply_at' => 'required|in:BEFORE,AFTER',
            'description' => 'nullable|string',
            'is_calculatable' => 'nullable|string',
            'status_id' => 'required|tenant_exists:statuses,id',
        ]);

        $validated['created_by'] = auth()->id();

        $deductionContribution = DeductionContribution::create($validated);

        return redirect()
            ->route('deduction-contributions.index', ['type' => strtolower($validated['apply_mode'])])
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Models\DeductionContribution $deductionContribution
     * @return \Illuminate\Http\Response
     */
    public function show(DeductionContribution $deductionContribution)
    {
        $this->authorize('view', $deductionContribution);

        $employees = Employee::whereHas('employeeDeductionContributions', function ($query) use ($deductionContribution) {
            $query->where('deduction_contribution_id', $deductionContribution->id);
        })->get();

        return view('app.deduction_contributions.show', compact('deductionContribution', 'employees'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Models\DeductionContribution $deductionContribution
     * @return \Illuminate\Http\Response
     */
    public function edit(DeductionContribution $deductionContribution)
    {
        $this->authorize('update', $deductionContribution);

        $statuses = Status::pluck('name', 'id');

        return view('app.deduction_contributions.edit', compact('deductionContribution', 'statuses'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\DeductionContribution $deductionContribution
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, DeductionContribution $deductionContribution)
    {
        $this->authorize('update', $deductionContribution);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:255',
            'formula' => 'required|string|max:255',
            'apply_at' => 'required|in:BEFORE,AFTER',
            'is_calculatable' => 'nullable|string',
            'description' => 'nullable|string',
            'status_id' => 'required|tenant_exists:statuses,id',
        ]);

        $validated['updated_by'] = auth()->id();

        $deductionContribution->update($validated);

        return redirect()
            ->route('deduction-contributions.index', ['type' => strtolower($deductionContribution->apply_mode)])
            ->withSuccess(__('crud.common.updated'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Models\DeductionContribution $deductionContribution
     * @return \Illuminate\Http\Response
     */
    public function destroy(DeductionContribution $deductionContribution)
    {
        $this->authorize('delete', $deductionContribution);

        $deductionContribution->delete();

        return redirect()
            ->route('deduction-contributions.index', ['type' => strtolower($deductionContribution->apply_mode)])
            ->withSuccess(__('crud.common.deleted'));
    }

    /**
     * Show form for assigning deduction/contribution to employees.
     *
     * @param \App\Models\DeductionContribution $deductionContribution
     * @return \Illuminate\Http\Response
     */
    public function assignForm(DeductionContribution $deductionContribution)
    {
        $this->authorize('update', $deductionContribution);

        $employees = Employee::orderBy('name')->get();
        
        $assignedEmployees = EmployeeDeductionContribution::where('deduction_contribution_id', $deductionContribution->id)
            ->pluck('employee_id')
            ->toArray();

        return view('app.deduction_contributions.assign', compact('deductionContribution', 'employees', 'assignedEmployees'));
    }

    /**
     * Process assignment of deduction/contribution to employees.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\DeductionContribution $deductionContribution
     * @return \Illuminate\Http\Response
     */
    public function assign(Request $request, DeductionContribution $deductionContribution)
    {
        $this->authorize('update', $deductionContribution);

        $validated = $request->validate([
            'employee_ids' => 'required|array',
            'employee_ids.*' => 'tenant_exists:employees,id',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        DB::beginTransaction();

        try {
            // Remove existing assignments
            EmployeeDeductionContribution::where('deduction_contribution_id', $deductionContribution->id)->delete();
            
            // Create new assignments
            foreach ($validated['employee_ids'] as $employeeId) {
                EmployeeDeductionContribution::create([
                    'employee_id' => $employeeId,
                    'deduction_contribution_id' => $deductionContribution->id,
                    'start_date' => $validated['start_date'],
                    'end_date' => $validated['end_date'],
                    'created_by' => auth()->id(),
                ]);
            }
            
            DB::commit();
            
            return redirect()
                ->route('deduction-contributions.show', $deductionContribution)
                ->withSuccess('Employees assigned successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()
                ->back()
                ->withInput()
                ->withError('An error occurred: ' . $e->getMessage());
        }
    }
}
