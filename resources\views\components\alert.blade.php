@props(['type' => 'info', 'dismissible' => true, 'icon' => null])

@php
    $alertClass = 'alert-info';
    $defaultIcon = 'bi-info-circle';
    
    switch ($type) {
        case 'success':
            $alertClass = 'alert-success';
            $defaultIcon = 'bi-check-circle';
            break;
        case 'warning':
            $alertClass = 'alert-warning';
            $defaultIcon = 'bi-exclamation-triangle';
            break;
        case 'danger':
            $alertClass = 'alert-danger';
            $defaultIcon = 'bi-x-circle';
            break;
        case 'primary':
            $alertClass = 'alert-primary';
            $defaultIcon = 'bi-bell';
            break;
        case 'secondary':
            $alertClass = 'alert-secondary';
            $defaultIcon = 'bi-bell';
            break;
        default:
            $alertClass = 'alert-info';
            $defaultIcon = 'bi-info-circle';
    }
    
    $iconToUse = $icon ?? $defaultIcon;
@endphp

<div {{ $attributes->merge(['class' => "alert $alertClass d-flex align-items-center"]) }} role="alert">
    <i class="bi {{ $iconToUse }} me-2 fs-5"></i>
    <div>
        {{ $slot }}
    </div>
    @if($dismissible)
    <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert" aria-label="Close"></button>
    @endif
</div>