<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\UserTenant;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Spatie\Multitenancy\Models\Tenant;
use App\Notifications\UserInvitationNotification;
use App\Mail\UserInvitationMail;
use Facades\App\Libraries\UserHandler;


class InvitationController extends Controller
{
    /**
     * Show invitation acceptance form
     */
    public function show($token)
    {
        // Query from landlord database
        $invitation = UserTenant::with(['tenant', 'inviter', 'warehouse'])->where('invitation_token', $token)->first();


        if (!$invitation) {
            return view('auth.invitation-invalid', [
                'message' => 'Invalid invitation link.'
            ]);
        }


        if (!$invitation->isValidInvitation()) {
            $message = $invitation->isExpired()
                ? 'This invitation has expired.'
                : 'This invitation has already been used.';

            return view('auth.invitation-invalid', [
                'message' => $message
            ]);
        }

        return view('auth.invitation-accept', [
            'invitation' => $invitation,
            'token' => $token
        ]);
    }

    /**
     * Process invitation acceptance
     */
    public function accept(Request $request, $token)
    {
        // Get invitation from landlord database
        $invitation = UserTenant::latest()->with(['tenant'])->where('invitation_token', $token)->first();
        $tenant = null;

        if($invitation) {
            $tenant = $invitation->tenant;
        }

        if (!$invitation) {
            return redirect()->route('login')->with('error', 'Invalid invitation link.');
        }


        if (!$invitation->isValidInvitation()) {
            $errorMessage = 'Invalid invitation.';
            if ($invitation->is_invitation_used) {
                $errorMessage = 'This invitation has already been used.';
            } elseif ($invitation->isExpired()) {
                $errorMessage = 'This invitation has expired.';
            } elseif ($invitation->status !== 'pending') {
                $errorMessage = 'This invitation is no longer valid (Status: ' . $invitation->status . ').';
            }
            return redirect()->route('login')->with('error', $errorMessage);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'password' => 'required|string|min:6|confirmed',
        ]);


        try {

            $tenant->makeCurrent();
            DB::beginTransaction();
            // Check if user already exists in the landlord database
            // Prepare user data for tenant
            $tenantUserData = [
                'name' => $request->name,
                'email' => $invitation->user_email,
                'password' => Hash::make($request->password),
                'email_verified_at' => now(),
                'business_type_id' => 1,
                'status_id' => 1, // Active status
                'warehouse_id' => $invitation->warehouse_id,
                'created_by' => $invitation->invited_by,
            ];
            // Check if user already exists in tenant database
            $tenantUser = User::where('email', $invitation->user_email)->first();

            if (!$tenantUser) {
                // Create user record in tenant database
                $tenantUser = User::create($tenantUserData);
                $tenantUser->assignRole($invitation->role);
            } else {
                // Update existing tenant user if needed
                $tenantUser->update([
                    'name' => $request->name,
                    'warehouse_id' => $invitation->warehouse_id,
                    'password' => Hash::make($request->password),
                ]);
                $tenantUser->assignRole($invitation->role);
            }


            DB::commit();

            UserHandler::createUser($tenantUserData);
            $tenant->switching();
            return redirect()->route('dashboard')->with('success',
                'Welcome! Your account has been created and you have been added to the team.');

        } catch (\Exception $e) {
            dd($e);
            DB::rollback();

            return redirect()->back()->with('error',
                'An error occurred while processing your invitation. Please try again.');
        }
    }

    /**
     * Resend invitation
     */
    public function resend(Request $request)
    {
        $request->validate([
            'invitation_id' => 'required|integer'
        ]);

        $invitation = UserTenant::on('landlord')->find($request->invitation_id);

        if (!$invitation) {
            return response()->json(['error' => 'Invitation not found'], 404);
        }

        // Check if user can resend this invitation
        if ($invitation->invited_by !== auth()->id() && !auth()->user()->isSuperAdmin()) {
            return response()->json(['error' => 'You are not authorized to resend this invitation'], 403);
        }

        if (!$invitation->isValidInvitation()) {
            return response()->json(['error' => 'Cannot resend expired or used invitation'], 400);
        }

        // Extend expiry
        $invitation->extendExpiry(7);

        try {
            $tenantName = ucfirst(str_replace('_', ' ', $invitation->tenant->name));
            $inviterName = auth()->user()->name;

            // Send email using Mail facade
            Mail::to($invitation->email)->send(new UserInvitationMail($invitation, $inviterName, $tenantName));

            return response()->json(['success' => 'Invitation resent successfully']);
        } catch (\Exception $e) {
            \Log::error('Failed to resend invitation email', [
                'invitation_id' => $invitation->id,
                'email' => $invitation->email,
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to resend invitation: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Cancel invitation
     */
    public function cancel(Request $request)
    {
        try {
            $request->validate([
                'invitation_id' => 'required|integer'
            ]);

            $invitation = UserTenant::on('landlord')->find($request->invitation_id);

            if (!$invitation) {
                return response()->json(['error' => 'Invitation not found'], 404);
            }

            // Check if user can cancel this invitation
            if ($invitation->invited_by !== auth()->id() && !auth()->user()->isSuperAdmin()) {
                return response()->json(['error' => 'You are not authorized to cancel this invitation'], 403);
            }

            if ($invitation->is_invitation_used) {
                return response()->json(['error' => 'Cannot cancel an invitation that has already been used'], 400);
            }

            // Cancel the invitation
            $invitation->cancelInvitation();

            return response()->json(['success' => 'Invitation cancelled successfully']);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['error' => 'Invalid invitation ID'], 422);
        } catch (\Exception $e) {
            \Log::error('Failed to cancel invitation: ' . $e->getMessage(), [
                'invitation_id' => $request->invitation_id,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to cancel invitation. Please try again.'], 500);
        }
    }
}
