<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\Invoice;
use App\Models\Order;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Facades\App\Libraries\InvoiceHandler;

class PaymentController extends Controller
{
    /**
     * Display a listing of payments
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Payment::class);

        // Build query with relationships
        $query = Payment::with([
            'paymentable',
            'currency',
            'createdBy',
            'updatedBy'
        ]);

        // Apply branch filter for non-admin users
        if (!auth()->user()->isSuperAdmin()) {
            $query->whereHas('paymentable', function($q) {
                $q->where('branch_id', auth()->user()->branch_id);
            });
        }

        // Apply filters
        $this->applyFilters($query, $request);

        // Get paginated results
        $payments = $query->orderBy('created_at', 'desc')->paginate(20);

        // Calculate analytics
        $analytics = $this->calculatePaymentAnalytics($request);

        // Get filter options
        $filterOptions = $this->getFilterOptions();

        return view('app.payments.index', compact('payments', 'analytics', 'filterOptions'));
    }

    /**
     * Show the form for creating a new payment
     */
    public function create(Request $request)
    {
        $this->authorize('create', Payment::class);

        // Get customers and suppliers for selection
        $customers = Customer::when(!auth()->user()->isSuperAdmin(), function($query) {
                              $query->where('branch_id', auth()->user()->branch_id);
                          })
                          ->orderBy('name')
                          ->get();

        $suppliers = Supplier::when(!auth()->user()->isSuperAdmin(), function($query) {
                              $query->where('branch_id', auth()->user()->branch_id);
                          })
                          ->orderBy('name')
                          ->get();

        $currencies = Currency::orderBy('name')->get();

        // Pre-select if coming from specific customer/supplier
        $selectedCustomer = $request->customer_id ? Customer::find($request->customer_id) : null;
        $selectedSupplier = $request->supplier_id ? Supplier::find($request->supplier_id) : null;
        $selectedInvoice = $request->invoice_id ? Invoice::find($request->invoice_id) : null;

        return view('app.payments.create', compact(
            'customers',
            'suppliers',
            'currencies',
            'selectedCustomer',
            'selectedSupplier',
            'selectedInvoice'
        ));
    }

    /**
     * Store a newly created payment
     */
    public function store(Request $request)
    {
        $this->authorize('create', Payment::class);

        $validated = $request->validate([
            'payment_type' => 'required|in:receivable,payable',
            'customer_id' => 'required_if:payment_type,receivable|tenant_exists:customers,id',
            'supplier_id' => 'required_if:payment_type,payable|tenant_exists:suppliers,id',
            'invoice_id' => 'required|tenant_exists:invoices,id',
            'amount' => 'required|numeric|min:0.01',
            'reference_no' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'comment' => 'nullable|string|max:1000',
            'payment_method' => 'nullable|string|max:100',
            'currency_id' => 'nullable|tenant_exists:currencies,id',
        ]);

        DB::beginTransaction();
        try {
            // Get the invoice
            $invoice = Invoice::findOrFail($validated['invoice_id']);

            // Validate payment amount doesn't exceed balance
            if ($validated['amount'] > $invoice->balance) {
                return back()
                    ->withInput()
                    ->with('error', 'Payment amount cannot exceed the outstanding balance of ' . _money($invoice->balance));
            }

            // Validate customer/supplier matches invoice
            if ($validated['payment_type'] === 'receivable' && $invoice->customer_id != $validated['customer_id']) {
                return back()
                    ->withInput()
                    ->with('error', 'Selected customer does not match the invoice customer.');
            }

            if ($validated['payment_type'] === 'payable' && $invoice->supplier_id != $validated['supplier_id']) {
                return back()
                    ->withInput()
                    ->with('error', 'Selected supplier does not match the invoice supplier.');
            }

            // Create payment using InvoiceHandler
            $paymentData = [
                'amount' => $validated['amount'],
                'description' => $validated['description'] ?? 'Payment for Invoice #' . $invoice->id,
                'comment' => $validated['comment'],
                'reference_no' => $validated['reference_no'],
                'payment_method' => $validated['payment_method'],
                'currency_id' => $validated['currency_id'],
                'balance' => $invoice->balance - $validated['amount'],
                'payment_type' => $validated['payment_type'],
            ];

            $payment = InvoiceHandler::addPayment($invoice, $paymentData);

            // Update the invoice balance
            $invoice->increment('amount_paid', $validated['amount']);

            DB::commit();

            return redirect()
                ->route('payments.show', $payment)
                ->with('success', 'Payment recorded successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()
                ->withInput()
                ->with('error', 'Failed to record payment: ' . $e->getMessage());
        }
    }

    /**
     * Store payment via AJAX (for modal submissions)
     */
    public function storeAjax(Request $request)
    {
        $this->authorize('create', Payment::class);

        $validated = $request->validate([
            'payment_type' => 'required|in:receivable,payable',
            'customer_id' => 'required_if:payment_type,receivable|nullable|tenant_exists:customers,id',
            'supplier_id' => 'required_if:payment_type,payable|nullable|tenant_exists:suppliers,id',
            'invoice_id' => 'required|numeric',
            'amount' => 'required|numeric|min:0.01',
            'reference_no' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'comment' => 'nullable|string|max:1000',
            'payment_method' => 'nullable|string|max:100',
            'currency_id' => 'nullable|tenant_exists:currencies,id',
        ]);

        DB::beginTransaction();
        try {
            // Determine the paymentable model based on payment type
            if ($validated['payment_type'] === 'receivable') {
                $paymentable = Invoice::findOrFail($validated['invoice_id']);
                $entity = Customer::findOrFail($validated['customer_id']);
            } else {
                $paymentable = Order::findOrFail($validated['invoice_id']);
                $entity = Supplier::findOrFail($validated['supplier_id']);
            }

            // Validate payment amount doesn't exceed balance
            if ($validated['amount'] > $paymentable->balance) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment amount cannot exceed the outstanding balance of ' . _money($paymentable->balance)
                ], 422);
            }

            // Create payment using InvoiceHandler
            $paymentData = [
                'amount' => $validated['amount'],
                'description' => $validated['description'] ?? 'Payment ' . ($validated['payment_type'] === 'receivable' ? 'from' : 'to') . ' ' . $entity->name,
                'comment' => $validated['comment'],
                'reference_no' => $validated['reference_no'],
                'payment_method' => $validated['payment_method'],
                'currency_id' => $validated['currency_id'],
                'balance' => $paymentable->balance - $validated['amount'],
                'payment_type' => $validated['payment_type'],
            ];

            $payment = InvoiceHandler::addPayment($paymentable, $paymentData);

            // Update the paymentable balance
            $paymentable->increment('amount_paid', $validated['amount']);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Payment recorded successfully.',
                'payment_id' => $payment->id
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Failed to record payment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified payment
     */
    public function show(Payment $payment)
    {
        $this->authorize('view', $payment);

        $payment->load([
            'paymentable',
            'currency',
            'createdBy',
            'updatedBy'
        ]);

        return view('app.payments.show', compact('payment'));
    }

    /**
     * Show the form for editing the specified payment
     */
    public function edit(Payment $payment)
    {
        $this->authorize('update', $payment);

        $currencies = Currency::orderBy('name')->get();

        return view('app.payments.edit', compact('payment', 'currencies'));
    }

    /**
     * Update the specified payment
     */
    public function update(Request $request, Payment $payment)
    {
        $this->authorize('update', $payment);

        $validated = $request->validate([
            'reference_no' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'comment' => 'nullable|string|max:1000',
            'currency_id' => 'nullable|tenant_exists:currencies,id',
        ]);

        try {
            $payment->update([
                'reference_no' => $validated['reference_no'],
                'description' => $validated['description'],
                'comment' => $validated['comment'],
                'currency_id' => $validated['currency_id'],
                'updated_by' => auth()->id(),
            ]);

            return redirect()
                ->route('payments.show', $payment)
                ->with('success', 'Payment updated successfully.');

        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to update payment: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Payment $payment)
    {
        $this->authorize('delete', $payment);

        DB::beginTransaction();
        try {
            // Reverse the payment from the paymentable
            if ($payment->paymentable) {
                $payment->paymentable->decrement('amount_paid', $payment->amount);
            }

            $payment->delete();

            DB::commit();

            return redirect()
                ->route('payments.index')
                ->with('success', 'Payment deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()
                ->with('error', 'Failed to delete payment: ' . $e->getMessage());
        }
    }

    /**
     * Apply filters to payment query
     */
    private function applyFilters($query, Request $request)
    {
        // Search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                  ->orWhere('comment', 'like', "%{$search}%")
                  ->orWhere('reference_no', 'like', "%{$search}%")
                  ->orWhere('amount', 'like', "%{$search}%");
            });
        }

        // Payment type filter
        if ($request->filled('payment_type')) {
            $query->where('paymentable_type', $request->payment_type);
        }

        // Date range filters
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Amount range filters
        if ($request->filled('amount_from')) {
            $query->where('amount', '>=', $request->amount_from);
        }

        if ($request->filled('amount_to')) {
            $query->where('amount', '<=', $request->amount_to);
        }

        // Currency filter
        if ($request->filled('currency_id')) {
            $query->where('currency_id', $request->currency_id);
        }
    }

    /**
     * Calculate payment analytics
     */
    private function calculatePaymentAnalytics(Request $request)
    {
        $baseQuery = Payment::query();

        // Apply same filters as main query
        $this->applyFilters($baseQuery, $request);

        return [
            'total_payments' => $baseQuery->count(),
            'total_amount' => $baseQuery->sum('amount'),
            'avg_payment' => $baseQuery->avg('amount'),
            'payments_today' => Payment::whereDate('created_at', today())->count(),
            'amount_today' => Payment::whereDate('created_at', today())->sum('amount'),
            'payments_this_month' => Payment::whereMonth('created_at', now()->month)->count(),
            'amount_this_month' => Payment::whereMonth('created_at', now()->month)->sum('amount'),
        ];
    }

    /**
     * Get filter options for dropdowns
     */
    private function getFilterOptions()
    {
        return [
            'payment_types' => [
                'receivable' => 'Receivables (Customer Payments)',
                'payable' => 'Payables (Supplier Payments)',
            ],
            'currencies' => Currency::orderBy('name')->get(),
        ];
    }

    /**
     * Get customer invoices for AJAX
     */
    public function getCustomerInvoices(Request $request)
    {
        $request->validate([
            'customer_id' => 'required|tenant_exists:customers,id',
        ]);

        $invoices = Invoice::with('customer')
                          ->where('customer_id', $request->customer_id)
                          ->where('balance', '>', 0)
                          ->when(!auth()->user()->isSuperAdmin(), function($query) {
                              $query->where('branch_id', auth()->user()->branch_id);
                          })
                          ->orderBy('created_at', 'desc')
                          ->get();

        return response()->json([
            'invoices' => $invoices->map(function($invoice) {
                return [
                    'id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number ?? '#' . $invoice->id,
                    'date' => $invoice->created_at->format('M d, Y'),
                    'total_amount' => $invoice->amount_total,
                    'paid_amount' => $invoice->amount_paid,
                    'balance' => $invoice->balance,
                    'currency' => $invoice->currency->code ?? 'USD',
                ];
            })
        ]);
    }

    /**
     * Get supplier bills/invoices for AJAX
     */
    public function getSupplierBills(Request $request)
    {
        $request->validate([
            'supplier_id' => 'required|tenant_exists:suppliers,id',
        ]);

        // Get supplier bills (purchase orders or supplier invoices)
        $bills = Order::with('supplier')
                     ->where('supplier_id', $request->supplier_id)
                     ->where('balance', '>', 0)
                     ->when(!auth()->user()->isSuperAdmin(), function($query) {
                         $query->where('branch_id', auth()->user()->branch_id);
                     })
                     ->orderBy('created_at', 'desc')
                     ->get();

        return response()->json([
            'bills' => $bills->map(function($bill) {
                return [
                    'id' => $bill->id,
                    'bill_number' => $bill->order_number ?? '#' . $bill->id,
                    'date' => $bill->created_at->format('M d, Y'),
                    'total_amount' => $bill->amount_total,
                    'paid_amount' => $bill->amount_paid,
                    'balance' => $bill->balance,
                    'currency' => $bill->currency->code ?? 'USD',
                ];
            })
        ]);
    }

    /**
     * Get invoice details for payment
     */
    public function getInvoiceDetails(Request $request)
    {
        $request->validate([
            'invoice_id' => 'required|integer',
            'payment_type' => 'required|in:receivable,payable'
        ]);

        if ($request->payment_type === 'receivable') {
            // Get invoice details for customer payment
            $invoice = Invoice::with(['customer', 'currency'])->find($request->invoice_id);

            if (!$invoice) {
                return response()->json(['error' => 'Invoice not found'], 404);
            }

            return response()->json([
                'id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number ?? '#' . $invoice->id,
                'customer' => $invoice->customer->name ?? 'N/A',
                'date' => $invoice->created_at->format('M d, Y'),
                'total_amount' => $invoice->amount_total,
                'paid_amount' => $invoice->amount_paid,
                'balance' => $invoice->balance,
                'currency' => $invoice->currency->code ?? 'USD',
            ]);
        } else {
            // Get order details for supplier payment
            $order = Order::with(['supplier', 'currency'])->find($request->invoice_id);

            if (!$order) {
                return response()->json(['error' => 'Order not found'], 404);
            }

            return response()->json([
                'id' => $order->id,
                'invoice_number' => $order->order_number ?? '#' . $order->id,
                'customer' => $order->supplier->name ?? 'N/A',
                'date' => $order->created_at->format('M d, Y'),
                'total_amount' => $order->amount_total,
                'paid_amount' => $order->amount_paid,
                'balance' => $order->balance,
                'currency' => $order->currency->code ?? 'USD',
            ]);
        }
    }
}
