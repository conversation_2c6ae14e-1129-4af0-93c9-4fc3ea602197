@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-plus-circle text-primary"></i> New Loan Application
            </h1>
            <p class="text-muted mb-0">Create a new employee loan application</p>
        </div>
        <div>
            <a href="{{ route('employee-loans.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Loans
            </a>
        </div>
    </div>

    <form action="{{ route('employee-loans.store') }}" method="POST" id="loanForm" novalidate>
        @csrf

        <div class="row">
            <!-- Left Column -->
            <div class="col-lg-8">
                <!-- Loan Information Card -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-gradient-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i> Loan Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="employee_id" class="form-label">
                                        Employee <span class="text-danger">*</span>
                                    </label>
                                    <select name="employee_id" id="employee_id"
                                            class="form-select @error('employee_id') is-invalid @enderror" required>
                                        <option value="">Select Employee</option>
                                        @foreach($employees as $employee)
                                            <option value="{{ $employee->id }}"
                                                    {{ old('employee_id') == $employee->id ? 'selected' : '' }}
                                                    data-email="{{ $employee->email }}"
                                                    data-basic-pay="{{ $employee->basic_pay }}">
                                                {{ $employee->name }} - K{{ number_format($employee->basic_pay, 2) }}/month
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('employee_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Select the employee applying for the loan</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="loan_amount" class="form-label">
                                        Loan Amount (K) <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" name="loan_amount" id="loan_amount"
                                           class="form-control @error('loan_amount') is-invalid @enderror"
                                           value="{{ old('loan_amount') }}" step="0.01" min="1" max="10000000" required>
                                    @error('loan_amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Maximum recommended: <span id="max-recommended">-</span></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="installment_amount" class="form-label">
                                        Monthly Installment (K) <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" name="installment_amount" id="installment_amount"
                                           class="form-control @error('installment_amount') is-invalid @enderror"
                                           value="{{ old('installment_amount') }}" step="0.01" min="1" required>
                                    @error('installment_amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Maximum recommended: <span id="max-installment">-</span></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="total_installments" class="form-label">Total Installments</label>
                                    <input type="number" id="total_installments" class="form-control" readonly>
                                    <div class="form-text">Calculated automatically based on loan amount and monthly payment</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="loan_date" class="form-label">
                                        Application Date <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" name="loan_date" id="loan_date"
                                           class="form-control @error('loan_date') is-invalid @enderror"
                                           value="{{ old('loan_date', date('Y-m-d')) }}" max="{{ date('Y-m-d') }}" required>
                                    @error('loan_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_deduction_date" class="form-label">
                                        First Deduction Date <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" name="first_deduction_date" id="first_deduction_date"
                                           class="form-control @error('first_deduction_date') is-invalid @enderror"
                                           value="{{ old('first_deduction_date') }}" min="{{ date('Y-m-d', strtotime('+1 day')) }}" required>
                                    @error('first_deduction_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">When should the first deduction be made from payroll?</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="purpose" class="form-label">
                                Loan Purpose <span class="text-danger">*</span>
                            </label>
                            <textarea name="purpose" id="purpose"
                                      class="form-control @error('purpose') is-invalid @enderror"
                                      rows="3" maxlength="1000" required>{{ old('purpose') }}</textarea>
                            @error('purpose')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Describe the purpose of this loan (max 1000 characters)</div>
                        </div>
                    </div>
                </div>

                <!-- Optional Information Card -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog"></i> Optional Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="interest_rate" class="form-label">Interest Rate (%)</label>
                                    <input type="number" name="interest_rate" id="interest_rate"
                                           class="form-control @error('interest_rate') is-invalid @enderror"
                                           value="{{ old('interest_rate', 0) }}" step="0.01" min="0" max="100">
                                    @error('interest_rate')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Leave as 0 for interest-free loans</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="interest_type" class="form-label">Interest Type</label>
                                    <select name="interest_type" id="interest_type"
                                            class="form-select @error('interest_type') is-invalid @enderror">
                                        <option value="simple" {{ old('interest_type') === 'simple' ? 'selected' : '' }}>Simple Interest</option>
                                        <option value="compound" {{ old('interest_type') === 'compound' ? 'selected' : '' }}>Compound Interest</option>
                                    </select>
                                    @error('interest_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="terms_conditions" class="form-label">Terms and Conditions</label>
                            <textarea name="terms_conditions" id="terms_conditions"
                                      class="form-control @error('terms_conditions') is-invalid @enderror"
                                      rows="4" maxlength="2000">{{ old('terms_conditions') }}</textarea>
                            @error('terms_conditions')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Additional terms and conditions for this loan (max 2000 characters)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Summary -->
            <div class="col-lg-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-calculator"></i> Loan Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="loan-summary">
                            <div class="row mb-3">
                                <div class="col-6"><strong>Employee:</strong></div>
                                <div class="col-6" id="summary-employee">-</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-6"><strong>Loan Amount:</strong></div>
                                <div class="col-6" id="summary-amount">K0.00</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-6"><strong>Monthly Payment:</strong></div>
                                <div class="col-6" id="summary-installment">K0.00</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-6"><strong>Total Payments:</strong></div>
                                <div class="col-6" id="summary-installments">0</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-6"><strong>Duration:</strong></div>
                                <div class="col-6" id="summary-duration">0 months</div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-6"><strong>Expected Completion:</strong></div>
                                <div class="col-6" id="summary-completion">-</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Guidelines Card -->
                <div class="card shadow-sm">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-exclamation-triangle"></i> Loan Guidelines
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i>
                                Maximum loan: 6x monthly salary
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i>
                                Maximum deduction: 30% of salary
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i>
                                One active loan per employee
                            </li>
                            <li class="mb-0">
                                <i class="fas fa-check text-success"></i>
                                Automatic payroll deduction
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('employee-loans.index') }}" class="btn btn-light">
                                <i class="fas fa-arrow-left"></i> Back to Loans
                            </a>
                            <div>
                                <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                                    <i class="fas fa-undo"></i> Reset Form
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Submit Application
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const employeeSelect = document.getElementById('employee_id');
    const loanAmountInput = document.getElementById('loan_amount');
    const installmentInput = document.getElementById('installment_amount');
    const firstDeductionInput = document.getElementById('first_deduction_date');

    // Update recommendations when employee changes
    employeeSelect.addEventListener('change', updateRecommendations);
    loanAmountInput.addEventListener('input', updateSummary);
    installmentInput.addEventListener('input', updateSummary);
    firstDeductionInput.addEventListener('change', updateSummary);

    function updateRecommendations() {
        const selectedOption = employeeSelect.options[employeeSelect.selectedIndex];
        if (selectedOption.value) {
            const basicPay = parseFloat(selectedOption.dataset.basicPay) || 0;
            const maxLoan = basicPay * 6;
            const maxInstallment = basicPay * 0.3;

            document.getElementById('max-recommended').textContent = `K${maxLoan.toLocaleString()}`;
            document.getElementById('max-installment').textContent = `K${maxInstallment.toLocaleString()}`;

            updateSummary();
        } else {
            document.getElementById('max-recommended').textContent = '-';
            document.getElementById('max-installment').textContent = '-';
        }
    }

    function updateSummary() {
        const selectedOption = employeeSelect.options[employeeSelect.selectedIndex];
        const loanAmount = parseFloat(loanAmountInput.value) || 0;
        const installmentAmount = parseFloat(installmentInput.value) || 0;
        const firstDeductionDate = firstDeductionInput.value;

        // Update employee name
        document.getElementById('summary-employee').textContent = selectedOption.text || '-';

        // Update amounts
        document.getElementById('summary-amount').textContent = `K${loanAmount.toLocaleString()}`;
        document.getElementById('summary-installment').textContent = `K${installmentAmount.toLocaleString()}`;

        // Calculate installments
        const totalInstallments = installmentAmount > 0 ? Math.ceil(loanAmount / installmentAmount) : 0;
        document.getElementById('total_installments').value = totalInstallments;
        document.getElementById('summary-installments').textContent = totalInstallments;
        document.getElementById('summary-duration').textContent = `${totalInstallments} months`;

        // Calculate completion date
        if (firstDeductionDate && totalInstallments > 0) {
            const startDate = new Date(firstDeductionDate);
            const completionDate = new Date(startDate);
            completionDate.setMonth(completionDate.getMonth() + totalInstallments - 1);
            document.getElementById('summary-completion').textContent = completionDate.toLocaleDateString();
        } else {
            document.getElementById('summary-completion').textContent = '-';
        }
    }

    // Initialize
    updateRecommendations();
});

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All entered data will be lost.')) {
        document.getElementById('loanForm').reset();
        updateRecommendations();
    }
}
</script>
@endpush
@endsection
