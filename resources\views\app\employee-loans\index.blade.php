@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <x-page-header title="Employee Loans" />

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Loan Management</h5>
                    @can('create', App\Models\EmployeeLoan::class)
                        <a href="{{ route('employee-loans.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> New Loan Application
                        </a>
                    @endcan
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="{{ route('employee-loans.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" name="search" class="form-control" placeholder="Search loans..." value="{{ $search }}">
                            </div>
                            <div class="col-md-2">
                                <select name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="pending" {{ $status == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="approved" {{ $status == 'approved' ? 'selected' : '' }}>Approved</option>
                                    <option value="active" {{ $status == 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="completed" {{ $status == 'completed' ? 'selected' : '' }}>Completed</option>
                                    <option value="cancelled" {{ $status == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="employee_id" class="form-control">
                                    <option value="">All Employees</option>
                                    @foreach($employees as $employee)
                                        <option value="{{ $employee->id }}" {{ $employee_id == $employee->id ? 'selected' : '' }}>
                                            {{ $employee->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-secondary">Filter</button>
                                <a href="{{ route('employee-loans.index') }}" class="btn btn-outline-secondary">Clear</a>
                            </div>
                        </div>
                    </form>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h6>Total Loans</h6>
                                    <h4 id="total-loans">{{ $loans->total() }}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h6>Pending Approval</h6>
                                    <h4 id="pending-loans">{{ $loans->where('status', 'pending')->count() }}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h6>Active Loans</h6>
                                    <h4 id="active-loans">{{ $loans->where('status', 'active')->count() }}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h6>Completed</h6>
                                    <h4 id="completed-loans">{{ $loans->where('status', 'completed')->count() }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Loans Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Reference</th>
                                    <th>Employee</th>
                                    <th>Loan Amount</th>
                                    <th>Installment</th>
                                    <th>Remaining</th>
                                    <th>Progress</th>
                                    <th>Status</th>
                                    <th>Loan Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($loans as $loan)
                                    <tr>
                                        <td>
                                            <a href="{{ route('employee-loans.show', $loan) }}" class="text-decoration-none">
                                                {{ $loan->loan_reference }}
                                            </a>
                                        </td>
                                        <td>{{ $loan->employee->name }}</td>
                                        <td>{{ number_format($loan->loan_amount, 2) }}</td>
                                        <td>{{ number_format($loan->installment_amount, 2) }}</td>
                                        <td>{{ number_format($loan->remaining_balance, 2) }}</td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: {{ $loan->progress_percentage }}%"
                                                     aria-valuenow="{{ $loan->progress_percentage }}" 
                                                     aria-valuemin="0" aria-valuemax="100">
                                                    {{ $loan->progress_percentage }}%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge badge-{{ $loan->status == 'active' ? 'info' : ($loan->status == 'completed' ? 'success' : ($loan->status == 'pending' ? 'warning' : ($loan->status == 'approved' ? 'primary' : 'danger'))) }}">
                                                {{ ucfirst($loan->status) }}
                                            </span>
                                        </td>
                                        <td>{{ $loan->loan_date->format('M d, Y') }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                @can('view', $loan)
                                                    <a href="{{ route('employee-loans.show', $loan) }}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                @endcan
                                                
                                                @can('update', $loan)
                                                    @if($loan->status == 'pending')
                                                        <a href="{{ route('employee-loans.edit', $loan) }}" class="btn btn-sm btn-outline-secondary">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    @endif
                                                @endcan
                                                
                                                @can('approve', $loan)
                                                    @if($loan->status == 'pending')
                                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                                onclick="approveLoan({{ $loan->id }})">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    @endif
                                                @endcan
                                                
                                                @can('cancel', $loan)
                                                    @if(in_array($loan->status, ['pending', 'approved']))
                                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                onclick="cancelLoan({{ $loan->id }})">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    @endif
                                                @endcan
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center">No loans found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {{ $loans->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Approve Loan</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="approvalForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="approval_notes">Approval Notes (Optional)</label>
                        <textarea name="approval_notes" id="approval_notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Approve Loan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Cancellation Modal -->
<div class="modal fade" id="cancellationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Loan</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="cancellationForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="cancellation_reason">Cancellation Reason *</label>
                        <textarea name="cancellation_reason" id="cancellation_reason" class="form-control" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-danger">Cancel Loan</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function approveLoan(loanId) {
    const form = document.getElementById('approvalForm');
    form.action = `/employee-loans/${loanId}/approve`;
    $('#approvalModal').modal('show');
}

function cancelLoan(loanId) {
    const form = document.getElementById('cancellationForm');
    form.action = `/employee-loans/${loanId}/cancel`;
    $('#cancellationModal').modal('show');
}
</script>
@endpush
@endsection
