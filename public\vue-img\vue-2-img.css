/*!
 * vue-2-img v0.0.6
 * (c) 2020 <PERSON>
 * Released under the MIT License.
 */

.vti__progressCapture{opacity:0;transition:opacity 0.5s ease}.vti__active .vti__progressCapture{position:fixed;top:0;left:0;width:100vw;height:100vh;background:linear-gradient(90deg, #499ef5, #22dae3, #3de5b7, #adfbcc);z-index:99999;box-sizing:border-box;opacity:1}.vti__active .vti__progressCapture::before{content:" ";position:fixed;top:0;left:0;height:100%;width:100%;z-index:9999;box-sizing:border-box;border-radius:65px;border:45px #fafafa solid;box-shadow:0px 0px 0px 30px #fafafa}.vti__active .vti__progressCapture::after{content:"Rendering Document";position:fixed;top:0;left:0;z-index:999999;height:100%;width:100%;color:white;font-size:45px;padding-top:45vh;text-align:center;font-family:Helvetica, Arial;white-space:pre;animation-duration:5s;animation-iteration-count:infinite;animation-timing-function:linear;animation-name:vti__loadingMessage}.vti__active .vti__progressCapture .vti__progressBar{position:absolute;top:0;right:0;width:100%;height:100%;background:#fafafa;transition:width 0.5s ease}.vti__active .vti__hidden{display:none !important}.vti__active .vti__show{display:inherit !important}.vti__hidden{display:inherit !important}.vti__show{display:none !important}@keyframes vti__loadingMessage{0%,100%{content:"Rendering Document \A   "}25%{content:"Rendering Document \A   ."}50%{content:"Rendering Document \A   .."}75%{content:"Rendering Document \A   ..."}}
