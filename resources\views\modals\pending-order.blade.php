<form action="" class="order-approval-form" method="POST" id="approve-order-el">
	@csrf
	<x-lg-modal>
		<x-slot name="class">pending-order-modal</x-slot>
		<x-slot name="title">Purchase Order</x-slot>

	    <div class="" v-if="order">
	    	<textarea name="description" class="form-control" placeholder="Comment ..." required></textarea> <br>
	    	<p> Please provide comment.</p>
	    	<div class="card">
	    		<div class="card-body">
	    			<div>Order Number: <span class="text-info" v-text="order.order_id"></span></div>
	    			<div>Order Total Amount: <span class="text-info"  v-text="order.amount_total"></span></div>
	    		</div>
	    	</div>
	     </div>
	     <div v-else>
	     		<h1 class="center">Loading...</h1>
	     </div>

		<x-slot name="footer">
			<div v-if="order" style="width: 100%;">
				<div v-if="order?.status_id != 13">
					<button type="submit" value="10" name="status_id" class="btn btn-primary mr-4"> Approve </button>
					<!-- <button type="submit" value="12" name="status_id" class="btn btn-danger mr-4"> Reject </button> -->
				</div>
				@if( auth()->user()->can('cancel orders'))
				<button type="submit" value="13" name="status_id" class="btn btn-danger" style="float: right;"> Cancel Order</button>
				@endif
			</div>
		</x-slot>
	</x-lg-modal>
</form>




@push('scripts')

  <script type="text/javascript">
    
    new Vue({
      el: "#approve-order-el",
      data(){
        return{
          order: null,
        }
      },

      methods: {

        getOrder(id) {
          axios.get('/ajax-order/' + id).then( res => {
          	this.order = res.data;
          	$(".order-approval-form").attr("action", "/approve-order/" + id);
          	console.log(this.order)
          }) 
        },

      },


      created(){
      	$("body").on("click", ".pending-order-btn", (e) => {
      		var id = $(e.target).attr("data-id");
      		this.getOrder(id);
      	})
      }

    });

  </script>

@endpush
