<?php

namespace App\Traits;

use Spatie\Multitenancy\Models\Tenant;

trait UsesTenantConnection
{
    /**
     * Boot the trait and set the tenant connection.
     */
    protected static function bootUsesTenantConnection()
    {
        static::creating(function ($model) {
            // Ensure a tenant is current before creating
            if (! Tenant::current()) {
                throw new \Exception('No tenant is currently set.');
            }
        });

        // Set the connection to tenant
        static::addGlobalScope('tenant', function ($builder) {
            $builder->setConnection('tenant');
        });
    }

    /**
     * Initialize the trait.
     */
    protected function initializeUsesTenantConnection()
    {
        $this->connection = 'tenant';
    }
}