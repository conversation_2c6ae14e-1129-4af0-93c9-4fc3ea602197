@extends('layouts.app')

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <x-page-header>
        <x-slot name="icon">credit-card</x-slot>
        <x-slot name="title">Record Payment</x-slot>
        <x-slot name="subtitle">Record a new payment for invoices or orders</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('payments.index') }}">Payments</a></li>
            <li class="breadcrumb-item active" aria-current="page">Record Payment</li>
        </x-slot>
        <x-slot name="controls">
            <a href="{{ route('payments.index') }}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-left me-1"></i>Back to Payments
            </a>
        </x-slot>
    </x-page-header>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-credit-card me-2"></i>Payment Information
                    </h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('payments.store') }}" method="POST" id="paymentForm">
                        @csrf

                        <!-- Payment Type Selection -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <label class="form-label">Payment Type <span class="text-danger">*</span></label>
                                <div class="tom-select-custom">
                                    <select class="js-select form-select" name="payment_type" id="paymentType"
                                            autocomplete="off" data-hs-tom-select-options='{"placeholder": "Select payment type..."}' required>
                                        <option value="">Select Payment Type</option>
                                        <option value="receivable" {{ old('payment_type') == 'receivable' ? 'selected' : '' }}>
                                            Receivable (Customer Payment)
                                        </option>
                                        <option value="payable" {{ old('payment_type') == 'payable' ? 'selected' : '' }}>
                                            Payable (Supplier Payment)
                                        </option>
                                    </select>
                                </div>
                                @error('payment_type')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror

                                @if(config('app.debug'))
                                <!-- Debug buttons (only in debug mode) -->
                                <div class="mt-2">
                                    <small class="text-muted">Debug:</small>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="testReceivable()">Test Receivable</button>
                                    <button type="button" class="btn btn-sm btn-outline-warning" onclick="testPayable()">Test Payable</button>
                                </div>
                                @endif
                            </div>
                        </div>

                        <!-- Customer Selection -->
                        <div class="row mb-4" id="customerSection" style="display: none;">
                            <div class="col-md-12">
                                <label class="form-label">Customer <span class="text-danger">*</span></label>
                                <div class="tom-select-custom">
                                    <select class="js-select form-select" name="customer_id" id="customerId"
                                            autocomplete="off" data-hs-tom-select-options='{"placeholder": "Select customer..."}'>
                                        <option value="">Select Customer</option>
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer->id }}" {{ old('customer_id') == $customer->id ? 'selected' : '' }}>
                                                {{ $customer->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                @error('customer_id')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Supplier Selection -->
                        <div class="row mb-4" id="supplierSection" style="display: none;">
                            <div class="col-md-12">
                                <label class="form-label">Supplier <span class="text-danger">*</span></label>
                                <div class="tom-select-custom">
                                    <select class="js-select form-select" name="supplier_id" id="supplierId"
                                            autocomplete="off" data-hs-tom-select-options='{"placeholder": "Select supplier..."}'>
                                        <option value="">Select Supplier</option>
                                        @foreach($suppliers as $supplier)
                                            <option value="{{ $supplier->id }}" {{ old('supplier_id') == $supplier->id ? 'selected' : '' }}>
                                                {{ $supplier->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                @error('supplier_id')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Invoice Selection -->
                        <div class="row mb-4" id="invoiceSection" style="display: none;">
                            <div class="col-md-12">
                                <label class="form-label">Select Invoice <span class="text-danger">*</span></label>
                                <div class="tom-select-custom">
                                    <select class="js-select form-select" name="invoice_id" id="invoiceId"
                                            autocomplete="off" data-hs-tom-select-options='{"placeholder": "Select invoice..."}' required>
                                        <option value="">Select Invoice</option>
                                    </select>
                                </div>
                                @error('invoice_id')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Payment Details Card -->
                        <div class="card bg-light mb-4" id="paymentDetailsCard" style="display: none;">
                            <div class="card-body">
                                <h6 class="card-title">Invoice Details</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <strong><span id="customerSupplierLabel">Customer</span>:</strong> <span id="customerName">-</span>
                                        </div>
                                        <div class="mb-2">
                                            <strong>Invoice Total:</strong> <span id="totalAmount">-</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <strong>Amount Paid:</strong> <span id="paidAmount">-</span>
                                        </div>
                                        <div class="mb-2">
                                            <strong>Outstanding Balance:</strong> <span id="outstandingBalance" class="text-danger fw-bold">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Amount -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">Payment Amount <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control @error('amount') is-invalid @enderror" 
                                           name="amount" id="paymentAmount" step="0.01" min="0.01" 
                                           value="{{ old('amount') }}" placeholder="0.00" required>
                                </div>
                                @error('amount')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Maximum: <span id="maxAmount">-</span></small>
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">Currency</label>
                                <div class="tom-select-custom">
                                    <select class="js-select form-select" name="currency_id" autocomplete="off" 
                                            data-hs-tom-select-options='{"placeholder": "Select currency..."}'>
                                        <option value="">Default Currency</option>
                                        @foreach($currencies as $currency)
                                            <option value="{{ $currency->id }}" {{ old('currency_id') == $currency->id ? 'selected' : '' }}>
                                                {{ $currency->name }} ({{ $currency->code }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                @error('currency_id')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Reference and Description -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">Reference Number</label>
                                <input type="text" class="form-control @error('reference_no') is-invalid @enderror" 
                                       name="reference_no" value="{{ old('reference_no') }}" 
                                       placeholder="Payment reference number">
                                @error('reference_no')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">Payment Method</label>
                                <div class="tom-select-custom">
                                    <select class="js-select form-select" name="payment_method" autocomplete="off" 
                                            data-hs-tom-select-options='{"placeholder": "Select payment method..."}'>
                                        <option value="">Select Method</option>
                                        <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>Cash</option>
                                        <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                                        <option value="credit_card" {{ old('payment_method') == 'credit_card' ? 'selected' : '' }}>Credit Card</option>
                                        <option value="debit_card" {{ old('payment_method') == 'debit_card' ? 'selected' : '' }}>Debit Card</option>
                                        <option value="check" {{ old('payment_method') == 'check' ? 'selected' : '' }}>Check</option>
                                        <option value="mobile_payment" {{ old('payment_method') == 'mobile_payment' ? 'selected' : '' }}>Mobile Payment</option>
                                        <option value="other" {{ old('payment_method') == 'other' ? 'selected' : '' }}>Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <label class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      name="description" rows="3" 
                                      placeholder="Payment description or notes">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Comments -->
                        <div class="mb-4">
                            <label class="form-label">Internal Comments</label>
                            <textarea class="form-control @error('comment') is-invalid @enderror" 
                                      name="comment" rows="2" 
                                      placeholder="Internal comments (not visible to customer)">{{ old('comment') }}</textarea>
                            @error('comment')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="{{ route('payments.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="bi bi-credit-card me-1"></i>Record Payment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let paymentTypeSelect = null;

$(document).ready(function() {
    console.log('Document ready, initializing payment form...');

    // Initialize Tom Select dropdowns with callback
    if (typeof HSCore !== 'undefined' && HSCore.components && HSCore.components.HSTomSelect) {
        HSCore.components.HSTomSelect.init('.js-select');
    }

    // Wait for Tom Select to initialize, then bind events
    setTimeout(function() {
        initializePaymentTypeHandlers();
        initializeCustomerSupplierHandlers();
        initializeInvoiceHandlers();
    }, 500);

    // Payment amount validation
    $('#paymentAmount').on('input', function() {
        const amount = parseFloat($(this).val());
        const maxAmount = parseFloat($('#maxAmount').text().replace(/[^0-9.-]+/g, ''));

        if (amount > maxAmount) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
            $(this).after('<div class="invalid-feedback">Amount cannot exceed outstanding balance</div>');
        } else {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        }
    });

    // Test the sections visibility on page load
    console.log('Customer section exists:', $('#customerSection').length);
    console.log('Supplier section exists:', $('#supplierSection').length);
    console.log('Invoice section exists:', $('#invoiceSection').length);
});

function initializePaymentTypeHandlers() {
    console.log('Initializing payment type handlers...');

    paymentTypeSelect = document.querySelector('#paymentType');

    if (paymentTypeSelect) {
        console.log('Payment type select found');

        // If Tom Select is initialized
        if (paymentTypeSelect.tomselect) {
            console.log('Tom Select found, binding to Tom Select events');
            paymentTypeSelect.tomselect.on('change', function(value) {
                console.log('Tom Select change event:', value);
                handlePaymentTypeChange(value);
            });
        } else {
            console.log('Tom Select not found, using regular change event');
            // Fallback to regular change event
            $(paymentTypeSelect).on('change', function() {
                console.log('Regular change event:', this.value);
                handlePaymentTypeChange(this.value);
            });
        }
    } else {
        console.error('Payment type select not found!');
    }
}

// Payment type change handler
function handlePaymentTypeChange(type) {
    if (!type) {
        type = paymentTypeSelect ? paymentTypeSelect.value : '';
    }

    console.log('Payment type changed to:', type);

    // Hide all sections first
    $('#customerSection, #supplierSection, #invoiceSection').hide();
    $('#paymentDetailsCard').hide();

    // Clear selections
    clearSelections();

    if (type === 'receivable') {
        console.log('Showing customer section');
        $('#customerSection').show();
        $('#customerSupplierLabel').text('Customer');
    } else if (type === 'payable') {
        console.log('Showing supplier section');
        $('#supplierSection').show();
        $('#customerSupplierLabel').text('Supplier');
    }
}

function clearSelections() {
    // Clear Tom Select instances if they exist
    const customerSelect = document.querySelector('#customerId');
    const supplierSelect = document.querySelector('#supplierId');
    const invoiceSelect = document.querySelector('#invoiceId');

    if (customerSelect && customerSelect.tomselect) {
        customerSelect.tomselect.clear();
    } else {
        $('#customerId').val('');
    }

    if (supplierSelect && supplierSelect.tomselect) {
        supplierSelect.tomselect.clear();
    } else {
        $('#supplierId').val('');
    }

    if (invoiceSelect && invoiceSelect.tomselect) {
        invoiceSelect.tomselect.clear();
    } else {
        $('#invoiceId').val('');
    }
}

// Debug functions (global scope for button access)
function testReceivable() {
    console.log('Testing receivable...');
    if (paymentTypeSelect && paymentTypeSelect.tomselect) {
        paymentTypeSelect.tomselect.setValue('receivable');
    } else {
        $('#paymentType').val('receivable');
        handlePaymentTypeChange('receivable');
    }
}

function testPayable() {
    console.log('Testing payable...');
    if (paymentTypeSelect && paymentTypeSelect.tomselect) {
        paymentTypeSelect.tomselect.setValue('payable');
    } else {
        $('#paymentType').val('payable');
        handlePaymentTypeChange('payable');
    }
}

function initializeCustomerSupplierHandlers() {
    console.log('Initializing customer/supplier handlers...');

    // Customer selection handler
    const customerSelect = document.querySelector('#customerId');
    if (customerSelect) {
        if (customerSelect.tomselect) {
            customerSelect.tomselect.on('change', function(value) {
                handleCustomerChange(value);
            });
        } else {
            $(customerSelect).on('change', function() {
                handleCustomerChange(this.value);
            });
        }
    }

    // Supplier selection handler
    const supplierSelect = document.querySelector('#supplierId');
    if (supplierSelect) {
        if (supplierSelect.tomselect) {
            supplierSelect.tomselect.on('change', function(value) {
                handleSupplierChange(value);
            });
        } else {
            $(supplierSelect).on('change', function() {
                handleSupplierChange(this.value);
            });
        }
    }
}

function initializeInvoiceHandlers() {
    console.log('Initializing invoice handlers...');

    const invoiceSelect = document.querySelector('#invoiceId');
    if (invoiceSelect) {
        if (invoiceSelect.tomselect) {
            invoiceSelect.tomselect.on('change', function(value) {
                handleInvoiceChange(value);
            });
        } else {
            $(invoiceSelect).on('change', function() {
                handleInvoiceChange(this.value);
            });
        }
    }
}

function handleCustomerChange(customerId) {
    console.log('Customer changed to:', customerId);

    if (customerId) {
        loadCustomerInvoices(customerId);
        $('#invoiceSection').show();
    } else {
        $('#invoiceSection').hide();
        $('#paymentDetailsCard').hide();
    }
}

function handleSupplierChange(supplierId) {
    console.log('Supplier changed to:', supplierId);

    if (supplierId) {
        loadSupplierBills(supplierId);
        $('#invoiceSection').show();
    } else {
        $('#invoiceSection').hide();
        $('#paymentDetailsCard').hide();
    }
}

function handleInvoiceChange(invoiceId) {
    console.log('Invoice changed to:', invoiceId);

    if (invoiceId) {
        loadInvoiceDetails(invoiceId);
    } else {
        $('#paymentDetailsCard').hide();
    }
}



    function loadCustomerInvoices(customerId) {
        $.get('{{ route("payments.customer-invoices") }}', {
            customer_id: customerId
        })
        .done(function(data) {
            const invoiceSelect = $('#invoiceId');
            invoiceSelect.empty().append('<option value="">Select Invoice</option>');

            if (data.invoices && data.invoices.length > 0) {
                data.invoices.forEach(function(invoice) {
                    invoiceSelect.append(
                        '<option value="' + invoice.id + '">' +
                        invoice.invoice_number + ' - ' + invoice.date +
                        ' (Balance: $' + parseFloat(invoice.balance).toFixed(2) + ')' +
                        '</option>'
                    );
                });
            } else {
                invoiceSelect.append('<option value="" disabled>No outstanding invoices found</option>');
            }
        })
        .fail(function() {
            alert('Failed to load customer invoices. Please try again.');
        });
    }

    function loadSupplierBills(supplierId) {
        $.get('{{ route("payments.supplier-bills") }}', {
            supplier_id: supplierId
        })
        .done(function(data) {
            const invoiceSelect = $('#invoiceId');
            invoiceSelect.empty().append('<option value="">Select Bill/Invoice</option>');

            if (data.bills && data.bills.length > 0) {
                data.bills.forEach(function(bill) {
                    invoiceSelect.append(
                        '<option value="' + bill.id + '">' +
                        bill.bill_number + ' - ' + bill.date +
                        ' (Balance: $' + parseFloat(bill.balance).toFixed(2) + ')' +
                        '</option>'
                    );
                });
            } else {
                invoiceSelect.append('<option value="" disabled>No outstanding bills found</option>');
            }
        })
        .fail(function() {
            alert('Failed to load supplier bills. Please try again.');
        });
    }

    function loadInvoiceDetails(invoiceId) {
        const paymentType = $('#paymentType').val();

        $.get('{{ route("payments.invoice-details") }}', {
            invoice_id: invoiceId,
            payment_type: paymentType
        })
        .done(function(data) {
            $('#customerName').text(data.customer);
            $('#totalAmount').text('$' + parseFloat(data.total_amount).toFixed(2));
            $('#paidAmount').text('$' + parseFloat(data.paid_amount).toFixed(2));
            $('#outstandingBalance').text('$' + parseFloat(data.balance).toFixed(2));
            $('#maxAmount').text('$' + parseFloat(data.balance).toFixed(2));

            // Set max amount for payment input
            $('#paymentAmount').attr('max', data.balance);

            $('#paymentDetailsCard').show();
        })
        .fail(function() {
            alert('Failed to load invoice details. Please try again.');
        });
    }

    // Pre-select if coming from specific customer/invoice
    @if($selectedCustomer)
        $('#paymentType').val('receivable').trigger('change');
        setTimeout(function() {
            $('#customerId').val('{{ $selectedCustomer->id }}').trigger('change');
        }, 100);
    @endif

    @if($selectedSupplier)
        $('#paymentType').val('payable').trigger('change');
        setTimeout(function() {
            $('#supplierId').val('{{ $selectedSupplier->id }}').trigger('change');
        }, 100);
    @endif

    @if($selectedInvoice)
        @if($selectedInvoice->customer_id)
            $('#paymentType').val('receivable').trigger('change');
            setTimeout(function() {
                $('#customerId').val('{{ $selectedInvoice->customer_id }}').trigger('change');
                setTimeout(function() {
                    $('#invoiceId').val('{{ $selectedInvoice->id }}').trigger('change');
                }, 200);
            }, 100);
        @endif
    @endif
});
</script>
@endpush
@endsection
