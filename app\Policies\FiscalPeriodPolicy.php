<?php

namespace App\Policies;

use App\Models\User;
use App\Models\FiscalPeriod;
use Illuminate\Auth\Access\HandlesAuthorization;

class FiscalPeriodPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo('list fiscal-periods');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\FiscalPeriod  $fiscalPeriod
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, FiscalPeriod $fiscalPeriod)
    {
        return $user->hasPermissionTo('view fiscal-periods');
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo('create fiscal-periods');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\FiscalPeriod  $fiscalPeriod
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, FiscalPeriod $fiscalPeriod)
    {
        return $user->hasPermissionTo('update fiscal-periods') && 
            $fiscalPeriod->status !== 'closed' && 
            $fiscalPeriod->fiscalYear->status !== 'closed';
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\FiscalPeriod  $fiscalPeriod
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, FiscalPeriod $fiscalPeriod)
    {
        return $user->hasPermissionTo('delete fiscal-periods') && 
            $fiscalPeriod->status !== 'closed' && 
            $fiscalPeriod->fiscalYear->status !== 'closed' && 
            $fiscalPeriod->journalEntries()->count() === 0;
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\FiscalPeriod  $fiscalPeriod
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, FiscalPeriod $fiscalPeriod)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\FiscalPeriod  $fiscalPeriod
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, FiscalPeriod $fiscalPeriod)
    {
        return false;
    }

    /**
     * Determine whether the user can close the fiscal period.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\FiscalPeriod  $fiscalPeriod
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function close(User $user, FiscalPeriod $fiscalPeriod)
    {
        return $user->hasPermissionTo('close fiscal-periods') && 
            $fiscalPeriod->status === 'open' && 
            $fiscalPeriod->fiscalYear->status !== 'closed';
    }

    /**
     * Determine whether the user can reopen the fiscal period.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\FiscalPeriod  $fiscalPeriod
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function reopen(User $user, FiscalPeriod $fiscalPeriod)
    {
        return $user->hasPermissionTo('reopen fiscal-periods') && 
            $fiscalPeriod->status === 'closed' && 
            $fiscalPeriod->fiscalYear->status !== 'closed';
    }
}
