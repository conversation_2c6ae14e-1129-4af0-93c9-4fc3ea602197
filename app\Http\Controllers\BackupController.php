<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class BackupController extends Controller
{

    public function resetDatabase(Request $request){
        \Artisan::call("migrate:fresh --seed ");
        return response(["message" => "Database Reset Successfully."]);
    }

    public function restoreDatabase(Request $request){
        // $sql = public_path('data.sql');

        $path = Storage::putFileAs('database', $request->database, 'database.sql' );
        $path = Storage::path($path);
        $db = [
            'username' => env('DB_USERNAME'),
            'password' => env('DB_PASSWORD'),
            'host' => env('DB_HOST'),
            'database' => env('DB_DATABASE')
        ];
  
        \Artisan::call("migrate:reset");

        exec("mysql --user={$db['username']} --password={$db['password']} --host={$db['host']} --database {$db['database']} < $path");

        return response(["message" => "Database Restored Successfully."]);
    }

}
