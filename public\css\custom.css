.dt-buttons{
	margin: 0 10px;
}

.dataTables_length,.dataTables_filter, .dataTables_info, .dataTables_paginate {
	display: inline-block;
	padding: 16px !important;
}

.table-dropdown-space{
	min-height: 300px;
}

.dataTables_filter, .dataTables_paginate{
	float: right;
}

._100{
	width: 100%;
}
._200px{
	min-width: 200px !important;
}

.transparent{
	opacity: 0.0;
}

/*.dt-button-collection div {
	width: 100px;
}*/

.dt-button-collection a.buttons-columnVisibility:before,
.dt-button-collection a.buttons-columnVisibility.active span:before {
	display:block;
	position:absolute;
	top:1.2em;
    left:0;
	width:12px;
	height:12px;
	box-sizing:border-box;
}

.dt-button-collection a.buttons-columnVisibility:before {
	content:' ';
	margin-top:-6px;
	margin-left:10px;
	border:1px solid black;
	border-radius:3px;
}

.dt-button-collection a.buttons-columnVisibility.active span:before {
	content:'\2714';
	margin-top:-11px;
	margin-left:12px;
	text-align:center;
	text-shadow:1px 1px #DDD, -1px -1px #DDD, 1px -1px #DDD, -1px 1px #DDD;
}

.dt-button-collection a.buttons-columnVisibility span {
    margin-left:20px;    
}