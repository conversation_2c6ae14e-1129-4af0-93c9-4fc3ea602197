<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BusinessTypeTrait;
use App\Traits\BranchTrait;
use DB;

class Invoice extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BusinessTypeTrait;
    use BranchTrait;

    protected $connection = "tenant";

    protected $fillable = [
        'date_from',
        'date_to',
        'sub_total',
        'amount_total',
        'amount_paid',
        'warehouse_id',
        'branch_id',
        'discount',
        'vat',
        'customer_id',
        'customer_name',
        'description',
        'created_by',
        'updated_by',
        'status_id',
        'approved_by',
        'business_type_id',
        'reference_no',
        'currency_id',

    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
        'closed_at' => 'datetime:Y-m-d',
        'date_from' => 'datetime:Y-m-d',
        'date_to' => 'datetime:Y-m-d',
    ];


    protected $appends = ['invoice_id', 'balance', 'products', 'prepared_by', 'quantity', 'amount_spend'];




    public function getProductsAttribute(){
        $products = $this->stockItems()->with("product")->select([
            "stock_items.*",
            DB::raw(" COUNT(*) AS quantity"),
            DB::raw(" SUM(discount) AS discount"),
            DB::raw(" SUM(selling_price) AS selling_price"),
            DB::raw(" SUM(selling_price) * COUNT(*) AS amount"),
        ])->groupBy("product_id")
        ->get();

        return $products;
    }


    public function getInvoiceIdAttribute(){
        return _pad( $this->id, 5, '0');
    }

    public function getQuantityAttribute(){
        return $this->sales->sum("unit_quantity");
    }

    public function getAmountSpendAttribute(){
        return $this->sales()->select(\DB::raw(" (buying_price * quantity) + ( buying_price * quantity * vat / 100 ) - discount AS amount"))->get()->sum("amount");
    }

    public function getPreparedByAttribute(){
        return $this->createdBy->name ?? '';
    }

    public function getBalanceAttribute(){
        return $this->amount_total - $this->amount_paid;
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function stockItems()
    {
        return $this->hasMany(StockItem::class);
    }


    public function payments()
    {
        return $this->morphMany(Payment::class, 'paymentable');
    }

    public function sales()
    {
        return $this->morphMany(Sale::class, 'saleable');
    }

    public function shipment()
    {
        return $this->hasOne(Shipment::class);
    }
}
