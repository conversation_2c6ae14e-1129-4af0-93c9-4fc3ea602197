<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Stock;

use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class StockStocksTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create(['email' => '<EMAIL>']);

        Sanctum::actingAs($user, [], 'web');

        $this->seed(\Database\Seeders\PermissionsSeeder::class);

        $this->withoutExceptionHandling();
    }

    /**
     * @test
     */
    public function it_gets_stock_stocks()
    {
        $stock = Stock::factory()->create();
        $stocks = Stock::factory()
            ->count(2)
            ->create([
                'stock_id' => $stock->id,
            ]);

        $response = $this->getJson(route('api.stocks.stocks.index', $stock));

        $response->assertOk()->assertSee($stocks[0]->description);
    }

    /**
     * @test
     */
    public function it_stores_the_stock_stocks()
    {
        $stock = Stock::factory()->create();
        $data = Stock::factory()
            ->make([
                'stock_id' => $stock->id,
            ])
            ->toArray();

        $response = $this->postJson(
            route('api.stocks.stocks.store', $stock),
            $data
        );

        unset($data['created_by']);
        unset($data['updated_by']);
        unset($data['status_id']);

        $this->assertDatabaseHas('stocks', $data);

        $response->assertStatus(201)->assertJsonFragment($data);

        $stock = Stock::latest('id')->first();

        $this->assertEquals($stock->id, $stock->stock_id);
    }
}
