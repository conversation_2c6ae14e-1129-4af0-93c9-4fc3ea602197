@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <x-page-header>
        <x-slot name="icon">receipt</x-slot>
        <x-slot name="title">Expense Report</x-slot>
        <x-slot name="subtitle">Track and analyze your business expenses</x-slot>
        <x-slot name="breadcrumbs">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="#">Reports</a></li>
            <li class="breadcrumb-item active" aria-current="page">Expenses</li>
        </x-slot>
        <x-slot name="controls">
            @if(auth()->user()->can('create expense'))
            <a href="#" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target=".expense-modal">
                <i class="bi bi-plus-circle me-1"></i> @lang('crud.common.create') Expense
            </a>
            @endif
        </x-slot>
    </x-page-header>
    <!-- End Page Header -->

    <!-- Stats Overview -->
    <div class="row mb-4">
        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-subtitle mb-2">Total Expenses</h6>
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="card-title text-inherit">{{ _money($debtors->sum('amount_total')) }}</h2>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-soft-primary text-primary p-2">
                                <i class="bi bi-receipt fs-3"></i>
                            </span>
                        </div>
                    </div>
                    <span class="badge bg-soft-secondary text-secondary">
                        {{ $debtors->count() }} transactions
                    </span>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-subtitle mb-2">Paid Expenses</h6>
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="card-title text-inherit">{{ _money($debtors->sum('amount_paid')) }}</h2>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-soft-success text-success p-2">
                                <i class="bi bi-check-circle fs-3"></i>
                            </span>
                        </div>
                    </div>
                    <span class="badge bg-soft-success text-success">
                        {{ number_format(($debtors->sum('amount_paid') / max($debtors->sum('amount_total'), 1)) * 100, 1) }}% of total
                    </span>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-subtitle mb-2">Pending Expenses</h6>
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="card-title text-inherit">{{ _money($debtors->sum('amount_total') - $debtors->sum('amount_paid')) }}</h2>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-soft-warning text-warning p-2">
                                <i class="bi bi-hourglass-split fs-3"></i>
                            </span>
                        </div>
                    </div>
                    <span class="badge bg-soft-warning text-warning">
                        {{ number_format((($debtors->sum('amount_total') - $debtors->sum('amount_paid')) / max($debtors->sum('amount_total'), 1)) * 100, 1) }}% of total
                    </span>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-lg-3 mb-3 mb-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-subtitle mb-2">Average Expense</h6>
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="card-title text-inherit">{{ _money($debtors->count() > 0 ? $debtors->sum('amount_total') / $debtors->count() : 0) }}</h2>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-soft-info text-info p-2">
                                <i class="bi bi-calculator fs-3"></i>
                            </span>
                        </div>
                    </div>
                    <span class="badge bg-soft-info text-info">
                        Per transaction
                    </span>
                </div>
            </div>
        </div>
    </div>
    <!-- End Stats Overview -->

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-header-title">Filter Expenses</h5>
        </div>
        <div class="card-body">
            <form id="filter" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Date Range</label>
                    <input type="hidden" name="from" value="{{ request()->from }}">
                    <input type="hidden" name="to" value="{{ request()->to }}">
                    <button id="js-daterangepicker-predefined" type="button" class="btn btn-white w-100">
                        <i class="bi-calendar-week me-1"></i>
                        <span class="js-daterangepicker-predefined-preview">Select date range</span>
                    </button>
                </div>

                <div class="col-md-3">
                    <label class="form-label">Branch</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select branch-filter" name="branch_id" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a branch..."
                        }'>
                            <option value="">All Branches</option>
                            @foreach(App\Models\Branch::get() as $branch)
                                <option value="{{ $branch->id }}" @if($branch->id == request()->branch_id) selected @endif>
                                    {{ $branch->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <label class="form-label">Category</label>
                    <div class="tom-select-custom">
                        <select class="js-select form-select category-filter" name="category_id" autocomplete="off" data-hs-tom-select-options='{
                            "placeholder": "Select a category..."
                        }'>
                            <option value="">All Categories</option>
                            @foreach(App\Models\Category::where("applied_to", "expenses")->get() as $category)
                                <option value="{{ $category->id }}" @if($category->id == request()->category_id) selected @endif>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-filter me-1"></i> Apply Filters
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- End Filters -->

    <!-- Table -->
    <div class="card card-table">
        <div class="card-header">
            <div class="row justify-content-between align-items-center flex-grow-1">
                <div class="col-md">
                    <h4 class="card-header-title">Expense Transactions</h4>
                </div>
                <div class="col-auto">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-download me-1"></i> Export
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="exportDropdown">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-excel me-1"></i> Excel</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-pdf me-1"></i> PDF</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-text me-1"></i> CSV</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Date</th>                 
                        <th>Reference No.</th>
                        <th>Branch</th>
                        <th>Category</th>
                        <th>Amount</th>                            
                        <th>Description</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($debtors as $debtor)
                    <tr>
                        <td>{{ $debtor->created_at->format('M d, Y') ?? '-' }}</td>
                        <td>
                            <a href="#" class="text-primary fw-semibold">{{ $debtor->debt_id ?? '-' }}</a>
                        </td>
                        <td>{{ $debtor->branch->name ?? '-' }}</td>
                        <td>
                            <span class="badge bg-soft-secondary">{{ $debtor->category ?? '-' }}</span>
                        </td>
                        <td>
                            <span class="fw-semibold">{{ _money($debtor->amount_total) }}</span>
                            @if($debtor->amount_total != $debtor->amount_paid)
                                <div class="mt-1">
                                    <span class="badge bg-success">Paid: {{ _money($debtor->amount_paid) }}</span>
                                </div>
                            @endif
                        </td>
                        <td>{{ Str::limit($debtor->description, 30) ?? '-' }}</td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <a href="#" class="btn btn-white btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="View Details">
                                    <i class="bi-eye"></i>
                                </a>
                                
                                @if(auth()->user()->can('update expense'))
                                <a href="#" class="btn btn-white btn-sm" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit">
                                    <i class="bi-pencil"></i>
                                </a>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center p-4">
                            <div class="d-flex flex-column align-items-center justify-content-center py-5">
                                <i class="bi bi-receipt text-muted" style="font-size: 3rem;"></i>
                                <h5 class="mt-4">No expenses found</h5>
                                <p class="text-muted">Try adjusting your search or filter to find what you're looking for.</p>
                                @if(auth()->user()->can('create expense'))
                                <a href="#" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target=".expense-modal">
                                    <i class="bi bi-plus-circle me-1"></i> Add New Expense
                                </a>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
                <tfoot>
                    <tr class="bg-light fw-bold">
                        <td colspan="4" class="text-end">Total:</td>
                        <td>
                            <span class="h5 text-primary">{{ _money($debtors->sum('amount_total')) }}</span>
                        </td>
                        <td colspan="2"></td>
                    </tr>
                    <tr class="bg-light fw-bold">
                        <td colspan="4" class="text-end">Total Paid:</td>
                        <td>
                            <span class="h5 text-success">{{ _money($debtors->sum('amount_paid')) }}</span>
                        </td>
                        <td colspan="2"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
        
        @if(isset($debtors) && method_exists($debtors, 'hasPages') && $debtors->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-center">
                {{ $debtors->links() }}
            </div>
        </div>
        @endif
    </div>
    <!-- End Table -->
</div>
@endsection

@push('scripts')
<script>
    $("document").ready(function () {
        dataTableBtn();
        
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
@endpush