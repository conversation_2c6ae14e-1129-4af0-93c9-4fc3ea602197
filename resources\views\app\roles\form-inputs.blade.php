@php $editing = isset($role) @endphp

<div class="row">
    <!-- Role Name Field -->
    <div class="col-sm-12 mb-4">
        <label for="name" class="form-label">Role Name</label>
        <div class="input-group">
            <span class="input-group-text">
                <i class="bi bi-person-badge"></i>
            </span>
            <input
                type="text"
                name="name"
                id="name"
                class="form-control @error('name') is-invalid @enderror"
                value="{{ old('name', ($editing ? $role->name : '')) }}"
                placeholder="Enter role name (e.g. Manager, Editor, Viewer)"
                required
                {{ $editing && $role->name == 'super-admin' ? 'readonly' : '' }}
            >
            @error('name')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <small class="form-text text-muted">
            Choose a descriptive name that reflects the role's function in the system.
        </small>
    </div>

    <!-- Permissions Section -->
    <div class="col-sm-12">
        <div class="mb-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-shield me-2"></i>Assign Permissions
                </h5>
                
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-secondary" id="select-all-permissions">Select All</button>
                    <button type="button" class="btn btn-outline-secondary" id="deselect-all-permissions">Deselect All</button>
                </div>
            </div>
            <p class="text-muted small">
                Select the permissions this role should have. Users with this role will be able to perform these actions.
            </p>
        </div>

        <!-- Group permissions by resource -->
        @php
            $groupedPermissions = $permissions->groupBy(function($permission) {
                // Better grouping logic
                $name = $permission->name;

                // Extract resource name from permission patterns
                if (preg_match('/^(view|create|update|delete|restore|force_delete)_(.+)$/', $name, $matches)) {
                    return ucfirst(str_replace('_', ' ', $matches[2]));
                } elseif (preg_match('/^(view_any)_(.+)$/', $name, $matches)) {
                    return ucfirst(str_replace('_', ' ', $matches[2]));
                } else {
                    // Fallback grouping
                    $parts = explode('_', $name);
                    return count($parts) > 1 ? ucfirst(str_replace('_', ' ', end($parts))) : 'General';
                }
            });

            // Sort groups alphabetically
            $groupedPermissions = $groupedPermissions->sortKeys();
        @endphp

        <div class="row">
            @foreach($groupedPermissions as $group => $perms)
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center py-3">
                        <h6 class="mb-0">
                            <i class="bi bi-folder me-2"></i>{{ $group }}
                            <span class="badge bg-soft-primary ms-2">{{ $perms->count() }}</span>
                        </h6>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm select-group-all"
                                    data-group="{{ Str::slug($group) }}" title="Select All {{ $group }}">
                                <i class="bi bi-check-all"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm deselect-group-all"
                                    data-group="{{ Str::slug($group) }}" title="Deselect All {{ $group }}">
                                <i class="bi bi-x-lg"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="permission-group" data-group="{{ Str::slug($group) }}">
                            @php
                                // Sort permissions within group by action type
                                $sortedPerms = $perms->sortBy(function($permission) {
                                    $actionOrder = ['view_any', 'view', 'create', 'update', 'delete', 'restore', 'force_delete'];
                                    $action = explode('_', $permission->name)[0];
                                    $index = array_search($action, $actionOrder);
                                    return $index !== false ? $index : 999;
                                });
                            @endphp

                            @foreach($sortedPerms as $permission)
                            <div class="form-check mb-2">
                                <input
                                    type="checkbox"
                                    id="permission{{ $permission->id }}"
                                    name="permissions[]"
                                    value="{{ $permission->id }}"
                                    class="form-check-input permission-checkbox group-{{ Str::slug($group) }}"
                                    {{ isset($role) && $role->hasPermissionTo($permission) ? 'checked' : '' }}
                                    {{ $editing && $role->name == 'super-admin' ? 'disabled' : '' }}
                                >
                                <label class="form-check-label d-flex justify-content-between align-items-center"
                                       for="permission{{ $permission->id }}">
                                    <span>
                                        @php
                                            $actionIcons = [
                                                'view_any' => 'bi-eye-fill text-info',
                                                'view' => 'bi-eye text-info',
                                                'create' => 'bi-plus-circle text-success',
                                                'update' => 'bi-pencil text-warning',
                                                'delete' => 'bi-trash text-danger',
                                                'restore' => 'bi-arrow-clockwise text-primary',
                                                'force_delete' => 'bi-x-circle text-danger'
                                            ];
                                            $action = explode('_', $permission->name)[0];
                                            $iconClass = $actionIcons[$action] ?? 'bi-shield text-muted';
                                        @endphp
                                        <i class="bi {{ $iconClass }} me-2"></i>
                                        {{ ucfirst(str_replace(['_', '-'], ' ', $permission->name)) }}
                                    </span>
                                    <small class="text-muted">{{ $action }}</small>
                                </label>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Global select all permissions
        document.getElementById('select-all-permissions').addEventListener('click', function() {
            document.querySelectorAll('.permission-checkbox:not([disabled])').forEach(function(checkbox) {
                checkbox.checked = true;
            });
            updateGroupSelectButtons();
        });

        // Global deselect all permissions
        document.getElementById('deselect-all-permissions').addEventListener('click', function() {
            document.querySelectorAll('.permission-checkbox:not([disabled])').forEach(function(checkbox) {
                checkbox.checked = false;
            });
            updateGroupSelectButtons();
        });

        // Group-specific select all
        document.querySelectorAll('.select-group-all').forEach(function(button) {
            button.addEventListener('click', function() {
                const group = this.getAttribute('data-group');
                document.querySelectorAll('.group-' + group + ':not([disabled])').forEach(function(checkbox) {
                    checkbox.checked = true;
                });
                updateGroupSelectButtons();
            });
        });

        // Group-specific deselect all
        document.querySelectorAll('.deselect-group-all').forEach(function(button) {
            button.addEventListener('click', function() {
                const group = this.getAttribute('data-group');
                document.querySelectorAll('.group-' + group + ':not([disabled])').forEach(function(checkbox) {
                    checkbox.checked = false;
                });
                updateGroupSelectButtons();
            });
        });

        // Update group button states when individual checkboxes change
        document.querySelectorAll('.permission-checkbox').forEach(function(checkbox) {
            checkbox.addEventListener('change', updateGroupSelectButtons);
        });

        // Function to update group select button states
        function updateGroupSelectButtons() {
            document.querySelectorAll('.permission-group').forEach(function(group) {
                const groupSlug = group.getAttribute('data-group');
                const checkboxes = group.querySelectorAll('.permission-checkbox:not([disabled])');
                const checkedBoxes = group.querySelectorAll('.permission-checkbox:not([disabled]):checked');

                const selectButton = document.querySelector('.select-group-all[data-group="' + groupSlug + '"]');
                const deselectButton = document.querySelector('.deselect-group-all[data-group="' + groupSlug + '"]');

                if (selectButton && deselectButton) {
                    if (checkedBoxes.length === 0) {
                        // None selected
                        selectButton.classList.remove('btn-primary');
                        selectButton.classList.add('btn-outline-primary');
                        deselectButton.classList.remove('btn-secondary');
                        deselectButton.classList.add('btn-outline-secondary');
                    } else if (checkedBoxes.length === checkboxes.length) {
                        // All selected
                        selectButton.classList.remove('btn-outline-primary');
                        selectButton.classList.add('btn-primary');
                        deselectButton.classList.remove('btn-outline-secondary');
                        deselectButton.classList.add('btn-secondary');
                    } else {
                        // Partially selected
                        selectButton.classList.remove('btn-primary');
                        selectButton.classList.add('btn-outline-primary');
                        deselectButton.classList.remove('btn-outline-secondary');
                        deselectButton.classList.add('btn-secondary');
                    }
                }
            });
        }

        // Initial state update
        updateGroupSelectButtons();
    });
</script>
@endpush
