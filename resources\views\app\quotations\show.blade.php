@extends('layouts.app')

@section('content')
    <!-- Content -->
    <div class="content container-fluid">
      <!-- Page Header -->
      <div class="page-header d-print-none">
        <div class="row align-items-center">
          <div class="col-sm mb-2 mb-sm-0">
            <nav aria-label="breadcrumb">
              <ol class="breadcrumb breadcrumb-no-gutter">
                <li class="breadcrumb-item">
                  <a class="breadcrumb-link" href="{{ route('quotations.index') }}">Quotations</a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">Quotation #{{ $quotation->quotation_id }}</li>
              </ol>
            </nav>
            <h1 class="page-header-title">
              <i class="bi-file-earmark-text me-2"></i>
              Quotation #{{ $quotation->quotation_id }}
              @php
                $statusClasses = [
                  '10' => 'bg-soft-secondary text-secondary',
                  '11' => 'bg-soft-info text-info',
                  '12' => 'bg-soft-success text-success',
                  '13' => 'bg-soft-danger text-danger',
                  '14' => 'bg-soft-warning text-warning'
                ];
                $statusNames = [
                  '10' => 'Draft',
                  '11' => 'Sent',
                  '12' => 'Accepted',
                  '13' => 'Rejected',
                  '14' => 'Expired'
                ];
                $statusClass = $statusClasses[$quotation->status_id] ?? 'bg-soft-secondary text-secondary';
                $statusName = $statusNames[$quotation->status_id] ?? 'Unknown';
              @endphp
              <span class="badge {{ $statusClass }} ms-2">{{ $statusName }}</span>
            </h1>
          </div>
          <!-- End Col -->

          <div class="col-sm-auto">
            <div class="d-flex gap-2">
              <a class="btn btn-outline-secondary" href="{{ route('quotations.index') }}">
                <i class="bi-arrow-left me-1"></i> Back
              </a>

              @if($quotation->status_id == '10' && !$quotation->approved_by)
                @can('update', $quotation)
                <form action="{{ route('quotations.convert-to-invoice', $quotation) }}" method="POST" class="d-inline">
                  @csrf
                  <button type="submit" class="btn btn-success" onclick="return confirm('Convert this quotation to invoice?')">
                    <i class="bi-receipt me-1"></i> Convert to Invoice
                  </button>
                </form>
                @endcan
              @endif

              @can('create', App\Models\Quotation::class)
              <a class="btn btn-primary" href="{{ route('quotations.create') }}">
                <i class="bi-plus-lg me-1"></i> New Quotation
              </a>
              @endcan
            </div>
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </div>
      <!-- End Page Header -->

      <div class="row">
        <div class="col-lg-8 mb-5 mb-lg-0">
          <!-- Card -->
          <div class="card card-lg mb-5">
            <div class="card-body quotation"  id="quotation">
              <div class="row justify-content-lg-between">
                <div class="col-sm quotation-2 quotation-sm-1 mb-3">
                  <div class="mb-2">
                    <img class="avatar" style="height:200px; width:200px" src="{{ auth()->user()->getGlobalInstance('logo')->path ?? asset('assets/svg/logos/logo-short.svg') }}" alt="Logo">
                  </div>

                  <h1 class="h2 text-primary">{{ auth()->user()->getGlobal("business") ?? ''}}</h1>
                </div>
                <!-- End Col -->

                <div class="col-sm-auto quotation-1 quotation-sm-2 text-sm-end mb-3">
                  <div class="mb-3">
                    <h2>Quotation #</h2>
                    <span class="d-block">{{ $quotation->quotation_id}}</span>
                  </div>

                  <address class="text-dark">
                    {{ $quotation->createdBy->name ?? ''}}<br>
                    {{ $quotation->createdBy->email ?? ''}}<br>
                    {{ $quotation->createdBy->address ?? ''}}<br>
                  </address>
                </div>
                <!-- End Col -->
              </div>
              <!-- End Row -->

              <div class="row justify-content-md-between mb-3">
                <div class="col-md">
                  <h4>Customer:</h4>
                  <h4>{{ $quotation->customer_name ?? ''}}</h4>

                  <address>
                    {{ $quotation->customer->phone ?? ''}}<br>
                    {{ $quotation->customer->email ?? ''}}<br>
                    {{ $quotation->customer->address ?? ''}}
                  </address>
                </div>
                <!-- End Col -->

                <div class="col-md text-md-end">
                  <dl class="row">
                    <dt class="col-sm-8">Quotation date:</dt>
                    <dd class="col-sm-4">{{ optional($quotation->created_at)->format('Y-m-d') ?? ''}}</dd>
                  </dl>
                  <dl class="row">
                    <dt class="col-sm-8">Due date:</dt>
                    <dd class="col-sm-4">{{ optional($quotation->date_to)->format('Y-m-d') ?? ''}}</dd>
                  </dl>
                </div>
                <!-- End Col -->
              </div>
              <!-- End Row -->

              <!-- Table -->
              <div class="table-responsive">
                <table class="table table-bquotationless table-nowrap table-align-middle">
                  <thead class="thead-light">
                    <tr>
                      <th>Item</th>
                      <th>Quantity</th>
                      <th>Price</th>
                      <th>Unit</th>
                      <th class="table-text-end">Amount</th>
                    </tr>
                  </thead>

                  <tbody>
                    @foreach ( $quotation->sales as $sale)
                    <tr>
                      <th>
                        <span class="d-block h5 mb-0"> {{ $sale->product->name ?? '-' }} </span>
                        <span class="d-block fs-5"> {{ $sale->product->description ?? ''}}</span>
                      </th>
                      <td> {{ $sale->quantity ?? ''}}</td>
                      <td> {{ _number( $sale->selling_price ) ?? ''}} </td>
                      <td> {{ $sale->unit->name ?? ''}} </td>
                      <td class="table-text-end">
                        {{ _number( $sale->quantity * $sale->selling_price )}}
                      </td>
                    </tr>
                    @endforeach
                  </tbody>
                </table>
              </div>
              <!-- End Table -->

              <hr class="my-5">

              <div class="row justify-content-md-end mb-3">
                <div class="col-md-8 col-lg-7">
                  <dl class="row text-sm-end">
                    <dt class="col-sm-6">Sub-Total:</dt>
                    <dd class="col-sm-6"> {{ _money( $quotation->sub_total ) ?? '' }} </dd>

                    <dt class="col-sm-6">Total Vat:</dt>
                    <dd class="col-sm-6"> {{ _money( $quotation->vat ) ?? '' }} </dd>

                    <dt class="col-sm-6">Total + Vat:</dt>
                    <dd class="col-sm-6"> {{ _money( $quotation->sub_total + $quotation->vat ) ?? '' }} </dd>

                    <dt class="col-sm-6">Discount:</dt>
                    <dd class="col-sm-6"> {{ _money( $quotation->discount ) ?? '' }} </dd>

                    <dt class="col-sm-6">Total Paid:</dt>
                    <dd class="col-sm-6"> {{  _money( $quotation->amount_total) ?? '' }} </dd>

                    <dt class="col-sm-6">Cash Paid:</dt>
                    <dd class="col-sm-6"> {{  _money( $quotation->amount_paid) ?? '' }} </dd>
                  </dl>
                  <!-- End Row -->
                </div>
              </div>
              <!-- End Row -->

              <div class="mb-3">
                <h3>Thank you!</h3>
                <p>If you have any questions concerning this quotation, use the following contact information:</p>
              </div>

              <p class="small mb-0">&copy; {{ date('Y')}}</p>
            </div>
          </div>
          <!-- End Card -->

          <!-- End Footer -->
        </div>


        <div class="col-lg-4">
          <div id="stickyBlockStartPoint">
            <div class="js-sticky-block" data-hs-sticky-block-options='{
                   "parentSelector": "#stickyBlockStartPoint",
                   "breakpoint": "lg",
                   "startPoint": "#stickyBlockStartPoint",
                   "endPoint": "#stickyBlockEndPoint",
                   "stickyOffsetTop": 20
                 }'>
              <div class="d-grid gap-2 gap-sm-3 mb-2 mb-sm-3">
                <button class="btn btn-soft-primary preview-quotation-btn" data-bs-toggle="modal" class="preview-quotation-btn" data-id="{{ $quotation->id }}" data-bs-target=".preview-quotation-modal">
                  <i class="bi-download me-1"></i> PRINT
                </button>
                   <a class="btn btn-soft-primary"  onclick="createPDFfromHTML('quotation')" href="javascript:;">
                  <i class="bi-download me-1"></i> PDF
                </a>
              </div>

            </div>
          </div>
        </div>


      </div>
    </div>
    <!-- End Content -->

@endsection
