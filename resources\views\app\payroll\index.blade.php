@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                Payroll List
            </h4>
        </div>

        <div class="card-body">
            <div class="mb-4">
                <form>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="month">Month</label>
                                <select name="month" id="month" class="form-control">
                                    @foreach($months as $key => $value)
                                        <option value="{{ $key }}" {{ $month == $key ? 'selected' : '' }}>{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="year">Year</label>
                                <select name="year" id="year" class="form-control">
                                    @foreach($years as $key => $value)
                                        <option value="{{ $key }}" {{ $year == $key ? 'selected' : '' }}>{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="search">Search</label>
                                <input type="text" name="search" id="search" value="{{ $search ?? '' }}" class="form-control" placeholder="Search...">
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">Filter</button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Bulk Email Section -->
            @if($payrolls->count() > 0)
            <div class="mb-4">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-envelope"></i> Email Payslips
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">Send payslips to all employees for the selected period via email.</p>
                        <form action="{{ route('payroll.bulk-email-payslips') }}" method="POST" onsubmit="return confirmBulkEmail()">
                            @csrf
                            <input type="hidden" name="month" value="{{ $month }}">
                            <input type="hidden" name="year" value="{{ $year }}">
                            <button type="submit" class="btn btn-info">
                                <i class="bi bi-send"></i> Send Payslips to All Employees ({{ $payrolls->count() }} employees)
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            @endif

            <div class="table-responsive">
                <table class="table table-hover table-striped">
                    <thead>
                        <tr>
                            <th>Employee</th>
                            <th>Period</th>
                            <th>Basic Pay</th>
                            <th>Gross Pay</th>
                            <th>Deductions</th>
                            <th>Net Pay</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($payrolls as $payroll)
                        <tr>
                            <td>{{ $payroll->employee->name ?? '-' }}</td>
                            <td>{{ $payroll->date_from ? $payroll->date_from->format('M Y') : '-' }}</td>
                            <td>{{ number_format($payroll->basic_pay, 2) }}</td>
                            <td>{{ number_format($payroll->gross, 2) }}</td>
                            <td>{{ number_format($payroll->payee + $payroll->deductions->sum('amount'), 2) }}</td>
                            <td>{{ number_format($payroll->net_pay, 2) }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ route('payroll.show', $payroll) }}" class="btn btn-sm btn-info" title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ route('payroll.edit', $payroll) }}" class="btn btn-sm btn-primary" title="Edit">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <a href="{{ route('payroll.payslip', $payroll) }}" class="btn btn-sm btn-success" target="_blank" title="Download PDF">
                                        <i class="bi bi-file-earmark-pdf"></i>
                                    </a>
                                    @if($payroll->employee && $payroll->employee->email)
                                    <form action="{{ route('payroll.email-payslip', $payroll) }}" method="POST" style="display: inline;">
                                        @csrf
                                        <button type="submit" class="btn btn-sm btn-warning" title="Email Payslip to {{ $payroll->employee->name }}" onclick="return confirm('Send payslip to {{ $payroll->employee->email }}?')">
                                            <i class="bi bi-envelope"></i>
                                        </button>
                                    </form>
                                    @else
                                    <button class="btn btn-sm btn-secondary" disabled title="No email address">
                                        <i class="bi bi-envelope-slash"></i>
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center">No payroll records found.</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                {{ $payrolls->links() }}
            </div>

            <div class="mt-4">
                <a href="{{ route('payroll.generate') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> Generate Payroll
                </a>
                <a href="{{ route('payroll.create-journal-entries') }}" class="btn btn-secondary">
                    <i class="bi bi-journal-text"></i> Create Journal Entries
                </a>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function confirmBulkEmail() {
    const month = document.getElementById('month').options[document.getElementById('month').selectedIndex].text;
    const year = document.getElementById('year').value;
    const count = {{ $payrolls->count() }};

    return confirm(`Are you sure you want to send payslips to all ${count} employees for ${month} ${year}? This action will send emails immediately.`);
}
</script>
@endpush
@endsection
