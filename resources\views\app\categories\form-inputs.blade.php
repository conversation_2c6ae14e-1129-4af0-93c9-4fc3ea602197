@php $editing = isset($category) @endphp

<!-- Form -->
<div class="row mb-4">
    <div class="col-sm-12">
        <!-- Form -->
        <div class="mb-4">
            <label for="name" class="form-label">Category Name <i class="bi-asterisk text-danger small"></i></label>
            <input type="text" class="form-control form-control-lg @error('name') is-invalid @enderror" 
                name="name" id="name" placeholder="Enter category name"
                value="{{ old('name', ($editing ? $category->name : '')) }}" 
                required maxlength="255">
            @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <!-- End Form -->
    </div>
</div>
<!-- End Form -->

<!-- Form -->
<div class="row mb-4">
    <div class="col-sm-6">
        <!-- Form -->
        <div class="mb-4">
            <label for="applied_to" class="form-label">Applied To <i class="bi-asterisk text-danger small"></i></label>
            <div class="tom-select-custom">
                <select class="js-select form-select @error('applied_to') is-invalid @enderror" 
                    name="applied_to" id="applied_to" required
                    data-hs-tom-select-options='{
                        "placeholder": "Select a group..."
                    }'>
                    @php $selected = old('applied_to', ($editing ? $category->applied_to : '')) @endphp
                    <option value="" disabled {{ empty($selected) ? 'selected' : '' }}>Please select the Group</option>
                    <option value="products" {{ $selected == 'products' ? 'selected' : '' }}>Products</option>
                    <option value="expenses" {{ $selected == 'expenses' ? 'selected' : '' }}>Expenses</option>
                    <option value="debtors" {{ $selected == 'debtors' ? 'selected' : '' }}>Debtors</option>
                </select>
            </div>
            @error('applied_to')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <!-- End Form -->
    </div>

    <div class="col-sm-6">
        <!-- Form -->
        <div class="mb-4">
            <label for="status_id" class="form-label">Status <i class="bi-asterisk text-danger small"></i></label>
            <div class="tom-select-custom">
                <select class="js-select form-select @error('status_id') is-invalid @enderror" 
                    name="status_id" id="status_id" required
                    data-hs-tom-select-options='{
                        "placeholder": "Select a status..."
                    }'>
                    @php $selected = old('status_id', ($editing ? $category->status_id : '')) @endphp
                    <option value="" disabled {{ empty($selected) ? 'selected' : '' }}>Please select the Status</option>
                    @foreach($statuses as $value => $label)
                    <option value="{{ $value }}" {{ $selected == $value ? 'selected' : '' }}>{{ $label }}</option>
                    @endforeach
                </select>
            </div>
            @error('status_id')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
        <!-- End Form -->
    </div>
</div>
<!-- End Form -->

<!-- Form -->
<div class="mb-4">
    <label for="description" class="form-label">Description</label>
    <textarea class="form-control @error('description') is-invalid @enderror" 
        name="description" id="description" rows="4" 
        placeholder="Enter category description">{{ old('description', ($editing ? $category->description : '')) }}</textarea>
    @error('description')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
    <small class="form-text text-muted">Provide a brief description of this category and its purpose</small>
</div>
<!-- End Form -->
