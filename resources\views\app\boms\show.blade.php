@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">
                <a href="{{ route('boms.index') }}" class="mr-4"
                    ><i class="icon ion-md-arrow-back"></i
                ></a>
                Bill of Materials Details
            </h4>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="mb-4">
                        <h5>BOM Number</h5>
                        <span>{{ $bom->bom_number ?? '-' }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Name</h5>
                        <span>{{ $bom->name ?? '-' }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Product</h5>
                        <span>{{ optional($bom->product)->name ?? '-' }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Quantity</h5>
                        <span>{{ number_format($bom->quantity, 2) }} {{ $bom->unit_of_measure }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Description</h5>
                        <span>{{ $bom->description ?? '-' }}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-4">
                        <h5>Status</h5>
                        <span class="badge badge-{{ 
                            $bom->status == 'active' ? 'success' : 
                            ($bom->status == 'draft' ? 'warning' : 'danger') 
                        }}">
                            {{ ucfirst($bom->status) }}
                        </span>
                    </div>
                    <div class="mb-4">
                        <h5>Default</h5>
                        <span>{{ $bom->is_default ? 'Yes' : 'No' }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Version</h5>
                        <span>{{ $bom->version ?? '-' }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Effective Date</h5>
                        <span>{{ $bom->effective_date ? $bom->effective_date->format('Y-m-d') : '-' }}</span>
                    </div>
                    <div class="mb-4">
                        <h5>Expiry Date</h5>
                        <span>{{ $bom->expiry_date ? $bom->expiry_date->format('Y-m-d') : '-' }}</span>
                    </div>
                </div>
            </div>

            <hr class="mt-4">

            <div class="mt-4">
                <h5>BOM Items</h5>

                <div class="d-flex justify-content-between mb-3">
                    <span>Total Items: {{ $bomItems->count() }}</span>
                    @can('update', $bom)
                    <a href="{{ route('boms.edit-items', $bom) }}" class="btn btn-primary btn-sm">
                        <i class="icon ion-md-create"></i> Edit Items
                    </a>
                    @endcan
                </div>

                @if($bomItems->count() > 0)
                <div class="table-responsive">
                    <table class="table table-borderless table-hover">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Quantity</th>
                                <th>Unit</th>
                                <th>Cost Per Unit</th>
                                <th>Total Cost</th>
                                <th>Type</th>
                                <th>Scrap</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($bomItems as $item)
                            <tr>
                                <td>{{ optional($item->product)->name ?? '-' }}</td>
                                <td>{{ number_format($item->quantity, 2) }}</td>
                                <td>{{ $item->unit_of_measure }}</td>
                                <td>{{ number_format($item->cost_per_unit, 2) }}</td>
                                <td>{{ number_format($item->total_cost, 2) }}</td>
                                <td>{{ ucfirst($item->item_type) }}</td>
                                <td>
                                    @if($item->is_scrap)
                                        {{ number_format($item->scrap_percentage, 2) }}%
                                    @else
                                        No
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="4" class="text-right">Total:</th>
                                <th>{{ number_format($bom->total_cost, 2) }}</th>
                                <th colspan="2"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                @else
                <div class="alert alert-info">
                    No items added to this BOM yet.
                </div>
                @endif
            </div>

            <div class="mt-4">
                <a
                    href="{{ route('boms.index') }}"
                    class="btn btn-light"
                >
                    <i class="icon ion-md-return-left"></i>
                    @lang('crud.common.back')
                </a>

                @can('create', App\Models\Bom::class)
                <a
                    href="{{ route('boms.create') }}"
                    class="btn btn-light"
                >
                    <i class="icon ion-md-add"></i> @lang('crud.common.create')
                </a>
                @endcan

                @can('update', $bom)
                <a
                    href="{{ route('boms.edit', $bom) }}"
                    class="btn btn-light"
                >
                    <i class="icon ion-md-create"></i> @lang('crud.common.edit')
                </a>
                @endcan

                @can('update', $bom)
                <a
                    href="{{ route('boms.edit-items', $bom) }}"
                    class="btn btn-primary"
                >
                    <i class="icon ion-md-list"></i> Edit Items
                </a>
                @endcan

                @can('create', App\Models\Bom::class)
                <form
                    action="{{ route('boms.copy', $bom) }}"
                    method="POST"
                    class="d-inline"
                >
                    @csrf
                    <button
                        type="submit"
                        class="btn btn-light"
                        onclick="return confirm('Are you sure you want to copy this BOM?')"
                    >
                        <i class="icon ion-md-copy"></i> Copy BOM
                    </button>
                </form>
                @endcan

                @can('delete', $bom)
                <form
                    action="{{ route('boms.destroy', $bom) }}"
                    method="POST"
                    class="d-inline"
                >
                    @csrf @method('DELETE')
                    <button
                        type="submit"
                        class="btn btn-danger"
                        onclick="return confirm('{{ __('crud.common.are_you_sure') }}')"
                    >
                        <i class="icon ion-md-trash"></i>
                        @lang('crud.common.delete')
                    </button>
                </form>
                @endcan
            </div>
        </div>
    </div>
</div>
@endsection
