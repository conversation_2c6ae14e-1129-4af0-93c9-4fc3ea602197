<?php

namespace App\Http\Controllers;

use App\Models\FiscalYear;
use App\Models\FiscalPeriod;
use Illuminate\Http\Request;

class FiscalPeriodController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', FiscalPeriod::class);

        $search = $request->get('search', '');
        $fiscalYearId = $request->get('fiscal_year_id', '');

        $fiscalPeriods = FiscalPeriod::search($search);
        
        if ($fiscalYearId) {
            $fiscalPeriods->where('fiscal_year_id', $fiscalYearId);
        }
        
        $fiscalPeriods = $fiscalPeriods->latest()
            ->paginate()
            ->withQueryString();

        $fiscalYears = FiscalYear::orderBy('name')
            ->pluck('name', 'id');

        return view('app.fiscal_periods.index', compact(
            'fiscalPeriods', 
            'search', 
            'fiscalYears',
            'fiscalYearId'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', FiscalPeriod::class);

        $fiscalYears = FiscalYear::where('is_closed', false)
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.fiscal_periods.create', compact('fiscalYears'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', FiscalPeriod::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'fiscal_year_id' => 'required|exists:fiscal_years,id',
            'description' => 'nullable|string',
        ]);

        // Check if the fiscal year is closed
        $fiscalYear = FiscalYear::find($validated['fiscal_year_id']);
        if ($fiscalYear->is_closed) {
            return redirect()
                ->back()
                ->withInput()
                ->withError('Cannot create periods for a closed fiscal year.');
        }
        
        // Check if the period dates are within the fiscal year dates
        if ($validated['start_date'] < $fiscalYear->start_date || $validated['end_date'] > $fiscalYear->end_date) {
            return redirect()
                ->back()
                ->withInput()
                ->withError('Period dates must be within the fiscal year dates.');
        }
        
        // Check for overlapping periods
        $overlappingPeriods = FiscalPeriod::where('fiscal_year_id', $validated['fiscal_year_id'])
            ->where(function ($query) use ($validated) {
                $query->whereBetween('start_date', [$validated['start_date'], $validated['end_date']])
                    ->orWhereBetween('end_date', [$validated['start_date'], $validated['end_date']])
                    ->orWhere(function ($query) use ($validated) {
                        $query->where('start_date', '<=', $validated['start_date'])
                            ->where('end_date', '>=', $validated['end_date']);
                    });
            })
            ->count();
            
        if ($overlappingPeriods > 0) {
            return redirect()
                ->back()
                ->withInput()
                ->withError('Period dates overlap with existing periods.');
        }

        $fiscalPeriod = FiscalPeriod::create($validated);

        return redirect()
            ->route('fiscal-periods.show', $fiscalPeriod)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\FiscalPeriod  $fiscalPeriod
     * @return \Illuminate\Http\Response
     */
    public function show(FiscalPeriod $fiscalPeriod)
    {
        $this->authorize('view', $fiscalPeriod);

        $journalEntries = $fiscalPeriod->journalEntries()
            ->latest()
            ->paginate(10);

        return view('app.fiscal_periods.show', compact('fiscalPeriod', 'journalEntries'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\FiscalPeriod  $fiscalPeriod
     * @return \Illuminate\Http\Response
     */
    public function edit(FiscalPeriod $fiscalPeriod)
    {
        $this->authorize('update', $fiscalPeriod);
        
        if ($fiscalPeriod->is_closed) {
            return redirect()
                ->route('fiscal-periods.show', $fiscalPeriod)
                ->withError('Closed fiscal periods cannot be edited.');
        }
        
        if ($fiscalPeriod->fiscalYear->is_closed) {
            return redirect()
                ->route('fiscal-periods.show', $fiscalPeriod)
                ->withError('Periods in closed fiscal years cannot be edited.');
        }

        $fiscalYears = FiscalYear::where('is_closed', false)
            ->orderBy('name')
            ->pluck('name', 'id');

        return view('app.fiscal_periods.edit', compact('fiscalPeriod', 'fiscalYears'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\FiscalPeriod  $fiscalPeriod
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, FiscalPeriod $fiscalPeriod)
    {
        $this->authorize('update', $fiscalPeriod);
        
        if ($fiscalPeriod->is_closed) {
            return redirect()
                ->route('fiscal-periods.show', $fiscalPeriod)
                ->withError('Closed fiscal periods cannot be edited.');
        }
        
        if ($fiscalPeriod->fiscalYear->is_closed) {
            return redirect()
                ->route('fiscal-periods.show', $fiscalPeriod)
                ->withError('Periods in closed fiscal years cannot be edited.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        $fiscalPeriod->update($validated);

        return redirect()
            ->route('fiscal-periods.show', $fiscalPeriod)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\FiscalPeriod  $fiscalPeriod
     * @return \Illuminate\Http\Response
     */
    public function destroy(FiscalPeriod $fiscalPeriod)
    {
        $this->authorize('delete', $fiscalPeriod);
        
        if ($fiscalPeriod->is_closed) {
            return redirect()
                ->route('fiscal-periods.index')
                ->withError('Closed fiscal periods cannot be deleted.');
        }
        
        if ($fiscalPeriod->fiscalYear->is_closed) {
            return redirect()
                ->route('fiscal-periods.index')
                ->withError('Periods in closed fiscal years cannot be deleted.');
        }
        
        if ($fiscalPeriod->journalEntries()->count() > 0) {
            return redirect()
                ->route('fiscal-periods.index')
                ->withError('Fiscal period has journal entries and cannot be deleted.');
        }

        $fiscalPeriod->delete();

        return redirect()
            ->route('fiscal-periods.index')
            ->withSuccess(__('crud.common.removed'));
    }
    
    /**
     * Close the fiscal period.
     *
     * @param  \App\Models\FiscalPeriod  $fiscalPeriod
     * @return \Illuminate\Http\Response
     */
    public function close(FiscalPeriod $fiscalPeriod)
    {
        $this->authorize('update', $fiscalPeriod);
        
        if ($fiscalPeriod->is_closed) {
            return redirect()
                ->route('fiscal-periods.show', $fiscalPeriod)
                ->withError('Fiscal period is already closed.');
        }
        
        // Check if previous periods are closed
        $previousPeriods = FiscalPeriod::where('fiscal_year_id', $fiscalPeriod->fiscal_year_id)
            ->where('end_date', '<', $fiscalPeriod->start_date)
            ->where('is_closed', false)
            ->count();
            
        if ($previousPeriods > 0) {
            return redirect()
                ->route('fiscal-periods.show', $fiscalPeriod)
                ->withError('All previous periods must be closed first.');
        }
        
        // Check for unposted journal entries
        $unpostedEntries = $fiscalPeriod->journalEntries()
            ->where('status', 'draft')
            ->count();
            
        if ($unpostedEntries > 0) {
            return redirect()
                ->route('fiscal-periods.show', $fiscalPeriod)
                ->withError('All journal entries must be posted before closing the period.');
        }

        $fiscalPeriod->update([
            'is_closed' => true,
            'closed_date' => now(),
            'closed_by' => auth()->id(),
        ]);
        
        return redirect()
            ->route('fiscal-periods.show', $fiscalPeriod)
            ->withSuccess('Fiscal period has been closed successfully.');
    }
    
    /**
     * Reopen the fiscal period.
     *
     * @param  \App\Models\FiscalPeriod  $fiscalPeriod
     * @return \Illuminate\Http\Response
     */
    public function reopen(FiscalPeriod $fiscalPeriod)
    {
        $this->authorize('update', $fiscalPeriod);
        
        if (!$fiscalPeriod->is_closed) {
            return redirect()
                ->route('fiscal-periods.show', $fiscalPeriod)
                ->withError('Fiscal period is not closed.');
        }
        
        if ($fiscalPeriod->fiscalYear->is_closed) {
            return redirect()
                ->route('fiscal-periods.show', $fiscalPeriod)
                ->withError('Cannot reopen a period in a closed fiscal year.');
        }
        
        // Check if later periods are closed
        $laterPeriods = FiscalPeriod::where('fiscal_year_id', $fiscalPeriod->fiscal_year_id)
            ->where('start_date', '>', $fiscalPeriod->end_date)
            ->where('is_closed', true)
            ->count();
            
        if ($laterPeriods > 0) {
            return redirect()
                ->route('fiscal-periods.show', $fiscalPeriod)
                ->withError('Later periods must be reopened first.');
        }

        $fiscalPeriod->update([
            'is_closed' => false,
            'closed_date' => null,
            'closed_by' => null,
        ]);
        
        return redirect()
            ->route('fiscal-periods.show', $fiscalPeriod)
            ->withSuccess('Fiscal period has been reopened successfully.');
    }
}
