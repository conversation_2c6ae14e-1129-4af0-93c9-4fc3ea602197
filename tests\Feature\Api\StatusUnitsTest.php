<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Unit;
use App\Models\Status;

use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class StatusUnitsTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create(['email' => '<EMAIL>']);

        Sanctum::actingAs($user, [], 'web');

        $this->seed(\Database\Seeders\PermissionsSeeder::class);

        $this->withoutExceptionHandling();
    }

    /**
     * @test
     */
    public function it_gets_status_units()
    {
        $status = Status::factory()->create();
        $units = Unit::factory()
            ->count(2)
            ->create([
                'status_id' => $status->id,
            ]);

        $response = $this->getJson(route('api.statuses.units.index', $status));

        $response->assertOk()->assertSee($units[0]->name);
    }

    /**
     * @test
     */
    public function it_stores_the_status_units()
    {
        $status = Status::factory()->create();
        $data = Unit::factory()
            ->make([
                'status_id' => $status->id,
            ])
            ->toArray();

        $response = $this->postJson(
            route('api.statuses.units.store', $status),
            $data
        );

        unset($data['created_by']);
        unset($data['updated_by']);

        $this->assertDatabaseHas('units', $data);

        $response->assertStatus(201)->assertJsonFragment($data);

        $unit = Unit::latest('id')->first();

        $this->assertEquals($status->id, $unit->status_id);
    }
}
