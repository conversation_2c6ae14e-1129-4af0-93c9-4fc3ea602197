@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title mb-0">
                <i class="bi bi-people"></i> Employee Management
            </h4>
            @can('create', App\Models\Employee::class)
            <a href="{{ route('employees.create') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Add New Employee
            </a>
            @endcan
        </div>

        <div class="card-body">
            <!-- Search and Filter Form -->
            <form method="GET" action="{{ route('employees.index') }}" class="mb-4">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" name="search" id="search" class="form-control"
                                   value="{{ $search }}" placeholder="Search by name, email, or phone...">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="grade">Grade</label>
                            <select name="grade" id="grade" class="form-control">
                                <option value="">All Grades</option>
                                @foreach($grades as $gradeOption)
                                    <option value="{{ $gradeOption }}" {{ $grade == $gradeOption ? 'selected' : '' }}>
                                        {{ $gradeOption }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-secondary">
                                    <i class="bi bi-search"></i> Filter
                                </button>
                                <a href="{{ route('employees.index') }}" class="btn btn-light">
                                    <i class="bi bi-x-circle"></i> Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Employees Table -->
            <div class="table-responsive">
                <table class="table table-hover table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Grade</th>
                            <th>Basic Pay</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($employees as $employee)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                   <x-partials.thumbnail src="{{ $employee->image ?? '' }}" />
                                    <div>
                                        <strong>{{ $employee->name }}</strong>
                                        @if($employee->user)
                                            <br><small class="text-muted">User Account: {{ $employee->user->name }}</small>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>{{ $employee->email ?? '-' }}</td>
                            <td>{{ $employee->phone ?? '-' }}</td>
                            <td>
                                @if($employee->grade)
                                    <span class="badge bg-info">{{ $employee->grade }}</span>
                                @else
                                    -
                                @endif
                            </td>
                            <td class="text-right">{{ number_format($employee->basic_pay ?? 0, 2) }}</td>
                            <td>
                                @if($employee->deleted_at)
                                    <span class="badge bg-danger">Inactive</span>
                                @else
                                    <span class="badge bg-success">Active</span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group">
                                    @can('view', $employee)
                                    <a href="{{ route('employees.show', $employee) }}" class="btn btn-sm btn-info">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    @endcan
                                    @can('update', $employee)
                                    <a href="{{ route('employees.edit', $employee) }}" class="btn btn-sm btn-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    @endcan
                                    <a href="{{ route('employee-deduction-contributions.index', ['employee_id' => $employee->id]) }}" class="btn btn-sm btn-secondary">
                                        <i class="bi bi-person-lines-fill"></i>
                                    </a>
                                    @can('delete', $employee)
                                    <form action="{{ route('employees.destroy', $employee) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this employee?');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                    @endcan
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center">
                                <div class="py-4">
                                    <i class="bi bi-people" style="font-size: 3rem; color: #ccc;"></i>
                                    <p class="mt-2 text-muted">No employees found.</p>
                                    @can('create', App\Models\Employee::class)
                                    <a href="{{ route('employees.create') }}" class="btn btn-primary">
                                        <i class="bi bi-plus-circle"></i> Add First Employee
                                    </a>
                                    @endcan
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($employees->hasPages())
            <div class="d-flex justify-content-center">
                {{ $employees->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
