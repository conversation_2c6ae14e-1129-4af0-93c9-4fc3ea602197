@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                {{ $deductionContribution->apply_mode == 'DEDUCTION' ? 'Deduction' : 'Contribution' }} Details
            </h4>
        </div>

        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>Basic Information</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th>Name</th>
                            <td>{{ $deductionContribution->name }}</td>
                        </tr>
                        <tr>
                            <th>Code</th>
                            <td>{{ $deductionContribution->code ?? '-' }}</td>
                        </tr>
                        <tr>
                            <th>Type</th>
                            <td>{{ $deductionContribution->apply_mode }}</td>
                        </tr>
                        <tr>
                            <th>Apply At</th>
                            <td>{{ $deductionContribution->apply_at }}</td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>{{ $deductionContribution->status->name ?? '-' }}</td>
                        </tr>
                        <tr>
                            <th>Is Calculated</th>
                            <td>{{ $deductionContribution->is_calculatable ? "Yes" : 'No' }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h5>Calculation Details</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th>Formula</th>
                            <td>{{ $deductionContribution->formula }}</td>
                        </tr>
                        <tr>
                            <th>Description</th>
                            <td>{{ $deductionContribution->description ?? '-' }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <h5>Assigned Employees</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($employees as $employee)
                                <tr>
                                    <td>{{ $employee->name }}</td>
                                    <td>
                                        @php
                                            $assignment = $employee->employeeDeductionContributions->where('deduction_contribution_id', $deductionContribution->id)->first();
                                        @endphp
                                        {{ $assignment && $assignment->start_date ? $assignment->start_date->format('d M Y') : '-' }}
                                    </td>
                                    <td>
                                        {{ $assignment && $assignment->end_date ? $assignment->end_date->format('d M Y') : '-' }}
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="3" class="text-center">No employees assigned.</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <div class="d-flex justify-content-between">
                    <a href="{{ route('deduction-contributions.index', ['type' => strtolower($deductionContribution->apply_mode)]) }}" class="btn btn-light">
                        <i class="bi bi-arrow-left"></i> Back
                    </a>
                    <div>
                        <a href="{{ route('deduction-contributions.edit', $deductionContribution) }}" class="btn btn-primary">
                            <i class="bi bi-pencil"></i> Edit
                        </a>
                        <a href="{{ route('deduction-contributions.assign-form', $deductionContribution) }}" class="btn btn-success">
                            <i class="bi bi-people"></i> Assign Employees
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
