@extends('emails.layout')

@section('title', 'Your Payslip - ' . $period)

@section('content')
<div class="greeting">
    Hello {{ $employee->name }},
</div>

<div class="content">
    <p>Your payslip for <strong>{{ $period }}</strong> has been generated and is ready for your review.</p>
    
    <p>Please find your detailed payslip attached to this email as a PDF document.</p>
</div>

<div class="success-box">
    <h3 style="margin-bottom: 15px; color: #2e7d32;">💰 Pay Summary</h3>
    <table class="details-table">
        <tr>
            <td><strong>Pay Period:</strong></td>
            <td>{{ $payroll->date_from->format('M d, Y') }} - {{ $payroll->date_to->format('M d, Y') }}</td>
        </tr>
        <tr>
            <td><strong>Gross Pay:</strong></td>
            <td class="amount">K{{ number_format($payroll->gross_pay, 2) }}</td>
        </tr>
        <tr>
            <td><strong>Total Deductions:</strong></td>
            <td>K{{ number_format($payroll->total_deductions, 2) }}</td>
        </tr>
        <tr>
            <td><strong>Tax (PAYE):</strong></td>
            <td>K{{ number_format($payroll->tax_amount, 2) }}</td>
        </tr>
        <tr style="background-color: #e8f5e8;">
            <td><strong>Net Pay:</strong></td>
            <td class="amount" style="font-size: 18px; color: #2e7d32;">K{{ number_format($payroll->net_pay, 2) }}</td>
        </tr>
    </table>
</div>

@if($payroll->overtime_hours > 0)
<div class="info-box">
    <h4 style="margin-bottom: 10px;">⏰ Overtime Information</h4>
    <p><strong>Overtime Hours:</strong> {{ number_format($payroll->overtime_hours, 2) }} hours</p>
    <p><strong>Overtime Pay:</strong> K{{ number_format($payroll->overtime_pay, 2) }}</p>
</div>
@endif

@php
    $loanDeductions = $payroll->payrollItems()->where('type', 'DEDUCTION')->whereNotNull('loan_id')->get();
@endphp

@if($loanDeductions->count() > 0)
<div class="info-box">
    <h4 style="margin-bottom: 15px;">🏦 Loan Deductions</h4>
    <table class="details-table">
        <thead>
            <tr>
                <th>Loan Reference</th>
                <th>Deduction Amount</th>
            </tr>
        </thead>
        <tbody>
            @foreach($loanDeductions as $deduction)
            <tr>
                <td>{{ $deduction->name }}</td>
                <td class="amount">K{{ number_format($deduction->amount, 2) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
</div>
@endif

<div class="content">
    <p>If you have any questions about your payslip, please don't hesitate to contact the HR department.</p>
    
    <p><strong>Important:</strong> Please keep this payslip for your records. You may need it for tax purposes or loan applications.</p>
</div>

<div class="highlight-box">
    <h3 style="margin-bottom: 10px;">📄 Payslip Attached</h3>
    <p>Your detailed payslip is attached as a PDF file to this email.</p>
    <p style="font-size: 14px; opacity: 0.9;">File: payslip_{{ $employee->name }}_{{ $payroll->date_from->format('Y-m') }}.pdf</p>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="{{ route('payroll.show', $payroll) }}" class="btn btn-primary">
        View Online Payslip
    </a>
</div>
@endsection
